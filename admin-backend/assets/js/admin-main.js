// TeleShop 后台管理系统主要脚本

class AdminApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.isLoggedIn = false;
        this.currentUser = null;
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.checkLoginStatus();
        this.initGlobalSearch();
    }
    
    setupEventListeners() {
        // 登录表单处理
        const loginForm = document.getElementById('adminLoginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }
        
        // 全局点击事件处理
        document.addEventListener('click', this.handleGlobalClick.bind(this));
        
        // 侧边栏切换
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', this.toggleSidebar.bind(this));
        }
    }
    
    // 处理登录
    handleLogin(event) {
        event.preventDefault();
        
        const email = document.getElementById('adminEmail').value;
        const password = document.getElementById('adminPassword').value;
        
        if (!email || !password) {
            this.showNotification('请填写完整的登录信息', 'error');
            return;
        }
        
        // 模拟登录验证
        this.showNotification('正在验证登录信息...', 'info');
        
        setTimeout(() => {
            if (email && password) {
                this.isLoggedIn = true;
                this.currentUser = {
                    name: '张管理员',
                    email: email,
                    role: '超级管理员',
                    avatar: 'assets/images/admin-avatar.png'
                };
                
                this.showDashboard();
                this.showNotification('登录成功，欢迎回来！', 'success');
            } else {
                this.showNotification('邮箱或密码错误，请重试', 'error');
            }
        }, 1500);
    }
    
    // 显示后台界面
    showDashboard() {
        document.getElementById('loginPage').style.display = 'none';
        document.getElementById('adminDashboard').style.display = 'flex';
        
        // 设置默认页面
        this.loadPage('dashboard');
    }
    
    // 页面切换功能
    loadPage(pageName) {
        // 隐藏所有内容页面
        const contentPages = document.querySelectorAll('.content-page');
        contentPages.forEach(page => page.classList.remove('active'));
        
        // 显示目标页面
        const targetPage = document.getElementById(pageName + '-content');
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = pageName;
        }
        
        // 更新菜单状态
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => item.classList.remove('active'));
        
        const activeMenuItem = document.getElementById('menu-' + pageName);
        if (activeMenuItem) {
            activeMenuItem.classList.add('active');
        }
        
        // 更新页面标题
        this.updatePageTitle(pageName);
        
        // 显示加载提示（对于尚未开发的页面）
        if (!['dashboard', 'user-management', 'content-management'].includes(pageName)) {
            this.showNotification('页面加载中...', 'info');
        }
        
        // 记录页面访问
        console.log(`页面切换到: ${pageName}`);
    }
    
    // 更新页面标题
    updatePageTitle(pageName) {
        const pageTitles = {
            'dashboard': '仪表盘',
            'user-management': '用户管理',
            'product-management': '商品管理',
            'order-management': '订单管理',
            'financial-management': '财务管理',
            'content-management': '聊天管理',
            'reports-analytics': '数据分析',
            'system-settings': '系统设置',
            'logs': '系统日志'
        };
        
        const title = pageTitles[pageName] || '未知页面';
        document.title = `${title} - TeleShop 后台管理`;
    }
    
    // 检查登录状态
    checkLoginStatus() {
        const savedUser = localStorage.getItem('adminUser');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);
            this.isLoggedIn = true;
            this.showDashboard();
        }
    }
    
    // 侧边栏切换
    toggleSidebar() {
        const sidebar = document.getElementById('adminSidebar');
        const main = document.getElementById('adminMain');
        
        if (sidebar && main) {
            sidebar.classList.toggle('collapsed');
            main.classList.toggle('expanded');
        }
    }
    
    // 切换通知面板
    toggleNotifications() {
        const panel = document.getElementById('notificationPanel');
        if (panel) {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
    }
    
    // 切换消息面板
    toggleMessages() {
        const panel = document.getElementById('messagePanel');
        if (panel) {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
    }
    
    // 切换个人资料菜单
    toggleProfileMenu() {
        const menu = document.getElementById('profileMenu');
        if (menu) {
            menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
        }
    }
    
    // 处理全局点击事件
    handleGlobalClick(event) {
        // 关闭打开的面板和菜单
        if (!event.target.closest('.notification-btn')) {
            const notificationPanel = document.getElementById('notificationPanel');
            if (notificationPanel && notificationPanel.style.display === 'block') {
                notificationPanel.style.display = 'none';
            }
        }
        
        if (!event.target.closest('.message-btn')) {
            const messagePanel = document.getElementById('messagePanel');
            if (messagePanel && messagePanel.style.display === 'block') {
                messagePanel.style.display = 'none';
            }
        }
        
        if (!event.target.closest('.admin-profile')) {
            const profileMenu = document.getElementById('profileMenu');
            if (profileMenu && profileMenu.style.display === 'block') {
                profileMenu.style.display = 'none';
            }
        }
    }
    
    // 密码显示切换
    togglePassword(inputId) {
        const input = document.getElementById(inputId);
        const button = input.nextElementSibling;
        const icon = button.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }
    
    // 退出登录
    logout() {
        if (confirm('确定要退出登录吗？')) {
            this.isLoggedIn = false;
            this.currentUser = null;
            localStorage.removeItem('adminUser');
            
            document.getElementById('adminDashboard').style.display = 'none';
            document.getElementById('loginPage').style.display = 'block';
            
            // 清空登录表单
            document.getElementById('adminEmail').value = '';
            document.getElementById('adminPassword').value = '';
            
            this.showNotification('已成功退出登录', 'success');
        }
    }
    
    // 通知系统
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `admin-notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 9999;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            color: white;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            max-width: 350px;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
            background-color: ${this.getNotificationColor(type)};
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // 自动移除通知
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    // 获取通知颜色
    getNotificationColor(type) {
        const colors = {
            'success': '#10b981',
            'error': '#ef4444',
            'warning': '#f59e0b',
            'info': '#3b82f6'
        };
        return colors[type] || colors.info;
    }
    
    // 数据刷新
    refreshData() {
        this.showNotification('正在刷新数据...', 'info');
        
        setTimeout(() => {
            this.showNotification('数据刷新完成！', 'success');
            
            // 这里可以添加实际的数据刷新逻辑
            if (this.currentPage === 'dashboard') {
                // 刷新仪表盘数据
                this.refreshDashboardData();
            }
        }, 1500);
    }
    
    // 刷新仪表盘数据
    refreshDashboardData() {
        // 这里可以添加仪表盘数据刷新逻辑
        console.log('刷新仪表盘数据');
    }
    
    // 增强的全局搜索功能
    initGlobalSearch() {
        const searchInput = document.getElementById('globalSearch');
        const searchBtn = document.querySelector('.search-btn');
        
        if (searchInput) {
            // 添加搜索建议功能
            this.setupSearchSuggestions(searchInput);
            
            // 搜索输入事件
            searchInput.addEventListener('input', (e) => {
                this.handleSearchInput(e.target.value);
            });
            
            // 回车搜索
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performAdvancedSearch(e.target.value);
                }
            });
        }
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performAdvancedSearch(searchInput.value);
            });
        }
    }
    
    // 搜索输入处理
    handleSearchInput(query) {
        if (query.length < 2) {
            this.hideSearchSuggestions();
            return;
        }
        
        // 实时搜索建议
        this.showSearchSuggestions(query);
    }
    
    // 显示搜索建议
    showSearchSuggestions(query) {
        const suggestions = this.getSearchSuggestions(query);
        const suggestionBox = this.getOrCreateSuggestionBox();
        
        suggestionBox.innerHTML = '';
        
        // 搜索类型分组
        const groupedSuggestions = {
            '用户': suggestions.users,
            '商品': suggestions.products,
            '订单': suggestions.orders,
            '其他': suggestions.others
        };
        
        let hasResults = false;
        
        Object.entries(groupedSuggestions).forEach(([category, items]) => {
            if (items && items.length > 0) {
                hasResults = true;
                
                // 分类标题
                const categoryHeader = document.createElement('div');
                categoryHeader.className = 'suggestion-category';
                categoryHeader.textContent = category;
                suggestionBox.appendChild(categoryHeader);
                
                // 建议项
                items.slice(0, 5).forEach(item => {
                    const suggestionItem = document.createElement('div');
                    suggestionItem.className = 'suggestion-item';
                    suggestionItem.innerHTML = `
                        <i class="${item.icon}"></i>
                        <div class="suggestion-content">
                            <div class="suggestion-title">${item.title}</div>
                            <div class="suggestion-desc">${item.description}</div>
                        </div>
                        <div class="suggestion-type">${item.type}</div>
                    `;
                    
                    suggestionItem.addEventListener('click', () => {
                        this.selectSuggestion(item);
                    });
                    
                    suggestionBox.appendChild(suggestionItem);
                });
            }
        });
        
        if (!hasResults) {
            suggestionBox.innerHTML = '<div class="no-suggestions">暂无相关建议</div>';
        }
        
        // 添加高级搜索选项
        const advancedSearch = document.createElement('div');
        advancedSearch.className = 'advanced-search-option';
        advancedSearch.innerHTML = `
            <i class="fas fa-search-plus"></i>
            <span>高级搜索</span>
        `;
        advancedSearch.addEventListener('click', () => {
            this.showAdvancedSearchModal(query);
        });
        suggestionBox.appendChild(advancedSearch);
        
        suggestionBox.style.display = 'block';
    }
    
    // 获取搜索建议
    getSearchSuggestions(query) {
        // 模拟智能搜索建议数据
        const mockSuggestions = {
            users: [
                { title: '张三', description: '<EMAIL>', type: '用户', icon: 'fas fa-user' },
                { title: '李四', description: '手机: 138****5678', type: '用户', icon: 'fas fa-user' },
                { title: '王五', description: 'VIP用户', type: '用户', icon: 'fas fa-crown' }
            ],
            products: [
                { title: 'iPhone 15', description: '手机/数码', type: '商品', icon: 'fas fa-mobile-alt' },
                { title: '华为 P60', description: '手机/数码', type: '商品', icon: 'fas fa-mobile-alt' },
                { title: '小米 14', description: '手机/数码', type: '商品', icon: 'fas fa-mobile-alt' }
            ],
            orders: [
                { title: '订单 #2024001', description: '待付款 - ¥1,299', type: '订单', icon: 'fas fa-shopping-cart' },
                { title: '订单 #2024002', description: '已发货 - ¥2,599', type: '订单', icon: 'fas fa-truck' }
            ],
            others: [
                { title: '商品分类管理', description: '管理功能', type: '功能', icon: 'fas fa-cog' },
                { title: '财务报表', description: '报表功能', type: '功能', icon: 'fas fa-chart-line' }
            ]
        };
        
        // 基于查询关键字过滤建议
        const filteredSuggestions = {};
        
        Object.keys(mockSuggestions).forEach(category => {
            filteredSuggestions[category] = mockSuggestions[category].filter(item => 
                item.title.toLowerCase().includes(query.toLowerCase()) ||
                item.description.toLowerCase().includes(query.toLowerCase())
            );
        });
        
        return filteredSuggestions;
    }
    
    // 获取或创建建议框
    getOrCreateSuggestionBox() {
        let suggestionBox = document.getElementById('searchSuggestions');
        
        if (!suggestionBox) {
            suggestionBox = document.createElement('div');
            suggestionBox.id = 'searchSuggestions';
            suggestionBox.className = 'search-suggestions';
            
            const searchContainer = document.querySelector('.global-search');
            searchContainer.appendChild(suggestionBox);
        }
        
        return suggestionBox;
    }
    
    // 隐藏搜索建议
    hideSearchSuggestions() {
        const suggestionBox = document.getElementById('searchSuggestions');
        if (suggestionBox) {
            suggestionBox.style.display = 'none';
        }
    }
    
    // 选择建议项
    selectSuggestion(suggestion) {
        const searchInput = document.getElementById('globalSearch');
        searchInput.value = suggestion.title;
        this.hideSearchSuggestions();
        
        // 保存搜索历史
        this.saveSearchHistory(suggestion.title);
        
        // 执行搜索
        this.performAdvancedSearch(suggestion.title, suggestion.type);
    }
    
    // 执行高级搜索
    performAdvancedSearch(query, type = 'all') {
        if (!query.trim()) return;
        
        console.log('执行高级搜索:', query, type);
        
        // 保存搜索历史
        this.saveSearchHistory(query);
        
        // 显示搜索结果页面
        this.showSearchResults(query, type);
    }
    
    // 显示高级搜索模态框
    showAdvancedSearchModal(query = '') {
        const modal = this.createAdvancedSearchModal(query);
        document.body.appendChild(modal);
        
        // 显示模态框
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
    
    // 创建高级搜索模态框
    createAdvancedSearchModal(query) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay advanced-search-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>高级搜索</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-group">
                            <label>搜索关键词</label>
                            <input type="text" id="advancedSearchQuery" value="${query}" placeholder="输入搜索关键词">
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>搜索类型</label>
                                <select id="advancedSearchType">
                                    <option value="all">全部</option>
                                    <option value="users">用户</option>
                                    <option value="products">商品</option>
                                    <option value="orders">订单</option>
                                    <option value="content">内容</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>时间范围</label>
                                <select id="advancedSearchTime">
                                    <option value="all">全部时间</option>
                                    <option value="today">今天</option>
                                    <option value="week">最近一周</option>
                                    <option value="month">最近一月</option>
                                    <option value="year">最近一年</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>状态筛选</label>
                            <div class="checkbox-group">
                                <label><input type="checkbox" value="active" checked> 激活</label>
                                <label><input type="checkbox" value="inactive"> 停用</label>
                                <label><input type="checkbox" value="pending"> 待处理</label>
                                <label><input type="checkbox" value="completed"> 已完成</label>
                            </div>
                        </div>
                        
                        <div class="search-history">
                            <h4>搜索历史</h4>
                            <div class="history-tags" id="searchHistoryTags">
                                <!-- 搜索历史标签 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn-primary" onclick="adminApp.executeAdvancedSearch()">搜索</button>
                </div>
            </div>
        `;
        
        // 加载搜索历史
        this.loadSearchHistory();
        
        return modal;
    }
    
    // 执行高级搜索
    executeAdvancedSearch() {
        const query = document.getElementById('advancedSearchQuery').value;
        const type = document.getElementById('advancedSearchType').value;
        const timeRange = document.getElementById('advancedSearchTime').value;
        
        const statusFilters = Array.from(document.querySelectorAll('.checkbox-group input:checked'))
            .map(checkbox => checkbox.value);
        
        // 关闭模态框
        document.querySelector('.advanced-search-modal').remove();
        
        // 执行搜索
        this.performAdvancedSearch(query, type, { timeRange, statusFilters });
    }
    
    // 保存搜索历史
    saveSearchHistory(query) {
        let history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
        
        // 避免重复
        history = history.filter(item => item !== query);
        history.unshift(query);
        
        // 限制历史记录数量
        history = history.slice(0, 10);
        
        localStorage.setItem('searchHistory', JSON.stringify(history));
    }
    
    // 加载搜索历史
    loadSearchHistory() {
        const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
        const container = document.getElementById('searchHistoryTags');
        
        if (container) {
            container.innerHTML = history.map(query => `
                <span class="history-tag" onclick="document.getElementById('advancedSearchQuery').value = '${query}'">${query}</span>
            `).join('');
        }
    }
    
    // 显示搜索结果
    showSearchResults(query, type, filters = {}) {
        // 创建搜索结果页面
        const resultsPage = this.createSearchResultsPage(query, type, filters);
        
        // 添加到主内容区域
        const mainContent = document.getElementById('mainContent');
        
        // 隐藏其他页面
        document.querySelectorAll('.content-page').forEach(page => {
            page.classList.remove('active');
        });
        
        // 显示搜索结果页面
        if (document.getElementById('search-results-content')) {
            document.getElementById('search-results-content').remove();
        }
        
        mainContent.appendChild(resultsPage);
        resultsPage.classList.add('active');
        
        this.showNotification(`搜索 "${query}" 完成`, 'success');
    }
    
    // 创建搜索结果页面
    createSearchResultsPage(query, type, filters) {
        const page = document.createElement('div');
        page.id = 'search-results-content';
        page.className = 'content-page';
        
        page.innerHTML = `
            <div class="search-results-container">
                <div class="search-results-header">
                    <h2>搜索结果</h2>
                    <div class="search-info">
                        <span>搜索关键词: <strong>${query}</strong></span>
                        <span>搜索类型: <strong>${this.getTypeLabel(type)}</strong></span>
                        <span>找到 <strong>25</strong> 条结果</span>
                    </div>
                </div>
                
                <div class="search-results-content">
                    <div class="results-tabs">
                        <button class="tab-btn active" data-tab="all">全部 (25)</button>
                        <button class="tab-btn" data-tab="users">用户 (8)</button>
                        <button class="tab-btn" data-tab="products">商品 (12)</button>
                        <button class="tab-btn" data-tab="orders">订单 (5)</button>
                    </div>
                    
                    <div class="results-list" id="searchResultsList">
                        ${this.generateSearchResults(query, type)}
                    </div>
                    
                    <div class="results-pagination">
                        <button class="btn-secondary">上一页</button>
                        <span class="pagination-info">第 1 页，共 3 页</span>
                        <button class="btn-secondary">下一页</button>
                    </div>
                </div>
            </div>
        `;
        
        // 添加标签页切换功能
        page.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn')) {
                const tabs = page.querySelectorAll('.tab-btn');
                tabs.forEach(tab => tab.classList.remove('active'));
                e.target.classList.add('active');
                
                const tabType = e.target.dataset.tab;
                this.filterSearchResults(tabType);
            }
        });
        
        return page;
    }
    
    // 获取类型标签
    getTypeLabel(type) {
        const labels = {
            'all': '全部',
            'users': '用户',
            'products': '商品',
            'orders': '订单',
            'content': '内容'
        };
        return labels[type] || '全部';
    }
    
    // 生成搜索结果
    generateSearchResults(query, type) {
        // 模拟搜索结果数据
        const mockResults = [
            {
                type: 'user',
                title: '张三',
                description: '注册用户 • 最后登录: 2024-01-15',
                email: '<EMAIL>',
                status: '活跃',
                icon: 'fas fa-user'
            },
            {
                type: 'product',
                title: 'iPhone 15 Pro',
                description: '手机数码 • 库存: 125',
                price: '¥8,999',
                status: '在售',
                icon: 'fas fa-mobile-alt'
            },
            {
                type: 'order',
                title: '订单 #2024001234',
                description: '2024-01-15 14:30 • 张三',
                amount: '¥1,299',
                status: '已完成',
                icon: 'fas fa-shopping-cart'
            }
        ];
        
        return mockResults.map(result => `
            <div class="result-item" data-type="${result.type}">
                <div class="result-icon">
                    <i class="${result.icon}"></i>
                </div>
                <div class="result-content">
                    <h4 class="result-title">${result.title}</h4>
                    <p class="result-description">${result.description}</p>
                    ${result.email ? `<p class="result-meta">邮箱: ${result.email}</p>` : ''}
                    ${result.price ? `<p class="result-meta">价格: ${result.price}</p>` : ''}
                    ${result.amount ? `<p class="result-meta">金额: ${result.amount}</p>` : ''}
                </div>
                <div class="result-status">
                    <span class="status-badge status-${result.status === '活跃' || result.status === '在售' || result.status === '已完成' ? 'success' : 'warning'}">
                        ${result.status}
                    </span>
                </div>
            </div>
        `).join('');
    }
    
    // 过滤搜索结果
    filterSearchResults(tabType) {
        const results = document.querySelectorAll('.result-item');
        
        results.forEach(result => {
            if (tabType === 'all') {
                result.style.display = 'flex';
            } else {
                const resultType = result.dataset.type;
                result.style.display = (tabType === resultType + 's' || tabType === resultType) ? 'flex' : 'none';
            }
        });
    }
    
    // 添加缺失的全局搜索方法
    performGlobalSearch(query) {
        if (!query || !query.trim()) {
            this.showNotification('请输入搜索关键词', 'warning');
            return;
        }
        
        console.log('执行全局搜索:', query);
        
        // 如果高级搜索功能已加载，则使用高级搜索
        if (window.AdvancedSearchManager) {
            try {
                const searchManager = new window.AdvancedSearchManager();
                searchManager.performSearch(query);
                return;
            } catch (error) {
                console.warn('高级搜索功能加载失败，使用基本搜索:', error);
            }
        }
        
        // 基本搜索功能
        this.showNotification(`正在搜索 "${query}"...`, 'info');
        
        // 模拟搜索结果
        setTimeout(() => {
            this.showNotification(`找到 ${Math.floor(Math.random() * 50) + 10} 条搜索结果`, 'success');
            // 这里可以添加显示搜索结果的逻辑
        }, 1000);
    }
}

// 全局函数（向后兼容）
function loadPage(pageName) {
    if (window.adminApp) {
        window.adminApp.loadPage(pageName);
    }
}

function toggleSidebar() {
    if (window.adminApp) {
        window.adminApp.toggleSidebar();
    }
}

function toggleNotifications() {
    if (window.adminApp) {
        window.adminApp.toggleNotifications();
    }
}

function toggleMessages() {
    if (window.adminApp) {
        window.adminApp.toggleMessages();
    }
}

function toggleProfileMenu() {
    if (window.adminApp) {
        window.adminApp.toggleProfileMenu();
    }
}

function togglePassword(inputId) {
    if (window.adminApp) {
        window.adminApp.togglePassword(inputId);
    }
}

function logout() {
    if (window.adminApp) {
        window.adminApp.logout();
    }
}

// 初始化应用
function initializeAdminApp() {
    console.log('开始初始化 AdminApp...');
    
    // 确保不重复初始化
    if (window.adminApp) {
        console.log('AdminApp 已存在，跳过重复初始化');
        return window.adminApp;
    }
    
    try {
        window.adminApp = new AdminApp();
        console.log('AdminApp 创建成功:', window.adminApp);
        
        // 设置全局搜索
        const globalSearch = document.getElementById('globalSearch');
        if (globalSearch) {
            globalSearch.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    window.adminApp.performGlobalSearch(this.value);
                }
            });
            console.log('全局搜索功能已设置');
        }
        
        // 设置当前日期
        const currentDate = document.getElementById('currentDate');
        if (currentDate) {
            currentDate.textContent = new Date().toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long', 
                day: 'numeric',
                weekday: 'long'
            });
        }
        
        // 验证关键功能
        if (typeof window.adminApp.loadPage === 'function') {
            console.log('loadPage 方法可用');
        } else {
            console.error('loadPage 方法不可用！');
        }
        
        console.log('TeleShop 后台管理系统初始化完成');
        return window.adminApp;
        
    } catch (error) {
        console.error('AdminApp 初始化失败:', error);
        throw error;
    }
}

// 多种方式确保初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAdminApp);
} else {
    // DOM 已经加载完成
    initializeAdminApp();
}

// 备用初始化（延迟执行）
setTimeout(() => {
    if (!window.adminApp) {
        console.warn('使用备用初始化方法...');
        initializeAdminApp();
    }
}, 500); 