/**
 * TeleShop Admin - 兼容性修复和页面优化脚本
 * 用于解决浏览器兼容性问题、布局问题和图片加载问题
 */

(function() {
    'use strict';

    console.log('🔧 TeleShop 兼容性修复脚本启动...');

    // 浏览器检测
    const BrowserDetect = {
        init: function() {
            this.browser = this.searchString(this.dataBrowser) || "Unknown";
            this.version = this.searchVersion(navigator.userAgent) || this.searchVersion(navigator.appVersion) || "Unknown";
            this.OS = this.searchString(this.dataOS) || "Unknown";
        },
        searchString: function(data) {
            for (let i = 0; i < data.length; i++) {
                const dataString = data[i].string;
                const dataProp = data[i].prop;
                this.versionSearchString = data[i].versionSearch || data[i].identity;
                if (dataString) {
                    if (dataString.indexOf(data[i].subString) !== -1)
                        return data[i].identity;
                } else if (dataProp)
                    return data[i].identity;
            }
        },
        searchVersion: function(dataString) {
            const index = dataString.indexOf(this.versionSearchString);
            if (index === -1) return;
            return parseFloat(dataString.substring(index + this.versionSearchString.length + 1));
        },
        dataBrowser: [
            { string: navigator.userAgent, subString: "Chrome", identity: "Chrome" },
            { string: navigator.userAgent, subString: "Safari", identity: "Safari" },
            { string: navigator.userAgent, subString: "Firefox", identity: "Firefox" },
            { string: navigator.userAgent, subString: "Edge", identity: "Edge" },
            { prop: window.opera, identity: "Opera" },
            { string: navigator.userAgent, subString: "MSIE", identity: "Internet Explorer", versionSearch: "MSIE" }
        ],
        dataOS: [
            { string: navigator.platform, subString: "Win", identity: "Windows" },
            { string: navigator.platform, subString: "Mac", identity: "Mac" },
            { string: navigator.userAgent, subString: "iPhone", identity: "iPhone/iPod" },
            { string: navigator.platform, subString: "Linux", identity: "Linux" }
        ]
    };

    // 初始化浏览器检测
    BrowserDetect.init();
    console.log(`🌐 浏览器信息: ${BrowserDetect.browser} ${BrowserDetect.version} on ${BrowserDetect.OS}`);

    // CSS兼容性修复
    const CSSCompatibility = {
        init: function() {
            this.addBrowserClasses();
            this.fixFlexbox();
            this.fixViewport();
            this.fixScrolling();
        },

        addBrowserClasses: function() {
            const html = document.documentElement;
            html.classList.add(BrowserDetect.browser.toLowerCase());
            html.classList.add(BrowserDetect.OS.toLowerCase());
            
            // 添加特定版本类
            if (BrowserDetect.browser === 'Internet Explorer' && BrowserDetect.version < 11) {
                html.classList.add('old-ie');
            }
            
            // 移动设备检测
            if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                html.classList.add('mobile');
            }
        },

        fixFlexbox: function() {
            // 为旧浏览器添加flexbox前缀
            const flexboxRules = `
                .admin-dashboard {
                    display: -webkit-box;
                    display: -webkit-flex;
                    display: -ms-flexbox;
                    display: flex;
                }
                .admin-sidebar {
                    -webkit-flex-shrink: 0;
                    -ms-flex-negative: 0;
                    flex-shrink: 0;
                }
                .admin-main {
                    -webkit-box-flex: 1;
                    -webkit-flex: 1;
                    -ms-flex: 1;
                    flex: 1;
                }
            `;
            
            this.addCSS(flexboxRules);
        },

        fixViewport: function() {
            // 修复移动端视口问题
            if (window.innerWidth <= 768) {
                const viewport = document.querySelector('meta[name="viewport"]');
                if (viewport) {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                }
            }
        },

        fixScrolling: function() {
            // 修复iOS滚动问题
            if (BrowserDetect.OS === 'iPhone/iPod') {
                const scrollableElements = document.querySelectorAll('.sidebar-menu, .panel-content');
                scrollableElements.forEach(el => {
                    el.style.webkitOverflowScrolling = 'touch';
                });
            }
        },

        addCSS: function(cssText) {
            const style = document.createElement('style');
            style.type = 'text/css';
            if (style.styleSheet) {
                style.styleSheet.cssText = cssText;
            } else {
                style.appendChild(document.createTextNode(cssText));
            }
            document.head.appendChild(style);
        }
    };

    // 图片加载修复
    const ImageLoader = {
        init: function() {
            this.handleImageErrors();
            this.createPlaceholders();
            this.lazyLoadImages();
        },

        handleImageErrors: function() {
            // 监听所有图片错误
            document.addEventListener('error', (e) => {
                if (e.target.tagName === 'IMG') {
                    this.replaceWithPlaceholder(e.target);
                }
            }, true);

            // 检查已有的图片
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                if (!img.complete || img.naturalHeight === 0) {
                    this.replaceWithPlaceholder(img);
                }
            });
        },

        replaceWithPlaceholder: function(img) {
            const placeholder = this.createImagePlaceholder(img);
            img.parentNode.replaceChild(placeholder, img);
        },

        createImagePlaceholder: function(img) {
            const placeholder = document.createElement('div');
            const width = img.width || img.getAttribute('width') || '100%';
            const height = img.height || img.getAttribute('height') || '100%';
            
            placeholder.className = 'image-placeholder';
            placeholder.style.width = typeof width === 'number' ? width + 'px' : width;
            placeholder.style.height = typeof height === 'number' ? height + 'px' : height;
            
            // 根据原图片的类名或用途添加特定类型
            if (img.classList.contains('avatar') || img.alt.toLowerCase().includes('avatar')) {
                placeholder.classList.add('type-avatar');
                placeholder.innerHTML = '<i class="fas fa-user"></i>';
            } else if (img.classList.contains('logo') || img.alt.toLowerCase().includes('logo')) {
                placeholder.classList.add('type-logo');
                placeholder.innerHTML = '<i class="fas fa-store"></i>';
            } else if (img.classList.contains('product') || img.alt.toLowerCase().includes('product')) {
                placeholder.classList.add('type-product');
                placeholder.innerHTML = '<i class="fas fa-box"></i>';
            } else {
                placeholder.innerHTML = '<i class="fas fa-image"></i>';
            }
            
            return placeholder;
        },

        createPlaceholders: function() {
            // 为没有src的img标签创建占位符
            const brokenImages = document.querySelectorAll('img:not([src]), img[src=""]');
            brokenImages.forEach(img => {
                this.replaceWithPlaceholder(img);
            });
        },

        lazyLoadImages: function() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            observer.unobserve(img);
                        }
                    });
                });

                const lazyImages = document.querySelectorAll('img[data-src]');
                lazyImages.forEach(img => {
                    imageObserver.observe(img);
                });
            }
        }
    };

    // 布局修复
    const LayoutFixer = {
        init: function() {
            this.fixResponsiveLayout();
            this.fixIframeHeight();
            this.fixOverflow();
            this.monitorWindowResize();
        },

        fixResponsiveLayout: function() {
            // 修复响应式布局问题
            const mediaQuery = window.matchMedia('(max-width: 768px)');
            this.handleResponsiveChange(mediaQuery);
            mediaQuery.addListener(this.handleResponsiveChange);
        },

        handleResponsiveChange: function(mq) {
            const dashboard = document.getElementById('adminDashboard');
            const sidebar = document.getElementById('adminSidebar');
            
            if (mq.matches) {
                // 移动端布局
                if (dashboard) dashboard.classList.add('mobile-layout');
                if (sidebar) sidebar.classList.add('mobile-sidebar');
            } else {
                // 桌面端布局
                if (dashboard) dashboard.classList.remove('mobile-layout');
                if (sidebar) sidebar.classList.remove('mobile-sidebar');
            }
        },

        fixIframeHeight: function() {
            // 修复iframe高度问题
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach(iframe => {
                iframe.addEventListener('load', () => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const height = iframeDoc.body.scrollHeight;
                        iframe.style.height = height + 'px';
                    } catch (e) {
                        // 跨域限制，使用默认高度
                        iframe.style.height = '100%';
                    }
                });
            });
        },

        fixOverflow: function() {
            // 修复overflow问题
            const contentPages = document.querySelectorAll('.content-page');
            contentPages.forEach(page => {
                if (page.scrollHeight > page.clientHeight) {
                    page.style.overflow = 'auto';
                }
            });
        },

        monitorWindowResize: function() {
            let resizeTimer;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(() => {
                    this.fixResponsiveLayout();
                    this.recalculateHeights();
                }, 250);
            });
        },

        recalculateHeights: function() {
            // 重新计算高度
            const contentPages = document.querySelectorAll('.content-page');
            const headerHeight = document.querySelector('.admin-header')?.offsetHeight || 80;
            
            contentPages.forEach(page => {
                page.style.height = `calc(100vh - ${headerHeight}px)`;
            });
        }
    };

    // 性能优化
    const PerformanceOptimizer = {
        init: function() {
            this.debounceEvents();
            this.optimizeAnimations();
            this.preloadCriticalResources();
        },

        debounceEvents: function() {
            // 防抖处理
            this.debounce = function(func, wait, immediate) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        timeout = null;
                        if (!immediate) func(...args);
                    };
                    const callNow = immediate && !timeout;
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                    if (callNow) func(...args);
                };
            };

            // 优化搜索输入
            const searchInput = document.getElementById('globalSearch');
            if (searchInput) {
                const debouncedSearch = this.debounce((e) => {
                    // 搜索逻辑
                    console.log('搜索:', e.target.value);
                }, 300);
                
                searchInput.addEventListener('input', debouncedSearch);
            }
        },

        optimizeAnimations: function() {
            // 检测是否支持硬件加速
            const testElement = document.createElement('div');
            testElement.style.transform = 'translate3d(0,0,0)';
            
            if (testElement.style.transform) {
                // 为动画元素添加硬件加速
                const animatedElements = document.querySelectorAll('.menu-item, .action-btn, .notification-panel');
                animatedElements.forEach(el => {
                    el.style.transform = 'translate3d(0,0,0)';
                });
            }
        },

        preloadCriticalResources: function() {
            // 预加载关键资源
            const criticalCSS = [
                'assets/css/admin-styles.css',
                'assets/css/mobile-enhancements.css'
            ];

            const criticalJS = [
                'assets/js/admin-main.js',
                'assets/js/advanced-search.js'
            ];

            // 预加载CSS
            criticalCSS.forEach(href => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'style';
                link.href = href;
                document.head.appendChild(link);
            });

            // 预加载JS
            criticalJS.forEach(src => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'script';
                link.href = src;
                document.head.appendChild(link);
            });
        }
    };

    // 错误处理
    const ErrorHandler = {
        init: function() {
            this.setupGlobalErrorHandler();
            this.setupUnhandledPromiseRejection();
        },

        setupGlobalErrorHandler: function() {
            window.addEventListener('error', (e) => {
                console.error('🚨 全局错误:', e.error);
                this.showErrorNotification('页面出现错误，请刷新重试');
            });
        },

        setupUnhandledPromiseRejection: function() {
            window.addEventListener('unhandledrejection', (e) => {
                console.error('🚨 未处理的Promise错误:', e.reason);
                this.showErrorNotification('网络请求失败，请检查网络连接');
            });
        },

        showErrorNotification: function(message) {
            // 显示错误通知
            const notification = document.createElement('div');
            notification.className = 'error-notification';
            notification.innerHTML = `
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${message}</span>
                    <button onclick="this.parentNode.parentNode.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // 添加样式
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #fee;
                border: 1px solid #fcc;
                border-radius: 8px;
                padding: 16px;
                z-index: 9999;
                max-width: 400px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            
            document.body.appendChild(notification);
            
            // 自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    };

    // 初始化所有修复
    const CompatibilityManager = {
        init: function() {
            console.log('🔧 开始初始化兼容性修复...');
            
            // 等待DOM准备就绪
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    this.runFixes();
                });
            } else {
                this.runFixes();
            }
        },

        runFixes: function() {
            try {
                CSSCompatibility.init();
                ImageLoader.init();
                LayoutFixer.init();
                PerformanceOptimizer.init();
                ErrorHandler.init();
                
                console.log('✅ 兼容性修复完成');
                
                // 添加修复完成标记
                document.body.classList.add('compatibility-fixed');
                
            } catch (error) {
                console.error('❌ 兼容性修复失败:', error);
            }
        }
    };

    // 导出到全局
    window.TeleShopCompatibility = CompatibilityManager;

    // 自动初始化
    CompatibilityManager.init();

})(); 