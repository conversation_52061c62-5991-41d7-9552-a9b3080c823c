/**
 * TeleShop 后台管理系统 - 高级搜索功能
 * 提供智能搜索、自动完成、搜索建议等功能
 */

class AdvancedSearchManager {
    constructor() {
        this.searchInput = null;
        this.searchResults = null;
        this.suggestions = null;
        this.searchHistory = this.loadSearchHistory();
        this.searchIndex = new Map();
        this.searchFilters = {};
        this.debounceTimer = null;
        this.currentQuery = '';
        this.isSearching = false;
        this.searchCache = new Map();
        this.currentSearchResults = [];
        this.searchTimer = null;
        this.isSearchActive = false;
        
        this.init();
    }

    init() {
        this.setupSearchElements();
        this.setupEventListeners();
        this.buildSearchIndex();
        this.initSearchUI();
    }

    // 设置搜索元素
    setupSearchElements() {
        this.searchInput = document.getElementById('globalSearch');
        if (!this.searchInput) return;

        // 创建搜索建议容器
        this.createSuggestionsContainer();
        
        // 创建搜索结果容器
        this.createSearchResultsContainer();
        
        // 创建高级搜索模态框
        this.createAdvancedSearchModal();
    }

    // 创建搜索建议容器
    createSuggestionsContainer() {
        const searchContainer = this.searchInput.parentElement;
        searchContainer.style.position = 'relative';

        this.suggestions = document.createElement('div');
        this.suggestions.className = 'search-suggestions';
        this.suggestions.style.display = 'none';
        searchContainer.appendChild(this.suggestions);
    }

    // 创建搜索结果容器
    createSearchResultsContainer() {
        this.searchResults = document.createElement('div');
        this.searchResults.className = 'search-results-modal modal-overlay';
        this.searchResults.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>搜索结果</h3>
                    <button class="modal-close" onclick="advancedSearch.closeSearchResults()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="search-results-container">
                        <div class="search-results-header">
                            <h2 id="searchResultsTitle">搜索结果</h2>
                            <div class="search-info">
                                <span>找到 <strong id="resultsCount">0</strong> 个结果</span>
                                <span>用时 <strong id="searchTime">0</strong> 毫秒</span>
                            </div>
                        </div>
                        <div class="results-tabs">
                            <button class="tab-btn active" data-tab="all">全部</button>
                            <button class="tab-btn" data-tab="users">用户</button>
                            <button class="tab-btn" data-tab="products">商品</button>
                            <button class="tab-btn" data-tab="orders">订单</button>
                            <button class="tab-btn" data-tab="content">内容</button>
                        </div>
                        <div class="results-list" id="searchResultsList">
                            <!-- 搜索结果将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(this.searchResults);
    }

    // 创建高级搜索模态框
    createAdvancedSearchModal() {
        const modal = document.createElement('div');
        modal.className = 'advanced-search-modal modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>高级搜索</h3>
                    <button class="modal-close" onclick="advancedSearch.closeAdvancedSearch()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="search-form" id="advancedSearchForm">
                        <div class="form-group">
                            <label>关键词</label>
                            <input type="text" id="advancedKeyword" placeholder="输入搜索关键词">
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>搜索范围</label>
                                <select id="searchScope">
                                    <option value="all">全部</option>
                                    <option value="users">用户</option>
                                    <option value="products">商品</option>
                                    <option value="orders">订单</option>
                                    <option value="content">内容</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>时间范围</label>
                                <select id="timeRange">
                                    <option value="all">全部时间</option>
                                    <option value="today">今天</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                    <option value="quarter">本季度</option>
                                    <option value="year">今年</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>筛选条件</label>
                            <div class="checkbox-group">
                                <label><input type="checkbox" value="active"> 仅活跃状态</label>
                                <label><input type="checkbox" value="featured"> 推荐内容</label>
                                <label><input type="checkbox" value="recent"> 最近更新</label>
                                <label><input type="checkbox" value="popular"> 热门内容</label>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>排序方式</label>
                                <select id="sortBy">
                                    <option value="relevance">相关性</option>
                                    <option value="date">日期</option>
                                    <option value="popularity">热度</option>
                                    <option value="name">名称</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>排序顺序</label>
                                <select id="sortOrder">
                                    <option value="desc">降序</option>
                                    <option value="asc">升序</option>
                                </select>
                            </div>
                        </div>

                        <div class="search-history">
                            <h4>搜索历史</h4>
                            <div class="history-tags" id="searchHistoryTags">
                                <!-- 搜索历史标签将在这里显示 -->
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="advancedSearch.clearSearchForm()">清空</button>
                    <button type="button" class="btn-primary" onclick="advancedSearch.performAdvancedSearch()">搜索</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        this.advancedSearchModal = modal;
    }

    // 设置事件监听器
    setupEventListeners() {
        if (!this.searchInput) return;

        // 输入事件 - 实时搜索建议
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        // 获得焦点时显示建议
        this.searchInput.addEventListener('focus', () => {
            if (this.searchInput.value.trim()) {
                this.showSuggestions();
            } else {
                this.showRecentSearches();
            }
        });

        // 失去焦点时隐藏建议（延迟执行，以便点击建议项）
        this.searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                this.hideSuggestions();
            }, 200);
        });

        // 键盘导航
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });

        // 点击外部关闭建议
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                this.hideSuggestions();
            }
        });

        // 搜索结果标签页切换
        this.setupTabSwitching();
    }

    // 处理搜索输入
    handleSearchInput(query) {
        this.currentQuery = query.trim();
        
        // 清除之前的定时器
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        // 防抖处理
        this.debounceTimer = setTimeout(() => {
            if (this.currentQuery.length > 0) {
                this.showSuggestions();
                this.performQuickSearch(this.currentQuery);
            } else {
                this.hideSuggestions();
            }
        }, 300);
    }

    // 显示搜索建议
    async showSuggestions() {
        if (!this.currentQuery) return;

        const suggestions = await this.generateSuggestions(this.currentQuery);
        this.renderSuggestions(suggestions);
        this.suggestions.style.display = 'block';
    }

    // 生成搜索建议
    async generateSuggestions(query) {
        const suggestions = {
            history: this.getHistorySuggestions(query),
            quick: this.getQuickSuggestions(query),
            smart: await this.getSmartSuggestions(query)
        };

        return suggestions;
    }

    // 获取历史搜索建议
    getHistorySuggestions(query) {
        return this.searchHistory
            .filter(item => item.query.toLowerCase().includes(query.toLowerCase()))
            .slice(0, 3)
            .map(item => ({
                type: 'history',
                title: item.query,
                description: `${item.results} 个结果`,
                icon: 'fas fa-history',
                action: () => this.performSearch(item.query)
            }));
    }

    // 获取快速建议
    getQuickSuggestions(query) {
        const quickSuggestions = [
            {
                pattern: /^用户|user/i,
                suggestions: [
                    { title: '用户管理', description: '查看所有用户', icon: 'fas fa-users', action: () => this.navigateTo('/users') },
                    { title: '活跃用户', description: '查看活跃用户', icon: 'fas fa-user-check', action: () => this.performSearch('活跃用户') }
                ]
            },
            {
                pattern: /^商品|product/i,
                suggestions: [
                    { title: '商品管理', description: '查看所有商品', icon: 'fas fa-box', action: () => this.navigateTo('/products') },
                    { title: '热销商品', description: '查看热销商品', icon: 'fas fa-fire', action: () => this.performSearch('热销商品') }
                ]
            },
            {
                pattern: /^订单|order/i,
                suggestions: [
                    { title: '订单管理', description: '查看所有订单', icon: 'fas fa-shopping-cart', action: () => this.navigateTo('/orders') },
                    { title: '待处理订单', description: '查看待处理订单', icon: 'fas fa-clock', action: () => this.performSearch('待处理订单') }
                ]
            }
        ];

        for (const item of quickSuggestions) {
            if (item.pattern.test(query)) {
                return item.suggestions.map(s => ({ ...s, type: 'quick' }));
            }
        }

        return [];
    }

    // 获取智能建议
    async getSmartSuggestions(query) {
        // 模拟智能建议生成
        return new Promise(resolve => {
            setTimeout(() => {
                const smartSuggestions = [
                    {
                        type: 'smart',
                        title: `搜索 "${query}" 相关内容`,
                        description: '在所有模块中搜索',
                        icon: 'fas fa-search',
                        action: () => this.performSearch(query)
                    }
                ];
                resolve(smartSuggestions);
            }, 100);
        });
    }

    // 渲染建议
    renderSuggestions(suggestions) {
        let html = '';

        // 历史建议
        if (suggestions.history.length > 0) {
            html += '<div class="suggestion-category">搜索历史</div>';
            suggestions.history.forEach(item => {
                html += this.renderSuggestionItem(item);
            });
        }

        // 快速建议
        if (suggestions.quick.length > 0) {
            html += '<div class="suggestion-category">快速访问</div>';
            suggestions.quick.forEach(item => {
                html += this.renderSuggestionItem(item);
            });
        }

        // 智能建议
        if (suggestions.smart.length > 0) {
            html += '<div class="suggestion-category">智能建议</div>';
            suggestions.smart.forEach(item => {
                html += this.renderSuggestionItem(item);
            });
        }

        // 高级搜索选项
        html += `
            <div class="advanced-search-option" onclick="advancedSearch.showAdvancedSearch()">
                <i class="fas fa-cog"></i>
                高级搜索
            </div>
        `;

        this.suggestions.innerHTML = html;
    }

    // 渲染建议项
    renderSuggestionItem(item) {
        return `
            <div class="suggestion-item" onclick="event.preventDefault(); (${item.action.toString()})()">
                <i class="${item.icon}"></i>
                <div class="suggestion-content">
                    <div class="suggestion-title">${item.title}</div>
                    <div class="suggestion-desc">${item.description}</div>
                </div>
                <div class="suggestion-type">${item.type}</div>
            </div>
        `;
    }

    // 显示最近搜索
    showRecentSearches() {
        if (this.searchHistory.length === 0) return;

        let html = '<div class="suggestion-category">最近搜索</div>';
        this.searchHistory.slice(0, 5).forEach(item => {
            html += `
                <div class="suggestion-item" onclick="advancedSearch.performSearch('${item.query}')">
                    <i class="fas fa-history"></i>
                    <div class="suggestion-content">
                        <div class="suggestion-title">${item.query}</div>
                        <div class="suggestion-desc">${item.results} 个结果</div>
                    </div>
                </div>
            `;
        });

        this.suggestions.innerHTML = html;
        this.suggestions.style.display = 'block';
    }

    // 隐藏建议
    hideSuggestions() {
        if (this.suggestions) {
            this.suggestions.style.display = 'none';
        }
    }

    // 键盘导航
    handleKeyboardNavigation(e) {
        const items = this.suggestions.querySelectorAll('.suggestion-item');
        const currentActive = this.suggestions.querySelector('.suggestion-item.active');
        
        let nextIndex = -1;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (currentActive) {
                    nextIndex = Array.from(items).indexOf(currentActive) + 1;
                } else {
                    nextIndex = 0;
                }
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                if (currentActive) {
                    nextIndex = Array.from(items).indexOf(currentActive) - 1;
                } else {
                    nextIndex = items.length - 1;
                }
                break;
                
            case 'Enter':
                e.preventDefault();
                if (currentActive) {
                    currentActive.click();
                } else {
                    this.performSearch(this.currentQuery);
                }
                return;
                
            case 'Escape':
                this.hideSuggestions();
                return;
        }
        
        // 更新活跃项
        if (nextIndex >= 0 && nextIndex < items.length) {
            items.forEach(item => item.classList.remove('active'));
            items[nextIndex].classList.add('active');
        }
    }

    // 执行快速搜索
    async performQuickSearch(query) {
        // 这里可以实现实时搜索预览
        console.log('Quick search:', query);
    }

    // 执行搜索
    async performSearch(query, container) {
        if (!query.trim()) return;

        this.hideSuggestions();
        this.addToSearchHistory(query);
        
        const startTime = Date.now();
        this.isSearching = true;
        
        try {
            const results = await this.searchContent(query);
            const searchTime = Date.now() - startTime;
            
            this.showSearchResults(results, searchTime, container);
        } catch (error) {
            console.error('搜索失败:', error);
            this.showSearchError(container);
        } finally {
            this.isSearching = false;
        }
    }

    // 搜索内容
    async searchContent(query, filters = {}) {
        // 检查缓存
        const cacheKey = query.toLowerCase();
        if (this.searchCache.has(cacheKey)) {
            const cachedResults = this.searchCache.get(cacheKey);
            return cachedResults;
        }

        // 执行搜索
        const results = await this.executeSearch(query);
        
        // 缓存结果
        this.searchCache.set(cacheKey, results);
        
        return results;
    }

    async executeSearch(query) {
        const results = {
            users: await this.searchUsers(query),
            products: await this.searchProducts(query),
            orders: await this.searchOrders(query),
            content: await this.searchContent(query),
            system: await this.searchSystem(query)
        };

        return results;
    }

    async searchUsers(query) {
        // 模拟用户搜索
        const mockUsers = [
            { id: 1, name: '张三', email: '<EMAIL>', role: '管理员', status: '活跃' },
            { id: 2, name: '李四', email: '<EMAIL>', role: '用户', status: '活跃' },
            { id: 3, name: '王五', email: '<EMAIL>', role: '编辑', status: '未激活' }
        ];

        return mockUsers.filter(user => 
            user.name.includes(query) || 
            user.email.includes(query) ||
            user.role.includes(query)
        ).slice(0, 5);
    }

    async searchProducts(query) {
        // 模拟商品搜索
        const mockProducts = [
            { id: 1, name: 'iPhone 15 Pro', category: '手机', price: 7999, stock: 50 },
            { id: 2, name: 'MacBook Pro', category: '笔记本', price: 15999, stock: 20 },
            { id: 3, name: 'AirPods Pro', category: '耳机', price: 1999, stock: 100 }
        ];

        return mockProducts.filter(product => 
            product.name.toLowerCase().includes(query.toLowerCase()) ||
            product.category.includes(query)
        ).slice(0, 5);
    }

    async searchOrders(query) {
        // 模拟订单搜索
        const mockOrders = [
            { id: 'ORD001', customer: '张三', amount: 7999, status: '已支付', date: '2024-01-15' },
            { id: 'ORD002', customer: '李四', amount: 1999, status: '待发货', date: '2024-01-14' },
            { id: 'ORD003', customer: '王五', amount: 15999, status: '已完成', date: '2024-01-13' }
        ];

        return mockOrders.filter(order => 
            order.id.toLowerCase().includes(query.toLowerCase()) ||
            order.customer.includes(query) ||
            order.status.includes(query)
        ).slice(0, 5);
    }

    async searchContent(query) {
        // 模拟内容搜索
        const mockContent = [
            { type: 'page', title: '用户管理', url: '/pages/user-management.html' },
            { type: 'page', title: '商品管理', url: '/pages/product-management.html' },
            { type: 'page', title: '订单管理', url: '/pages/order-management.html' },
            { type: 'setting', title: '系统设置', url: '/pages/system-settings.html' }
        ];

        return mockContent.filter(item => 
            item.title.includes(query)
        ).slice(0, 5);
    }

    async searchSystem(query) {
        // 模拟系统功能搜索
        const systemItems = [
            { type: 'function', title: '导出数据', description: '导出用户或订单数据' },
            { type: 'function', title: '发送通知', description: '向用户发送系统通知' },
            { type: 'function', title: '数据备份', description: '备份系统数据' }
        ];

        return systemItems.filter(item => 
            item.title.includes(query) || 
            item.description.includes(query)
        ).slice(0, 3);
    }

    // 显示搜索结果
    showSearchResults(results, searchTime, container) {
        const totalResults = Object.values(results).reduce((sum, arr) => sum + arr.length, 0);
        
        // 更新结果统计
        document.getElementById('searchResultsTitle').textContent = `"${this.currentQuery}" 的搜索结果`;
        document.getElementById('resultsCount').textContent = totalResults;
        document.getElementById('searchTime').textContent = searchTime;
        
        // 渲染结果
        this.renderSearchResults(results);
        
        // 显示结果模态框
        this.searchResults.classList.add('show');
        setTimeout(() => {
            this.searchResults.style.opacity = '1';
        }, 10);
    }

    // 渲染搜索结果
    renderSearchResults(results) {
        const resultsList = document.getElementById('searchResultsList');
        let html = '';

        Object.entries(results).forEach(([category, items]) => {
            if (items.length > 0) {
                items.forEach(item => {
                    html += `
                        <div class="result-item" data-category="${category}" onclick="advancedSearch.openResult('${item.type}', ${item.id})">
                            <div class="result-icon">
                                <i class="${this.getResultIcon(item.type)}"></i>
                            </div>
                            <div class="result-content">
                                <div class="result-title">${item.title}</div>
                                <div class="result-description">${item.description}</div>
                                <div class="result-meta">类型：${this.getResultTypeName(item.type)}</div>
                            </div>
                            <div class="result-status">
                                <span class="status-badge status-${item.status}">${this.getStatusText(item.status)}</span>
                            </div>
                        </div>
                    `;
                });
            }
        });

        if (html === '') {
            html = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h3>未找到相关结果</h3>
                    <p>请尝试使用其他关键词或使用高级搜索功能</p>
                </div>
            `;
        }

        resultsList.innerHTML = html;
    }

    // 获取结果图标
    getResultIcon(type) {
        const icons = {
            user: 'fas fa-user',
            product: 'fas fa-box',
            order: 'fas fa-shopping-cart',
            content: 'fas fa-file-alt'
        };
        return icons[type] || 'fas fa-file';
    }

    // 获取结果类型名称
    getResultTypeName(type) {
        const names = {
            user: '用户',
            product: '商品',
            order: '订单',
            content: '内容'
        };
        return names[type] || '未知';
    }

    // 获取状态文本
    getStatusText(status) {
        const texts = {
            active: '活跃',
            inactive: '非活跃',
            available: '可用',
            unavailable: '不可用',
            completed: '已完成',
            pending: '待处理',
            published: '已发布',
            draft: '草稿'
        };
        return texts[status] || status;
    }

    // 设置标签页切换
    setupTabSwitching() {
        // 这里可以添加标签页切换逻辑
    }

    // 打开结果
    openResult(type, id) {
        const routes = {
            user: `/users/${id}`,
            product: `/products/${id}`,
            order: `/orders/${id}`,
            content: `/content/${id}`
        };
        
        if (routes[type]) {
            this.navigateTo(routes[type]);
            this.closeSearchResults();
        }
    }

    // 导航到指定页面
    navigateTo(path) {
        // 这里可以实现页面导航逻辑
        console.log('Navigate to:', path);
    }

    // 关闭搜索结果
    closeSearchResults() {
        this.searchResults.classList.remove('show');
        setTimeout(() => {
            this.searchResults.style.opacity = '0';
        }, 10);
    }

    // 显示高级搜索
    showAdvancedSearch() {
        this.hideSuggestions();
        this.renderSearchHistory();
        this.advancedSearchModal.classList.add('show');
        setTimeout(() => {
            this.advancedSearchModal.style.opacity = '1';
        }, 10);
    }

    // 关闭高级搜索
    closeAdvancedSearch() {
        this.advancedSearchModal.classList.remove('show');
        setTimeout(() => {
            this.advancedSearchModal.style.opacity = '0';
        }, 10);
    }

    // 执行高级搜索
    performAdvancedSearch() {
        const form = document.getElementById('advancedSearchForm');
        const formData = new FormData(form);
        const filters = Object.fromEntries(formData.entries());
        
        const keyword = document.getElementById('advancedKeyword').value.trim();
        if (!keyword) {
            alert('请输入搜索关键词');
            return;
        }
        
        this.closeAdvancedSearch();
        this.performSearch(keyword, filters);
    }

    // 清空搜索表单
    clearSearchForm() {
        document.getElementById('advancedSearchForm').reset();
    }

    // 渲染搜索历史
    renderSearchHistory() {
        const historyContainer = document.getElementById('searchHistoryTags');
        let html = '';
        
        this.searchHistory.slice(0, 10).forEach(item => {
            html += `
                <span class="history-tag" onclick="document.getElementById('advancedKeyword').value='${item.query}'">${item.query}</span>
            `;
        });
        
        historyContainer.innerHTML = html;
    }

    // 添加到搜索历史
    addToSearchHistory(query) {
        // 移除重复项
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);
        
        // 添加到开头
        this.searchHistory.unshift({
            query,
            timestamp: Date.now(),
            results: 0 // 实际应用中应该是真实的结果数量
        });
        
        // 限制历史记录数量
        this.searchHistory = this.searchHistory.slice(0, 20);
        
        // 保存到本地存储
        this.saveSearchHistory();
    }

    // 加载搜索历史
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('search_history');
            if (history) {
                this.searchHistory = JSON.parse(history);
            }
        } catch (error) {
            console.error('加载搜索历史失败:', error);
            this.searchHistory = [];
        }
    }

    // 保存搜索历史
    saveSearchHistory() {
        try {
            localStorage.setItem('search_history', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.error('保存搜索历史失败:', error);
        }
    }

    // 构建搜索索引
    buildSearchIndex() {
        // 这里可以预构建搜索索引以提高搜索性能
        console.log('Building search index...');
    }

    // 显示搜索错误
    showSearchError(container) {
        console.error('Search error:', error);
        // 可以显示错误提示
    }

    initSearchUI() {
        // 在页面中添加搜索相关的UI元素
        this.addGlobalSearchButton();
        this.addSearchStyles();
    }

    addGlobalSearchButton() {
        const navbar = document.querySelector('.navbar, .header');
        if (navbar && !navbar.querySelector('.global-search-btn')) {
            const searchBtn = document.createElement('button');
            searchBtn.className = 'global-search-btn';
            searchBtn.innerHTML = `
                <i class="fas fa-search"></i>
                <span>搜索</span>
                <kbd>Ctrl+K</kbd>
            `;
            searchBtn.addEventListener('click', () => this.showSearchModal());
            
            navbar.appendChild(searchBtn);
        }
    }

    addSearchStyles() {
        if (document.getElementById('advancedSearchStyles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'advancedSearchStyles';
        styles.textContent = `
            /* 全局搜索按钮样式 */
            .global-search-btn {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 16px;
                border: 1px solid #e1e5e9;
                border-radius: 8px;
                background: #fff;
                color: #6c757d;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s;
            }
            
            .global-search-btn:hover {
                border-color: #007bff;
                color: #007bff;
            }
            
            .global-search-btn kbd {
                padding: 2px 6px;
                font-size: 11px;
                background: #f8f9fa;
                border: 1px solid #e1e5e9;
                border-radius: 4px;
            }
            
            /* 搜索模态框样式 */
            .search-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 9999;
                display: none;
            }
            
            .search-modal.active {
                display: block;
            }
            
            .search-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(4px);
            }
            
            .search-modal-container {
                position: relative;
                max-width: 600px;
                margin: 10vh auto;
                background: #fff;
                border-radius: 12px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
                overflow: hidden;
            }
            
            .search-modal-header {
                padding: 20px;
                border-bottom: 1px solid #e9ecef;
            }
            
            .search-input-container {
                position: relative;
                display: flex;
                align-items: center;
            }
            
            .search-icon {
                position: absolute;
                left: 16px;
                color: #6c757d;
                z-index: 1;
            }
            
            .search-input {
                width: 100%;
                padding: 16px 16px 16px 48px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 16px;
                outline: none;
                transition: border-color 0.2s;
            }
            
            .search-input:focus {
                border-color: #007bff;
            }
            
            .search-shortcuts {
                position: absolute;
                right: 16px;
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #6c757d;
            }
            
            .shortcut {
                padding: 2px 6px;
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
            }
            
            .search-modal-body {
                max-height: 400px;
                overflow-y: auto;
            }
            
            .search-loading {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
                padding: 40px;
                color: #6c757d;
            }
            
            .search-results {
                padding: 12px 0;
            }
            
            .search-result-section {
                margin-bottom: 16px;
            }
            
            .search-section-header {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 20px;
                font-size: 12px;
                font-weight: 600;
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            
            .result-count {
                margin-left: auto;
                padding: 2px 6px;
                background: #e9ecef;
                border-radius: 10px;
                font-size: 10px;
            }
            
            .search-result-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px 20px;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            
            .search-result-item:hover,
            .search-result-item.highlighted {
                background: #f8f9fa;
            }
            
            .result-icon {
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #e9ecef;
                border-radius: 6px;
                color: #6c757d;
            }
            
            .result-content {
                flex: 1;
                min-width: 0;
            }
            
            .result-title {
                font-weight: 500;
                color: #212529;
                margin-bottom: 2px;
            }
            
            .result-title mark {
                background: #fff3cd;
                color: #856404;
                padding: 1px 2px;
                border-radius: 2px;
            }
            
            .result-subtitle {
                font-size: 12px;
                color: #6c757d;
            }
            
            .result-meta {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;
                color: #6c757d;
            }
            
            .status-badge {
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 10px;
                font-weight: 500;
            }
            
            .status-badge.active {
                background: #d4edda;
                color: #155724;
            }
            
            .status-badge.inactive {
                background: #f8d7da;
                color: #721c24;
            }
            
            .search-suggestions {
                padding: 16px 20px;
                border-bottom: 1px solid #e9ecef;
            }
            
            .suggestions-header {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 12px;
                font-size: 12px;
                font-weight: 600;
                color: #6c757d;
            }
            
            .search-suggestion {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
                margin-bottom: 4px;
                background: #f8f9fa;
                border-radius: 6px;
                cursor: pointer;
                transition: background-color 0.2s;
                font-size: 14px;
            }
            
            .search-suggestion:hover,
            .search-suggestion.highlighted {
                background: #e9ecef;
            }
            
            .search-empty-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 40px 20px;
                text-align: center;
            }
            
            .empty-icon {
                width: 64px;
                height: 64px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8f9fa;
                border-radius: 50%;
                color: #6c757d;
                font-size: 24px;
                margin-bottom: 16px;
            }
            
            .empty-text h3 {
                margin: 0 0 8px 0;
                color: #212529;
            }
            
            .empty-text p {
                margin: 0 0 20px 0;
                color: #6c757d;
            }
            
            .search-tips {
                display: flex;
                gap: 16px;
                font-size: 12px;
                color: #6c757d;
            }
            
            .search-tips kbd {
                padding: 2px 6px;
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                margin: 0 2px;
            }
            
            .search-no-results {
                padding: 40px 20px;
                text-align: center;
            }
            
            .no-results-icon {
                width: 64px;
                height: 64px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8f9fa;
                border-radius: 50%;
                color: #6c757d;
                font-size: 24px;
                margin: 0 auto 16px;
            }
            
            .no-results-text h3 {
                margin: 0 0 8px 0;
                color: #212529;
            }
            
            .no-results-text p {
                margin: 0 0 20px 0;
                color: #6c757d;
            }
            
            .search-suggestions-alt h4 {
                margin: 0 0 12px 0;
                font-size: 14px;
                color: #495057;
            }
            
            .suggestions-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                justify-content: center;
            }
            
            .suggestion-tag {
                padding: 6px 12px;
                background: #e9ecef;
                border-radius: 16px;
                font-size: 12px;
                color: #495057;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            
            .suggestion-tag:hover {
                background: #dee2e6;
            }
            
            .search-error {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
                padding: 40px;
                color: #dc3545;
            }
            
            /* 移动端适配 */
            @media (max-width: 768px) {
                .search-modal-container {
                    margin: 5vh 16px;
                    max-width: none;
                }
                
                .search-modal-header {
                    padding: 16px;
                }
                
                .search-input {
                    font-size: 16px;
                    padding: 14px 14px 14px 44px;
                }
                
                .search-shortcuts {
                    display: none;
                }
                
                .search-modal-body {
                    max-height: 50vh;
                }
            }
        `;
        
        document.head.appendChild(styles);
    }

    showSearchModal() {
        let modal = document.getElementById('advancedSearchModal');
        
        if (!modal) {
            modal = this.createSearchModal();
            document.body.appendChild(modal);
        }
        
        modal.classList.add('active');
        this.isSearchActive = true;
        
        // 聚焦搜索框
        const searchInput = modal.querySelector('.search-input');
        if (searchInput) {
            searchInput.focus();
        }
    }

    hideSearchModal() {
        const modal = document.getElementById('advancedSearchModal');
        if (modal) {
            modal.classList.remove('active');
        }
        this.isSearchActive = false;
    }

    createSearchModal() {
        const modal = document.createElement('div');
        modal.id = 'advancedSearchModal';
        modal.className = 'search-modal';
        
        modal.innerHTML = `
            <div class="search-overlay"></div>
            <div class="search-modal-container">
                <div class="search-modal-header">
                    <div class="search-input-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="全局搜索... (Ctrl+K)" autocomplete="off">
                        <div class="search-shortcuts">
                            <span class="shortcut">Ctrl</span>
                            <span>+</span>
                            <span class="shortcut">K</span>
                        </div>
                    </div>
                </div>
                <div class="search-modal-body">
                    <div class="search-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>搜索中...</span>
                    </div>
                    <div class="search-results"></div>
                    <div class="search-empty-state" style="display: none;">
                        <div class="empty-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="empty-text">
                            <h3>开始搜索</h3>
                            <p>输入关键词搜索用户、商品、订单等信息</p>
                        </div>
                        <div class="search-tips">
                            <div class="tip-item">
                                <kbd>↑</kbd><kbd>↓</kbd> 导航
                            </div>
                            <div class="tip-item">
                                <kbd>Enter</kbd> 选择
                            </div>
                            <div class="tip-item">
                                <kbd>Esc</kbd> 关闭
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return modal;
    }

    showSearchLoading(container) {
        const loading = container.querySelector('.search-loading');
        if (loading) {
            loading.style.display = 'flex';
        }
        
        const results = container.querySelector('.search-results');
        if (results) {
            results.style.display = 'none';
        }
    }

    showSearchResults(container) {
        const loading = container.querySelector('.search-loading');
        if (loading) {
            loading.style.display = 'none';
        }
        
        const results = container.querySelector('.search-results');
        if (results) {
            results.style.display = 'block';
        }
        
        const emptyState = container.querySelector('.search-empty-state');
        if (emptyState) {
            emptyState.style.display = 'none';
        }
    }

    hideSearchResults(container) {
        const results = container.querySelector('.search-results');
        if (results) {
            results.innerHTML = '';
            results.style.display = 'none';
        }
        
        const emptyState = container.querySelector('.search-empty-state');
        if (emptyState) {
            emptyState.style.display = 'flex';
        }
    }

    showNoResults(container, query) {
        container.innerHTML = `
            <div class="search-no-results">
                <div class="no-results-icon">
                    <i class="fas fa-search"></i>
                </div>
                <div class="no-results-text">
                    <h3>没有找到相关结果</h3>
                    <p>没有找到与 "${query}" 相关的内容</p>
                </div>
                <div class="search-suggestions-alt">
                    <h4>尝试搜索：</h4>
                    <div class="suggestions-tags">
                        <span class="suggestion-tag">用户管理</span>
                        <span class="suggestion-tag">商品列表</span>
                        <span class="suggestion-tag">订单统计</span>
                        <span class="suggestion-tag">系统设置</span>
                    </div>
                </div>
            </div>
        `;
    }

    handleResultClick(resultItem) {
        const type = resultItem.dataset.type;
        const id = resultItem.dataset.id;
        const url = resultItem.dataset.url;
        const action = resultItem.dataset.action;
        
        switch (type) {
            case 'user':
                this.navigateToUser(id);
                break;
            case 'product':
                this.navigateToProduct(id);
                break;
            case 'order':
                this.navigateToOrder(id);
                break;
            case 'page':
                this.navigateToPage(url);
                break;
            case 'function':
                this.executeSystemFunction(action);
                break;
        }
        
        this.hideSearchModal();
    }

    handleSuggestionClick(suggestionItem) {
        const suggestion = suggestionItem.dataset.suggestion;
        const searchInput = document.querySelector('.search-modal .search-input');
        
        if (searchInput) {
            searchInput.value = suggestion;
            searchInput.dispatchEvent(new Event('input'));
        }
    }

    navigateToUser(userId) {
        window.location.href = `/admin-backend/pages/user-management/user-detail.html?id=${userId}`;
    }

    navigateToProduct(productId) {
        window.location.href = `/admin-backend/pages/product-management/product-detail.html?id=${productId}`;
    }

    navigateToOrder(orderId) {
        window.location.href = `/admin-backend/pages/order-management/order-detail.html?id=${orderId}`;
    }

    navigateToPage(url) {
        window.location.href = url;
    }

    executeSystemFunction(action) {
        console.log(`执行系统功能: ${action}`);
        // 这里可以实现具体的系统功能调用
    }

    handleSearchFocus(e) {
        const container = e.target.closest('.search-container');
        if (container) {
            container.classList.add('search-focused');
        }
    }

    handleSearchBlur(e) {
        // 延迟隐藏，以便处理点击结果
        setTimeout(() => {
            const container = e.target.closest('.search-container');
            if (container && !container.matches(':hover')) {
                container.classList.remove('search-focused');
                this.hideSearchResults(container);
            }
        }, 200);
    }

    handleSearchKeydown(e) {
        const container = e.target.closest('.search-container') || 
                         document.querySelector('.search-modal');
        const results = container.querySelector('.search-results');
        
        if (!results) return;
        
        const items = results.querySelectorAll('.search-result-item, .search-suggestion');
        let currentIndex = Array.from(items).findIndex(item => item.classList.contains('highlighted'));
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentIndex = Math.min(currentIndex + 1, items.length - 1);
                this.highlightResultItem(items, currentIndex);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                currentIndex = Math.max(currentIndex - 1, 0);
                this.highlightResultItem(items, currentIndex);
                break;
                
            case 'Enter':
                e.preventDefault();
                if (currentIndex >= 0 && items[currentIndex]) {
                    items[currentIndex].click();
                }
                break;
        }
    }

    highlightResultItem(items, index) {
        items.forEach((item, i) => {
            item.classList.toggle('highlighted', i === index);
        });
        
        // 滚动到可见区域
        if (items[index]) {
            items[index].scrollIntoView({ block: 'nearest' });
        }
    }
}

// 创建全局搜索管理器实例
const advancedSearch = new AdvancedSearchManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedSearchManager;
} else {
    window.AdvancedSearchManager = AdvancedSearchManager;
    window.advancedSearch = advancedSearch;
} 