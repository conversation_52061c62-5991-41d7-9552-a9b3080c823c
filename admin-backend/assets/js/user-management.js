let selectedUsers = new Set();
let bulkActionsVisible = false;

// 搜索用户
function searchUsers(query) {
    if (query.trim()) {
        showNotification(`搜索用户: ${query}`, 'info');
    }
}

// 按状态过滤
function filterByStatus(status) {
    showNotification(`已过滤到 ${status || '全部'} 状态用户`, 'info');
}

// 按角色过滤
function filterByRole(role) {
    showNotification(`已过滤到 ${role || '全部'} 角色用户`, 'info');
}

// 按来源过滤
function filterBySource(source) {
    showNotification(`已过滤到 ${source || '全部'} 来源用户`, 'info');
}

// 按注册时间过滤
function filterByRegistration(time) {
    showNotification(`已过滤到 ${time || '全部'} 时间用户`, 'info');
}

// 刷新用户列表
function refreshUsers() {
    showNotification('正在刷新用户列表...', 'info');
    setTimeout(() => {
        showNotification('用户列表已更新！', 'success');
    }, 1500);
}
// 切换批量操作
function toggleBulkActions() {
    bulkActionsVisible = !bulkActionsVisible;
    const bulkActions = document.getElementById('bulkActions');
    if (bulkActionsVisible) {
        bulkActions.classList.add('show');
    } else {
        bulkActions.classList.remove('show');
        // 取消所有选择
        document.querySelectorAll('.user-checkbox').forEach(cb => cb.checked = false);
        selectedUsers.clear();
        updateSelectedCount();
    }
}

// 全选/取消全选
function toggleSelectAll(checked) {
    document.querySelectorAll('.user-checkbox').forEach(cb => {
        cb.checked = checked;
        const userId = cb.getAttribute('data-user-id');
        if (checked) {
            selectedUsers.add(userId);
        } else {
            selectedUsers.delete(userId);
        }
    });
    updateSelectedCount();
}

// 更新选中数量
function updateSelectedCount() {
    document.getElementById('selectedCount').textContent = selectedUsers.size;
    
    // 自动显示/隐藏批量操作栏
    const bulkActions = document.getElementById('bulkActions');
    if (selectedUsers.size > 0) {
        bulkActions.classList.add('show');
        bulkActionsVisible = true;
    } else {
        bulkActions.classList.remove('show');
        bulkActionsVisible = false;
    }
}

// 单个用户选择
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('user-checkbox')) {
        const userId = e.target.getAttribute('data-user-id');
        if (e.target.checked) {
            selectedUsers.add(userId);
        } else {
            selectedUsers.delete(userId);
        }
        updateSelectedCount();
    }
});
// 查看用户详情
function viewUser(userId) {
    showNotification(`正在打开用户 ${userId} 详情页面...`, 'info');
    setTimeout(() => {
        window.open(`user-detail.html?id=${userId}`, '_blank');
    }, 500);
}

// 编辑用户
function editUser(userId) {
    showNotification(`正在打开用户 ${userId} 编辑页面...`, 'info');
    setTimeout(() => {
        window.open(`edit-user.html?id=${userId}`, '_blank');
    }, 500);
}

// 登录为用户
function loginAsUser(userId) {
    if (confirm('确定要以该用户身份登录吗？')) {
        showNotification(`正在切换到用户 ${userId}...`, 'info');
    }
}

// 封禁用户
function banUser(userId) {
    if (confirm('确定要封禁这个用户吗？')) {
        showNotification(`用户 ${userId} 已被封禁`, 'success');
    }
}

// 解封用户
function unbanUser(userId) {
    if (confirm('确定要解封这个用户吗？')) {
        showNotification(`用户 ${userId} 已解封`, 'success');
    }
}

// 删除用户
function deleteUser(userId) {
    if (confirm('确定要删除这个用户吗？此操作不可恢复！')) {
        showNotification(`用户 ${userId} 已删除`, 'success');
    }
}

// 验证用户
function verifyUser(userId) {
    showNotification(`用户 ${userId} 已验证`, 'success');
}

// 升级为VIP
function upgradeToVip(userId) {
    if (confirm('确定要将该用户升级为VIP吗？')) {
        showNotification(`用户 ${userId} 已升级为VIP`, 'success');
    }
}

// 重置密码
function resetPassword(userId) {
    if (confirm('确定要重置该用户密码吗？')) {
        showNotification(`用户 ${userId} 密码重置邮件已发送`, 'success');
    }
}

// 权限管理
function managePermissions(userId) {
    showNotification(`打开用户 ${userId} 权限管理`, 'info');
}

// 添加用户
function addUser() {
    showNotification('打开添加用户对话框', 'info');
}
// 导出用户
function exportUsers() {
    showExportModal();
}

// 导入用户
function importUsers() {
    showImportModal();
}

// 批量操作
function bulkExport() {
    if (selectedUsers.size === 0) {
        showNotification('请先选择要导出的用户', 'warning');
        return;
    }
    showNotification(`正在导出 ${selectedUsers.size} 个用户数据...`, 'info');
    
    // 模拟导出过程
    setTimeout(() => {
        // 创建并下载CSV文件
        const csvData = generateUserCSV(Array.from(selectedUsers));
        downloadCSV(csvData, `users_export_${new Date().toISOString().split('T')[0]}.csv`);
        showNotification(`成功导出 ${selectedUsers.size} 个用户数据`, 'success');
    }, 2000);
}

// 生成CSV数据
function generateUserCSV(userIds) {
    const headers = ['用户ID', '用户名', '邮箱', '状态', '角色', '注册时间'];
    const csvContent = [
        headers.join(','),
        ...userIds.map(id => `${id},用户${id},user${id}@example.com,活跃,普通用户,2024-01-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`)
    ].join('\n');
    return csvContent;
}

// 下载CSV文件
function downloadCSV(csvContent, filename) {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function bulkActivate() {
    if (selectedUsers.size === 0) {
        showNotification('请先选择要激活的用户', 'warning');
        return;
    }
    showNotification(`已激活 ${selectedUsers.size} 个用户`, 'success');
}

function bulkDeactivate() {
    if (selectedUsers.size === 0) {
        showNotification('请先选择要停用的用户', 'warning');
        return;
    }
    showNotification(`已停用 ${selectedUsers.size} 个用户`, 'success');
}

function bulkDelete() {
    if (selectedUsers.size === 0) {
        showNotification('请先选择要删除的用户', 'warning');
        return;
    }
    if (confirm(`确定要删除选中的 ${selectedUsers.size} 个用户吗？此操作不可恢复！`)) {
        showNotification(`已删除 ${selectedUsers.size} 个用户`, 'success');
        selectedUsers.clear();
        updateSelectedCount();
    }
}
// 通知系统
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg text-white text-sm font-medium transition-all duration-300 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'warning' ? 'bg-orange-500' : 'bg-blue-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// ==================== 用户导出功能 ====================

function showExportModal() {
    const modal = createModal('导出用户数据', `
        <div class="export-options">
            <div class="form-group" style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #374151;">导出格式：</label>
                <select id="exportFormat" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                    <option value="xlsx">Excel 文件 (.xlsx)</option>
                    <option value="csv">CSV 文件 (.csv)</option>
                    <option value="json">JSON 文件 (.json)</option>
                    <option value="pdf">PDF 报告 (.pdf)</option>
                </select>
            </div>
            
            <div class="form-group" style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #374151;">导出范围：</label>
                <div class="radio-group" style="display: flex; flex-direction: column; gap: 8px;">
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="radio" name="exportRange" value="all" checked style="margin: 0;">
                        <span>所有用户 (12,847个)</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="radio" name="exportRange" value="filtered" style="margin: 0;">
                        <span>当前筛选结果</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="radio" name="exportRange" value="active" style="margin: 0;">
                        <span>仅活跃用户 (8,234个)</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="radio" name="exportRange" value="vip" style="margin: 0;">
                        <span>仅VIP用户 (567个)</span>
                    </label>
                </div>
            </div>
            
            <div class="form-group" style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #374151;">包含字段：</label>
                <div class="checkbox-group" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" checked style="margin: 0;">
                        <span>用户ID</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" checked style="margin: 0;">
                        <span>用户名</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" checked style="margin: 0;">
                        <span>邮箱地址</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" checked style="margin: 0;">
                        <span>手机号码</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" checked style="margin: 0;">
                        <span>用户状态</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" checked style="margin: 0;">
                        <span>用户角色</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" style="margin: 0;">
                        <span>钱包余额</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" style="margin: 0;">
                        <span>注册来源</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" checked style="margin: 0;">
                        <span>注册时间</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" style="margin: 0;">
                        <span>最后活跃时间</span>
                    </label>
                </div>
            </div>
            
            <div class="form-group" style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #374151;">时间范围：</label>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                    <div>
                        <label style="display: block; margin-bottom: 4px; font-size: 12px; color: #6b7280;">开始日期</label>
                        <input type="date" id="exportStartDate" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 4px; font-size: 12px; color: #6b7280;">结束日期</label>
                        <input type="date" id="exportEndDate" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                    </div>
                </div>
            </div>
        </div>
        
        <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <button onclick="closeModal()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; font-size: 14px; cursor: pointer;">取消</button>
            <button onclick="executeExport()" style="padding: 8px 16px; border: 1px solid #3b82f6; border-radius: 6px; background: #3b82f6; color: white; font-size: 14px; cursor: pointer;">开始导出</button>
        </div>
    `);
    
    // 设置默认日期
    const today = new Date();
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    document.getElementById('exportStartDate').value = lastMonth.toISOString().split('T')[0];
    document.getElementById('exportEndDate').value = today.toISOString().split('T')[0];
}

function executeExport() {
    const format = document.getElementById('exportFormat').value;
    const range = document.querySelector('input[name="exportRange"]:checked').value;
    const fields = Array.from(document.querySelectorAll('.checkbox-group input:checked')).map(cb => cb.parentElement.textContent.trim());
    const startDate = document.getElementById('exportStartDate').value;
    const endDate = document.getElementById('exportEndDate').value;
    
    closeModal();
    
    // 显示导出进度
    showExportProgress(format, range, fields, startDate, endDate);
}

function showExportProgress(format, range, fields, startDate, endDate) {
    const progressModal = createModal('导出进度', `
        <div class="export-progress">
            <div style="text-align: center; margin-bottom: 20px;">
                <div class="progress-icon" style="width: 60px; height: 60px; margin: 0 auto 16px; border: 4px solid #e5e7eb; border-top: 4px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                <h3 style="margin: 0 0 8px 0; color: #1f2937;">正在导出用户数据...</h3>
                <p style="margin: 0; color: #6b7280; font-size: 14px;">请稍候，这可能需要几分钟时间</p>
            </div>
            
            <div class="progress-bar" style="width: 100%; height: 8px; background: #f3f4f6; border-radius: 4px; overflow: hidden; margin-bottom: 16px;">
                <div id="progressFill" style="width: 0%; height: 100%; background: #3b82f6; transition: width 0.3s ease;"></div>
            </div>
            
            <div id="progressText" style="text-align: center; font-size: 14px; color: #6b7280;">准备导出数据...</div>
        
            <div style="margin-top: 20px; padding: 16px; background: #f9fafb; border-radius: 6px; border: 1px solid #e5e7eb;">
                <h4 style="margin: 0 0 8px 0; font-size: 14px; color: #374151;">导出设置</h4>
                <div style="font-size: 12px; color: #6b7280; line-height: 1.5;">
                    <div><strong>格式：</strong> ${format.toUpperCase()}</div>
                    <div><strong>范围：</strong> ${getRangeText(range)}</div>
                    <div><strong>字段：</strong> ${fields.length} 个字段</div>
                    <div><strong>时间：</strong> ${startDate} 至 ${endDate}</div>
                </div>
            </div>
        </div>
        
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `);
    
    // 模拟导出进度
    let progress = 0;
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    
    const steps = [
        { progress: 10, text: '正在查询用户数据...' },
        { progress: 30, text: '正在处理用户信息...' },
        { progress: 50, text: '正在生成导出文件...' },
        { progress: 70, text: '正在压缩数据...' },
        { progress: 90, text: '正在准备下载...' },
        { progress: 100, text: '导出完成！' }
    ];
    
    let stepIndex = 0;
    const updateProgress = () => {
        if (stepIndex < steps.length) {
            const step = steps[stepIndex];
            progressFill.style.width = step.progress + '%';
            progressText.textContent = step.text;
            stepIndex++;
            
            if (stepIndex < steps.length) {
                setTimeout(updateProgress, 1000 + Math.random() * 1000);
            } else {
                // 导出完成
                setTimeout(() => {
                    closeModal();
                    downloadFile(format, range, fields);
                    showNotification(`${format.toUpperCase()} 文件导出成功！`, 'success');
                }, 1000);
            }
        }
    };
    
    setTimeout(updateProgress, 500);
}

function getRangeText(range) {
    const rangeMap = {
        'all': '所有用户',
        'filtered': '当前筛选结果', 
        'active': '仅活跃用户',
        'vip': '仅VIP用户'
    };
    return rangeMap[range] || range;
}

function downloadFile(format, range, fields) {
    // 模拟文件下载
    const filename = `用户数据_${range}_${new Date().toISOString().split('T')[0]}.${format}`;
    
    if (format === 'csv') {
        const csvData = generateAdvancedUserCSV(range, fields);
        downloadCSV(csvData, filename);
    } else if (format === 'json') {
        const jsonData = generateUserJSON(range, fields);
        downloadJSON(jsonData, filename);
    } else {
        // Excel 和 PDF 格式的模拟下载
        console.log(`下载 ${format} 文件: ${filename}`);
    }
}

function generateAdvancedUserCSV(range, fields) {
    const headers = fields;
    const sampleData = [
        ['1', 'Alice Chen', '<EMAIL>', '13812345678', '活跃', 'VIP', '¥12,458.50', 'Telegram', '2024-01-15', '刚刚'],
        ['2', 'Bob Wang', '<EMAIL>', '13823456789', '待验证', '商家', '¥8,234.00', 'Web', '2024-02-20', '8分钟前'],
        ['3', 'Charlie Li', '<EMAIL>', '13834567890', '活跃', '用户', '¥567.80', '推荐', '2024-03-10', '2小时前']
    ];
    
    return [headers.join(','), ...sampleData.map(row => row.join(','))].join('\n');
}

function generateUserJSON(range, fields) {
    const userData = [
        {
            id: 1,
            name: 'Alice Chen',
            email: '<EMAIL>',
            phone: '13812345678',
            status: 'active',
            role: 'vip',
            balance: 12458.50,
            source: 'telegram',
            registerTime: '2024-01-15',
            lastActive: '刚刚'
        },
        {
            id: 2,
            name: 'Bob Wang', 
            email: '<EMAIL>',
            phone: '13823456789',
            status: 'pending',
            role: 'seller',
            balance: 8234.00,
            source: 'web',
            registerTime: '2024-02-20',
            lastActive: '8分钟前'
        }
    ];
    
    return JSON.stringify({
        exportTime: new Date().toISOString(),
        range: range,
        fields: fields,
        totalCount: userData.length,
        data: userData
    }, null, 2);
}

function downloadJSON(jsonContent, filename) {
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// ==================== 用户导入功能 ====================

function showImportModal() {
    const modal = createModal('批量导入用户', `
        <div class="import-options">
            <div class="import-step" id="step1">
                <h4 style="margin: 0 0 16px 0; color: #1f2937;">第一步：选择导入文件</h4>
                
                <div class="file-upload-area" style="border: 2px dashed #d1d5db; border-radius: 8px; padding: 40px 20px; text-align: center; background: #f9fafb; cursor: pointer;" onclick="document.getElementById('importFile').click()">
                    <div style="font-size: 48px; color: #9ca3af; margin-bottom: 16px;">📁</div>
                    <h3 style="margin: 0 0 8px 0; color: #374151;">点击选择文件或拖拽文件到此处</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">支持 Excel (.xlsx), CSV (.csv) 格式</p>
                    <input type="file" id="importFile" accept=".xlsx,.csv" style="display: none;" onchange="handleFileSelect(this)">
                </div>
                
                <div id="selectedFile" style="display: none; margin-top: 16px; padding: 12px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="font-size: 20px;">📄</span>
                        <div>
                            <div id="fileName" style="font-weight: 500; color: #0f172a;"></div>
                            <div id="fileSize" style="font-size: 12px; color: #64748b;"></div>
                        </div>
                        <button onclick="clearFile()" style="margin-left: auto; padding: 4px 8px; border: none; background: #ef4444; color: white; border-radius: 4px; cursor: pointer;">删除</button>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4 style="margin: 0 0 12px 0; color: #1f2937;">导入设置</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                        <div>
                            <label style="display: block; margin-bottom: 4px; font-size: 14px; color: #374151;">重复处理方式</label>
                            <select id="duplicateHandling" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="skip">跳过重复用户</option>
                                <option value="update">更新已有用户</option>
                                <option value="error">报错停止</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 4px; font-size: 14px; color: #374151;">默认用户角色</label>
                            <select id="defaultRole" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="user">普通用户</option>
                                <option value="vip">VIP用户</option>
                                <option value="seller">商家</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                        <input type="checkbox" id="sendWelcomeEmail" checked style="margin: 0;">
                        <span style="font-size: 14px; color: #374151;">发送欢迎邮件给新用户</span>
                    </label>
                </div>
                
                <div style="margin-top: 20px; padding: 16px; background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px;">
                    <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 14px;">📋 导入模板说明</h4>
                    <p style="margin: 0; font-size: 12px; color: #92400e; line-height: 1.5;">
                        Excel/CSV 文件应包含以下列：<br>
                        <strong>必需：</strong>用户名, 邮箱地址<br>
                        <strong>可选：</strong>手机号码, 用户角色, 注册来源, 初始余额
                    </p>
                    <button onclick="downloadTemplate()" style="margin-top: 8px; padding: 4px 8px; border: 1px solid #f59e0b; background: white; color: #f59e0b; border-radius: 4px; font-size: 12px; cursor: pointer;">下载模板文件</button>
                </div>
            </div>
            
            <div class="import-step" id="step2" style="display: none;">
                <h4 style="margin: 0 0 16px 0; color: #1f2937;">第二步：数据预览与验证</h4>
                
                <div id="dataPreview" style="border: 1px solid #e5e7eb; border-radius: 6px; overflow: hidden;">
                    <!-- 数据预览表格将在这里显示 -->
                </div>
                
                <div id="validationResults" style="margin-top: 16px;">
                    <!-- 验证结果将在这里显示 -->
                </div>
            </div>
            
            <div class="import-step" id="step3" style="display: none;">
                <h4 style="margin: 0 0 16px 0; color: #1f2937;">第三步：导入进度</h4>
                
                <div style="text-align: center; margin-bottom: 20px;">
                    <div class="progress-icon" style="width: 60px; height: 60px; margin: 0 auto 16px; border: 4px solid #e5e7eb; border-top: 4px solid #10b981; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    <h3 style="margin: 0 0 8px 0; color: #1f2937;">正在导入用户数据...</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">请稍候，正在处理您的数据</p>
                </div>
                
                <div class="progress-bar" style="width: 100%; height: 8px; background: #f3f4f6; border-radius: 4px; overflow: hidden; margin-bottom: 16px;">
                    <div id="importProgressFill" style="width: 0%; height: 100%; background: #10b981; transition: width 0.3s ease;"></div>
                </div>
                
                <div id="importProgressText" style="text-align: center; font-size: 14px; color: #6b7280;">准备导入数据...</div>
            
                <div id="importResults" style="margin-top: 20px; display: none;">
                    <!-- 导入结果将在这里显示 -->
                </div>
            </div>
        </div>
        
        <div style="display: flex; justify-content: space-between; margin-top: 24px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <button id="prevBtn" onclick="previousStep()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; font-size: 14px; cursor: pointer; display: none;">上一步</button>
            <div style="margin-left: auto; display: flex; gap: 12px;">
                <button onclick="closeModal()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; font-size: 14px; cursor: pointer;">取消</button>
                <button id="nextBtn" onclick="nextStep()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: #f3f4f6; color: #9ca3af; font-size: 14px; cursor: not-allowed;" disabled>下一步</button>
            </div>
        </div>
    `);
}

let currentStep = 1;
let importData = null;

function handleFileSelect(input) {
    const file = input.files[0];
    if (file) {
        document.getElementById('selectedFile').style.display = 'block';
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = `${(file.size / 1024).toFixed(1)} KB`;
        document.getElementById('nextBtn').disabled = false;
        document.getElementById('nextBtn').style.background = '#3b82f6';
        document.getElementById('nextBtn').style.color = 'white';
        document.getElementById('nextBtn').style.cursor = 'pointer';
        
        // 模拟文件解析
        parseImportFile(file);
    }
}

function clearFile() {
    document.getElementById('importFile').value = '';
    document.getElementById('selectedFile').style.display = 'none';
    document.getElementById('nextBtn').disabled = true;
    document.getElementById('nextBtn').style.background = '#f3f4f6';
    document.getElementById('nextBtn').style.color = '#9ca3af';
    document.getElementById('nextBtn').style.cursor = 'not-allowed';
    importData = null;
}

function parseImportFile(file) {
    // 模拟文件解析
    importData = {
        headers: ['用户名', '邮箱地址', '手机号码', '用户角色'],
        rows: [
            ['张三', '<EMAIL>', '13812345678', '用户'],
            ['李四', '<EMAIL>', '13823456789', 'VIP'],
            ['王五', '<EMAIL>', '13834567890', '商家'],
            ['赵六', '<EMAIL>', '13845678901', '用户']
        ],
        validRows: 4,
        invalidRows: 0,
        duplicates: 0
    };
}

function nextStep() {
    if (currentStep === 1 && importData) {
        showStep(2);
        showDataPreview();
    } else if (currentStep === 2) {
        showStep(3);
        executeImport();
    }
}

function previousStep() {
    if (currentStep > 1) {
        showStep(currentStep - 1);
    }
}

function showStep(step) {
    // 隐藏所有步骤
    for (let i = 1; i <= 3; i++) {
        document.getElementById(`step${i}`).style.display = 'none';
    }
    
    // 显示当前步骤
    document.getElementById(`step${step}`).style.display = 'block';
    currentStep = step;
    
    // 更新按钮状态
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    prevBtn.style.display = step > 1 ? 'block' : 'none';
    
    if (step === 3) {
        nextBtn.style.display = 'none';
    } else {
        nextBtn.style.display = 'block';
        nextBtn.textContent = step === 2 ? '开始导入' : '下一步';
    }
}

function showDataPreview() {
    const preview = document.getElementById('dataPreview');
    const validation = document.getElementById('validationResults');
    
    // 显示数据预览表格
    preview.innerHTML = `
        <table style="width: 100%; border-collapse: collapse;">
            <thead style="background: #f9fafb;">
                <tr>
                    ${importData.headers.map(header => `<th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 500; color: #374151;">${header}</th>`).join('')}
                </tr>
            </thead>
            <tbody>
                ${importData.rows.slice(0, 5).map(row => `
                    <tr>
                        ${row.map(cell => `<td style="padding: 12px; border-bottom: 1px solid #f3f4f6; color: #1f2937;">${cell}</td>`).join('')}
                    </tr>
                `).join('')}
            </tbody>
        </table>
        ${importData.rows.length > 5 ? `<div style="padding: 12px; text-align: center; color: #6b7280; font-size: 14px; border-top: 1px solid #e5e7eb;">... 还有 ${importData.rows.length - 5} 行数据</div>` : ''}
    `;
    
    // 显示验证结果
    validation.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
            <div style="padding: 16px; background: #f0fdf4; border: 1px solid #22c55e; border-radius: 6px; text-align: center;">
                <div style="font-size: 24px; font-weight: 700; color: #16a34a;">${importData.validRows}</div>
                <div style="font-size: 14px; color: #15803d;">有效记录</div>
            </div>
            <div style="padding: 16px; background: #fef2f2; border: 1px solid #ef4444; border-radius: 6px; text-align: center;">
                <div style="font-size: 24px; font-weight: 700; color: #dc2626;">${importData.invalidRows}</div>
                <div style="font-size: 14px; color: #dc2626;">无效记录</div>
            </div>
            <div style="padding: 16px; background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; text-align: center;">
                <div style="font-size: 24px; font-weight: 700; color: #d97706;">${importData.duplicates}</div>
                <div style="font-size: 14px; color: #d97706;">重复记录</div>
            </div>
        </div>
        
        <div style="margin-top: 16px; padding: 12px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px;">
            <div style="font-size: 14px; color: #0f172a;">✅ 数据验证通过，可以开始导入</div>
        </div>
    `;
}

function executeImport() {
    const progressFill = document.getElementById('importProgressFill');
    const progressText = document.getElementById('importProgressText');
    const results = document.getElementById('importResults');
    
    const steps = [
        { progress: 20, text: '正在验证用户数据...' },
        { progress: 40, text: '正在检查重复用户...' },
        { progress: 60, text: '正在创建用户账号...' },
        { progress: 80, text: '正在发送欢迎邮件...' },
        { progress: 100, text: '导入完成！' }
    ];
    
    let stepIndex = 0;
    const updateProgress = () => {
        if (stepIndex < steps.length) {
            const step = steps[stepIndex];
            progressFill.style.width = step.progress + '%';
            progressText.textContent = step.text;
            stepIndex++;
            
            if (stepIndex < steps.length) {
                setTimeout(updateProgress, 800 + Math.random() * 400);
            } else {
                // 导入完成
                setTimeout(() => {
                    results.style.display = 'block';
                    results.innerHTML = `
                        <div style="text-align: center; padding: 20px;">
                            <div style="font-size: 48px; margin-bottom: 16px;">🎉</div>
                            <h3 style="margin: 0 0 8px 0; color: #16a34a;">导入成功完成！</h3>
                            <p style="margin: 0; color: #6b7280; font-size: 14px;">共导入 ${importData.validRows} 个用户</p>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; margin-top: 20px;">
                            <div style="padding: 16px; background: #f0fdf4; border-radius: 6px; text-align: center;">
                                <div style="font-size: 20px; font-weight: 600; color: #16a34a;">${importData.validRows}</div>
                                <div style="font-size: 12px; color: #15803d;">成功导入</div>
                            </div>
                            <div style="padding: 16px; background: #fef3c7; border-radius: 6px; text-align: center;">
                                <div style="font-size: 20px; font-weight: 600; color: #d97706;">${importData.duplicates}</div>
                                <div style="font-size: 12px; color: #d97706;">跳过重复</div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 20px; text-align: center;">
                            <button onclick="closeModal(); refreshUsers();" style="padding: 8px 24px; border: 1px solid #16a34a; border-radius: 6px; background: #16a34a; color: white; font-size: 14px; cursor: pointer;">完成</button>
                        </div>
                    `;
                    showNotification('用户批量导入成功！', 'success');
                }, 1000);
            }
        }
    };
    
    setTimeout(updateProgress, 500);
}

function downloadTemplate() {
    const templateData = [
        ['用户名', '邮箱地址', '手机号码', '用户角色', '注册来源', '初始余额'],
        ['张三', '<EMAIL>', '13812345678', '用户', 'Web', '0'],
        ['李四', '<EMAIL>', '13823456789', 'VIP', 'Telegram', '100'],
        ['王五', '<EMAIL>', '13834567890', '商家', '推荐', '500']
    ];
    
    const csvContent = templateData.map(row => row.join(',')).join('\n');
    downloadCSV(csvContent, '用户导入模板.csv');
    showNotification('模板文件下载成功！', 'success');
}

// ==================== 通用模态框功能 ====================

function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'custom-modal';
    modal.style.cssText = `
        position: fixed; top: 0; left: 0; right: 0; bottom: 0;
        background: rgba(0,0,0,0.5); z-index: 1000;
        display: flex; align-items: center; justify-content: center;
        padding: 20px;
    `;
    
    modal.innerHTML = `
        <div class="modal-content" style="background: white; border-radius: 12px; max-width: 800px; max-height: 90vh; overflow-y: auto; width: 100%; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);">
            <div class="modal-header" style="padding: 24px 24px 0 24px; border-bottom: 1px solid #e5e7eb; margin-bottom: 24px;">
                <h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #1f2937; padding-bottom: 16px;">${title}</h3>
            </div>
            <div class="modal-body" style="padding: 0 24px 24px 24px;">
                ${content}
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    return modal;
}

function closeModal() {
    const modal = document.querySelector('.custom-modal');
    if (modal) {
        modal.remove();
        currentStep = 1;
        importData = null;
    }
}
