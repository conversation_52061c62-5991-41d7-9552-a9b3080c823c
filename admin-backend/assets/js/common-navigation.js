// TeleShop 后台管理系统 - 通用导航组件
class AdminNavigation {
    constructor() {
        this.init();
    }

    init() {
        this.createTopNavigation();
        this.bindEvents();
        this.setActiveMenuItem();
    }

    // 创建顶部导航栏
    createTopNavigation() {
        // 如果页面已有导航，则不创建
        if (document.querySelector('.admin-top-nav')) return;

        const navHTML = `
            <div class="admin-top-nav" style="
                background: white; 
                padding: 12px 24px; 
                border-bottom: 1px solid #e2e8f0; 
                position: sticky; 
                top: 0; 
                z-index: 1000;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            ">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 24px;">
                        <div class="logo" style="display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-shopping-cart" style="font-size: 24px; color: #3b82f6;"></i>
                            <span style="font-size: 20px; font-weight: 700; color: #1e293b;">TeleShop</span>
                        </div>
                        
                        <nav class="quick-nav" style="display: flex; gap: 16px;">
                            <a href="../dashboard/index.html" class="nav-item" data-section="dashboard">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>仪表板</span>
                            </a>
                            <a href="../user-management/index.html" class="nav-item" data-section="users">
                                <i class="fas fa-users"></i>
                                <span>用户</span>
                            </a>
                            <a href="../content-management/message-content-audit.html" class="nav-item" data-section="content">
                                <i class="fas fa-shield-alt"></i>
                                <span>内容</span>
                            </a>
                            <a href="../order-management/index.html" class="nav-item" data-section="orders">
                                <i class="fas fa-shopping-bag"></i>
                                <span>订单</span>
                            </a>
                            <a href="../data-analytics/index.html" class="nav-item" data-section="analytics">
                                <i class="fas fa-chart-bar"></i>
                                <span>分析</span>
                            </a>
                            <a href="../system-settings/index.html" class="nav-item" data-section="settings">
                                <i class="fas fa-cogs"></i>
                                <span>设置</span>
                            </a>
                        </nav>
                    </div>
                    
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <button class="notification-btn" onclick="adminNav.showNotifications()" style="
                            background: none; border: none; font-size: 18px; color: #64748b; 
                            cursor: pointer; padding: 8px; border-radius: 4px; position: relative;
                        ">
                            <i class="fas fa-bell"></i>
                            <span style="
                                position: absolute; top: 4px; right: 4px; background: #ef4444; 
                                color: white; font-size: 10px; padding: 2px 5px; border-radius: 10px;
                                min-width: 16px; text-align: center;
                            ">3</span>
                        </button>
                        
                        <div class="user-menu" style="
                            display: flex; align-items: center; gap: 8px; cursor: pointer; 
                            padding: 8px; border-radius: 6px; transition: background 0.2s;
                        " onclick="adminNav.showUserMenu()">
                            <img src="../../assets/images/avatar-default.png" alt="用户头像" style="
                                width: 32px; height: 32px; border-radius: 50%; background: #e2e8f0;
                            ">
                            <div>
                                <div style="font-size: 14px; font-weight: 600; color: #1e293b;">管理员</div>
                                <div style="font-size: 12px; color: #64748b;">超级管理员</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 在body开头插入导航
        document.body.insertAdjacentHTML('afterbegin', navHTML);

        // 添加CSS样式
        this.addNavigationStyles();
    }

    // 添加导航样式
    addNavigationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .admin-top-nav .nav-item {
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 8px 12px;
                color: #64748b;
                text-decoration: none;
                font-size: 14px;
                font-weight: 500;
                border-radius: 6px;
                transition: all 0.2s;
            }
            
            .admin-top-nav .nav-item:hover {
                background: #f1f5f9;
                color: #1e293b;
                text-decoration: none;
            }
            
            .admin-top-nav .nav-item.active {
                background: #dbeafe;
                color: #1d4ed8;
            }
            
            .admin-top-nav .nav-item i {
                width: 16px;
                text-align: center;
            }
            
            .admin-top-nav .notification-btn:hover {
                background: #f1f5f9;
            }
            
            .admin-top-nav .user-menu:hover {
                background: #f1f5f9;
            }
            
            /* 响应式处理 */
            @media (max-width: 768px) {
                .admin-top-nav .quick-nav {
                    display: none;
                }
                
                .admin-top-nav .nav-item span {
                    display: none;
                }
            }
            
            @media (max-width: 480px) {
                .admin-top-nav {
                    padding: 8px 16px !important;
                }
                
                .admin-top-nav .logo span {
                    display: none;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 设置当前激活的菜单项
    setActiveMenuItem() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.admin-top-nav .nav-item');
        
        navItems.forEach(item => {
            item.classList.remove('active');
            const href = item.getAttribute('href');
            if (currentPath.includes(href.split('/').pop().split('.')[0])) {
                item.classList.add('active');
            }
        });
    }

    // 绑定事件
    bindEvents() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // 监听页面切换
        document.addEventListener('click', (e) => {
            if (e.target.closest('.nav-item')) {
                this.handleNavigation(e);
            }
        });
    }

    // 处理窗口大小变化
    handleResize() {
        // 可以在这里添加响应式处理逻辑
    }

    // 处理页面导航
    handleNavigation(e) {
        const navItem = e.target.closest('.nav-item');
        if (!navItem) return;

        // 移除所有active状态
        document.querySelectorAll('.admin-top-nav .nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // 添加active状态到当前项
        navItem.classList.add('active');
    }

    // 显示通知
    showNotifications() {
        // 创建通知弹窗
        const notifications = [
            { id: 1, title: '新用户注册', message: '5分钟前有3位新用户注册', time: '5分钟前', type: 'info' },
            { id: 2, title: '订单异常', message: '订单 #12345 需要人工处理', time: '10分钟前', type: 'warning' },
            { id: 3, title: '系统更新', message: '系统将在今晚23:00进行维护', time: '1小时前', type: 'info' }
        ];

        this.showModal('通知中心', this.createNotificationContent(notifications));
    }

    // 创建通知内容
    createNotificationContent(notifications) {
        return `
            <div style="max-height: 400px; overflow-y: auto;">
                ${notifications.map(notif => `
                    <div style="
                        padding: 16px; border-bottom: 1px solid #f1f5f9; 
                        display: flex; align-items: flex-start; gap: 12px;
                    ">
                        <div style="
                            width: 8px; height: 8px; border-radius: 50%; 
                            background: ${notif.type === 'warning' ? '#f59e0b' : '#3b82f6'}; 
                            margin-top: 8px; flex-shrink: 0;
                        "></div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: #1e293b; margin-bottom: 4px;">
                                ${notif.title}
                            </div>
                            <div style="color: #64748b; font-size: 14px; margin-bottom: 8px;">
                                ${notif.message}
                            </div>
                            <div style="color: #94a3b8; font-size: 12px;">
                                ${notif.time}
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div style="
                padding: 16px; border-top: 1px solid #f1f5f9; 
                text-align: center; background: #f8fafc;
            ">
                <a href="../content-management/notification-system.html" style="
                    color: #3b82f6; text-decoration: none; font-size: 14px; font-weight: 500;
                ">查看所有通知</a>
            </div>
        `;
    }

    // 显示用户菜单
    showUserMenu() {
        const menuItems = [
            { icon: 'fas fa-user', text: '个人资料', action: () => console.log('个人资料') },
            { icon: 'fas fa-cog', text: '账户设置', action: () => console.log('账户设置') },
            { icon: 'fas fa-shield-alt', text: '安全设置', action: () => window.location.href = '../system-settings/security-management.html' },
            { icon: 'fas fa-sign-out-alt', text: '退出登录', action: () => this.logout() }
        ];

        this.showModal('用户菜单', this.createUserMenuContent(menuItems));
    }

    // 创建用户菜单内容
    createUserMenuContent(menuItems) {
        return `
            <div>
                ${menuItems.map(item => `
                    <div onclick="adminNav.handleUserMenuAction('${item.text}')" style="
                        padding: 12px 16px; cursor: pointer; display: flex; 
                        align-items: center; gap: 12px; transition: background 0.2s;
                        border-radius: 6px; margin-bottom: 4px;
                    " onmouseover="this.style.background='#f1f5f9'" 
                       onmouseout="this.style.background='transparent'">
                        <i class="${item.icon}" style="width: 16px; color: #64748b;"></i>
                        <span style="color: #1e293b;">${item.text}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    // 处理用户菜单操作
    handleUserMenuAction(action) {
        this.closeModal();
        
        switch(action) {
            case '个人资料':
                console.log('打开个人资料');
                break;
            case '账户设置':
                console.log('打开账户设置');
                break;
            case '安全设置':
                window.location.href = '../system-settings/security-management.html';
                break;
            case '退出登录':
                this.logout();
                break;
        }
    }

    // 退出登录
    logout() {
        if (confirm('确定要退出登录吗？')) {
            // 清除登录状态
            localStorage.removeItem('adminToken');
            sessionStorage.clear();
            
            // 跳转到登录页面
            window.location.href = '../../../login.html';
        }
    }

    // 显示模态框
    showModal(title, content) {
        // 如果已存在模态框，先移除
        this.closeModal();

        const modalHTML = `
            <div class="admin-modal-overlay" style="
                position: fixed; top: 0; left: 0; right: 0; bottom: 0; 
                background: rgba(0, 0, 0, 0.5); z-index: 10000; 
                display: flex; align-items: center; justify-content: center;
                animation: fadeIn 0.2s ease;
            ">
                <div class="admin-modal" style="
                    background: white; border-radius: 12px; 
                    max-width: 400px; width: calc(100% - 32px); 
                    max-height: 80vh; overflow: hidden;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="
                        padding: 20px 24px; border-bottom: 1px solid #e2e8f0;
                        display: flex; justify-content: space-between; align-items: center;
                    ">
                        <h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #1e293b;">
                            ${title}
                        </h3>
                        <button onclick="adminNav.closeModal()" style="
                            background: none; border: none; font-size: 20px; 
                            color: #64748b; cursor: pointer; padding: 4px;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div style="overflow-y: auto; max-height: 60vh;">
                        ${content}
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            @keyframes slideIn {
                from { transform: translateY(-20px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        // 点击遮罩关闭
        document.querySelector('.admin-modal-overlay').addEventListener('click', (e) => {
            if (e.target.classList.contains('admin-modal-overlay')) {
                this.closeModal();
            }
        });
    }

    // 关闭模态框
    closeModal() {
        const modal = document.querySelector('.admin-modal-overlay');
        if (modal) {
            modal.remove();
        }
    }

    // 显示快速操作面板
    showQuickActions() {
        const actions = [
            { icon: 'fas fa-plus', text: '新增用户', action: () => window.location.href = '../user-management/edit-user.html' },
            { icon: 'fas fa-upload', text: '批量导入', action: () => console.log('批量导入') },
            { icon: 'fas fa-download', text: '导出数据', action: () => console.log('导出数据') },
            { icon: 'fas fa-cog', text: '系统设置', action: () => window.location.href = '../system-settings/index.html' }
        ];

        this.showModal('快速操作', this.createQuickActionsContent(actions));
    }

    // 创建快速操作内容
    createQuickActionsContent(actions) {
        return `
            <div style="padding: 16px;">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                    ${actions.map(action => `
                        <div onclick="adminNav.handleQuickAction('${action.text}')" style="
                            padding: 16px; border: 1px solid #e2e8f0; border-radius: 8px;
                            cursor: pointer; text-align: center; transition: all 0.2s;
                        " onmouseover="this.style.background='#f8fafc'; this.style.borderColor='#cbd5e1'"
                           onmouseout="this.style.background='white'; this.style.borderColor='#e2e8f0'">
                            <i class="${action.icon}" style="font-size: 24px; color: #3b82f6; margin-bottom: 8px;"></i>
                            <div style="font-size: 14px; color: #1e293b; font-weight: 500;">${action.text}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // 处理快速操作
    handleQuickAction(action) {
        this.closeModal();
        console.log('执行快速操作:', action);
    }
}

// 初始化导航
let adminNav;
document.addEventListener('DOMContentLoaded', function() {
    adminNav = new AdminNavigation();
}); 