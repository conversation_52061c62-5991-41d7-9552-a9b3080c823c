/**
 * TeleShop 后台管理系统 - API集成服务
 * 提供统一的API调用接口、认证管理、错误处理、缓存策略等功能
 */

// API服务基类
class APIService {
    constructor(options = {}) {
        this.baseURL = options.baseURL || this.detectEnvironment();
        this.timeout = options.timeout || 30000;
        this.retryCount = options.retryCount || 3;
        this.retryDelay = options.retryDelay || 1000;
        
        // 认证相关
        this.token = this.getStoredToken();
        this.refreshToken = this.getStoredRefreshToken();
        this.isRefreshing = false;
        this.failedQueue = [];
        
        // 缓存相关
        this.cache = new Map();
        this.cacheTimeout = options.cacheTimeout || 300000; // 5分钟
        
        // 请求拦截器
        this.requestInterceptors = [];
        this.responseInterceptors = [];
        
        // 离线相关
        this.isOffline = !navigator.onLine;
        this.offlineQueue = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupDefaultInterceptors();
    }

    detectEnvironment() {
        const hostname = window.location.hostname;
        
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'http://localhost:3000/api';
        } else if (hostname.includes('staging') || hostname.includes('test')) {
            return 'https://staging-api.teleshop.com/api';
        } else {
            return 'https://api.teleshop.com/api';
        }
    }

    setupEventListeners() {
        // 网络状态监听
        window.addEventListener('online', () => {
            this.isOffline = false;
            this.processOfflineQueue();
        });

        window.addEventListener('offline', () => {
            this.isOffline = true;
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    setupDefaultInterceptors() {
        // 请求拦截器 - 添加认证头
        this.addRequestInterceptor((config) => {
            if (this.token && !config.skipAuth) {
                config.headers = config.headers || {};
                config.headers['Authorization'] = `Bearer ${this.token}`;
            }
            
            // 添加请求ID用于追踪
            config.headers = config.headers || {};
            config.headers['X-Request-ID'] = this.generateRequestId();
            
            return config;
        });

        // 响应拦截器 - 处理认证错误
        this.addResponseInterceptor(
            (response) => response,
            async (error) => {
                const originalRequest = error.config;
                
                if (error.status === 401 && !originalRequest._retry) {
                    if (this.isRefreshing) {
                        return this.queueFailedRequest(originalRequest);
                    }
                    
                    originalRequest._retry = true;
                    this.isRefreshing = true;
                    
                    try {
                        await this.refreshAuthToken();
                        return this.request(originalRequest);
                    } catch (refreshError) {
                        this.handleAuthFailure();
                        return Promise.reject(refreshError);
                    } finally {
                        this.isRefreshing = false;
                    }
                }
                
                return Promise.reject(error);
            }
        );
    }

    addRequestInterceptor(onFulfilled, onRejected) {
        this.requestInterceptors.push({ onFulfilled, onRejected });
    }

    addResponseInterceptor(onFulfilled, onRejected) {
        this.responseInterceptors.push({ onFulfilled, onRejected });
    }

    async request(config) {
        // 如果离线且不是重要请求，则加入队列
        if (this.isOffline && !config.urgent) {
            return this.queueOfflineRequest(config);
        }

        // 应用请求拦截器
        let processedConfig = { ...config };
        for (const interceptor of this.requestInterceptors) {
            if (interceptor.onFulfilled) {
                try {
                    processedConfig = await interceptor.onFulfilled(processedConfig);
                } catch (error) {
                    if (interceptor.onRejected) {
                        return interceptor.onRejected(error);
                    }
                    throw error;
                }
            }
        }

        return this.executeRequest(processedConfig);
    }

    async executeRequest(config, retryCount = 0) {
        const url = this.buildURL(config.url, config.params);
        const options = this.buildRequestOptions(config);

        try {
            // 检查缓存
            if (config.method === 'GET' && config.cache !== false) {
                const cachedResponse = this.getFromCache(url);
                if (cachedResponse) {
                    return cachedResponse;
                }
            }

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);

            options.signal = controller.signal;

            const response = await fetch(url, options);
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new APIError(response.status, await this.parseErrorResponse(response));
            }

            const data = await this.parseResponse(response);
            const result = {
                data,
                status: response.status,
                statusText: response.statusText,
                headers: this.parseHeaders(response.headers),
                config
            };

            // 缓存GET请求结果
            if (config.method === 'GET' && config.cache !== false) {
                this.setCache(url, result);
            }

            // 应用响应拦截器
            let processedResult = result;
            for (const interceptor of this.responseInterceptors) {
                if (interceptor.onFulfilled) {
                    processedResult = await interceptor.onFulfilled(processedResult);
                }
            }

            return processedResult;

        } catch (error) {
            // 应用错误拦截器
            let processedError = error;
            for (const interceptor of this.responseInterceptors) {
                if (interceptor.onRejected) {
                    try {
                        processedError = await interceptor.onRejected(processedError);
                        return processedError; // 如果错误被处理，返回结果
                    } catch (interceptorError) {
                        processedError = interceptorError;
                    }
                }
            }

            // 重试逻辑
            if (this.shouldRetry(error, retryCount)) {
                await this.delay(this.retryDelay * Math.pow(2, retryCount));
                return this.executeRequest(config, retryCount + 1);
            }

            throw processedError;
        }
    }

    buildURL(url, params) {
        const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;
        
        if (!params) return fullURL;

        const searchParams = new URLSearchParams();
        Object.keys(params).forEach(key => {
            const value = params[key];
            if (value !== null && value !== undefined) {
                searchParams.append(key, value);
            }
        });

        const paramString = searchParams.toString();
        return paramString ? `${fullURL}?${paramString}` : fullURL;
    }

    buildRequestOptions(config) {
        const options = {
            method: config.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...config.headers
            }
        };

        if (config.data && config.method !== 'GET') {
            if (config.data instanceof FormData) {
                delete options.headers['Content-Type'];
                options.body = config.data;
            } else {
                options.body = JSON.stringify(config.data);
            }
        }

        return options;
    }

    async parseResponse(response) {
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else if (contentType && contentType.includes('text/')) {
            return await response.text();
        } else {
            return await response.blob();
        }
    }

    async parseErrorResponse(response) {
        try {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return { message: response.statusText };
            }
        } catch (error) {
            return { message: response.statusText };
        }
    }

    parseHeaders(headers) {
        const parsed = {};
        headers.forEach((value, key) => {
            parsed[key] = value;
        });
        return parsed;
    }

    shouldRetry(error, retryCount) {
        if (retryCount >= this.retryCount) return false;
        
        if (error.name === 'AbortError') return false;
        
        if (error instanceof APIError) {
            return error.status >= 500 || error.status === 429;
        }
        
        return true;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 缓存管理
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        this.cache.delete(key);
        return null;
    }

    setCache(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    clearCache() {
        this.cache.clear();
    }

    // 认证管理
    setToken(token, refreshToken) {
        this.token = token;
        this.refreshToken = refreshToken;
        localStorage.setItem('teleshop_token', token);
        if (refreshToken) {
            localStorage.setItem('teleshop_refresh_token', refreshToken);
        }
    }

    getStoredToken() {
        return localStorage.getItem('teleshop_token');
    }

    getStoredRefreshToken() {
        return localStorage.getItem('teleshop_refresh_token');
    }

    clearTokens() {
        this.token = null;
        this.refreshToken = null;
        localStorage.removeItem('teleshop_token');
        localStorage.removeItem('teleshop_refresh_token');
    }

    async refreshAuthToken() {
        if (!this.refreshToken) {
            throw new Error('No refresh token available');
        }

        const response = await fetch(`${this.baseURL}/auth/refresh`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                refreshToken: this.refreshToken
            })
        });

        if (!response.ok) {
            throw new Error('Token refresh failed');
        }

        const data = await response.json();
        this.setToken(data.token, data.refreshToken);
        
        // 处理等待队列
        this.processFailedQueue();
    }

    queueFailedRequest(originalRequest) {
        return new Promise((resolve, reject) => {
            this.failedQueue.push({ resolve, reject, config: originalRequest });
        });
    }

    processFailedQueue() {
        this.failedQueue.forEach(({ resolve, reject, config }) => {
            this.request(config).then(resolve).catch(reject);
        });
        this.failedQueue = [];
    }

    handleAuthFailure() {
        this.clearTokens();
        this.processFailedQueue();
        
        // 触发认证失败事件
        window.dispatchEvent(new CustomEvent('auth:failure'));
    }

    // 离线支持
    queueOfflineRequest(config) {
        return new Promise((resolve, reject) => {
            this.offlineQueue.push({ resolve, reject, config });
        });
    }

    processOfflineQueue() {
        this.offlineQueue.forEach(({ resolve, reject, config }) => {
            this.request(config).then(resolve).catch(reject);
        });
        this.offlineQueue = [];
    }

    // 便捷方法
    get(url, config = {}) {
        return this.request({ ...config, method: 'GET', url });
    }

    post(url, data, config = {}) {
        return this.request({ ...config, method: 'POST', url, data });
    }

    put(url, data, config = {}) {
        return this.request({ ...config, method: 'PUT', url, data });
    }

    patch(url, data, config = {}) {
        return this.request({ ...config, method: 'PATCH', url, data });
    }

    delete(url, config = {}) {
        return this.request({ ...config, method: 'DELETE', url });
    }

    // 文件上传
    async upload(url, files, options = {}) {
        const formData = new FormData();
        
        if (Array.isArray(files)) {
            files.forEach((file, index) => {
                formData.append(`file_${index}`, file);
            });
        } else {
            formData.append('file', files);
        }

        // 添加额外数据
        if (options.data) {
            Object.keys(options.data).forEach(key => {
                formData.append(key, options.data[key]);
            });
        }

        return this.request({
            method: 'POST',
            url,
            data: formData,
            headers: {
                // 让浏览器自动设置 Content-Type
            },
            onUploadProgress: options.onProgress
        });
    }

    // 批量请求
    async batch(requests) {
        const promises = requests.map(request => this.request(request));
        return Promise.allSettled(promises);
    }

    // WebSocket连接
    createWebSocket(url, protocols) {
        const wsUrl = url.replace(/^http/, 'ws');
        return new WebSocket(wsUrl, protocols);
    }

    // 工具方法
    generateRequestId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    cleanup() {
        this.clearCache();
        this.failedQueue = [];
        this.offlineQueue = [];
    }
}

// API错误类
class APIError extends Error {
    constructor(status, response) {
        super(response.message || `HTTP Error ${status}`);
        this.name = 'APIError';
        this.status = status;
        this.response = response;
    }
}

// TeleShop业务API类
class TeleShopAPI extends APIService {
    constructor(options = {}) {
        super(options);
    }

    // 认证相关API
    async login(credentials) {
        const response = await this.post('/auth/login', credentials);
        if (response.data.token) {
            this.setToken(response.data.token, response.data.refreshToken);
        }
        return response;
    }

    async logout() {
        try {
            await this.post('/auth/logout', { refreshToken: this.refreshToken });
        } catch (error) {
            console.warn('Logout API call failed:', error);
        } finally {
            this.clearTokens();
        }
    }

    async register(userData) {
        return this.post('/auth/register', userData);
    }

    async resetPassword(email) {
        return this.post('/auth/reset-password', { email });
    }

    async changePassword(passwords) {
        return this.post('/auth/change-password', passwords);
    }

    // 仪表盘API
    async getDashboardData(timeRange = '7d') {
        return this.get('/dashboard', { params: { range: timeRange } });
    }

    async getRealtimeStats() {
        return this.get('/dashboard/realtime');
    }

    async getChartData(type, period = '7d') {
        return this.get(`/dashboard/charts/${type}`, { params: { period } });
    }

    // 用户管理API
    async getUsers(params = {}) {
        return this.get('/users', { params });
    }

    async getUser(userId) {
        return this.get(`/users/${userId}`);
    }

    async createUser(userData) {
        return this.post('/users', userData);
    }

    async updateUser(userId, userData) {
        return this.put(`/users/${userId}`, userData);
    }

    async deleteUser(userId) {
        return this.delete(`/users/${userId}`);
    }

    async batchDeleteUsers(userIds) {
        return this.post('/users/batch-delete', { ids: userIds });
    }

    async exportUsers(params = {}) {
        return this.get('/users/export', { params });
    }

    // 商品管理API
    async getProducts(params = {}) {
        return this.get('/products', { params });
    }

    async getProduct(productId) {
        return this.get(`/products/${productId}`);
    }

    async createProduct(productData) {
        return this.post('/products', productData);
    }

    async updateProduct(productId, productData) {
        return this.put(`/products/${productId}`, productData);
    }

    async deleteProduct(productId) {
        return this.delete(`/products/${productId}`);
    }

    async batchUpdateProducts(updates) {
        return this.post('/products/batch-update', updates);
    }

    async getCategories() {
        return this.get('/products/categories');
    }

    async uploadProductImages(productId, files) {
        return this.upload(`/products/${productId}/images`, files);
    }

    // 订单管理API
    async getOrders(params = {}) {
        return this.get('/orders', { params });
    }

    async getOrder(orderId) {
        return this.get(`/orders/${orderId}`);
    }

    async updateOrderStatus(orderId, status, note = '') {
        return this.patch(`/orders/${orderId}/status`, { status, note });
    }

    async cancelOrder(orderId, reason) {
        return this.post(`/orders/${orderId}/cancel`, { reason });
    }

    async refundOrder(orderId, amount, reason) {
        return this.post(`/orders/${orderId}/refund`, { amount, reason });
    }

    async getOrderStatistics(params = {}) {
        return this.get('/orders/statistics', { params });
    }

    // 财务管理API
    async getFinancialData(params = {}) {
        return this.get('/finance', { params });
    }

    async getTransactions(params = {}) {
        return this.get('/finance/transactions', { params });
    }

    async getPaymentMethods() {
        return this.get('/finance/payment-methods');
    }

    async processRefund(transactionId, amount) {
        return this.post(`/finance/refund/${transactionId}`, { amount });
    }

    // 数据分析API
    async getAnalyticsData(type, params = {}) {
        return this.get(`/analytics/${type}`, { params });
    }

    async getSalesReport(params = {}) {
        return this.get('/analytics/sales', { params });
    }

    async getUserAnalytics(params = {}) {
        return this.get('/analytics/users', { params });
    }

    async getProductAnalytics(params = {}) {
        return this.get('/analytics/products', { params });
    }

    async exportReport(type, params = {}) {
        return this.get(`/analytics/export/${type}`, { params });
    }

    // 内容管理API
    async getPages(params = {}) {
        return this.get('/content/pages', { params });
    }

    async getPage(pageId) {
        return this.get(`/content/pages/${pageId}`);
    }

    async createPage(pageData) {
        return this.post('/content/pages', pageData);
    }

    async updatePage(pageId, pageData) {
        return this.put(`/content/pages/${pageId}`, pageData);
    }

    async deletePage(pageId) {
        return this.delete(`/content/pages/${pageId}`);
    }

    // 系统设置API
    async getSettings() {
        return this.get('/settings');
    }

    async updateSettings(settings) {
        return this.put('/settings', settings);
    }

    async getNotifications(params = {}) {
        return this.get('/notifications', { params });
    }

    async markNotificationRead(notificationId) {
        return this.patch(`/notifications/${notificationId}/read`);
    }

    // 文件管理API
    async uploadFile(file, options = {}) {
        return this.upload('/files/upload', file, options);
    }

    async deleteFile(fileId) {
        return this.delete(`/files/${fileId}`);
    }

    async getFiles(params = {}) {
        return this.get('/files', { params });
    }

    // 搜索API
    async search(query, type = 'all', params = {}) {
        return this.get('/search', { 
            params: { q: query, type, ...params } 
        });
    }

    async getSearchSuggestions(query) {
        return this.get('/search/suggestions', { params: { q: query } });
    }

    // 日志API
    async getLogs(params = {}) {
        return this.get('/logs', { params });
    }

    async getSystemStatus() {
        return this.get('/system/status');
    }

    // 备份API
    async createBackup() {
        return this.post('/system/backup');
    }

    async getBackups() {
        return this.get('/system/backups');
    }

    async restoreBackup(backupId) {
        return this.post(`/system/restore/${backupId}`);
    }
}

// 创建全局API实例
const teleshopAPI = new TeleShopAPI();

// 处理全局认证事件
window.addEventListener('auth:failure', () => {
    // 重定向到登录页面
    if (window.location.pathname !== '/login.html') {
        window.location.href = '/login.html';
    }
});

// 导出API实例和类
window.TeleShopAPI = TeleShopAPI;
window.APIService = APIService;
window.teleshopAPI = teleshopAPI;

// 如果支持模块化，也导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TeleShopAPI, APIService, teleshopAPI };
}

console.log('TeleShop API服务已加载'); 