/**
 * TeleShop Admin - 全局修复脚本
 * 修复页面显示、图片加载和兼容性问题
 */

(function() {
    'use strict';

    console.log('🔧 TeleShop 全局修复脚本启动...');

    // 主要修复函数
    const GlobalFixes = {
        // 修复图片加载问题
        fixImages() {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                // 处理空或无效图片路径
                if (!img.src || img.src.includes('undefined') || img.src.includes('null')) {
                    this.replaceImageWithPlaceholder(img);
                }

                // 处理图片加载错误
                img.onerror = () => {
                    this.replaceImageWithPlaceholder(img);
                };
            });
        },

        // 替换图片为占位符
        replaceImageWithPlaceholder(img) {
            const placeholder = document.createElement('div');
            
            // 确定占位符类型
            let type = 'default';
            if (img.className.includes('avatar')) type = 'avatar';
            else if (img.className.includes('product')) type = 'product';
            else if (img.className.includes('logo')) type = 'logo';

            // 设置占位符
            placeholder.className = `image-placeholder type-${type}`;
            placeholder.style.width = img.width + 'px' || '40px';
            placeholder.style.height = img.height + 'px' || '40px';
            
            // 添加图标
            const icons = {
                avatar: 'fa-user',
                product: 'fa-box',
                logo: 'fa-building',
                default: 'fa-image'
            };
            placeholder.innerHTML = `<i class="fas ${icons[type]}"></i>`;
            
            // 替换元素
            img.parentNode.replaceChild(placeholder, img);
        },

        // 修复页面布局
        fixLayout() {
            // 修复高度问题
            const contentPages = document.querySelectorAll('.content-page');
            contentPages.forEach(page => {
                if (page.scrollHeight > page.clientHeight) {
                    page.style.height = 'auto';
                    page.style.minHeight = '100vh';
                }
            });

            // 修复移动端布局
            if (window.innerWidth <= 768) {
                document.body.classList.add('mobile-view');
                document.body.style.overflowX = 'hidden';
            }
        },

        // 修复脚本问题
        fixScripts() {
            // 确保loadPage函数存在
            if (typeof window.loadPage === 'undefined') {
                window.loadPage = function(pageName) {
                    console.log(`正在加载页面: ${pageName}`);
                    
                    // 隐藏当前页面
                    const currentPage = document.querySelector('.content-page.active');
                    if (currentPage) {
                        currentPage.classList.remove('active');
                    }
                    
                    // 显示目标页面
                    const targetPage = document.querySelector(`#${pageName}-content`);
                    if (targetPage) {
                        targetPage.classList.add('active');
                        console.log(`✅ 成功加载页面: ${pageName}`);
                    } else {
                        console.error(`❌ 未找到页面: ${pageName}`);
                    }
                };
            }
        },

        // 修复CSS兼容性
        fixCSS() {
            // 添加CSS前缀
            const style = document.createElement('style');
            style.textContent = `
                .content-page {
                    -webkit-transition: all 0.3s ease;
                    -moz-transition: all 0.3s ease;
                    -o-transition: all 0.3s ease;
                    transition: all 0.3s ease;
                }
                
                .flex {
                    display: -webkit-box;
                    display: -webkit-flex;
                    display: -ms-flexbox;
                    display: flex;
                }
                
                .grid {
                    display: -ms-grid;
                    display: grid;
                }
                
                /* 修复图片显示问题 */
                .image-placeholder {
                    background: #f1f5f9;
                    border: 1px solid #e2e8f0;
                    border-radius: 6px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #64748b;
                    font-size: 16px;
                }
                
                .image-placeholder.type-avatar {
                    border-radius: 50%;
                    background: #dbeafe;
                    color: #2563eb;
                }
                
                .image-placeholder.type-product {
                    background: #fef3c7;
                    color: #d97706;
                }
                
                /* 移动端优化 */
                @media (max-width: 768px) {
                    .mobile-view .sidebar {
                        transform: translateX(-100%);
                    }
                    
                    .mobile-view .main-content {
                        margin-left: 0;
                        width: 100%;
                    }
                }
            `;
            document.head.appendChild(style);
        },

        // 初始化所有修复
        init() {
            this.fixImages();
            this.fixLayout();
            this.fixScripts();
            this.fixCSS();
            
            console.log('✅ 全局修复完成');
        }
    };

    // 等待DOM加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            GlobalFixes.init();
        });
    } else {
        GlobalFixes.init();
    }

    // 导出到全局对象
    window.TeleShopGlobalFixes = GlobalFixes;

})(); 