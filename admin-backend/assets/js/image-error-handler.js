/* TeleShop Admin - 增强的图片错误处理脚本 */

// 图片类型配置
const IMAGE_TYPES = {
    avatar: {
        className: 'image-placeholder type-avatar',
        icon: 'fas fa-user',
        defaultSize: 'size-md'
    },
    product: {
        className: 'image-placeholder type-product',
        icon: 'fas fa-box',
        defaultSize: 'size-md'
    },
    logo: {
        className: 'image-placeholder type-logo',
        icon: 'fas fa-building',
        defaultSize: 'size-md'
    },
    banner: {
        className: 'image-placeholder type-banner',
        icon: 'fas fa-image',
        defaultSize: 'size-xl'
    },
    default: {
        className: 'image-placeholder',
        icon: 'fas fa-image',
        defaultSize: 'size-md'
    }
};

// 获取图片类型
function getImageType(img) {
    const src = img.src || '';
    const alt = img.alt || '';
    const className = img.className || '';
    
    // 根据类名判断
    if (className.includes('avatar') || className.includes('user')) return 'avatar';
    if (className.includes('product')) return 'product';
    if (className.includes('logo')) return 'logo';
    if (className.includes('banner')) return 'banner';
    
    // 根据alt属性判断
    if (alt.includes('头像') || alt.includes('用户') || alt.includes('avatar')) return 'avatar';
    if (alt.includes('商品') || alt.includes('产品') || alt.includes('product')) return 'product';
    if (alt.includes('logo') || alt.includes('标志')) return 'logo';
    if (alt.includes('banner') || alt.includes('横幅')) return 'banner';
    
    // 根据src路径判断
    if (src.includes('avatar') || src.includes('user')) return 'avatar';
    if (src.includes('product') || src.includes('goods')) return 'product';
    if (src.includes('logo')) return 'logo';
    if (src.includes('banner')) return 'banner';
    
    return 'default';
}

// 获取图片尺寸
function getImageSize(img) {
    const width = img.width || img.offsetWidth || 48;
    const height = img.height || img.offsetHeight || 48;
    const maxSize = Math.max(width, height);
    
    if (maxSize <= 24) return 'size-xs';
    if (maxSize <= 32) return 'size-sm';
    if (maxSize <= 48) return 'size-md';
    if (maxSize <= 64) return 'size-lg';
    return 'size-xl';
}

// 创建占位符元素
function createPlaceholder(img, type = 'default') {
    const config = IMAGE_TYPES[type] || IMAGE_TYPES.default;
    const size = getImageSize(img);
    
    const placeholder = document.createElement('div');
    placeholder.className = `${config.className} ${size}`;
    placeholder.style.width = img.width ? `${img.width}px` : img.style.width || '48px';
    placeholder.style.height = img.height ? `${img.height}px` : img.style.height || '48px';
    
    // 添加图标
    const icon = document.createElement('i');
    icon.className = config.icon;
    placeholder.appendChild(icon);
    
    // 添加文字（如果有alt属性）
    if (img.alt && img.alt.length > 0 && img.alt.length < 10) {
        const text = document.createElement('span');
        text.textContent = img.alt.substring(0, 2).toUpperCase();
        text.style.fontSize = '12px';
        text.style.fontWeight = '600';
        text.style.marginTop = '4px';
        placeholder.appendChild(text);
    }
    
    // 复制原始图片的一些属性
    if (img.title) placeholder.title = img.title;
    if (img.className) placeholder.className += ` ${img.className.replace(/\b(user-avatar|product-image|logo-placeholder)\b/g, '')}`;
    
    return placeholder;
}

// 处理图片加载错误
function handleImageError(img) {
    const type = getImageType(img);
    const placeholder = createPlaceholder(img, type);
    
    // 替换图片
    img.parentNode.replaceChild(placeholder, img);
    
    console.log(`图片加载失败，已替换为占位符: ${img.src}`);
}

// 处理图片加载成功
function handleImageLoad(img) {
    // 移除loading类
    img.classList.remove('image-loading');
    
    // 添加成功加载的样式
    img.style.opacity = '1';
    img.style.transition = 'opacity 0.3s ease';
}

// 添加图片加载状态
function addLoadingState(img) {
    img.classList.add('image-loading');
    img.style.opacity = '0.7';
}

// 处理所有图片
function processImages() {
    const images = document.querySelectorAll('img');
    let processedCount = 0;
    
    images.forEach(img => {
        // 跳过已经处理过的图片
        if (img.dataset.processed) return;
        
        // 标记为已处理
        img.dataset.processed = 'true';
        
        // 如果图片已经加载失败
        if (img.complete && img.naturalHeight === 0) {
            handleImageError(img);
            processedCount++;
            return;
        }
        
        // 如果图片已经加载成功
        if (img.complete && img.naturalHeight !== 0) {
            handleImageLoad(img);
            return;
        }
        
        // 添加loading状态
        addLoadingState(img);
        
        // 设置错误处理
        img.onerror = function() {
            handleImageError(this);
        };
        
        // 设置加载成功处理
        img.onload = function() {
            handleImageLoad(this);
        };
        
        // 设置超时处理（10秒后如果还没加载完成，视为失败）
        setTimeout(() => {
            if (!img.complete || img.naturalHeight === 0) {
                handleImageError(img);
            }
        }, 10000);
        
        processedCount++;
    });
    
    if (processedCount > 0) {
        console.log(`已处理 ${processedCount} 张图片`);
    }
}

// 处理动态添加的图片
function setupImageObserver() {
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            let hasNewImages = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // 检查新添加的节点是否是图片
                            if (node.tagName === 'IMG') {
                                hasNewImages = true;
                            }
                            // 检查新添加的节点是否包含图片
                            else if (node.querySelectorAll && node.querySelectorAll('img').length > 0) {
                                hasNewImages = true;
                            }
                        }
                    });
                }
            });
            
            if (hasNewImages) {
                // 延迟一点处理，确保DOM完全更新
                setTimeout(processImages, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
}

// 初始化函数
function initImageHandler() {
    // 处理现有图片
    processImages();
    
    // 设置观察器监听新图片
    setupImageObserver();
    
    // 定期检查（防止某些情况下的遗漏）
    setInterval(processImages, 30000); // 每30秒检查一次
    
    console.log('图片错误处理器已初始化');
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initImageHandler);
} else {
    initImageHandler();
}

// 如果页面已经完全加载，再次处理
if (document.readyState === 'complete') {
    setTimeout(initImageHandler, 100);
}

// 公开的API
window.ImageHandler = {
    processImages: processImages,
    createPlaceholder: createPlaceholder,
    handleImageError: handleImageError
}; 