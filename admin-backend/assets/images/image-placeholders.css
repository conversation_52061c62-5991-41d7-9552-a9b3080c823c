/* TeleShop Admin - 图片占位符和错误处理样式 */

/* 基础占位符样式 */
.image-placeholder {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    font-size: 24px;
    position: relative;
    overflow: hidden;
    user-select: none;
}

.image-placeholder::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 不同尺寸的占位符 */
.image-placeholder.size-sm {
    font-size: 12px;
}

.image-placeholder.size-md {
    font-size: 16px;
}

.image-placeholder.size-lg {
    font-size: 32px;
}

.image-placeholder.size-xl {
    font-size: 48px;
}

/* 不同类型的占位符 */
.image-placeholder.type-avatar {
    border-radius: 50%;
    background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%);
    color: #7c3aed;
}

.image-placeholder.type-logo {
    border-radius: 12px;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #3b82f6;
}

.image-placeholder.type-product {
    border-radius: 8px;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #d97706;
}

.image-placeholder.type-user {
    border-radius: 50%;
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #16a34a;
}

/* 图片加载状态 */
.image-loading {
    position: relative;
    overflow: hidden;
}

.image-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.2s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 图片错误处理 */
.image-error {
    background: #fef2f2;
    color: #dc2626;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 8px;
    padding: 16px;
    text-align: center;
    border: 1px dashed #fca5a5;
}

.image-error i {
    font-size: 24px;
    opacity: 0.7;
}

.image-error span {
    font-size: 12px;
    opacity: 0.8;
}

/* 图片容器样式 */
.image-container {
    position: relative;
    overflow: hidden;
}

.image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
}

.image-container img.loading {
    opacity: 0.7;
}

.image-container img.error {
    display: none;
}

/* 头像组件 */
.avatar-container {
    position: relative;
    display: inline-block;
}

.avatar-container .avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-container .avatar-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    font-size: 18px;
}

.avatar-container .avatar-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.avatar-container .avatar-status.online {
    background: #10b981;
}

.avatar-container .avatar-status.offline {
    background: #6b7280;
}

.avatar-container .avatar-status.busy {
    background: #ef4444;
}

/* Logo 组件 */
.logo-container {
    position: relative;
    display: inline-block;
}

.logo-container .logo-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.logo-container .logo-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    font-size: 24px;
    border-radius: 8px;
}

/* 商品图片组件 */
.product-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    background: #f8fafc;
}

.product-image-container .product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-image-container:hover .product-image {
    transform: scale(1.05);
}

.product-image-container .product-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #d97706;
    font-size: 32px;
}

.product-image-container .product-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ef4444;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* 响应式图片 */
.responsive-image {
    max-width: 100%;
    height: auto;
    display: block;
}

/* 图片网格 */
.image-grid {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
}

.image-grid-item {
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    background: #f8fafc;
    position: relative;
}

.image-grid-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-grid-item .placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f5f9;
    color: #64748b;
    font-size: 24px;
}

/* 图片上传区域 */
.image-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 32px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.image-upload-area:hover {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.image-upload-area.dragover {
    border-color: #10b981;
    background: #ecfdf5;
}

.image-upload-area i {
    font-size: 48px;
    color: #9ca3af;
    margin-bottom: 16px;
}

.image-upload-area h3 {
    margin: 0 0 8px 0;
    color: #374151;
    font-size: 18px;
    font-weight: 600;
}

.image-upload-area p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
}

/* 图片预览 */
.image-preview {
    position: relative;
    display: inline-block;
    margin: 8px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.image-preview .remove-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ef4444;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-preview:hover .remove-btn {
    opacity: 1;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 12px;
    }
    
    .image-upload-area {
        padding: 24px 16px;
    }
    
    .image-upload-area i {
        font-size: 36px;
    }
    
    .image-upload-area h3 {
        font-size: 16px;
    }
    
    .image-upload-area p {
        font-size: 13px;
    }
    
    .image-preview {
        margin: 6px;
    }
    
    .image-preview img {
        width: 80px;
        height: 80px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .image-placeholder {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
        color: #9ca3af;
    }
    
    .image-placeholder.type-avatar {
        background: linear-gradient(135deg, #581c87 0%, #7c3aed 100%);
        color: #c4b5fd;
    }
    
    .image-placeholder.type-logo {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: #bfdbfe;
    }
    
    .image-placeholder.type-product {
        background: linear-gradient(135deg, #b45309 0%, #d97706 100%);
        color: #fde68a;
    }
    
    .image-error {
        background: #451a1a;
        color: #fca5a5;
        border-color: #991b1b;
    }
    
    .image-upload-area {
        background: #1f2937;
        border-color: #4b5563;
    }
    
    .image-upload-area:hover {
        border-color: #60a5fa;
        background: #1e3a8a;
    }
} 