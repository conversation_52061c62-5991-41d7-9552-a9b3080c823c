/* TeleShop Admin - 按钮布局修复增强版 */

/* 通用按钮布局修复 */
.dashboard-actions,
.header-actions,
.toolbar-actions,
.card-actions,
.form-actions,
.page-actions,
.action-buttons,
.bulk-buttons {
    display: flex !important;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: flex-end;
    min-height: 40px;
    position: relative;
    z-index: 10;
}

/* 按钮基础样式修复 */
.action-btn,
.btn,
.btn-sm,
.table-action-btn,
.pagination-btn,
.filter-btn,
.date-btn,
button:not(.menu-btn):not(.nav-btn):not(.filter-tab) {
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 16px;
    min-height: 36px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: #ffffff;
    color: #374151;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.action-btn:hover,
.btn:hover,
.btn-sm:hover,
.table-action-btn:hover,
.pagination-btn:hover:not(:disabled),
.filter-btn:hover,
.date-btn:hover,
button:not(.menu-btn):not(.nav-btn):not(.filter-tab):hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

/* 主要按钮样式 */
.action-btn.primary,
.btn.primary,
.btn-primary,
.pagination-btn.active,
.filter-btn.active,
.date-btn.active {
    background: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
}

.action-btn.primary:hover,
.btn.primary:hover,
.btn-primary:hover,
.pagination-btn.active:hover,
.filter-btn.active:hover,
.date-btn.active:hover {
    background: #2563eb !important;
    border-color: #2563eb !important;
}

/* 危险按钮 */
.action-btn.danger,
.btn.danger,
.btn-danger,
.btn-sm.danger {
    background: #ef4444 !important;
    color: white !important;
    border-color: #ef4444 !important;
}

.action-btn.danger:hover,
.btn.danger:hover,
.btn-danger:hover,
.btn-sm.danger:hover {
    background: #dc2626 !important;
    border-color: #dc2626 !important;
}

/* 警告按钮 */
.action-btn.warning,
.btn.warning,
.btn-warning,
.btn-sm.warning {
    background: #f59e0b !important;
    color: white !important;
    border-color: #f59e0b !important;
}

.action-btn.warning:hover,
.btn.warning:hover,
.btn-warning:hover,
.btn-sm.warning:hover {
    background: #d97706 !important;
    border-color: #d97706 !important;
}

/* 小按钮样式 */
.btn-sm,
.table-action-btn {
    padding: 6px 12px;
    min-height: 32px;
    font-size: 12px;
}

/* 图标按钮 */
.action-icon-btn,
.table-action-btn {
    width: 32px;
    height: 32px;
    padding: 0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 图标和文字间距 */
.action-btn i,
.btn i,
.btn-sm i,
button i {
    margin-right: 4px;
    font-size: 12px;
    flex-shrink: 0;
}

/* 只有图标的按钮 */
.action-btn:not(:has(span)),
.btn:not(:has(span)) {
    min-width: 36px;
    padding: 8px;
}

.action-btn:not(:has(span)) i,
.btn:not(:has(span)) i {
    margin: 0;
    font-size: 14px;
}

/* 防止按钮文字换行 */
.action-btn span,
.btn span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

/* 页面头部按钮组 */
.page-header .page-actions {
    margin-left: auto;
    flex-shrink: 0;
}

/* 表格中的动作按钮 */
.action-buttons {
    display: flex !important;
    gap: 4px;
    justify-content: center;
    flex-wrap: wrap;
}

/* 批量操作栏 */
.bulk-actions {
    background: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    display: none;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    flex-wrap: wrap;
}

.bulk-actions.show {
    display: flex !important;
}

.bulk-info,
.bulk-actions-info {
    color: #1e40af;
    font-weight: 500;
    font-size: 14px;
}

/* 分页按钮组 */
.pagination-controls {
    display: flex;
    gap: 4px;
    align-items: center;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* 筛选按钮组 */
.filter-tabs {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    overflow-x: auto;
    padding: 0;
}

.filter-tab {
    padding: 12px 16px;
    border: none;
    background: none;
    color: #64748b;
    font-size: 14px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
    white-space: nowrap;
    min-height: auto;
}

.filter-tab.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: none;
}

.filter-tab:hover {
    color: #3b82f6;
    background: #f8fafc;
    transform: none;
    box-shadow: none;
}

/* 视图控制按钮 */
.view-controls {
    display: flex;
    gap: 4px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
}

.view-btn {
    padding: 8px 12px;
    border: none;
    background: white;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
    min-height: auto;
}

.view-btn.active {
    background: #3b82f6;
    color: white;
}

/* 移动端按钮优化 */
@media (max-width: 768px) {
    .dashboard-actions,
    .header-actions,
    .toolbar-actions,
    .page-actions {
        flex-direction: row;
        justify-content: flex-start;
        gap: 6px;
        width: 100%;
    }
    
    .action-btn,
    .btn,
    button:not(.menu-btn):not(.nav-btn):not(.filter-tab) {
        padding: 8px 12px;
        min-height: 40px;
        font-size: 13px;
        flex: 1;
        justify-content: center;
    }
    
    /* 移动端显示图标和文字 */
    .action-btn span,
    .btn span {
        display: inline;
        max-width: none;
    }
    
    .action-btn i,
    .btn i {
        margin-right: 4px;
        font-size: 12px;
    }
    
    /* 表格操作按钮在移动端垂直排列 */
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
    
    .btn-sm {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .dashboard-actions,
    .header-actions,
    .toolbar-actions,
    .page-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .action-btn,
    .btn {
        width: 100%;
        justify-content: center;
        padding: 12px 16px;
        min-height: 44px;
    }
    
    .bulk-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .bulk-buttons {
        justify-content: stretch;
    }
    
    .bulk-buttons .action-btn {
        flex: 1;
    }
    
    .pagination {
        flex-direction: column;
        gap: 12px;
    }
    
    .pagination-controls {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* 图片占位符修复 */
.image-placeholder,
.avatar-placeholder,
.product-placeholder,
.user-avatar:empty,
.product-image:empty,
.customer-avatar:empty,
.product-thumb:empty {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #64748b;
    font-size: 14px;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.image-placeholder::before,
.avatar-placeholder::before,
.product-placeholder::before,
.user-avatar:empty::before,
.product-image:empty::before,
.customer-avatar:empty::before,
.product-thumb:empty::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 用户头像占位符 */
.user-avatar,
.customer-avatar,
.admin-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #3b82f6;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.user-avatar img,
.customer-avatar img,
.admin-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: inherit;
}

/* 商品图片占位符 */
.product-image,
.product-thumb {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    background: linear-gradient(135deg, #fef3c7 0%, #fcd34d 100%);
    color: #d97706;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.product-image img,
.product-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: inherit;
}

/* 表格中的图片 */
.data-table .user-avatar,
.data-table .product-thumb,
.orders-table .customer-avatar,
.orders-table .product-image {
    margin: 0;
}

/* 状态徽章 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    line-height: 1;
}

.status-badge.success,
.status-badge.status-success,
.status-active,
.status-delivered {
    background: #dcfce7;
    color: #166534;
}

.status-badge.warning,
.status-badge.status-warning,
.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.error,
.status-badge.status-error,
.status-cancelled {
    background: #fef2f2;
    color: #dc2626;
}

.status-badge.info,
.status-paid,
.status-shipped {
    background: #dbeafe;
    color: #1e40af;
}

.status-inactive,
.status-banned {
    background: #f3f4f6;
    color: #374151;
}

/* 加载状态 */
.loading-placeholder {
    background: linear-gradient(
        90deg,
        #f1f5f9 25%,
        #e2e8f0 50%,
        #f1f5f9 75%
    );
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 确保所有交互元素都有合适的点击区域 */
.action-btn,
.btn,
.btn-sm,
.table-action-btn,
button,
a.action-btn {
    min-height: 36px;
    min-width: 36px;
    touch-action: manipulation;
}

/* 在触摸设备上增大点击区域 */
@media (pointer: coarse) {
    .action-btn,
    .btn,
    .btn-sm,
    .table-action-btn,
    button,
    a.action-btn {
        min-height: 44px;
        min-width: 44px;
    }
}

/* 精确指针设备上使用较小的按钮 */
@media (pointer: fine) {
    .btn-sm,
    .table-action-btn {
        min-height: 32px;
        min-width: 32px;
    }
}

/* 文字显示优化 */
.page-title,
.content-title,
.table-title,
.stat-title,
.user-name,
.product-title,
.product-name {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    line-height: 1.4;
    color: #1e293b;
    word-break: break-word;
    hyphens: auto;
}

/* 确保数值正确显示 */
.stat-value,
.amount-value,
.price-value {
    font-family: 'Inter', monospace, sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
    word-break: keep-all;
    white-space: nowrap;
}

/* 修复表格文字对齐 */
.data-table,
.orders-table,
.users-table {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.data-table th,
.orders-table th,
.users-table th {
    font-weight: 600;
    color: #374151;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table td,
.orders-table td,
.users-table td {
    font-size: 14px;
    color: #1e293b;
    vertical-align: middle;
}

/* 确保表格内容不会溢出 */
.product-info,
.customer-info,
.user-info {
    max-width: 200px;
    overflow: hidden;
}

.product-title,
.product-name,
.user-name,
.customer-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 禁用按钮状态 */
.action-btn:disabled,
.btn:disabled,
.btn-sm:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.action-btn:disabled:hover,
.btn:disabled:hover,
.btn-sm:disabled:hover {
    background: #ffffff;
    border-color: #e2e8f0;
    transform: none;
    box-shadow: none;
} 