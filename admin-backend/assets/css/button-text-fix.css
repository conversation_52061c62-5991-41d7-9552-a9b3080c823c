/* TeleShop Admin - 按钮文字显示修复 */

/* 重置所有按钮的基本样式，确保文字显示 */
.btn,
.btn-primary,
.btn-secondary,
.btn-success,
.btn-warning,
.btn-danger,
.btn-info,
.btn-sm,
.btn-xs,
.action-btn,
.table-action-btn,
.pagination-btn,
.filter-btn,
.date-btn,
button {
    /* 确保文字显示 */
    color: inherit !important;
    font-size: inherit !important;
    line-height: normal !important;
    text-indent: 0 !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    word-spacing: normal !important;
    white-space: nowrap !important;
    
    /* 确保内容可见 */
    overflow: visible !important;
    text-overflow: visible !important;
    
    /* 确保文字不被隐藏 */
    opacity: 1 !important;
    visibility: visible !important;
    
    /* 确保字体设置正确 */
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 500 !important;
    
    /* 确保尺寸设置正确 */
    min-height: auto !important;
    height: auto !important;
    
    /* 防止内容被截断 */
    text-decoration: none !important;
}

/* 主要按钮样式 - 确保文字颜色正确 */
.btn-primary {
    background-color: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: #ffffff !important;
}

.btn-primary:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
}

/* 次要按钮样式 */
.btn-secondary {
    background-color: #6b7280 !important;
    border-color: #6b7280 !important;
    color: #ffffff !important;
}

.btn-secondary:hover {
    background-color: #4b5563 !important;
    border-color: #4b5563 !important;
    color: #ffffff !important;
}

/* 成功按钮样式 */
.btn-success {
    background-color: #10b981 !important;
    border-color: #10b981 !important;
    color: #ffffff !important;
}

.btn-success:hover {
    background-color: #059669 !important;
    border-color: #059669 !important;
    color: #ffffff !important;
}

/* 警告按钮样式 */
.btn-warning {
    background-color: #f59e0b !important;
    border-color: #f59e0b !important;
    color: #ffffff !important;
}

.btn-warning:hover {
    background-color: #d97706 !important;
    border-color: #d97706 !important;
    color: #ffffff !important;
}

/* 危险按钮样式 */
.btn-danger {
    background-color: #ef4444 !important;
    border-color: #ef4444 !important;
    color: #ffffff !important;
}

.btn-danger:hover {
    background-color: #dc2626 !important;
    border-color: #dc2626 !important;
    color: #ffffff !important;
}

/* 信息按钮样式 */
.btn-info {
    background-color: #06b6d4 !important;
    border-color: #06b6d4 !important;
    color: #ffffff !important;
}

.btn-info:hover {
    background-color: #0891b2 !important;
    border-color: #0891b2 !important;
    color: #ffffff !important;
}

/* 默认按钮样式 */
.btn:not(.btn-primary):not(.btn-secondary):not(.btn-success):not(.btn-warning):not(.btn-danger):not(.btn-info) {
    background-color: #ffffff !important;
    border-color: #d1d5db !important;
    color: #374151 !important;
}

.btn:not(.btn-primary):not(.btn-secondary):not(.btn-success):not(.btn-warning):not(.btn-danger):not(.btn-info):hover {
    background-color: #f9fafb !important;
    border-color: #9ca3af !important;
    color: #111827 !important;
}

/* 小按钮样式 */
.btn-sm {
    padding: 6px 12px !important;
    font-size: 13px !important;
    line-height: 1.4 !important;
}

/* 超小按钮样式 */
.btn-xs {
    padding: 4px 8px !important;
    font-size: 12px !important;
    line-height: 1.4 !important;
}

/* 确保按钮内的图标和文字正确显示 */
.btn i,
.btn-sm i,
.btn-xs i {
    margin-right: 4px !important;
    font-size: inherit !important;
    vertical-align: middle !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 确保按钮内的文字span正确显示 */
.btn span,
.btn-sm span,
.btn-xs span {
    opacity: 1 !important;
    visibility: visible !important;
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
}

/* 禁用状态的按钮 */
.btn:disabled,
.btn-sm:disabled,
.btn-xs:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    color: #9ca3af !important;
}

/* 确保链接样式的按钮也正确显示 */
a.btn,
a.btn-primary,
a.btn-secondary,
a.btn-success,
a.btn-warning,
a.btn-danger,
a.btn-info,
a.btn-sm,
a.btn-xs {
    text-decoration: none !important;
    color: inherit !important;
    display: inline-block !important;
}

/* 修复可能的CSS冲突 */
.btn * {
    color: inherit !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 确保按钮容器不影响文字显示 */
.action-buttons .btn,
.header-actions .btn,
.form-actions .btn,
.page-actions .btn {
    color: inherit !important;
    font-size: inherit !important;
}

/* 表格中的按钮 */
table .btn,
table .btn-sm,
table .btn-xs {
    margin: 0 2px !important;
    color: inherit !important;
    font-size: inherit !important;
}

/* 修复可能的字体问题 */
@font-face {
    font-family: 'Inter-fallback';
    src: local('Arial'), local('Helvetica'), local('sans-serif');
}

.btn,
.btn-sm,
.btn-xs {
    font-family: 'Inter', 'Inter-fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif !important;
}

/* 确保按钮在不同设备上的文字显示 */
@media (max-width: 768px) {
    .btn,
    .btn-sm,
    .btn-xs {
        font-size: 14px !important;
        padding: 8px 12px !important;
        line-height: 1.4 !important;
    }
    
    .btn-sm {
        font-size: 13px !important;
        padding: 6px 10px !important;
    }
    
    .btn-xs {
        font-size: 12px !important;
        padding: 4px 8px !important;
    }
}

/* 确保按钮文字在打印时也显示 */
@media print {
    .btn,
    .btn-sm,
    .btn-xs {
        color: #000000 !important;
        background-color: transparent !important;
        border: 1px solid #000000 !important;
        font-size: 12px !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* 修复可能的伪元素问题 */
.btn::before,
.btn::after,
.btn-sm::before,
.btn-sm::after,
.btn-xs::before,
.btn-xs::after {
    content: none !important;
    display: none !important;
}

/* 确保按钮在各种状态下的文字显示 */
.btn:focus,
.btn:active,
.btn:visited,
.btn-sm:focus,
.btn-sm:active,
.btn-sm:visited,
.btn-xs:focus,
.btn-xs:active,
.btn-xs:visited {
    color: inherit !important;
    text-decoration: none !important;
    outline: none !important;
}

/* 修复可能的CSS Transform问题 */
.btn,
.btn-sm,
.btn-xs {
    transform: none !important;
    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease !important;
}

.btn:hover,
.btn-sm:hover,
.btn-xs:hover {
    transform: translateY(-1px) !important;
    text-decoration: none !important;
    color: inherit !important;
} 