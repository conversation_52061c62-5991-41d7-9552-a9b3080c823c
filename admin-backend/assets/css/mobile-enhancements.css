/**
 * TeleShop 后台管理系统 - 移动端优化样式
 * 提供响应式设计、触摸友好界面、移动端导航等优化
 */

/* ===== 基础响应式变量 ===== */
:root {
    --mobile-header-height: 60px;
    --mobile-bottom-nav-height: 70px;
    --mobile-sidebar-width: 280px;
    --touch-target-min: 44px;
    --mobile-padding: 16px;
    --mobile-border-radius: 12px;
    --mobile-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    --mobile-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 移动端基础重置 ===== */
@media (max-width: 768px) {
    * {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
    }
    
    html {
        font-size: 14px;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
    }
    
    body {
        padding-bottom: var(--mobile-bottom-nav-height);
        overflow-x: hidden;
    }
    
    /* 防止iOS缩放 */
    input, textarea, select {
        font-size: 16px !important;
    }
}

/* ===== 响应式导航系统 ===== */
@media (max-width: 768px) {
    /* 主导航栏移动端适配 */
    .navbar, .header {
        height: var(--mobile-header-height);
        padding: 0 var(--mobile-padding);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: #fff;
        border-bottom: 1px solid #e9ecef;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    /* 侧边栏移动端适配 */
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: var(--mobile-sidebar-width);
        height: 100vh;
        z-index: 1200;
        background: #fff;
        transition: var(--mobile-transition);
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .sidebar.active {
        left: 0;
    }
    
    /* 侧边栏遮罩层 */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1100;
        opacity: 0;
        visibility: hidden;
        transition: var(--mobile-transition);
    }
    
    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }
    
    /* 汉堡菜单按钮 */
    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: var(--touch-target-min);
        height: var(--touch-target-min);
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 8px;
        transition: var(--mobile-transition);
    }
    
    .mobile-menu-toggle:hover {
        background: #f8f9fa;
    }
    
    .mobile-menu-toggle .hamburger {
        width: 20px;
        height: 16px;
        position: relative;
    }
    
    .mobile-menu-toggle .hamburger span {
        display: block;
        width: 100%;
        height: 2px;
        background: #495057;
        border-radius: 1px;
        position: absolute;
        transition: var(--mobile-transition);
    }
    
    .mobile-menu-toggle .hamburger span:nth-child(1) {
        top: 0;
    }
    
    .mobile-menu-toggle .hamburger span:nth-child(2) {
        top: 7px;
    }
    
    .mobile-menu-toggle .hamburger span:nth-child(3) {
        top: 14px;
    }
    
    .mobile-menu-toggle.active .hamburger span:nth-child(1) {
        transform: rotate(45deg);
        top: 7px;
    }
    
    .mobile-menu-toggle.active .hamburger span:nth-child(2) {
        opacity: 0;
    }
    
    .mobile-menu-toggle.active .hamburger span:nth-child(3) {
        transform: rotate(-45deg);
        top: 7px;
    }
    
    /* 主内容区域适配 */
    .main-content {
        margin-top: var(--mobile-header-height);
        padding: var(--mobile-padding);
        margin-left: 0;
        width: 100%;
    }
}

/* ===== 触摸友好界面 ===== */
@media (max-width: 768px) {
    /* 最小触摸目标尺寸 */
    button, 
    .btn, 
    a, 
    .clickable,
    input[type="checkbox"],
    input[type="radio"],
    .form-control,
    .dropdown-toggle {
        min-height: var(--touch-target-min);
        min-width: var(--touch-target-min);
    }
    
    /* 按钮触摸优化 */
    .btn {
        padding: 12px 20px;
        font-size: 16px;
        border-radius: var(--mobile-border-radius);
        touch-action: manipulation;
        user-select: none;
    }
    
    .btn-sm {
        padding: 8px 16px;
        font-size: 14px;
        min-height: 36px;
    }
    
    .btn-lg {
        padding: 16px 24px;
        font-size: 18px;
        min-height: 52px;
    }
    
    /* 表单控件触摸优化 */
    .form-control, 
    .form-select {
        height: var(--touch-target-min);
        padding: 12px 16px;
        font-size: 16px;
        border-radius: var(--mobile-border-radius);
        border: 2px solid #e9ecef;
        transition: var(--mobile-transition);
    }
    
    .form-control:focus, 
    .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    }
    
    /* 触摸反馈效果 */
    .touch-feedback {
        position: relative;
        overflow: hidden;
    }
    
    .touch-feedback::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
    }
    
    .touch-feedback:active::after {
        width: 300px;
        height: 300px;
    }
}

/* ===== 移动端表格优化 ===== */
@media (max-width: 768px) {
    /* 表格响应式处理 */
    .table-responsive-mobile {
        border: none;
        border-radius: var(--mobile-border-radius);
        overflow: hidden;
    }
    
    .table-responsive-mobile .table {
        display: none;
    }
    
    /* 卡片式表格布局 */
    .mobile-table-cards {
        display: block;
    }
    
    .mobile-table-card {
        display: block;
        background: #fff;
        border-radius: var(--mobile-border-radius);
        box-shadow: var(--mobile-shadow);
        margin-bottom: 16px;
        padding: 16px;
        position: relative;
    }
    
    .mobile-table-card::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: #007bff;
        border-radius: 4px 0 0 4px;
    }
    
    .mobile-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .mobile-card-title {
        font-weight: 600;
        font-size: 16px;
        color: #212529;
    }
    
    .mobile-card-status {
        padding: 4px 8px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .mobile-card-body {
        margin-bottom: 12px;
    }
    
    .mobile-card-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .mobile-card-label {
        font-size: 14px;
        color: #6c757d;
        font-weight: 500;
    }
    
    .mobile-card-value {
        font-size: 14px;
        color: #212529;
        text-align: right;
    }
    
    .mobile-card-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        border-top: 1px solid #e9ecef;
        padding-top: 12px;
    }
    
    .mobile-card-actions .btn {
        flex: 1;
        min-width: auto;
        font-size: 14px;
        padding: 8px 12px;
    }
}

/* ===== 移动端模态框优化 ===== */
@media (max-width: 768px) {
    .modal {
        padding: 0;
    }
    
    .modal-dialog {
        width: 100%;
        height: 100%;
        margin: 0;
        max-width: none;
        max-height: none;
    }
    
    .modal-content {
        height: 100vh;
        border-radius: 0;
        border: none;
        display: flex;
        flex-direction: column;
    }
    
    /* 底部弹出式模态框 */
    .modal-bottom .modal-dialog {
        display: flex;
        align-items: flex-end;
        height: 100vh;
        padding: 0;
    }
    
    .modal-bottom .modal-content {
        height: auto;
        max-height: 80vh;
        border-radius: var(--mobile-border-radius) var(--mobile-border-radius) 0 0;
        transform: translateY(100%);
        transition: var(--mobile-transition);
    }
    
    .modal-bottom.show .modal-content {
        transform: translateY(0);
    }
    
    .modal-header {
        padding: 20px var(--mobile-padding);
        border-bottom: 1px solid #e9ecef;
        flex-shrink: 0;
        position: relative;
    }
    
    .modal-header::before {
        content: '';
        position: absolute;
        top: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 4px;
        background: #dee2e6;
        border-radius: 2px;
    }
    
    .modal-body {
        padding: var(--mobile-padding);
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .modal-footer {
        padding: var(--mobile-padding);
        border-top: 1px solid #e9ecef;
        flex-shrink: 0;
        flex-direction: column;
        gap: 12px;
    }
    
    .modal-footer .btn {
        width: 100%;
        margin: 0;
    }
}

/* ===== 移动端表单优化 ===== */
@media (max-width: 768px) {
    .form-group, .mb-3 {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #495057;
    }
    
    .form-control {
        background-clip: padding-box;
    }
    
    .form-control:disabled, 
    .form-control[readonly] {
        background-color: #f8f9fa;
        opacity: 1;
    }
    
    /* 文件上传优化 */
    .file-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: var(--mobile-border-radius);
        padding: 40px 20px;
        text-align: center;
        transition: var(--mobile-transition);
        cursor: pointer;
    }
    
    .file-upload-area:hover,
    .file-upload-area.dragover {
        border-color: #007bff;
        background: #f8f9ff;
    }
    
    /* 切换开关优化 */
    .custom-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }
    
    .custom-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .switch-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: var(--mobile-transition);
        border-radius: 34px;
    }
    
    .switch-slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: var(--mobile-transition);
        border-radius: 50%;
    }
    
    input:checked + .switch-slider {
        background-color: #007bff;
    }
    
    input:checked + .switch-slider:before {
        transform: translateX(26px);
    }
}

/* ===== 底部导航栏 ===== */
@media (max-width: 768px) {
    .mobile-bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: var(--mobile-bottom-nav-height);
        background: #fff;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: space-around;
        align-items: center;
        z-index: 1000;
        padding: 8px 0;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .mobile-bottom-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4px 8px;
        text-decoration: none;
        color: #6c757d;
        transition: var(--mobile-transition);
        border-radius: 8px;
        min-width: var(--touch-target-min);
        position: relative;
    }
    
    .mobile-bottom-nav-item:hover,
    .mobile-bottom-nav-item.active {
        color: #007bff;
        background: rgba(0, 123, 255, 0.1);
    }
    
    .mobile-bottom-nav-item i {
        font-size: 20px;
        margin-bottom: 2px;
    }
    
    .mobile-bottom-nav-item span {
        font-size: 10px;
        font-weight: 500;
    }
    
    .mobile-bottom-nav-item .badge {
        position: absolute;
        top: 2px;
        right: 8px;
        min-width: 16px;
        height: 16px;
        background: #dc3545;
        color: white;
        border-radius: 8px;
        font-size: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* ===== 滑动手势支持 ===== */
@media (max-width: 768px) {
    .swipe-container {
        overflow: hidden;
        position: relative;
    }
    
    .swipe-item {
        position: relative;
        background: #fff;
        transition: transform 0.3s ease;
    }
    
    .swipe-actions {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        display: flex;
        align-items: center;
        background: #dc3545;
        padding: 0 20px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .swipe-item.swiped .swipe-actions {
        transform: translateX(0);
    }
    
    .swipe-action-btn {
        background: none;
        border: none;
        color: white;
        font-size: 16px;
        padding: 12px;
        border-radius: 50%;
        cursor: pointer;
        transition: var(--mobile-transition);
    }
    
    .swipe-action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
    }
}

/* ===== 下拉刷新 ===== */
@media (max-width: 768px) {
    .pull-to-refresh {
        position: relative;
        overflow: hidden;
    }
    
    .pull-refresh-indicator {
        position: absolute;
        top: -60px;
        left: 0;
        right: 0;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        color: #6c757d;
        font-size: 14px;
        transition: var(--mobile-transition);
    }
    
    .pull-refresh-indicator.active {
        top: 0;
    }
    
    .pull-refresh-indicator i {
        margin-right: 8px;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
}

/* ===== 无限滚动 ===== */
@media (max-width: 768px) {
    .infinite-scroll-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        color: #6c757d;
        font-size: 14px;
    }
    
    .infinite-scroll-loading i {
        margin-right: 8px;
        animation: spin 1s linear infinite;
    }
    
    .infinite-scroll-end {
        text-align: center;
        padding: 20px;
        color: #6c757d;
        font-size: 14px;
        border-top: 1px solid #e9ecef;
    }
}

/* ===== 浮动操作按钮 ===== */
@media (max-width: 768px) {
    .fab {
        position: fixed;
        bottom: calc(var(--mobile-bottom-nav-height) + 20px);
        right: 20px;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: #007bff;
        color: white;
        border: none;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        cursor: pointer;
        transition: var(--mobile-transition);
        z-index: 999;
    }
    
    .fab:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(0, 123, 255, 0.5);
    }
    
    .fab:active {
        transform: scale(0.95);
    }
    
    /* 多个FAB */
    .fab-group {
        position: fixed;
        bottom: calc(var(--mobile-bottom-nav-height) + 20px);
        right: 20px;
        z-index: 999;
    }
    
    .fab-group .fab {
        position: relative;
        bottom: auto;
        right: auto;
        margin-bottom: 12px;
    }
    
    .fab-group .fab-mini {
        width: 40px;
        height: 40px;
        font-size: 16px;
        opacity: 0;
        transform: scale(0);
        transition: var(--mobile-transition);
    }
    
    .fab-group.open .fab-mini {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== 状态栏适配 (iOS) ===== */
@media (max-width: 768px) {
    @supports (padding-top: env(safe-area-inset-top)) {
        .navbar, .header {
            padding-top: calc(env(safe-area-inset-top) + 10px);
            height: calc(var(--mobile-header-height) + env(safe-area-inset-top));
        }
        
        .main-content {
            margin-top: calc(var(--mobile-header-height) + env(safe-area-inset-top));
        }
        
        .mobile-bottom-nav {
            padding-bottom: calc(8px + env(safe-area-inset-bottom));
            height: calc(var(--mobile-bottom-nav-height) + env(safe-area-inset-bottom));
        }
        
        body {
            padding-bottom: calc(var(--mobile-bottom-nav-height) + env(safe-area-inset-bottom));
        }
    }
}

/* ===== 横屏适配 ===== */
@media (max-width: 768px) and (orientation: landscape) {
    .navbar, .header {
        height: 50px;
    }
    
    .main-content {
        margin-top: 50px;
    }
    
    .mobile-bottom-nav {
        height: 60px;
        padding: 6px 0;
    }
    
    .mobile-bottom-nav-item i {
        font-size: 18px;
    }
    
    .mobile-bottom-nav-item span {
        font-size: 9px;
    }
    
    body {
        padding-bottom: 60px;
    }
    
    .fab {
        bottom: 20px;
    }
    
    .fab-group {
        bottom: 20px;
    }
}

/* ===== 暗色模式支持 ===== */
@media (max-width: 768px) and (prefers-color-scheme: dark) {
    .navbar, .header {
        background: #1a1a1a;
        border-bottom-color: #333;
        color: #fff;
    }
    
    .sidebar {
        background: #1a1a1a;
        color: #fff;
    }
    
    .mobile-bottom-nav {
        background: #1a1a1a;
        border-top-color: #333;
    }
    
    .mobile-bottom-nav-item {
        color: #aaa;
    }
    
    .mobile-bottom-nav-item:hover,
    .mobile-bottom-nav-item.active {
        color: #007bff;
        background: rgba(0, 123, 255, 0.2);
    }
    
    .mobile-table-card {
        background: #2d2d2d;
        color: #fff;
    }
    
    .modal-content {
        background: #1a1a1a;
        color: #fff;
    }
    
    .form-control {
        background: #2d2d2d;
        border-color: #333;
        color: #fff;
    }
    
    .form-control:focus {
        background: #2d2d2d;
        border-color: #007bff;
    }
}

/* ===== 自定义滚动条 (移动端Webkit) ===== */
@media (max-width: 768px) {
    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }
    
    ::-webkit-scrollbar-track {
        background: transparent;
    }
    
    ::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
    }
}

/* ===== 工具类 ===== */
@media (max-width: 768px) {
    .mobile-only {
        display: block !important;
    }
    
    .desktop-only {
        display: none !important;
    }
    
    .mobile-hide {
        display: none !important;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
    
    .mobile-text-center {
        text-align: center !important;
    }
    
    .mobile-no-padding {
        padding: 0 !important;
    }
    
    .mobile-padding {
        padding: var(--mobile-padding) !important;
    }
    
    .mobile-margin-bottom {
        margin-bottom: var(--mobile-padding) !important;
    }
}

/* ===== 性能优化 ===== */
@media (max-width: 768px) {
    /* 硬件加速 */
    .sidebar,
    .modal-content,
    .mobile-bottom-nav,
    .fab,
    .touch-feedback::after {
        will-change: transform;
        transform: translateZ(0);
    }
    
    /* 减少重绘 */
    .mobile-table-card,
    .mobile-bottom-nav-item,
    .btn {
        contain: layout;
    }
}

/* ===== 特殊设备适配 ===== */
/* iPhone X及以上机型 */
@media only screen 
    and (device-width: 375px) 
    and (device-height: 812px) 
    and (-webkit-device-pixel-ratio: 3) {
    
    .navbar, .header {
        padding-top: calc(44px + 10px);
        height: calc(var(--mobile-header-height) + 44px);
    }
    
    .main-content {
        margin-top: calc(var(--mobile-header-height) + 44px);
    }
}

/* iPad适配 */
@media (min-width: 768px) and (max-width: 1024px) {
    .mobile-table-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;
    }
    
    .modal-dialog {
        max-width: 600px;
        margin: 2rem auto;
    }
    
    .modal-content {
        border-radius: var(--mobile-border-radius);
        height: auto;
    }
} 