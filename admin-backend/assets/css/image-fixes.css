/* TeleShop Admin - 图片修复和占位符样式 */

/* 基础图片样式修复 */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* 图片加载错误处理 */
img[src=""],
img:not([src]),
img[src="#"] {
    display: none;
}

/* 通用图片占位符 */
.image-placeholder {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    font-size: 16px;
    position: relative;
    overflow: hidden;
    user-select: none;
    border-radius: 6px;
}

.image-placeholder::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 不同尺寸的占位符 */
.image-placeholder.size-xs {
    width: 24px;
    height: 24px;
    font-size: 10px;
}

.image-placeholder.size-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
}

.image-placeholder.size-md {
    width: 48px;
    height: 48px;
    font-size: 16px;
}

.image-placeholder.size-lg {
    width: 64px;
    height: 64px;
    font-size: 20px;
}

.image-placeholder.size-xl {
    width: 96px;
    height: 96px;
    font-size: 24px;
}

/* 不同类型的占位符 */
.image-placeholder.type-avatar {
    border-radius: 50%;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #2563eb;
}

.image-placeholder.type-product {
    border-radius: 8px;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #d97706;
}

.image-placeholder.type-logo {
    border-radius: 4px;
    background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
    color: #4338ca;
}

.image-placeholder.type-banner {
    border-radius: 12px;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #059669;
    min-height: 120px;
}

/* 用户头像占位符 */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0284c7;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

/* 商品图片占位符 */
.product-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #d97706;
    font-size: 18px;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    object-fit: cover;
}

/* Logo占位符 */
.logo-placeholder {
    width: 120px;
    height: 40px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    font-weight: 600;
    font-size: 12px;
    border: 1px solid #e2e8f0;
}

/* 图片加载状态 */
.image-loading {
    position: relative;
    overflow: hidden;
}

.image-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 图片错误处理 */
.image-error {
    background: #fee2e2;
    color: #dc2626;
    border: 1px dashed #fca5a5;
}

/* 响应式图片容器 */
.responsive-image {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 比例 */
    overflow: hidden;
    border-radius: 8px;
}

.responsive-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 图片上传预览 */
.image-upload-preview {
    width: 100%;
    max-width: 200px;
    height: 200px;
    border: 2px dashed #cbd5e1;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    background: #fafafa;
}

.image-upload-preview:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.image-upload-preview.has-image {
    border-style: solid;
    border-color: #e2e8f0;
}

.upload-icon {
    font-size: 24px;
    color: #94a3b8;
    margin-bottom: 8px;
}

.upload-text {
    font-size: 14px;
    color: #64748b;
    text-align: center;
}

/* 图片画廊 */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
    padding: 16px;
}

.gallery-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1;
    background: #f1f5f9;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .product-image {
        width: 48px;
        height: 48px;
        font-size: 14px;
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
    
    .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 12px;
        padding: 12px;
    }
    
    .responsive-image {
        padding-bottom: 75%; /* 4:3 比例 */
    }
}

@media (max-width: 480px) {
    .product-image {
        width: 40px;
        height: 40px;
        font-size: 12px;
    }
    
    .user-avatar {
        width: 28px;
        height: 28px;
        font-size: 10px;
    }
    
    .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
        padding: 8px;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .image-placeholder {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
        color: #9ca3af;
    }
    
    .image-placeholder.type-avatar {
        background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
        color: #93c5fd;
    }
    
    .image-placeholder.type-product {
        background: linear-gradient(135deg, #92400e 0%, #b45309 100%);
        color: #fbbf24;
    }
    
    .logo-placeholder {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
        color: #9ca3af;
        border-color: #4b5563;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .image-placeholder {
        border: 2px solid #000;
        background: #fff;
        color: #000;
    }
    
    .user-avatar {
        border: 2px solid #000;
        background: #fff;
        color: #000;
    }
}

/* 打印样式 */
@media print {
    .image-placeholder {
        background: #f5f5f5 !important;
        color: #333 !important;
        border: 1px solid #ccc;
    }
    
    .image-upload-preview {
        display: none;
    }
} 