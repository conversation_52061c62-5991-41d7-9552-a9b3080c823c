/* TeleShop 后台管理系统样式 */

/* 基础变量定义 */
:root {
    --primary-color: #007AFF;
    --primary-dark: #0051D5;
    --secondary-color: #5AC8FA;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --error-color: #FF3B30;
    --info-color: #5856D6;
    
    --bg-color: #F8F9FA;
    --surface-color: #FFFFFF;
    --card-color: #FFFFFF;
    --border-color: #E5E5E7;
    --divider-color: #F2F2F7;
    
    --text-primary: #1D1D1F;
    --text-secondary: #6D6D80;
    --text-tertiary: #8E8E93;
    --text-disabled: #C7C7CC;
    
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    --space-xs: 4px;
    --space-sm: 8px;
    --space-md: 16px;
    --space-lg: 24px;
    --space-xl: 32px;
    --space-2xl: 48px;
    
    --transition-fast: 150ms ease;
    --transition-normal: 250ms ease;
    --transition-slow: 350ms ease;
    
    --sidebar-width: 280px;
    --header-height: 80px;
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    height: 100%;
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-color);
    overflow-x: hidden;
    height: 100%;
    margin: 0;
    padding: 0;
}

/* 登录页面样式 */
.login-page {
    min-height: 100vh;
    height: 100vh;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-lg);
}

.login-container {
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--space-2xl);
    width: 100%;
    max-width: 480px;
    animation: slideUp 0.5s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: var(--space-2xl);
}

.admin-logo {
    width: 64px;
    height: 64px;
    margin-bottom: var(--space-md);
}

.login-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
}

.login-header p {
    color: var(--text-secondary);
    font-size: 16px;
}

.login-form {
    margin-bottom: var(--space-xl);
}

.form-group {
    margin-bottom: var(--space-lg);
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    font-size: 14px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: var(--space-md);
    color: var(--text-tertiary);
    z-index: 2;
}

.input-wrapper input {
    width: 100%;
    padding: var(--space-md) var(--space-md) var(--space-md) 48px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 16px;
    transition: var(--transition-fast);
    background: var(--surface-color);
}

.input-wrapper input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.toggle-password {
    position: absolute;
    right: var(--space-md);
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: var(--space-sm);
    z-index: 2;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-secondary);
}

.checkbox-wrapper input {
    margin-right: var(--space-sm);
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.login-btn {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.security-notice {
    background: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: var(--radius-md);
    padding: var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: var(--space-lg);
}

.login-footer {
    text-align: center;
    color: var(--text-tertiary);
    font-size: 12px;
}

.login-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

/* 主后台界面布局 */
.admin-dashboard {
    display: flex;
    height: 100vh;
    overflow: hidden;
    background: var(--bg-color);
}

/* 顶部导航栏 */
.admin-header {
    height: var(--header-height);
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-lg);
    position: relative;
    z-index: 100;
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--bg-color);
    color: var(--text-primary);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.header-logo {
    width: 32px;
    height: 32px;
}

.brand-name {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
}

.header-center {
    flex: 1;
    max-width: 600px;
    margin: 0 var(--space-xl);
}

.global-search {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
}

.global-search:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.global-search i {
    position: absolute;
    left: var(--space-md);
    color: var(--text-tertiary);
}

.global-search input {
    flex: 1;
    padding: var(--space-md) var(--space-md) var(--space-md) 48px;
    border: none;
    background: transparent;
    font-size: 14px;
    color: var(--text-primary);
}

.global-search input::placeholder {
    color: var(--text-tertiary);
}

.search-btn {
    padding: var(--space-md) var(--space-lg);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.header-right {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.action-btn:hover {
    background: var(--bg-color);
    color: var(--text-primary);
}

.notification-badge,
.message-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--error-color);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translate(6px, -6px);
}

.admin-profile {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
}

.admin-profile:hover {
    background: var(--bg-color);
}

.admin-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.admin-info {
    display: flex;
    flex-direction: column;
}

.admin-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.admin-role {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 侧边栏 */
.admin-sidebar {
    width: var(--sidebar-width);
    background: var(--surface-color);
    border-right: 1px solid var(--border-color);
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    transition: var(--transition-normal);
}

.admin-sidebar.collapsed {
    width: 80px;
}

.sidebar-menu {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-lg) 0;
}

.menu-section {
    margin-bottom: var(--space-xl);
}

.menu-section h3 {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 var(--space-lg) var(--space-md) var(--space-lg);
}

.menu-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu-item {
    margin-bottom: var(--space-xs);
}

.menu-item a {
    display: flex;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition-fast);
    border-right: 3px solid transparent;
    cursor: pointer;
}

.menu-item a:hover {
    background: var(--bg-color);
    color: var(--text-primary);
}

.menu-item.active a {
    background: #eff6ff;
    color: var(--primary-color);
    border-right-color: var(--primary-color);
}

.menu-item i {
    width: 20px;
    margin-right: var(--space-md);
    font-size: 16px;
    flex-shrink: 0;
}

.menu-item span {
    flex: 1;
}

.menu-count {
    margin-left: auto;
    background: #f1f5f9;
    color: var(--text-tertiary);
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.menu-item.active .menu-count {
    background: #dbeafe;
    color: var(--primary-color);
}

.sidebar-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--border-color);
    background: var(--surface-color);
}

.system-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 12px;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-dot.online {
    background: var(--success-color);
}

.system-version {
    font-size: 12px;
    color: var(--text-tertiary);
}

/* 主内容区域 */
.admin-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--bg-color);
}

.admin-main.expanded {
    margin-left: 0;
}

.main-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    background: var(--surface-color);
}

/* 页面加载状态 */
.page-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-secondary);
}

.page-loading i {
    font-size: 48px;
    margin-bottom: var(--space-lg);
    animation: spin 1s linear infinite;
}

/* 通知和消息面板 */
.notification-panel,
.message-panel {
    position: fixed;
    top: calc(var(--header-height) + 8px);
    right: var(--space-lg);
    width: 380px;
    max-height: 500px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    transform: translateY(-10px);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    animation: slideDown var(--transition-normal) ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.panel-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.panel-header button {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.panel-content {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item,
.message-item {
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    gap: var(--space-md);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.notification-item:hover,
.message-item:hover {
    background: var(--bg-color);
}

.notification-item.unread,
.message-item.unread {
    background: #eff6ff;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.notification-content,
.message-content {
    flex: 1;
}

.notification-content h4,
.message-content h4 {
    margin: 0 0 var(--space-xs) 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.notification-content p,
.message-content p {
    margin: 0 0 var(--space-sm) 0;
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.notification-time,
.message-time {
    font-size: 12px;
    color: var(--text-tertiary);
}

/* 个人资料菜单 */
.profile-menu {
    position: fixed;
    top: calc(var(--header-height) - 8px);
    right: var(--space-lg);
    width: 220px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    transform: translateY(-10px);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    overflow: hidden;
}

.profile-menu-item {
    padding: var(--space-md) var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-md);
    cursor: pointer;
    transition: background var(--transition-fast);
    font-size: 14px;
    color: var(--text-secondary);
}

.profile-menu-item:hover {
    background: var(--bg-color);
}

.profile-menu-item.logout {
    color: var(--error-color);
}

.profile-menu-item.logout:hover {
    background: #fef2f2;
}

.profile-menu-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--space-sm) 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --sidebar-width: 100%;
        --header-height: 60px;
    }
    
    .login-container {
        padding: var(--space-lg);
        margin: var(--space-md);
    }
    
    .admin-header {
        height: var(--header-height);
        padding: 0 var(--space-md);
    }
    
    .header-center {
        display: none;
    }
    
    .admin-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: var(--sidebar-width);
        height: 100vh;
        z-index: 1200;
        transform: translateX(-100%);
    }
    
    .admin-sidebar.open {
        transform: translateX(0);
    }
    
    .admin-main {
        width: 100%;
        margin-left: 0;
    }
    
    .notification-panel,
    .message-panel {
        right: var(--space-md);
        width: calc(100vw - 2 * var(--space-md));
        max-width: 380px;
    }
    
    .profile-menu {
        right: var(--space-md);
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: var(--text-primary);
    color: white;
    text-align: center;
    border-radius: var(--radius-sm);
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity var(--transition-fast);
    font-size: 12px;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--text-primary) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* 动画 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==================== 智能搜索功能样式 ==================== */

/* 搜索建议框 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    display: none;
    margin-top: 4px;
}

/* 搜索建议分类 */
.suggestion-category {
    padding: 12px 16px 8px;
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

/* 搜索建议项 */
.suggestion-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s;
    border-bottom: 1px solid #f1f5f9;
}

.suggestion-item:hover {
    background: #f8fafc;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item i {
    width: 20px;
    margin-right: 12px;
    color: #64748b;
    font-size: 14px;
}

.suggestion-content {
    flex: 1;
}

.suggestion-title {
    font-size: 14px;
    font-weight: 500;
    color: #1e293b;
    margin-bottom: 2px;
}

.suggestion-desc {
    font-size: 12px;
    color: #64748b;
}

.suggestion-type {
    font-size: 12px;
    color: #64748b;
    background: #f1f5f9;
    padding: 2px 8px;
    border-radius: 6px;
}

/* 无建议提示 */
.no-suggestions {
    padding: 20px 16px;
    text-align: center;
    color: #64748b;
    font-size: 14px;
}

/* 高级搜索选项 */
.advanced-search-option {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
    color: #3b82f6;
    font-weight: 500;
    transition: all 0.2s;
}

.advanced-search-option:hover {
    background: #eff6ff;
}

.advanced-search-option i {
    margin-right: 8px;
}

/* 高级搜索模态框 */
.advanced-search-modal .modal-content {
    width: 90%;
    max-width: 600px;
}

.search-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-size: 14px;
    cursor: pointer;
}

.checkbox-group input {
    margin-right: 6px;
}

/* 搜索历史 */
.search-history {
    margin-top: 24px;
}

.search-history h4 {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 12px;
}

.history-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.history-tag {
    background: #f1f5f9;
    color: #64748b;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.history-tag:hover {
    background: #e2e8f0;
    color: #1e293b;
}

/* 搜索结果页面 */
.search-results-container {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
}

.search-results-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.search-results-header h2 {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 8px;
}

.search-info {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    font-size: 14px;
    color: #64748b;
}

.search-info span {
    display: flex;
    align-items: center;
}

.search-info strong {
    color: #1e293b;
    margin-left: 4px;
}

/* 搜索结果标签页 */
.results-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 24px;
    background: #f1f5f9;
    padding: 4px;
    border-radius: 8px;
}

.tab-btn {
    flex: 1;
    padding: 8px 16px;
    background: transparent;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
}

.tab-btn.active {
    background: white;
    color: #3b82f6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.5);
}

/* 搜索结果列表 */
.results-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
}

.result-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.2s;
    cursor: pointer;
}

.result-item:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-icon {
    width: 40px;
    height: 40px;
    background: #f8fafc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.result-icon i {
    font-size: 16px;
    color: #64748b;
}

.result-content {
    flex: 1;
}

.result-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.result-description {
    font-size: 14px;
    color: #64748b;
    margin-bottom: 4px;
}

.result-meta {
    font-size: 12px;
    color: #94a3b8;
    margin: 0;
}

.result-status {
    margin-left: 16px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.status-success {
    background: #dcfce7;
    color: #16a34a;
}

.status-badge.status-warning {
    background: #fef3c7;
    color: #d97706;
}

.status-badge.status-error {
    background: #fee2e2;
    color: #dc2626;
}

/* 搜索结果分页 */
.results-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    padding: 16px;
    border-top: 1px solid #e2e8f0;
}

.pagination-info {
    font-size: 14px;
    color: #64748b;
}

/* 模态框样式增强 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s;
}

.modal-overlay.show {
    opacity: 1;
}

.modal-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    max-height: 90vh;
    overflow: hidden;
    transform: translateY(20px);
    transition: transform 0.3s;
}

.modal-overlay.show .modal-content {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.modal-close {
    width: 32px;
    height: 32px;
    background: #f1f5f9;
    border: none;
    border-radius: 8px;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
}

.modal-close:hover {
    background: #e2e8f0;
    color: #1e293b;
}

.modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
}

.btn-primary {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-secondary {
    background: white;
    color: #64748b;
    border: 1px solid #e2e8f0;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-secondary:hover {
    border-color: #cbd5e1;
    color: #1e293b;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .search-form .form-row {
        grid-template-columns: 1fr;
    }
    
    .search-info {
        flex-direction: column;
        gap: 8px;
    }
    
    .results-tabs {
        flex-direction: column;
    }
    
    .result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .result-status {
        margin-left: 0;
        align-self: flex-end;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
} 