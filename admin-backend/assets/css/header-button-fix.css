/* TeleShop Admin - 顶部按钮文字显示修复 */

/* 修复页面头部按钮容器 */
.page-actions,
.header-actions,
.dashboard-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 12px !important;
    align-items: center !important;
    justify-content: flex-end !important;
    min-width: 0 !important;
    flex-shrink: 0 !important;
}

/* 修复顶部按钮样式 */
.page-actions .action-btn,
.header-actions .action-btn,
.dashboard-actions .action-btn,
.page-actions button,
.header-actions button,
.dashboard-actions button {
    /* 确保按钮有足够的空间显示文字 */
    min-width: auto !important;
    max-width: none !important;
    width: auto !important;
    
    /* 防止文字被截断 */
    white-space: nowrap !important;
    overflow: visible !important;
    text-overflow: clip !important;
    
    /* 确保文字显示 */
    font-size: 14px !important;
    line-height: 1.4 !important;
    padding: 10px 16px !important;
    
    /* 确保文字颜色正确 */
    color: inherit !important;
    
    /* 防止按钮内容被隐藏 */
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    
    /* 字体设置 */
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
    font-weight: 500 !important;
    
    /* 边框和背景 */
    border: 1px solid #e2e8f0 !important;
    border-radius: 8px !important;
    background: white !important;
    
    /* 过渡效果 */
    transition: all 0.2s ease !important;
    
    /* 确保按钮可以点击 */
    cursor: pointer !important;
    text-decoration: none !important;
    
    /* 防止按钮收缩 */
    flex-shrink: 0 !important;
}

/* 修复不同状态的按钮颜色 */
.page-actions .action-btn:not(.primary):not(.warning):not(.danger),
.header-actions .action-btn:not(.primary):not(.warning):not(.danger),
.dashboard-actions .action-btn:not(.primary):not(.warning):not(.danger) {
    color: #64748b !important;
    background: white !important;
    border-color: #e2e8f0 !important;
}

.page-actions .action-btn.primary,
.header-actions .action-btn.primary,
.dashboard-actions .action-btn.primary {
    background: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
}

/* 悬停效果 */
.page-actions .action-btn:hover,
.header-actions .action-btn:hover,
.dashboard-actions .action-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    text-decoration: none !important;
}

/* 修复按钮内的图标和文字 */
.page-actions .action-btn i,
.header-actions .action-btn i,
.dashboard-actions .action-btn i {
    margin-right: 6px !important;
    font-size: 14px !important;
    opacity: 1 !important;
    visibility: visible !important;
    flex-shrink: 0 !important;
}

/* 修复页面头部布局 */
.page-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 16px !important;
    margin-bottom: 24px !important;
    min-height: 60px !important;
}

/* 响应式设计修复 */
@media (max-width: 768px) {
    .page-actions .action-btn,
    .header-actions .action-btn,
    .dashboard-actions .action-btn {
        font-size: 12px !important;
        padding: 8px 12px !important;
    }
} 