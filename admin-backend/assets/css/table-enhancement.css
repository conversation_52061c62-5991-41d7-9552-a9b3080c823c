/* 表格增强样式 - 统一用户列表和商品列表显示 */

/* 表格基础样式增强 */
.user-table, .users-table, .product-table, .products-table, .merchant-table {
    width: 100% !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    background: white !important;
    font-size: 14px !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.user-table thead th, 
.users-table thead th, 
.product-table thead th, 
.products-table thead th, 
.merchant-table thead th {
    background: #f8fafc !important;
    color: #374151 !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
    padding: 16px 12px !important;
    border-bottom: 1px solid #e2e8f0 !important;
    text-align: left !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
    white-space: nowrap !important;
}

.user-table tbody td, 
.users-table tbody td, 
.product-table tbody td, 
.products-table tbody td, 
.merchant-table tbody td {
    padding: 16px 12px !important;
    border-bottom: 1px solid #f1f5f9 !important;
    vertical-align: middle !important;
    color: #1e293b !important;
    font-size: 14px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 0 !important;
}

.user-table tbody tr:last-child td, 
.users-table tbody tr:last-child td, 
.product-table tbody tr:last-child td, 
.products-table tbody tr:last-child td, 
.merchant-table tbody tr:last-child td {
    border-bottom: none !important;
}

.user-table tbody tr:hover, 
.users-table tbody tr:hover, 
.product-table tbody tr:hover, 
.products-table tbody tr:hover, 
.merchant-table tbody tr:hover {
    background: #f8fafc !important;
}

/* 用户信息展示样式 */
.user-info {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    min-width: 220px !important;
    max-width: 280px !important;
}

.user-avatar {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
    flex-shrink: 0 !important;
    border: 2px solid #e2e8f0 !important;
}

.user-details {
    flex: 1 !important;
    overflow: hidden !important;
}

.user-name {
    font-weight: 600 !important;
    color: #1e293b !important;
    font-size: 14px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.user-email {
    color: #64748b !important;
    font-size: 12px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    margin-top: 2px !important;
}

/* 商品信息展示样式 */
.product-info {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    min-width: 220px !important;
    max-width: 280px !important;
}

.product-image {
    width: 40px !important;
    height: 40px !important;
    border-radius: 6px !important;
    object-fit: cover !important;
    flex-shrink: 0 !important;
    border: 1px solid #e2e8f0 !important;
    background: #f1f5f9 !important;
}

.product-details {
    flex: 1 !important;
    overflow: hidden !important;
}

.product-name {
    font-weight: 600 !important;
    color: #1e293b !important;
    font-size: 14px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.product-sku {
    color: #64748b !important;
    font-size: 12px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    margin-top: 2px !important;
}

/* 商户信息展示样式 */
.merchant-info {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    min-width: 220px !important;
    max-width: 280px !important;
}

.merchant-logo {
    width: 40px !important;
    height: 40px !important;
    border-radius: 6px !important;
    object-fit: cover !important;
    flex-shrink: 0 !important;
    border: 1px solid #e2e8f0 !important;
    background: #f1f5f9 !important;
}

.merchant-details {
    flex: 1 !important;
    overflow: hidden !important;
}

.merchant-name {
    font-weight: 600 !important;
    color: #1e293b !important;
    font-size: 14px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.merchant-contact {
    color: #64748b !important;
    font-size: 12px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    margin-top: 2px !important;
}

/* 状态徽章样式 */
.status-badge {
    display: inline-flex !important;
    align-items: center !important;
    padding: 4px 10px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-transform: capitalize !important;
    white-space: nowrap !important;
}

.status-badge.status-active, .status-badge.active {
    background: #dcfce7 !important;
    color: #16a34a !important;
}

.status-badge.status-inactive, .status-badge.inactive {
    background: #f1f5f9 !important;
    color: #64748b !important;
}

.status-badge.status-pending, .status-badge.pending {
    background: #fef3c7 !important;
    color: #d97706 !important;
}

.status-badge.status-banned, .status-badge.banned {
    background: #fee2e2 !important;
    color: #dc2626 !important;
}

.status-badge.status-suspended, .status-badge.suspended {
    background: #fef2f2 !important;
    color: #ef4444 !important;
}

/* 角色徽章样式 */
.role-badge {
    display: inline-flex !important;
    align-items: center !important;
    padding: 4px 10px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-transform: capitalize !important;
    white-space: nowrap !important;
}

.role-badge.role-user {
    background: #f1f5f9 !important;
    color: #64748b !important;
}

.role-badge.role-vip {
    background: #fef3c7 !important;
    color: #d97706 !important;
}

.role-badge.role-seller {
    background: #eff6ff !important;
    color: #2563eb !important;
}

.role-badge.role-admin {
    background: #f3e8ff !important;
    color: #8b5cf6 !important;
}

/* 商户类型徽章 */
.type-badge {
    display: inline-flex !important;
    align-items: center !important;
    padding: 4px 10px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    white-space: nowrap !important;
}

.type-badge.flagship {
    background: #fef3c7 !important;
    color: #d97706 !important;
}

.type-badge.enterprise {
    background: #eff6ff !important;
    color: #2563eb !important;
}

.type-badge.individual {
    background: #f1f5f9 !important;
    color: #64748b !important;
}

/* 价格显示样式 */
.price-display {
    font-weight: 600 !important;
    color: #ef4444 !important;
    font-size: 14px !important;
    white-space: nowrap !important;
}

.price-original {
    color: #9ca3af !important;
    text-decoration: line-through !important;
    font-size: 12px !important;
    margin-left: 8px !important;
}

/* 钱包余额样式 */
.wallet-amount {
    font-weight: 600 !important;
    color: #059669 !important;
    font-size: 14px !important;
    white-space: nowrap !important;
}

/* 在线状态指示器 */
.online-indicator {
    display: inline-block !important;
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    background: #10b981 !important;
    margin-right: 6px !important;
    animation: pulse 2s infinite !important;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.last-active {
    font-size: 12px !important;
    color: #64748b !important;
    display: flex !important;
    align-items: center !important;
    margin-top: 2px !important;
}

/* 注册来源样式 */
.registration-source {
    display: inline-flex !important;
    align-items: center !important;
    padding: 4px 8px !important;
    background: #f1f5f9 !important;
    color: #64748b !important;
    border-radius: 12px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    white-space: nowrap !important;
}

/* 操作按钮组样式 */
.action-buttons {
    display: flex !important;
    gap: 6px !important;
    align-items: center !important;
    justify-content: flex-start !important;
    flex-wrap: wrap !important;
}

.action-icon-btn {
    width: 32px !important;
    height: 32px !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    background: white !important;
    color: #64748b !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 12px !important;
    flex-shrink: 0 !important;
}

.action-icon-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.action-icon-btn.primary {
    background: #eff6ff !important;
    color: #2563eb !important;
    border-color: #bfdbfe !important;
}

.action-icon-btn.danger {
    background: #fef2f2 !important;
    color: #dc2626 !important;
    border-color: #fecaca !important;
}

.action-icon-btn.success {
    background: #f0fdf4 !important;
    color: #16a34a !important;
    border-color: #bbf7d0 !important;
}

.action-icon-btn.warning {
    background: #fef3c7 !important;
    color: #d97706 !important;
    border-color: #fde68a !important;
}

/* 数据统计样式 */
.data-stats {
    display: flex !important;
    flex-direction: column !important;
}

.main-stat {
    font-weight: 600 !important;
    color: #1e293b !important;
    font-size: 14px !important;
}

.sub-stat {
    font-size: 12px !important;
    color: #64748b !important;
    margin-top: 2px !important;
}

/* 表格宽度控制 */
.user-table th:first-child, 
.users-table th:first-child, 
.product-table th:first-child, 
.products-table th:first-child, 
.merchant-table th:first-child {
    width: 40px !important;
}

.user-table th:nth-child(2), 
.users-table th:nth-child(2), 
.product-table th:nth-child(2), 
.products-table th:nth-child(2), 
.merchant-table th:nth-child(2) {
    width: 280px !important;
}

.user-table th:last-child, 
.users-table th:last-child, 
.product-table th:last-child, 
.products-table th:last-child, 
.merchant-table th:last-child {
    width: 150px !important;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .user-table, .users-table, .product-table, .products-table, .merchant-table {
        font-size: 12px !important;
    }
    
    .user-info, .product-info, .merchant-info {
        min-width: 180px !important;
        max-width: 200px !important;
    }
    
    .user-avatar, .product-image, .merchant-logo {
        width: 32px !important;
        height: 32px !important;
    }
    
    .action-buttons {
        gap: 4px !important;
    }
    
    .action-icon-btn {
        width: 28px !important;
        height: 28px !important;
        font-size: 11px !important;
    }
} 