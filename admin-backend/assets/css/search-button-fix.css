/* 搜索和重置按钮专用修复样式 */

/* 搜索按钮样式修复 */
.search-btn, 
button[type="submit"],
.btn-search,
.filter-btn,
.action-btn:contains("搜索"),
.action-btn:contains("重置"),
button:contains("搜索"),
button:contains("重置") {
    color: #ffffff !important;
    background-color: #3b82f6 !important;
    border-color: #3b82f6 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    border: 1px solid #3b82f6 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    white-space: nowrap !important;
    text-decoration: none !important;
    min-width: 80px !important;
    height: auto !important;
    line-height: 1.4 !important;
}

.search-btn:hover,
button[type="submit"]:hover,
.btn-search:hover,
.filter-btn:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.25) !important;
}

/* 重置按钮样式修复 */
.reset-btn,
button[type="reset"],
.btn-reset,
.action-btn.secondary {
    color: #64748b !important;
    background-color: #ffffff !important;
    border-color: #e2e8f0 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    border: 1px solid #e2e8f0 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    white-space: nowrap !important;
    text-decoration: none !important;
    min-width: 80px !important;
    height: auto !important;
    line-height: 1.4 !important;
}

.reset-btn:hover,
button[type="reset"]:hover,
.btn-reset:hover,
.action-btn.secondary:hover {
    background-color: #f1f5f9 !important;
    border-color: #cbd5e1 !important;
    color: #475569 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(148, 163, 184, 0.25) !important;
}

/* 筛选区域按钮组样式 */
.filter-actions,
.search-actions,
.filter-row .action-btn,
.search-filters .action-btn {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.filter-actions button,
.search-actions button,
.filter-row button,
.search-filters button {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: inherit !important;
    background-color: inherit !important;
    border-color: inherit !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    white-space: nowrap !important;
    min-width: 80px !important;
    height: auto !important;
    line-height: 1.4 !important;
}

/* 主要按钮颜色修复 */
.filter-actions button.primary,
.search-actions button.primary,
.filter-row button.primary,
.search-filters button.primary,
.action-btn.primary {
    color: #ffffff !important;
    background-color: #3b82f6 !important;
    border-color: #3b82f6 !important;
}

.filter-actions button.primary:hover,
.search-actions button.primary:hover,
.filter-row button.primary:hover,
.search-filters button.primary:hover,
.action-btn.primary:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
}

/* 次要按钮颜色修复 */
.filter-actions button:not(.primary),
.search-actions button:not(.primary),
.filter-row button:not(.primary),
.search-filters button:not(.primary),
.action-btn:not(.primary):not(.danger) {
    color: #64748b !important;
    background-color: #ffffff !important;
    border-color: #e2e8f0 !important;
}

.filter-actions button:not(.primary):hover,
.search-actions button:not(.primary):hover,
.filter-row button:not(.primary):hover,
.search-filters button:not(.primary):hover,
.action-btn:not(.primary):not(.danger):hover {
    background-color: #f1f5f9 !important;
    border-color: #cbd5e1 !important;
    color: #475569 !important;
}

/* 图标样式修复 */
.filter-actions button i,
.search-actions button i,
.filter-row button i,
.search-filters button i,
.action-btn i {
    font-size: 12px !important;
    margin-right: 4px !important;
    color: inherit !important;
}

/* 按钮文本确保可见性 */
.filter-actions button span,
.search-actions button span,
.filter-row button span,
.search-filters button span,
.action-btn span {
    color: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    display: inline !important;
}

/* 特殊容器中的按钮修复 */
.page-actions button,
.header-actions button,
.toolbar-actions button,
.content-actions button {
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    white-space: nowrap !important;
    min-width: 80px !important;
    height: auto !important;
    line-height: 1.4 !important;
    color: inherit !important;
    background-color: inherit !important;
    border-color: inherit !important;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .filter-actions,
    .search-actions,
    .filter-row,
    .search-filters {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 8px !important;
    }
    
    .filter-actions button,
    .search-actions button,
    .filter-row button,
    .search-filters button,
    .action-btn {
        width: 100% !important;
        min-width: auto !important;
        justify-content: center !important;
    }
}

/* 确保所有可能的按钮选择器都被覆盖 */
input[type="submit"],
input[type="button"],
input[type="reset"] {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #64748b !important;
    background-color: #ffffff !important;
    border-color: #e2e8f0 !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    min-width: 80px !important;
    height: auto !important;
    line-height: 1.4 !important;
}

input[type="submit"]:hover,
input[type="button"]:hover,
input[type="reset"]:hover {
    background-color: #f1f5f9 !important;
    border-color: #cbd5e1 !important;
    color: #475569 !important;
} 