/* TeleShop Admin - 通用修复样式 */

/* 全局重置和基础样式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #1e293b;
    background-color: #f8fafc;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 图片通用修复 */
img {
    max-width: 100%;
    height: auto;
    display: block;
    border: 0;
    vertical-align: middle;
}

/* 图片加载错误处理 */
img[src=""],
img:not([src]),
img[src="#"] {
    display: none;
}

/* 头像图片修复 */
.user-avatar,
.customer-avatar,
.admin-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0284c7;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    flex-shrink: 0;
    overflow: hidden;
    position: relative;
}

.user-avatar img,
.customer-avatar img,
.admin-avatar img {
    width: 100%;
    height: 100%;
    border-radius: inherit;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
}

/* 商品图片修复 */
.product-image,
.product-thumb {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #d97706;
    font-size: 14px;
    flex-shrink: 0;
    overflow: hidden;
    position: relative;
}

.product-image img,
.product-thumb img {
    width: 100%;
    height: 100%;
    border-radius: inherit;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
}

/* 图片占位符 */
.user-avatar:empty::before,
.customer-avatar:empty::before {
    content: '👤';
    font-size: 14px;
    color: #64748b;
}

.product-image:empty::before,
.product-thumb:empty::before {
    content: '📦';
    font-size: 16px;
    color: #64748b;
}

/* 文字显示修复 */
.page-title,
.content-title,
.stat-title,
.table-title {
    word-break: keep-all;
    overflow-wrap: break-word;
    line-height: 1.4;
    font-weight: 600;
}

.page-title {
    font-size: 28px;
    color: #1e293b;
    margin: 0;
}

.content-title {
    font-size: 18px;
    color: #1e293b;
    margin: 0;
}

.stat-title {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

/* 数值显示修复 */
.stat-value,
.amount-value {
    font-variant-numeric: tabular-nums;
    letter-spacing: -0.5px;
    font-weight: 700;
}

.stat-value {
    font-size: 24px;
    color: #1e293b;
}

.amount-value {
    font-family: 'Inter', monospace;
    color: #1e293b;
}

/* 按钮修复 */
.action-btn,
.btn,
.btn-sm {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    user-select: none;
    line-height: 1;
    min-height: 36px;
    box-sizing: border-box;
}

.action-btn:hover,
.btn:hover,
.btn-sm:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    text-decoration: none;
    transform: translateY(-1px);
}

.action-btn.primary,
.btn.primary {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.action-btn.primary:hover,
.btn.primary:hover {
    background: #2563eb;
    border-color: #2563eb;
}

.action-btn.danger,
.btn.danger {
    background: #ef4444;
    color: white;
    border-color: #ef4444;
}

.action-btn.danger:hover,
.btn.danger:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.action-btn.warning,
.btn.warning {
    background: #f59e0b;
    color: white;
    border-color: #f59e0b;
}

.action-btn.warning:hover,
.btn.warning:hover {
    background: #d97706;
    border-color: #d97706;
}

/* 状态徽章修复 */
.status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    white-space: nowrap;
    min-width: 0;
}

.status-badge.status-success,
.status-delivered {
    background: #dcfce7;
    color: #166534;
}

.status-badge.status-warning,
.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.status-error,
.status-cancelled {
    background: #fee2e2;
    color: #991b1b;
}

.status-paid {
    background: #dbeafe;
    color: #1e40af;
}

.status-shipped {
    background: #e0e7ff;
    color: #3730a3;
}

.status-refunding {
    background: #fed7aa;
    color: #ea580c;
}

/* 表格修复 */
.data-table,
.orders-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    background: white;
}

.data-table th,
.data-table td,
.orders-table th,
.orders-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #f1f5f9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

.data-table th,
.orders-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 12px;
}

/* 表格容器 */
.table-container,
.table-card {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

/* 卡片样式修复 */
.stat-card,
.main-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 表单元素修复 */
.filter-input,
.filter-select,
input[type="text"],
input[type="email"],
input[type="password"],
select {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    color: #1e293b;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
    box-sizing: border-box;
}

.filter-input:focus,
.filter-select:focus,
input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 响应式修复 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .page-actions,
    .header-actions {
        flex-direction: row;
        justify-content: space-around;
        gap: 8px;
        flex-wrap: wrap;
    }
    
    .action-btn {
        flex: 1;
        min-width: 0;
        padding: 8px 4px;
        font-size: 12px;
    }
    
    .stats-grid,
    .stats-overview {
        grid-template-columns: 1fr 1fr;
        gap: 12px;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .data-table,
    .orders-table {
        min-width: 600px;
        font-size: 12px;
    }
    
    .data-table th,
    .data-table td,
    .orders-table th,
    .orders-table td {
        padding: 8px 4px;
    }
    
    .customer-info,
    .product-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .user-avatar,
    .customer-avatar,
    .product-image,
    .product-thumb {
        width: 24px;
        height: 24px;
    }
}

@media (max-width: 480px) {
    .page-actions,
    .header-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .action-btn {
        width: 100%;
        justify-content: center;
    }
    
    .stats-grid,
    .stats-overview {
        grid-template-columns: 1fr;
    }
    
    .page-title {
        font-size: 20px;
    }
    
    .content-title {
        font-size: 16px;
    }
}

/* 动画和过渡效果 */
.loading-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 提升性能 */
.stat-card,
.main-content,
.action-btn {
    will-change: transform;
}

/* 打印样式 */
@media print {
    .action-btn,
    .page-actions,
    .header-actions {
        display: none !important;
    }
    
    .main-content {
        box-shadow: none;
        border: 1px solid #ccc;
    }
} 