/* TeleShop Admin - 仪表盘页面修复样式 */

/* 按钮重叠问题修复 */
.dashboard-header {
    position: relative;
    z-index: 10;
}

.dashboard-actions {
    position: relative;
    z-index: 11;
}

.action-btn {
    position: relative;
    z-index: 12;
    flex-shrink: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 图片显示异常修复 */
.product-thumb,
.user-avatar {
    position: relative;
    flex-shrink: 0;
    overflow: hidden;
    background: #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-thumb {
    width: 40px;
    height: 40px;
    border-radius: 6px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.product-thumb img,
.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: inherit;
}

/* 图片加载失败时的占位符 */
.product-thumb:empty::before,
.user-avatar:empty::before {
    content: '📦';
    font-size: 16px;
    color: #64748b;
}

.user-avatar:empty::before {
    content: '👤';
    font-size: 12px;
}

/* 文字显示异常修复 */
.dashboard-title,
.dashboard-subtitle,
.stat-title,
.chart-title,
.table-title {
    word-break: keep-all;
    overflow-wrap: break-word;
    line-height: 1.5;
}

.stat-value {
    font-variant-numeric: tabular-nums;
    letter-spacing: -0.5px;
}

/* 状态徽章修复 */
.status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    white-space: nowrap;
    min-width: 0;
}

.status-badge.status-success {
    background: #dcfce7;
    color: #166534;
}

.status-badge.status-warning {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.status-error {
    background: #fee2e2;
    color: #991b1b;
}

/* 表格响应式修复 */
.table-card {
    overflow: hidden;
}

.data-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #f1f5f9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.data-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    position: sticky;
    top: 0;
    z-index: 10;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .dashboard-actions {
        flex-direction: row;
        justify-content: space-around;
        gap: 8px;
    }
    
    .action-btn {
        flex: 1;
        min-width: 0;
        padding: 8px 4px;
        font-size: 12px;
    }
    
    .action-btn span {
        display: none;
    }
    
    .action-btn i {
        margin: 0;
    }
    
    .table-card {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .data-table {
        min-width: 600px;
    }
}

@media (max-width: 480px) {
    .dashboard-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .action-btn {
        width: 100%;
        justify-content: center;
    }
    
    .action-btn span {
        display: inline;
    }
}

/* 加载状态修复 */
.loading-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 通知样式修复 */
.notification-toast {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
    max-width: 350px;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    font-weight: 500;
    color: white;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.notification-toast.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-toast.success {
    background: #10b981;
}

.notification-toast.error {
    background: #ef4444;
}

.notification-toast.warning {
    background: #f59e0b;
}

.notification-toast.info {
    background: #3b82f6;
} 