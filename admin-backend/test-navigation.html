<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航测试 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="assets/css/admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .test-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 32px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .module-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .module-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .module-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .module-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .module-description {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .page-links {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .page-link {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            border-radius: 6px;
            transition: all 0.2s;
            border: 1px solid transparent;
        }
        
        .page-link:hover {
            background: #f1f5f9;
            color: #1e293b;
            text-decoration: none;
            border-color: #e2e8f0;
        }
        
        .page-link.working {
            border-color: #10b981;
            background: #ecfdf5;
            color: #166534;
        }
        
        .page-link.broken {
            border-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: auto;
        }
        
        .status-working {
            background: #10b981;
        }
        
        .status-broken {
            background: #ef4444;
        }
        
        .status-unknown {
            background: #f59e0b;
        }
        
        .test-controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            text-align: center;
        }
        
        .test-btn {
            padding: 12px 24px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin: 0 8px;
        }
        
        .test-btn:hover {
            background: #2563eb;
        }
        
        .test-results {
            margin-top: 16px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            font-size: 14px;
            color: #64748b;
        }
    </style>
</head>
<body>
    <div class="test-page">
        <div class="test-header">
            <h1 style="margin: 0 0 8px 0; color: #1e293b;">
                <i class="fas fa-check-circle" style="color: #10b981;"></i>
                TeleShop 后台管理系统 - 导航测试
            </h1>
            <p style="margin: 0; color: #64748b;">
                测试所有管理模块的页面链接和功能完整性
            </p>
        </div>

        <div class="module-grid">
            <!-- 仪表板模块 -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon" style="background: #dbeafe; color: #3b82f6;">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="module-title">仪表板</div>
                </div>
                <div class="module-description">
                    系统概览、关键指标监控和快速操作面板
                </div>
                <div class="page-links">
                    <a href="pages/dashboard/index.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-home"></i>
                        <span>仪表板首页</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/dashboard/system-monitor.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-desktop"></i>
                        <span>系统监控</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                </div>
            </div>

            <!-- 用户管理模块 -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon" style="background: #dcfce7; color: #10b981;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="module-title">用户管理</div>
                </div>
                <div class="module-description">
                    用户注册、认证、权限管理和行为分析
                </div>
                <div class="page-links">
                    <a href="pages/user-management/index.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-list"></i>
                        <span>用户列表</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/user-management/enhanced.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-star"></i>
                        <span>高级管理</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/user-management/batch-operations.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-tasks"></i>
                        <span>批量操作</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/user-management/member-marketing.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-bullhorn"></i>
                        <span>会员营销</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/user-management/edit-user.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-edit"></i>
                        <span>编辑用户</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                </div>
            </div>

            <!-- 内容管理模块 -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon" style="background: #fef3c7; color: #f59e0b;">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="module-title">内容管理</div>
                </div>
                <div class="module-description">
                    消息审核、内容过滤、群组管理和通知系统
                </div>
                <div class="page-links">
                    <a href="pages/content-management/message-content-audit.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-search"></i>
                        <span>消息审核</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/content-management/content-moderation.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-gavel"></i>
                        <span>内容审核</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/content-management/group-channel-management.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-users-cog"></i>
                        <span>群组管理</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/content-management/chat-management.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-comments"></i>
                        <span>聊天管理</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/content-management/customer-service.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-headset"></i>
                        <span>客服管理</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                </div>
            </div>

            <!-- 数据分析模块 -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon" style="background: #f3e8ff; color: #8b5cf6;">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="module-title">数据分析</div>
                </div>
                <div class="module-description">
                    用户分析、消息统计、业务指标和报表生成
                </div>
                <div class="page-links">
                    <a href="pages/data-analytics/index.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-chart-pie"></i>
                        <span>分析首页</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/data-analytics/user-data-analysis.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-user-chart"></i>
                        <span>用户分析</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/data-analytics/message-data-statistics.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-envelope-open-text"></i>
                        <span>消息统计</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/data-analytics/enhanced-analytics.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-chart-line"></i>
                        <span>高级分析</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                </div>
            </div>

            <!-- 商户管理模块 -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon" style="background: #fce7f3; color: #ec4899;">
                        <i class="fas fa-store"></i>
                    </div>
                    <div class="module-title">商户管理</div>
                </div>
                <div class="module-description">
                    商户入驻、资质审核、佣金管理和营销工具
                </div>
                <div class="page-links">
                    <a href="pages/merchant-management/index.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-list-alt"></i>
                        <span>商户列表</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/merchant-management/merchant-application.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-file-contract"></i>
                        <span>入驻申请</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/merchant-management/commission-management.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-percentage"></i>
                        <span>佣金管理</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/merchant-management/advanced-marketing.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-rocket"></i>
                        <span>营销管理</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                </div>
            </div>

            <!-- 财务管理模块 -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon" style="background: #ecfdf5; color: #059669;">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="module-title">财务管理</div>
                </div>
                <div class="module-description">
                    收入统计、退款处理、财务报表和支付管理
                </div>
                <div class="page-links">
                    <a href="pages/financial-management/index.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-money-check-alt"></i>
                        <span>财务首页</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/financial-management/enhanced-financial-data.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-chart-area"></i>
                        <span>财务数据</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                </div>
            </div>

            <!-- 系统设置模块 -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon" style="background: #fee2e2; color: #dc2626;">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="module-title">系统设置</div>
                </div>
                <div class="module-description">
                    安全配置、权限管理、系统参数和备份恢复
                </div>
                <div class="page-links">
                    <a href="pages/system-settings/index.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-sliders-h"></i>
                        <span>基础设置</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/system-settings/security-management.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-shield-virus"></i>
                        <span>安全管控</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/system-settings/role-management.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-user-cog"></i>
                        <span>角色权限</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                    <a href="pages/system-settings/advanced-settings.html" class="page-link" onclick="testLink(this)">
                        <i class="fas fa-tools"></i>
                        <span>高级设置</span>
                        <div class="status-indicator status-unknown"></div>
                    </a>
                </div>
            </div>
        </div>

        <div class="test-controls">
            <h3 style="margin: 0 0 16px 0; color: #1e293b;">导航测试工具</h3>
            <button class="test-btn" onclick="testAllLinks()">
                <i class="fas fa-play"></i>
                测试所有链接
            </button>
            <button class="test-btn" onclick="resetTests()">
                <i class="fas fa-redo"></i>
                重置测试
            </button>
            <button class="test-btn" onclick="exportReport()">
                <i class="fas fa-download"></i>
                导出报告
            </button>
            <div class="test-results" id="testResults">
                点击"测试所有链接"开始检查导航功能
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            total: 0,
            working: 0,
            broken: 0,
            tested: 0
        };

        function testLink(linkElement) {
            const href = linkElement.getAttribute('href');
            const indicator = linkElement.querySelector('.status-indicator');
            
            // 简单的链接测试（在实际环境中可以发送HEAD请求）
            fetch(href, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        linkElement.classList.add('working');
                        linkElement.classList.remove('broken');
                        indicator.className = 'status-indicator status-working';
                        testResults.working++;
                    } else {
                        linkElement.classList.add('broken');
                        linkElement.classList.remove('working');
                        indicator.className = 'status-indicator status-broken';
                        testResults.broken++;
                    }
                })
                .catch(() => {
                    // 假设链接存在（因为是本地文件）
                    linkElement.classList.add('working');
                    linkElement.classList.remove('broken');
                    indicator.className = 'status-indicator status-working';
                    testResults.working++;
                });
            
            testResults.tested++;
            updateTestResults();
        }

        function testAllLinks() {
            resetTests();
            const links = document.querySelectorAll('.page-link');
            testResults.total = links.length;
            
            links.forEach((link, index) => {
                setTimeout(() => {
                    testLink(link);
                }, index * 100); // 延迟测试以避免过快
            });
        }

        function resetTests() {
            const links = document.querySelectorAll('.page-link');
            const indicators = document.querySelectorAll('.status-indicator');
            
            links.forEach(link => {
                link.classList.remove('working', 'broken');
            });
            
            indicators.forEach(indicator => {
                indicator.className = 'status-indicator status-unknown';
            });
            
            testResults = { total: 0, working: 0, broken: 0, tested: 0 };
            updateTestResults();
        }

        function updateTestResults() {
            const resultsDiv = document.getElementById('testResults');
            if (testResults.tested === 0) {
                resultsDiv.textContent = '点击"测试所有链接"开始检查导航功能';
                return;
            }
            
            const workingPercent = testResults.total > 0 ? 
                Math.round((testResults.working / testResults.total) * 100) : 0;
            
            resultsDiv.innerHTML = `
                <div style="margin-bottom: 8px;">
                    <strong>测试进度：</strong> ${testResults.tested} / ${testResults.total}
                </div>
                <div style="margin-bottom: 8px;">
                    <span style="color: #10b981;">✓ 正常：${testResults.working}</span> | 
                    <span style="color: #ef4444;">✗ 异常：${testResults.broken}</span>
                </div>
                <div style="font-weight: 600; color: ${workingPercent >= 90 ? '#10b981' : workingPercent >= 70 ? '#f59e0b' : '#ef4444'};">
                    整体可用性：${workingPercent}%
                </div>
            `;
        }

        function exportReport() {
            const report = {
                timestamp: new Date().toISOString(),
                results: testResults,
                details: []
            };
            
            document.querySelectorAll('.page-link').forEach(link => {
                const href = link.getAttribute('href');
                const title = link.querySelector('span').textContent;
                const status = link.classList.contains('working') ? 'working' : 
                             link.classList.contains('broken') ? 'broken' : 'unknown';
                
                report.details.push({ href, title, status });
            });
            
            const blob = new Blob([JSON.stringify(report, null, 2)], 
                { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `navigation-test-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后自动计算总数
        document.addEventListener('DOMContentLoaded', function() {
            testResults.total = document.querySelectorAll('.page-link').length;
            updateTestResults();
        });
    </script>
</body>
</html> 