<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单功能测试 - TeleShop Admin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: #3b82f6;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-content {
            display: flex;
            min-height: 600px;
        }
        
        .test-sidebar {
            width: 280px;
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 20px;
        }
        
        .test-main {
            flex: 1;
            padding: 20px;
        }
        
        .menu-item {
            margin-bottom: 8px;
        }
        
        .menu-item a {
            display: block;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            text-decoration: none;
            color: #374151;
            transition: all 0.2s;
        }
        
        .menu-item a:hover {
            background: #eff6ff;
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .menu-item.active a {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .test-output {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 200px;
        }
        
        .test-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.5;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 4px;
        }
        
        .log-success { color: #10b981; }
        .log-error { color: #ef4444; }
        .log-warning { color: #f59e0b; }
        .log-info { color: #6b7280; }
        
        .test-buttons {
            margin-bottom: 20px;
        }
        
        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        
        .test-btn:hover {
            background: #2563eb;
        }
        
        .test-btn.secondary {
            background: #6b7280;
        }
        
        .test-btn.secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>TeleShop 后台管理系统 - 菜单功能测试</h1>
            <p>此页面用于测试左侧菜单的点击功能是否正常工作</p>
        </div>
        
        <div class="test-content">
            <div class="test-sidebar">
                <h3>测试菜单</h3>
                <div class="menu-item active">
                    <a href="#" onclick="testLoadPage('dashboard')" data-page="dashboard">
                        <i class="fas fa-chart-pie"></i> 仪表盘
                    </a>
                </div>
                <div class="menu-item">
                    <a href="#" onclick="testLoadPage('user-management')" data-page="user-management">
                        <i class="fas fa-users"></i> 用户管理
                    </a>
                </div>
                <div class="menu-item">
                    <a href="#" onclick="testLoadPage('product-management')" data-page="product-management">
                        <i class="fas fa-box"></i> 商品管理
                    </a>
                </div>
                <div class="menu-item">
                    <a href="#" onclick="testLoadPage('order-management')" data-page="order-management">
                        <i class="fas fa-shopping-cart"></i> 订单管理
                    </a>
                </div>
                <div class="menu-item">
                    <a href="#" onclick="testLoadPage('reports-analytics')" data-page="reports-analytics">
                        <i class="fas fa-chart-bar"></i> 数据分析
                    </a>
                </div>
                <div class="menu-item">
                    <a href="#" onclick="testLoadPage('system-settings')" data-page="system-settings">
                        <i class="fas fa-cog"></i> 系统设置
                    </a>
                </div>
            </div>
            
            <div class="test-main">
                <div class="test-buttons">
                    <button class="test-btn" onclick="clearLog()">清除日志</button>
                    <button class="test-btn secondary" onclick="testMainPageFunction()">测试主页面功能</button>
                    <button class="test-btn secondary" onclick="checkAdminApp()">检查AdminApp状态</button>
                </div>
                
                <div class="test-output">
                    <h3>当前显示页面</h3>
                    <div id="currentPageDisplay">
                        <h4>仪表盘</h4>
                        <p>这是仪表盘页面的内容</p>
                    </div>
                </div>
                
                <div class="test-log" id="testLog">
                    <div class="log-entry log-info">测试页面已加载</div>
                    <div class="log-entry log-info">等待用户操作...</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 测试用的页面内容
        const pageContents = {
            'dashboard': {
                title: '仪表盘',
                content: '这是仪表盘页面的内容，显示系统总览数据'
            },
            'user-management': {
                title: '用户管理',
                content: '这是用户管理页面，可以查看和管理所有用户'
            },
            'product-management': {
                title: '商品管理',
                content: '这是商品管理页面，可以管理商品信息和库存'
            },
            'order-management': {
                title: '订单管理',
                content: '这是订单管理页面，可以查看和处理订单'
            },
            'reports-analytics': {
                title: '数据分析',
                content: '这是数据分析页面，显示各种图表和报表'
            },
            'system-settings': {
                title: '系统设置',
                content: '这是系统设置页面，可以配置系统参数'
            }
        };
        
        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // 清除日志
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('日志已清除');
        }
        
        // 测试页面加载功能
        function testLoadPage(pageName) {
            log(`点击菜单项: ${pageName}`, 'info');
            
            try {
                // 更新菜单状态
                const menuItems = document.querySelectorAll('.menu-item');
                menuItems.forEach(item => item.classList.remove('active'));
                
                const clickedItem = document.querySelector(`[data-page="${pageName}"]`).closest('.menu-item');
                if (clickedItem) {
                    clickedItem.classList.add('active');
                    log(`菜单状态更新成功: ${pageName}`, 'success');
                }
                
                // 更新页面内容
                const pageData = pageContents[pageName];
                if (pageData) {
                    const displayElement = document.getElementById('currentPageDisplay');
                    displayElement.innerHTML = `
                        <h4>${pageData.title}</h4>
                        <p>${pageData.content}</p>
                        <p><strong>页面ID:</strong> ${pageName}</p>
                        <p><strong>加载时间:</strong> ${new Date().toLocaleString()}</p>
                    `;
                    log(`页面内容更新成功: ${pageData.title}`, 'success');
                } else {
                    log(`未找到页面内容: ${pageName}`, 'warning');
                }
                
                // 测试主页面的loadPage函数
                if (typeof loadPage === 'function') {
                    log('调用主页面的loadPage函数', 'info');
                    loadPage(pageName);
                    log('主页面loadPage函数调用成功', 'success');
                } else {
                    log('主页面loadPage函数不可用', 'warning');
                }
                
            } catch (error) {
                log(`页面加载出错: ${error.message}`, 'error');
            }
        }
        
        // 检查AdminApp状态
        function checkAdminApp() {
            log('检查AdminApp状态...', 'info');
            
            if (typeof AdminApp !== 'undefined') {
                log('AdminApp类已定义', 'success');
            } else {
                log('AdminApp类未定义', 'error');
            }
            
            if (window.adminApp) {
                log('window.adminApp实例存在', 'success');
                log(`当前页面: ${window.adminApp.currentPage || '未知'}`, 'info');
                
                if (typeof window.adminApp.loadPage === 'function') {
                    log('adminApp.loadPage方法可用', 'success');
                } else {
                    log('adminApp.loadPage方法不可用', 'error');
                }
            } else {
                log('window.adminApp实例不存在', 'error');
            }
            
            // 检查全局函数
            if (typeof loadPage === 'function') {
                log('全局loadPage函数可用', 'success');
            } else {
                log('全局loadPage函数不可用', 'error');
            }
        }
        
        // 测试主页面功能
        function testMainPageFunction() {
            log('测试与主页面的交互...', 'info');
            
            // 尝试调用主页面的函数
            try {
                if (parent && parent.window && parent.window.adminApp) {
                    log('检测到父页面的adminApp', 'success');
                    parent.window.adminApp.loadPage('dashboard');
                    log('成功调用父页面的loadPage', 'success');
                } else {
                    log('未检测到父页面的adminApp', 'warning');
                }
            } catch (error) {
                log(`调用父页面功能失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面DOM加载完成', 'success');
            log('开始加载主页面的JavaScript文件...', 'info');
            
            // 尝试加载主页面的JS文件
            const script = document.createElement('script');
            script.src = 'assets/js/admin-main.js';
            script.onload = function() {
                log('主页面JavaScript文件加载成功', 'success');
                setTimeout(checkAdminApp, 100);
            };
            script.onerror = function() {
                log('主页面JavaScript文件加载失败', 'error');
            };
            document.head.appendChild(script);
        });
        
        // 添加Font Awesome图标支持
        const fontAwesome = document.createElement('link');
        fontAwesome.rel = 'stylesheet';
        fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
        document.head.appendChild(fontAwesome);
    </script>
</body>
</html> 