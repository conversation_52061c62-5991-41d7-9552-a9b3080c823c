<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TeleShop Admin - 菜单功能调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .debug-header {
            background: #ef4444;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .debug-content {
            padding: 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-error {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #d97706;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .log-section {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .log-item {
            font-family: monospace;
            font-size: 12px;
            color: #374151;
            margin-bottom: 5px;
            padding: 5px;
            background: white;
            border-radius: 4px;
        }
        
        .log-error {
            color: #dc2626;
            background: #fef2f2;
        }
        
        .log-success {
            color: #166534;
            background: #dcfce7;
        }
        
        .log-warning {
            color: #d97706;
            background: #fef3c7;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-header">
            <h1>🔧 TeleShop Admin 菜单功能调试</h1>
            <p>检查左侧菜单点击功能是否正常工作</p>
        </div>
        
        <div class="debug-content">
            <!-- 功能检测 -->
            <div class="test-section">
                <h3>📋 功能检测</h3>
                <div class="test-item">
                    <span>AdminApp类加载</span>
                    <span id="adminAppStatus" class="status-badge status-warning">检测中...</span>
                </div>
                <div class="test-item">
                    <span>window.adminApp实例</span>
                    <span id="adminAppInstanceStatus" class="status-badge status-warning">检测中...</span>
                </div>
                <div class="test-item">
                    <span>loadPage方法</span>
                    <span id="loadPageStatus" class="status-badge status-warning">检测中...</span>
                </div>
                <div class="test-item">
                    <span>全局loadPage函数</span>
                    <span id="globalLoadPageStatus" class="status-badge status-warning">检测中...</span>
                </div>
            </div>
            
            <!-- DOM检测 -->
            <div class="test-section">
                <h3>🌐 DOM元素检测</h3>
                <div class="test-item">
                    <span>菜单项 (.menu-item)</span>
                    <span id="menuItemsStatus" class="status-badge status-warning">检测中...</span>
                </div>
                <div class="test-item">
                    <span>内容页面 (.content-page)</span>
                    <span id="contentPagesStatus" class="status-badge status-warning">检测中...</span>
                </div>
                <div class="test-item">
                    <span>菜单点击事件</span>
                    <span id="menuClickStatus" class="status-badge status-warning">检测中...</span>
                </div>
            </div>
            
            <!-- 手动测试 -->
            <div class="test-section">
                <h3>🧪 手动测试</h3>
                <div class="test-item">
                    <span>测试加载仪表盘</span>
                    <button class="test-button" onclick="testLoadPage('dashboard')">测试</button>
                </div>
                <div class="test-item">
                    <span>测试加载用户管理</span>
                    <button class="test-button" onclick="testLoadPage('user-management')">测试</button>
                </div>
                <div class="test-item">
                    <span>测试加载数据分析</span>
                    <button class="test-button" onclick="testLoadPage('reports-analytics')">测试</button>
                </div>
                <div class="test-item">
                    <span>测试加载系统设置</span>
                    <button class="test-button" onclick="testLoadPage('system-settings')">测试</button>
                </div>
            </div>
            
            <!-- 日志区域 -->
            <div class="log-section">
                <h4>📝 调试日志</h4>
                <div id="debugLog">
                    <div class="log-item">初始化调试工具...</div>
                </div>
            </div>
            
            <!-- 直接链接测试 -->
            <div class="test-section">
                <h3>🔗 直接链接测试</h3>
                <p>如果以上测试失败，可以直接访问以下链接:</p>
                <ul>
                    <li><a href="index.html" target="_blank">主页面 (index.html)</a></li>
                    <li><a href="pages/dashboard/index.html" target="_blank">仪表盘</a></li>
                    <li><a href="pages/user-management/index.html" target="_blank">用户管理</a></li>
                    <li><a href="pages/reports-analytics/advanced-reports.html" target="_blank">高级报表</a></li>
                    <li><a href="pages/system-settings/role-management.html" target="_blank">角色管理</a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // 调试工具
        class DebugTool {
            constructor() {
                this.logContainer = document.getElementById('debugLog');
                this.init();
            }
            
            init() {
                this.log('开始检测...', 'success');
                
                // 延时检测，等待主页面加载
                setTimeout(() => {
                    this.checkAdminApp();
                    this.checkDOM();
                    this.checkMenuEvents();
                }, 500);
            }
            
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logItem = document.createElement('div');
                logItem.className = `log-item log-${type}`;
                logItem.textContent = `[${timestamp}] ${message}`;
                this.logContainer.appendChild(logItem);
                this.logContainer.scrollTop = this.logContainer.scrollHeight;
            }
            
            updateStatus(elementId, status, message) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = message;
                    element.className = `status-badge status-${status}`;
                }
            }
            
            checkAdminApp() {
                this.log('检测AdminApp...', 'info');
                
                // 检测AdminApp类
                if (typeof AdminApp !== 'undefined') {
                    this.updateStatus('adminAppStatus', 'success', '✅ 已加载');
                    this.log('AdminApp类已加载', 'success');
                } else {
                    this.updateStatus('adminAppStatus', 'error', '❌ 未找到');
                    this.log('AdminApp类未找到', 'error');
                }
                
                // 检测window.adminApp实例
                if (window.adminApp) {
                    this.updateStatus('adminAppInstanceStatus', 'success', '✅ 已创建');
                    this.log('window.adminApp实例已创建', 'success');
                } else {
                    this.updateStatus('adminAppInstanceStatus', 'error', '❌ 未创建');
                    this.log('window.adminApp实例未创建', 'error');
                }
                
                // 检测loadPage方法
                if (window.adminApp && typeof window.adminApp.loadPage === 'function') {
                    this.updateStatus('loadPageStatus', 'success', '✅ 可用');
                    this.log('loadPage方法可用', 'success');
                } else {
                    this.updateStatus('loadPageStatus', 'error', '❌ 不可用');
                    this.log('loadPage方法不可用', 'error');
                }
                
                // 检测全局loadPage函数
                if (typeof loadPage === 'function') {
                    this.updateStatus('globalLoadPageStatus', 'success', '✅ 可用');
                    this.log('全局loadPage函数可用', 'success');
                } else {
                    this.updateStatus('globalLoadPageStatus', 'error', '❌ 不可用');
                    this.log('全局loadPage函数不可用', 'error');
                }
            }
            
            checkDOM() {
                this.log('检测DOM元素...', 'info');
                
                // 检测菜单项
                const menuItems = document.querySelectorAll('.menu-item');
                if (menuItems.length > 0) {
                    this.updateStatus('menuItemsStatus', 'success', `✅ 找到 ${menuItems.length} 个`);
                    this.log(`找到 ${menuItems.length} 个菜单项`, 'success');
                } else {
                    this.updateStatus('menuItemsStatus', 'error', '❌ 未找到');
                    this.log('未找到菜单项', 'error');
                }
                
                // 检测内容页面
                const contentPages = document.querySelectorAll('.content-page');
                if (contentPages.length > 0) {
                    this.updateStatus('contentPagesStatus', 'success', `✅ 找到 ${contentPages.length} 个`);
                    this.log(`找到 ${contentPages.length} 个内容页面`, 'success');
                } else {
                    this.updateStatus('contentPagesStatus', 'error', '❌ 未找到');
                    this.log('未找到内容页面', 'error');
                }
            }
            
            checkMenuEvents() {
                this.log('检测菜单点击事件...', 'info');
                
                const menuLinks = document.querySelectorAll('.menu-item a[onclick]');
                if (menuLinks.length > 0) {
                    this.updateStatus('menuClickStatus', 'success', `✅ ${menuLinks.length} 个有效`);
                    this.log(`找到 ${menuLinks.length} 个有onclick事件的菜单链接`, 'success');
                } else {
                    this.updateStatus('menuClickStatus', 'error', '❌ 未找到');
                    this.log('未找到有onclick事件的菜单链接', 'error');
                }
            }
        }
        
        // 测试加载页面
        function testLoadPage(pageName) {
            debugTool.log(`尝试加载页面: ${pageName}`, 'info');
            
            try {
                if (typeof loadPage === 'function') {
                    loadPage(pageName);
                    debugTool.log(`成功调用loadPage('${pageName}')`, 'success');
                } else if (window.adminApp && typeof window.adminApp.loadPage === 'function') {
                    window.adminApp.loadPage(pageName);
                    debugTool.log(`成功调用window.adminApp.loadPage('${pageName}')`, 'success');
                } else {
                    debugTool.log('没有可用的loadPage方法', 'error');
                }
            } catch (error) {
                debugTool.log(`加载页面时出错: ${error.message}`, 'error');
            }
        }
        
        // 初始化调试工具
        let debugTool;
        
        // 确保在DOM加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                debugTool = new DebugTool();
            });
        } else {
            debugTool = new DebugTool();
        }
        
        // 尝试加载主页面的脚本
        const script = document.createElement('script');
        script.src = 'assets/js/admin-main.js';
        script.onload = () => {
            debugTool.log('admin-main.js 加载完成', 'success');
            // 重新检测
            setTimeout(() => {
                debugTool.checkAdminApp();
            }, 100);
        };
        script.onerror = () => {
            debugTool.log('admin-main.js 加载失败', 'error');
        };
        document.head.appendChild(script);
    </script>
</body>
</html> 