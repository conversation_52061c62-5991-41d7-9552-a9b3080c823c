<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析页面 - 子菜单功能测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin-bottom: 24px;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            font-size: 16px;
            color: #64748b;
        }
        
        /* 子菜单导航样式 */
        .sub-navigation {
            background: #ffffff;
            border-radius: 12px;
            padding: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin-bottom: 24px;
        }
        
        .nav-tabs {
            display: flex;
            gap: 4px;
        }
        
        .nav-tab {
            flex: 1;
            padding: 12px 16px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            transition: all 0.2s ease;
        }
        
        .nav-tab:hover {
            background: #f1f5f9;
            color: #1e293b;
        }
        
        .nav-tab.active {
            background: #3b82f6;
            color: white;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }
        
        /* 内容区域样式 */
        .analysis-content {
            display: none;
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin-bottom: 24px;
            min-height: 400px;
        }
        
        .analysis-content.active {
            display: block;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .content-header h2 {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 12px;
        }
        
        .content-header p {
            color: #64748b;
            font-size: 18px;
            margin-bottom: 24px;
        }
        
        .test-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .test-success {
            background: #f0fdf4;
            border: 1px solid #22c55e;
            color: #15803d;
        }
        
        .test-error {
            background: #fef2f2;
            border: 1px solid #ef4444;
            color: #dc2626;
        }
        
        .debug-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #1e293b;
            color: white;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-width: 350px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .debug-header {
            font-weight: bold;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #475569;
        }
        
        .debug-log div {
            margin-bottom: 4px;
            padding: 2px 0;
        }
        
        .test-buttons {
            background: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin-bottom: 24px;
        }
        
        .test-buttons button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .test-buttons button:hover {
            background: #2563eb;
        }
        
        .test-buttons button.danger {
            background: #ef4444;
        }
        
        .test-buttons button.danger:hover {
            background: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">数据分析 - 子菜单功能测试</h1>
            <p class="page-subtitle">测试页面专门用于验证子菜单切换功能是否正常工作</p>
        </div>
        
        <div class="test-buttons">
            <button onclick="testAllTabs()">🧪 自动测试所有子菜单</button>
            <button onclick="clearDebugLog()">🗑️ 清除调试日志</button>
            <button onclick="toggleDebugPanel()" class="danger">🔧 切换调试面板</button>
        </div>
        
        <!-- 子菜单导航 -->
        <div class="sub-navigation">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="switchSection('overview', this)">
                    <i class="fas fa-chart-line"></i>
                    概览
                </button>
                <button class="nav-tab" onclick="switchSection('sales', this)">
                    <i class="fas fa-chart-bar"></i>
                    销售分析
                </button>
                <button class="nav-tab" onclick="switchSection('users', this)">
                    <i class="fas fa-users"></i>
                    用户分析
                </button>
                <button class="nav-tab" onclick="switchSection('products', this)">
                    <i class="fas fa-box"></i>
                    商品分析
                </button>
                <button class="nav-tab" onclick="switchSection('finance', this)">
                    <i class="fas fa-dollar-sign"></i>
                    财务报告
                </button>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div id="overview-content" class="analysis-content active">
            <div class="content-header">
                <h2>概览</h2>
                <p>数据分析概览页面，显示关键业务指标</p>
            </div>
            <div class="test-info test-success">
                ✅ 概览页面内容正常显示
            </div>
            <p>这里会显示业务概览的关键指标，包括收入、用户、订单等数据。</p>
        </div>
        
        <div id="sales-content" class="analysis-content">
            <div class="content-header">
                <h2>销售分析</h2>
                <p>深入分析销售数据，掌握业务增长趋势</p>
            </div>
            <div class="test-info test-success">
                ✅ 销售分析页面内容正常显示
            </div>
            <p>这里会显示销售数据分析，包括销售趋势、热门产品、销售渠道等信息。</p>
        </div>
        
        <div id="users-content" class="analysis-content">
            <div class="content-header">
                <h2>用户分析</h2>
                <p>了解用户行为模式，优化用户体验</p>
            </div>
            <div class="test-info test-success">
                ✅ 用户分析页面内容正常显示
            </div>
            <p>这里会显示用户行为分析，包括用户活跃度、用户留存、用户画像等数据。</p>
        </div>
        
        <div id="products-content" class="analysis-content">
            <div class="content-header">
                <h2>商品分析</h2>
                <p>分析商品表现，优化商品策略</p>
            </div>
            <div class="test-info test-success">
                ✅ 商品分析页面内容正常显示
            </div>
            <p>这里会显示商品数据分析，包括商品销量、库存情况、用户评价等信息。</p>
        </div>
        
        <div id="finance-content" class="analysis-content">
            <div class="content-header">
                <h2>财务报告</h2>
                <p>全面的财务数据分析和报告</p>
            </div>
            <div class="test-info test-success">
                ✅ 财务报告页面内容正常显示
            </div>
            <p>这里会显示财务数据分析，包括收入趋势、成本分析、利润报表等信息。</p>
        </div>
        
        <!-- 调试面板 -->
        <div id="debugPanel" class="debug-panel">
            <div class="debug-header">调试信息</div>
            <div id="debugLog" class="debug-log">
                <div>等待页面加载...</div>
            </div>
        </div>
    </div>
    
    <script>
        let debugLogElement;
        let debugPanel;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            debugLogElement = document.getElementById('debugLog');
            debugPanel = document.getElementById('debugPanel');
            
            debugLog('✅ 页面加载完成');
            debugLog(`📊 找到 ${document.querySelectorAll('.nav-tab').length} 个导航标签`);
            debugLog(`📄 找到 ${document.querySelectorAll('.analysis-content').length} 个内容区域`);
            
            // 验证初始状态
            const activeTab = document.querySelector('.nav-tab.active');
            const activeContent = document.querySelector('.analysis-content.active');
            
            if (activeTab && activeContent) {
                debugLog('✅ 初始状态正确：概览页面已激活');
            } else {
                debugLog('❌ 初始状态错误：找不到激活的标签或内容');
            }
        });
        
        // 调试日志函数
        function debugLog(message) {
            if (debugLogElement) {
                const time = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `[${time}] ${message}`;
                debugLogElement.appendChild(logEntry);
                debugLogElement.scrollTop = debugLogElement.scrollHeight;
            }
            console.log(message);
        }
        
        // 子菜单切换功能
        function switchSection(sectionId, tabElement) {
            debugLog(`🔄 开始切换到: ${sectionId}`);
            
            try {
                // 隐藏所有内容区域
                const allContent = document.querySelectorAll('.analysis-content');
                debugLog(`📄 找到 ${allContent.length} 个内容区域`);
                allContent.forEach(content => {
                    content.classList.remove('active');
                });
                
                // 移除所有标签的active状态
                const allTabs = document.querySelectorAll('.nav-tab');
                debugLog(`🏷️ 找到 ${allTabs.length} 个导航标签`);
                allTabs.forEach(tab => {
                    tab.classList.remove('active');
                });
                
                // 显示对应的内容区域
                const targetContent = document.getElementById(sectionId + '-content');
                
                if (targetContent) {
                    targetContent.classList.add('active');
                    debugLog(`✅ 成功激活内容区域: ${sectionId}`);
                } else {
                    debugLog(`❌ 找不到内容区域: ${sectionId}-content`);
                    return;
                }
                
                // 激活点击的标签
                if (tabElement) {
                    tabElement.classList.add('active');
                    debugLog(`✅ 成功激活标签按钮`);
                } else {
                    debugLog(`⚠️ 警告: 标签按钮为空`);
                }
                
                debugLog(`🎉 切换完成: ${sectionId}`);
                
            } catch (error) {
                debugLog(`❌ 切换错误: ${error.message}`);
                console.error('switchSection 错误:', error);
            }
        }
        
        // 测试所有子菜单
        function testAllTabs() {
            const sections = ['overview', 'sales', 'users', 'products', 'finance'];
            let index = 0;
            
            debugLog('🧪 开始自动测试所有子菜单');
            
            function testNext() {
                if (index < sections.length) {
                    const sectionId = sections[index];
                    const tabElement = document.querySelector(`[onclick*="${sectionId}"]`);
                    
                    debugLog(`🎯 测试第 ${index + 1} 个子菜单: ${sectionId}`);
                    
                    if (tabElement) {
                        switchSection(sectionId, tabElement);
                        index++;
                        setTimeout(testNext, 2000);
                    } else {
                        debugLog(`❌ 找不到子菜单按钮: ${sectionId}`);
                        index++;
                        setTimeout(testNext, 1000);
                    }
                } else {
                    debugLog('🏁 所有子菜单测试完成');
                }
            }
            
            testNext();
        }
        
        // 清除调试日志
        function clearDebugLog() {
            if (debugLogElement) {
                debugLogElement.innerHTML = '<div>调试日志已清除</div>';
                debugLog('🧹 调试日志已清除');
            }
        }
        
        // 切换调试面板显示/隐藏
        function toggleDebugPanel() {
            if (debugPanel) {
                if (debugPanel.style.display === 'none') {
                    debugPanel.style.display = 'block';
                    debugLog('👁️ 调试面板已显示');
                } else {
                    debugPanel.style.display = 'none';
                }
            }
        }
    </script>
</body>
</html> 