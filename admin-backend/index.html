<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>TeleShop 后台管理系统</title>
    <!-- 核心样式优先加载 -->
    <link rel="stylesheet" href="assets/css/admin-styles.css">
    <link rel="stylesheet" href="assets/css/button-text-fix.css">
    <!-- 延迟加载其他样式 -->
    <link rel="preload" href="assets/css/mobile-enhancements.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="assets/css/image-fixes.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <!-- 字体异步加载 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- 图标字体延迟加载 -->
    <style>
        /* 修复页面显示问题的样式 */
        * {
            box-sizing: border-box;
        }
        
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow-x: hidden;
        }
        
        /* 内容页面基础样式 */
        .content-page {
            display: none;
            width: 100%;
            height: calc(100vh - 80px);
            position: relative;
            overflow: auto;
        }
        
        .content-page.active {
            display: block;
        }
        
        /* iframe 样式优化 */
        .content-page iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: #fff;
        }
        
        /* 加载状态样式 */
        .content-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #64748b;
            text-align: center;
            padding: 40px;
            background: #fff;
        }
        
        .content-loading i {
            font-size: 64px;
            margin-bottom: 24px;
            opacity: 0.5;
            color: #3b82f6;
        }
        
        .content-loading h3 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #1e293b;
        }
        
        .content-loading p {
            font-size: 16px;
            margin: 0;
        }
        
        /* 主布局修复 */
        .admin-dashboard {
            display: flex !important;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            background: #f8fafc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* 头部导航修复 */
        .admin-header {
            height: 80px;
            background: white;
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            position: relative;
            z-index: 100;
            flex-shrink: 0;
        }
        
        /* 主体布局 */
        .admin-body {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        /* 侧边栏修复 */
        .admin-sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid #e2e8f0;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
        }
        
        .sidebar-menu {
            flex: 1;
            overflow-y: auto;
            padding: 24px 0;
        }
        
        /* 菜单项样式修复 */
        .menu-section {
            margin-bottom: 32px;
        }
        
        .menu-section h3 {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 0 24px 16px 24px;
        }
        
        .menu-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .menu-item {
            margin-bottom: 4px;
        }
        
        .menu-item a {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            border-right: 3px solid transparent;
            cursor: pointer;
        }
        
        .menu-item a:hover {
            background: #f8fafc;
            color: #1e293b;
        }
        
        .menu-item.active a {
            background: #eff6ff;
            color: #3b82f6;
            border-right-color: #3b82f6;
        }
        
        .menu-item a i {
            width: 20px;
            margin-right: 12px;
            font-size: 16px;
        }
        
        .menu-count {
            margin-left: auto;
            background: #f1f5f9;
            color: #64748b;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .menu-item.active .menu-count {
            background: #dbeafe;
            color: #3b82f6;
        }
        
        /* 二级菜单样式 */
        .menu-item.has-submenu > a {
            position: relative;
        }
        
        .menu-arrow {
            margin-left: auto;
            font-size: 12px;
            transition: transform 0.3s ease;
        }
        
        .menu-item.has-submenu.open .menu-arrow {
            transform: rotate(180deg);
        }
        
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8fafc;
            border-radius: 8px;
            margin: 8px 0 0 0;
            padding: 0;
            list-style: none;
        }
        
        .menu-item.has-submenu.open .submenu {
            max-height: 300px;
            padding: 8px 0;
        }
        
        .submenu li {
            margin: 0;
        }
        
        .submenu a {
            display: block;
            padding: 8px 16px 8px 48px;
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s;
            border-radius: 6px;
            margin: 2px 8px;
            border-right: none;
        }
        
        .submenu a:hover {
            background: #e2e8f0;
            color: #1e293b;
        }
        
        .submenu a.active {
            background: #3b82f6;
            color: white;
        }
        
        /* 主内容区域修复 */
        .admin-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background: #f8fafc;
            position: relative;
        }
        
        .main-content {
            flex: 1;
            overflow: auto;
            position: relative;
            padding: 24px;
        }
        
        /* 侧边栏底部 */
        .sidebar-footer {
            padding: 20px 24px;
            border-top: 1px solid #e2e8f0;
            background: white;
        }
        
        .system-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #64748b;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-dot.online {
            background: #10b981;
        }
        
        .system-version {
            font-size: 12px;
            color: #94a3b8;
        }
        
        /* 通知和消息面板样式 */
        .notification-panel,
        .message-panel {
            position: fixed;
            top: 80px;
            right: 24px;
            width: 380px;
            max-height: 500px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            transform: translateY(-10px);
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
        }
        
        .notification-panel.show,
        .message-panel.show {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }
        
        .panel-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .panel-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .panel-header button {
            background: none;
            border: none;
            color: #64748b;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
        }
        
        .panel-header button:hover {
            background: #f1f5f9;
        }
        
        .panel-content {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .notification-item,
        .message-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            gap: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .notification-item:hover,
        .message-item:hover {
            background: #f8fafc;
        }
        
        .notification-item:last-child,
        .message-item:last-child {
            border-bottom: none;
        }
        
        .notification-item.unread,
        .message-item.unread {
            background: #eff6ff;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }
        
        .notification-icon .fa-exclamation-triangle {
            background: #f59e0b;
        }
        
        .notification-icon .fa-info-circle {
            background: #3b82f6;
        }
        
        .notification-icon .fa-chart-line {
            background: #10b981;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            flex-shrink: 0;
        }
        
        .message-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .notification-content,
        .message-content {
            flex: 1;
        }
        
        .notification-content h4,
        .message-content h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .notification-content p,
        .message-content p {
            margin: 0 0 8px 0;
            font-size: 13px;
            color: #64748b;
            line-height: 1.4;
        }
        
        .notification-time,
        .message-time {
            font-size: 12px;
            color: #94a3b8;
        }
        
        /* 个人资料菜单 */
        .profile-menu {
            position: fixed;
            top: 70px;
            right: 24px;
            width: 220px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            transform: translateY(-10px);
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            overflow: hidden;
        }
        
        .profile-menu.show {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }
        
        .profile-menu-item {
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 14px;
            color: #64748b;
        }
        
        .profile-menu-item:hover {
            background: #f8fafc;
        }
        
        .profile-menu-item.logout {
            color: #ef4444;
        }
        
        .profile-menu-item.logout:hover {
            background: #fef2f2;
        }
        
        .profile-menu-divider {
            height: 1px;
            background: #e2e8f0;
            margin: 8px 0;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .admin-dashboard {
                flex-direction: column;
            }
            
            .admin-header {
                order: 1;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
            }
            
            .admin-sidebar {
                width: 100%;
                height: auto;
                order: 3;
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                border-top: 1px solid #e2e8f0;
                border-right: none;
                padding: 10px 0;
                z-index: 999;
            }
            
            .admin-main {
                order: 2;
                margin-top: 80px;
                margin-bottom: 80px;
            }
            
            .content-page {
                height: calc(100vh - 160px);
            }
            
            .sidebar-menu {
                display: flex;
                overflow-x: auto;
                padding: 0;
            }
            
            .menu-section {
                display: flex;
                margin: 0;
            }
            
            .menu-section h3 {
                display: none;
            }
            
            .menu-list {
                display: flex;
                flex-direction: row;
                align-items: center;
            }
            
            .menu-item {
                margin: 0 4px;
                flex-shrink: 0;
            }
            
            .menu-item a {
                flex-direction: column;
                padding: 8px 12px;
                text-align: center;
                border-right: none;
                border-bottom: 3px solid transparent;
                min-width: 60px;
            }
            
            .menu-item.active a {
                border-right: none;
                border-bottom-color: #3b82f6;
            }
            
            .menu-item a i {
                margin: 0 0 4px 0;
                font-size: 18px;
            }
            
            .menu-item a span {
                font-size: 11px;
                white-space: nowrap;
            }
            
            .menu-count {
                display: none;
            }
            
            .sidebar-footer {
                display: none;
            }
            
            .notification-panel,
            .message-panel,
            .profile-menu {
                right: 10px;
                width: calc(100vw - 20px);
                max-width: 380px;
            }
        }
        
        /* 图片占位符 */
        .image-placeholder {
            background: #f1f5f9;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-size: 24px;
        }
        
        /* 图片错误处理 */
        img {
            max-width: 100%;
            height: auto;
        }
        
        img[src=""], 
        img:not([src]) {
            opacity: 0;
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="login-page">
        <div class="login-container">
            <div class="login-header">
                <div class="admin-logo image-placeholder" style="width: 64px; height: 64px; border-radius: 12px; margin: 0 auto 16px;">
                    <i class="fas fa-store"></i>
                </div>
                <h1>TeleShop 管理后台</h1>
                <p>欢迎使用 TeleShop 后台管理系统</p>
            </div>
            
            <form class="login-form" id="adminLoginForm">
                <div class="form-group">
                    <label for="adminEmail">邮箱地址</label>
                    <div class="input-wrapper">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="adminEmail" placeholder="请输入管理员邮箱" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="adminPassword">密码</label>
                    <div class="input-wrapper">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="adminPassword" placeholder="请输入密码" required>
                        <button type="button" class="toggle-password" onclick="togglePassword('adminPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-options">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        记住登录状态
                    </label>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>
                
                <button type="submit" class="login-btn">
                    <span class="btn-text">登录后台</span>
                    <i class="fas fa-arrow-right"></i>
                </button>
                
                <div class="security-notice">
                    <i class="fas fa-shield-alt"></i>
                    <span>安全提醒：管理员账户具有最高权限，请妥善保管登录凭证</span>
                </div>
            </form>
            
            <div class="login-footer">
                <p>&copy; 2024 TeleShop. 保留所有权利 | <a href="#">隐私政策</a> | <a href="#">服务条款</a></p>
            </div>
        </div>
    </div>
    
    <!-- 主后台界面 -->
    <div id="adminDashboard" class="admin-dashboard" style="display: none;">
        <!-- 顶部导航栏 -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo-section">
                    <div class="header-logo image-placeholder" style="width: 32px; height: 32px; border-radius: 6px;">
                        <i class="fas fa-store" style="font-size: 14px;"></i>
                    </div>
                    <span class="brand-name">TeleShop Admin</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="global-search">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="搜索用户、订单、商品..." id="globalSearch">
                    <button class="search-btn">搜索</button>
                </div>
            </div>
            
            <div class="header-right">
                <div class="header-actions">
                    <button class="action-btn notification-btn" onclick="toggleNotifications()">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <button class="action-btn message-btn" onclick="toggleMessages()">
                        <i class="fas fa-envelope"></i>
                        <span class="message-badge">7</span>
                    </button>
                    
                    <div class="admin-profile" onclick="toggleProfileMenu()">
                        <div class="admin-avatar image-placeholder" style="width: 40px; height: 40px; border-radius: 50%;">
                            <i class="fas fa-user" style="font-size: 16px;"></i>
                        </div>
                        <div class="admin-info">
                            <span class="admin-name">张管理员</span>
                            <span class="admin-role">超级管理员</span>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- 主体区域 -->
        <div class="admin-body">
            <!-- 侧边栏 -->
            <nav class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-menu">
                <div class="menu-section">
                    <h3>主要功能</h3>
                    <ul class="menu-list">
                        <li class="menu-item active" id="menu-dashboard">
                            <a href="#dashboard" onclick="loadPage('dashboard')">
                                <i class="fas fa-chart-pie"></i>
                                <span>仪表盘</span>
                            </a>
                        </li>
                        <li class="menu-item" id="menu-user-management">
                            <a href="#user-management" onclick="loadPage('user-management')">
                                <i class="fas fa-users"></i>
                                <span>用户管理</span>
                                <span class="menu-count">12,847</span>
                            </a>
                        </li>
                        <li class="menu-item has-submenu" id="menu-merchant-management">
                            <a href="#merchant-management" onclick="toggleSubmenu('merchant-management')">
                                <i class="fas fa-store"></i>
                                <span>商家管理</span>
                                <span class="menu-count">156</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu" id="submenu-merchant-management">
                                <li><a href="pages/merchant-management/merchant-list.html" onclick="loadPage('merchant-management', 'list')">商家列表</a></li>
                                <li><a href="pages/merchant-management/merchant-application.html" onclick="loadPage('merchant-management', 'application')">入驻申请</a></li>
                                <li><a href="pages/merchant-management/merchant-review.html" onclick="loadPage('merchant-management', 'review')">资质审核</a></li>
                                <li><a href="pages/merchant-management/merchant-permissions.html" onclick="loadPage('merchant-management', 'permissions')">权限管理</a></li>
                                <li><a href="pages/merchant-management/merchant-settings-enhanced.html" onclick="loadPage('merchant-management', 'settings')">商家设置</a></li>
                                <li><a href="pages/merchant-management/platform-operation-enhanced.html" onclick="loadPage('merchant-management', 'operation')">平台运营</a></li>
                            </ul>
                        </li>
                        <li class="menu-item" id="menu-product-management">
                            <a href="#product-management" onclick="loadPage('product-management')">
                                <i class="fas fa-box"></i>
                                <span>商品管理</span>
                                <span class="menu-count">567</span>
                            </a>
                        </li>
                        <li class="menu-item" id="menu-order-management">
                            <a href="#order-management" onclick="loadPage('order-management')">
                                <i class="fas fa-shopping-cart"></i>
                                <span>订单管理</span>
                                <span class="menu-count">1,892</span>
                            </a>
                        </li>
                        <li class="menu-item has-submenu" id="menu-marketing-tools">
                            <a href="#marketing-tools" onclick="toggleSubmenu('marketing-tools')">
                                <i class="fas fa-bullhorn"></i>
                                <span>营销工具</span>
                                <span class="menu-count">HOT</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu" id="submenu-marketing-tools">
                                <li><a href="pages/marketing-tools/promotional-activities.html" onclick="loadPage('marketing-tools', 'promotional')">促销活动</a></li>
                                <li><a href="pages/marketing-tools/social-marketing.html" onclick="loadPage('marketing-tools', 'social')">社交营销</a></li>
                                <li><a href="pages/marketing-tools/membership-system.html" onclick="loadPage('marketing-tools', 'membership')">会员体系</a></li>
                                <li><a href="pages/marketing-tools/new-retail.html" onclick="loadPage('marketing-tools', 'retail')">新零售</a></li>
                                <li><a href="pages/marketing-tools/marketing-analytics.html" onclick="loadPage('marketing-tools', 'analytics')">营销数据</a></li>
                            </ul>
                        </li>
                        <li class="menu-item" id="menu-financial-management">
                            <a href="#financial-management" onclick="loadPage('financial-management')">
                                <i class="fas fa-wallet"></i>
                                <span>财务管理</span>
                            </a>
                        </li>
                        <li class="menu-item has-submenu" id="menu-wallet-management">
                            <a href="#wallet-management" onclick="toggleSubmenu('wallet-management')">
                                <i class="fas fa-credit-card"></i>
                                <span>钱包管理</span>
                                <span class="menu-count">数字化</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu" id="submenu-wallet-management">
                                <li><a href="pages/wallet-management/user-wallet-management.html" onclick="loadPage('wallet-management', 'user-wallet')">用户钱包</a></li>
                                <li><a href="pages/wallet-management/transaction-management.html" onclick="loadPage('wallet-management', 'transaction')">交易管理</a></li>
                                <li><a href="pages/wallet-management/deposit-withdrawal.html" onclick="loadPage('wallet-management', 'deposit')">充值提现</a></li>
                                <li><a href="pages/wallet-management/risk-control.html" onclick="loadPage('wallet-management', 'risk')">风控管理</a></li>
                                <li><a href="pages/wallet-management/digital-currency.html" onclick="loadPage('wallet-management', 'digital')">数字货币</a></li>
                                <li><a href="pages/wallet-management/financial-reconciliation.html" onclick="loadPage('wallet-management', 'reconciliation')">财务对账</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>内容运营</h3>
                    <ul class="menu-list">
                        <li class="menu-item" id="menu-homepage-editor">
                            <a href="#homepage-editor" onclick="loadPage('homepage-editor')">
                                <i class="fas fa-home"></i>
                                <span>首页管理</span>
                                <span class="menu-count">可视化</span>
                            </a>
                        </li>
                        <li class="menu-item has-submenu" id="menu-content-management">
                            <a href="#content-management" onclick="toggleSubmenu('content-management')">
                                <i class="fas fa-comments"></i>
                                <span>内容管理</span>
                                <span class="menu-count">45.2K</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu" id="submenu-content-management">
                                <li><a href="pages/content-management/chat-management.html" onclick="loadPage('content-management', 'chat')">聊天管理</a></li>
                                <li><a href="pages/content-management/customer-service.html" onclick="loadPage('content-management', 'service')">客服系统</a></li>
                            </ul>
                        </li>
                        <li class="menu-item has-submenu" id="menu-data-analytics-enhanced">
                            <a href="#data-analytics-enhanced" onclick="toggleSubmenu('data-analytics-enhanced')">
                                <i class="fas fa-chart-line"></i>
                                <span>数据分析</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu" id="submenu-data-analytics-enhanced">
                                <li><a href="pages/data-analytics/index.html" onclick="loadPage('data-analytics', 'basic')">基础分析</a></li>
                                <li><a href="pages/data-analytics/enhanced-analytics.html" onclick="loadPage('data-analytics', 'enhanced')">高级分析</a></li>
                                <li><a href="pages/reports-analytics/index.html" onclick="loadPage('reports-analytics', 'reports')">报表中心</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>系统设置</h3>
                    <ul class="menu-list">
                        <li class="menu-item has-submenu" id="menu-system-settings">
                            <a href="#system-settings" onclick="toggleSubmenu('system-settings')">
                                <i class="fas fa-cog"></i>
                                <span>系统设置</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu" id="submenu-system-settings">
                                <li><a href="pages/system-settings/index.html#general" onclick="loadPage('system-settings', 'basic')">基础设置</a></li>
                                <li><a href="pages/system-settings/advanced-settings.html" onclick="loadPage('system-settings', 'advanced')">高级设置</a></li>
                                <li><a href="pages/system-settings/role-management.html" onclick="loadPage('system-settings', 'roles')">角色管理</a></li>
                            </ul>
                        </li>
                        <li class="menu-item has-submenu" id="menu-permission-management">
                            <a href="#permission-management" onclick="toggleSubmenu('permission-management')">
                                <i class="fas fa-shield-alt"></i>
                                <span>权限管理</span>
                                <span class="menu-count">安全</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu" id="submenu-permission-management">
                                <li><a href="pages/permission-management/admin-permissions.html" onclick="loadPage('permission-management', 'admin')">管理员权限</a></li>
                                <li><a href="pages/permission-management/api-permissions.html" onclick="loadPage('permission-management', 'api')">API权限</a></li>
                            </ul>
                        </li>

                        <li class="menu-item" id="menu-logs">
                            <a href="#logs" onclick="loadPage('logs')">
                                <i class="fas fa-list-alt"></i>
                                <span>系统日志</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="sidebar-footer">
                <div class="system-status">
                    <div class="status-item">
                        <span class="status-dot online"></span>
                        <span>系统正常</span>
                    </div>
                    <div class="system-version">v2.1.0</div>
                </div>
            </div>
        </nav>
        
        <!-- 主内容区域 -->
        <main class="admin-main" id="adminMain">
            <div class="main-content" id="mainContent">
                <!-- 仪表盘页面（默认显示） -->
                <div id="dashboard-content" class="content-page active">
                    <iframe src="pages/dashboard/index.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 用户管理页面 -->
                <div id="user-management-content" class="content-page">
                    <iframe src="pages/user-management/index.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 商品管理页面 -->
                <div id="product-management-content" class="content-page">
                    <iframe src="pages/product-management/index.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 订单管理页面 -->
                <div id="order-management-content" class="content-page">
                    <iframe src="pages/order-management/index.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 财务管理页面 -->
                <div id="financial-management-content" class="content-page">
                    <iframe src="pages/financial-management/index.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 商家管理页面 -->
                <div id="merchant-management-content" class="content-page">
                    <iframe src="pages/merchant-management/merchant-list.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 内容管理页面 -->
                <div id="content-management-content" class="content-page">
                    <iframe src="pages/content-management/chat-management.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 数据分析页面 -->
                <div id="data-analytics-enhanced-content" class="content-page">
                    <iframe src="pages/data-analytics/enhanced-analytics.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 报表分析页面 -->
                <div id="reports-analytics-content" class="content-page">
                    <iframe src="pages/reports-analytics/index.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 系统设置页面 -->
                <div id="system-settings-content" class="content-page">
                    <iframe src="pages/system-settings/index.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 首页管理页面 -->
                <div id="homepage-editor-content" class="content-page">
                    <iframe src="pages/homepage-editor/index.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 钱包管理页面 -->
                <div id="wallet-management-content" class="content-page">
                    <iframe src="pages/wallet-management/user-wallet-management.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 权限管理页面 -->
                <div id="permission-management-content" class="content-page">
                    <iframe src="pages/permission-management/admin-permissions.html" style="width: 100%; height: 100%; border: none;"></iframe>
                </div>
                
                <!-- 系统日志页面 -->
                <div id="logs-content" class="content-page">
                    <div class="content-loading">
                        <i class="fas fa-list-alt"></i>
                        <h3>系统日志</h3>
                        <p>系统日志功能正在开发中...</p>
                    </div>
                </div>
            </div>
        </main>
        </div> <!-- admin-body 结束 -->
        
        <!-- 通知面板 -->
        <div class="notification-panel" id="notificationPanel">
            <div class="panel-header">
                <h3>系统通知</h3>
                <button onclick="toggleNotifications()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="panel-content">
                <div class="notification-item unread">
                    <div class="notification-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="notification-content">
                        <h4>系统警告</h4>
                        <p>检测到异常登录尝试，请注意账户安全</p>
                        <span class="notification-time">5分钟前</span>
                    </div>
                </div>
                <div class="notification-item">
                    <div class="notification-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="notification-content">
                        <h4>系统更新</h4>
                        <p>后台管理系统已更新至 v2.1.0</p>
                        <span class="notification-time">2小时前</span>
                    </div>
                </div>
                <div class="notification-item">
                    <div class="notification-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="notification-content">
                        <h4>数据报告</h4>
                        <p>今日销售额已突破 ¥100,000</p>
                        <span class="notification-time">3小时前</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 消息面板 -->
        <div class="message-panel" id="messagePanel">
            <div class="panel-header">
                <h3>系统消息</h3>
                <button onclick="toggleMessages()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="panel-content">
                <div class="message-item unread">
                    <div class="message-avatar image-placeholder">
                        <i class="fas fa-user" style="font-size: 14px;"></i>
                    </div>
                    <div class="message-content">
                        <h4>用户投诉</h4>
                        <p>用户反馈商品质量问题，需要处理</p>
                        <span class="message-time">10分钟前</span>
                    </div>
                </div>
                <div class="message-item">
                    <div class="message-avatar image-placeholder">
                        <i class="fas fa-user" style="font-size: 14px;"></i>
                    </div>
                    <div class="message-content">
                        <h4>退款申请</h4>
                        <p>订单 #12345 申请退款，等待审核</p>
                        <span class="message-time">1小时前</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 个人资料菜单 -->
        <div class="profile-menu" id="profileMenu">
            <div class="profile-menu-item">
                <i class="fas fa-user"></i>
                <span>个人资料</span>
            </div>
            <div class="profile-menu-item">
                <i class="fas fa-cog"></i>
                <span>账户设置</span>
            </div>
            <div class="profile-menu-item">
                <i class="fas fa-bell"></i>
                <span>通知设置</span>
            </div>
            <div class="profile-menu-divider"></div>
            <div class="profile-menu-item logout" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                <span>退出登录</span>
            </div>
        </div>
    </div>
    
    <!-- 轻量级JavaScript - 优先保证登录功能 -->
    <script>
        console.log('🚀 TeleShop Admin 启动（轻量级模式）');
        
        // 立即确保输入框可用
        function enableInputs() {
            const emailInput = document.getElementById('adminEmail');
            const passwordInput = document.getElementById('adminPassword');
            
            if (emailInput) {
                emailInput.removeAttribute('disabled');
                emailInput.style.pointerEvents = 'auto';
                console.log('✅ 邮箱输入框已启用');
            }
            
            if (passwordInput) {
                passwordInput.removeAttribute('disabled');
                passwordInput.style.pointerEvents = 'auto';
                console.log('✅ 密码输入框已启用');
            }
        }
        
        // 页面加载完成后立即启用
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM加载完成');
            
            // 显示登录页面
            const loginPage = document.getElementById('loginPage');
            const adminDashboard = document.getElementById('adminDashboard');
            
            if (loginPage) {
                loginPage.style.display = 'flex';
                console.log('✅ 登录页面已显示');
            }
            
            if (adminDashboard) {
                adminDashboard.style.display = 'none';
            }
            
            // 启用输入框
            enableInputs();
            
            // 设置焦点
            setTimeout(() => {
                const emailInput = document.getElementById('adminEmail');
                if (emailInput) {
                    emailInput.focus();
                    console.log('✅ 输入框焦点已设置');
                }
            }, 100);
        });
        
        // 如果DOM已经加载，立即执行
        if (document.readyState === 'loading') {
            // DOM仍在加载
        } else {
            // DOM已经加载完成
            enableInputs();
        }
        
        // 绑定登录表单
        function setupLoginForm() {
            const loginForm = document.getElementById('adminLoginForm');
            if (!loginForm) return;
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = document.getElementById('adminEmail').value.trim();
                const password = document.getElementById('adminPassword').value.trim();
                
                console.log('🔐 尝试登录:', email);
                
                if (!email || !password) {
                    alert('请输入邮箱和密码');
                    return;
                }
                
                // 简单验证（演示用）
                if (email.includes('@') && password.length >= 4) {
                    console.log('✅ 登录验证成功');
                    
                    // 隐藏登录页面
                    const loginPage = document.getElementById('loginPage');
                    const adminDashboard = document.getElementById('adminDashboard');
                    
                    if (loginPage) {
                        loginPage.style.display = 'none';
                        console.log('✅ 登录页面已隐藏');
                    }
                    
                    if (adminDashboard) {
                        adminDashboard.style.display = 'flex';
                        console.log('✅ 后台界面已显示');
                    }
                    
                    // 初始化后台功能
                    setTimeout(initAdminPanel, 100);
                    
                } else {
                    alert('邮箱格式不正确或密码太短（至少4位）');
                }
            });
        }
        
        // 初始化后台面板
        function initAdminPanel() {
            console.log('🎛️ 初始化后台面板...');
            
            // 确保默认页面显示
            const defaultPage = document.getElementById('dashboard-content');
            if (defaultPage) {
                defaultPage.classList.add('active');
                console.log('✅ 默认仪表盘页面已激活');
            }
            
            // 激活默认菜单项
            const defaultMenuItem = document.getElementById('menu-dashboard');
            if (defaultMenuItem) {
                defaultMenuItem.classList.add('active');
                console.log('✅ 默认菜单项已激活');
            }
        }
        
        // DOM加载完成后绑定登录表单
        document.addEventListener('DOMContentLoaded', setupLoginForm);
        
        // 退出登录函数
        function logout() {
            console.log('👋 退出登录');
            
            const loginPage = document.getElementById('loginPage');
            const adminDashboard = document.getElementById('adminDashboard');
            
            if (adminDashboard) {
                adminDashboard.style.display = 'none';
                console.log('✅ 后台界面已隐藏');
            }
            
            if (loginPage) {
                loginPage.style.display = 'flex';
                console.log('✅ 登录页面已显示');
            }
            
            // 清空表单
            const emailInput = document.getElementById('adminEmail');
            const passwordInput = document.getElementById('adminPassword');
            
            if (emailInput) emailInput.value = '';
            if (passwordInput) passwordInput.value = '';
            
            // 重新设置焦点
            setTimeout(() => {
                if (emailInput) emailInput.focus();
            }, 100);
        }
        
        // 延迟加载其他资源（可选）
        setTimeout(() => {
            console.log('⏳ 开始加载额外资源...');
            // 这里可以加载其他JS文件，但不影响登录功能
        }, 2000);
    </script>
    <script>
        // 页面初始化和修复脚本
        console.log('🚀 TeleShop Admin 页面初始化...');
        
        // 图片错误处理
        function handleImageError(img) {
            const placeholder = img.nextElementSibling || img.previousElementSibling;
            if (placeholder && placeholder.classList.contains('image-placeholder')) {
                img.style.display = 'none';
                placeholder.style.display = 'flex';
            }
        }
        
        // 为所有图片添加错误处理
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.addEventListener('error', function() {
                    handleImageError(this);
                });
                
                // 检查是否已经加载失败
                if (!img.complete || img.naturalHeight === 0) {
                    handleImageError(img);
                }
            });
        });
        
        // 统一的页面标题管理
        const PAGE_TITLES = {
            'dashboard': '仪表盘',
            'user-management': '用户管理',
            'merchant-management': '商家管理',
            'product-management': '商品管理',
            'order-management': '订单管理',
            'financial-management': '财务管理',
            'wallet-management': '钱包管理',
            'permission-management': '权限管理',
            'content-management': '内容管理',
            'data-analytics-enhanced': '数据分析',
            'reports-analytics': '报表中心',
            'system-settings': '系统设置',
            'logs': '系统日志'
        };
        
        function updatePageTitle(pageName) {
            const title = PAGE_TITLES[pageName] || '未知页面';
            document.title = `${title} - TeleShop 后台管理`;
        }
        
        // 立即可用的页面切换函数
        function loadPageDirect(pageName) {
            console.log('📄 切换页面:', pageName);
            
            try {
                // 隐藏所有内容页面
                const contentPages = document.querySelectorAll('.content-page');
                contentPages.forEach(page => {
                    page.classList.remove('active');
                });
                
                // 显示目标页面
                const targetPage = document.getElementById(pageName + '-content');
                if (targetPage) {
                    targetPage.classList.add('active');
                    console.log('✅ 页面显示成功:', targetPage.id);
                } else {
                    console.error('❌ 未找到目标页面:', pageName + '-content');
                    return;
                }
                
                // 更新菜单状态
                const menuItems = document.querySelectorAll('.menu-item');
                menuItems.forEach(item => {
                    item.classList.remove('active');
                });
                
                const activeMenuItem = document.getElementById('menu-' + pageName);
                if (activeMenuItem) {
                    activeMenuItem.classList.add('active');
                    console.log('✅ 菜单激活成功:', activeMenuItem.id);
                }
                
                // 更新页面标题
                updatePageTitle(pageName);
                
                console.log('✅ 页面切换完成:', pageName);
                
            } catch (error) {
                console.error('❌ 页面切换出错:', error);
            }
        }
        
        // 增强的页面加载函数，支持子页面
        function loadPageEnhanced(pageName, subPage) {
            console.log('📄 切换页面:', pageName, subPage ? `(${subPage})` : '');
            
            try {
                // 隐藏所有内容页面
                const contentPages = document.querySelectorAll('.content-page');
                contentPages.forEach(page => {
                    page.classList.remove('active');
                });
                
                // 显示目标页面
                const targetPage = document.getElementById(pageName + '-content');
                if (targetPage) {
                    targetPage.classList.add('active');
                    
                    // 如果有子页面参数，更新iframe源
                    if (subPage) {
                        const iframe = targetPage.querySelector('iframe');
                        if (iframe) {
                            const pageUrls = {
                                'merchant-management': {
                                    'list': 'pages/merchant-management/merchant-list.html',
                                    'application': 'pages/merchant-management/merchant-application.html',
                                    'review': 'pages/merchant-management/merchant-review.html',
                                    'permissions': 'pages/merchant-management/merchant-permissions.html',
                                    'settings': 'pages/merchant-management/merchant-settings.html',
                                    'operation': 'pages/merchant-management/platform-operation.html'
                                },
                                'wallet-management': {
                                    'user-wallet': 'pages/wallet-management/user-wallet-management.html',
                                    'transaction': 'pages/wallet-management/transaction-management.html',
                                    'deposit': 'pages/wallet-management/deposit-withdrawal.html',
                                    'risk': 'pages/wallet-management/risk-control.html',
                                    'digital': 'pages/wallet-management/digital-currency.html',
                                    'reconciliation': 'pages/wallet-management/financial-reconciliation.html'
                                },
                                'permission-management': {
                                    'admin': 'pages/permission-management/admin-permissions.html',
                                    'api': 'pages/permission-management/api-permissions.html'
                                },
                                'content-management': {
                                    'chat': 'pages/content-management/chat-management.html',
                                    'service': 'pages/content-management/customer-service.html'
                                },
                                'data-analytics': {
                                    'basic': 'pages/data-analytics/index.html',
                                    'enhanced': 'pages/data-analytics/enhanced-analytics.html'
                                },
                                'reports-analytics': {
                                    'reports': 'pages/reports-analytics/index.html'
                                },
                                'system-settings': {
                                    'basic': 'pages/system-settings/index.html',
                                    'advanced': 'pages/system-settings/advanced-settings.html',
                                    'roles': 'pages/system-settings/role-management.html'
                                }
                            };
                            
                            if (pageUrls[pageName] && pageUrls[pageName][subPage]) {
                                iframe.src = pageUrls[pageName][subPage];
                                console.log('🔄 更新iframe源:', iframe.src);
                            }
                        }
                    }
                    
                    console.log('✅ 页面显示成功:', targetPage.id);
                } else {
                    console.error('❌ 未找到目标页面:', pageName + '-content');
                    return;
                }
                
                // 更新菜单状态
                const menuItems = document.querySelectorAll('.menu-item');
                menuItems.forEach(item => {
                    item.classList.remove('active');
                });
                
                const activeMenuItem = document.getElementById('menu-' + pageName);
                if (activeMenuItem) {
                    activeMenuItem.classList.add('active');
                    console.log('✅ 菜单激活成功:', activeMenuItem.id);
                }
                
                // 更新页面标题
                updatePageTitle(pageName);
                
                console.log('✅ 页面切换完成:', pageName);
                
            } catch (error) {
                console.error('❌ 页面切换出错:', error);
            }
        }
        
        // 覆盖全局函数
        window.loadPage = function(pageName, subPage) {
            if (subPage) {
                loadPageEnhanced(pageName, subPage);
            } else {
                loadPageDirect(pageName);
            }
        };
        
        // 通知面板控制
        function toggleNotifications() {
            const panel = document.getElementById('notificationPanel');
            if (panel.classList.contains('show')) {
                panel.classList.remove('show');
            } else {
                // 关闭其他面板
                document.getElementById('messagePanel').classList.remove('show');
                document.getElementById('profileMenu').classList.remove('show');
                panel.classList.add('show');
            }
        }
        
        // 消息面板控制
        function toggleMessages() {
            const panel = document.getElementById('messagePanel');
            if (panel.classList.contains('show')) {
                panel.classList.remove('show');
            } else {
                // 关闭其他面板
                document.getElementById('notificationPanel').classList.remove('show');
                document.getElementById('profileMenu').classList.remove('show');
                panel.classList.add('show');
            }
        }
        
        // 个人资料菜单控制
        function toggleProfileMenu() {
            const menu = document.getElementById('profileMenu');
            if (menu.classList.contains('show')) {
                menu.classList.remove('show');
            } else {
                // 关闭其他面板
                document.getElementById('notificationPanel').classList.remove('show');
                document.getElementById('messagePanel').classList.remove('show');
                menu.classList.add('show');
            }
        }
        
        // 点击外部关闭面板
        document.addEventListener('click', function(event) {
            const panels = ['notificationPanel', 'messagePanel', 'profileMenu'];
            const triggers = ['.notification-btn', '.message-btn', '.admin-profile'];
            
            let clickedTrigger = false;
            triggers.forEach(selector => {
                if (event.target.closest(selector)) {
                    clickedTrigger = true;
                }
            });
            
            if (!clickedTrigger) {
                panels.forEach(panelId => {
                    const panel = document.getElementById(panelId);
                    if (!panel.contains(event.target)) {
                        panel.classList.remove('show');
                    }
                });
            }
        });
        
        // 侧边栏切换（移动端）
        function toggleSidebar() {
            const sidebar = document.getElementById('adminSidebar');
            sidebar.classList.toggle('mobile-open');
        }
        
        // 二级菜单切换
        function toggleSubmenu(menuId) {
            const menuItem = document.getElementById('menu-' + menuId);
            const allMenuItems = document.querySelectorAll('.menu-item.has-submenu');
            
            // 关闭其他二级菜单
            allMenuItems.forEach(item => {
                if (item !== menuItem) {
                    item.classList.remove('open');
                }
            });
            
            // 切换当前菜单
            menuItem.classList.toggle('open');
        }
        
        // 密码显示切换
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const button = input.nextElementSibling;
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }
        
        // 登录处理
        document.getElementById('adminLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('adminEmail').value;
            const password = document.getElementById('adminPassword').value;
            
            // 简单验证（实际项目中应该连接后端API）
            if (email && password) {
                console.log('🔐 登录成功, 切换到后台界面');
                document.getElementById('loginPage').style.display = 'none';
                document.getElementById('adminDashboard').style.display = 'flex';
                
                // 初始化后台功能
                setTimeout(() => {
                    if (window.AdminApp) {
                        console.log('🎯 初始化AdminApp');
                        const app = new AdminApp();
                        app.init();
                    }
                    
                    if (window.AdvancedSearchManager) {
                        console.log('🔍 初始化搜索功能');
                        const searchManager = new AdvancedSearchManager();
                        searchManager.init();
                    }
                    
                    if (window.APIService) {
                        console.log('🌐 初始化API服务');
                        const apiService = new APIService();
                        window.api = apiService;
                    }
                }, 100);
            } else {
                alert('请输入邮箱和密码');
            }
        });
        
        // 退出登录
        function logout() {
            console.log('👋 退出登录');
            document.getElementById('adminDashboard').style.display = 'none';
            document.getElementById('loginPage').style.display = 'flex';
            
            // 清理状态
            document.getElementById('adminEmail').value = '';
            document.getElementById('adminPassword').value = '';
            document.getElementById('rememberMe').checked = false;
            
            // 关闭所有面板
            document.getElementById('notificationPanel').classList.remove('show');
            document.getElementById('messagePanel').classList.remove('show');
            document.getElementById('profileMenu').classList.remove('show');
        }
        
        // 全局搜索处理
        document.getElementById('globalSearch').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    console.log('🔍 执行全局搜索:', query);
                    // 这里可以调用高级搜索功能
                    if (window.AdvancedSearchManager) {
                        // 触发搜索
                    }
                }
            }
        });
        
        // 响应式处理
        function handleResize() {
            const width = window.innerWidth;
            const dashboard = document.getElementById('adminDashboard');
            
            if (width <= 768) {
                dashboard.classList.add('mobile-view');
            } else {
                dashboard.classList.remove('mobile-view');
            }
        }
        
        window.addEventListener('resize', handleResize);
        handleResize(); // 初始检查
        
        console.log('✅ TeleShop Admin 初始化完成');
    </script>
</body>
</html> 