#!/usr/bin/env node

/**
 * TeleShop Admin - 批量页面修复脚本
 * 修复所有admin-backend页面的兼容性和显示问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始批量修复admin-backend页面...');

// 需要修复的页面列表
const pagesToFix = [
    'pages/data-analytics/index.html',
    'pages/content-management/chat-management.html',
    'pages/user-management/edit-user.html',
    'pages/user-management/user-detail.html',
    'pages/user-management/batch-operations.html',
    'pages/user-management/enhanced.html',
    'pages/product-management/category-management.html',
    'pages/product-management/inventory-management.html',
    'pages/product-management/product-edit.html',
    'pages/order-management/order-detail.html',
    'pages/order-management/enhanced-index.html',
    'pages/order-management/complete-index.html',
    'pages/order-management/test.html',
    'debug-test.html',
    'menu-test.html'
];

// 标准的head修复模板
const standardHeadFix = `    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">`;

// 根目录页面的head修复模板
const rootHeadFix = `    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="assets/css/admin-styles.css">
    <link rel="stylesheet" href="assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="assets/css/image-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">`;

// 通用的兼容性修复CSS
const compatibilityCSS = `
    <style>
        /* 兼容性修复 */
        * {
            box-sizing: border-box;
        }
        
        html, body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* 图片修复 */
        img {
            max-width: 100%;
            height: auto;
        }
        
        img[src=""], img:not([src]) {
            opacity: 0;
        }
        
        .image-placeholder {
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-size: 16px;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            body {
                overflow-x: hidden;
            }
            
            .container {
                padding: 12px !important;
            }
            
            .table-responsive {
                overflow-x: auto;
            }
        }
        
        /* 布局修复 */
        .flex {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
        }
        
        .grid {
            display: -ms-grid;
            display: grid;
        }
    </style>`;

function fixHtmlFile(filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            console.warn(`⚠️ 文件不存在: ${filePath}`);
            return false;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // 检查是否需要修复viewport
        if (!content.includes('maximum-scale=1.0, user-scalable=no')) {
            content = content.replace(
                /<meta name="viewport" content="width=device-width, initial-scale=1\.0">/,
                '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">'
            );
            modified = true;
        }

        // 检查是否需要添加移动端CSS
        if (!content.includes('mobile-enhancements.css')) {
            // 确定CSS路径前缀
            const cssPrefix = filePath.includes('pages/') ? '../../assets/css/' : 'assets/css/';
            
            // 在第一个CSS引用后添加缺失的CSS文件
            content = content.replace(
                /(<link rel="stylesheet" href="[^"]*admin-styles\.css">)/,
                `$1
    <link rel="stylesheet" href="${cssPrefix}mobile-enhancements.css">
    <link rel="stylesheet" href="${cssPrefix}image-fixes.css">`
            );
            modified = true;
        }

        // 检查是否需要添加字体预连接
        if (!content.includes('fonts.googleapis.com')) {
            content = content.replace(
                /(<link rel="stylesheet" href="[^"]*image-fixes\.css">)/,
                `$1
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>`
            );
            modified = true;
        }

        // 检查是否需要添加兼容性CSS
        if (!content.includes('兼容性修复')) {
            content = content.replace(
                /(<\/head>)/,
                `${compatibilityCSS}
$1`
            );
            modified = true;
        }

        if (modified) {
            fs.writeFileSync(filePath, content);
            console.log(`✅ 修复完成: ${filePath}`);
            return true;
        } else {
            console.log(`ℹ️ 无需修复: ${filePath}`);
            return false;
        }

    } catch (error) {
        console.error(`❌ 修复失败: ${filePath}`, error.message);
        return false;
    }
}

// 批量修复页面
let fixedCount = 0;
let totalCount = pagesToFix.length;

console.log(`📋 准备修复 ${totalCount} 个页面...`);

pagesToFix.forEach(pagePath => {
    const fullPath = path.join(__dirname, pagePath);
    if (fixHtmlFile(fullPath)) {
        fixedCount++;
    }
});

console.log(`\n🎉 批量修复完成！`);
console.log(`📊 修复统计: ${fixedCount}/${totalCount} 个页面已修复`);
console.log(`⏰ 修复时间: ${new Date().toLocaleString()}`);

// 创建修复报告
const report = {
    timestamp: new Date().toISOString(),
    totalPages: totalCount,
    fixedPages: fixedCount,
    fixedList: pagesToFix,
    summary: {
        addedMobileCSS: fixedCount,
        addedImageFixCSS: fixedCount,
        fixedViewport: fixedCount,
        addedCompatibilityCSS: fixedCount
    }
};

fs.writeFileSync(
    path.join(__dirname, 'fix-report.json'), 
    JSON.stringify(report, null, 2)
);

console.log(`📄 修复报告已保存: fix-report.json`);
console.log(`\n🔧 所有页面修复完成！现在可以测试后台系统的兼容性。`); 