<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析页面调试测试</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f8fafc;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-info {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .sub-navigation {
            background: #ffffff;
            border-radius: 12px;
            padding: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin-bottom: 24px;
        }
        .nav-tabs {
            display: flex;
            gap: 4px;
        }
        .nav-tab {
            flex: 1;
            padding: 12px 16px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            transition: all 0.2s ease;
        }
        .nav-tab:hover {
            background: #f1f5f9;
            color: #1e293b;
        }
        .nav-tab.active {
            background: #3b82f6;
            color: white;
        }
        .analysis-content {
            display: none;
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            border: 2px solid #e2e8f0;
        }
        .analysis-content.active {
            display: block;
            border-color: #3b82f6;
        }
        .content-header h2 {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        .content-header p {
            color: #64748b;
            font-size: 16px;
            margin-bottom: 20px;
        }
        .test-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        .test-status.success {
            background: #dcfce7;
            color: #166534;
        }
        .test-status.error {
            background: #fef2f2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>数据分析页面调试测试</h1>
        
        <div class="debug-info">
            <strong>调试信息:</strong><br>
            <span id="debugStatus">正在初始化...</span><br>
            <span id="jsStatus">JavaScript状态: 检查中...</span><br>
            <span id="clickStatus">点击测试: 未开始</span>
        </div>

        <!-- 子菜单导航 -->
        <div class="sub-navigation">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="switchSection('overview', this)" data-test="overview">
                    <i class="fas fa-chart-line"></i>
                    概览
                </button>
                <button class="nav-tab" onclick="switchSection('sales', this)" data-test="sales">
                    <i class="fas fa-chart-bar"></i>
                    销售分析
                </button>
                <button class="nav-tab" onclick="switchSection('users', this)" data-test="users">
                    <i class="fas fa-users"></i>
                    用户分析
                </button>
                <button class="nav-tab" onclick="switchSection('products', this)" data-test="products">
                    <i class="fas fa-box"></i>
                    商品分析
                </button>
                <button class="nav-tab" onclick="switchSection('finance', this)" data-test="finance">
                    <i class="fas fa-dollar-sign"></i>
                    财务报告
                </button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div id="overview-content" class="analysis-content active">
            <div class="content-header">
                <h2>概览</h2>
                <p>这是概览页面内容</p>
            </div>
            <div>概览页面正常显示 <span class="test-status success">✓ 正常</span></div>
        </div>

        <div id="sales-content" class="analysis-content">
            <div class="content-header">
                <h2>销售分析</h2>
                <p>这是销售分析页面内容</p>
            </div>
            <div>销售分析页面正常显示 <span class="test-status success">✓ 正常</span></div>
        </div>

        <div id="users-content" class="analysis-content">
            <div class="content-header">
                <h2>用户分析</h2>
                <p>这是用户分析页面内容</p>
            </div>
            <div>用户分析页面正常显示 <span class="test-status success">✓ 正常</span></div>
        </div>

        <div id="products-content" class="analysis-content">
            <div class="content-header">
                <h2>商品分析</h2>
                <p>这是商品分析页面内容</p>
            </div>
            <div>商品分析页面正常显示 <span class="test-status success">✓ 正常</span></div>
        </div>

        <div id="finance-content" class="analysis-content">
            <div class="content-header">
                <h2>财务报告</h2>
                <p>这是财务报告页面内容</p>
            </div>
            <div>财务报告页面正常显示 <span class="test-status success">✓ 正常</span></div>
        </div>

        <div style="margin-top: 30px;">
            <h3>测试按钮</h3>
            <button onclick="testAllTabs()" style="padding: 10px 20px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer;">
                自动测试所有标签页
            </button>
            <button onclick="clearDebug()" style="padding: 10px 20px; background: #64748b; color: white; border: none; border-radius: 6px; cursor: pointer; margin-left: 10px;">
                清除调试信息
            </button>
        </div>

        <div id="testResults" style="margin-top: 20px; padding: 15px; background: #f8fafc; border-radius: 6px;">
            <h4>测试结果:</h4>
            <div id="resultsList"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        // 子菜单切换功能
        function switchSection(sectionId, tabElement) {
            const debugStatus = document.getElementById('debugStatus');
            const clickStatus = document.getElementById('clickStatus');
            
            try {
                debugStatus.innerHTML = `正在切换到: ${sectionId}`;
                console.log('switchSection 被调用:', sectionId);
                
                // 隐藏所有内容区域
                document.querySelectorAll('.analysis-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // 移除所有标签的active状态
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                
                // 显示对应的内容区域
                const targetContent = document.getElementById(sectionId + '-content');
                if (targetContent) {
                    targetContent.classList.add('active');
                    debugStatus.innerHTML = `成功切换到: ${sectionId}`;
                    clickStatus.innerHTML = `点击测试: 成功 - ${sectionId}`;
                } else {
                    debugStatus.innerHTML = `错误: 找不到内容区域 ${sectionId}-content`;
                    clickStatus.innerHTML = `点击测试: 失败 - 找不到${sectionId}内容`;
                }
                
                // 激活点击的标签
                if (tabElement) {
                    tabElement.classList.add('active');
                }
                
                // 记录测试结果
                addTestResult(sectionId, targetContent ? 'success' : 'error');
                
            } catch (error) {
                debugStatus.innerHTML = `JavaScript错误: ${error.message}`;
                clickStatus.innerHTML = `点击测试: JavaScript错误`;
                console.error('switchSection 错误:', error);
                addTestResult(sectionId, 'error', error.message);
            }
        }

        function addTestResult(sectionId, status, error = null) {
            const resultsList = document.getElementById('resultsList');
            const time = new Date().toLocaleTimeString();
            const statusClass = status === 'success' ? 'success' : 'error';
            const statusText = status === 'success' ? '✓ 成功' : '✗ 失败';
            
            const result = document.createElement('div');
            result.style.marginBottom = '5px';
            result.innerHTML = `
                <span style="color: #64748b;">[${time}]</span> 
                <strong>${sectionId}</strong>: 
                <span class="test-status ${statusClass}">${statusText}</span>
                ${error ? ` - ${error}` : ''}
            `;
            
            resultsList.appendChild(result);
        }

        function testAllTabs() {
            const tabs = ['overview', 'sales', 'users', 'products', 'finance'];
            let index = 0;
            
            function testNext() {
                if (index < tabs.length) {
                    const tabId = tabs[index];
                    const tabElement = document.querySelector(`[data-test="${tabId}"]`);
                    
                    if (tabElement) {
                        switchSection(tabId, tabElement);
                        index++;
                        setTimeout(testNext, 1000); // 1秒间隔测试下一个
                    }
                } else {
                    document.getElementById('debugStatus').innerHTML = '自动测试完成';
                }
            }
            
            testNext();
        }

        function clearDebug() {
            document.getElementById('resultsList').innerHTML = '';
            document.getElementById('debugStatus').innerHTML = '调试信息已清除';
            document.getElementById('clickStatus').innerHTML = '点击测试: 已重置';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            const debugStatus = document.getElementById('debugStatus');
            const jsStatus = document.getElementById('jsStatus');
            
            debugStatus.innerHTML = '页面加载完成';
            jsStatus.innerHTML = 'JavaScript状态: ✓ 正常加载';
            
            // 测试DOM元素
            const navTabs = document.querySelectorAll('.nav-tab');
            const contentAreas = document.querySelectorAll('.analysis-content');
            
            console.log('找到导航标签:', navTabs.length);
            console.log('找到内容区域:', contentAreas.length);
            
            if (navTabs.length > 0 && contentAreas.length > 0) {
                jsStatus.innerHTML = 'JavaScript状态: ✓ 正常，找到' + navTabs.length + '个标签和' + contentAreas.length + '个内容区域';
            } else {
                jsStatus.innerHTML = 'JavaScript状态: ✗ 错误，DOM元素不完整';
            }
        });
    </script>
</body>
</html> 