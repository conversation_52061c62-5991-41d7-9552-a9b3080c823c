import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Button,
  Select,
  DatePicker,
  Space,
  Progress,
  Tooltip,
  Modal,
  Form,
  Input,
  message,
  Tabs,
  Badge,
  Avatar,
  List,
  Typography,
  Divider
} from 'antd';
import {
  MessageOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  StarOutlined,
  RobotOutlined,
  TeamOutlined,
  WarningOutlined,
  ReloadOutlined,
  SettingOutlined,
  ExportOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import { customerServiceApi } from '../../services/customerService';
import './CustomerServiceMonitor.less';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;
const { Text, Title } = Typography;

const CustomerServiceMonitor = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({});
  const [sessions, setSessions] = useState([]);
  const [agents, setAgents] = useState([]);
  const [chartData, setChartData] = useState({});
  const [filters, setFilters] = useState({
    dateRange: null,
    status: 'all',
    serviceType: 'all'
  });
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [configForm] = Form.useForm();

  // 加载数据
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const [statsRes, sessionsRes, agentsRes, chartRes] = await Promise.all([
        customerServiceApi.getOverallStats(filters),
        customerServiceApi.getSessionList(filters),
        customerServiceApi.getAgentList(),
        customerServiceApi.getChartData(filters)
      ]);

      setStats(statsRes.data);
      setSessions(sessionsRes.data);
      setAgents(agentsRes.data);
      setChartData(chartRes.data);
    } catch (error) {
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    loadData();
    // 设置定时刷新
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, [loadData]);

  // 统计卡片配置
  const statisticCards = [
    {
      title: '总会话数',
      value: stats.totalSessions || 0,
      icon: <MessageOutlined />,
      color: '#1890ff',
      suffix: '个'
    },
    {
      title: '活跃会话',
      value: stats.activeSessions || 0,
      icon: <UserOutlined />,
      color: '#52c41a',
      suffix: '个'
    },
    {
      title: '平均响应时间',
      value: stats.avgResponseTime || 0,
      icon: <ClockCircleOutlined />,
      color: '#faad14',
      suffix: '秒'
    },
    {
      title: '解决率',
      value: stats.resolutionRate || 0,
      icon: <CheckCircleOutlined />,
      color: '#722ed1',
      suffix: '%'
    },
    {
      title: '满意度',
      value: stats.satisfactionRate || 0,
      icon: <StarOutlined />,
      color: '#eb2f96',
      suffix: '%'
    },
    {
      title: 'AI处理率',
      value: stats.aiHandlingRate || 0,
      icon: <RobotOutlined />,
      color: '#13c2c2',
      suffix: '%'
    }
  ];

  // 会话列表列配置
  const sessionColumns = [
    {
      title: '会话ID',
      dataIndex: 'sessionId',
      key: 'sessionId',
      width: 120,
      render: (text) => <Text code>{text}</Text>
    },
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      render: (user) => (
        <Space>
          <Avatar src={user.avatar} size="small">
            {user.name?.charAt(0)}
          </Avatar>
          <span>{user.name}</span>
        </Space>
      )
    },
    {
      title: '服务类型',
      dataIndex: 'serviceType',
      key: 'serviceType',
      render: (type) => (
        <Tag color={type === 'ai' ? 'blue' : 'green'}>
          {type === 'ai' ? 'AI客服' : '人工客服'}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusConfig = {
          active: { color: 'processing', text: '进行中' },
          waiting: { color: 'warning', text: '等待中' },
          closed: { color: 'default', text: '已结束' },
          transferred: { color: 'purple', text: '已转接' }
        };
        const config = statusConfig[status] || statusConfig.closed;
        return <Badge status={config.color} text={config.text} />;
      }
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration) => `${Math.floor(duration / 60)}分${duration % 60}秒`
    },
    {
      title: '消息数',
      dataIndex: 'messageCount',
      key: 'messageCount'
    },
    {
      title: '满意度',
      dataIndex: 'satisfaction',
      key: 'satisfaction',
      render: (score) => score ? (
        <Tooltip title={`${score}/5分`}>
          <Progress
            percent={(score / 5) * 100}
            size="small"
            showInfo={false}
            strokeColor={score >= 4 ? '#52c41a' : score >= 3 ? '#faad14' : '#ff4d4f'}
          />
        </Tooltip>
      ) : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => viewSessionDetail(record)}>
            查看详情
          </Button>
          {record.status === 'active' && (
            <Button size="small" type="primary" onClick={() => joinSession(record)}>
              介入会话
            </Button>
          )}
        </Space>
      )
    }
  ];

  // 客服代理列表列配置
  const agentColumns = [
    {
      title: '客服',
      dataIndex: 'agent',
      key: 'agent',
      render: (agent) => (
        <Space>
          <Avatar src={agent.avatar} size="small">
            {agent.name?.charAt(0)}
          </Avatar>
          <div>
            <div>{agent.name}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {agent.department}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: '在线状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      render: (status) => (
        <Badge
          status={status === 'online' ? 'success' : status === 'busy' ? 'processing' : 'default'}
          text={status === 'online' ? '在线' : status === 'busy' ? '忙碌' : '离线'}
        />
      )
    },
    {
      title: '当前会话',
      dataIndex: 'currentSessions',
      key: 'currentSessions',
      render: (count, record) => (
        <span>
          {count}/{record.maxSessions}
        </span>
      )
    },
    {
      title: '今日处理',
      dataIndex: 'todayHandled',
      key: 'todayHandled'
    },
    {
      title: '平均响应',
      dataIndex: 'avgResponseTime',
      key: 'avgResponseTime',
      render: (time) => `${time}秒`
    },
    {
      title: '满意度',
      dataIndex: 'satisfaction',
      key: 'satisfaction',
      render: (score) => (
        <Tooltip title={`${score}/5分`}>
          <Progress
            percent={(score / 5) * 100}
            size="small"
            showInfo={false}
            strokeColor={score >= 4 ? '#52c41a' : score >= 3 ? '#faad14' : '#ff4d4f'}
          />
        </Tooltip>
      )
    }
  ];

  // 会话趋势图配置
  const sessionTrendConfig = {
    data: chartData.sessionTrend || [],
    xField: 'time',
    yField: 'count',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  // 服务类型分布图配置
  const serviceTypeConfig = {
    data: chartData.serviceTypeDistribution || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [{ type: 'element-active' }],
  };

  // 响应时间分布图配置
  const responseTimeConfig = {
    data: chartData.responseTimeDistribution || [],
    xField: 'timeRange',
    yField: 'count',
    color: '#1890ff',
    columnWidthRatio: 0.8,
  };

  // 事件处理函数
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const viewSessionDetail = (session) => {
    // 实现查看会话详情
    Modal.info({
      title: `会话详情 - ${session.sessionId}`,
      width: 800,
      content: (
        <div>
          <p>用户：{session.user.name}</p>
          <p>开始时间：{new Date(session.startTime).toLocaleString()}</p>
          <p>状态：{session.status}</p>
          <p>消息数：{session.messageCount}</p>
          {/* 这里可以添加更多详情内容 */}
        </div>
      ),
    });
  };

  const joinSession = (session) => {
    // 实现介入会话
    Modal.confirm({
      title: '介入会话',
      content: `确定要介入会话 ${session.sessionId} 吗？`,
      onOk: async () => {
        try {
          await customerServiceApi.joinSession(session.sessionId);
          message.success('已成功介入会话');
          loadData();
        } catch (error) {
          message.error('介入会话失败');
        }
      },
    });
  };

  const exportReport = () => {
    // 实现导出报告
    message.info('导出功能开发中...');
  };

  const openConfig = () => {
    setConfigModalVisible(true);
  };

  const handleConfigSubmit = async (values) => {
    try {
      await customerServiceApi.updateConfig(values);
      message.success('配置更新成功');
      setConfigModalVisible(false);
      loadData();
    } catch (error) {
      message.error('配置更新失败');
    }
  };

  return (
    <div className="customer-service-monitor">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Title level={2}>智能客服监控</Title>
          <Text type="secondary">实时监控客服系统运行状态和服务质量</Text>
        </div>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={loadData} loading={loading}>
            刷新
          </Button>
          <Button icon={<ExportOutlined />} onClick={exportReport}>
            导出报告
          </Button>
          <Button icon={<SettingOutlined />} onClick={openConfig}>
            系统配置
          </Button>
        </Space>
      </div>

      {/* 筛选器 */}
      <Card className="filter-card">
        <Space wrap>
          <RangePicker
            placeholder={['开始时间', '结束时间']}
            onChange={(dates) => handleFilterChange('dateRange', dates)}
          />
          <Select
            placeholder="会话状态"
            style={{ width: 120 }}
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
          >
            <Option value="all">全部状态</Option>
            <Option value="active">进行中</Option>
            <Option value="waiting">等待中</Option>
            <Option value="closed">已结束</Option>
          </Select>
          <Select
            placeholder="服务类型"
            style={{ width: 120 }}
            value={filters.serviceType}
            onChange={(value) => handleFilterChange('serviceType', value)}
          >
            <Option value="all">全部类型</Option>
            <Option value="ai">AI客服</Option>
            <Option value="human">人工客服</Option>
          </Select>
        </Space>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        {statisticCards.map((card, index) => (
          <Col xs={24} sm={12} md={8} lg={4} key={index}>
            <Card>
              <Statistic
                title={card.title}
                value={card.value}
                suffix={card.suffix}
                prefix={
                  <span style={{ color: card.color, fontSize: 20 }}>
                    {card.icon}
                  </span>
                }
                valueStyle={{ color: card.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} className="charts-row">
        <Col xs={24} lg={12}>
          <Card title="会话趋势" loading={loading}>
            <Line {...sessionTrendConfig} height={300} />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="服务类型分布" loading={loading}>
            <Pie {...serviceTypeConfig} height={300} />
          </Card>
        </Col>
        <Col xs={24}>
          <Card title="响应时间分布" loading={loading}>
            <Column {...responseTimeConfig} height={300} />
          </Card>
        </Col>
      </Row>

      {/* 详细数据 */}
      <Card>
        <Tabs defaultActiveKey="sessions">
          <TabPane tab="会话列表" key="sessions">
            <Table
              columns={sessionColumns}
              dataSource={sessions}
              rowKey="sessionId"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>
          <TabPane tab="客服代理" key="agents">
            <Table
              columns={agentColumns}
              dataSource={agents}
              rowKey="agentId"
              loading={loading}
              pagination={false}
            />
          </TabPane>
          <TabPane tab="系统日志" key="logs">
            <List
              dataSource={chartData.systemLogs || []}
              renderItem={(log) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        style={{
                          backgroundColor: log.level === 'error' ? '#ff4d4f' : 
                                          log.level === 'warning' ? '#faad14' : '#52c41a'
                        }}
                      >
                        {log.level === 'error' ? <WarningOutlined /> : 
                         log.level === 'warning' ? <ClockCircleOutlined /> : <CheckCircleOutlined />}
                      </Avatar>
                    }
                    title={log.message}
                    description={new Date(log.timestamp).toLocaleString()}
                  />
                </List.Item>
              )}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 配置对话框 */}
      <Modal
        title="系统配置"
        visible={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={configForm}
          layout="vertical"
          onFinish={handleConfigSubmit}
        >
          <Form.Item
            name="maxConcurrentSessions"
            label="最大并发会话数"
            rules={[{ required: true, message: '请输入最大并发会话数' }]}
          >
            <Input type="number" placeholder="请输入最大并发会话数" />
          </Form.Item>
          
          <Form.Item
            name="aiConfidenceThreshold"
            label="AI置信度阈值"
            rules={[{ required: true, message: '请输入AI置信度阈值' }]}
          >
            <Input type="number" step="0.1" min="0" max="1" placeholder="0.8" />
          </Form.Item>
          
          <Form.Item
            name="autoTransferThreshold"
            label="自动转人工阈值"
            rules={[{ required: true, message: '请输入自动转人工阈值' }]}
          >
            <Input type="number" placeholder="3" />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存配置
              </Button>
              <Button onClick={() => setConfigModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CustomerServiceMonitor;
