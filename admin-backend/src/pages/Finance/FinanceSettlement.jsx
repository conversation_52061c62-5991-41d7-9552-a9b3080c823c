import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Button,
  Select,
  DatePicker,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  message,
  Tabs,
  Badge,
  Progress,
  Tooltip,
  Divider,
  Upload
} from 'antd';
import {
  DollarOutlined,
  BankOutlined,
  TransactionOutlined,
  CalculatorOutlined,
  FileTextOutlined,
  DownloadOutlined,
  UploadOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import { financeApi } from '../../services/finance';
import './FinanceSettlement.less';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

const FinanceSettlement = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [financeStats, setFinanceStats] = useState({});
  const [settlementRecords, setSettlementRecords] = useState([]);
  const [commissionRecords, setCommissionRecords] = useState([]);
  const [withdrawalRecords, setWithdrawalRecords] = useState([]);
  const [chartData, setChartData] = useState({});
  const [filters, setFilters] = useState({
    dateRange: null,
    settlementType: 'all',
    status: 'all',
    merchantId: null
  });
  const [settingModalVisible, setSettingModalVisible] = useState(false);
  const [settingForm] = Form.useForm();

  // 加载数据
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const [statsRes, settlementRes, commissionRes, withdrawalRes, chartRes] = await Promise.all([
        financeApi.getFinanceStats(filters),
        financeApi.getSettlementRecords(filters),
        financeApi.getCommissionRecords(filters),
        financeApi.getWithdrawalRecords(filters),
        financeApi.getChartData(filters)
      ]);

      setFinanceStats(statsRes.data);
      setSettlementRecords(settlementRes.data);
      setCommissionRecords(commissionRes.data);
      setWithdrawalRecords(withdrawalRes.data);
      setChartData(chartRes.data);
    } catch (error) {
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    loadData();
    // 设置定时刷新
    const interval = setInterval(loadData, 60000);
    return () => clearInterval(interval);
  }, [loadData]);

  // 统计卡片配置
  const statisticCards = [
    {
      title: '平台总收入',
      value: financeStats.totalRevenue || 0,
      icon: <DollarOutlined />,
      color: '#52c41a',
      prefix: '¥',
      precision: 2
    },
    {
      title: '待结算金额',
      value: financeStats.pendingSettlement || 0,
      icon: <ClockCircleOutlined />,
      color: '#faad14',
      prefix: '¥',
      precision: 2
    },
    {
      title: '今日佣金',
      value: financeStats.todayCommission || 0,
      icon: <CalculatorOutlined />,
      color: '#1890ff',
      prefix: '¥',
      precision: 2
    },
    {
      title: '待审核提现',
      value: financeStats.pendingWithdrawal || 0,
      icon: <BankOutlined />,
      color: '#722ed1',
      prefix: '¥',
      precision: 2
    },
    {
      title: '结算成功率',
      value: financeStats.settlementSuccessRate || 0,
      icon: <CheckCircleOutlined />,
      color: '#13c2c2',
      suffix: '%'
    },
    {
      title: '平均结算周期',
      value: financeStats.avgSettlementCycle || 0,
      icon: <TransactionOutlined />,
      color: '#eb2f96',
      suffix: '天'
    }
  ];

  // 结算记录列配置
  const settlementColumns = [
    {
      title: '结算单号',
      dataIndex: 'settlementId',
      key: 'settlementId',
      width: 150,
      render: (text) => <span style={{ fontFamily: 'monospace' }}>{text}</span>
    },
    {
      title: '商家信息',
      dataIndex: 'merchantInfo',
      key: 'merchantInfo',
      render: (merchant) => (
        <div>
          <div>{merchant.name}</div>
          <div style={{ fontSize: 12, color: '#999' }}>ID: {merchant.id}</div>
        </div>
      )
    },
    {
      title: '结算类型',
      dataIndex: 'settlementType',
      key: 'settlementType',
      render: (type) => {
        const typeConfig = {
          daily: { color: 'blue', text: '日结算' },
          weekly: { color: 'green', text: '周结算' },
          monthly: { color: 'purple', text: '月结算' },
          manual: { color: 'orange', text: '手动结算' }
        };
        const config = typeConfig[type] || typeConfig.daily;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '结算金额',
      dataIndex: 'settlementAmount',
      key: 'settlementAmount',
      render: (amount) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ¥{amount.toFixed(2)}
        </span>
      )
    },
    {
      title: '手续费',
      dataIndex: 'serviceFee',
      key: 'serviceFee',
      render: (fee) => <span>¥{fee.toFixed(2)}</span>
    },
    {
      title: '实际到账',
      dataIndex: 'actualAmount',
      key: 'actualAmount',
      render: (amount) => (
        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
          ¥{amount.toFixed(2)}
        </span>
      )
    },
    {
      title: '结算周期',
      dataIndex: 'settlementPeriod',
      key: 'settlementPeriod',
      render: (period) => `${period.start} ~ ${period.end}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusConfig = {
          pending: { color: 'processing', text: '待结算' },
          processing: { color: 'warning', text: '结算中' },
          completed: { color: 'success', text: '已完成' },
          failed: { color: 'error', text: '失败' }
        };
        const config = statusConfig[status] || statusConfig.pending;
        return <Badge status={config.color} text={config.text} />;
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => viewSettlementDetail(record)}>
            查看详情
          </Button>
          {record.status === 'pending' && (
            <Button size="small" type="primary" onClick={() => processSettlement(record)}>
              执行结算
            </Button>
          )}
          <Button size="small" onClick={() => downloadSettlementReport(record)}>
            下载报告
          </Button>
        </Space>
      )
    }
  ];

  // 佣金记录列配置
  const commissionColumns = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 150
    },
    {
      title: '商家',
      dataIndex: 'merchantName',
      key: 'merchantName'
    },
    {
      title: '订单金额',
      dataIndex: 'orderAmount',
      key: 'orderAmount',
      render: (amount) => `¥${amount.toFixed(2)}`
    },
    {
      title: '佣金率',
      dataIndex: 'commissionRate',
      key: 'commissionRate',
      render: (rate) => `${(rate * 100).toFixed(2)}%`
    },
    {
      title: '平台佣金',
      dataIndex: 'platformCommission',
      key: 'platformCommission',
      render: (commission) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ¥{commission.toFixed(2)}
        </span>
      )
    },
    {
      title: '商家收入',
      dataIndex: 'merchantIncome',
      key: 'merchantIncome',
      render: (income) => `¥${income.toFixed(2)}`
    },
    {
      title: '结算状态',
      dataIndex: 'settlementStatus',
      key: 'settlementStatus',
      render: (status) => {
        const statusConfig = {
          unsettled: { color: 'orange', text: '未结算' },
          settled: { color: 'green', text: '已结算' }
        };
        const config = statusConfig[status] || statusConfig.unsettled;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '订单时间',
      dataIndex: 'orderTime',
      key: 'orderTime',
      render: (time) => new Date(time).toLocaleString()
    }
  ];

  // 提现记录列配置
  const withdrawalColumns = [
    {
      title: '提现单号',
      dataIndex: 'withdrawalId',
      key: 'withdrawalId',
      width: 150
    },
    {
      title: '商家信息',
      dataIndex: 'merchantInfo',
      key: 'merchantInfo',
      render: (merchant) => (
        <div>
          <div>{merchant.name}</div>
          <div style={{ fontSize: 12, color: '#999' }}>ID: {merchant.id}</div>
        </div>
      )
    },
    {
      title: '提现金额',
      dataIndex: 'withdrawalAmount',
      key: 'withdrawalAmount',
      render: (amount) => (
        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
          ¥{amount.toFixed(2)}
        </span>
      )
    },
    {
      title: '手续费',
      dataIndex: 'fee',
      key: 'fee',
      render: (fee) => `¥${fee.toFixed(2)}`
    },
    {
      title: '到账金额',
      dataIndex: 'actualAmount',
      key: 'actualAmount',
      render: (amount) => `¥${amount.toFixed(2)}`
    },
    {
      title: '银行账户',
      dataIndex: 'bankAccount',
      key: 'bankAccount',
      render: (account) => (
        <div>
          <div>{account.bankName}</div>
          <div style={{ fontSize: 12, color: '#999' }}>
            {account.accountNumber.replace(/(\d{4})\d*(\d{4})/, '$1****$2')}
          </div>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusConfig = {
          pending: { color: 'processing', text: '待审核' },
          approved: { color: 'warning', text: '已审核' },
          processing: { color: 'processing', text: '处理中' },
          completed: { color: 'success', text: '已完成' },
          rejected: { color: 'error', text: '已拒绝' }
        };
        const config = statusConfig[status] || statusConfig.pending;
        return <Badge status={config.color} text={config.text} />;
      }
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      key: 'applyTime',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => viewWithdrawalDetail(record)}>
            查看详情
          </Button>
          {record.status === 'pending' && (
            <>
              <Button size="small" type="primary" onClick={() => approveWithdrawal(record)}>
                审核通过
              </Button>
              <Button size="small" danger onClick={() => rejectWithdrawal(record)}>
                拒绝
              </Button>
            </>
          )}
        </Space>
      )
    }
  ];

  // 收入趋势图配置
  const revenueTrendConfig = {
    data: chartData.revenueTrend || [],
    xField: 'date',
    yField: 'amount',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  // 佣金分布图配置
  const commissionDistributionConfig = {
    data: chartData.commissionDistribution || [],
    xField: 'category',
    yField: 'commission',
    color: '#1890ff',
    columnWidthRatio: 0.8,
  };

  // 事件处理函数
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const viewSettlementDetail = (record) => {
    Modal.info({
      title: `结算详情 - ${record.settlementId}`,
      width: 800,
      content: (
        <div>
          <Row gutter={16}>
            <Col span={12}>
              <p><strong>商家:</strong> {record.merchantInfo.name}</p>
              <p><strong>结算类型:</strong> {record.settlementType}</p>
              <p><strong>结算金额:</strong> ¥{record.settlementAmount.toFixed(2)}</p>
              <p><strong>手续费:</strong> ¥{record.serviceFee.toFixed(2)}</p>
            </Col>
            <Col span={12}>
              <p><strong>实际到账:</strong> ¥{record.actualAmount.toFixed(2)}</p>
              <p><strong>结算周期:</strong> {record.settlementPeriod.start} ~ {record.settlementPeriod.end}</p>
              <p><strong>状态:</strong> {record.status}</p>
              <p><strong>创建时间:</strong> {new Date(record.createTime).toLocaleString()}</p>
            </Col>
          </Row>
          <Divider />
          <h4>结算明细</h4>
          <Table
            size="small"
            dataSource={record.details || []}
            columns={[
              { title: '订单号', dataIndex: 'orderNumber', key: 'orderNumber' },
              { title: '订单金额', dataIndex: 'orderAmount', key: 'orderAmount', render: (amount) => `¥${amount.toFixed(2)}` },
              { title: '佣金', dataIndex: 'commission', key: 'commission', render: (commission) => `¥${commission.toFixed(2)}` }
            ]}
            pagination={false}
          />
        </div>
      ),
    });
  };

  const processSettlement = async (record) => {
    Modal.confirm({
      title: '确认结算',
      content: `确定要执行结算单 ${record.settlementId} 吗？`,
      onOk: async () => {
        try {
          await financeApi.processSettlement(record.settlementId);
          message.success('结算执行成功');
          loadData();
        } catch (error) {
          message.error('结算执行失败');
        }
      },
    });
  };

  const downloadSettlementReport = async (record) => {
    try {
      const response = await financeApi.downloadSettlementReport(record.settlementId);
      // 处理文件下载
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `settlement_${record.settlementId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      message.error('下载失败');
    }
  };

  const viewWithdrawalDetail = (record) => {
    Modal.info({
      title: `提现详情 - ${record.withdrawalId}`,
      width: 600,
      content: (
        <div>
          <p><strong>商家:</strong> {record.merchantInfo.name}</p>
          <p><strong>提现金额:</strong> ¥{record.withdrawalAmount.toFixed(2)}</p>
          <p><strong>手续费:</strong> ¥{record.fee.toFixed(2)}</p>
          <p><strong>到账金额:</strong> ¥{record.actualAmount.toFixed(2)}</p>
          <p><strong>银行:</strong> {record.bankAccount.bankName}</p>
          <p><strong>账户:</strong> {record.bankAccount.accountNumber}</p>
          <p><strong>户名:</strong> {record.bankAccount.accountName}</p>
          <p><strong>申请时间:</strong> {new Date(record.applyTime).toLocaleString()}</p>
          <p><strong>状态:</strong> {record.status}</p>
        </div>
      ),
    });
  };

  const approveWithdrawal = async (record) => {
    Modal.confirm({
      title: '审核通过',
      content: `确定要审核通过提现申请 ${record.withdrawalId} 吗？`,
      onOk: async () => {
        try {
          await financeApi.approveWithdrawal(record.withdrawalId);
          message.success('审核通过');
          loadData();
        } catch (error) {
          message.error('操作失败');
        }
      },
    });
  };

  const rejectWithdrawal = (record) => {
    Modal.confirm({
      title: '拒绝提现',
      content: (
        <div>
          <p>确定要拒绝提现申请 {record.withdrawalId} 吗？</p>
          <TextArea placeholder="请输入拒绝原因" />
        </div>
      ),
      onOk: async () => {
        try {
          await financeApi.rejectWithdrawal(record.withdrawalId, '拒绝原因');
          message.success('已拒绝');
          loadData();
        } catch (error) {
          message.error('操作失败');
        }
      },
    });
  };

  const openSettingModal = () => {
    setSettingModalVisible(true);
  };

  const handleSettingSubmit = async (values) => {
    try {
      await financeApi.updateFinanceSettings(values);
      message.success('设置保存成功');
      setSettingModalVisible(false);
      loadData();
    } catch (error) {
      message.error('设置保存失败');
    }
  };

  const exportFinanceReport = () => {
    message.info('导出功能开发中...');
  };

  const batchSettlement = () => {
    Modal.confirm({
      title: '批量结算',
      content: '确定要执行批量结算吗？这将处理所有待结算的记录。',
      onOk: async () => {
        try {
          await financeApi.batchSettlement();
          message.success('批量结算已启动');
          loadData();
        } catch (error) {
          message.error('批量结算失败');
        }
      },
    });
  };

  return (
    <div className="finance-settlement">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <h2>财务结算管理</h2>
          <p>管理平台收入、佣金结算、商家提现等财务操作</p>
        </div>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={loadData} loading={loading}>
            刷新
          </Button>
          <Button icon={<ExportOutlined />} onClick={exportFinanceReport}>
            导出报告
          </Button>
          <Button type="primary" onClick={batchSettlement}>
            批量结算
          </Button>
          <Button icon={<SettingOutlined />} onClick={openSettingModal}>
            财务设置
          </Button>
        </Space>
      </div>

      {/* 筛选器 */}
      <Card className="filter-card">
        <Space wrap>
          <RangePicker
            placeholder={['开始时间', '结束时间']}
            onChange={(dates) => handleFilterChange('dateRange', dates)}
          />
          <Select
            placeholder="结算类型"
            style={{ width: 120 }}
            value={filters.settlementType}
            onChange={(value) => handleFilterChange('settlementType', value)}
          >
            <Option value="all">全部类型</Option>
            <Option value="daily">日结算</Option>
            <Option value="weekly">周结算</Option>
            <Option value="monthly">月结算</Option>
            <Option value="manual">手动结算</Option>
          </Select>
          <Select
            placeholder="状态"
            style={{ width: 120 }}
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
          >
            <Option value="all">全部状态</Option>
            <Option value="pending">待结算</Option>
            <Option value="processing">结算中</Option>
            <Option value="completed">已完成</Option>
            <Option value="failed">失败</Option>
          </Select>
        </Space>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        {statisticCards.map((card, index) => (
          <Col xs={24} sm={12} md={8} lg={4} key={index}>
            <Card>
              <Statistic
                title={card.title}
                value={card.value}
                prefix={card.prefix}
                suffix={card.suffix}
                precision={card.precision}
                prefix={
                  <span style={{ color: card.color, fontSize: 20 }}>
                    {card.icon}
                  </span>
                }
                valueStyle={{ color: card.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} className="charts-row">
        <Col xs={24} lg={16}>
          <Card title="收入趋势" loading={loading}>
            <Line {...revenueTrendConfig} height={300} />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="佣金分布" loading={loading}>
            <Column {...commissionDistributionConfig} height={300} />
          </Card>
        </Col>
      </Row>

      {/* 详细数据 */}
      <Card>
        <Tabs defaultActiveKey="settlement">
          <TabPane tab="结算记录" key="settlement">
            <Table
              columns={settlementColumns}
              dataSource={settlementRecords}
              rowKey="settlementId"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>
          <TabPane tab="佣金记录" key="commission">
            <Table
              columns={commissionColumns}
              dataSource={commissionRecords}
              rowKey="orderNumber"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>
          <TabPane tab="提现记录" key="withdrawal">
            <Table
              columns={withdrawalColumns}
              dataSource={withdrawalRecords}
              rowKey="withdrawalId"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 财务设置对话框 */}
      <Modal
        title="财务设置"
        visible={settingModalVisible}
        onCancel={() => setSettingModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={settingForm}
          layout="vertical"
          onFinish={handleSettingSubmit}
        >
          <Form.Item
            name="commissionRate"
            label="平台佣金率"
            rules={[{ required: true, message: '请输入平台佣金率' }]}
          >
            <InputNumber min={0} max={100} precision={2} style={{ width: '100%' }} addonAfter="%" />
          </Form.Item>
          
          <Form.Item
            name="withdrawalFeeRate"
            label="提现手续费率"
            rules={[{ required: true, message: '请输入提现手续费率' }]}
          >
            <InputNumber min={0} max={100} precision={2} style={{ width: '100%' }} addonAfter="%" />
          </Form.Item>
          
          <Form.Item
            name="minWithdrawalAmount"
            label="最小提现金额"
            rules={[{ required: true, message: '请输入最小提现金额' }]}
          >
            <InputNumber min={0} precision={2} style={{ width: '100%' }} addonBefore="¥" />
          </Form.Item>
          
          <Form.Item
            name="autoSettlement"
            label="自动结算"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存设置
              </Button>
              <Button onClick={() => setSettingModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FinanceSettlement;
