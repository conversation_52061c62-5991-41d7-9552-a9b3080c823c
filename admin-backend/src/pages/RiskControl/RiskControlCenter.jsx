import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Button,
  Select,
  DatePicker,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  message,
  Tabs,
  Badge,
  Alert,
  Progress,
  Tooltip,
  Divider
} from 'antd';
import {
  ShieldOutlined,
  WarningOutlined,
  UserDeleteOutlined,
  ShopOutlined,
  TransactionOutlined,
  EyeOutlined,
  StopOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import { riskControlApi } from '../../services/riskControl';
import './RiskControlCenter.less';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

const RiskControlCenter = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [riskStats, setRiskStats] = useState({});
  const [riskEvents, setRiskEvents] = useState([]);
  const [riskUsers, setRiskUsers] = useState([]);
  const [riskMerchants, setRiskMerchants] = useState([]);
  const [chartData, setChartData] = useState({});
  const [filters, setFilters] = useState({
    dateRange: null,
    riskLevel: 'all',
    eventType: 'all',
    status: 'all'
  });
  const [ruleModalVisible, setRuleModalVisible] = useState(false);
  const [ruleForm] = Form.useForm();

  // 加载数据
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const [statsRes, eventsRes, usersRes, merchantsRes, chartRes] = await Promise.all([
        riskControlApi.getRiskStats(filters),
        riskControlApi.getRiskEvents(filters),
        riskControlApi.getRiskUsers(filters),
        riskControlApi.getRiskMerchants(filters),
        riskControlApi.getChartData(filters)
      ]);

      setRiskStats(statsRes.data);
      setRiskEvents(eventsRes.data);
      setRiskUsers(usersRes.data);
      setRiskMerchants(merchantsRes.data);
      setChartData(chartRes.data);
    } catch (error) {
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    loadData();
    // 设置定时刷新
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, [loadData]);

  // 统计卡片配置
  const statisticCards = [
    {
      title: '今日风险事件',
      value: riskStats.todayRiskEvents || 0,
      icon: <WarningOutlined />,
      color: '#faad14',
      suffix: '个'
    },
    {
      title: '高风险用户',
      value: riskStats.highRiskUsers || 0,
      icon: <UserDeleteOutlined />,
      color: '#ff4d4f',
      suffix: '个'
    },
    {
      title: '风险商家',
      value: riskStats.riskMerchants || 0,
      icon: <ShopOutlined />,
      color: '#ff7a45',
      suffix: '个'
    },
    {
      title: '异常交易',
      value: riskStats.abnormalTransactions || 0,
      icon: <TransactionOutlined />,
      color: '#722ed1',
      suffix: '笔'
    },
    {
      title: '拦截率',
      value: riskStats.interceptRate || 0,
      icon: <ShieldOutlined />,
      color: '#52c41a',
      suffix: '%'
    },
    {
      title: '误报率',
      value: riskStats.falsePositiveRate || 0,
      icon: <ExclamationCircleOutlined />,
      color: '#1890ff',
      suffix: '%'
    }
  ];

  // 风险事件列表列配置
  const riskEventColumns = [
    {
      title: '事件ID',
      dataIndex: 'eventId',
      key: 'eventId',
      width: 120,
      render: (text) => <span style={{ fontFamily: 'monospace' }}>{text}</span>
    },
    {
      title: '风险类型',
      dataIndex: 'riskType',
      key: 'riskType',
      render: (type) => {
        const typeConfig = {
          fraud: { color: 'red', text: '欺诈风险' },
          abuse: { color: 'orange', text: '滥用风险' },
          security: { color: 'purple', text: '安全风险' },
          compliance: { color: 'blue', text: '合规风险' }
        };
        const config = typeConfig[type] || typeConfig.fraud;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      render: (level) => {
        const levelConfig = {
          high: { color: 'red', text: '高风险' },
          medium: { color: 'orange', text: '中风险' },
          low: { color: 'yellow', text: '低风险' }
        };
        const config = levelConfig[level] || levelConfig.medium;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '涉及对象',
      dataIndex: 'targetInfo',
      key: 'targetInfo',
      render: (target) => (
        <div>
          <div>{target.type === 'user' ? '用户' : '商家'}: {target.name}</div>
          <div style={{ fontSize: 12, color: '#999' }}>ID: {target.id}</div>
        </div>
      )
    },
    {
      title: '风险描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '风险分数',
      dataIndex: 'riskScore',
      key: 'riskScore',
      render: (score) => (
        <Tooltip title={`风险分数: ${score}/100`}>
          <Progress
            percent={score}
            size="small"
            strokeColor={score >= 80 ? '#ff4d4f' : score >= 60 ? '#faad14' : '#52c41a'}
          />
        </Tooltip>
      )
    },
    {
      title: '发生时间',
      dataIndex: 'occurTime',
      key: 'occurTime',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '处理状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusConfig = {
          pending: { color: 'processing', text: '待处理' },
          processing: { color: 'warning', text: '处理中' },
          resolved: { color: 'success', text: '已处理' },
          ignored: { color: 'default', text: '已忽略' }
        };
        const config = statusConfig[status] || statusConfig.pending;
        return <Badge status={config.color} text={config.text} />;
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => viewEventDetail(record)}>
            查看详情
          </Button>
          {record.status === 'pending' && (
            <>
              <Button size="small" type="primary" onClick={() => handleRiskEvent(record, 'block')}>
                拦截
              </Button>
              <Button size="small" onClick={() => handleRiskEvent(record, 'ignore')}>
                忽略
              </Button>
            </>
          )}
        </Space>
      )
    }
  ];

  // 风险用户列表列配置
  const riskUserColumns = [
    {
      title: '用户信息',
      dataIndex: 'userInfo',
      key: 'userInfo',
      render: (user) => (
        <div>
          <div>{user.nickname || user.phone}</div>
          <div style={{ fontSize: 12, color: '#999' }}>ID: {user.userId}</div>
        </div>
      )
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      render: (level) => {
        const levelConfig = {
          high: { color: 'red', text: '高风险' },
          medium: { color: 'orange', text: '中风险' },
          low: { color: 'yellow', text: '低风险' }
        };
        const config = levelConfig[level] || levelConfig.medium;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '风险标签',
      dataIndex: 'riskTags',
      key: 'riskTags',
      render: (tags) => (
        <div>
          {tags.map(tag => (
            <Tag key={tag} size="small" color="red">{tag}</Tag>
          ))}
        </div>
      )
    },
    {
      title: '风险事件数',
      dataIndex: 'riskEventCount',
      key: 'riskEventCount'
    },
    {
      title: '最近风险时间',
      dataIndex: 'lastRiskTime',
      key: 'lastRiskTime',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '账户状态',
      dataIndex: 'accountStatus',
      key: 'accountStatus',
      render: (status) => {
        const statusConfig = {
          normal: { color: 'success', text: '正常' },
          restricted: { color: 'warning', text: '受限' },
          frozen: { color: 'error', text: '冻结' },
          banned: { color: 'default', text: '封禁' }
        };
        const config = statusConfig[status] || statusConfig.normal;
        return <Badge status={config.color} text={config.text} />;
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => viewUserRiskProfile(record)}>
            风险画像
          </Button>
          <Button size="small" onClick={() => manageUserRisk(record)}>
            风险管理
          </Button>
        </Space>
      )
    }
  ];

  // 风险趋势图配置
  const riskTrendConfig = {
    data: chartData.riskTrend || [],
    xField: 'date',
    yField: 'count',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  // 风险类型分布图配置
  const riskTypeConfig = {
    data: chartData.riskTypeDistribution || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [{ type: 'element-active' }],
  };

  // 事件处理函数
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const viewEventDetail = (event) => {
    Modal.info({
      title: `风险事件详情 - ${event.eventId}`,
      width: 800,
      content: (
        <div>
          <p><strong>风险类型:</strong> {event.riskType}</p>
          <p><strong>风险等级:</strong> {event.riskLevel}</p>
          <p><strong>风险分数:</strong> {event.riskScore}/100</p>
          <p><strong>涉及对象:</strong> {event.targetInfo.name} (ID: {event.targetInfo.id})</p>
          <p><strong>风险描述:</strong> {event.description}</p>
          <p><strong>发生时间:</strong> {new Date(event.occurTime).toLocaleString()}</p>
          <Divider />
          <h4>风险详情</h4>
          <pre style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
            {JSON.stringify(event.details, null, 2)}
          </pre>
        </div>
      ),
    });
  };

  const handleRiskEvent = async (event, action) => {
    try {
      await riskControlApi.handleRiskEvent(event.eventId, action);
      message.success(`风险事件${action === 'block' ? '拦截' : '忽略'}成功`);
      loadData();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const viewUserRiskProfile = (user) => {
    // 显示用户风险画像
    Modal.info({
      title: `用户风险画像 - ${user.userInfo.nickname}`,
      width: 800,
      content: (
        <div>
          <Row gutter={16}>
            <Col span={12}>
              <Card size="small" title="基本信息">
                <p>用户ID: {user.userInfo.userId}</p>
                <p>注册时间: {user.userInfo.registerTime}</p>
                <p>最后登录: {user.userInfo.lastLoginTime}</p>
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small" title="风险信息">
                <p>风险等级: {user.riskLevel}</p>
                <p>风险事件数: {user.riskEventCount}</p>
                <p>最近风险时间: {user.lastRiskTime}</p>
              </Card>
            </Col>
          </Row>
          <Card size="small" title="风险标签" style={{ marginTop: 16 }}>
            {user.riskTags.map(tag => (
              <Tag key={tag} color="red">{tag}</Tag>
            ))}
          </Card>
        </div>
      ),
    });
  };

  const manageUserRisk = (user) => {
    // 用户风险管理
    Modal.confirm({
      title: '用户风险管理',
      content: `确定要对用户 ${user.userInfo.nickname} 进行风险管理操作吗？`,
      onOk: async () => {
        try {
          await riskControlApi.manageUserRisk(user.userInfo.userId);
          message.success('风险管理操作成功');
          loadData();
        } catch (error) {
          message.error('操作失败');
        }
      },
    });
  };

  const openRuleConfig = () => {
    setRuleModalVisible(true);
  };

  const handleRuleSubmit = async (values) => {
    try {
      await riskControlApi.updateRiskRules(values);
      message.success('风控规则更新成功');
      setRuleModalVisible(false);
      loadData();
    } catch (error) {
      message.error('风控规则更新失败');
    }
  };

  const exportRiskReport = () => {
    message.info('导出功能开发中...');
  };

  return (
    <div className="risk-control-center">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <h2>风控管理中心</h2>
          <p>实时监控和管理平台风险事件，保障交易安全</p>
        </div>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={loadData} loading={loading}>
            刷新
          </Button>
          <Button icon={<ExportOutlined />} onClick={exportRiskReport}>
            导出报告
          </Button>
          <Button icon={<SettingOutlined />} onClick={openRuleConfig}>
            规则配置
          </Button>
        </Space>
      </div>

      {/* 实时告警 */}
      {riskStats.criticalAlerts > 0 && (
        <Alert
          message={`检测到 ${riskStats.criticalAlerts} 个严重风险事件，请及时处理！`}
          type="error"
          showIcon
          closable
          style={{ marginBottom: 20 }}
        />
      )}

      {/* 筛选器 */}
      <Card className="filter-card">
        <Space wrap>
          <RangePicker
            placeholder={['开始时间', '结束时间']}
            onChange={(dates) => handleFilterChange('dateRange', dates)}
          />
          <Select
            placeholder="风险等级"
            style={{ width: 120 }}
            value={filters.riskLevel}
            onChange={(value) => handleFilterChange('riskLevel', value)}
          >
            <Option value="all">全部等级</Option>
            <Option value="high">高风险</Option>
            <Option value="medium">中风险</Option>
            <Option value="low">低风险</Option>
          </Select>
          <Select
            placeholder="事件类型"
            style={{ width: 120 }}
            value={filters.eventType}
            onChange={(value) => handleFilterChange('eventType', value)}
          >
            <Option value="all">全部类型</Option>
            <Option value="fraud">欺诈风险</Option>
            <Option value="abuse">滥用风险</Option>
            <Option value="security">安全风险</Option>
            <Option value="compliance">合规风险</Option>
          </Select>
          <Select
            placeholder="处理状态"
            style={{ width: 120 }}
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
          >
            <Option value="all">全部状态</Option>
            <Option value="pending">待处理</Option>
            <Option value="processing">处理中</Option>
            <Option value="resolved">已处理</Option>
            <Option value="ignored">已忽略</Option>
          </Select>
        </Space>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        {statisticCards.map((card, index) => (
          <Col xs={24} sm={12} md={8} lg={4} key={index}>
            <Card>
              <Statistic
                title={card.title}
                value={card.value}
                suffix={card.suffix}
                prefix={
                  <span style={{ color: card.color, fontSize: 20 }}>
                    {card.icon}
                  </span>
                }
                valueStyle={{ color: card.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} className="charts-row">
        <Col xs={24} lg={16}>
          <Card title="风险事件趋势" loading={loading}>
            <Line {...riskTrendConfig} height={300} />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="风险类型分布" loading={loading}>
            <Pie {...riskTypeConfig} height={300} />
          </Card>
        </Col>
      </Row>

      {/* 详细数据 */}
      <Card>
        <Tabs defaultActiveKey="events">
          <TabPane tab="风险事件" key="events">
            <Table
              columns={riskEventColumns}
              dataSource={riskEvents}
              rowKey="eventId"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>
          <TabPane tab="风险用户" key="users">
            <Table
              columns={riskUserColumns}
              dataSource={riskUsers}
              rowKey="userId"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>
          <TabPane tab="风险商家" key="merchants">
            {/* 风险商家表格 */}
          </TabPane>
        </Tabs>
      </Card>

      {/* 风控规则配置对话框 */}
      <Modal
        title="风控规则配置"
        visible={ruleModalVisible}
        onCancel={() => setRuleModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={ruleForm}
          layout="vertical"
          onFinish={handleRuleSubmit}
        >
          <Form.Item
            name="fraudThreshold"
            label="欺诈风险阈值"
            rules={[{ required: true, message: '请输入欺诈风险阈值' }]}
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="autoBlock"
            label="自动拦截"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name="alertNotification"
            label="告警通知"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存配置
              </Button>
              <Button onClick={() => setRuleModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RiskControlCenter;
