/**
 * 后台管理系统统一身份认证服务
 * 负责管理员认证、权限验证和会话管理
 */

import axios from 'axios';
import { message } from 'antd';
import { history } from 'umi';

// 认证配置
const AUTH_CONFIG = {
  baseURL: process.env.REACT_APP_AUTH_BASE_URL || 'http://localhost:8081/auth-service',
  timeout: 30000,
  clientId: 'teleshop-admin-web',
};

// 存储键常量
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'teleshop_admin_access_token',
  REFRESH_TOKEN: 'teleshop_admin_refresh_token',
  USER_INFO: 'teleshop_admin_user_info',
  PERMISSIONS: 'teleshop_admin_permissions',
  MENU_CONFIG: 'teleshop_admin_menu_config',
  LAST_ACTIVITY: 'teleshop_admin_last_activity',
};

// 创建认证API客户端
const authClient = axios.create({
  baseURL: AUTH_CONFIG.baseURL,
  timeout: AUTH_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
authClient.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 更新最后活动时间
    updateLastActivity();
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
authClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        await refreshToken();
        const newToken = getAccessToken();
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return authClient(originalRequest);
      } catch (refreshError) {
        // 刷新失败，清除认证信息并跳转登录
        handleAuthFailure();
        return Promise.reject(refreshError);
      }
    }
    
    // 处理其他错误
    if (error.response?.status === 403) {
      message.error('权限不足，无法访问该资源');
    } else if (error.response?.status >= 500) {
      message.error('服务器错误，请稍后重试');
    }
    
    return Promise.reject(error);
  }
);

/**
 * 管理员登录
 * @param {Object} loginData 登录数据
 * @returns {Promise<Object>} 登录结果
 */
export async function adminLogin(loginData) {
  try {
    const response = await authClient.post('/api/v1/auth/login', {
      ...loginData,
      loginType: 'USERNAME',
      clientId: AUTH_CONFIG.clientId,
    });
    
    if (response.data.code === 200) {
      const tokenInfo = response.data.data;
      
      // 验证用户是否有管理员权限
      if (!isAdminUser(tokenInfo.userInfo)) {
        throw new Error('您没有管理员权限');
      }
      
      // 保存认证信息
      await saveAuthData(tokenInfo);
      
      return {
        success: true,
        data: tokenInfo,
        message: '登录成功',
      };
    } else {
      throw new Error(response.data.message || '登录失败');
    }
  } catch (error) {
    console.error('管理员登录失败:', error);
    throw new Error(error.response?.data?.message || error.message || '登录失败');
  }
}

/**
 * 管理员登出
 */
export async function adminLogout() {
  try {
    const token = getAccessToken();
    if (token) {
      await authClient.post('/api/v1/auth/logout');
    }
  } catch (error) {
    console.error('登出请求失败:', error);
  } finally {
    clearAuthData();
    history.push('/login');
  }
}

/**
 * 刷新访问令牌
 */
export async function refreshToken() {
  const refreshTokenValue = getRefreshToken();
  if (!refreshTokenValue) {
    throw new Error('刷新令牌不存在');
  }
  
  try {
    const response = await authClient.post('/api/v1/auth/refresh', {
      refreshToken: refreshTokenValue,
      clientId: AUTH_CONFIG.clientId,
    });
    
    if (response.data.code === 200) {
      const tokenInfo = response.data.data;
      saveTokens(tokenInfo);
      return tokenInfo;
    } else {
      throw new Error(response.data.message || '令牌刷新失败');
    }
  } catch (error) {
    console.error('令牌刷新失败:', error);
    throw error;
  }
}

/**
 * 获取当前管理员信息
 */
export async function getCurrentAdmin() {
  try {
    const response = await authClient.get('/api/v1/auth/me');
    
    if (response.data.code === 200) {
      const userInfo = response.data.data;
      
      // 验证管理员权限
      if (!isAdminUser(userInfo)) {
        throw new Error('用户权限不足');
      }
      
      saveUserInfo(userInfo);
      return userInfo;
    } else {
      throw new Error(response.data.message || '获取用户信息失败');
    }
  } catch (error) {
    console.error('获取管理员信息失败:', error);
    throw error;
  }
}

/**
 * 验证管理员权限
 * @param {string} permission 权限标识
 * @returns {boolean} 是否有权限
 */
export function hasAdminPermission(permission) {
  const permissions = getPermissions();
  if (!permissions || !Array.isArray(permissions)) {
    return false;
  }
  
  // 超级管理员拥有所有权限
  if (permissions.includes('*') || permissions.includes('admin:*')) {
    return true;
  }
  
  return permissions.includes(permission);
}

/**
 * 验证管理员角色
 * @param {string} role 角色名称
 * @returns {boolean} 是否有角色
 */
export function hasAdminRole(role) {
  const userInfo = getUserInfo();
  if (!userInfo || !userInfo.roles) {
    return false;
  }
  
  return userInfo.roles.includes(role);
}

/**
 * 检查是否为超级管理员
 */
export function isSuperAdmin() {
  return hasAdminRole('SUPER_ADMIN') || hasAdminPermission('*');
}

/**
 * 检查是否为系统管理员
 */
export function isSystemAdmin() {
  return hasAdminRole('SYSTEM_ADMIN') || hasAdminRole('ADMIN');
}

/**
 * 获取管理员菜单权限
 */
export function getAdminMenuPermissions() {
  const permissions = getPermissions();
  if (!permissions) {
    return [];
  }
  
  // 过滤出菜单相关权限
  return permissions.filter(permission => 
    permission.startsWith('menu:') || 
    permission.startsWith('page:') ||
    permission === '*'
  );
}

/**
 * 检查会话是否过期
 */
export function isSessionExpired() {
  const lastActivity = getLastActivity();
  if (!lastActivity) {
    return true;
  }
  
  const now = Date.now();
  const sessionTimeout = 8 * 60 * 60 * 1000; // 8小时
  
  return (now - lastActivity) > sessionTimeout;
}

/**
 * 更新最后活动时间
 */
function updateLastActivity() {
  localStorage.setItem(STORAGE_KEYS.LAST_ACTIVITY, Date.now().toString());
}

/**
 * 获取最后活动时间
 */
function getLastActivity() {
  const timeStr = localStorage.getItem(STORAGE_KEYS.LAST_ACTIVITY);
  return timeStr ? parseInt(timeStr, 10) : null;
}

// ============ 存储管理 ============

/**
 * 保存认证数据
 */
async function saveAuthData(tokenInfo) {
  saveTokens(tokenInfo);
  saveUserInfo(tokenInfo.userInfo);
  
  // 保存权限信息
  if (tokenInfo.userInfo && tokenInfo.userInfo.permissions) {
    savePermissions(tokenInfo.userInfo.permissions);
  }
  
  updateLastActivity();
}

/**
 * 保存令牌
 */
function saveTokens(tokenInfo) {
  if (tokenInfo.accessToken) {
    localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, tokenInfo.accessToken);
  }
  if (tokenInfo.refreshToken) {
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokenInfo.refreshToken);
  }
}

/**
 * 获取访问令牌
 */
export function getAccessToken() {
  return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
}

/**
 * 获取刷新令牌
 */
function getRefreshToken() {
  return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
}

/**
 * 保存用户信息
 */
function saveUserInfo(userInfo) {
  if (userInfo) {
    localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
  }
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  const userInfoStr = localStorage.getItem(STORAGE_KEYS.USER_INFO);
  if (userInfoStr) {
    try {
      return JSON.parse(userInfoStr);
    } catch (error) {
      console.error('解析用户信息失败:', error);
      localStorage.removeItem(STORAGE_KEYS.USER_INFO);
      return null;
    }
  }
  return null;
}

/**
 * 保存权限信息
 */
function savePermissions(permissions) {
  if (permissions) {
    localStorage.setItem(STORAGE_KEYS.PERMISSIONS, JSON.stringify(permissions));
  }
}

/**
 * 获取权限信息
 */
export function getPermissions() {
  const permissionsStr = localStorage.getItem(STORAGE_KEYS.PERMISSIONS);
  if (permissionsStr) {
    try {
      return JSON.parse(permissionsStr);
    } catch (error) {
      console.error('解析权限信息失败:', error);
      localStorage.removeItem(STORAGE_KEYS.PERMISSIONS);
      return [];
    }
  }
  return [];
}

/**
 * 检查是否已认证
 */
export function isAuthenticated() {
  const token = getAccessToken();
  const userInfo = getUserInfo();
  
  if (!token || !userInfo) {
    return false;
  }
  
  // 检查会话是否过期
  if (isSessionExpired()) {
    clearAuthData();
    return false;
  }
  
  // 检查是否为管理员用户
  return isAdminUser(userInfo);
}

/**
 * 检查是否为管理员用户
 */
function isAdminUser(userInfo) {
  if (!userInfo) {
    return false;
  }
  
  // 检查用户类型
  if (userInfo.userType === 'ADMIN_USER') {
    return true;
  }
  
  // 检查角色
  if (userInfo.roles && Array.isArray(userInfo.roles)) {
    const adminRoles = ['ADMIN', 'SUPER_ADMIN', 'SYSTEM_ADMIN'];
    return userInfo.roles.some(role => adminRoles.includes(role));
  }
  
  return false;
}

/**
 * 清除认证数据
 */
function clearAuthData() {
  Object.values(STORAGE_KEYS).forEach(key => {
    localStorage.removeItem(key);
  });
}

/**
 * 处理认证失败
 */
function handleAuthFailure() {
  clearAuthData();
  message.error('登录已过期，请重新登录');
  
  // 跳转到登录页
  if (history) {
    history.push('/login');
  } else {
    window.location.href = '/login';
  }
}

/**
 * 初始化认证状态
 */
export async function initAdminAuth() {
  if (!isAuthenticated()) {
    return false;
  }
  
  try {
    // 验证令牌并获取最新用户信息
    await getCurrentAdmin();
    return true;
  } catch (error) {
    console.error('认证初始化失败:', error);
    handleAuthFailure();
    return false;
  }
}

// 导出认证客户端
export { authClient };
