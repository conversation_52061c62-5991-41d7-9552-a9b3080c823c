/**
 * 后台管理系统WebSocket服务
 * 负责管理员实时通信和系统监控
 */

import { message, notification } from 'antd';
import { getUserInfo, getAccessToken } from './authService';

class AdminWebSocketService {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.shouldReconnect = true;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000;
    this.heartbeatInterval = 30000;
    this.heartbeatTimer = null;
    this.reconnectTimer = null;
    
    // 事件监听器
    this.eventListeners = new Map();
    
    // 消息统计
    this.messageStats = {
      sent: 0,
      received: 0,
      errors: 0,
    };
  }
  
  /**
   * 连接WebSocket
   */
  async connect() {
    if (this.isConnected || this.isConnecting) {
      return;
    }
    
    try {
      this.isConnecting = true;
      
      // 获取管理员信息和访问令牌
      const userInfo = getUserInfo();
      const accessToken = getAccessToken();
      
      if (!userInfo || !accessToken) {
        throw new Error('管理员未认证');
      }
      
      // 验证管理员权限
      if (!this.isAdminUser(userInfo)) {
        throw new Error('权限不足，仅限管理员访问');
      }
      
      // 构建WebSocket URL
      const wsUrl = this.buildWebSocketUrl(userInfo.userId, accessToken);
      
      // 创建WebSocket连接
      this.ws = new WebSocket(wsUrl, ['teleshop-admin-protocol']);
      
      // 设置事件监听器
      this.ws.onopen = this.onOpen.bind(this);
      this.ws.onmessage = this.onMessage.bind(this);
      this.ws.onerror = this.onError.bind(this);
      this.ws.onclose = this.onClose.bind(this);
      
      console.log('管理员WebSocket连接中...', wsUrl);
      
    } catch (error) {
      this.isConnecting = false;
      console.error('管理员WebSocket连接失败:', error);
      this.emit('error', error);
      
      if (this.shouldReconnect) {
        this.scheduleReconnect();
      }
      
      throw error;
    }
  }
  
  /**
   * 断开WebSocket连接
   */
  disconnect() {
    this.shouldReconnect = false;
    
    // 清除定时器
    this.clearTimers();
    
    // 关闭WebSocket连接
    if (this.ws) {
      this.ws.close(1000, '管理员主动断开');
      this.ws = null;
    }
    
    // 重置状态
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    this.emit('disconnected');
    console.log('管理员WebSocket连接已断开');
  }
  
  /**
   * 发送系统广播消息
   */
  sendSystemBroadcast(title, content, priority = 'NORMAL') {
    this.sendMessage({
      messageType: 'SYSTEM',
      receiverType: 'BROADCAST',
      title,
      content,
      priority,
    });
  }
  
  /**
   * 发送用户通知
   */
  sendUserNotification(userId, title, content, priority = 'NORMAL') {
    this.sendMessage({
      messageType: 'NOTIFICATION',
      receiverType: 'USER',
      receiverId: userId,
      title,
      content,
      priority,
    });
  }
  
  /**
   * 发送角色通知
   */
  sendRoleNotification(roleName, title, content, priority = 'NORMAL') {
    this.sendMessage({
      messageType: 'NOTIFICATION',
      receiverType: 'ROLE',
      conversationId: roleName,
      title,
      content,
      priority,
    });
  }
  
  /**
   * 发送营销消息
   */
  sendMarketingMessage(userIds, title, content, extraData = {}) {
    userIds.forEach(userId => {
      this.sendMessage({
        messageType: 'MARKETING',
        receiverType: 'USER',
        receiverId: userId,
        title,
        content,
        priority: 'NORMAL',
        extraData,
      });
    });
  }
  
  /**
   * 发送订单处理通知
   */
  sendOrderProcessNotification(orderId, userId, status, content) {
    this.sendMessage({
      messageType: 'ORDER',
      receiverType: 'USER',
      receiverId: userId,
      title: '订单状态更新',
      content,
      priority: 'HIGH',
      extraData: {
        orderId,
        status,
      },
    });
  }
  
  /**
   * 发送支付处理通知
   */
  sendPaymentNotification(paymentId, userId, status, amount, content) {
    this.sendMessage({
      messageType: 'PAYMENT',
      receiverType: 'USER',
      receiverId: userId,
      title: '支付通知',
      content,
      priority: 'HIGH',
      extraData: {
        paymentId,
        status,
        amount,
      },
    });
  }
  
  /**
   * 获取在线用户统计
   */
  requestOnlineUserStats() {
    this.sendMessage({
      messageType: 'SYSTEM',
      subType: 'GET_ONLINE_STATS',
      content: 'request_online_stats',
    });
  }
  
  /**
   * 获取系统状态
   */
  requestSystemStatus() {
    this.sendMessage({
      messageType: 'SYSTEM',
      subType: 'GET_SYSTEM_STATUS',
      content: 'request_system_status',
    });
  }
  
  /**
   * 发送消息
   */
  sendMessage(message) {
    if (!this.isConnected) {
      console.warn('管理员WebSocket未连接，无法发送消息');
      return;
    }
    
    try {
      const messageData = {
        messageId: this.generateMessageId(),
        timestamp: new Date().toISOString(),
        senderType: 'ADMIN',
        ...message,
      };
      
      this.ws.send(JSON.stringify(messageData));
      this.messageStats.sent++;
      
      console.log('管理员WebSocket消息发送成功:', messageData.messageId);
      
    } catch (error) {
      this.messageStats.errors++;
      console.error('管理员WebSocket消息发送失败:', error);
      throw error;
    }
  }
  
  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }
  
  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (!this.eventListeners.has(event)) return;
    
    const listeners = this.eventListeners.get(event);
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }
  
  /**
   * 触发事件
   */
  emit(event, data) {
    if (!this.eventListeners.has(event)) return;
    
    const listeners = this.eventListeners.get(event);
    listeners.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('管理员WebSocket事件监听器执行失败:', error);
      }
    });
  }
  
  /**
   * 获取消息统计
   */
  getMessageStats() {
    return { ...this.messageStats };
  }
  
  /**
   * 重置消息统计
   */
  resetMessageStats() {
    this.messageStats = {
      sent: 0,
      received: 0,
      errors: 0,
    };
  }
  
  /**
   * WebSocket连接打开
   */
  onOpen(event) {
    console.log('管理员WebSocket连接成功');
    
    this.isConnected = true;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    // 启动心跳
    this.startHeartbeat();
    
    this.emit('connected', event);
    
    message.success('管理员实时通信连接成功');
  }
  
  /**
   * 接收WebSocket消息
   */
  onMessage(event) {
    try {
      const messageData = JSON.parse(event.data);
      this.messageStats.received++;
      
      console.log('管理员WebSocket消息接收:', messageData);
      
      // 处理特殊消息类型
      switch (messageData.messageType) {
        case 'HEARTBEAT':
          this.handleHeartbeat(messageData);
          break;
        case 'SYSTEM':
          this.handleSystemMessage(messageData);
          break;
        case 'ERROR':
          this.handleErrorMessage(messageData);
          break;
        case 'CUSTOMER_SERVICE':
          this.handleCustomerServiceMessage(messageData);
          break;
        default:
          this.handleGeneralMessage(messageData);
          break;
      }
      
      this.emit('message', messageData);
      
    } catch (error) {
      this.messageStats.errors++;
      console.error('解析管理员WebSocket消息失败:', error);
    }
  }
  
  /**
   * WebSocket连接错误
   */
  onError(error) {
    console.error('管理员WebSocket连接错误:', error);
    
    this.isConnected = false;
    this.emit('error', error);
    
    message.error('管理员实时通信连接异常');
  }
  
  /**
   * WebSocket连接关闭
   */
  onClose(event) {
    console.log('管理员WebSocket连接关闭:', event.code, event.reason);
    
    this.isConnected = false;
    this.clearTimers();
    
    this.emit('disconnected', event);
    
    // 自动重连
    if (this.shouldReconnect && event.code !== 1000) {
      this.scheduleReconnect();
    }
  }
  
  /**
   * 处理心跳消息
   */
  handleHeartbeat(message) {
    if (message.content === 'ping') {
      this.sendMessage({
        messageType: 'HEARTBEAT',
        content: 'pong',
        relatedMessageId: message.messageId,
      });
    }
  }
  
  /**
   * 处理系统消息
   */
  handleSystemMessage(message) {
    if (message.subType === 'ONLINE_STATS') {
      this.emit('onlineStats', message.extraData);
    } else if (message.subType === 'SYSTEM_STATUS') {
      this.emit('systemStatus', message.extraData);
    } else {
      notification.info({
        message: '系统消息',
        description: message.content,
        duration: 0,
      });
    }
  }
  
  /**
   * 处理错误消息
   */
  handleErrorMessage(message) {
    console.error('收到错误消息:', message.content);
    notification.error({
      message: '系统错误',
      description: message.content,
      duration: 0,
    });
  }
  
  /**
   * 处理客服消息
   */
  handleCustomerServiceMessage(message) {
    notification.warning({
      message: '客服告警',
      description: `有新的客服请求需要处理：${message.content}`,
      duration: 0,
      onClick: () => {
        // 跳转到客服管理页面
        window.location.href = '/customer-service';
      },
    });
  }
  
  /**
   * 处理一般消息
   */
  handleGeneralMessage(message) {
    // 可以根据需要处理其他类型的消息
    this.emit('generalMessage', message);
  }
  
  /**
   * 启动心跳
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.sendMessage({
          messageType: 'HEARTBEAT',
          content: 'ping',
        });
      }
    }, this.heartbeatInterval);
  }
  
  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('管理员WebSocket重连次数超过限制，停止重连');
      message.error('连接失败，请刷新页面重试');
      return;
    }
    
    this.reconnectAttempts++;
    
    this.reconnectTimer = setTimeout(() => {
      console.log(`管理员WebSocket开始第 ${this.reconnectAttempts} 次重连`);
      this.connect();
    }, this.reconnectDelay * this.reconnectAttempts);
  }
  
  /**
   * 清除定时器
   */
  clearTimers() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }
  
  /**
   * 检查是否为管理员用户
   */
  isAdminUser(userInfo) {
    if (!userInfo) return false;
    
    // 检查用户类型
    if (userInfo.userType === 'ADMIN_USER') {
      return true;
    }
    
    // 检查角色
    if (userInfo.roles && Array.isArray(userInfo.roles)) {
      const adminRoles = ['ADMIN', 'SUPER_ADMIN', 'SYSTEM_ADMIN'];
      return userInfo.roles.some(role => adminRoles.includes(role));
    }
    
    return false;
  }
  
  /**
   * 构建WebSocket URL
   */
  buildWebSocketUrl(userId, accessToken) {
    const baseUrl = process.env.REACT_APP_WEBSOCKET_BASE_URL || 'ws://localhost:8082/message-service';
    return `${baseUrl}/websocket/${userId}?token=${accessToken}&client=admin`;
  }
  
  /**
   * 生成消息ID
   */
  generateMessageId() {
    return `admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 创建全局管理员WebSocket服务实例
const adminWebSocketService = new AdminWebSocketService();

export default adminWebSocketService;

// 导出便捷方法
export const connectAdminWebSocket = () => adminWebSocketService.connect();
export const disconnectAdminWebSocket = () => adminWebSocketService.disconnect();
export const sendSystemBroadcast = (title, content, priority) => 
  adminWebSocketService.sendSystemBroadcast(title, content, priority);
export const sendUserNotification = (userId, title, content, priority) => 
  adminWebSocketService.sendUserNotification(userId, title, content, priority);
export const onAdminMessage = (callback) => adminWebSocketService.on('message', callback);
export const onAdminConnected = (callback) => adminWebSocketService.on('connected', callback);
export const onAdminDisconnected = (callback) => adminWebSocketService.on('disconnected', callback);
