<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拼团管理 - 修复版</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <style>
        /* 核心样式 */
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background: #f8fafc; }
        .page-container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .page-header { background: white; padding: 24px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin-bottom: 24px; }
        .page-title { font-size: 24px; font-weight: 600; color: #1e293b; margin: 0 0 8px 0; }
        .page-description { color: #64748b; margin: 0; }
        
        /* 统计卡片 */
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px; }
        .stat-card { background: white; padding: 20px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .stat-number { font-size: 28px; font-weight: 700; color: #1e293b; margin: 0 0 4px 0; }
        .stat-label { color: #64748b; font-size: 14px; margin: 0; }
        
        /* 工具栏 */
        .toolbar { background: white; padding: 16px 24px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); margin-bottom: 24px; display: flex; justify-content: space-between; align-items: center; }
        .toolbar-left { display: flex; gap: 12px; align-items: center; }
        .toolbar-right { display: flex; gap: 12px; align-items: center; }
        
        /* 按钮样式 */
        .btn { display: inline-flex; align-items: center; gap: 8px; padding: 10px 16px; border: none; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; text-decoration: none; transition: all 0.2s; }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-primary:hover { background: #2563eb; }
        .btn-secondary { background: #f1f5f9; color: #475569; border: 1px solid #e2e8f0; }
        .btn-secondary:hover { background: #e2e8f0; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-danger:hover { background: #dc2626; }
        .btn-success { background: #10b981; color: white; }
        .btn-success:hover { background: #059669; }
        
        /* 表格样式 */
        .table-container { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); overflow: hidden; }
        .table { width: 100%; border-collapse: collapse; }
        .table th { background: #f8fafc; padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; }
        .table td { padding: 16px; border-bottom: 1px solid #f3f4f6; }
        .table tbody tr:hover { background: #f9fafb; }
        
        /* 状态徽章 */
        .status-badge { display: inline-flex; align-items: center; gap: 6px; padding: 4px 12px; border-radius: 9999px; font-size: 12px; font-weight: 500; }
        .status-badge.active { background: #ecfdf5; color: #065f46; }
        .status-badge.pending { background: #fef3c7; color: #92400e; }
        .status-badge.ended { background: #f3f4f6; color: #374151; }
        
        /* 操作按钮 */
        .action-btn { display: inline-flex; align-items: center; gap: 4px; padding: 6px 12px; border: none; border-radius: 6px; font-size: 12px; cursor: pointer; text-decoration: none; transition: all 0.2s; }
        .action-btn:hover { transform: translateY(-1px); }
        
        /* Toast 消息 */
        .toast { position: fixed; top: 20px; right: 20px; background: white; border-radius: 8px; box-shadow: 0 10px 25px rgba(0,0,0,0.1); padding: 16px 20px; z-index: 10000; min-width: 300px; }
        .toast.success { border-left: 4px solid #10b981; }
        .toast.error { border-left: 4px solid #ef4444; }
        .toast.info { border-left: 4px solid #3b82f6; }
        
        /* 模态框 */
        .modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: none; align-items: center; justify-content: center; z-index: 10000; }
        .modal.show { display: flex; }
        .modal-content { background: white; border-radius: 12px; padding: 24px; max-width: 500px; width: 90%; max-height: 80vh; overflow-y: auto; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .modal-title { font-size: 18px; font-weight: 600; margin: 0; }
        .modal-close { background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; }
        .modal-footer { display: flex; gap: 12px; justify-content: flex-end; margin-top: 20px; }
        
        /* 表单样式 */
        .form-group { margin-bottom: 16px; }
        .form-label { display: block; margin-bottom: 6px; font-weight: 500; color: #374151; }
        .form-input { width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; }
        .form-input:focus { outline: none; border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59,130,246,0.1); }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .toolbar { flex-direction: column; gap: 12px; }
            .toolbar-left, .toolbar-right { width: 100%; justify-content: center; }
            .stats-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">拼团管理</h1>
            <p class="page-description">管理所有拼团活动，查看统计数据和处理订单</p>
        </div>

        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalActivities">0</div>
                <p class="stat-label">总活动数</p>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeActivities">0</div>
                <p class="stat-label">进行中</p>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="completedActivities">0</div>
                <p class="stat-label">已完成</p>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalRevenue">¥0</div>
                <p class="stat-label">总收入</p>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <button class="btn btn-primary" data-action="create">
                    <span>➕</span> 创建活动
                </button>
                <button class="btn btn-secondary" data-action="export">
                    <span>📊</span> 导出数据
                </button>
                <button class="btn btn-secondary" data-action="refresh">
                    <span>🔄</span> 刷新
                </button>
            </div>
            <div class="toolbar-right">
                <input type="text" class="form-input" placeholder="搜索活动名称或商家..." style="width: 250px;">
                <select class="form-input" style="width: 120px;">
                    <option value="all">全部状态</option>
                    <option value="active">进行中</option>
                    <option value="pending">待开始</option>
                    <option value="ended">已结束</option>
                </select>
            </div>
        </div>

        <!-- 活动列表 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>活动名称</th>
                        <th>商家</th>
                        <th>商品</th>
                        <th>原价/团购价</th>
                        <th>成团人数</th>
                        <th>状态</th>
                        <th>开始时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="activitiesTable">
                    <!-- 示例数据 -->
                    <tr data-id="1">
                        <td><strong>超值拼团活动</strong></td>
                        <td>优选商城</td>
                        <td>iPhone 14 Pro</td>
                        <td>¥8999 / <strong style="color: #ef4444;">¥7999</strong></td>
                        <td>5人成团</td>
                        <td><span class="status-badge active">进行中</span></td>
                        <td>2024-01-15 10:00</td>
                        <td>
                            <button class="action-btn btn-secondary" data-action="view" data-id="1">查看详情</button>
                            <button class="action-btn btn-primary" data-action="edit" data-id="1">编辑</button>
                            <button class="action-btn btn-success" data-action="report" data-id="1">查看报告</button>
                            <button class="action-btn btn-secondary" data-action="copy" data-id="1">复制活动</button>
                            <button class="action-btn btn-danger" data-action="delete" data-id="1">删除</button>
                        </td>
                    </tr>
                    <tr data-id="2">
                        <td><strong>限时团购</strong></td>
                        <td>潮流数码</td>
                        <td>AirPods Pro</td>
                        <td>¥1999 / <strong style="color: #ef4444;">¥1599</strong></td>
                        <td>3人成团</td>
                        <td><span class="status-badge pending">待开始</span></td>
                        <td>2024-01-20 09:00</td>
                        <td>
                            <button class="action-btn btn-secondary" data-action="view" data-id="2">查看详情</button>
                            <button class="action-btn btn-primary" data-action="edit" data-id="2">编辑</button>
                            <button class="action-btn btn-success" data-action="start" data-id="2">启动</button>
                            <button class="action-btn btn-secondary" data-action="copy" data-id="2">复制活动</button>
                            <button class="action-btn btn-danger" data-action="delete" data-id="2">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal" id="viewModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">活动详情</h3>
                <button class="modal-close" onclick="GroupBuyManager.hideModal('viewModal')">✕</button>
            </div>
            <div id="viewModalContent">
                <!-- 详情内容将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 编辑模态框 -->
    <div class="modal" id="editModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">编辑活动</h3>
                <button class="modal-close" onclick="GroupBuyManager.hideModal('editModal')">✕</button>
            </div>
            <form id="editForm">
                <div class="form-group">
                    <label class="form-label">活动名称</label>
                    <input type="text" class="form-input" id="editActivityName" required>
                </div>
                <div class="form-group">
                    <label class="form-label">团购价格</label>
                    <input type="number" class="form-input" id="editGroupPrice" required>
                </div>
                <div class="form-group">
                    <label class="form-label">成团人数</label>
                    <input type="number" class="form-input" id="editGroupSize" required>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="GroupBuyManager.hideModal('editModal')">取消</button>
                    <button type="submit" class="btn btn-primary">保存更改</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 拼团管理器
        const GroupBuyManager = {
            init() {
                console.log('✅ 拼团管理页面初始化开始...');
                
                try {
                    this.bindEvents();
                    this.updateStats();
                    this.initToastContainer();
                    
                    console.log('✅ 拼团管理页面初始化完成！');
                    this.showToast('拼团管理页面加载成功！', 'success');
                    
                } catch (error) {
                    console.error('❌ 初始化失败:', error);
                    this.showToast('页面初始化失败: ' + error.message, 'error');
                }
            },

            bindEvents() {
                // 绑定所有按钮点击事件
                document.addEventListener('click', (e) => {
                    const btn = e.target.closest('[data-action]');
                    if (btn) {
                        e.preventDefault();
                        const action = btn.dataset.action;
                        const id = btn.dataset.id;
                        
                        console.log('🖱️ 按钮点击:', action, id);
                        this.handleAction(action, id, btn);
                    }
                });

                // 绑定表单提交
                const editForm = document.getElementById('editForm');
                if (editForm) {
                    editForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.saveActivity();
                    });
                }

                // 绑定模态框点击外部关闭
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('modal')) {
                        this.hideModal(e.target.id);
                    }
                });
            },

            handleAction(action, id, element) {
                console.log(`🔧 执行操作: ${action}, ID: ${id}`);
                
                switch(action) {
                    case 'view':
                    case 'view-detail':
                        this.viewDetails(id);
                        break;
                    case 'edit':
                        this.editItem(id);
                        break;
                    case 'delete':
                        this.deleteItem(id);
                        break;
                    case 'start':
                        this.startActivity(id);
                        break;
                    case 'copy':
                        this.copyActivity(id);
                        break;
                    case 'report':
                    case 'view-report':
                        this.viewReport(id);
                        break;
                    case 'create':
                        this.createActivity();
                        break;
                    case 'export':
                        this.exportData();
                        break;
                    case 'refresh':
                        this.refreshData();
                        break;
                    default:
                        console.warn('未知操作:', action);
                        this.showToast('未知操作: ' + action, 'error');
                }
            },

            viewDetails(id) {
                console.log('📋 查看详情:', id);
                
                const content = `
                    <div style="line-height: 1.6;">
                        <p><strong>活动ID:</strong> ${id}</p>
                        <p><strong>活动名称:</strong> 超值拼团活动</p>
                        <p><strong>商家:</strong> 优选商城</p>
                        <p><strong>商品:</strong> iPhone 14 Pro</p>
                        <p><strong>原价:</strong> ¥8999</p>
                        <p><strong>团购价:</strong> ¥7999</p>
                        <p><strong>成团人数:</strong> 5人</p>
                        <p><strong>已参团:</strong> 23人</p>
                        <p><strong>已成团:</strong> 4个</p>
                        <p><strong>状态:</strong> <span class="status-badge active">进行中</span></p>
                        <p><strong>开始时间:</strong> 2024-01-15 10:00:00</p>
                        <p><strong>结束时间:</strong> 2024-01-25 10:00:00</p>
                    </div>
                `;
                
                document.getElementById('viewModalContent').innerHTML = content;
                this.showModal('viewModal');
                this.showToast('查看活动详情', 'info');
            },

            editItem(id) {
                console.log('✏️ 编辑活动:', id);
                
                // 填充表单数据
                document.getElementById('editActivityName').value = '超值拼团活动';
                document.getElementById('editGroupPrice').value = '7999';
                document.getElementById('editGroupSize').value = '5';
                
                this.showModal('editModal');
                this.showToast('编辑活动', 'info');
            },

            deleteItem(id) {
                console.log('🗑️ 删除活动:', id);
                
                if (confirm('确定要删除这个拼团活动吗？此操作不可撤销！')) {
                    const row = document.querySelector(`tr[data-id="${id}"]`);
                    if (row) {
                        row.style.transition = 'opacity 0.3s';
                        row.style.opacity = '0';
                        setTimeout(() => {
                            row.remove();
                            this.updateStats();
                        }, 300);
                    }
                    this.showToast('活动已删除', 'success');
                }
            },

            startActivity(id) {
                console.log('🚀 启动活动:', id);
                
                const statusBadge = document.querySelector(`tr[data-id="${id}"] .status-badge`);
                if (statusBadge) {
                    statusBadge.className = 'status-badge active';
                    statusBadge.textContent = '进行中';
                }
                
                this.showToast('活动已启动', 'success');
                this.updateStats();
            },

            copyActivity(id) {
                console.log('📋 复制活动:', id);
                this.showToast('活动已复制到草稿箱', 'success');
            },

            viewReport(id) {
                console.log('📊 查看报告:', id);
                
                const reportData = {
                    activityId: id,
                    totalParticipants: 23,
                    successfulGroups: 4,
                    revenue: 31996,
                    conversionRate: 17.4
                };
                
                const content = `
                    <div style="line-height: 1.6;">
                        <h4 style="margin-top: 0;">活动统计报告</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin: 16px 0;">
                            <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: bold; color: #3b82f6;">${reportData.totalParticipants}</div>
                                <div style="font-size: 12px; color: #64748b;">总参团人数</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: bold; color: #10b981;">${reportData.successfulGroups}</div>
                                <div style="font-size: 12px; color: #64748b;">成功成团</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: bold; color: #f59e0b;">¥${reportData.revenue.toLocaleString()}</div>
                                <div style="font-size: 12px; color: #64748b;">总收入</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: bold; color: #ef4444;">${reportData.conversionRate}%</div>
                                <div style="font-size: 12px; color: #64748b;">转化率</div>
                            </div>
                        </div>
                        <p><strong>详细分析:</strong></p>
                        <ul>
                            <li>活动吸引了 ${reportData.totalParticipants} 人参与</li>
                            <li>成功组成 ${reportData.successfulGroups} 个拼团</li>
                            <li>总收入达到 ¥${reportData.revenue.toLocaleString()}</li>
                            <li>转化率为 ${reportData.conversionRate}%，表现良好</li>
                        </ul>
                    </div>
                `;
                
                document.getElementById('viewModalContent').innerHTML = content;
                this.showModal('viewModal');
                this.showToast('查看活动报告', 'info');
            },

            createActivity() {
                console.log('➕ 创建新活动');
                this.showToast('跳转到创建页面...', 'info');
            },

            exportData() {
                console.log('📊 导出数据');
                this.showToast('正在导出数据...', 'info');
                
                setTimeout(() => {
                    this.showToast('数据导出完成', 'success');
                }, 1500);
            },

            refreshData() {
                console.log('🔄 刷新数据');
                this.showToast('正在刷新数据...', 'info');
                
                setTimeout(() => {
                    this.updateStats();
                    this.showToast('数据已刷新', 'success');
                }, 1000);
            },

            saveActivity() {
                console.log('💾 保存活动');
                
                const name = document.getElementById('editActivityName').value;
                const price = document.getElementById('editGroupPrice').value;
                const size = document.getElementById('editGroupSize').value;
                
                if (!name || !price || !size) {
                    this.showToast('请填写所有必填字段', 'error');
                    return;
                }
                
                this.hideModal('editModal');
                this.showToast('活动已保存', 'success');
            },

            updateStats() {
                // 更新统计数据
                document.getElementById('totalActivities').textContent = '2';
                document.getElementById('activeActivities').textContent = '1';
                document.getElementById('completedActivities').textContent = '0';
                document.getElementById('totalRevenue').textContent = '¥31,996';
            },

            showModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                }
            },

            hideModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.remove('show');
                    document.body.style.overflow = '';
                }
            },

            initToastContainer() {
                if (!document.getElementById('toast-container')) {
                    const container = document.createElement('div');
                    container.id = 'toast-container';
                    container.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10001;';
                    document.body.appendChild(container);
                }
            },

            showToast(message, type = 'info') {
                const container = document.getElementById('toast-container');
                if (!container) return;
                
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                
                const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
                
                toast.innerHTML = `
                    <div style="display: flex; align-items: flex-start; gap: 12px;">
                        <span style="font-size: 16px;">${icon}</span>
                        <div style="flex: 1;">
                            <div style="font-weight: 500; margin-bottom: 2px;">${type === 'success' ? '成功' : type === 'error' ? '错误' : '提示'}</div>
                            <div style="font-size: 14px; color: #64748b;">${message}</div>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer; color: #6b7280;">×</button>
                    </div>
                `;
                
                container.appendChild(toast);
                
                // 3秒后自动移除
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.style.transition = 'opacity 0.3s, transform 0.3s';
                        toast.style.opacity = '0';
                        toast.style.transform = 'translateX(100%)';
                        setTimeout(() => toast.remove(), 300);
                    }
                }, 3000);
            }
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 页面DOM加载完成，开始初始化...');
            
            try {
                // 确保GroupBuyManager对象存在
                if (typeof GroupBuyManager === 'undefined') {
                    throw new Error('GroupBuyManager未定义');
                }
                
                // 初始化页面
                GroupBuyManager.init();
                
                console.log('🎉 页面初始化成功！所有功能已就绪');
                
            } catch (error) {
                console.error('❌ 页面初始化失败:', error);
                
                // 显示错误提示
                document.body.insertAdjacentHTML('afterbegin', `
                    <div style="background: #fef2f2; border: 1px solid #fecaca; border-left: 4px solid #ef4444; color: #991b1b; padding: 16px; margin: 20px; border-radius: 8px;">
                        <div style="font-weight: bold;">⚠️ 页面初始化失败</div>
                        <div style="margin-top: 8px; font-size: 14px;">错误: ${error.message}</div>
                        <button onclick="location.reload()" style="margin-top: 12px; background: #ef4444; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">🔄 重新加载页面</button>
                    </div>
                `);
            }
        });

        // 全局错误处理
        window.addEventListener('error', (e) => {
            console.error('🚨 全局错误:', e.error);
        });

        window.addEventListener('unhandledrejection', (e) => {
            console.error('🚨 未处理的Promise拒绝:', e.reason);
        });
    </script>
</body>
</html> 