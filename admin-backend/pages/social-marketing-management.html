<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社交营销管理 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .social-marketing-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 页面头部增强 */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 32px;
            border-radius: 16px;
            margin-bottom: 24px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 8px 0;
        }

        .page-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }

        .header-stats {
            display: flex;
            gap: 24px;
        }

        .header-stat {
            text-align: center;
            padding: 16px;
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 数据概览面板 */
        .data-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .overview-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .overview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .card-group::before { background: linear-gradient(90deg, #667eea, #764ba2); }
        .card-bargain::before { background: linear-gradient(90deg, #ff6b6b, #feca57); }
        .card-referral::before { background: linear-gradient(90deg, #4ecdc4, #44a08d); }
        .card-share::before { background: linear-gradient(90deg, #a8edea, #fed6e3); }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .card-icon.group { background: linear-gradient(135deg, #667eea, #764ba2); }
        .card-icon.bargain { background: linear-gradient(135deg, #ff6b6b, #feca57); }
        .card-icon.referral { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
        .card-icon.share { background: linear-gradient(135deg, #a8edea, #fed6e3); }

        .card-value {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin: 8px 0;
        }

        .card-label {
            color: #7f8c8d;
            font-size: 14px;
            margin: 0;
        }

        .card-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            margin-top: 8px;
        }

        .trend-up { color: #27ae60; }
        .trend-down { color: #e74c3c; }

        /* 主要内容区域 */
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }

        .content-left {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .content-right {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        /* 拼团管理增强 */
        .group-buy-panel {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .panel-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .panel-actions {
            display: flex;
            gap: 8px;
        }

        /* 标签页增强 */
        .enhanced-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .tab-item {
            flex: 1;
            padding: 12px 16px;
            text-align: center;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            position: relative;
        }

        .tab-item.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 表格增强 */
        .enhanced-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #374151;
        }

        .data-table tbody tr:hover {
            background: #f8f9fa;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active { background: #e8f5e8; color: #2e8b57; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }

        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-action {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .btn-primary { background: #667eea; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }

        .btn-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* 图表容器 */
        .chart-container {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            height: 400px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .social-marketing-page {
                padding: 16px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .header-content {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }
            
            .header-stats {
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .data-overview {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="social-marketing-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title"><i class="fas fa-share-alt"></i> 社交营销管理</h1>
                    <p class="page-subtitle">全面管理拼团、砍价、分享等社交营销活动</p>
                </div>
                <div class="header-stats">
                    <div class="header-stat">
                        <div class="stat-number">48</div>
                        <div class="stat-label">活跃拼团</div>
                    </div>
                    <div class="header-stat">
                        <div class="stat-number">¥156.8万</div>
                        <div class="stat-label">社交销售额</div>
                    </div>
                    <div class="header-stat">
                        <div class="stat-number">89.2%</div>
                        <div class="stat-label">平均成团率</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据概览 -->
        <div class="data-overview">
            <div class="overview-card card-group">
                <div class="card-header">
                    <div class="card-icon group">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="card-value">2,847</div>
                <p class="card-label">总拼团活动</p>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span>本月 +23.5%</span>
                </div>
            </div>
            
            <div class="overview-card card-bargain">
                <div class="card-header">
                    <div class="card-icon bargain">
                        <i class="fas fa-cut"></i>
                    </div>
                </div>
                <div class="card-value">1,562</div>
                <p class="card-label">砍价活动</p>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span>本月 +18.2%</span>
                </div>
            </div>
            
            <div class="overview-card card-referral">
                <div class="card-header">
                    <div class="card-icon referral">
                        <i class="fas fa-gift"></i>
                    </div>
                </div>
                <div class="card-value">8,945</div>
                <p class="card-label">邀请奖励</p>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span>本月 +31.7%</span>
                </div>
            </div>
            
            <div class="overview-card card-share">
                <div class="card-header">
                    <div class="card-icon share">
                        <i class="fas fa-share-square"></i>
                    </div>
                </div>
                <div class="card-value">15,632</div>
                <p class="card-label">分享次数</p>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span>本月 +42.8%</span>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <div class="content-left">
                <!-- 拼团管理面板 -->
                <div class="group-buy-panel">
                    <div class="panel-header">
                        <h3 class="panel-title">拼团活动管理</h3>
                        <div class="panel-actions">
                            <button class="btn-action btn-secondary">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button class="btn-action btn-primary" onclick="createGroupBuy()">
                                <i class="fas fa-plus"></i> 创建拼团
                            </button>
                        </div>
                    </div>
                    
                    <div class="enhanced-tabs">
                        <button class="tab-item active" onclick="switchTab('active')">进行中 (24)</button>
                        <button class="tab-item" onclick="switchTab('pending')">待开始 (8)</button>
                        <button class="tab-item" onclick="switchTab('completed')">已完成 (156)</button>
                        <button class="tab-item" onclick="switchTab('analysis')">数据分析</button>
                    </div>
                    
                    <!-- 活动列表 -->
                    <div class="enhanced-table">
                        <div class="table-header">
                            <h4>活动列表</h4>
                            <div class="filter-actions">
                                <input type="search" placeholder="搜索活动..." class="search-input">
                                <select class="filter-select">
                                    <option>全部状态</option>
                                    <option>进行中</option>
                                    <option>已完成</option>
                                    <option>已暂停</option>
                                </select>
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>活动信息</th>
                                        <th>成团进度</th>
                                        <th>参与人数</th>
                                        <th>成团率</th>
                                        <th>剩余时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <img src="../../assets/images/product-default.jpg" alt="商品" style="width: 48px; height: 48px; border-radius: 8px;">
                                                <div>
                                                    <div style="font-weight: 600;">iPhone 15 Pro 拼团</div>
                                                    <div style="font-size: 12px; color: #666;">3人团 · ¥7,999</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="flex: 1; background: #f0f0f0; height: 8px; border-radius: 4px;">
                                                    <div style="width: 67%; background: #667eea; height: 100%; border-radius: 4px;"></div>
                                                </div>
                                                <span style="font-size: 12px;">67%</span>
                                            </div>
                                        </td>
                                        <td>285人</td>
                                        <td>
                                            <span style="color: #28a745; font-weight: 600;">89.2%</span>
                                        </td>
                                        <td>
                                            <span style="color: #dc3545; font-weight: 600;">12:34:56</span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-active">进行中</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action btn-primary" onclick="viewDetails(1)">详情</button>
                                                <button class="btn-action btn-secondary" onclick="editActivity(1)">编辑</button>
                                                <button class="btn-action btn-danger" onclick="pauseActivity(1)">暂停</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <img src="../../assets/images/product-default.jpg" alt="商品" style="width: 48px; height: 48px; border-radius: 8px;">
                                                <div>
                                                    <div style="font-weight: 600;">AirPods Pro 拼团</div>
                                                    <div style="font-size: 12px; color: #666;">5人团 · ¥1,599</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="flex: 1; background: #f0f0f0; height: 8px; border-radius: 4px;">
                                                    <div style="width: 100%; background: #28a745; height: 100%; border-radius: 4px;"></div>
                                                </div>
                                                <span style="font-size: 12px;">100%</span>
                                            </div>
                                        </td>
                                        <td>542人</td>
                                        <td>
                                            <span style="color: #28a745; font-weight: 600;">95.6%</span>
                                        </td>
                                        <td>
                                            <span style="color: #28a745; font-weight: 600;">已完成</span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-completed">已完成</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action btn-primary" onclick="viewDetails(2)">详情</button>
                                                <button class="btn-action btn-success" onclick="processOrders(2)">处理订单</button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-right">
                <!-- 实时数据图表 -->
                <div class="chart-container">
                    <h4 class="chart-title">拼团数据趋势</h4>
                    <canvas id="groupBuyChart" style="width: 100%; height: 300px;"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签切换功能
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 添加当前活动状态
            event.target.classList.add('active');
            
            console.log('切换到标签:', tabName);
            // 这里可以添加加载不同数据的逻辑
        }

        // 创建拼团活动
        function createGroupBuy() {
            window.location.href = '../merchant-management/group-buy-management.html';
        }

        // 查看详情
        function viewDetails(id) {
            console.log('查看详情:', id);
        }

        // 编辑活动
        function editActivity(id) {
            console.log('编辑活动:', id);
        }

        // 暂停活动
        function pauseActivity(id) {
            if (confirm('确定要暂停这个拼团活动吗？')) {
                console.log('暂停活动:', id);
            }
        }

        // 处理订单
        function processOrders(id) {
            console.log('处理订单:', id);
        }

        // 初始化图表
        function initChart() {
            const canvas = document.getElementById('groupBuyChart');
            const ctx = canvas.getContext('2d');
            
            // 简单的图表绘制示例
            ctx.fillStyle = '#667eea';
            ctx.fillRect(50, 200, 30, 100);
            ctx.fillRect(100, 150, 30, 150);
            ctx.fillRect(150, 100, 30, 200);
            ctx.fillRect(200, 80, 30, 220);
            ctx.fillRect(250, 120, 30, 180);
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('拼团数据趋势图', 50, 30);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            console.log('社交营销管理页面加载完成');
        });
    </script>
</body>
</html> 