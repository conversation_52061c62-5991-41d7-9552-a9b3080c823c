<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营销数据分析 - TeleShop Admin</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .analytics-dashboard {
            padding: 20px;
        }
        
        .analytics-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
        }
        
        .analytics-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .overview-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chart-row {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .analytics-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .analytics-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            color: #666;
            font-size: 14px;
        }
        
        .metric-value {
            font-weight: 600;
            color: #333;
        }
        
        .metric-trend {
            font-size: 12px;
            margin-left: 8px;
        }
        
        .trend-up { color: #28a745; }
        .trend-down { color: #dc3545; }
        
        .chart-placeholder {
            height: 200px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #28a745;
        }
        
        .overview-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .filter-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .top-activities {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-info {
            flex: 1;
        }
        
        .activity-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .activity-type {
            font-size: 12px;
            color: #666;
        }
        
        .activity-stats {
            text-align: right;
        }
        
        .activity-revenue {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 4px;
        }
        
        .activity-conversion {
            font-size: 12px;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="analytics-dashboard">
        <div class="analytics-header">
            <div>
                <h1><i class="fas fa-chart-bar"></i> 营销数据分析</h1>
                <p>全面分析营销活动效果，优化营销策略和ROI</p>
            </div>
            <div style="display: flex; gap: 10px;">
                <button class="btn btn-secondary">
                    <i class="fas fa-download"></i> 导出报告
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i> 自定义报表
                </button>
            </div>
        </div>
        
        <div class="filter-tabs">
            <div class="tab active">今日</div>
            <div class="tab">本周</div>
            <div class="tab">本月</div>
            <div class="tab">本季</div>
            <div class="tab">自定义</div>
        </div>
        
        <div class="analytics-overview">
            <div class="overview-card">
                <h3>营销活动总数</h3>
                <div class="stat-value">156</div>
                <div class="stat-change">较上期 +23</div>
            </div>
            <div class="overview-card">
                <h3>总参与用户</h3>
                <div class="stat-value">48.5K</div>
                <div class="stat-change">较上期 +12.8%</div>
            </div>
            <div class="overview-card">
                <h3>营销投入成本</h3>
                <div class="stat-value">¥89.2K</div>
                <div class="stat-change">较上期 +8.5%</div>
            </div>
            <div class="overview-card">
                <h3>营销ROI</h3>
                <div class="stat-value">3.42</div>
                <div class="stat-change">较上期 +0.38</div>
            </div>
        </div>
        
        <div class="chart-row">
            <div class="chart-container">
                <h3><i class="fas fa-line-chart"></i> 营销收入趋势</h3>
                <div class="chart-placeholder">
                    <i class="fas fa-chart-line" style="font-size: 48px; opacity: 0.3;"></i>
                    <span style="margin-left: 10px;">收入趋势图表区域</span>
                </div>
            </div>
            <div class="chart-container">
                <h3><i class="fas fa-pie-chart"></i> 活动类型分布</h3>
                <div class="chart-placeholder">
                    <i class="fas fa-chart-pie" style="font-size: 48px; opacity: 0.3;"></i>
                    <span style="margin-left: 10px;">饼图区域</span>
                </div>
            </div>
        </div>
        
        <div class="analytics-grid">
            <div class="analytics-card">
                <h3><i class="fas fa-trophy"></i> 热门营销活动</h3>
                <div class="top-activities">
                    <div class="activity-item">
                        <div class="activity-info">
                            <div class="activity-name">双11购物节</div>
                            <div class="activity-type">满减活动</div>
                        </div>
                        <div class="activity-stats">
                            <div class="activity-revenue">¥156,789</div>
                            <div class="activity-conversion">转化率 8.9%</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-info">
                            <div class="activity-name">新品拼团</div>
                            <div class="activity-type">拼团活动</div>
                        </div>
                        <div class="activity-stats">
                            <div class="activity-revenue">¥89,456</div>
                            <div class="activity-conversion">转化率 12.3%</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-info">
                            <div class="activity-name">限时秒杀</div>
                            <div class="activity-type">闪购活动</div>
                        </div>
                        <div class="activity-stats">
                            <div class="activity-revenue">¥67,234</div>
                            <div class="activity-conversion">转化率 15.6%</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-info">
                            <div class="activity-name">会员专享</div>
                            <div class="activity-type">会员营销</div>
                        </div>
                        <div class="activity-stats">
                            <div class="activity-revenue">¥45,678</div>
                            <div class="activity-conversion">转化率 18.2%</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="analytics-card">
                <h3><i class="fas fa-users"></i> 用户参与统计</h3>
                <div class="metric-item">
                    <span class="metric-label">新用户参与</span>
                    <span class="metric-value">
                        15,234
                        <span class="metric-trend trend-up">↑ 12.5%</span>
                    </span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">老用户复购</span>
                    <span class="metric-value">
                        8,967
                        <span class="metric-trend trend-up">↑ 8.3%</span>
                    </span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">分享传播</span>
                    <span class="metric-value">
                        23,456
                        <span class="metric-trend trend-up">↑ 25.8%</span>
                    </span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">平均参与时长</span>
                    <span class="metric-value">
                        4.2分钟
                        <span class="metric-trend trend-down">↓ 0.8%</span>
                    </span>
                </div>
            </div>
            
            <div class="analytics-card">
                <h3><i class="fas fa-money-bill-wave"></i> 收入分析</h3>
                <div class="metric-item">
                    <span class="metric-label">直接销售</span>
                    <span class="metric-value">
                        ¥234,567
                        <span class="metric-trend trend-up">↑ 15.6%</span>
                    </span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">社交裂变</span>
                    <span class="metric-value">
                        ¥89,234
                        <span class="metric-trend trend-up">↑ 32.1%</span>
                    </span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">会员消费</span>
                    <span class="metric-value">
                        ¥156,789
                        <span class="metric-trend trend-up">↑ 18.9%</span>
                    </span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">新零售收入</span>
                    <span class="metric-value">
                        ¥45,678
                        <span class="metric-trend trend-up">↑ 45.3%</span>
                    </span>
                </div>
            </div>
            
            <div class="analytics-card">
                <h3><i class="fas fa-chart-line"></i> 转化漏斗</h3>
                <div class="metric-item">
                    <span class="metric-label">活动曝光</span>
                    <span class="metric-value">
                        1,234,567
                        <span class="metric-trend">100%</span>
                    </span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">点击参与</span>
                    <span class="metric-value">
                        234,567
                        <span class="metric-trend">19.0%</span>
                    </span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">加入购物车</span>
                    <span class="metric-value">
                        45,678
                        <span class="metric-trend">19.5%</span>
                    </span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">完成购买</span>
                    <span class="metric-value">
                        23,456
                        <span class="metric-trend">51.3%</span>
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initAnalyticsData();
            bindTabEvents();
            bindActionEvents();
        });
        
        function initAnalyticsData() {
            console.log('Loading marketing analytics data...');
            // 这里可以加载真实的数据
        }
        
        function bindTabEvents() {
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有活跃状态
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    // 添加当前活跃状态
                    this.classList.add('active');
                    
                    // 根据选择的时间段重新加载数据
                    const period = this.textContent;
                    loadDataByPeriod(period);
                });
            });
        }
        
        function bindActionEvents() {
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.textContent.trim();
                    handleAnalyticsAction(action);
                });
            });
        }
        
        function loadDataByPeriod(period) {
            console.log('Loading data for period:', period);
            // 这里可以根据时间段重新加载数据
        }
        
        function handleAnalyticsAction(action) {
            switch(action) {
                case '导出报告':
                    exportAnalyticsReport();
                    break;
                case '自定义报表':
                    showCustomReportModal();
                    break;
                default:
                    console.log('Analytics action:', action);
            }
        }
        
        function exportAnalyticsReport() {
            alert('导出营销数据报告功能开发中...');
        }
        
        function showCustomReportModal() {
            alert('自定义报表功能开发中...');
        }
    </script>
</body>
</html> 