<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社交营销 - TeleShop Admin</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .social-dashboard {
            padding: 20px;
        }
        
        .social-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
        }
        
        .social-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .tool-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .tool-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 20px;
        }
        
        .tool-icon.group { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .tool-icon.bargain { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .tool-icon.invite { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .tool-icon.distribution { background: linear-gradient(135deg, #ff8a80 0%, #ea4c89 100%); }
        
        .tool-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .tool-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .tool-features {
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
        }
        
        .tool-features li {
            padding: 5px 0;
            color: #555;
            font-size: 13px;
        }
        
        .tool-features li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .tool-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .activity-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-draft { background: #fff3cd; color: #856404; }
        .status-hot { background: #f8d7da; color: #721c24; }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #28a745;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="social-dashboard">
        <div class="social-header">
            <div>
                <h1><i class="fas fa-share-alt"></i> 社交营销管理</h1>
                <p>通过社交裂变和用户互动，扩大品牌影响力和销售转化</p>
            </div>
            <button class="btn btn-primary">
                <i class="fas fa-rocket"></i> 启动营销
            </button>
        </div>
        
        <div class="social-stats">
            <div class="stat-card">
                <h3>拼团参与人数</h3>
                <div class="stat-value">8,642</div>
                <div class="stat-change">较上月 +45.8%</div>
            </div>
            <div class="stat-card">
                <h3>分销推广员</h3>
                <div class="stat-value">1,234</div>
                <div class="stat-change">较上月 +18.9%</div>
            </div>
            <div class="stat-card">
                <h3>邀请转化率</h3>
                <div class="stat-value">15.6%</div>
                <div class="stat-change">较上月 +2.3%</div>
            </div>
            <div class="stat-card">
                <h3>社交订单占比</h3>
                <div class="stat-value">32.8%</div>
                <div class="stat-change">较上月 +5.7%</div>
            </div>
        </div>
        
        <div class="tools-grid">
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon group">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <div class="tool-title">拼团活动</div>
                        <span class="activity-status status-hot">热门功能</span>
                    </div>
                </div>
                <div class="tool-desc">
                    创建多人拼团购买活动，通过社交分享降低商品价格，提高销量和用户粘性。
                </div>
                <ul class="tool-features">
                    <li>多规格拼团商品设置</li>
                    <li>拼团人数和时限配置</li>
                    <li>拼团进度实时显示</li>
                    <li>分享奖励和推广机制</li>
                    <li>拼团失败自动退款</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-cog"></i> 管理拼团
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-chart-bar"></i> 拼团数据
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon bargain">
                        <i class="fas fa-hand-scissors"></i>
                    </div>
                    <div>
                        <div class="tool-title">砍价活动</div>
                        <span class="activity-status status-active">6个进行中</span>
                    </div>
                </div>
                <div class="tool-desc">
                    用户邀请好友帮忙砍价，通过社交互动获得更低价格，增强用户参与度。
                </div>
                <ul class="tool-features">
                    <li>砍价商品和底价设置</li>
                    <li>砍价次数和金额规则</li>
                    <li>好友助力机制</li>
                    <li>砍价进度可视化</li>
                    <li>防刷和风控系统</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-cog"></i> 设置砍价
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-eye"></i> 实时监控
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon invite">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div>
                        <div class="tool-title">邀请好友</div>
                        <span class="activity-status status-active">常驻活动</span>
                    </div>
                </div>
                <div class="tool-desc">
                    设置邀请奖励机制，鼓励用户邀请新用户注册，实现用户增长和留存。
                </div>
                <ul class="tool-features">
                    <li>邀请奖励规则设置</li>
                    <li>新用户注册奖励</li>
                    <li>邀请链接和海报生成</li>
                    <li>邀请关系链追踪</li>
                    <li>奖励发放和统计</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-gift"></i> 设置奖励
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-chart-line"></i> 邀请统计
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon distribution">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div>
                        <div class="tool-title">分销系统</div>
                        <span class="activity-status status-active">多级分销</span>
                    </div>
                </div>
                <div class="tool-desc">
                    建立多级分销体系，让用户成为推广员，通过佣金激励扩大销售渠道。
                </div>
                <ul class="tool-features">
                    <li>多级分销关系管理</li>
                    <li>佣金比例灵活设置</li>
                    <li>推广员等级制度</li>
                    <li>分销海报和素材</li>
                    <li>佣金结算和提现</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary" id="commission-settings-btn">
                        <i class="fas fa-percentage"></i> 佣金设置
                    </button>
                    <button class="btn btn-secondary" id="promoters-management-btn">
                        <i class="fas fa-users-cog"></i> 推广员管理
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initSocialMarketingData();
            bindEventListeners();
        });
        
        function initSocialMarketingData() {
            console.log('Loading social marketing data...');
            loadDistributionStats();
        }
        
        function loadDistributionStats() {
            console.log('Loading distribution system statistics...');
            // 这里可以添加异步加载统计数据的代码
            // 模拟数据
            updateDistributionStats({
                promoters: 1234,
                growth: 18.9,
                orders: 5689,
                commissions: 280000
            });
        }
        
        function updateDistributionStats(data) {
            const statCards = document.querySelectorAll('.stat-card');
            // 更新分销推广员数据卡片
            if (statCards[1]) {
                const valueElement = statCards[1].querySelector('.stat-value');
                const changeElement = statCards[1].querySelector('.stat-change');
                if (valueElement) valueElement.textContent = data.promoters.toLocaleString();
                if (changeElement) changeElement.textContent = `较上月 +${data.growth}%`;
            }
        }
        
        function bindEventListeners() {
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.textContent.trim();
                    handleSocialAction(action, this);
                });
            });
            
            // 特殊按钮直接绑定事件
            if (document.getElementById('commission-settings-btn')) {
                document.getElementById('commission-settings-btn').addEventListener('click', function(e) {
                    e.preventDefault();
                    loadCommissionSettings();
                });
            }
            
            if (document.getElementById('promoters-management-btn')) {
                document.getElementById('promoters-management-btn').addEventListener('click', function(e) {
                    e.preventDefault();
                    loadPromotersManagement();
                });
            }
        }
        
        function handleSocialAction(action, element) {
            switch(action) {
                case '启动营销':
                    showSocialCampaignModal();
                    break;
                case '管理拼团':
                    loadGroupBuyManagement();
                    break;
                case '设置砍价':
                    loadBargainSettings();
                    break;
                case '设置奖励':
                    loadInviteRewards();
                    break;
                case '佣金设置':
                    loadCommissionSettings();
                    break;
                default:
                    console.log('Social action:', action);
            }
        }
        
        function showSocialCampaignModal() {
            alert('启动社交营销活动功能开发中...');
        }
        
        function loadGroupBuyManagement() {
            console.log('跳转到拼团管理页面');
            // 跳转到拼团管理页面
            window.location.href = '../merchant-management/group-buy-management.html';
        }
        
        function loadBargainSettings() {
            console.log('跳转到砍价管理页面');
            // 跳转到砍价管理页面
            window.location.href = '../merchant-management/bargain-management.html';
        }
        
        function loadInviteRewards() {
            alert('邀请好友奖励设置功能开发中...');
        }
        
        function loadCommissionSettings() {
            console.log('跳转到分销系统管理页面');
            // 跳转到分销系统管理页面
            window.location.href = '../merchant-management/distribution-system.html';
        }
        
        function loadPromotersManagement() {
            console.log('跳转到分销推广员管理页面');
            // 跳转到分销系统管理页面并定位到推广员管理选项卡
            window.location.href = '../merchant-management/distribution-system.html?tab=distributors';
        }
    </script>
</body>
</html> 