<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员体系 - TeleShop Admin</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .membership-dashboard {
            padding: 20px;
        }
        
        .membership-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 12px;
            color: white;
        }
        
        .membership-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #f093fb;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .tool-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .tool-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 20px;
        }
        
        .tool-icon.member { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .tool-icon.points { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .tool-icon.checkin { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .tool-icon.gift { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        
        .tool-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .tool-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .tool-features {
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
        }
        
        .tool-features li {
            padding: 5px 0;
            color: #555;
            font-size: 13px;
        }
        
        .tool-features li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .tool-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #f093fb;
            color: white;
        }
        
        .btn-primary:hover {
            background: #e07be0;
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .activity-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-premium { background: #fff3cd; color: #856404; }
        .status-system { background: #d1ecf1; color: #0c5460; }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #f093fb;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #28a745;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .member-levels {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .level-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .level-bronze { background: #cd7f32; color: white; }
        .level-silver { background: #c0c0c0; color: white; }
        .level-gold { background: #ffd700; color: #333; }
        .level-diamond { background: #b9f2ff; color: #333; }
    </style>
</head>
<body>
    <div class="membership-dashboard">
        <div class="membership-header">
            <div>
                <h1><i class="fas fa-crown"></i> 会员体系管理</h1>
                <p>构建完整的会员成长体系，提升用户忠诚度和生命周期价值</p>
            </div>
            <button class="btn btn-primary">
                <i class="fas fa-user-plus"></i> 会员升级
            </button>
        </div>
        
        <div class="membership-stats">
            <div class="stat-card">
                <h3>总会员数</h3>
                <div class="stat-value">46,832</div>
                <div class="stat-change">较上月 +8.5%</div>
            </div>
            <div class="stat-card">
                <h3>付费会员</h3>
                <div class="stat-value">12,456</div>
                <div class="stat-change">较上月 +15.3%</div>
            </div>
            <div class="stat-card">
                <h3>积分流通量</h3>
                <div class="stat-value">2.8M</div>
                <div class="stat-change">较上月 +22.1%</div>
            </div>
            <div class="stat-card">
                <h3>会员贡献占比</h3>
                <div class="stat-value">68.5%</div>
                <div class="stat-change">较上月 +3.2%</div>
            </div>
        </div>
        
        <div class="member-levels">
            <div class="level-badge level-bronze">青铜会员 (28,456)</div>
            <div class="level-badge level-silver">白银会员 (12,234)</div>
            <div class="level-badge level-gold">黄金会员 (5,678)</div>
            <div class="level-badge level-diamond">钻石会员 (464)</div>
        </div>
        
        <div class="tools-grid">
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon member">
                        <i class="fas fa-vip-card"></i>
                    </div>
                    <div>
                        <div class="tool-title">会员营销</div>
                        <span class="activity-status status-premium">VIP专享</span>
                    </div>
                </div>
                <div class="tool-desc">
                    设计多层级会员体系，为不同等级会员提供专属权益和个性化服务。
                </div>
                <ul class="tool-features">
                    <li>多级会员等级设置</li>
                    <li>会员专享价格和折扣</li>
                    <li>生日特权和节日福利</li>
                    <li>会员专属客服通道</li>
                    <li>升级条件和权益配置</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-cog"></i> 会员设置
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-chart-bar"></i> 会员分析
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon points">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div>
                        <div class="tool-title">积分系统</div>
                        <span class="activity-status status-system">核心系统</span>
                    </div>
                </div>
                <div class="tool-desc">
                    建立完整的积分获取、消费和兑换体系，增强用户粘性和活跃度。
                </div>
                <ul class="tool-features">
                    <li>积分获取规则配置</li>
                    <li>积分商城和兑换</li>
                    <li>积分过期和清零规则</li>
                    <li>积分抵扣现金功能</li>
                    <li>积分流水和统计</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-store"></i> 积分商城
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-list-alt"></i> 积分规则
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon checkin">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div>
                        <div class="tool-title">签到有礼</div>
                        <span class="activity-status status-active">每日活跃</span>
                    </div>
                </div>
                <div class="tool-desc">
                    设置每日签到奖励机制，通过连续签到获得递增奖励，提升用户日活。
                </div>
                <ul class="tool-features">
                    <li>每日签到奖励设置</li>
                    <li>连续签到加倍奖励</li>
                    <li>签到日历和进度</li>
                    <li>补签功能和规则</li>
                    <li>签到统计和分析</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-gift"></i> 奖励设置
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-chart-line"></i> 签到统计
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon gift">
                        <i class="fas fa-gift-card"></i>
                    </div>
                    <div>
                        <div class="tool-title">新用户礼包</div>
                        <span class="activity-status status-active">欢迎礼包</span>
                    </div>
                </div>
                <div class="tool-desc">
                    为新注册用户提供专属礼包和优惠，提高新用户转化率和首单转化。
                </div>
                <ul class="tool-features">
                    <li>新人专享优惠券包</li>
                    <li>首单立减和免邮</li>
                    <li>新人任务和奖励</li>
                    <li>注册礼品和积分</li>
                    <li>新用户引导流程</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-box-open"></i> 礼包配置
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-user-check"></i> 转化分析
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initMembershipData();
            bindEventListeners();
        });
        
        function initMembershipData() {
            console.log('Loading membership system data...');
        }
        
        function bindEventListeners() {
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.textContent.trim();
                    handleMembershipAction(action, this);
                });
            });
        }
        
        function handleMembershipAction(action, element) {
            switch(action) {
                case '会员升级':
                    showMemberUpgradeModal();
                    break;
                case '会员设置':
                    loadMemberSettings();
                    break;
                case '积分商城':
                    loadPointsMall();
                    break;
                case '奖励设置':
                    loadCheckinRewards();
                    break;
                case '礼包配置':
                    loadNewcomerPackage();
                    break;
                default:
                    console.log('Membership action:', action);
            }
        }
        
        function showMemberUpgradeModal() {
            alert('会员升级功能开发中...');
        }
        
        function loadMemberSettings() {
            alert('会员设置功能开发中...');
        }
        
        function loadPointsMall() {
            alert('积分商城功能开发中...');
        }
        
        function loadCheckinRewards() {
            alert('签到奖励设置功能开发中...');
        }
        
        function loadNewcomerPackage() {
            alert('新用户礼包配置功能开发中...');
        }
    </script>
</body>
</html> 