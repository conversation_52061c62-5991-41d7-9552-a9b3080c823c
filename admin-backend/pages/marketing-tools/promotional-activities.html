<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>促销活动 - TeleShop Admin</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .marketing-dashboard {
            padding: 20px;
        }
        
        .marketing-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
        }
        
        .marketing-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #28a745;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .tool-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .tool-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 20px;
        }
        
        .tool-icon.coupon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .tool-icon.flash { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .tool-icon.discount { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .tool-icon.festival { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        
        .tool-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .tool-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .tool-features {
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
        }
        
        .tool-features li {
            padding: 5px 0;
            color: #555;
            font-size: 13px;
        }
        
        .tool-features li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .tool-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .activity-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-draft { background: #fff3cd; color: #856404; }
        .status-expired { background: #f8d7da; color: #721c24; }
        
        /* 筛选区域样式 */
        .filter-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }
        
        .search-box {
            position: relative;
            flex: 1;
            min-width: 250px;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px 40px 10px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .search-box input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .search-box i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
        }
        
        .filter-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .filter-controls select {
            padding: 8px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }
        
        .filter-controls select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        /* 活动列表表格样式 */
        .activities-table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-header h3 {
            margin: 0;
            color: #333;
            font-size: 18px;
        }
        
        .table-actions {
            display: flex;
            gap: 10px;
        }
        
        .activities-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .activities-table th,
        .activities-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .activities-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 14px;
        }
        
        .activities-table tbody tr:hover {
            background: #f8fafc;
        }
        
        .activity-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .activity-desc {
            font-size: 13px;
            color: #666;
        }
        
        .activity-type {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .type-discount { background: #e6fffa; color: #00695c; }
        .type-flash-sale { background: #e3f2fd; color: #0d47a1; }
        .type-coupon { background: #fce4ec; color: #880e4f; }
        .type-festival { background: #fff3e0; color: #e65100; }
        
        .activity-metrics {
            font-size: 13px;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .metric-value {
            font-weight: 600;
            color: #667eea;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .action-btn {
            padding: 5px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .action-btn.edit {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .action-btn.delete {
            background: #ffebee;
            color: #d32f2f;
        }
        
        .action-btn.toggle {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .action-btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        /* 批量操作工具栏 */
        .bulk-actions {
            background: #667eea;
            color: white;
            padding: 15px 20px;
            display: none;
            align-items: center;
            justify-content: space-between;
        }
        
        .bulk-actions.show {
            display: flex;
        }
        
        .bulk-info {
            font-size: 14px;
        }
        
        .bulk-buttons {
            display: flex;
            gap: 10px;
        }
        
        .bulk-btn {
            padding: 6px 12px;
            border: 1px solid rgba(255,255,255,0.3);
            background: transparent;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s ease;
        }
        
        .bulk-btn:hover {
            background: rgba(255,255,255,0.1);
        }
        
        /* 分页组件 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: white;
            border-top: 1px solid #e2e8f0;
        }
        
        .pagination-info {
            font-size: 14px;
            color: #666;
        }
        
        .pagination {
            display: flex;
            gap: 5px;
        }
        
        .pagination button {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #4a5568;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s ease;
        }
        
        .pagination button:hover:not(:disabled) {
            background: #f8fafc;
        }
        
        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 模态框样式优化 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .modal-overlay.show {
            display: flex;
        }
        
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 100%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            margin: auto;
        }
        
        .modal-content.modal-sm {
            max-width: 400px;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .modal-header h3 {
            margin: 0;
            color: #333;
            font-size: 18px;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #999;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .modal-close:hover {
            background: #f8fafc;
            color: #666;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 20px;
            border-top: 1px solid #e2e8f0;
        }
        
        /* 表单样式 */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-row.single {
            grid-template-columns: 1fr;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }
        
        .required {
            color: #e53e3e;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .form-group .price-input {
            position: relative;
        }
        
        .form-group .price-input::before {
            content: '¥';
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #718096;
            font-weight: 500;
        }
        
        .form-group .price-input input {
            padding-left: 25px;
        }
        
        .form-group .stock-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 5px;
            font-size: 12px;
            color: #718096;
        }
        
        .form-group .time-hint {
            font-size: 12px;
            color: #718096;
            margin-top: 3px;
        }
        
        /* 移动端适配优化 */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr !important;
                gap: 10px;
            }
            
            .modal-content {
                width: calc(100% - 20px);
                margin: 10px;
                max-height: calc(100vh - 40px);
            }
            
            .modal-body {
                padding: 15px;
            }
            
            .flash-sale-rules {
                padding: 12px;
            }
            
            .flash-sale-rules label {
                padding: 6px 0;
            }
        }
        
        .activity-options {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .activity-options h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        /* 加载指示器 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }
        
        .loading-overlay.show {
            display: flex;
        }
        
        .loading-spinner {
            text-align: center;
            color: #667eea;
        }
        
        .loading-spinner i {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .loading-spinner p {
            margin: 0;
            font-size: 14px;
        }
        
        /* 消息提示 */
        .message-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #48bb78;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            display: none;
            align-items: center;
            gap: 8px;
            z-index: 3000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .message-toast.show {
            display: flex;
            transform: translateX(0);
        }
        
        .message-toast.error {
            background: #e53e3e;
        }
        
        /* 限时抢购管理样式 */
        .flash-sale-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .flash-sales-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .flash-sales-table th,
        .flash-sales-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .flash-sales-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 13px;
        }
        
        .flash-sales-table tr:hover {
            background: #f8fafc;
        }
        
        .flash-sale-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .flash-sale-name {
            font-weight: 600;
            color: #2d3748;
        }
        
        .flash-sale-id {
            font-size: 12px;
            color: #718096;
        }
        
        .product-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .product-thumb {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            object-fit: cover;
        }
        
        .product-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .product-name {
            font-weight: 600;
            color: #2d3748;
            font-size: 14px;
        }
        
        .product-sku {
            font-size: 12px;
            color: #718096;
        }
        
        .price-stock {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .price-comparison {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .original-price {
            text-decoration: line-through;
            color: #718096;
            font-size: 12px;
        }
        
        .flash-price {
            color: #e53e3e;
            font-weight: 600;
        }
        
        .stock-info {
            font-size: 12px;
            color: #666;
        }
        
        .time-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 12px;
        }
        
        .time-range {
            color: #2d3748;
        }
        
        .time-status {
            color: #e53e3e;
            font-weight: 500;
        }
        
        .participation-data {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 12px;
        }
        
        .data-item {
            display: flex;
            justify-content: space-between;
        }
        
        .flash-sale-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-waiting {
            background: #fef5e7;
            color: #d69e2e;
        }
        
        .status-active {
            background: #c6f6d5;
            color: #38a169;
        }
        
        .status-ended {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .status-canceled {
            background: #fed7d7;
            color: #e53e3e;
        }
        
        /* 抢购规则设置样式优化 */
        .flash-sale-rules {
            display: flex;
            flex-direction: column;
            gap: 12px;
            padding: 15px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        
        .flash-sale-rules label {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: #2d3748;
            cursor: pointer;
            padding: 8px 0;
            position: relative;
        }
        
        .flash-sale-rules label:hover {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 4px;
            padding: 8px 10px;
            margin: 0 -10px;
        }
        
        .flash-sale-rules input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin: 0;
            accent-color: #667eea;
        }
        
        /* 确保单列表单行正确显示 */
        .form-row.single .form-group {
            width: 100%;
        }
        
        .form-row.single .flash-sale-rules {
            width: 100%;
            box-sizing: border-box;
        }
        
        /* 抢购规则设置容器优化 */
        .form-row.single .form-group:has(.flash-sale-rules) {
            margin-bottom: 20px;
        }
        
        .form-row.single .form-group label:first-child {
            margin-bottom: 10px;
            font-weight: 600;
            color: #2d3748;
        }
        
        /* 实时监控样式 */
        .monitor-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .monitor-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .monitor-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 20px;
        }
        
        .monitor-info {
            flex: 1;
        }
        
        .monitor-label {
            font-size: 12px;
            color: #718096;
            margin-bottom: 5px;
        }
        
        .monitor-value {
            font-size: 24px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .monitor-change {
            font-size: 12px;
            color: #48bb78;
        }
        
        .monitor-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .live-activities {
            margin-bottom: 30px;
        }
        
        .live-activities h4 {
            margin-bottom: 15px;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .live-activities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .live-activity-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        
        .live-activity-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .activity-title {
            font-weight: 600;
            color: #2d3748;
        }
        
        .activity-status-badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .live-data {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 10px;
        }
        
        .live-data-item {
            text-align: center;
            padding: 8px;
            background: #f8fafc;
            border-radius: 4px;
        }
        
        .data-value {
            font-weight: 600;
            color: #2d3748;
        }
        
        .data-label {
            font-size: 11px;
            color: #718096;
        }
        
        .monitor-charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .chart-container h4 {
            margin-bottom: 15px;
            color: #2d3748;
        }
        
        .chart-placeholder {
            height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
            border-radius: 8px;
            color: #718096;
        }
        
        .chart-placeholder i {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .alert-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .alert-panel h4 {
            margin-bottom: 15px;
        }
        
        /* 批量管理样式 */
        .batch-info {
            margin-bottom: 30px;
        }
        
        .selected-count {
            background: #e6f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: #0066cc;
            margin-bottom: 20px;
        }
        
        .batch-actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .batch-action-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .batch-action-card:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
        }
        
        .batch-action-card.warning {
            border-color: #fed7d7;
        }
        
        .batch-action-card.warning:hover {
            border-color: #e53e3e;
            box-shadow: 0 4px 12px rgba(229, 62, 62, 0.15);
        }
        
        .action-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .action-icon.active {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }
        
        .action-icon.paused {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }
        
        .action-icon.stock {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        }
        
        .action-icon.time {
            background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
        }
        
        .action-icon.copy {
            background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
        }
        
        .action-icon.delete {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
        }
        
        .action-info h4 {
            margin: 0 0 5px 0;
            color: #2d3748;
            font-size: 16px;
        }
        
        .action-info p {
            margin: 0;
            color: #718096;
            font-size: 14px;
        }
        
        .selected-activities {
            border-top: 1px solid #e2e8f0;
            padding-top: 20px;
        }
        
        .selected-activities h4 {
            margin-bottom: 15px;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .activities-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            margin-bottom: 8px;
        }
        
        .activity-basic-info {
            flex: 1;
        }
        
        .activity-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .activity-meta {
            font-size: 12px;
            color: #718096;
            display: flex;
            gap: 15px;
        }
        
        .activity-controls {
            display: flex;
            gap: 8px;
        }
        
        .activity-controls .btn {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .alert-panel h4 {
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
        }
        
        .alert-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .alert-item {
            padding: 12px;
            border-left: 4px solid #f56565;
            background: #fed7d7;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .alert-content {
            flex: 1;
        }
        
        .alert-title {
            font-weight: 600;
            color: #c53030;
            margin-bottom: 4px;
        }
        
        .alert-description {
            font-size: 12px;
            color: #e53e3e;
        }
        
        .alert-time {
            font-size: 11px;
            color: #c53030;
        }

        /* 优惠券管理样式 */
        .coupon-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .toolbar-left {
            display: flex;
            gap: 10px;
        }
        
        .toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .coupon-list {
            margin-bottom: 20px;
        }
        
        .coupons-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .coupons-table th,
        .coupons-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .coupons-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 13px;
        }
        
        .coupons-table tr:hover {
            background: #f8fafc;
        }
        
        .coupon-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .coupon-name {
            font-weight: 600;
            color: #2d3748;
        }
        
        .coupon-id {
            font-size: 12px;
            color: #718096;
        }
        
        .coupon-type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .coupon-type-amount { background: #bee3f8; color: #2b6cb0; }
        .coupon-type-discount { background: #c6f6d5; color: #276749; }
        .coupon-type-shipping { background: #fbb6ce; color: #97266d; }
        .coupon-type-gift { background: #faf089; color: #744210; }
        
        .coupon-value {
            font-weight: 600;
            color: #e53e3e;
        }
        
        .coupon-condition {
            font-size: 12px;
            color: #666;
        }
        
        .usage-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
            font-size: 12px;
        }
        
        .usage-issued {
            color: #4a5568;
        }
        
        .usage-used {
            color: #38a169;
        }
        
        .coupon-date {
            font-size: 12px;
            color: #666;
        }
        
        .coupon-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .coupon-status-active { background: #d4edda; color: #155724; }
        .coupon-status-draft { background: #fff3cd; color: #856404; }
        .coupon-status-expired { background: #f8d7da; color: #721c24; }
        .coupon-status-used-up { background: #e2e8f0; color: #4a5568; }
        
        /* 统计查看样式 */
        .stats-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .time-range-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .time-range-selector label {
            font-weight: 500;
            color: #4a5568;
        }
        
        .time-range-selector select,
        .time-range-selector input {
            padding: 6px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .stats-actions {
            display: flex;
            gap: 10px;
        }
        
        .statistics-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .statistics-overview .stat-card {
            display: flex;
            align-items: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 24px;
        }
        
        .stat-content h4 {
            margin: 0 0 8px 0;
            color: #4a5568;
            font-size: 14px;
            font-weight: 500;
        }
        
        .stat-content .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .stat-content .stat-change {
            font-size: 13px;
            font-weight: 500;
        }
        
        .stat-change.positive {
            color: #38a169;
        }
        
        .stat-change.negative {
            color: #e53e3e;
        }
        
        .charts-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-header h3 {
            margin: 0;
            color: #2d3748;
            font-size: 16px;
        }
        
        .chart-controls select {
            padding: 6px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .chart-content {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
            border-radius: 8px;
            color: #666;
        }
        
        .detailed-stats {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .stats-table th,
        .stats-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .stats-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 13px;
        }
        
        .stats-table tr:hover {
            background: #f8fafc;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .charts-container {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .coupon-toolbar,
            .stats-toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .toolbar-right,
            .stats-actions {
                justify-content: center;
            }
            
            .statistics-overview {
                grid-template-columns: 1fr;
            }
            
            .modal-content {
                margin: 10px;
            }
            
            .coupons-table,
            .stats-table {
                font-size: 12px;
            }
            
            .coupons-table th,
            .coupons-table td,
            .stats-table th,
            .stats-table td {
                padding: 8px;
            }
        }
        
        .message-toast.warning {
            background: #ed8936;
        }
        
        .message-toast.info {
            background: #3182ce;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .marketing-dashboard {
                padding: 15px;
            }
            
            .marketing-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .filter-section {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-controls {
                justify-content: stretch;
            }
            
            .filter-controls select {
                flex: 1;
            }
            
            .activities-table-container {
                overflow-x: auto;
            }
            
            .activities-table {
                min-width: 800px;
            }
            
            .bulk-actions {
                flex-direction: column;
                gap: 10px;
            }
            
            .bulk-buttons {
                justify-content: center;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .modal-content {
                width: 95%;
                margin: 10px;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .marketing-stats {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
        
        @media (max-width: 480px) {
            .marketing-header h1 {
                font-size: 20px;
            }
            
            .stat-card {
                padding: 15px;
            }
            
            .stat-value {
                font-size: 20px;
            }
            
            .tool-card {
                padding: 15px;
            }
            
            .pagination-container {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
        
        /* =============== 满减活动管理样式 =============== */
        
        .discount-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .discount-toolbar .toolbar-left {
            display: flex;
            gap: 10px;
        }
        
        .discount-toolbar .toolbar-right {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .discount-toolbar .search-box {
            position: relative;
            min-width: 250px;
        }
        
        .discount-toolbar .search-box input {
            width: 100%;
            padding: 8px 35px 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .discount-toolbar .search-box i {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        .discount-toolbar select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            min-width: 120px;
        }
        
        .discount-list {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .discount-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .discount-table th {
            background: #f8f9fa;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            font-size: 14px;
        }
        
        .discount-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
        }
        
        .discount-table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .discount-activity-info h4 {
            margin: 0 0 5px 0;
            color: #212529;
            font-size: 16px;
            font-weight: 600;
        }
        
        .discount-activity-info p {
            margin: 0;
            color: #6c757d;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .discount-activity-info .activity-id {
            color: #007bff;
            font-family: monospace;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .discount-rules {
            background: #f8f9fa;
            padding: 10px 12px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
        }
        
        .discount-rule-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
            font-size: 13px;
        }
        
        .discount-rule-item:last-child {
            margin-bottom: 0;
        }
        
        .discount-rule-item i {
            color: #28a745;
            width: 16px;
        }
        
        .discount-scope {
            font-size: 13px;
            color: #495057;
        }
        
        .discount-scope .scope-type {
            display: inline-block;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .discount-scope .scope-details {
            color: #6c757d;
            line-height: 1.4;
        }
        
        .discount-participation {
            text-align: center;
        }
        
        .discount-participation .participation-stat {
            display: block;
            margin-bottom: 5px;
            font-size: 13px;
        }
        
        .discount-participation .stat-value {
            font-weight: 600;
            color: #007bff;
        }
        
        .discount-participation .stat-label {
            color: #6c757d;
        }
        
        .discount-participation .conversion-rate {
            background: #fff3cd;
            color: #856404;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .discount-time-info {
            font-size: 13px;
            line-height: 1.4;
        }
        
        .discount-time-info .time-label {
            color: #6c757d;
            display: block;
            margin-bottom: 3px;
        }
        
        .discount-time-info .time-value {
            color: #495057;
            font-weight: 500;
        }
        
        .discount-time-info .duration {
            color: #17a2b8;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .discount-status {
            text-align: center;
        }
        
        .discount-status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .discount-status-badge.active {
            background: #d4edda;
            color: #155724;
        }
        
        .discount-status-badge.scheduled {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .discount-status-badge.paused {
            background: #fff3cd;
            color: #856404;
        }
        
        .discount-status-badge.ended {
            background: #f8d7da;
            color: #721c24;
        }
        
        .discount-status-badge.draft {
            background: #e2e3e5;
            color: #383d41;
        }
        
        .discount-actions {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
        
        .discount-actions .action-btn {
            padding: 6px 10px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .discount-actions .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .discount-actions .action-btn.primary {
            background: #007bff;
            color: white;
        }
        
        .discount-actions .action-btn.success {
            background: #28a745;
            color: white;
        }
        
        .discount-actions .action-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .discount-actions .action-btn.danger {
            background: #dc3545;
            color: white;
        }
        
        .discount-actions .action-btn.secondary {
            background: #6c757d;
            color: white;
        }
        
        /* =============== 满减活动实时监控样式 =============== */
        
        .monitor-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .monitor-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: transform 0.2s;
        }
        
        .monitor-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .monitor-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }
        
        .monitor-info {
            flex: 1;
        }
        
        .monitor-label {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .monitor-value {
            font-size: 24px;
            font-weight: 700;
            color: #212529;
            margin-bottom: 5px;
        }
        
        .monitor-change {
            font-size: 12px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .monitor-change.positive {
            background: #d4edda;
            color: #155724;
        }
        
        .monitor-change.negative {
            background: #f8d7da;
            color: #721c24;
        }
        
        .monitor-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .live-activities {
            margin-bottom: 30px;
        }
        
        .live-activities h4 {
            margin-bottom: 15px;
            color: #212529;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .live-activities h4 i {
            color: #ff6b35;
        }
        
        .live-activities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .live-activity-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: box-shadow 0.2s;
        }
        
        .live-activity-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .live-activity-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .live-activity-title {
            font-weight: 600;
            color: #212529;
            font-size: 14px;
        }
        
        .live-activity-status {
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .live-activity-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 13px;
        }
        
        .live-activity-stats .stat-item {
            text-align: center;
        }
        
        .live-activity-stats .stat-value {
            font-weight: 600;
            color: #007bff;
            display: block;
        }
        
        .live-activity-stats .stat-label {
            color: #6c757d;
            font-size: 11px;
        }
        
        .live-activity-progress {
            background: #e9ecef;
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .live-activity-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        
        .monitor-charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .chart-container h4 {
            margin-bottom: 15px;
            color: #212529;
            font-size: 16px;
        }
        
        .chart-placeholder {
            height: 200px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #6c757d;
        }
        
        .chart-placeholder i {
            font-size: 32px;
            margin-bottom: 10px;
            color: #007bff;
        }
        
        .chart-placeholder p {
            margin: 0 0 5px 0;
            font-weight: 500;
        }
        
        .chart-placeholder small {
            font-size: 12px;
        }
        
        .alert-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }
        
        .alert-panel h4 {
            margin-bottom: 15px;
            color: #212529;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .alert-panel h4 i {
            color: #ffc107;
        }
        
        .alert-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .alert-item {
            padding: 12px;
            margin-bottom: 10px;
            border-radius: 6px;
            border-left: 4px solid;
            font-size: 14px;
        }
        
        .alert-item.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .alert-item.danger {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .alert-item.info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .alert-item .alert-time {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        /* =============== 创建满减活动表单样式 =============== */
        
        .discount-rules {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        
        .rule-placeholder {
            text-align: center;
            padding: 30px;
            color: #6c757d;
        }
        
        .rule-placeholder i {
            font-size: 32px;
            margin-bottom: 10px;
            color: #007bff;
        }
        
        .discount-rule-editor {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .discount-rule-editor:last-child {
            margin-bottom: 0;
        }
        
        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .rule-title {
            font-weight: 600;
            color: #212529;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .rule-actions button {
            padding: 4px 8px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 5px;
        }
        
        .rule-actions .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .rule-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            align-items: end;
        }
        
        .rule-form .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }
        
        .rule-form .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .add-rule-btn {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }
        
        .add-rule-btn:hover {
            background: #0056b3;
        }
        
        .discount-scope {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(210px, 1fr));
            gap: 18px;
            padding: 18px 15px 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            font-size: 14px;
            color: #495057;
            margin-top: 4px;
        }
        .scope-option {
            display: flex;
            align-items: center;
            gap: 10px;
            background: #fff;
            border-radius: 6px;
            padding: 7px 10px;
            border: 1px solid #e9ecef;
            transition: box-shadow 0.2s, border 0.2s;
        }
        .scope-option label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
            color: #495057;
            font-weight: 500;
        }
        .scope-option label:hover {
            color: #007bff;
        }
        .scope-option input[type="radio"],
        .scope-option input[type="checkbox"] {
            margin: 0 6px 0 0;
            accent-color: #007bff;
            width: 17px;
            height: 17px;
        }
        .scope-details {
            margin-top: 10px;
            padding: 12px 10px;
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            color: #6c757d;
            line-height: 1.6;
            font-size: 13.5px;
        }
        .scope-selector {
            position: relative;
        }
        .scope-search {
            width: 100%;
            padding: 8px 12px;
            border: 1.5px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .scope-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            background: #fff;
        }
        .scope-item {
            padding: 8px 12px;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }
        .scope-item:hover {
            background: #f1f3f5;
        }
        .scope-item input[type="checkbox"] {
            margin: 0 6px 0 0;
            accent-color: #007bff;
        }
        @media (max-width: 768px) {
            .discount-scope {
                grid-template-columns: 1fr;
                gap: 10px;
                padding: 10px 4px 6px 4px;
                border-radius: 7px;
            }
            .scope-option {
                padding: 6px 6px;
                font-size: 13px;
            }
            .scope-details {
                padding: 8px 6px;
                font-size: 12.5px;
            }
        }
        
        .discount-advanced-settings {
            display: flex;
            flex-direction: column;
            gap: 14px;
            padding: 18px 15px 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin-top: 4px;
        }
        .discount-advanced-settings label {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            font-size: 15px;
            color: #495057;
            padding: 4px 0;
            border-radius: 4px;
            transition: background 0.2s;
        }
        .discount-advanced-settings label:hover {
            background: #e9ecef;
        }
        .discount-advanced-settings input[type="checkbox"] {
            margin: 0 6px 0 0;
            accent-color: #007bff;
            width: 18px;
            height: 18px;
        }
        @media (max-width: 768px) {
            .discount-advanced-settings {
                padding: 10px 6px 6px 6px;
                gap: 10px;
            }
            .discount-advanced-settings label {
                font-size: 14px;
                gap: 8px;
            }
        }
        /* 分组样式（如有多组可扩展） */
        .discount-advanced-settings .settings-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        /* =============== 响应式适配 =============== */
        
        @media (max-width: 768px) {
            .discount-toolbar {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .discount-toolbar .toolbar-left,
            .discount-toolbar .toolbar-right {
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .discount-toolbar .search-box {
                min-width: auto;
                flex: 1;
            }
            
            .monitor-overview {
                grid-template-columns: 1fr;
            }
            
            .monitor-charts {
                grid-template-columns: 1fr;
            }
            
            .live-activities-grid {
                grid-template-columns: 1fr;
            }
            
            .discount-table {
                font-size: 12px;
            }
            
            .discount-table th,
            .discount-table td {
                padding: 8px 6px;
            }
            
            .discount-actions {
                flex-direction: column;
                gap: 5px;
            }
            
            .discount-actions .action-btn {
                width: 100%;
                justify-content: center;
            }
            
            .rule-form {
                grid-template-columns: 1fr;
            }
            
                         .discount-scope {
                 grid-template-columns: 1fr;
             }
         }
         
         /* =============== 批量操作模态框样式 =============== */
         
         .batch-options {
             display: flex;
             flex-direction: column;
             gap: 25px;
         }
         
         .batch-section {
             padding: 20px;
             background: #f8f9fa;
             border-radius: 8px;
             border: 1px solid #e9ecef;
         }
         
         .batch-section h4 {
             margin: 0 0 15px 0;
             color: #212529;
             font-size: 16px;
             font-weight: 600;
             display: flex;
             align-items: center;
             gap: 8px;
         }
         
         .batch-section h4 i {
             color: #007bff;
         }
         
         .batch-buttons {
             display: flex;
             gap: 10px;
             flex-wrap: wrap;
         }
         
         .batch-buttons .btn {
             padding: 8px 16px;
             font-size: 14px;
             border-radius: 5px;
             border: none;
             cursor: pointer;
             transition: all 0.2s;
             display: flex;
             align-items: center;
             gap: 6px;
         }
         
         .batch-buttons .btn:hover {
             transform: translateY(-1px);
             box-shadow: 0 2px 4px rgba(0,0,0,0.2);
         }
         
         .batch-edit-form {
             background: white;
             padding: 15px;
             border-radius: 6px;
             border: 1px solid #dee2e6;
         }
         
         .batch-edit-form .form-row {
             display: grid;
             grid-template-columns: 1fr 1fr;
             gap: 15px;
             margin-bottom: 15px;
         }
         
         .batch-edit-form .form-group {
             display: flex;
             flex-direction: column;
             gap: 8px;
         }
         
         .batch-edit-form .form-group label {
             font-weight: 500;
             color: #495057;
             font-size: 14px;
         }
         
         .batch-edit-form .form-group input,
         .batch-edit-form .form-group select {
             padding: 8px 12px;
             border: 1px solid #ced4da;
             border-radius: 4px;
             font-size: 14px;
         }
         
         .batch-edit-form .btn {
             padding: 6px 12px;
             font-size: 12px;
             margin-top: 8px;
         }
         
         .discount-adjust {
             display: flex;
             gap: 8px;
             align-items: center;
         }
         
         .discount-adjust select {
             min-width: 100px;
         }
         
         .discount-adjust input {
             flex: 1;
         }
         
         .batch-copy-form {
             background: white;
             padding: 15px;
             border-radius: 6px;
             border: 1px solid #dee2e6;
         }
         
         .copy-options {
             display: flex;
             flex-direction: column;
             gap: 8px;
             margin-bottom: 15px;
         }
         
         .copy-options label {
             display: flex;
             align-items: center;
             gap: 8px;
             font-size: 14px;
             color: #495057;
             cursor: pointer;
         }
         
         .copy-options input[type="checkbox"] {
             margin: 0;
         }
         
         .export-options {
             background: white;
             padding: 15px;
             border-radius: 6px;
             border: 1px solid #dee2e6;
         }
         
         .export-format {
             margin-bottom: 15px;
         }
         
         .export-format label {
             font-weight: 500;
             color: #495057;
             margin-bottom: 5px;
             display: block;
         }
         
         .export-format select {
             width: 100%;
             padding: 8px 12px;
             border: 1px solid #ced4da;
             border-radius: 4px;
             font-size: 14px;
         }
         
         .export-fields {
             margin-bottom: 15px;
         }
         
         .export-fields label {
             font-weight: 500;
             color: #495057;
             margin-bottom: 10px;
             display: block;
         }
         
         .field-checkboxes {
             display: grid;
             grid-template-columns: 1fr 1fr;
             gap: 8px;
         }
         
         .field-checkboxes label {
             display: flex;
             align-items: center;
             gap: 8px;
             font-size: 14px;
             color: #495057;
             cursor: pointer;
             font-weight: normal;
         }
         
         .field-checkboxes input[type="checkbox"] {
             margin: 0;
         }
         
         .batch-selected-info {
             margin-top: 20px;
             padding: 15px;
             background: #e9ecef;
             border-radius: 6px;
             display: flex;
             justify-content: space-between;
             align-items: center;
         }
         
         .selected-count {
             font-weight: 600;
             color: #495057;
         }
         
         .batch-actions {
             display: flex;
             gap: 10px;
         }
         
         .batch-actions .btn {
             padding: 6px 12px;
             font-size: 14px;
         }
         
         @media (max-width: 768px) {
             .batch-edit-form .form-row {
                 grid-template-columns: 1fr;
             }
             
             .field-checkboxes {
                 grid-template-columns: 1fr;
             }
             
             .batch-buttons {
                 flex-direction: column;
             }
             
             .batch-selected-info {
                 flex-direction: column;
                 gap: 10px;
                 text-align: center;
             }
             
             .batch-actions {
                 justify-content: center;
             }
         }
    </style>
</head>
<body>
    <div class="marketing-dashboard">
        <div class="marketing-header">
            <div>
                <h1><i class="fas fa-tags"></i> 促销活动管理</h1>
                <p>管理各类促销活动，提升商品销量和用户参与度</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="showBatchActions()">
                    <i class="fas fa-tasks"></i> 批量操作
                </button>
                <button class="btn btn-primary" onclick="showCreateActivityModal()">
                    <i class="fas fa-plus"></i> 创建活动
                </button>
            </div>
        </div>
        
        <div class="marketing-stats">
            <div class="stat-card">
                <h3>活跃促销活动</h3>
                <div class="stat-value">24</div>
                <div class="stat-change">较上月 +8.5%</div>
            </div>
            <div class="stat-card">
                <h3>本月参与用户</h3>
                <div class="stat-value">15,847</div>
                <div class="stat-change">较上月 +12.3%</div>
            </div>
            <div class="stat-card">
                <h3>促销订单金额</h3>
                <div class="stat-value">¥589,234</div>
                <div class="stat-change">较上月 +25.6%</div>
            </div>
            <div class="stat-card">
                <h3>优惠券使用率</h3>
                <div class="stat-value">78.5%</div>
                <div class="stat-change">较上月 +3.2%</div>
            </div>
        </div>
        
        <!-- 活动筛选和搜索区域 -->
        <div class="filter-section">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索活动名称、类型..." oninput="searchActivities()">
                <i class="fas fa-search"></i>
            </div>
            <div class="filter-controls">
                <select id="statusFilter" onchange="filterActivities()">
                    <option value="">全部状态</option>
                    <option value="draft">草稿</option>
                    <option value="active">进行中</option>
                    <option value="paused">已暂停</option>
                    <option value="ended">已结束</option>
                    <option value="scheduled">已预约</option>
                </select>
                <select id="typeFilter" onchange="filterActivities()">
                    <option value="">全部类型</option>
                    <option value="discount">满减活动</option>
                    <option value="flash-sale">限时抢购</option>
                    <option value="coupon">优惠券</option>
                    <option value="festival">节日营销</option>
                    <option value="buy-gift">买赠活动</option>
                </select>
                <select id="timeFilter" onchange="filterActivities()">
                    <option value="">全部时间</option>
                    <option value="today">今天</option>
                    <option value="week">本周</option>
                    <option value="month">本月</option>
                    <option value="custom">自定义</option>
                </select>
            </div>
        </div>
        
        <div class="tools-grid">
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon coupon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div>
                        <div class="tool-title">优惠券管理</div>
                        <span class="activity-status status-active">12个活跃</span>
                    </div>
                </div>
                <div class="tool-desc">
                    创建和管理各类优惠券，包括满减券、折扣券、免邮券等，支持批量发放和精准投放。
                </div>
                <ul class="tool-features">
                    <li>满减券、折扣券、免邮券创建</li>
                    <li>批量发放和定向投放</li>
                    <li>使用条件和有效期设置</li>
                    <li>券码生成和防刷机制</li>
                    <li>使用统计和效果分析</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary" onclick="showCouponManagementModal()">
                        <i class="fas fa-cog"></i> 管理优惠券
                    </button>
                    <button class="btn btn-secondary" onclick="showStatisticsModal()">
                        <i class="fas fa-chart-bar"></i> 查看统计
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon flash">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div>
                        <div class="tool-title">限时抢购</div>
                        <span class="activity-status status-active">5个进行中</span>
                    </div>
                </div>
                <div class="tool-desc">
                    创建秒杀、闪购等限时抢购活动，通过稀缺性和紧迫感刺激用户购买欲望。
                </div>
                <ul class="tool-features">
                    <li>秒杀活动创建和管理</li>
                    <li>库存控制和抢购限制</li>
                    <li>倒计时和实时更新</li>
                    <li>防刷和反作弊机制</li>
                    <li>活动预热和推广</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary" onclick="showFlashSaleManagement()">
                        <i class="fas fa-cog"></i> 管理抢购
                    </button>
                    <button class="btn btn-secondary" onclick="showFlashSaleMonitor()">
                        <i class="fas fa-eye"></i> 实时监控
                    </button>
                    <button class="btn btn-secondary" onclick="createFlashSale()">
                        <i class="fas fa-plus"></i> 创建活动
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon discount">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div>
                        <div class="tool-title">满减活动</div>
                        <span class="activity-status status-active">8个活跃</span>
                    </div>
                </div>
                <div class="tool-desc">
                    设置满额减免、阶梯优惠等促销规则，提高客单价和用户购买转化率。
                </div>
                <ul class="tool-features">
                    <li>满减规则灵活配置</li>
                    <li>阶梯优惠和累计折扣</li>
                    <li>商品分类和品牌限制</li>
                    <li>用户群体定向设置</li>
                    <li>活动效果实时分析</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary" onclick="showDiscountManagement()">
                        <i class="fas fa-cog"></i> 管理满减
                    </button>
                    <button class="btn btn-secondary" onclick="showDiscountMonitor()">
                        <i class="fas fa-chart-line"></i> 实时监控
                    </button>
                    <button class="btn btn-secondary" onclick="createDiscountActivity()">
                        <i class="fas fa-plus"></i> 创建活动
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon festival">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div>
                        <div class="tool-title">节日营销</div>
                        <span class="activity-status status-draft">3个待发布</span>
                    </div>
                </div>
                <div class="tool-desc">
                    结合节假日、购物节等特殊时期，策划主题营销活动，提升品牌影响力。
                </div>
                <ul class="tool-features">
                    <li>节日主题活动策划</li>
                    <li>专题页面和装修</li>
                    <li>限时特价和专享优惠</li>
                    <li>节日礼品和套餐</li>
                    <li>社交媒体联动推广</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-calendar-alt"></i> 策划活动
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-history"></i> 历史活动
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 活动列表表格 -->
        <div class="activities-table-container">
            <div class="discount-batch-toolbar">
                <button class="btn btn-secondary" onclick="batchDiscountActions()">
                    <i class="fas fa-tasks"></i> 批量操作
                </button>
            </div>
            
            <div class="table-header">
                <h3>活动列表</h3>
                <div class="table-actions">
                    <button class="btn btn-secondary" onclick="exportActivities()">
                        <i class="fas fa-download"></i> 导出数据
                    </button>
                    <button class="btn btn-secondary" onclick="refreshActivities()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
            
            <div class="table-wrapper">
                <table class="activities-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>活动信息</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>时间期间</th>
                            <th>参与数据</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="activitiesTableBody">
                        <!-- 动态生成的活动列表 -->
                    </tbody>
                </table>
            </div>
            
            <div class="pagination-container">
                <div class="pagination-info">
                    显示第 <span id="currentStart">1</span>-<span id="currentEnd">10</span> 条，共 <span id="totalCount">0</span> 条记录
                </div>
                <div class="pagination" id="pagination">
                    <!-- 动态生成的分页按钮 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 优惠券管理模态框 -->
    <div id="couponManagementModal" class="modal-overlay">
        <div class="modal-content" style="max-width: 1200px;">
            <div class="modal-header">
                <h3><i class="fas fa-ticket-alt"></i> 优惠券管理</h3>
                <button class="modal-close" onclick="closeModal('couponManagementModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 优惠券操作栏 -->
                <div class="coupon-toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showCreateCouponModal()">
                            <i class="fas fa-plus"></i> 创建优惠券
                        </button>
                        <button class="btn btn-secondary" onclick="batchIssueCoupons()">
                            <i class="fas fa-share-alt"></i> 批量发放
                        </button>
                        <button class="btn btn-secondary" onclick="exportCoupons()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <input type="text" id="couponSearch" placeholder="搜索优惠券名称..." onkeyup="searchCoupons()">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="couponTypeFilter" onchange="filterCoupons()">
                            <option value="">全部类型</option>
                            <option value="amount">满减券</option>
                            <option value="discount">折扣券</option>
                            <option value="shipping">免邮券</option>
                            <option value="gift">赠品券</option>
                        </select>
                        <select id="couponStatusFilter" onchange="filterCoupons()">
                            <option value="">全部状态</option>
                            <option value="active">进行中</option>
                            <option value="draft">草稿</option>
                            <option value="expired">已过期</option>
                            <option value="used-up">已用完</option>
                        </select>
                    </div>
                </div>
                
                <!-- 优惠券列表 -->
                <div class="coupon-list">
                    <div class="table-wrapper">
                        <table class="coupons-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAllCoupons" onchange="toggleSelectAllCoupons()"></th>
                                    <th>优惠券信息</th>
                                    <th>类型</th>
                                    <th>优惠额度</th>
                                    <th>使用条件</th>
                                    <th>发放/使用</th>
                                    <th>有效期</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="couponsTableBody">
                                <!-- 动态生成的优惠券列表 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 优惠券分页 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        显示第 <span id="couponCurrentStart">1</span>-<span id="couponCurrentEnd">10</span> 条，共 <span id="couponTotalCount">0</span> 条记录
                    </div>
                    <div class="pagination" id="couponPagination">
                        <!-- 动态生成的分页按钮 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 创建优惠券模态框 -->
    <div id="createCouponModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> 创建优惠券</h3>
                <button class="modal-close" onclick="closeModal('createCouponModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="createCouponForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="couponName">优惠券名称 <span class="required">*</span></label>
                            <input type="text" id="couponName" name="couponName" required maxlength="50">
                        </div>
                        <div class="form-group">
                            <label for="couponType">优惠券类型 <span class="required">*</span></label>
                            <select id="couponType" name="couponType" required onchange="toggleCouponOptions()">
                                <option value="">请选择类型</option>
                                <option value="amount">满减券</option>
                                <option value="discount">折扣券</option>
                                <option value="shipping">免邮券</option>
                                <option value="gift">赠品券</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 满减券配置 -->
                    <div id="amountCouponOptions" class="activity-options" style="display: none;">
                        <h4><i class="fas fa-minus-circle"></i> 满减券配置</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="minOrderAmount">最低消费金额 <span class="required">*</span></label>
                                <input type="number" id="minOrderAmount" name="minOrderAmount" min="0" step="0.01">
                            </div>
                            <div class="form-group">
                                <label for="discountAmount">减免金额 <span class="required">*</span></label>
                                <input type="number" id="discountAmount" name="discountAmount" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 折扣券配置 -->
                    <div id="discountCouponOptions" class="activity-options" style="display: none;">
                        <h4><i class="fas fa-percentage"></i> 折扣券配置</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="discountPercent">折扣比例 <span class="required">*</span></label>
                                <input type="number" id="discountPercent" name="discountPercent" min="1" max="99" step="1">
                                <small>请输入1-99的整数，代表折扣百分比</small>
                            </div>
                            <div class="form-group">
                                <label for="maxDiscountAmount">最高减免金额</label>
                                <input type="number" id="maxDiscountAmount" name="maxDiscountAmount" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 免邮券配置 -->
                    <div id="shippingCouponOptions" class="activity-options" style="display: none;">
                        <h4><i class="fas fa-shipping-fast"></i> 免邮券配置</h4>
                        <div class="form-group">
                            <label for="shippingMinAmount">免邮最低消费</label>
                            <input type="number" id="shippingMinAmount" name="shippingMinAmount" min="0" step="0.01">
                        </div>
                    </div>
                    
                    <!-- 赠品券配置 -->
                    <div id="giftCouponOptions" class="activity-options" style="display: none;">
                        <h4><i class="fas fa-gift"></i> 赠品券配置</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="giftProduct">赠品商品 <span class="required">*</span></label>
                                <input type="text" id="giftProduct" name="giftProduct" placeholder="输入商品名称或ID">
                            </div>
                            <div class="form-group">
                                <label for="giftQuantity">赠品数量</label>
                                <input type="number" id="giftQuantity" name="giftQuantity" min="1" value="1">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="totalQuantity">发放总数 <span class="required">*</span></label>
                            <input type="number" id="totalQuantity" name="totalQuantity" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="limitPerUser">每人限领数量</label>
                            <input type="number" id="limitPerUser" name="limitPerUser" min="1" value="1">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="validStartTime">有效开始时间 <span class="required">*</span></label>
                            <input type="datetime-local" id="validStartTime" name="validStartTime" required>
                        </div>
                        <div class="form-group">
                            <label for="validEndTime">有效结束时间 <span class="required">*</span></label>
                            <input type="datetime-local" id="validEndTime" name="validEndTime" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="couponDescription">使用说明</label>
                        <textarea id="couponDescription" name="couponDescription" rows="3" maxlength="200"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('createCouponModal')">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveCoupon()">保存优惠券</button>
            </div>
        </div>
    </div>
    
    <!-- 限时抢购管理模态框 -->
    <div id="flashSaleManagementModal" class="modal-overlay">
        <div class="modal-content" style="max-width: 1200px;">
            <div class="modal-header">
                <h3><i class="fas fa-bolt"></i> 限时抢购管理</h3>
                <button class="modal-close" onclick="closeModal('flashSaleManagementModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 抢购操作栏 -->
                <div class="flash-sale-toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showCreateFlashSaleModal()">
                            <i class="fas fa-plus"></i> 创建抢购
                        </button>
                        <button class="btn btn-secondary" onclick="batchManageFlashSales()">
                            <i class="fas fa-cogs"></i> 批量管理
                        </button>
                        <button class="btn btn-secondary" onclick="exportFlashSales()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <input type="text" id="flashSaleSearch" placeholder="搜索抢购活动..." onkeyup="searchFlashSales()">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="flashSaleStatusFilter" onchange="filterFlashSales()">
                            <option value="">全部状态</option>
                            <option value="waiting">待开始</option>
                            <option value="active">进行中</option>
                            <option value="ended">已结束</option>
                            <option value="canceled">已取消</option>
                        </select>
                    </div>
                </div>
                
                <!-- 抢购列表 -->
                <div class="flash-sale-list">
                    <table class="flash-sales-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAllFlashSales" onchange="toggleSelectAllFlashSales()"></th>
                                <th>抢购信息</th>
                                <th>商品信息</th>
                                <th>价格/库存</th>
                                <th>时间安排</th>
                                <th>参与数据</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="flashSalesTableBody">
                            <!-- 动态生成 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        显示第 <span id="flashSaleCurrentStart">1</span>-<span id="flashSaleCurrentEnd">10</span> 条，共 <span id="flashSaleTotalCount">0</span> 条记录
                    </div>
                    <div class="pagination" id="flashSalePagination">
                        <!-- 动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 限时抢购实时监控模态框 -->
    <div id="flashSaleMonitorModal" class="modal-overlay">
        <div class="modal-content" style="max-width: 1400px;">
            <div class="modal-header">
                <h3><i class="fas fa-chart-line"></i> 限时抢购实时监控</h3>
                <div class="monitor-controls">
                    <button class="btn btn-secondary" onclick="refreshMonitorData()" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i> <span id="refreshCountdown">5</span>秒后刷新
                    </button>
                    <button class="btn btn-secondary" onclick="toggleAutoRefresh()" id="autoRefreshBtn">
                        <i class="fas fa-play"></i> 自动刷新
                    </button>
                </div>
                <button class="modal-close" onclick="closeModal('flashSaleMonitorModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 实时概览 -->
                <div class="monitor-overview">
                    <div class="monitor-card">
                        <div class="monitor-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">进行中活动</div>
                            <div class="monitor-value" id="activeFlashSalesCount">0</div>
                            <div class="monitor-change">+2 vs 昨日</div>
                        </div>
                    </div>
                    <div class="monitor-card">
                        <div class="monitor-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">实时参与人数</div>
                            <div class="monitor-value" id="currentParticipants">0</div>
                            <div class="monitor-change">实时更新</div>
                        </div>
                    </div>
                    <div class="monitor-card">
                        <div class="monitor-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">今日成交订单</div>
                            <div class="monitor-value" id="todayOrders">0</div>
                            <div class="monitor-change">+15% vs 昨日</div>
                        </div>
                    </div>
                    <div class="monitor-card">
                        <div class="monitor-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">今日成交金额</div>
                            <div class="monitor-value" id="todayRevenue">¥0</div>
                            <div class="monitor-change">+23% vs 昨日</div>
                        </div>
                    </div>
                </div>
                
                <!-- 活动实时状态 -->
                <div class="live-activities">
                    <h4><i class="fas fa-fire"></i> 热门抢购活动</h4>
                    <div class="live-activities-grid" id="liveActivitiesGrid">
                        <!-- 动态生成 -->
                    </div>
                </div>
                
                <!-- 实时数据图表 -->
                <div class="monitor-charts">
                    <div class="chart-container">
                        <h4>实时参与趋势</h4>
                        <div class="chart-placeholder" id="participantChart">
                            <i class="fas fa-chart-area"></i>
                            <p>实时参与人数变化趋势图</p>
                        </div>
                    </div>
                    <div class="chart-container">
                        <h4>订单成交情况</h4>
                        <div class="chart-placeholder" id="orderChart">
                            <i class="fas fa-chart-bar"></i>
                            <p>每小时订单成交统计图</p>
                        </div>
                    </div>
                </div>
                
                <!-- 异常警报 -->
                <div class="alert-panel" id="alertPanel">
                    <h4><i class="fas fa-exclamation-triangle"></i> 系统警报</h4>
                    <div class="alert-list" id="alertList">
                        <!-- 动态生成警报信息 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 创建抢购活动模态框 -->
    <div id="createFlashSaleModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> 创建限时抢购</h3>
                <button class="modal-close" onclick="closeModal('createFlashSaleModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="createFlashSaleForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="flashSaleName">活动名称 <span class="required">*</span></label>
                            <input type="text" id="flashSaleName" name="flashSaleName" placeholder="例：iPhone 15 Pro 限时抢购" required>
                        </div>
                        <div class="form-group">
                            <label for="flashSaleProduct">选择商品 <span class="required">*</span></label>
                            <select id="flashSaleProduct" name="flashSaleProduct" required onchange="updateProductInfo()">
                                <option value="">请选择商品</option>
                                <option value="1" data-price="7999">iPhone 15 Pro - ¥7999</option>
                                <option value="2" data-price="8999">MacBook Air M2 - ¥8999</option>
                                <option value="3" data-price="1899">AirPods Pro 2 - ¥1899</option>
                                <option value="4" data-price="4399">iPad Air 5 - ¥4399</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="originalPrice">商品原价 <span class="required">*</span></label>
                            <div class="price-input">
                                <input type="number" id="originalPrice" name="originalPrice" step="0.01" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="flashSalePrice">限时抢购价 <span class="required">*</span></label>
                            <div class="price-input">
                                <input type="number" id="flashSalePrice" name="flashSalePrice" step="0.01" placeholder="设置抢购价格" required>
                            </div>
                            <div id="discountInfo" class="stock-info" style="color: #e53e3e;"></div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="totalStock">抢购库存 <span class="required">*</span></label>
                            <input type="number" id="totalStock" name="totalStock" min="1" placeholder="设置抢购数量" required>
                            <div class="stock-info">
                                <i class="fas fa-info-circle"></i>
                                <span>建议设置合理的库存数量以营造稀缺感</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="limitPerUser">单人限购数量</label>
                            <input type="number" id="limitPerUser" name="limitPerUser" min="1" max="10" value="1" placeholder="每人最多购买">
                            <div class="stock-info">
                                <i class="fas fa-user"></i>
                                <span>控制单人购买数量，防止囤货</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="flashStartTime">活动开始时间 <span class="required">*</span></label>
                            <input type="datetime-local" id="flashStartTime" name="flashStartTime" required>
                            <div class="time-hint">建议选择用户活跃时段</div>
                        </div>
                        <div class="form-group">
                            <label for="flashEndTime">活动结束时间 <span class="required">*</span></label>
                            <input type="datetime-local" id="flashEndTime" name="flashEndTime" required>
                            <div class="time-hint">
                                <span id="durationInfo">活动时长：未设置</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row single">
                        <div class="form-group">
                            <label>抢购规则设置</label>
                            <div class="flash-sale-rules">
                                <label>
                                    <input type="checkbox" name="enablePreview" checked> 
                                    开启预告功能（提前1小时显示）
                                </label>
                                <label>
                                    <input type="checkbox" name="enableQueue" checked> 
                                    启用排队系统（高并发保护）
                                </label>
                                <label>
                                    <input type="checkbox" name="enableCaptcha"> 
                                    验证码防刷（防机器人）
                                </label>
                                <label>
                                    <input type="checkbox" name="enableRealName"> 
                                    实名认证要求
                                </label>
                                <label>
                                    <input type="checkbox" name="enableNotification" checked> 
                                    开始前推送通知
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row single">
                        <div class="form-group">
                            <label for="flashSaleDescription">活动描述</label>
                            <textarea id="flashSaleDescription" name="flashSaleDescription" rows="4" placeholder="输入吸引人的活动描述，提高参与度..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('createFlashSaleModal')">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveFlashSale()">创建抢购</button>
            </div>
        </div>
    </div>

    <!-- 批量管理抢购活动模态框 -->
    <div id="batchFlashSaleModal" class="modal-overlay">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3><i class="fas fa-cogs"></i> 批量管理抢购活动</h3>
                <button class="modal-close" onclick="closeModal('batchFlashSaleModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="batch-info">
                    <div class="selected-count">
                        已选择 <span id="selectedFlashSaleCount">0</span> 个抢购活动
                    </div>
                    <div class="batch-actions-grid">
                        <div class="batch-action-card" onclick="batchUpdateStatus('active')">
                            <div class="action-icon active">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="action-info">
                                <h4>批量启用</h4>
                                <p>启用选中的抢购活动</p>
                            </div>
                        </div>
                        
                        <div class="batch-action-card" onclick="batchUpdateStatus('paused')">
                            <div class="action-icon paused">
                                <i class="fas fa-pause"></i>
                            </div>
                            <div class="action-info">
                                <h4>批量暂停</h4>
                                <p>暂停选中的抢购活动</p>
                            </div>
                        </div>
                        
                        <div class="batch-action-card" onclick="batchUpdateStock()">
                            <div class="action-icon stock">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="action-info">
                                <h4>库存调整</h4>
                                <p>批量调整抢购库存数量</p>
                            </div>
                        </div>
                        
                        <div class="batch-action-card" onclick="batchUpdateTime()">
                            <div class="action-icon time">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="action-info">
                                <h4>时间调整</h4>
                                <p>批量调整活动时间</p>
                            </div>
                        </div>
                        
                        <div class="batch-action-card" onclick="batchCopyActivities()">
                            <div class="action-icon copy">
                                <i class="fas fa-copy"></i>
                            </div>
                            <div class="action-info">
                                <h4>批量复制</h4>
                                <p>复制选中的抢购活动</p>
                            </div>
                        </div>
                        
                        <div class="batch-action-card warning" onclick="batchDeleteActivities()">
                            <div class="action-icon delete">
                                <i class="fas fa-trash"></i>
                            </div>
                            <div class="action-info">
                                <h4>批量删除</h4>
                                <p>删除选中的抢购活动</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="selected-activities">
                    <h4><i class="fas fa-list"></i> 选中的活动列表</h4>
                    <div id="selectedActivitiesList" class="activities-list">
                        <!-- 动态生成选中的活动列表 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 满减活动管理模态框 -->
    <div id="discountManagementModal" class="modal-overlay">
        <div class="modal-content" style="max-width: 1400px;">
            <div class="modal-header">
                <h3><i class="fas fa-percentage"></i> 满减活动管理</h3>
                <button class="modal-close" onclick="closeModal('discountManagementModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 满减活动操作栏 -->
                <div class="discount-toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showCreateDiscountModal()">
                            <i class="fas fa-plus"></i> 创建满减活动
                        </button>
                        <button class="btn btn-secondary" onclick="batchDiscountActions()">
                            <i class="fas fa-tasks"></i> 批量操作
                        </button>
                        <button class="btn btn-secondary" onclick="exportDiscountData()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                        <button class="btn btn-secondary" onclick="importDiscountTemplate()">
                            <i class="fas fa-upload"></i> 导入模板
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <input type="text" id="discountSearch" placeholder="搜索活动名称..." onkeyup="searchDiscounts()">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="discountStatusFilter" onchange="filterDiscounts()">
                            <option value="">全部状态</option>
                            <option value="active">进行中</option>
                            <option value="scheduled">已预约</option>
                            <option value="paused">已暂停</option>
                            <option value="ended">已结束</option>
                            <option value="draft">草稿</option>
                        </select>
                        <select id="discountTypeFilter" onchange="filterDiscounts()">
                            <option value="">全部类型</option>
                            <option value="full-reduction">满减优惠</option>
                            <option value="tiered">阶梯满减</option>
                            <option value="category">品类满减</option>
                            <option value="member">会员专享</option>
                        </select>
                    </div>
                </div>
                
                <!-- 满减活动列表 -->
                <div class="discount-list">
                    <div class="table-wrapper">
                        <table class="discount-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAllDiscounts" onchange="toggleSelectAllDiscounts()"></th>
                                    <th>活动信息</th>
                                    <th>满减规则</th>
                                    <th>适用范围</th>
                                    <th>参与数据</th>
                                    <th>活动时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="discountTableBody">
                                <!-- 动态生成的满减活动列表 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 满减活动分页 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        显示第 <span id="discountCurrentStart">1</span>-<span id="discountCurrentEnd">10</span> 条，共 <span id="discountTotalCount">0</span> 条记录
                    </div>
                    <div class="pagination" id="discountPagination">
                        <!-- 动态生成的分页按钮 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 满减活动实时监控模态框 -->
    <div id="discountMonitorModal" class="modal-overlay">
        <div class="modal-content" style="max-width: 1600px;">
            <div class="modal-header">
                <h3><i class="fas fa-chart-line"></i> 满减活动实时监控</h3>
                <button class="modal-close" onclick="closeModal('discountMonitorModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 实时概览 -->
                <div class="monitor-overview">
                    <div class="monitor-card">
                        <div class="monitor-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">进行中的满减活动</div>
                            <div class="monitor-value" id="activeDiscountCount">0</div>
                            <div class="monitor-change" id="activeDiscountChange">+0%</div>
                        </div>
                    </div>
                    
                    <div class="monitor-card">
                        <div class="monitor-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">实时参与用户</div>
                            <div class="monitor-value" id="liveDiscountUsers">0</div>
                            <div class="monitor-change" id="liveUsersChange">+0%</div>
                        </div>
                    </div>
                    
                    <div class="monitor-card">
                        <div class="monitor-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">满减订单数</div>
                            <div class="monitor-value" id="discountOrderCount">0</div>
                            <div class="monitor-change" id="orderCountChange">+0%</div>
                        </div>
                    </div>
                    
                    <div class="monitor-card">
                        <div class="monitor-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">节省金额</div>
                            <div class="monitor-value" id="totalSavings">¥0</div>
                            <div class="monitor-change" id="savingsChange">+0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- 监控控制 -->
                <div class="monitor-controls">
                    <button class="btn btn-primary" id="discountMonitorToggle" onclick="toggleDiscountMonitor()">
                        <i class="fas fa-play"></i> 开始监控
                    </button>
                    <button class="btn btn-secondary" onclick="refreshDiscountMonitor()">
                        <i class="fas fa-sync-alt"></i> 刷新数据
                    </button>
                    <button class="btn btn-secondary" onclick="exportDiscountReport()">
                        <i class="fas fa-download"></i> 导出报告  
                    </button>
                </div>
                
                <!-- 热门满减活动 -->
                <div class="live-activities">
                    <h4><i class="fas fa-fire"></i> 热门满减活动</h4>
                    <div class="live-activities-grid" id="liveDiscountActivities">
                        <!-- 动态生成热门满减活动卡片 -->
                    </div>
                </div>
                
                <!-- 数据图表 -->
                <div class="monitor-charts">
                    <div class="chart-container">
                        <h4>满减活动参与趋势</h4>
                        <div class="chart-placeholder" id="discountTrendChart">
                            <i class="fas fa-chart-line"></i>
                            <p>实时趋势图表</p>
                            <small>数据每5秒更新一次</small>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <h4>满减金额分布</h4>
                        <div class="chart-placeholder" id="discountDistributionChart">
                            <i class="fas fa-chart-pie"></i>
                            <p>满减金额分布图</p>
                            <small>显示不同满减档位的使用情况</small>
                        </div>
                    </div>
                </div>
                
                <!-- 系统警报 -->
                <div class="alert-panel">
                    <h4><i class="fas fa-exclamation-triangle"></i> 系统警报</h4>
                    <div class="alert-list" id="discountAlertList">
                        <!-- 动态生成的警报信息 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建满减活动模态框 -->
    <div id="createDiscountModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> 创建满减活动</h3>
                <button class="modal-close" onclick="closeModal('createDiscountModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="createDiscountForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="discountName">活动名称 <span class="required">*</span></label>
                            <input type="text" id="discountName" name="discountName" placeholder="例：全场满199减30" required>
                        </div>
                        <div class="form-group">
                            <label for="discountType">满减类型 <span class="required">*</span></label>
                            <select id="discountType" name="discountType" required onchange="updateDiscountRules()">
                                <option value="">请选择满减类型</option>
                                <option value="full-reduction">满减优惠（满X减Y）</option>
                                <option value="tiered">阶梯满减（多档满减）</option>
                                <option value="category">品类满减（指定分类）</option>
                                <option value="member">会员专享（会员满减）</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row single">
                        <div class="form-group">
                            <label>满减规则设置</label>
                            <div id="discountRulesContainer" class="discount-rules">
                                <div class="rule-placeholder">
                                    <i class="fas fa-info-circle"></i>
                                    <p>请先选择满减类型，然后配置相应的规则</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="discountStartTime">活动开始时间 <span class="required">*</span></label>
                            <input type="datetime-local" id="discountStartTime" name="discountStartTime" required>
                        </div>
                        <div class="form-group">
                            <label for="discountEndTime">活动结束时间 <span class="required">*</span></label>
                            <input type="datetime-local" id="discountEndTime" name="discountEndTime" required>
                            <div class="time-hint">
                                <span id="discountDurationInfo">活动时长：未设置</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row single">
                        <div class="form-group">
                            <label>适用范围设置</label>
                            <div class="discount-scope">
                                <div class="scope-option">
                                    <label>
                                        <input type="radio" name="scopeType" value="all" checked onclick="updateScopeSettings()"> 
                                        全店通用
                                    </label>
                                </div>
                                <div class="scope-option">
                                    <label>
                                        <input type="radio" name="scopeType" value="category" onclick="updateScopeSettings()"> 
                                        指定分类
                                    </label>
                                </div>
                                <div class="scope-option">
                                    <label>
                                        <input type="radio" name="scopeType" value="product" onclick="updateScopeSettings()"> 
                                        指定商品
                                    </label>
                                </div>
                                <div class="scope-option">
                                    <label>
                                        <input type="radio" name="scopeType" value="brand" onclick="updateScopeSettings()"> 
                                        指定品牌
                                    </label>
                                </div>
                            </div>
                            <div id="scopeDetailsContainer" class="scope-details" style="display: none;">
                                <!-- 动态生成适用范围详细设置 -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row single">
                        <div class="form-group">
                            <label>高级设置</label>
                            <div class="discount-advanced-settings">
                                <label>
                                    <input type="checkbox" name="enableUserLimit"> 
                                    限制参与用户（每用户限用次数）
                                </label>
                                <label>
                                    <input type="checkbox" name="enableTotalLimit"> 
                                    限制活动总预算
                                </label>
                                <label>
                                    <input type="checkbox" name="enableCouponStack"> 
                                    允许与优惠券叠加使用
                                </label>
                                <label>
                                    <input type="checkbox" name="enableMemberOnly"> 
                                    仅限会员参与
                                </label>
                                <label>
                                    <input type="checkbox" name="enableNotification" checked> 
                                    活动开始前推送通知
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row single">
                        <div class="form-group">
                            <label for="discountDescription">活动描述</label>
                            <textarea id="discountDescription" name="discountDescription" rows="4" placeholder="输入活动描述，吸引用户参与..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('createDiscountModal')">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveDiscountActivity()">创建满减活动</button>
            </div>
        </div>
    </div>

    <!-- 统计查看模态框 -->
    <div id="statisticsModal" class="modal-overlay">
        <div class="modal-content" style="max-width: 1400px;">
            <div class="modal-header">
                <h3><i class="fas fa-chart-bar"></i> 促销活动统计分析</h3>
                <button class="modal-close" onclick="closeModal('statisticsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 统计时间选择 -->
                <div class="stats-toolbar">
                    <div class="time-range-selector">
                        <label>统计时间范围：</label>
                        <select id="statsTimeRange" onchange="updateStatistics()">
                            <option value="7">最近7天</option>
                            <option value="30" selected>最近30天</option>
                            <option value="90">最近90天</option>
                            <option value="custom">自定义</option>
                        </select>
                        <div id="customDateRange" style="display: none;">
                            <input type="date" id="statsStartDate" onchange="updateStatistics()">
                            <span>至</span>
                            <input type="date" id="statsEndDate" onchange="updateStatistics()">
                        </div>
                    </div>
                    <div class="stats-actions">
                        <button class="btn btn-secondary" onclick="exportStatistics()">
                            <i class="fas fa-download"></i> 导出报表
                        </button>
                        <button class="btn btn-secondary" onclick="refreshStatistics()">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="statistics-overview">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h4>总参与人数</h4>
                            <div class="stat-value" id="totalParticipants">0</div>
                            <div class="stat-change positive" id="participantsChange">+0%</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-content">
                            <h4>总订单数</h4>
                            <div class="stat-value" id="totalOrders">0</div>
                            <div class="stat-change positive" id="ordersChange">+0%</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h4>总成交金额</h4>
                            <div class="stat-value" id="totalRevenue">¥0</div>
                            <div class="stat-change positive" id="revenueChange">+0%</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <h4>平均转化率</h4>
                            <div class="stat-value" id="avgConversionRate">0%</div>
                            <div class="stat-change positive" id="conversionChange">+0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- 图表区域 -->
                <div class="charts-container">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>活动参与趋势</h3>
                            <div class="chart-controls">
                                <select id="trendChartType" onchange="updateTrendChart()">
                                    <option value="participants">参与人数</option>
                                    <option value="orders">订单数量</option>
                                    <option value="revenue">成交金额</option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-content">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>
                    
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>活动类型分布</h3>
                        </div>
                        <div class="chart-content">
                            <canvas id="typeDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- 详细统计表格 -->
                <div class="detailed-stats">
                    <div class="table-header">
                        <h3>活动详细统计</h3>
                        <div class="table-actions">
                            <button class="btn btn-secondary" onclick="exportDetailedStats()">
                                <i class="fas fa-table"></i> 导出表格
                            </button>
                        </div>
                    </div>
                    <div class="table-wrapper">
                        <table class="stats-table">
                            <thead>
                                <tr>
                                    <th>活动名称</th>
                                    <th>类型</th>
                                    <th>活动时间</th>
                                    <th>参与人数</th>
                                    <th>订单数量</th>
                                    <th>成交金额</th>
                                    <th>转化率</th>
                                    <th>ROI</th>
                                </tr>
                            </thead>
                            <tbody id="detailedStatsTableBody">
                                <!-- 动态生成的统计数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建活动模态框 -->
    <div id="createActivityModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> 创建促销活动</h3>
                <button class="modal-close" onclick="closeModal('createActivityModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="createActivityForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="activityName">活动名称 <span class="required">*</span></label>
                            <input type="text" id="activityName" name="activityName" required maxlength="50">
                        </div>
                        <div class="form-group">
                            <label for="activityType">活动类型 <span class="required">*</span></label>
                            <select id="activityType" name="activityType" required onchange="toggleActivityOptions()">
                                <option value="">请选择活动类型</option>
                                <option value="discount">满减活动</option>
                                <option value="flash-sale">限时抢购</option>
                                <option value="coupon">优惠券</option>
                                <option value="festival">节日营销</option>
                                <option value="buy-gift">买赠活动</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="activityDesc">活动描述</label>
                        <textarea id="activityDesc" name="activityDesc" rows="3" maxlength="200"></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="startTime">开始时间 <span class="required">*</span></label>
                            <input type="datetime-local" id="startTime" name="startTime" required>
                        </div>
                        <div class="form-group">
                            <label for="endTime">结束时间 <span class="required">*</span></label>
                            <input type="datetime-local" id="endTime" name="endTime" required>
                        </div>
                    </div>
                    
                    <!-- 满减活动配置 -->
                    <div id="discountOptions" class="activity-options" style="display: none;">
                        <h4><i class="fas fa-percentage"></i> 满减规则配置</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="minAmount">满足金额</label>
                                <input type="number" id="minAmount" name="minAmount" step="0.01" min="0">
                            </div>
                            <div class="form-group">
                                <label for="discountAmount">减免金额</label>
                                <input type="number" id="discountAmount" name="discountAmount" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 限时抢购配置 -->
                    <div id="flashSaleOptions" class="activity-options" style="display: none;">
                        <h4><i class="fas fa-bolt"></i> 抢购活动配置</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="originalPrice">原价</label>
                                <input type="number" id="originalPrice" name="originalPrice" step="0.01" min="0">
                            </div>
                            <div class="form-group">
                                <label for="salePrice">抢购价</label>
                                <input type="number" id="salePrice" name="salePrice" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="totalStock">总库存</label>
                                <input type="number" id="totalStock" name="totalStock" min="1">
                            </div>
                            <div class="form-group">
                                <label for="limitPerUser">每人限购</label>
                                <input type="number" id="limitPerUser" name="limitPerUser" min="1">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 优惠券配置 -->
                    <div id="couponOptions" class="activity-options" style="display: none;">
                        <h4><i class="fas fa-ticket-alt"></i> 优惠券配置</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="couponType">优惠券类型</label>
                                <select id="couponType" name="couponType">
                                    <option value="amount">满减券</option>
                                    <option value="percent">折扣券</option>
                                    <option value="shipping">免邮券</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="couponValue">优惠金额/折扣</label>
                                <input type="number" id="couponValue" name="couponValue" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="totalCoupons">发放总量</label>
                                <input type="number" id="totalCoupons" name="totalCoupons" min="1">
                            </div>
                            <div class="form-group">
                                <label for="dailyLimit">每日限量</label>
                                <input type="number" id="dailyLimit" name="dailyLimit" min="1">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="targetUsers">目标用户</label>
                            <select id="targetUsers" name="targetUsers">
                                <option value="all">所有用户</option>
                                <option value="new">新用户</option>
                                <option value="vip">VIP用户</option>
                                <option value="inactive">沉寂用户</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="activityStatus">活动状态</label>
                            <select id="activityStatus" name="activityStatus">
                                <option value="draft">草稿</option>
                                <option value="scheduled">已预约</option>
                                <option value="active">立即生效</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('createActivityModal')">
                    取消
                </button>
                <button type="button" class="btn btn-primary" onclick="saveActivity()">
                    <i class="fas fa-save"></i> 保存活动
                </button>
            </div>
        </div>
    </div>
    
    <!-- 确认对话框 -->
    <div id="confirmModal" class="modal-overlay">
        <div class="modal-content modal-sm">
            <div class="modal-header">
                <h3 id="confirmTitle">确认操作</h3>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('confirmModal')">
                    取消
                </button>
                <button type="button" class="btn btn-primary" id="confirmBtn">
                    确定
                </button>
            </div>
        </div>
    </div>
    
    <!-- 批量发放优惠券模态框 -->
    <div id="batchIssueModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>批量发放优惠券</h3>
                <button type="button" class="close-btn" onclick="closeModal('batchIssueModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="batchIssueForm">
                    <div class="form-group">
                        <label for="issueType">发放类型</label>
                        <select id="issueType" name="issueType" required>
                            <option value="">请选择发放类型</option>
                            <option value="immediate">立即发放</option>
                            <option value="scheduled">定时发放</option>
                            <option value="conditional">条件发放</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="issueQuantity">发放数量</label>
                        <input type="number" id="issueQuantity" name="issueQuantity" min="1" required placeholder="请输入每种优惠券的发放数量">
                        <small class="form-hint">每种优惠券的发放数量</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="targetUsers">目标用户</label>
                        <select id="targetUsers" name="targetUsers" required>
                            <option value="all">全部用户</option>
                            <option value="new">新用户</option>
                            <option value="vip">VIP用户</option>
                            <option value="active">活跃用户</option>
                            <option value="birthday">生日用户</option>
                            <option value="custom">指定用户</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="issueNote">发放说明</label>
                        <textarea id="issueNote" name="issueNote" rows="3" placeholder="请输入发放说明（可选）"></textarea>
                    </div>
                    
                    <div class="selected-coupons-info">
                        <h4>已选择的优惠券</h4>
                        <div id="selectedCouponsDisplay">
                            <!-- 显示选中的优惠券 -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('batchIssueModal')">
                    取消
                </button>
                <button type="button" class="btn btn-primary" onclick="confirmBatchIssue()">
                    <i class="fas fa-paper-plane"></i> 确认发放
                </button>
            </div>
        </div>
    </div>
    
    <!-- 优惠券统计模态框 -->
    <div id="couponStatisticsModal" class="modal-overlay">
        <div class="modal-content modal-lg">
            <div class="modal-header">
                <h3>优惠券详细统计</h3>
                <button type="button" class="close-btn" onclick="closeModal('couponStatisticsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="coupon-stats-header">
                    <h4 id="statisticsCouponName">优惠券名称</h4>
                    <div class="stats-summary">
                        <div class="stats-item">
                            <span class="stats-label">已发放</span>
                            <span class="stats-value" id="statisticsIssuedQuantity">0</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">已使用</span>
                            <span class="stats-value" id="statisticsUsedQuantity">0</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">剩余</span>
                            <span class="stats-value" id="statisticsRemainingQuantity">0</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">使用率</span>
                            <span class="stats-value" id="statisticsUsageRate">0%</span>
                        </div>
                    </div>
                </div>
                
                <div class="stats-tabs">
                    <button class="tab-btn active" onclick="switchStatsTab('daily')">每日使用情况</button>
                    <button class="tab-btn" onclick="switchStatsTab('userGroup')">用户群体分析</button>
                </div>
                
                <div id="dailyStatsTab" class="stats-tab-content active">
                    <h5>每日使用情况</h5>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>发放数量</th>
                                    <th>使用数量</th>
                                    <th>使用率</th>
                                </tr>
                            </thead>
                            <tbody id="dailyUsageTableBody">
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div id="userGroupStatsTab" class="stats-tab-content">
                    <h5>用户群体使用分析</h5>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>用户群体</th>
                                    <th>发放数量</th>
                                    <th>使用数量</th>
                                    <th>使用率</th>
                                </tr>
                            </thead>
                            <tbody id="userGroupTableBody">
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="exportCouponStats()">
                    <i class="fas fa-download"></i> 导出统计
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('couponStatisticsModal')">
                    关闭
                </button>
            </div>
        </div>
    </div>
    
    <!-- 加载指示器 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>正在加载...</p>
        </div>
    </div>
    
    <!-- 消息提示 -->
    <div id="messageToast" class="message-toast">
        <i class="fas fa-check-circle"></i>
        <span id="messageText"></span>
    </div>

    <!-- 满减活动批量操作模态框 -->
    <div id="batchDiscountModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-tasks"></i> 满减活动批量操作</h3>
                <button class="modal-close" onclick="closeModal('batchDiscountModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="batch-options">
                    <div class="batch-section">
                        <h4><i class="fas fa-play-circle"></i> 状态操作</h4>
                        <div class="batch-buttons">
                            <button class="btn btn-success" onclick="batchDiscountOperation('enable')">
                                <i class="fas fa-play"></i> 批量启用
                            </button>
                            <button class="btn btn-warning" onclick="batchDiscountOperation('pause')">
                                <i class="fas fa-pause"></i> 批量暂停
                            </button>
                            <button class="btn btn-danger" onclick="batchDiscountOperation('delete')">
                                <i class="fas fa-trash"></i> 批量删除
                            </button>
                        </div>
                    </div>
                    
                    <div class="batch-section">
                        <h4><i class="fas fa-edit"></i> 批量编辑</h4>
                        <div class="batch-edit-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>批量调整开始时间</label>
                                    <input type="datetime-local" id="batchStartTime">
                                    <button class="btn btn-sm btn-secondary" onclick="applyBatchTimeChange('start')">应用</button>
                                </div>
                                <div class="form-group">
                                    <label>批量调整结束时间</label>
                                    <input type="datetime-local" id="batchEndTime">
                                    <button class="btn btn-sm btn-secondary" onclick="applyBatchTimeChange('end')">应用</button>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label>批量调整优惠力度</label>
                                    <div class="discount-adjust">
                                        <select id="batchDiscountType">
                                            <option value="increase">增加</option>
                                            <option value="decrease">减少</option>
                                            <option value="multiply">倍数调整</option>
                                        </select>
                                        <input type="number" id="batchDiscountValue" step="0.01" placeholder="调整值">
                                        <button class="btn btn-sm btn-secondary" onclick="applyBatchDiscountChange()">应用</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="batch-section">
                        <h4><i class="fas fa-copy"></i> 批量复制</h4>
                        <div class="batch-copy-form">
                            <div class="form-group">
                                <label>复制设置</label>
                                <div class="copy-options">
                                    <label><input type="checkbox" id="copyRules" checked> 复制优惠规则</label>
                                    <label><input type="checkbox" id="copyScope" checked> 复制适用范围</label>
                                    <label><input type="checkbox" id="copySettings"> 复制高级设置</label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>新活动名称前缀</label>
                                <input type="text" id="copyPrefix" placeholder="例：复制_" value="副本_">
                            </div>
                            <button class="btn btn-primary" onclick="batchCopyDiscountActivities()">
                                <i class="fas fa-copy"></i> 开始复制
                            </button>
                        </div>
                    </div>
                    
                    <div class="batch-section">
                        <h4><i class="fas fa-download"></i> 批量导出</h4>
                        <div class="export-options">
                            <div class="export-format">
                                <label>导出格式：</label>
                                <select id="exportFormat">
                                    <option value="excel">Excel (.xlsx)</option>
                                    <option value="csv">CSV (.csv)</option>
                                    <option value="pdf">PDF (.pdf)</option>
                                </select>
                            </div>
                            <div class="export-fields">
                                <label>导出字段：</label>
                                <div class="field-checkboxes">
                                    <label><input type="checkbox" id="exportBasic" checked> 基本信息</label>
                                    <label><input type="checkbox" id="exportRules" checked> 优惠规则</label>
                                    <label><input type="checkbox" id="exportStats"> 统计数据</label>
                                    <label><input type="checkbox" id="exportAdvanced"> 高级设置</label>
                                </div>
                            </div>
                            <button class="btn btn-primary" onclick="batchExportDiscountActivities()">
                                <i class="fas fa-download"></i> 导出选中活动
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="batch-selected-info">
                    <div class="selected-count">
                        已选择 <span id="selectedDiscountCount">0</span> 个满减活动
                    </div>
                    <div class="batch-actions">
                        <button class="btn btn-secondary" onclick="selectAllDiscountActivities()">全选</button>
                        <button class="btn btn-secondary" onclick="clearDiscountSelection()">清空选择</button>
                        <button class="btn btn-secondary" onclick="closeModal('batchDiscountModal')">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let activitiesData = [];
        let filteredData = [];
        let currentPage = 1;
        let pageSize = 10;
        let selectedIds = new Set();
        let currentConfirmAction = null;
        
        // 示例数据
        const sampleActivities = [
            {
                id: 1,
                name: '双11狂欢节大促',
                type: 'festival',
                status: 'active',
                description: '年度最大促销活动，全场满300减50',
                startTime: '2024-11-01 00:00:00',
                endTime: '2024-11-12 23:59:59',
                participants: 1234,
                orders: 567,
                revenue: 89456.78,
                config: { minAmount: 300, discountAmount: 50 }
            },
            {
                id: 2,
                name: '新用户专享优惠券',
                type: 'coupon',
                status: 'active',
                description: '新用户注册送50元优惠券',
                startTime: '2024-01-01 00:00:00',
                endTime: '2024-12-31 23:59:59',
                participants: 2345,
                orders: 1234,
                revenue: 45678.90,
                config: { couponType: 'amount', couponValue: 50, totalCoupons: 10000 }
            },
            {
                id: 3,
                name: '周末限时抢购',
                type: 'flash-sale',
                status: 'paused',
                description: '精选商品限时抢购，数量有限',
                startTime: '2024-03-01 00:00:00',
                endTime: '2024-03-31 23:59:59',
                participants: 567,
                orders: 234,
                revenue: 12345.67,
                config: { originalPrice: 199, salePrice: 99, totalStock: 500 }
            },
            {
                id: 4,
                name: '满减活动',
                type: 'discount',
                status: 'ended',
                description: '满200减30，满500减100',
                startTime: '2024-02-01 00:00:00',
                endTime: '2024-02-29 23:59:59',
                participants: 890,
                orders: 456,
                revenue: 67890.12,
                config: { minAmount: 200, discountAmount: 30 }
            },
            {
                id: 5,
                name: '春节买赠活动',
                type: 'buy-gift',
                status: 'scheduled',
                description: '买二送一，春节特惠',
                startTime: '2024-02-10 00:00:00',
                endTime: '2024-02-17 23:59:59',
                participants: 0,
                orders: 0,
                revenue: 0,
                config: { buyQuantity: 2, giftQuantity: 1 }
            }
        ];
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initPromotionalData();
            bindEventListeners();
            loadActivitiesData();
        });
        
        function initPromotionalData() {
            // 初始化数据
            activitiesData = [...sampleActivities];
            filteredData = [...activitiesData];
            updateStatistics();
            renderActivitiesTable();
            setupPagination();
        }
        
        function bindEventListeners() {
            // 模态框关闭事件
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal-overlay')) {
                    closeModal(e.target.id);
                }
            });
            
            // 表单提交防止页面刷新
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                });
            });
        }
        
        function updateStatistics() {
            const activeCount = activitiesData.filter(a => a.status === 'active').length;
            const totalParticipants = activitiesData.reduce((sum, a) => sum + a.participants, 0);
            const totalRevenue = activitiesData.reduce((sum, a) => sum + a.revenue, 0);
            const avgCouponUsage = 78.5; // 模拟数据
            
            document.querySelector('.stat-card:nth-child(1) .stat-value').textContent = activeCount;
            document.querySelector('.stat-card:nth-child(2) .stat-value').textContent = totalParticipants.toLocaleString();
            document.querySelector('.stat-card:nth-child(3) .stat-value').textContent = '¥' + totalRevenue.toLocaleString();
            document.querySelector('.stat-card:nth-child(4) .stat-value').textContent = avgCouponUsage + '%';
        }
        
        function loadActivitiesData() {
            showLoading();
            // 模拟API调用
            setTimeout(() => {
                hideLoading();
                renderActivitiesTable();
                showMessage('success', '活动数据加载完成');
            }, 1000);
        }
        
        function renderActivitiesTable() {
            const tbody = document.getElementById('activitiesTableBody');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredData.slice(startIndex, endIndex);
            
            if (pageData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-inbox" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>
                            暂无活动数据
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = pageData.map(activity => `
                <tr>
                    <td>
                        <input type="checkbox" value="${activity.id}" onchange="toggleActivitySelection(${activity.id})">
                    </td>
                    <td>
                        <div class="activity-name">${activity.name}</div>
                        <div class="activity-desc">${activity.description}</div>
                    </td>
                    <td>
                        <span class="activity-type type-${activity.type}">
                            ${getActivityTypeText(activity.type)}
                        </span>
                    </td>
                    <td>
                        <span class="activity-status status-${activity.status}">
                            ${getActivityStatusText(activity.status)}
                        </span>
                    </td>
                    <td>
                        <div style="font-size: 13px;">
                            <div>开始：${formatDateTime(activity.startTime)}</div>
                            <div>结束：${formatDateTime(activity.endTime)}</div>
                        </div>
                    </td>
                    <td>
                        <div class="activity-metrics">
                            <div class="metric-item">
                                <span>参与:</span>
                                <span class="metric-value">${activity.participants}</span>
                            </div>
                            <div class="metric-item">
                                <span>订单:</span>
                                <span class="metric-value">${activity.orders}</span>
                            </div>
                            <div class="metric-item">
                                <span>收入:</span>
                                <span class="metric-value">¥${activity.revenue.toLocaleString()}</span>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn edit" onclick="editActivity(${activity.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn toggle" onclick="toggleActivityStatus(${activity.id})" title="启用/暂停">
                                <i class="fas fa-${activity.status === 'active' ? 'pause' : 'play'}"></i>
                            </button>
                            <button class="action-btn delete" onclick="deleteActivity(${activity.id})" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            updatePaginationInfo();
        }
        
        function getActivityTypeText(type) {
            const typeMap = {
                'discount': '满减活动',
                'flash-sale': '限时抢购',
                'coupon': '优惠券',
                'festival': '节日营销',
                'buy-gift': '买赠活动'
            };
            return typeMap[type] || type;
        }
        
        function getActivityStatusText(status) {
            const statusMap = {
                'draft': '草稿',
                'active': '进行中',
                'paused': '已暂停',
                'ended': '已结束',
                'scheduled': '已预约'
            };
            return statusMap[status] || status;
        }
        
        function formatDateTime(dateTimeString) {
            const date = new Date(dateTimeString);
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        // 搜索和筛选功能
        function searchActivities() {
            const keyword = document.getElementById('searchInput').value.toLowerCase();
            applyFilters();
        }
        
        function filterActivities() {
            applyFilters();
        }
        
        function applyFilters() {
            const keyword = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            
            filteredData = activitiesData.filter(activity => {
                const matchKeyword = !keyword || 
                    activity.name.toLowerCase().includes(keyword) || 
                    activity.description.toLowerCase().includes(keyword);
                
                const matchStatus = !statusFilter || activity.status === statusFilter;
                const matchType = !typeFilter || activity.type === typeFilter;
                
                return matchKeyword && matchStatus && matchType;
            });
            
            currentPage = 1;
            renderActivitiesTable();
            setupPagination();
        }
        
        // 分页功能
        function setupPagination() {
            const totalPages = Math.ceil(filteredData.length / pageSize);
            const pagination = document.getElementById('pagination');
            
            let paginationHTML = '';
            
            // 上一页
            paginationHTML += `
                <button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
            
            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <button class="${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                            ${i}
                        </button>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += '<span>...</span>';
                }
            }
            
            // 下一页
            paginationHTML += `
                <button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
            
            pagination.innerHTML = paginationHTML;
        }
        
        function changePage(page) {
            const totalPages = Math.ceil(filteredData.length / pageSize);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderActivitiesTable();
                setupPagination();
            }
        }
        
        function updatePaginationInfo() {
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredData.length);
            
            document.getElementById('currentStart').textContent = filteredData.length > 0 ? startIndex : 0;
            document.getElementById('currentEnd').textContent = endIndex;
            document.getElementById('totalCount').textContent = filteredData.length;
        }
        
        // 批量操作功能
        function toggleActivitySelection(id) {
            if (selectedIds.has(id)) {
                selectedIds.delete(id);
            } else {
                selectedIds.add(id);
            }
            updateBulkActionsUI();
        }
        
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('#activitiesTableBody input[type="checkbox"]');
            
            if (selectAllCheckbox.checked) {
                checkboxes.forEach(cb => {
                    cb.checked = true;
                    selectedIds.add(parseInt(cb.value));
                });
            } else {
                checkboxes.forEach(cb => {
                    cb.checked = false;
                    selectedIds.delete(parseInt(cb.value));
                });
            }
            updateBulkActionsUI();
        }
        
        function updateBulkActionsUI() {
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            
            selectedCount.textContent = selectedIds.size;
            
            if (selectedIds.size > 0) {
                bulkActions.classList.add('show');
            } else {
                bulkActions.classList.remove('show');
            }
        }
        
        function clearSelection() {
            selectedIds.clear();
            document.querySelectorAll('#activitiesTableBody input[type="checkbox"]').forEach(cb => {
                cb.checked = false;
            });
            document.getElementById('selectAll').checked = false;
            updateBulkActionsUI();
        }
        
        function bulkEnable() {
            if (selectedIds.size === 0) return;
            
            showConfirm(
                '批量启用活动',
                `确定要启用选中的 ${selectedIds.size} 个活动吗？`,
                () => {
                    showLoading();
                    setTimeout(() => {
                        activitiesData.forEach(activity => {
                            if (selectedIds.has(activity.id) && activity.status !== 'ended') {
                                activity.status = 'active';
                            }
                        });
                        
                        hideLoading();
                        renderActivitiesTable();
                        clearSelection();
                        updateStatistics();
                        showMessage('success', `成功启用 ${selectedIds.size} 个活动`);
                    }, 1000);
                }
            );
        }
        
        function bulkDisable() {
            if (selectedIds.size === 0) return;
            
            showConfirm(
                '批量暂停活动',
                `确定要暂停选中的 ${selectedIds.size} 个活动吗？`,
                () => {
                    showLoading();
                    setTimeout(() => {
                        activitiesData.forEach(activity => {
                            if (selectedIds.has(activity.id) && activity.status === 'active') {
                                activity.status = 'paused';
                            }
                        });
                        
                        hideLoading();
                        renderActivitiesTable();
                        clearSelection();
                        updateStatistics();
                        showMessage('success', `成功暂停 ${selectedIds.size} 个活动`);
                    }, 1000);
                }
            );
        }
        
        function bulkDelete() {
            if (selectedIds.size === 0) return;
            
            showConfirm(
                '批量删除活动',
                `确定要删除选中的 ${selectedIds.size} 个活动吗？此操作不可恢复！`,
                () => {
                    showLoading();
                    setTimeout(() => {
                        activitiesData = activitiesData.filter(activity => !selectedIds.has(activity.id));
                        filteredData = filteredData.filter(activity => !selectedIds.has(activity.id));
                        
                        hideLoading();
                        renderActivitiesTable();
                        clearSelection();
                        updateStatistics();
                        setupPagination();
                        showMessage('success', `成功删除 ${selectedIds.size} 个活动`);
                    }, 1000);
                }
            );
        }
        
        // 单个活动操作
        function editActivity(id) {
            const activity = activitiesData.find(a => a.id === id);
            if (activity) {
                // 填充表单数据
                document.getElementById('activityName').value = activity.name;
                document.getElementById('activityType').value = activity.type;
                document.getElementById('activityDesc').value = activity.description;
                document.getElementById('startTime').value = activity.startTime.replace(' ', 'T');
                document.getElementById('endTime').value = activity.endTime.replace(' ', 'T');
                
                // 根据活动类型填充配置
                toggleActivityOptions();
                fillActivityConfig(activity);
                
                // 标记为编辑模式
                document.getElementById('createActivityForm').dataset.editId = id;
                document.querySelector('#createActivityModal .modal-header h3').innerHTML = '<i class="fas fa-edit"></i> 编辑促销活动';
                
                showModal('createActivityModal');
            }
        }
        
        function fillActivityConfig(activity) {
            const config = activity.config;
            
            switch (activity.type) {
                case 'discount':
                    document.getElementById('minAmount').value = config.minAmount || '';
                    document.getElementById('discountAmount').value = config.discountAmount || '';
                    break;
                case 'flash-sale':
                    document.getElementById('originalPrice').value = config.originalPrice || '';
                    document.getElementById('salePrice').value = config.salePrice || '';
                    document.getElementById('totalStock').value = config.totalStock || '';
                    document.getElementById('limitPerUser').value = config.limitPerUser || '';
                    break;
                case 'coupon':
                    document.getElementById('couponType').value = config.couponType || '';
                    document.getElementById('couponValue').value = config.couponValue || '';
                    document.getElementById('totalCoupons').value = config.totalCoupons || '';
                    document.getElementById('dailyLimit').value = config.dailyLimit || '';
                    break;
            }
        }
        
        function toggleActivityStatus(id) {
            const activity = activitiesData.find(a => a.id === id);
            if (activity) {
                const newStatus = activity.status === 'active' ? 'paused' : 'active';
                const actionText = newStatus === 'active' ? '启用' : '暂停';
                
                showConfirm(
                    `${actionText}活动`,
                    `确定要${actionText}活动"${activity.name}"吗？`,
                    () => {
                        activity.status = newStatus;
                        renderActivitiesTable();
                        updateStatistics();
                        showMessage('success', `活动${actionText}成功`);
                    }
                );
            }
        }
        
        function deleteActivity(id) {
            const activity = activitiesData.find(a => a.id === id);
            if (activity) {
                showConfirm(
                    '删除活动',
                    `确定要删除活动"${activity.name}"吗？此操作不可恢复！`,
                    () => {
                        activitiesData = activitiesData.filter(a => a.id !== id);
                        filteredData = filteredData.filter(a => a.id !== id);
                        renderActivitiesTable();
                        updateStatistics();
                        setupPagination();
                        showMessage('success', '活动删除成功');
                    }
                );
            }
        }
        
        // 模态框管理
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('show');
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
            document.body.style.overflow = '';
            
            // 重置创建活动表单
            if (modalId === 'createActivityModal') {
                document.getElementById('createActivityForm').reset();
                document.getElementById('createActivityForm').removeAttribute('data-edit-id');
                document.querySelector('#createActivityModal .modal-header h3').innerHTML = '<i class="fas fa-plus"></i> 创建促销活动';
                hideAllActivityOptions();
            }
        }
        
        function showCreateActivityModal() {
            closeModal('createActivityModal');
            setTimeout(() => showModal('createActivityModal'), 100);
        }
        
        function toggleActivityOptions() {
            const activityType = document.getElementById('activityType').value;
            hideAllActivityOptions();
            
            if (activityType) {
                const optionsId = activityType.replace('-', '') + 'Options';
                const optionsElement = document.getElementById(optionsId);
                if (optionsElement) {
                    optionsElement.style.display = 'block';
                }
            }
        }
        
        function hideAllActivityOptions() {
            const optionsElements = document.querySelectorAll('.activity-options');
            optionsElements.forEach(element => {
                element.style.display = 'none';
            });
        }
        
        function saveActivity() {
            const form = document.getElementById('createActivityForm');
            const formData = new FormData(form);
            
            // 基础验证
            if (!formData.get('activityName') || !formData.get('activityType')) {
                showMessage('error', '请填写必填字段');
                return;
            }
            
            showLoading();
            
            setTimeout(() => {
                const editId = form.dataset.editId;
                const isEdit = !!editId;
                
                const activityData = {
                    id: isEdit ? parseInt(editId) : Date.now(),
                    name: formData.get('activityName'),
                    type: formData.get('activityType'),
                    description: formData.get('activityDesc') || '',
                    startTime: formData.get('startTime').replace('T', ' ') + ':00',
                    endTime: formData.get('endTime').replace('T', ' ') + ':00',
                    status: formData.get('activityStatus') || 'draft',
                    participants: isEdit ? activitiesData.find(a => a.id === parseInt(editId)).participants : 0,
                    orders: isEdit ? activitiesData.find(a => a.id === parseInt(editId)).orders : 0,
                    revenue: isEdit ? activitiesData.find(a => a.id === parseInt(editId)).revenue : 0,
                    config: buildActivityConfig(formData)
                };
                
                if (isEdit) {
                    const index = activitiesData.findIndex(a => a.id === parseInt(editId));
                    activitiesData[index] = activityData;
                } else {
                    activitiesData.unshift(activityData);
                }
                
                // 更新筛选数据
                applyFilters();
                
                hideLoading();
                closeModal('createActivityModal');
                updateStatistics();
                showMessage('success', isEdit ? '活动更新成功' : '活动创建成功');
            }, 1000);
        }
        
        function buildActivityConfig(formData) {
            const type = formData.get('activityType');
            const config = {};
            
            switch (type) {
                case 'discount':
                    config.minAmount = parseFloat(formData.get('minAmount')) || 0;
                    config.discountAmount = parseFloat(formData.get('discountAmount')) || 0;
                    break;
                case 'flash-sale':
                    config.originalPrice = parseFloat(formData.get('originalPrice')) || 0;
                    config.salePrice = parseFloat(formData.get('salePrice')) || 0;
                    config.totalStock = parseInt(formData.get('totalStock')) || 0;
                    config.limitPerUser = parseInt(formData.get('limitPerUser')) || 1;
                    break;
                case 'coupon':
                    config.couponType = formData.get('couponType') || 'amount';
                    config.couponValue = parseFloat(formData.get('couponValue')) || 0;
                    config.totalCoupons = parseInt(formData.get('totalCoupons')) || 0;
                    config.dailyLimit = parseInt(formData.get('dailyLimit')) || 0;
                    break;
            }
            
            return config;
        }
        
        // 其他功能
        function exportActivities() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showMessage('success', '数据导出完成');
            }, 1500);
        }
        
        function refreshActivities() {
            loadActivitiesData();
        }
        
        function showBatchActions() {
            if (selectedIds.size > 0) {
                document.getElementById('bulkActions').classList.add('show');
            } else {
                showMessage('warning', '请先选择要操作的活动');
            }
        }
        
        // 工具卡片功能
        function loadCouponManagement() {
            showMessage('info', '正在跳转到优惠券管理页面...');
            setTimeout(() => {
                window.location.href = 'coupon-management.html';
            }, 1000);
        }
        
        // ===== 限时抢购管理功能 =====
        let flashSalesData = [];
        let filteredFlashSalesData = [];
        let selectedFlashSaleIds = new Set();
        let flashSaleCurrentPage = 1;
        let flashSaleItemsPerPage = 10;
        let monitorInterval = null;
        let autoRefreshEnabled = false;
        let refreshCountdown = 5;
        
        // 模拟抢购数据
        const mockFlashSalesData = [
            {
                id: 1,
                name: 'iPhone 15 Pro 年终大促',
                productId: 1,
                productName: 'iPhone 15 Pro',
                productImage: '../../assets/images/product-default.jpg',
                originalPrice: 7999,
                flashPrice: 6999,
                totalStock: 100,
                soldStock: 85,
                limitPerUser: 1,
                startTime: '2024-01-15 10:00:00',
                endTime: '2024-01-15 22:00:00',
                participants: 1250,
                orders: 85,
                revenue: 594915,
                status: 'active',
                enablePreview: true,
                enableQueue: true,
                enableCaptcha: false,
                enableRealName: true
            },
            {
                id: 2,
                name: 'MacBook Air M2 限时抢购',
                productId: 2,
                productName: 'MacBook Air M2',
                productImage: '../../assets/images/product-default.jpg',
                originalPrice: 8999,
                flashPrice: 7899,
                totalStock: 50,
                soldStock: 12,
                limitPerUser: 1,
                startTime: '2024-01-20 14:00:00',
                endTime: '2024-01-20 16:00:00',
                participants: 320,
                orders: 12,
                revenue: 94788,
                status: 'waiting',
                enablePreview: true,
                enableQueue: true,
                enableCaptcha: true,
                enableRealName: true
            },
            {
                id: 3,
                name: 'AirPods Pro 2 闪购',
                productId: 3,
                productName: 'AirPods Pro 2',
                productImage: '../../assets/images/product-default.jpg',
                originalPrice: 1899,
                flashPrice: 1599,
                totalStock: 200,
                soldStock: 200,
                limitPerUser: 2,
                startTime: '2024-01-10 12:00:00',
                endTime: '2024-01-10 14:00:00',
                participants: 856,
                orders: 200,
                revenue: 319800,
                status: 'ended',
                enablePreview: false,
                enableQueue: true,
                enableCaptcha: false,
                enableRealName: false
            }
        ];
        
        function showFlashSaleManagement() {
            flashSalesData = [...mockFlashSalesData];
            filteredFlashSalesData = [...flashSalesData];
            renderFlashSalesTable();
            setupFlashSalePagination();
            showModal('flashSaleManagementModal');
        }
        
        function renderFlashSalesTable() {
            const tbody = document.getElementById('flashSalesTableBody');
            const startIndex = (flashSaleCurrentPage - 1) * flashSaleItemsPerPage;
            const endIndex = startIndex + flashSaleItemsPerPage;
            const pageData = filteredFlashSalesData.slice(startIndex, endIndex);
            
            tbody.innerHTML = pageData.map(flashSale => {
                const discount = Math.round((1 - flashSale.flashPrice / flashSale.originalPrice) * 100);
                const stockPercent = Math.round((flashSale.soldStock / flashSale.totalStock) * 100);
                const conversionRate = flashSale.participants > 0 ? Math.round((flashSale.orders / flashSale.participants) * 100) : 0;
                
                return `
                    <tr>
                        <td><input type="checkbox" onchange="toggleFlashSaleSelection(${flashSale.id})"></td>
                        <td>
                            <div class="flash-sale-info">
                                <div class="flash-sale-name">${flashSale.name}</div>
                                <div class="flash-sale-id">ID: ${flashSale.id}</div>
                            </div>
                        </td>
                        <td>
                            <div class="product-info">
                                <img src="${flashSale.productImage}" alt="${flashSale.productName}" class="product-thumb">
                                <div class="product-details">
                                    <div class="product-name">${flashSale.productName}</div>
                                    <div class="product-sku">SKU: ${flashSale.productId}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="price-stock">
                                <div class="price-comparison">
                                    <span class="original-price">¥${flashSale.originalPrice}</span>
                                    <span class="flash-price">¥${flashSale.flashPrice}</span>
                                    <span style="color: #e53e3e; font-size: 12px;">${discount}%OFF</span>
                                </div>
                                <div class="stock-info">库存: ${flashSale.totalStock - flashSale.soldStock}/${flashSale.totalStock} (${stockPercent}%已抢)</div>
                            </div>
                        </td>
                        <td>
                            <div class="time-info">
                                <div class="time-range">${flashSale.startTime} 至<br>${flashSale.endTime}</div>
                                <div class="time-status">${getTimeStatus(flashSale)}</div>
                            </div>
                        </td>
                        <td>
                            <div class="participation-data">
                                <div class="data-item"><span>参与:</span><span>${flashSale.participants}</span></div>
                                <div class="data-item"><span>订单:</span><span>${flashSale.orders}</span></div>
                                <div class="data-item"><span>成交:</span><span>¥${flashSale.revenue.toLocaleString()}</span></div>
                                <div class="data-item"><span>转化:</span><span>${conversionRate}%</span></div>
                            </div>
                        </td>
                        <td>
                            <span class="flash-sale-status status-${flashSale.status}">
                                ${getFlashSaleStatusText(flashSale.status)}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action" onclick="editFlashSale(${flashSale.id})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action" onclick="viewFlashSaleDetails(${flashSale.id})" title="详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${flashSale.status === 'active' ? 
                                    `<button class="btn-action" onclick="pauseFlashSale(${flashSale.id})" title="暂停">
                                        <i class="fas fa-pause"></i>
                                    </button>` : 
                                    flashSale.status === 'waiting' ? 
                                    `<button class="btn-action" onclick="startFlashSale(${flashSale.id})" title="开启">
                                        <i class="fas fa-play"></i>
                                    </button>` : ''
                                }
                                <button class="btn-action danger" onclick="deleteFlashSale(${flashSale.id})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }
        
        function getTimeStatus(flashSale) {
            const now = new Date();
            const startTime = new Date(flashSale.startTime);
            const endTime = new Date(flashSale.endTime);
            
            if (now < startTime) {
                const diff = Math.ceil((startTime - now) / (1000 * 60 * 60));
                return `${diff}小时后开始`;
            } else if (now >= startTime && now <= endTime) {
                const diff = Math.ceil((endTime - now) / (1000 * 60 * 60));
                return `${diff}小时后结束`;
            } else {
                return '已结束';
            }
        }
        
        function getFlashSaleStatusText(status) {
            const statusMap = {
                'waiting': '待开始',
                'active': '进行中',
                'ended': '已结束',
                'canceled': '已取消'
            };
            return statusMap[status] || status;
        }
        
        function setupFlashSalePagination() {
            const totalPages = Math.ceil(filteredFlashSalesData.length / flashSaleItemsPerPage);
            const pagination = document.getElementById('flashSalePagination');
            const startRecord = (flashSaleCurrentPage - 1) * flashSaleItemsPerPage + 1;
            const endRecord = Math.min(startRecord + flashSaleItemsPerPage - 1, filteredFlashSalesData.length);
            
            document.getElementById('flashSaleCurrentStart').textContent = startRecord;
            document.getElementById('flashSaleCurrentEnd').textContent = endRecord;
            document.getElementById('flashSaleTotalCount').textContent = filteredFlashSalesData.length;
            
            pagination.innerHTML = '';
            
            // 上一页
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.disabled = flashSaleCurrentPage === 1;
            prevBtn.onclick = () => {
                if (flashSaleCurrentPage > 1) {
                    flashSaleCurrentPage--;
                    renderFlashSalesTable();
                    setupFlashSalePagination();
                }
            };
            pagination.appendChild(prevBtn);
            
            // 页码
            for (let i = 1; i <= totalPages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === flashSaleCurrentPage ? 'active' : '';
                pageBtn.onclick = () => {
                    flashSaleCurrentPage = i;
                    renderFlashSalesTable();
                    setupFlashSalePagination();
                };
                pagination.appendChild(pageBtn);
            }
            
            // 下一页
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.disabled = flashSaleCurrentPage === totalPages;
            nextBtn.onclick = () => {
                if (flashSaleCurrentPage < totalPages) {
                    flashSaleCurrentPage++;
                    renderFlashSalesTable();
                    setupFlashSalePagination();
                }
            };
            pagination.appendChild(nextBtn);
        }
        
        function searchFlashSales() {
            const query = document.getElementById('flashSaleSearch').value.toLowerCase();
            filteredFlashSalesData = flashSalesData.filter(flashSale => 
                flashSale.name.toLowerCase().includes(query) ||
                flashSale.productName.toLowerCase().includes(query)
            );
            flashSaleCurrentPage = 1;
            renderFlashSalesTable();
            setupFlashSalePagination();
        }
        
        function filterFlashSales() {
            const statusFilter = document.getElementById('flashSaleStatusFilter').value;
            const searchQuery = document.getElementById('flashSaleSearch').value.toLowerCase();
            
            filteredFlashSalesData = flashSalesData.filter(flashSale => {
                const matchesStatus = !statusFilter || flashSale.status === statusFilter;
                const matchesSearch = !searchQuery || 
                    flashSale.name.toLowerCase().includes(searchQuery) ||
                    flashSale.productName.toLowerCase().includes(searchQuery);
                return matchesStatus && matchesSearch;
            });
            
            flashSaleCurrentPage = 1;
            renderFlashSalesTable();
            setupFlashSalePagination();
        }
        
        function toggleSelectAllFlashSales() {
            const selectAll = document.getElementById('selectAllFlashSales');
            const checkboxes = document.querySelectorAll('#flashSalesTableBody input[type="checkbox"]');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                const flashSaleId = parseInt(checkbox.onchange.toString().match(/\d+/)[0]);
                if (selectAll.checked) {
                    selectedFlashSaleIds.add(flashSaleId);
                } else {
                    selectedFlashSaleIds.delete(flashSaleId);
                }
            });
        }
        
        function toggleFlashSaleSelection(flashSaleId) {
            if (selectedFlashSaleIds.has(flashSaleId)) {
                selectedFlashSaleIds.delete(flashSaleId);
            } else {
                selectedFlashSaleIds.add(flashSaleId);
            }
        }
        
        function showCreateFlashSaleModal() {
            document.getElementById('createFlashSaleForm').reset();
            showModal('createFlashSaleModal');
        }
        
        function createFlashSale() {
            showCreateFlashSaleModal();
        }
        
        function saveFlashSale() {
            const form = document.getElementById('createFlashSaleForm');
            const formData = new FormData(form);
            
            // 基础验证
            if (!formData.get('flashSaleName') || !formData.get('flashSaleProduct') || 
                !formData.get('originalPrice') || !formData.get('flashSalePrice')) {
                showMessage('error', '请填写必填字段');
                return;
            }
            
            const originalPrice = parseFloat(formData.get('originalPrice'));
            const flashPrice = parseFloat(formData.get('flashSalePrice'));
            
            if (flashPrice >= originalPrice) {
                showMessage('error', '抢购价必须低于原价');
                return;
            }
            
            showLoading();
            
            setTimeout(() => {
                const newFlashSale = {
                    id: Date.now(),
                    name: formData.get('flashSaleName'),
                    productId: parseInt(formData.get('flashSaleProduct')),
                    productName: getProductName(formData.get('flashSaleProduct')),
                    productImage: '../../assets/images/product-default.jpg',
                    originalPrice: originalPrice,
                    flashPrice: flashPrice,
                    totalStock: parseInt(formData.get('totalStock')),
                    soldStock: 0,
                    limitPerUser: parseInt(formData.get('limitPerUser')),
                    startTime: formData.get('flashStartTime').replace('T', ' '),
                    endTime: formData.get('flashEndTime').replace('T', ' '),
                    participants: 0,
                    orders: 0,
                    revenue: 0,
                    status: 'waiting',
                    enablePreview: form.querySelector('[name="enablePreview"]').checked,
                    enableQueue: form.querySelector('[name="enableQueue"]').checked,
                    enableCaptcha: form.querySelector('[name="enableCaptcha"]').checked,
                    enableRealName: form.querySelector('[name="enableRealName"]').checked,
                    description: formData.get('flashSaleDescription')
                };
                
                flashSalesData.unshift(newFlashSale);
                filteredFlashSalesData = [...flashSalesData];
                renderFlashSalesTable();
                setupFlashSalePagination();
                
                hideLoading();
                closeModal('createFlashSaleModal');
                showMessage('success', '限时抢购创建成功');
            }, 1000);
        }
        
        function getProductName(productId) {
            const products = {
                '1': 'iPhone 15 Pro',
                '2': 'MacBook Air M2',
                '3': 'AirPods Pro 2',
                '4': 'iPad Air 5'
            };
            return products[productId] || '未知商品';
        }
        
        function editFlashSale(id) {
            const flashSale = flashSalesData.find(f => f.id === id);
            if (!flashSale) return;
            
            // 填充编辑表单
            document.getElementById('flashSaleName').value = flashSale.name;
            document.getElementById('flashSaleProduct').value = flashSale.productId;
            document.getElementById('originalPrice').value = flashSale.originalPrice;
            document.getElementById('flashSalePrice').value = flashSale.flashPrice;
            document.getElementById('totalStock').value = flashSale.totalStock;
            document.getElementById('limitPerUser').value = flashSale.limitPerUser;
            
            const startTime = new Date(flashSale.startTime).toISOString().slice(0, 16);
            const endTime = new Date(flashSale.endTime).toISOString().slice(0, 16);
            document.getElementById('flashStartTime').value = startTime;
            document.getElementById('flashEndTime').value = endTime;
            
            document.getElementById('flashSaleDescription').value = flashSale.description || '';
            
            // 设置规则复选框
            const form = document.getElementById('createFlashSaleForm');
            form.querySelector('[name="enablePreview"]').checked = flashSale.enablePreview;
            form.querySelector('[name="enableQueue"]').checked = flashSale.enableQueue;
            form.querySelector('[name="enableCaptcha"]').checked = flashSale.enableCaptcha;
            form.querySelector('[name="enableRealName"]').checked = flashSale.enableRealName;
            
            // 修改保存按钮
            const saveBtn = document.querySelector('#createFlashSaleModal .btn-primary');
            saveBtn.textContent = '更新抢购';
            saveBtn.onclick = () => updateFlashSale(id);
            
            showModal('createFlashSaleModal');
        }
        
        function updateFlashSale(id) {
            const form = document.getElementById('createFlashSaleForm');
            const formData = new FormData(form);
            
            showLoading();
            
            setTimeout(() => {
                const flashSaleIndex = flashSalesData.findIndex(f => f.id === id);
                if (flashSaleIndex !== -1) {
                    flashSalesData[flashSaleIndex] = {
                        ...flashSalesData[flashSaleIndex],
                        name: formData.get('flashSaleName'),
                        productId: parseInt(formData.get('flashSaleProduct')),
                        productName: getProductName(formData.get('flashSaleProduct')),
                        originalPrice: parseFloat(formData.get('originalPrice')),
                        flashPrice: parseFloat(formData.get('flashSalePrice')),
                        totalStock: parseInt(formData.get('totalStock')),
                        limitPerUser: parseInt(formData.get('limitPerUser')),
                        startTime: formData.get('flashStartTime').replace('T', ' '),
                        endTime: formData.get('flashEndTime').replace('T', ' '),
                        enablePreview: form.querySelector('[name="enablePreview"]').checked,
                        enableQueue: form.querySelector('[name="enableQueue"]').checked,
                        enableCaptcha: form.querySelector('[name="enableCaptcha"]').checked,
                        enableRealName: form.querySelector('[name="enableRealName"]').checked,
                        description: formData.get('flashSaleDescription')
                    };
                    
                    filteredFlashSalesData = [...flashSalesData];
                    renderFlashSalesTable();
                    setupFlashSalePagination();
                }
                
                hideLoading();
                closeModal('createFlashSaleModal');
                showMessage('success', '限时抢购更新成功');
                
                // 重置保存按钮
                const saveBtn = document.querySelector('#createFlashSaleModal .btn-primary');
                saveBtn.textContent = '创建抢购';
                saveBtn.onclick = saveFlashSale;
            }, 1000);
        }
        
        function deleteFlashSale(id) {
            const flashSale = flashSalesData.find(f => f.id === id);
            if (flashSale) {
                showConfirm(
                    '删除限时抢购',
                    `确定要删除抢购活动"${flashSale.name}"吗？`,
                    () => {
                        flashSalesData = flashSalesData.filter(f => f.id !== id);
                        filteredFlashSalesData = filteredFlashSalesData.filter(f => f.id !== id);
                        renderFlashSalesTable();
                        setupFlashSalePagination();
                        showMessage('success', '限时抢购删除成功');
                    }
                );
            }
        }
        
        function startFlashSale(id) {
            const flashSale = flashSalesData.find(f => f.id === id);
            if (flashSale) {
                showConfirm(
                    '开启抢购',
                    `确定要立即开启"${flashSale.name}"吗？`,
                    () => {
                        const flashSaleIndex = flashSalesData.findIndex(f => f.id === id);
                        flashSalesData[flashSaleIndex].status = 'active';
                        filteredFlashSalesData = [...flashSalesData];
                        renderFlashSalesTable();
                        showMessage('success', '抢购活动已开启');
                    }
                );
            }
        }
        
        function pauseFlashSale(id) {
            const flashSale = flashSalesData.find(f => f.id === id);
            if (flashSale) {
                showConfirm(
                    '暂停抢购',
                    `确定要暂停"${flashSale.name}"吗？`,
                    () => {
                        const flashSaleIndex = flashSalesData.findIndex(f => f.id === id);
                        flashSalesData[flashSaleIndex].status = 'canceled';
                        filteredFlashSalesData = [...flashSalesData];
                        renderFlashSalesTable();
                        showMessage('success', '抢购活动已暂停');
                    }
                );
            }
        }
        
        function batchManageFlashSales() {
            if (selectedFlashSaleIds.size === 0) {
                showMessage('warning', '请先选择要管理的抢购活动');
                return;
            }
            
            // 显示批量管理模态框
            showModal('batchFlashSaleModal');
            
            // 更新选择的活动数量
            document.getElementById('selectedFlashSaleCount').textContent = selectedFlashSaleIds.size;
            
            // 显示选择的活动列表
            displaySelectedFlashSales();
        }
        
        function exportFlashSales() {
            showMessage('info', '正在导出抢购数据...');
            // 模拟导出功能
            setTimeout(() => {
                showMessage('success', '抢购数据导出成功');
            }, 1500);
        }
        
        // 显示选中的抢购活动列表
        function displaySelectedFlashSales() {
            const container = document.getElementById('selectedActivitiesList');
            container.innerHTML = '';
            
            // 模拟选中的活动数据
            const selectedActivities = [
                {
                    id: 1,
                    name: 'iPhone 15 Pro 限时抢购',
                    product: 'iPhone 15 Pro',
                    price: '¥6999',
                    stock: 100,
                    status: 'active',
                    startTime: '2024-01-15 10:00'
                },
                {
                    id: 2,
                    name: 'MacBook Air M2 新年特惠',
                    product: 'MacBook Air M2',
                    price: '¥7999',
                    stock: 50,
                    status: 'pending',
                    startTime: '2024-01-20 09:00'
                }
            ];
            
            selectedActivities.forEach(activity => {
                const activityItem = document.createElement('div');
                activityItem.className = 'activity-item';
                activityItem.innerHTML = `
                    <div class="activity-basic-info">
                        <div class="activity-name">${activity.name}</div>
                        <div class="activity-meta">
                            <span>商品：${activity.product}</span>
                            <span>价格：${activity.price}</span>
                            <span>库存：${activity.stock}</span>
                            <span>开始：${activity.startTime}</span>
                        </div>
                    </div>
                    <div class="activity-controls">
                        <span class="activity-status status-${activity.status}">
                            ${activity.status === 'active' ? '进行中' : activity.status === 'pending' ? '待开始' : '已结束'}
                        </span>
                        <button class="btn btn-sm btn-secondary" onclick="removeFromSelection(${activity.id})">
                            <i class="fas fa-times"></i> 移除
                        </button>
                    </div>
                `;
                container.appendChild(activityItem);
            });
        }
        
        // 从选择中移除活动
        function removeFromSelection(activityId) {
            selectedFlashSaleIds.delete(activityId);
            document.getElementById('selectedFlashSaleCount').textContent = selectedFlashSaleIds.size;
            displaySelectedFlashSales();
            
            if (selectedFlashSaleIds.size === 0) {
                closeModal('batchFlashSaleModal');
                showMessage('info', '已清空选择列表');
            }
        }
        
        // 批量更新状态
        function batchUpdateStatus(status) {
            if (!confirm(`确定要批量${status === 'active' ? '启用' : '暂停'}选中的抢购活动吗？`)) {
                return;
            }
            
            showLoadingOverlay();
            
            setTimeout(() => {
                hideLoadingOverlay();
                closeModal('batchFlashSaleModal');
                showMessage('success', `已成功${status === 'active' ? '启用' : '暂停'} ${selectedFlashSaleIds.size} 个抢购活动`);
                selectedFlashSaleIds.clear();
                // 这里应该调用API更新数据库
                loadFlashSaleManagement();
            }, 2000);
        }
        
        // 批量更新库存
        function batchUpdateStock() {
            const newStock = prompt('请输入新的库存数量：');
            if (!newStock || isNaN(newStock) || newStock < 0) {
                showMessage('warning', '请输入有效的库存数量');
                return;
            }
            
            if (!confirm(`确定要将选中的 ${selectedFlashSaleIds.size} 个抢购活动的库存设置为 ${newStock} 吗？`)) {
                return;
            }
            
            showLoadingOverlay();
            
            setTimeout(() => {
                hideLoadingOverlay();
                closeModal('batchFlashSaleModal');
                showMessage('success', `已成功更新 ${selectedFlashSaleIds.size} 个抢购活动的库存`);
                selectedFlashSaleIds.clear();
                loadFlashSaleManagement();
            }, 2000);
        }
        
        // 批量调整时间
        function batchUpdateTime() {
            const newStartTime = prompt('请输入新的开始时间（格式：YYYY-MM-DD HH:MM）：');
            if (!newStartTime) {
                return;
            }
            
            const newEndTime = prompt('请输入新的结束时间（格式：YYYY-MM-DD HH:MM）：');
            if (!newEndTime) {
                return;
            }
            
            if (!confirm(`确定要批量调整选中活动的时间吗？\n开始时间：${newStartTime}\n结束时间：${newEndTime}`)) {
                return;
            }
            
            showLoadingOverlay();
            
            setTimeout(() => {
                hideLoadingOverlay();
                closeModal('batchFlashSaleModal');
                showMessage('success', `已成功更新 ${selectedFlashSaleIds.size} 个抢购活动的时间`);
                selectedFlashSaleIds.clear();
                loadFlashSaleManagement();
            }, 2000);
        }
        
        // 批量复制活动
        function batchCopyActivities() {
            if (!confirm(`确定要复制选中的 ${selectedFlashSaleIds.size} 个抢购活动吗？`)) {
                return;
            }
            
            showLoadingOverlay();
            
            setTimeout(() => {
                hideLoadingOverlay();
                closeModal('batchFlashSaleModal');
                showMessage('success', `已成功复制 ${selectedFlashSaleIds.size} 个抢购活动`);
                selectedFlashSaleIds.clear();
                loadFlashSaleManagement();
            }, 2000);
        }
        
        // 批量删除活动
        function batchDeleteActivities() {
            if (!confirm(`确定要删除选中的 ${selectedFlashSaleIds.size} 个抢购活动吗？\n此操作不可恢复！`)) {
                return;
            }
            
            showLoadingOverlay();
            
            setTimeout(() => {
                hideLoadingOverlay();
                closeModal('batchFlashSaleModal');
                showMessage('success', `已成功删除 ${selectedFlashSaleIds.size} 个抢购活动`);
                selectedFlashSaleIds.clear();
                loadFlashSaleManagement();
            }, 2000);
        }
        
        // 更新商品信息（创建抢购时的辅助函数）
        function updateProductInfo() {
            const selectElement = document.getElementById('flashSaleProduct');
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            const originalPriceInput = document.getElementById('originalPrice');
            const flashSalePriceInput = document.getElementById('flashSalePrice');
            const discountInfo = document.getElementById('discountInfo');
            
            if (selectedOption.value) {
                const price = selectedOption.getAttribute('data-price');
                originalPriceInput.value = price;
                
                // 监听抢购价格变化，计算折扣
                flashSalePriceInput.oninput = function() {
                    const originalPrice = parseFloat(originalPriceInput.value);
                    const flashPrice = parseFloat(this.value);
                    
                    if (originalPrice && flashPrice) {
                        const discount = ((originalPrice - flashPrice) / originalPrice * 100).toFixed(1);
                        const savings = (originalPrice - flashPrice).toFixed(2);
                        discountInfo.innerHTML = `
                            <i class="fas fa-tag"></i>
                            优惠 ¥${savings} (${discount}% 折扣)
                        `;
                        discountInfo.style.color = flashPrice < originalPrice ? '#e53e3e' : '#718096';
                    } else {
                        discountInfo.innerHTML = '';
                    }
                };
            } else {
                originalPriceInput.value = '';
                flashSalePriceInput.value = '';
                discountInfo.innerHTML = '';
            }
        }
        
        // 监听时间变化，计算活动时长
        document.addEventListener('DOMContentLoaded', function() {
            const startTimeInput = document.getElementById('flashStartTime');
            const endTimeInput = document.getElementById('flashEndTime');
            const durationInfo = document.getElementById('durationInfo');
            
            function updateDuration() {
                const startTime = new Date(startTimeInput.value);
                const endTime = new Date(endTimeInput.value);
                
                if (startTime && endTime && endTime > startTime) {
                    const duration = (endTime - startTime) / (1000 * 60 * 60); // 小时
                    durationInfo.textContent = `活动时长：${duration.toFixed(1)} 小时`;
                    durationInfo.style.color = duration <= 24 ? '#48bb78' : '#ed8936';
                } else {
                    durationInfo.textContent = '活动时长：未设置';
                    durationInfo.style.color = '#718096';
                }
            }
            
            if (startTimeInput && endTimeInput) {
                startTimeInput.addEventListener('change', updateDuration);
                endTimeInput.addEventListener('change', updateDuration);
            }
        });
        
        function viewFlashSaleDetails(id) {
            const flashSale = flashSalesData.find(f => f.id === id);
            if (flashSale) {
                showMessage('info', `查看抢购活动"${flashSale.name}"详情功能开发中...`);
            }
        }

        function loadFlashSaleManagement() {
            showFlashSaleManagement();
        }
        
        // ===== 限时抢购实时监控功能 =====
        let flashSaleMonitorInterval = null;
        let monitorData = {};
        
        function showFlashSaleMonitor() {
            initializeMonitorData();
            startRealTimeMonitoring();
            showModal('flashSaleMonitorModal');
        }
        
        function initializeMonitorData() {
            monitorData = {
                activeFlashSales: 3,
                totalParticipants: 8567,
                totalRevenue: 256789.50,
                avgConversionRate: 45.8,
                systemLoad: 78,
                errorRate: 0.2,
                hotActivities: [
                    {
                        id: 1,
                        name: 'iPhone 15 Pro 限时抢购',
                        participants: 3245,
                        progress: 85,
                        status: 'hot',
                        timeLeft: '2小时15分'
                    },
                    {
                        id: 2,
                        name: 'MacBook Air M2 特惠',
                        participants: 2156,
                        progress: 68,
                        status: 'normal',
                        timeLeft: '4小时30分'
                    },
                    {
                        id: 3,
                        name: 'AirPods Pro 2代',
                        participants: 1876,
                        progress: 92,
                        status: 'ending',
                        timeLeft: '45分钟'
                    }
                ],
                recentOrders: [
                    { time: '14:32:15', user: 'user_***1234', product: 'iPhone 15 Pro', amount: 7999 },
                    { time: '14:31:58', user: 'user_***5678', product: 'MacBook Air', amount: 8999 },
                    { time: '14:31:42', user: 'user_***9012', product: 'AirPods Pro', amount: 1899 },
                    { time: '14:31:28', user: 'user_***3456', product: 'iPhone 15 Pro', amount: 7999 },
                    { time: '14:31:15', user: 'user_***7890', product: 'AirPods Pro', amount: 1899 }
                ],
                alerts: []
            };
            
            updateMonitorDisplay();
        }
        
        function startRealTimeMonitoring() {
            if (flashSaleMonitorInterval) {
                clearInterval(flashSaleMonitorInterval);
            }
            
            flashSaleMonitorInterval = setInterval(() => {
                updateRealTimeData();
                updateMonitorDisplay();
                checkAlerts();
            }, 3000); // 每3秒更新一次
        }
        
        function stopRealTimeMonitoring() {
            if (flashSaleMonitorInterval) {
                clearInterval(flashSaleMonitorInterval);
                flashSaleMonitorInterval = null;
            }
        }
        
        function updateRealTimeData() {
            // 模拟实时数据变化
            monitorData.totalParticipants += Math.floor(Math.random() * 20);
            monitorData.totalRevenue += Math.random() * 5000;
            monitorData.avgConversionRate = Math.max(40, Math.min(60, monitorData.avgConversionRate + (Math.random() - 0.5) * 2));
            monitorData.systemLoad = Math.max(50, Math.min(95, monitorData.systemLoad + (Math.random() - 0.5) * 10));
            monitorData.errorRate = Math.max(0, Math.min(5, monitorData.errorRate + (Math.random() - 0.5) * 0.5));
            
            // 更新热门活动参与人数
            monitorData.hotActivities.forEach(activity => {
                activity.participants += Math.floor(Math.random() * 10);
                activity.progress = Math.min(100, activity.progress + Math.random() * 2);
                
                // 更新倒计时（简化处理）
                const timeLeft = activity.timeLeft;
                if (timeLeft.includes('小时')) {
                    const hours = parseInt(timeLeft.match(/(\d+)小时/)[1]);
                    const minutes = parseInt(timeLeft.match(/(\d+)分/)?.[1] || 0);
                    const totalMinutes = hours * 60 + minutes - 1;
                    if (totalMinutes <= 0) {
                        activity.timeLeft = '已结束';
                        activity.status = 'ended';
                    } else {
                        const newHours = Math.floor(totalMinutes / 60);
                        const newMinutes = totalMinutes % 60;
                        activity.timeLeft = `${newHours}小时${newMinutes}分`;
                    }
                } else if (timeLeft.includes('分钟')) {
                    const minutes = parseInt(timeLeft.match(/(\d+)分钟/)[1]) - 1;
                    if (minutes <= 0) {
                        activity.timeLeft = '已结束';
                        activity.status = 'ended';
                    } else {
                        activity.timeLeft = `${minutes}分钟`;
                    }
                }
            });
            
            // 模拟新订单
            if (Math.random() < 0.3) {
                const newOrder = {
                    time: new Date().toLocaleTimeString(),
                    user: 'user_***' + Math.floor(Math.random() * 9999),
                    product: ['iPhone 15 Pro', 'MacBook Air', 'AirPods Pro'][Math.floor(Math.random() * 3)],
                    amount: [7999, 8999, 1899][Math.floor(Math.random() * 3)]
                };
                monitorData.recentOrders.unshift(newOrder);
                monitorData.recentOrders = monitorData.recentOrders.slice(0, 5);
            }
        }
        
        function updateMonitorDisplay() {
            // 更新概览数据
            document.getElementById('activeFlashSalesCount').textContent = monitorData.activeFlashSales;
            const currentParticipantsElement = document.getElementById('currentParticipants');
            const todayOrdersElement = document.getElementById('todayOrders');
            const todayRevenueElement = document.getElementById('todayRevenue');
            
            if (currentParticipantsElement) {
                currentParticipantsElement.textContent = monitorData.totalParticipants.toLocaleString();
            }
            if (todayOrdersElement) {
                todayOrdersElement.textContent = Math.floor(monitorData.totalParticipants * monitorData.avgConversionRate / 100).toLocaleString();
            }
            if (todayRevenueElement) {
                todayRevenueElement.textContent = `¥${monitorData.totalRevenue.toLocaleString()}`;
            }
            
            // 更新热门活动
            updateHotActivitiesDisplay();
            
            // 更新警报信息
            updateAlertsDisplay();
        }
        
        function updateHotActivitiesDisplay() {
            const container = document.getElementById('liveActivitiesGrid');
            if (!container) return;
            
            container.innerHTML = monitorData.hotActivities.map(activity => `
                <div class="live-activity-card status-${activity.status}">
                    <div class="activity-header">
                        <h4>${activity.name}</h4>
                        <span class="activity-status status-${activity.status}">${getActivityStatusText(activity.status)}</span>
                    </div>
                    <div class="activity-stats">
                        <div class="stat-row">
                            <span class="stat-label">参与人数:</span>
                            <span class="stat-value">${activity.participants.toLocaleString()}</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">完成进度:</span>
                            <span class="stat-value">${activity.progress.toFixed(1)}%</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">剩余时间:</span>
                            <span class="stat-value time-left">${activity.timeLeft}</span>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${activity.progress}%"></div>
                    </div>
                </div>
            `).join('');
        }
        

        
        function getActivityStatusText(status) {
            const statusMap = {
                'hot': '火爆',
                'normal': '正常',
                'ending': '即将结束',
                'ended': '已结束'
            };
            return statusMap[status] || status;
        }
        
        function checkAlerts() {
            monitorData.alerts = [];
            
            // 检查系统负载
            if (monitorData.systemLoad > 90) {
                monitorData.alerts.push({
                    type: 'error',
                    message: `系统负载过高: ${monitorData.systemLoad.toFixed(1)}%`,
                    time: new Date().toLocaleTimeString()
                });
            }
            
            // 检查错误率
            if (monitorData.errorRate > 2) {
                monitorData.alerts.push({
                    type: 'warning',
                    message: `错误率较高: ${monitorData.errorRate.toFixed(2)}%`,
                    time: new Date().toLocaleTimeString()
                });
            }
            
            // 检查活动状态
            monitorData.hotActivities.forEach(activity => {
                if (activity.status === 'ending' && activity.timeLeft.includes('分钟')) {
                    const minutes = parseInt(activity.timeLeft.match(/(\d+)分钟/)?.[1] || 0);
                    if (minutes <= 30) {
                        monitorData.alerts.push({
                            type: 'info',
                            message: `活动"${activity.name}"即将结束`,
                            time: new Date().toLocaleTimeString()
                        });
                    }
                }
            });
            
            updateAlertsDisplay();
        }
        
        function updateAlertsDisplay() {
            const container = document.getElementById('alertList');
            if (!container) return;
            
            if (monitorData.alerts.length === 0) {
                container.innerHTML = '<div class="no-alerts">当前无异常警报</div>';
                return;
            }
            
            container.innerHTML = monitorData.alerts.map(alert => `
                <div class="alert-item alert-${alert.type}">
                    <div class="alert-icon">
                        <i class="fas fa-${alert.type === 'error' ? 'exclamation-triangle' : alert.type === 'warning' ? 'exclamation-circle' : 'info-circle'}"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-message">${alert.message}</div>
                        <div class="alert-time">${alert.time}</div>
                    </div>
                </div>
            `).join('');
        }
        
        // 监控模态框关闭时停止实时更新
        function closeFlashSaleMonitor() {
            stopRealTimeMonitoring();
            closeModal('flashSaleMonitorModal');
        }
        
        function loadDiscountRules() {
            showMessage('info', '正在跳转到满减活动配置页面...');
            setTimeout(() => {
                window.location.href = 'discount-rules.html';
            }, 1000);
        }
        
        function loadFestivalPlanning() {
            showMessage('info', '正在跳转到节日营销策划页面...');
            setTimeout(() => {
                window.location.href = 'festival-marketing.html';
            }, 1000);
        }
        
        // 工具函数
        function showLoading() {
            document.getElementById('loadingOverlay').classList.add('show');
        }
        
        function hideLoading() {
            document.getElementById('loadingOverlay').classList.remove('show');
        }
        
        function showMessage(type, message) {
            const toast = document.getElementById('messageToast');
            const messageText = document.getElementById('messageText');
            
            toast.className = `message-toast ${type}`;
            messageText.textContent = message;
            
            toast.classList.add('show');
            
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }
        
        function showConfirm(title, message, callback) {
            document.getElementById('confirmTitle').textContent = title;
            document.getElementById('confirmMessage').textContent = message;
            
            const confirmBtn = document.getElementById('confirmBtn');
            confirmBtn.onclick = () => {
                closeModal('confirmModal');
                if (callback) callback();
            };
            
            showModal('confirmModal');
        }
        
        // =============== 优惠券管理功能 ===============
        let couponsData = [];
        let filteredCouponsData = [];
        let selectedCouponIds = new Set();
        
        function showCouponManagementModal() {
            showModal('couponManagementModal');
            loadCouponsData();
        }
        
        function loadCouponsData() {
            // 模拟优惠券数据
            couponsData = [
                {
                    id: 1,
                    name: '新用户专享券',
                    type: 'amount',
                    value: 50,
                    condition: '满200元可用',
                    totalQuantity: 1000,
                    issuedQuantity: 680,
                    usedQuantity: 245,
                    startTime: '2024-01-01 00:00:00',
                    endTime: '2024-03-31 23:59:59',
                    status: 'active',
                    description: '仅限新用户首次购买使用'
                },
                {
                    id: 2,
                    name: '会员专享8折券',
                    type: 'discount',
                    value: 20,
                    condition: '全场通用',
                    totalQuantity: 500,
                    issuedQuantity: 500,
                    usedQuantity: 387,
                    startTime: '2024-02-01 00:00:00',
                    endTime: '2024-02-29 23:59:59',
                    status: 'expired',
                    description: '会员专享折扣券'
                },
                {
                    id: 3,
                    name: '免邮券',
                    type: 'shipping',
                    value: 0,
                    condition: '满99元包邮',
                    totalQuantity: 2000,
                    issuedQuantity: 1200,
                    usedQuantity: 890,
                    startTime: '2024-01-15 00:00:00',
                    endTime: '2024-12-31 23:59:59',
                    status: 'active',
                    description: '满99元享受免邮服务'
                },
                {
                    id: 4,
                    name: '生日礼品券',
                    type: 'gift',
                    value: 0,
                    condition: '生日当月可用',
                    totalQuantity: 200,
                    issuedQuantity: 150,
                    usedQuantity: 68,
                    startTime: '2024-01-01 00:00:00',
                    endTime: '2024-12-31 23:59:59',
                    status: 'active',
                    description: '生日当月专享礼品'
                },
                {
                    id: 5,
                    name: '节日狂欢券',
                    type: 'amount',
                    value: 100,
                    condition: '满500元可用',
                    totalQuantity: 300,
                    issuedQuantity: 0,
                    usedQuantity: 0,
                    startTime: '2024-06-01 00:00:00',
                    endTime: '2024-06-30 23:59:59',
                    status: 'draft',
                    description: '节日期间大额优惠券'
                }
            ];
            
            filteredCouponsData = [...couponsData];
            renderCouponsTable();
            setupCouponPagination();
        }
        
        function renderCouponsTable() {
            const tbody = document.getElementById('couponsTableBody');
            tbody.innerHTML = '';
            
            filteredCouponsData.forEach(coupon => {
                const row = document.createElement('tr');
                
                const typeNames = {
                    'amount': '满减券',
                    'discount': '折扣券',
                    'shipping': '免邮券',
                    'gift': '赠品券'
                };
                
                const statusNames = {
                    'active': '进行中',
                    'draft': '草稿',
                    'expired': '已过期',
                    'used-up': '已用完'
                };
                
                const valueDisplay = coupon.type === 'amount' ? `￥${coupon.value}` :
                                   coupon.type === 'discount' ? `${coupon.value}折` :
                                   coupon.type === 'shipping' ? '免邮' : '赠品';
                
                row.innerHTML = `
                    <td><input type="checkbox" onchange="toggleCouponSelection(${coupon.id})"></td>
                    <td>
                        <div class="coupon-info">
                            <div class="coupon-name">${coupon.name}</div>
                            <div class="coupon-id">ID: ${coupon.id}</div>
                        </div>
                    </td>
                    <td><span class="coupon-type-badge coupon-type-${coupon.type}">${typeNames[coupon.type]}</span></td>
                    <td>
                        <div class="coupon-value">${valueDisplay}</div>
                    </td>
                    <td>
                        <div class="coupon-condition">${coupon.condition}</div>
                    </td>
                    <td>
                        <div class="usage-info">
                            <div class="usage-issued">已发放: ${coupon.issuedQuantity}/${coupon.totalQuantity}</div>
                            <div class="usage-used">已使用: ${coupon.usedQuantity}</div>
                        </div>
                    </td>
                    <td>
                        <div class="coupon-date">${coupon.startTime.split(' ')[0]}<br>至<br>${coupon.endTime.split(' ')[0]}</div>
                    </td>
                    <td><span class="coupon-status coupon-status-${coupon.status}">${statusNames[coupon.status]}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-sm btn-primary" onclick="editCoupon(${coupon.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-sm btn-secondary" onclick="copyCoupon(${coupon.id})" title="复制">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="btn-sm ${coupon.status === 'active' ? 'btn-warning' : 'btn-success'}" 
                                    onclick="toggleCouponStatus(${coupon.id})" 
                                    title="${coupon.status === 'active' ? '暂停' : '启用'}">
                                <i class="fas fa-${coupon.status === 'active' ? 'pause' : 'play'}"></i>
                            </button>
                            <button class="btn-sm btn-info" onclick="viewCouponStatistics(${coupon.id})" title="统计">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                            <button class="btn-sm btn-danger" onclick="deleteCoupon(${coupon.id})" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }
        
        function setupCouponPagination() {
            // 简化的分页实现
            document.getElementById('couponCurrentStart').textContent = '1';
            document.getElementById('couponCurrentEnd').textContent = Math.min(10, filteredCouponsData.length);
            document.getElementById('couponTotalCount').textContent = filteredCouponsData.length;
        }
        
        function showCreateCouponModal() {
            showModal('createCouponModal');
        }
        
        function toggleCouponOptions() {
            const couponType = document.getElementById('couponType').value;
            
            // 隐藏所有选项
            document.querySelectorAll('[id$="CouponOptions"]').forEach(el => {
                el.style.display = 'none';
            });
            
            // 显示对应类型的选项
            if (couponType) {
                const optionsId = couponType + 'CouponOptions';
                const optionsElement = document.getElementById(optionsId);
                if (optionsElement) {
                    optionsElement.style.display = 'block';
                }
            }
        }
        
        function saveCoupon() {
            const form = document.getElementById('createCouponForm');
            const formData = new FormData(form);
            
            // 基础验证
            if (!formData.get('couponName') || !formData.get('couponType')) {
                showMessage('error', '请填写必填字段');
                return;
            }
            
            showLoading();
            
            setTimeout(() => {
                // 模拟保存优惠券
                const newCoupon = {
                    id: Date.now(),
                    name: formData.get('couponName'),
                    type: formData.get('couponType'),
                    value: parseFloat(formData.get('discountAmount') || formData.get('discountPercent') || 0),
                    condition: formData.get('minOrderAmount') ? `满${formData.get('minOrderAmount')}元可用` : '无门槛',
                    totalQuantity: parseInt(formData.get('totalQuantity')),
                    issuedQuantity: 0,
                    usedQuantity: 0,
                    startTime: formData.get('validStartTime').replace('T', ' '),
                    endTime: formData.get('validEndTime').replace('T', ' '),
                    status: 'draft',
                    description: formData.get('couponDescription') || ''
                };
                
                couponsData.unshift(newCoupon);
                filteredCouponsData = [...couponsData];
                renderCouponsTable();
                setupCouponPagination();
                
                hideLoading();
                closeModal('createCouponModal');
                showMessage('success', '优惠券创建成功');
            }, 1000);
        }
        
        function filterCoupons() {
            const typeFilter = document.getElementById('couponTypeFilter').value;
            const statusFilter = document.getElementById('couponStatusFilter').value;
            const searchText = document.getElementById('couponSearch').value.toLowerCase();
            
            filteredCouponsData = couponsData.filter(coupon => {
                const matchType = !typeFilter || coupon.type === typeFilter;
                const matchStatus = !statusFilter || coupon.status === statusFilter;
                const matchSearch = !searchText || coupon.name.toLowerCase().includes(searchText);
                
                return matchType && matchStatus && matchSearch;
            });
            
            renderCouponsTable();
            setupCouponPagination();
        }
        
        function searchCoupons() {
            filterCoupons();
        }
        
        function toggleSelectAllCoupons() {
            const selectAll = document.getElementById('selectAllCoupons');
            const checkboxes = document.querySelectorAll('#couponsTableBody input[type="checkbox"]');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                const couponId = parseInt(checkbox.onchange.toString().match(/\d+/)[0]);
                if (selectAll.checked) {
                    selectedCouponIds.add(couponId);
                } else {
                    selectedCouponIds.delete(couponId);
                }
            });
        }
        
        function toggleCouponSelection(couponId) {
            if (selectedCouponIds.has(couponId)) {
                selectedCouponIds.delete(couponId);
            } else {
                selectedCouponIds.add(couponId);
            }
        }
        
        function editCoupon(id) {
            const coupon = couponsData.find(c => c.id === id);
            if (!coupon) return;
            
            // 填充编辑表单
            document.getElementById('couponName').value = coupon.name;
            document.getElementById('couponType').value = coupon.type;
            document.getElementById('couponDescription').value = coupon.description || '';
            document.getElementById('totalQuantity').value = coupon.totalQuantity;
            
            // 设置时间
            const startTime = new Date(coupon.startTime).toISOString().slice(0, 16);
            const endTime = new Date(coupon.endTime).toISOString().slice(0, 16);
            document.getElementById('validStartTime').value = startTime;
            document.getElementById('validEndTime').value = endTime;
            
            // 根据类型填充对应字段
            toggleCouponOptions();
            
            switch(coupon.type) {
                case 'amount':
                    document.getElementById('discountAmount').value = coupon.value;
                    const minAmount = coupon.condition.match(/满(\d+)元/);
                    if (minAmount) {
                        document.getElementById('minOrderAmount').value = minAmount[1];
                    }
                    break;
                case 'discount':
                    document.getElementById('discountPercent').value = coupon.value;
                    break;
                case 'shipping':
                    const shippingMin = coupon.condition.match(/满(\d+)元/);
                    if (shippingMin) {
                        document.getElementById('freeShippingMin').value = shippingMin[1];
                    }
                    break;
                case 'gift':
                    document.getElementById('giftDescription').value = coupon.description || '';
                    break;
            }
            
            // 修改保存按钮，用于更新而非创建
            const saveBtn = document.querySelector('#createCouponModal .btn-primary');
            saveBtn.textContent = '更新优惠券';
            saveBtn.onclick = () => updateCoupon(id);
            
            showModal('createCouponModal');
        }
        
        function updateCoupon(id) {
            const form = document.getElementById('createCouponForm');
            const formData = new FormData(form);
            
            // 基础验证
            if (!formData.get('couponName') || !formData.get('couponType')) {
                showMessage('error', '请填写必填字段');
                return;
            }
            
            showLoading();
            
            setTimeout(() => {
                // 找到并更新优惠券
                const couponIndex = couponsData.findIndex(c => c.id === id);
                if (couponIndex !== -1) {
                    const couponType = formData.get('couponType');
                    let value = 0;
                    let condition = '无门槛';
                    
                    // 根据类型设置值和条件
                    switch(couponType) {
                        case 'amount':
                            value = parseFloat(formData.get('discountAmount'));
                            const minOrder = formData.get('minOrderAmount');
                            if (minOrder) condition = `满${minOrder}元可用`;
                            break;
                        case 'discount':
                            value = parseFloat(formData.get('discountPercent'));
                            break;
                        case 'shipping':
                            value = 0;
                            const freeMin = formData.get('freeShippingMin');
                            if (freeMin) condition = `满${freeMin}元包邮`;
                            break;
                        case 'gift':
                            value = 0;
                            condition = '生日当月可用';
                            break;
                    }
                    
                    // 更新优惠券数据
                    couponsData[couponIndex] = {
                        ...couponsData[couponIndex],
                        name: formData.get('couponName'),
                        type: couponType,
                        value: value,
                        condition: condition,
                        totalQuantity: parseInt(formData.get('totalQuantity')),
                        startTime: formData.get('validStartTime').replace('T', ' '),
                        endTime: formData.get('validEndTime').replace('T', ' '),
                        description: formData.get('couponDescription') || ''
                    };
                    
                    // 更新过滤数据
                    const filteredIndex = filteredCouponsData.findIndex(c => c.id === id);
                    if (filteredIndex !== -1) {
                        filteredCouponsData[filteredIndex] = couponsData[couponIndex];
                    }
                    
                    renderCouponsTable();
                    setupCouponPagination();
                }
                
                hideLoading();
                closeModal('createCouponModal');
                showMessage('success', '优惠券更新成功');
                
                // 重置保存按钮
                const saveBtn = document.querySelector('#createCouponModal .btn-primary');
                saveBtn.textContent = '创建优惠券';
                saveBtn.onclick = saveCoupon;
            }, 1000);
        }
        
        function copyCoupon(id) {
            const coupon = couponsData.find(c => c.id === id);
            if (!coupon) return;
            
            showConfirm(
                '复制优惠券',
                `确定要复制优惠券"${coupon.name}"吗？`,
                () => {
                    showLoading();
                    
                    setTimeout(() => {
                        const newCoupon = {
                            ...coupon,
                            id: Date.now(),
                            name: coupon.name + ' (副本)',
                            issuedQuantity: 0,
                            usedQuantity: 0,
                            status: 'draft'
                        };
                        
                        couponsData.unshift(newCoupon);
                        filteredCouponsData = [...couponsData];
                        renderCouponsTable();
                        setupCouponPagination();
                        
                        hideLoading();
                        showMessage('success', '优惠券复制成功');
                    }, 800);
                }
            );
        }
        
        function deleteCoupon(id) {
            const coupon = couponsData.find(c => c.id === id);
            if (coupon) {
                showConfirm(
                    '删除优惠券',
                    `确定要删除优惠券"${coupon.name}"吗？`,
                    () => {
                        couponsData = couponsData.filter(c => c.id !== id);
                        filteredCouponsData = filteredCouponsData.filter(c => c.id !== id);
                        renderCouponsTable();
                        setupCouponPagination();
                        showMessage('success', '优惠券删除成功');
                    }
                );
            }
        }
        
        function batchIssueCoupons() {
            if (selectedCouponIds.size === 0) {
                showMessage('warning', '请先选择要发放的优惠券');
                return;
            }
            
            showModal('batchIssueModal');
        }
        
        function confirmBatchIssue() {
            const issueType = document.getElementById('issueType').value;
            const issueQuantity = parseInt(document.getElementById('issueQuantity').value) || 0;
            const targetUsers = document.getElementById('targetUsers').value;
            
            if (!issueType || issueQuantity <= 0) {
                showMessage('error', '请填写发放数量和发放方式');
                return;
            }
            
            showConfirm(
                '批量发放优惠券',
                `确定要向${getTargetUsersText(targetUsers)}发放${selectedCouponIds.size}种优惠券，每种发放${issueQuantity}张吗？`,
                () => {
                    showLoading();
                    
                    setTimeout(() => {
                        // 更新选中优惠券的发放数量
                        selectedCouponIds.forEach(couponId => {
                            const couponIndex = couponsData.findIndex(c => c.id === couponId);
                            if (couponIndex !== -1) {
                                couponsData[couponIndex].issuedQuantity = Math.min(
                                    couponsData[couponIndex].issuedQuantity + issueQuantity,
                                    couponsData[couponIndex].totalQuantity
                                );
                                
                                // 如果发放完毕，更新状态
                                if (couponsData[couponIndex].issuedQuantity >= couponsData[couponIndex].totalQuantity) {
                                    couponsData[couponIndex].status = 'used-up';
                                } else if (couponsData[couponIndex].status === 'draft') {
                                    couponsData[couponIndex].status = 'active';
                                }
                            }
                        });
                        
                        // 同步更新过滤数据
                        filteredCouponsData = [...couponsData];
                        renderCouponsTable();
                        setupCouponPagination();
                        
                        // 清除选择
                        selectedCouponIds.clear();
                        document.getElementById('selectAllCoupons').checked = false;
                        document.querySelectorAll('#couponsTableBody input[type="checkbox"]').forEach(cb => {
                            cb.checked = false;
                        });
                        
                        hideLoading();
                        closeModal('batchIssueModal');
                        showMessage('success', `成功发放${selectedCouponIds.size}种优惠券`);
                    }, 1500);
                }
            );
        }
        
        function getTargetUsersText(targetUsers) {
            const targetMap = {
                'all': '全部用户',
                'new': '新用户',
                'vip': 'VIP用户',
                'active': '活跃用户',
                'birthday': '生日用户',
                'custom': '指定用户'
            };
            return targetMap[targetUsers] || '指定用户';
        }
        
        // 优惠券状态管理功能
        function toggleCouponStatus(id) {
            const coupon = couponsData.find(c => c.id === id);
            if (!coupon) return;
            
            const newStatus = coupon.status === 'active' ? 'paused' : 'active';
            const action = newStatus === 'active' ? '启用' : '暂停';
            
            showConfirm(
                `${action}优惠券`,
                `确定要${action}优惠券"${coupon.name}"吗？`,
                () => {
                    const couponIndex = couponsData.findIndex(c => c.id === id);
                    if (couponIndex !== -1) {
                        couponsData[couponIndex].status = newStatus;
                        
                        // 同步更新过滤数据
                        const filteredIndex = filteredCouponsData.findIndex(c => c.id === id);
                        if (filteredIndex !== -1) {
                            filteredCouponsData[filteredIndex].status = newStatus;
                        }
                        
                        renderCouponsTable();
                        showMessage('success', `优惠券已${action}`);
                    }
                }
            );
        }
        
        // 查看优惠券详细统计
        function viewCouponStatistics(id) {
            const coupon = couponsData.find(c => c.id === id);
            if (!coupon) return;
            
            // 模拟详细统计数据
            const statisticsData = {
                basicInfo: {
                    name: coupon.name,
                    type: coupon.type,
                    value: coupon.value,
                    condition: coupon.condition,
                    totalQuantity: coupon.totalQuantity,
                    issuedQuantity: coupon.issuedQuantity,
                    usedQuantity: coupon.usedQuantity,
                    remainingQuantity: coupon.totalQuantity - coupon.issuedQuantity,
                    usageRate: coupon.issuedQuantity > 0 ? (coupon.usedQuantity / coupon.issuedQuantity * 100).toFixed(1) : 0
                },
                dailyUsage: [
                    { date: '2024-03-01', issued: 50, used: 12 },
                    { date: '2024-03-02', issued: 80, used: 25 },
                    { date: '2024-03-03', issued: 65, used: 18 },
                    { date: '2024-03-04', issued: 45, used: 15 },
                    { date: '2024-03-05', issued: 70, used: 22 }
                ],
                userGroups: [
                    { group: '新用户', issued: 120, used: 45 },
                    { group: 'VIP用户', issued: 200, used: 89 },
                    { group: '活跃用户', issued: 180, used: 67 },
                    { group: '普通用户', issued: 150, used: 44 }
                ]
            };
            
            // 填充统计模态框数据
            document.getElementById('statisticsCouponName').textContent = statisticsData.basicInfo.name;
            document.getElementById('statisticsIssuedQuantity').textContent = statisticsData.basicInfo.issuedQuantity;
            document.getElementById('statisticsUsedQuantity').textContent = statisticsData.basicInfo.usedQuantity;
            document.getElementById('statisticsRemainingQuantity').textContent = statisticsData.basicInfo.remainingQuantity;
            document.getElementById('statisticsUsageRate').textContent = statisticsData.basicInfo.usageRate + '%';
            
            // 填充每日使用情况表格
            const dailyTbody = document.getElementById('dailyUsageTableBody');
            dailyTbody.innerHTML = statisticsData.dailyUsage.map(day => `
                <tr>
                    <td>${day.date}</td>
                    <td>${day.issued}</td>
                    <td>${day.used}</td>
                    <td>${day.issued > 0 ? (day.used / day.issued * 100).toFixed(1) : 0}%</td>
                </tr>
            `).join('');
            
            // 填充用户群体使用情况表格
            const groupTbody = document.getElementById('userGroupTableBody');
            groupTbody.innerHTML = statisticsData.userGroups.map(group => `
                <tr>
                    <td>${group.group}</td>
                    <td>${group.issued}</td>
                    <td>${group.used}</td>
                    <td>${group.issued > 0 ? (group.used / group.issued * 100).toFixed(1) : 0}%</td>
                </tr>
            `).join('');
            
            showModal('couponStatisticsModal');
        }
        
        function exportCoupons() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showMessage('success', '优惠券数据导出完成');
            }, 1500);
        }
        
        // =============== 统计查看功能 ===============
        let statisticsData = {};
        
        function showStatisticsModal() {
            showModal('statisticsModal');
            loadStatisticsData();
        }
        
        function loadStatisticsData() {
            // 模拟统计数据
            statisticsData = {
                overview: {
                    totalParticipants: 15680,
                    participantsChange: 12.5,
                    totalOrders: 8932,
                    ordersChange: 8.3,
                    totalRevenue: 1256780.50,
                    revenueChange: 15.7,
                    avgConversionRate: 56.9,
                    conversionChange: 2.1
                },
                activities: [
                    {
                        name: '新春大促满减活动',
                        type: '满减活动',
                        period: '2024-01-20 至 2024-01-31',
                        participants: 5680,
                        orders: 3245,
                        revenue: 456789.20,
                        conversionRate: 57.1,
                        roi: 4.2
                    },
                    {
                        name: '限时闪购活动',
                        type: '限时抢购',
                        period: '2024-02-01 至 2024-02-07',
                        participants: 3420,
                        orders: 2134,
                        revenue: 298765.30,
                        conversionRate: 62.4,
                        roi: 3.8
                    },
                    {
                        name: '会员专享券',
                        type: '优惠券',
                        period: '2024-02-10 至 2024-02-29',
                        participants: 4580,
                        orders: 2553,
                        revenue: 387245.70,
                        conversionRate: 55.7,
                        roi: 3.5
                    },
                    {
                        name: '情人节特惠',
                        type: '节日营销',
                        period: '2024-02-12 至 2024-02-16',
                        participants: 2000,
                        orders: 1200,
                        revenue: 114000.30,
                        conversionRate: 60.0,
                        roi: 2.9
                    }
                ]
            };
            
            updateStatisticsDisplay();
        }
        
        function updateStatisticsDisplay() {
            const { overview } = statisticsData;
            
            // 更新概览数据
            document.getElementById('totalParticipants').textContent = overview.totalParticipants.toLocaleString();
            document.getElementById('participantsChange').textContent = `+${overview.participantsChange}%`;
            
            document.getElementById('totalOrders').textContent = overview.totalOrders.toLocaleString();
            document.getElementById('ordersChange').textContent = `+${overview.ordersChange}%`;
            
            document.getElementById('totalRevenue').textContent = `¥${overview.totalRevenue.toLocaleString()}`;
            document.getElementById('revenueChange').textContent = `+${overview.revenueChange}%`;
            
            document.getElementById('avgConversionRate').textContent = `${overview.avgConversionRate}%`;
            document.getElementById('conversionChange').textContent = `+${overview.conversionChange}%`;
            
            // 更新详细统计表格
            renderDetailedStatsTable();
            
            // 模拟图表显示
            updateChartPlaceholders();
        }
        
        function renderDetailedStatsTable() {
            const tbody = document.getElementById('detailedStatsTableBody');
            tbody.innerHTML = '';
            
            statisticsData.activities.forEach(activity => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${activity.name}</td>
                    <td>${activity.type}</td>
                    <td>${activity.period}</td>
                    <td>${activity.participants.toLocaleString()}</td>
                    <td>${activity.orders.toLocaleString()}</td>
                    <td>¥${activity.revenue.toLocaleString()}</td>
                    <td>${activity.conversionRate}%</td>
                    <td>${activity.roi}</td>
                `;
                tbody.appendChild(row);
            });
        }
        
        function updateChartPlaceholders() {
            const trendChart = document.getElementById('trendChart');
            const typeChart = document.getElementById('typeDistributionChart');
            
            if (trendChart) {
                trendChart.style.display = 'none';
                trendChart.parentElement.innerHTML = '<div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8fafc; border-radius: 8px; color: #666;"><i class="fas fa-chart-line" style="font-size: 48px; margin-right: 15px;"></i><span>趋势图表 (需要图表库支持)</span></div>';
            }
            
            if (typeChart) {
                typeChart.style.display = 'none';
                typeChart.parentElement.innerHTML = '<div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8fafc; border-radius: 8px; color: #666;"><i class="fas fa-chart-pie" style="font-size: 48px; margin-right: 15px;"></i><span>分布图表 (需要图表库支持)</span></div>';
            }
        }
        
        function updateStatistics() {
            const timeRange = document.getElementById('statsTimeRange').value;
            
            if (timeRange === 'custom') {
                document.getElementById('customDateRange').style.display = 'block';
            } else {
                document.getElementById('customDateRange').style.display = 'none';
            }
            
            showLoading();
            setTimeout(() => {
                hideLoading();
                showMessage('success', '统计数据已更新');
            }, 1000);
        }
        
        function updateTrendChart() {
            const chartType = document.getElementById('trendChartType').value;
            showMessage('info', `切换到${chartType}趋势图`);
        }
        
        function exportStatistics() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showMessage('success', '统计报表导出完成');
            }, 1500);
        }
        
        function refreshStatistics() {
            showLoading();
            setTimeout(() => {
                loadStatisticsData();
                hideLoading();
                showMessage('success', '统计数据已刷新');
            }, 1000);
        }
        
        function exportDetailedStats() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showMessage('success', '详细统计表格导出完成');
            }, 1000);
        }
        
        // =============== 满减活动管理功能 ===============
        let discountActivitiesData = [];
        let filteredDiscountData = [];
        let discountCurrentPage = 1;
        let discountItemsPerPage = 10;
        let discountMonitorInterval = null;
        let discountMonitorData = {};
        
        // 模拟满减活动数据
        const mockDiscountData = [
            {
                id: 1,
                name: '新春满减大促',
                type: 'full-reduction',
                rules: [{ threshold: 199, discount: 30 }],
                scope: { type: 'all', details: [] },
                startTime: '2024-03-01 10:00:00',
                endTime: '2024-03-31 23:59:59',
                participants: 5680,
                orders: 3245,
                revenue: 456789.20,
                totalSavings: 97350,
                status: 'active',
                enableUserLimit: true,
                userLimit: 3,
                enableTotalLimit: true,
                totalBudget: 500000,
                description: '全场满199减30，春季大促销'
            },
            {
                id: 2,
                name: '阶梯满减优惠',
                type: 'tiered',
                rules: [
                    { threshold: 100, discount: 10 },
                    { threshold: 200, discount: 25 },
                    { threshold: 500, discount: 80 }
                ],
                scope: { type: 'category', details: ['服装', '数码'] },
                startTime: '2024-03-15 00:00:00',
                endTime: '2024-03-25 23:59:59',
                participants: 2340,
                orders: 1567,
                revenue: 234560.80,
                totalSavings: 46890,
                status: 'scheduled',
                enableUserLimit: false,
                enableTotalLimit: false,
                description: '满100减10，满200减25，满500减80'
            }
        ];
        
        function showDiscountManagement() {
            discountActivitiesData = [...mockDiscountData];
            filteredDiscountData = [...discountActivitiesData];
            renderDiscountTable();
            setupDiscountPagination();
            showModal('discountManagementModal');
        }
        
        function showDiscountMonitor() {
            initializeDiscountMonitorData();
            startDiscountMonitoring();
            showModal('discountMonitorModal');
        }
        
        function createDiscountActivity() {
            showCreateDiscountModal();
        }
        
        function showCreateDiscountModal() {
            document.getElementById('createDiscountForm').reset();
            updateDiscountRules();
            updateScopeSettings();
            showModal('createDiscountModal');
        }
        
        function renderDiscountTable() {
            const tbody = document.getElementById('discountTableBody');
            const startIndex = (discountCurrentPage - 1) * discountItemsPerPage;
            const endIndex = startIndex + discountItemsPerPage;
            const pageData = filteredDiscountData.slice(startIndex, endIndex);
            
            tbody.innerHTML = pageData.map(discount => {
                const rulesText = discount.rules.map(rule => 
                    `满¥${rule.threshold}减¥${rule.discount}`
                ).join('；');
                
                const scopeText = discount.scope.type === 'all' ? '全店通用' : 
                    discount.scope.type === 'category' ? '指定分类' : 
                    discount.scope.type === 'product' ? '指定商品' : '指定品牌';
                
                const conversionRate = discount.participants > 0 ? 
                    ((discount.orders / discount.participants) * 100).toFixed(1) : 0;
                
                return `
                    <tr>
                        <td><input type="checkbox" class="discount-select" value="${discount.id}" onchange="toggleDiscountSelection(${discount.id})"></td>
                        <td>
                            <div class="discount-info">
                                <div class="discount-name">${discount.name}</div>
                                <div class="discount-id">ID: ${discount.id}</div>
                                <div class="discount-type">${getDiscountTypeText(discount.type)}</div>
                            </div>
                        </td>
                        <td>
                            <div class="discount-rules">
                                <div class="rules-text">${rulesText}</div>
                                <div class="rules-count">${discount.rules.length}个规则</div>
                            </div>
                        </td>
                        <td>
                            <div class="discount-scope">
                                <div class="scope-type">${scopeText}</div>
                                ${discount.scope.details.length > 0 ? 
                                    `<div class="scope-details">${discount.scope.details.join('、')}</div>` : ''}
                            </div>
                        </td>
                        <td>
                            <div class="participation-stats">
                                <div class="stat-item">参与: ${discount.participants.toLocaleString()}</div>
                                <div class="stat-item">订单: ${discount.orders.toLocaleString()}</div>
                                <div class="stat-item">金额: ¥${discount.revenue.toLocaleString()}</div>
                                <div class="stat-item">节省: ¥${discount.totalSavings.toLocaleString()}</div>
                                <div class="stat-item">转化: ${conversionRate}%</div>
                            </div>
                        </td>
                        <td>
                            <div class="time-info">
                                <div class="time-range">${discount.startTime} 至<br>${discount.endTime}</div>
                                <div class="time-status">${getDiscountTimeStatus(discount)}</div>
                            </div>
                        </td>
                        <td>
                            <span class="discount-status status-${discount.status}">
                                ${getDiscountStatusText(discount.status)}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action" onclick="editDiscountActivity(${discount.id})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action" onclick="viewDiscountDetails(${discount.id})" title="详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${discount.status === 'active' ? 
                                    `<button class="btn-action" onclick="pauseDiscountActivity(${discount.id})" title="暂停">
                                        <i class="fas fa-pause"></i>
                                    </button>` : 
                                    discount.status === 'scheduled' ? 
                                    `<button class="btn-action" onclick="startDiscountActivity(${discount.id})" title="启动">
                                        <i class="fas fa-play"></i>
                                    </button>` : ''
                                }
                                <button class="btn-action danger" onclick="deleteDiscountActivity(${discount.id})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }
        
        function getDiscountTypeText(type) {
            const typeMap = {
                'full-reduction': '满减优惠',
                'tiered': '阶梯满减', 
                'category': '品类满减',
                'member': '会员专享'
            };
            return typeMap[type] || type;
        }
        
        function getDiscountStatusText(status) {
            const statusMap = {
                'active': '进行中',
                'scheduled': '已预约',
                'paused': '已暂停',
                'ended': '已结束',
                'draft': '草稿'
            };
            return statusMap[status] || status;
        }
        
        function getDiscountTimeStatus(discount) {
            const now = new Date();
            const startTime = new Date(discount.startTime);
            const endTime = new Date(discount.endTime);
            
            if (now < startTime) {
                const diff = Math.ceil((startTime - now) / (1000 * 60 * 60));
                return `${diff}小时后开始`;
            } else if (now >= startTime && now <= endTime) {
                const diff = Math.ceil((endTime - now) / (1000 * 60 * 60));
                return `${diff}小时后结束`;
            } else {
                return '已结束';
            }
        }
        
        function setupDiscountPagination() {
            const totalPages = Math.ceil(filteredDiscountData.length / discountItemsPerPage);
            const pagination = document.getElementById('discountPagination');
            const startRecord = (discountCurrentPage - 1) * discountItemsPerPage + 1;
            const endRecord = Math.min(startRecord + discountItemsPerPage - 1, filteredDiscountData.length);
            
            document.getElementById('discountCurrentStart').textContent = startRecord;
            document.getElementById('discountCurrentEnd').textContent = endRecord;
            document.getElementById('discountTotalCount').textContent = filteredDiscountData.length;
            
            pagination.innerHTML = '';
            
            // 上一页
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.disabled = discountCurrentPage === 1;
            prevBtn.onclick = () => {
                if (discountCurrentPage > 1) {
                    discountCurrentPage--;
                    renderDiscountTable();
                    setupDiscountPagination();
                }
            };
            pagination.appendChild(prevBtn);
            
            // 页码
            for (let i = 1; i <= totalPages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === discountCurrentPage ? 'active' : '';
                pageBtn.onclick = () => {
                    discountCurrentPage = i;
                    renderDiscountTable();
                    setupDiscountPagination();
                };
                pagination.appendChild(pageBtn);
            }
            
            // 下一页
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.disabled = discountCurrentPage === totalPages;
            nextBtn.onclick = () => {
                if (discountCurrentPage < totalPages) {
                    discountCurrentPage++;
                    renderDiscountTable();
                    setupDiscountPagination();
                }
            };
            pagination.appendChild(nextBtn);
        }
        
        function searchDiscounts() {
            const query = document.getElementById('discountSearch').value.toLowerCase();
            filteredDiscountData = discountActivitiesData.filter(discount => 
                discount.name.toLowerCase().includes(query) ||
                discount.description.toLowerCase().includes(query)
            );
            discountCurrentPage = 1;
            renderDiscountTable();
            setupDiscountPagination();
        }
        
        function filterDiscounts() {
            const statusFilter = document.getElementById('discountStatusFilter').value;
            const typeFilter = document.getElementById('discountTypeFilter').value;
            const searchQuery = document.getElementById('discountSearch').value.toLowerCase();
            
            filteredDiscountData = discountActivitiesData.filter(discount => {
                const matchesStatus = !statusFilter || discount.status === statusFilter;
                const matchesType = !typeFilter || discount.type === typeFilter;
                const matchesSearch = !searchQuery || 
                    discount.name.toLowerCase().includes(searchQuery) ||
                    discount.description.toLowerCase().includes(searchQuery);
                return matchesStatus && matchesType && matchesSearch;
            });
            
            discountCurrentPage = 1;
            renderDiscountTable();
            setupDiscountPagination();
        }
        
        function saveDiscountActivity() {
            const form = document.getElementById('createDiscountForm');
            const formData = new FormData(form);
            
            // 基础验证
            if (!formData.get('discountName') || !formData.get('discountType')) {
                showMessage('error', '请填写必填字段');
                return;
            }
            
            showLoading();
            
            setTimeout(() => {
                const newDiscount = {
                    id: Date.now(),
                    name: formData.get('discountName'),
                    type: formData.get('discountType'),
                    rules: getCurrentDiscountRules(),
                    scope: getCurrentDiscountScope(),
                    startTime: formData.get('discountStartTime').replace('T', ' '),
                    endTime: formData.get('discountEndTime').replace('T', ' '),
                    participants: 0,
                    orders: 0,
                    revenue: 0,
                    totalSavings: 0,
                    status: 'draft',
                    enableUserLimit: form.querySelector('[name="enableUserLimit"]').checked,
                    enableTotalLimit: form.querySelector('[name="enableTotalLimit"]').checked,
                    enableCouponStack: form.querySelector('[name="enableCouponStack"]').checked,
                    enableMemberOnly: form.querySelector('[name="enableMemberOnly"]').checked,
                    enableNotification: form.querySelector('[name="enableNotification"]').checked,
                    description: formData.get('discountDescription')
                };
                
                discountActivitiesData.unshift(newDiscount);
                filteredDiscountData = [...discountActivitiesData];
                renderDiscountTable();
                setupDiscountPagination();
                
                hideLoading();
                closeModal('createDiscountModal');
                showMessage('success', '满减活动创建成功');
            }, 1000);
        }
        
        function getCurrentDiscountRules() {
            const discountType = document.getElementById('discountType').value;
            const rulesContainer = document.getElementById('discountRulesContainer');
            
            if (discountType === 'full-reduction') {
                const threshold = rulesContainer.querySelector('[name="threshold"]')?.value || 0;
                const discount = rulesContainer.querySelector('[name="discount"]')?.value || 0;
                return [{ threshold: parseFloat(threshold), discount: parseFloat(discount) }];
            } else if (discountType === 'tiered') {
                const rules = [];
                const thresholds = rulesContainer.querySelectorAll('[name="threshold"]');
                const discounts = rulesContainer.querySelectorAll('[name="discount"]');
                
                for (let i = 0; i < thresholds.length; i++) {
                    if (thresholds[i].value && discounts[i].value) {
                        rules.push({
                            threshold: parseFloat(thresholds[i].value),
                            discount: parseFloat(discounts[i].value)
                        });
                    }
                }
                return rules;
            }
            return [];
        }
        
        function getCurrentDiscountScope() {
            const scopeType = document.querySelector('[name="scopeType"]:checked').value;
            const details = [];
            
            if (scopeType !== 'all') {
                const scopeDetails = document.getElementById('scopeDetailsContainer');
                const checkboxes = scopeDetails.querySelectorAll('input[type="checkbox"]:checked');
                checkboxes.forEach(cb => details.push(cb.value));
            }
            
            return { type: scopeType, details };
        }
        
        function updateDiscountRules() {
            const discountType = document.getElementById('discountType').value;
            const rulesContainer = document.getElementById('discountRulesContainer');
            
            let html = '';
            
            if (discountType === 'full-reduction') {
                html = `
                    <div class="rule-item">
                        <label>满金额</label>
                        <input type="number" name="threshold" placeholder="199" min="0.01" step="0.01" required>
                        <label>减金额</label>
                        <input type="number" name="discount" placeholder="30" min="0.01" step="0.01" required>
                    </div>
                `;
            } else if (discountType === 'tiered') {
                html = `
                    <div class="tiered-rules">
                        <div class="rule-item">
                            <label>满金额</label>
                            <input type="number" name="threshold" placeholder="100" min="0.01" step="0.01">
                            <label>减金额</label>
                            <input type="number" name="discount" placeholder="10" min="0.01" step="0.01">
                        </div>
                        <div class="rule-item">
                            <label>满金额</label>
                            <input type="number" name="threshold" placeholder="200" min="0.01" step="0.01">
                            <label>减金额</label>
                            <input type="number" name="discount" placeholder="25" min="0.01" step="0.01">
                        </div>
                        <div class="rule-item">
                            <label>满金额</label>
                            <input type="number" name="threshold" placeholder="500" min="0.01" step="0.01">
                            <label>减金额</label>
                            <input type="number" name="discount" placeholder="80" min="0.01" step="0.01">
                        </div>
                        <button type="button" class="btn btn-secondary" onclick="addTieredRule()">
                            <i class="fas fa-plus"></i> 添加档位
                        </button>
                    </div>
                `;
            } else if (discountType === 'category') {
                html = `
                    <div class="category-rules">
                        <p>品类满减规则将基于选择的商品分类进行满减优惠</p>
                        <div class="rule-item">
                            <label>满金额</label>
                            <input type="number" name="threshold" placeholder="199" min="0.01" step="0.01" required>
                            <label>减金额</label>
                            <input type="number" name="discount" placeholder="30" min="0.01" step="0.01" required>
                        </div>
                    </div>
                `;
            } else if (discountType === 'member') {
                html = `
                    <div class="member-rules">
                        <p>会员专享满减，仅限会员参与</p>
                        <div class="rule-item">
                            <label>满金额</label>
                            <input type="number" name="threshold" placeholder="99" min="0.01" step="0.01" required>
                            <label>减金额</label>
                            <input type="number" name="discount" placeholder="20" min="0.01" step="0.01" required>
                        </div>
                    </div>
                `;
            } else {
                html = `
                    <div class="rule-placeholder">
                        <i class="fas fa-info-circle"></i>
                        <p>请先选择满减类型，然后配置相应的规则</p>
                    </div>
                `;
            }
            
            rulesContainer.innerHTML = html;
        }
        
        function updateScopeSettings() {
            const scopeType = document.querySelector('[name="scopeType"]:checked').value;
            const scopeDetails = document.getElementById('scopeDetailsContainer');
            
            if (scopeType === 'all') {
                scopeDetails.style.display = 'none';
            } else {
                scopeDetails.style.display = 'block';
                
                let html = '';
                if (scopeType === 'category') {
                    html = `
                        <h5>选择适用分类</h5>
                        <div class="scope-checkboxes">
                            <label><input type="checkbox" value="服装"> 服装</label>
                            <label><input type="checkbox" value="数码"> 数码</label>
                            <label><input type="checkbox" value="家居"> 家居</label>
                            <label><input type="checkbox" value="美妆"> 美妆</label>
                            <label><input type="checkbox" value="运动"> 运动</label>
                            <label><input type="checkbox" value="食品"> 食品</label>
                        </div>
                    `;
                } else if (scopeType === 'product') {
                    html = `
                        <h5>选择适用商品</h5>
                        <div class="product-selector">
                            <input type="text" placeholder="搜索商品..." onkeyup="searchProducts(this.value)">
                            <div class="product-list" id="productSelectorList">
                                <!-- 动态加载商品列表 -->
                            </div>
                        </div>
                    `;
                } else if (scopeType === 'brand') {
                    html = `
                        <h5>选择适用品牌</h5>
                        <div class="scope-checkboxes">
                            <label><input type="checkbox" value="Apple"> Apple</label>
                            <label><input type="checkbox" value="Samsung"> Samsung</label>
                            <label><input type="checkbox" value="Nike"> Nike</label>
                            <label><input type="checkbox" value="Adidas"> Adidas</label>
                            <label><input type="checkbox" value="Xiaomi"> 小米</label>
                            <label><input type="checkbox" value="Huawei"> 华为</label>
                        </div>
                    `;
                }
                
                scopeDetails.innerHTML = html;
            }
        }
        
        // 满减活动实时监控功能
        
        function showDiscountMonitor() {
            openModal('discountMonitorModal');
            updateDiscountMonitorData();
            
            // 每3秒更新一次数据
            if (discountMonitorInterval) {
                clearInterval(discountMonitorInterval);
            }
            discountMonitorInterval = setInterval(updateDiscountMonitorData, 3000);
        }
        
        function updateDiscountMonitorData() {
            updateDiscountOverview();
            updateActiveDiscountList();
            updateDiscountChart();
            updateDiscountAlerts();
        }
        
        function updateDiscountOverview() {
            const activeDiscounts = discountActivitiesData.filter(d => d.status === 'active');
            const totalParticipants = discountActivitiesData.reduce((sum, d) => sum + d.participants, 0);
            const totalOrders = discountActivitiesData.reduce((sum, d) => sum + d.orders, 0);
            const totalSavings = discountActivitiesData.reduce((sum, d) => sum + d.totalSavings, 0);
            
            document.getElementById('activeDiscountCount').textContent = activeDiscounts.length;
            document.getElementById('totalDiscountParticipants').textContent = formatNumber(totalParticipants);
            document.getElementById('totalDiscountOrders').textContent = formatNumber(totalOrders);
            document.getElementById('totalDiscountSavings').textContent = formatNumber(totalSavings);
        }
        
        function updateActiveDiscountList() {
            const activeDiscounts = discountActivitiesData.filter(d => d.status === 'active');
            const container = document.getElementById('activeDiscountsList');
            
            if (activeDiscounts.length === 0) {
                container.innerHTML = '<div class="no-data">暂无进行中的满减活动</div>';
                return;
            }
            
            let html = '';
            activeDiscounts.forEach(discount => {
                const timeRemaining = calculateTimeRemaining(discount.endTime);
                const participationRate = discount.participants > 0 ? 
                    Math.round((discount.orders / discount.participants) * 100) : 0;
                
                html += `
                    <div class="discount-item">
                        <div class="discount-info">
                            <h5>${discount.name}</h5>
                            <div class="discount-meta">
                                <span class="type-badge type-${discount.type}">${getDiscountTypeText(discount.type)}</span>
                                <span class="time-remaining">${timeRemaining}</span>
                            </div>
                        </div>
                        <div class="discount-stats">
                            <div class="stat">
                                <span class="label">参与人数</span>
                                <span class="value">${formatNumber(discount.participants)}</span>
                            </div>
                            <div class="stat">
                                <span class="label">完成订单</span>
                                <span class="value">${formatNumber(discount.orders)}</span>
                            </div>
                            <div class="stat">
                                <span class="label">转化率</span>
                                <span class="value">${participationRate}%</span>
                            </div>
                        </div>
                        <div class="discount-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${participationRate}%"></div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function updateDiscountChart() {
            // 模拟图表数据更新
            console.log('更新满减活动图表数据');
        }
        
        function updateDiscountAlerts() {
            const alerts = [];
            const now = new Date();
            
            discountActivitiesData.forEach(discount => {
                if (discount.status === 'active') {
                    const endTime = new Date(discount.endTime);
                    const hoursRemaining = (endTime - now) / (1000 * 60 * 60);
                    
                    if (hoursRemaining < 2 && hoursRemaining > 0) {
                        alerts.push({
                            type: 'warning',
                            message: `满减活动 "${discount.name}" 即将在 ${Math.round(hoursRemaining)} 小时后结束`,
                            time: '刚刚'
                        });
                    }
                    
                    if (discount.participants > 1000) {
                        alerts.push({
                            type: 'info',
                            message: `满减活动 "${discount.name}" 参与人数已超过 1000 人`,
                            time: '刚刚'
                        });
                    }
                }
            });
            
            const container = document.getElementById('discountAlertsList');
            if (alerts.length === 0) {
                container.innerHTML = '<div class="no-alerts">暂无系统警报</div>';
            } else {
                let html = '';
                alerts.forEach(alert => {
                    html += `
                        <div class="alert-item alert-${alert.type}">
                            <i class="fas fa-${alert.type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                            <div class="alert-content">
                                <p>${alert.message}</p>
                                <span class="alert-time">${alert.time}</span>
                            </div>
                        </div>
                    `;
                });
                container.innerHTML = html;
            }
        }
        
        // 满减活动其他管理功能
        function editDiscountActivity(id) {
            const discount = discountActivitiesData.find(d => d.id === id);
            if (!discount) return;
            
            // 填充编辑表单
            document.getElementById('discountName').value = discount.name;
            document.getElementById('discountType').value = discount.type;
            document.getElementById('discountDescription').value = discount.description;
            document.getElementById('discountStartTime').value = discount.startTime.replace(' ', 'T');
            document.getElementById('discountEndTime').value = discount.endTime.replace(' ', 'T');
            
            // 设置高级选项
            document.querySelector('[name="enableUserLimit"]').checked = discount.enableUserLimit;
            document.querySelector('[name="enableTotalLimit"]').checked = discount.enableTotalLimit;
            document.querySelector('[name="enableCouponStack"]').checked = discount.enableCouponStack;
            document.querySelector('[name="enableMemberOnly"]').checked = discount.enableMemberOnly;
            document.querySelector('[name="enableNotification"]').checked = discount.enableNotification;
            
            // 更新规则和范围设置
            updateDiscountRules();
            updateScopeSettings();
            
            // 修改模态框标题和按钮
            document.querySelector('#createDiscountModal .modal-header h3').textContent = '编辑满减活动';
            document.querySelector('#createDiscountModal .btn-primary').textContent = '保存修改';
            document.querySelector('#createDiscountModal .btn-primary').setAttribute('onclick', `updateDiscountActivity(${id})`);
            
            openModal('createDiscountModal');
        }
        
        function deleteDiscountActivity(id) {
            const discount = discountActivitiesData.find(d => d.id === id);
            if (!discount) return;
            
            showConfirm(
                '确认删除',
                `确定要删除满减活动 "${discount.name}" 吗？此操作不可撤销。`,
                () => {
                    discountActivitiesData = discountActivitiesData.filter(d => d.id !== id);
                    filteredDiscountData = filteredDiscountData.filter(d => d.id !== id);
                    renderDiscountTable();
                    setupDiscountPagination();
                    showMessage('success', '满减活动删除成功');
                }
            );
        }
        
        function toggleDiscountStatus(id) {
            const discount = discountActivitiesData.find(d => d.id === id);
            if (!discount) return;
            
            const newStatus = discount.status === 'active' ? 'paused' : 'active';
            
            showLoading();
            
            setTimeout(() => {
                discount.status = newStatus;
                renderDiscountTable();
                hideLoading();
                showMessage('success', `满减活动已${newStatus === 'active' ? '启用' : '暂停'}`);
            }, 500);
        }
        
        function duplicateDiscountActivity(id) {
            const discount = discountActivitiesData.find(d => d.id === id);
            if (!discount) return;
            
            const newDiscount = {
                ...discount,
                id: Date.now(),
                name: discount.name + ' (副本)',
                status: 'draft',
                participants: 0,
                orders: 0,
                revenue: 0,
                totalSavings: 0
            };
            
            discountActivitiesData.unshift(newDiscount);
            filteredDiscountData = [...discountActivitiesData];
            renderDiscountTable();
            setupDiscountPagination();
            showMessage('success', '满减活动复制成功');
        }
        
        function addTieredRule() {
            const tieredRules = document.querySelector('.tiered-rules');
            const newRule = document.createElement('div');
            newRule.className = 'rule-item';
            newRule.innerHTML = `
                <label>满金额</label>
                <input type="number" name="threshold" placeholder="输入金额" min="0.01" step="0.01">
                <label>减金额</label>
                <input type="number" name="discount" placeholder="输入金额" min="0.01" step="0.01">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeRule(this)">
                    <i class="fas fa-minus"></i>
                </button>
            `;
            
            const addButton = tieredRules.querySelector('button[onclick="addTieredRule()"]');
            tieredRules.insertBefore(newRule, addButton);
        }
        
        function removeRule(button) {
            button.closest('.rule-item').remove();
        }
        
        function searchProducts(query) {
            // 模拟商品搜索
            const products = [
                { id: 1, name: 'iPhone 15 Pro', category: '数码', price: 7999 },
                { id: 2, name: 'MacBook Air M3', category: '数码', price: 8999 },
                { id: 3, name: 'Nike Air Max', category: '运动', price: 899 },
                { id: 4, name: '兰蔻小黑瓶', category: '美妆', price: 1280 },
                { id: 5, name: '优衣库羽绒服', category: '服装', price: 399 }
            ];
            
            const filteredProducts = products.filter(p => 
                p.name.toLowerCase().includes(query.toLowerCase())
            );
            
            const container = document.getElementById('productSelectorList');
            let html = '';
            
            if (filteredProducts.length === 0) {
                html = '<div class="no-results">未找到相关商品</div>';
            } else {
                filteredProducts.forEach(product => {
                    html += `
                        <div class="product-item">
                            <label>
                                <input type="checkbox" value="${product.id}">
                                <div class="product-info">
                                    <span class="product-name">${product.name}</span>
                                    <span class="product-meta">${product.category} · ¥${product.price}</span>
                                </div>
                            </label>
                        </div>
                    `;
                });
            }
            
            container.innerHTML = html;
        }
        
        // 满减活动批量操作功能
        function batchDiscountActions() {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要操作的满减活动');
                return;
            }
            
            // 更新选中数量显示
            document.getElementById('selectedDiscountCount').textContent = selected.length;
            openModal('batchDiscountModal');
        }
        
        function batchDiscountOperation(action) {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要操作的满减活动');
                return;
            }
            
            const ids = Array.from(selected).map(cb => parseInt(cb.value));
            let message = '';
            
            switch(action) {
                case 'enable':
                    message = `确定要启用选中的 ${selected.length} 个满减活动吗？`;
                    break;
                case 'pause':
                    message = `确定要暂停选中的 ${selected.length} 个满减活动吗？`;
                    break;
                case 'delete':
                    message = `确定要删除选中的 ${selected.length} 个满减活动吗？此操作不可撤销。`;
                    break;
            }
            
            showConfirm('批量操作确认', message, () => {
                showLoading();
                
                setTimeout(() => {
                    if (action === 'delete') {
                        discountActivitiesData = discountActivitiesData.filter(d => !ids.includes(d.id));
                        filteredDiscountData = filteredDiscountData.filter(d => !ids.includes(d.id));
                    } else {
                        const newStatus = action === 'enable' ? 'active' : 'paused';
                        ids.forEach(id => {
                            const discount = discountActivitiesData.find(d => d.id === id);
                            if (discount) {
                                discount.status = newStatus;
                            }
                        });
                    }
                    
                    renderDiscountTable();
                    setupDiscountPagination();
                    hideLoading();
                    closeModal('batchDiscountModal');
                    
                    const actionText = action === 'enable' ? '启用' : (action === 'pause' ? '暂停' : '删除');
                    showMessage('success', `成功${actionText}了 ${selected.length} 个满减活动`);
                }, 1000);
            });
        }
        
        function applyBatchTimeChange(type) {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要操作的满减活动');
                return;
            }
            
            const timeInput = type === 'start' ? 'batchStartTime' : 'batchEndTime';
            const newTime = document.getElementById(timeInput).value;
            
            if (!newTime) {
                showMessage('warning', '请选择时间');
                return;
            }
            
            showLoading();
            
            setTimeout(() => {
                const ids = Array.from(selected).map(cb => parseInt(cb.value));
                ids.forEach(id => {
                    const discount = discountActivitiesData.find(d => d.id === id);
                    if (discount) {
                        if (type === 'start') {
                            discount.startTime = newTime;
                        } else {
                            discount.endTime = newTime;
                        }
                    }
                });
                
                renderDiscountTable();
                hideLoading();
                showMessage('success', `成功批量调整了 ${selected.length} 个活动的${type === 'start' ? '开始' : '结束'}时间`);
            }, 800);
        }
        
        function applyBatchDiscountChange() {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要操作的满减活动');
                return;
            }
            
            const type = document.getElementById('batchDiscountType').value;
            const value = parseFloat(document.getElementById('batchDiscountValue').value);
            
            if (!value || value <= 0) {
                showMessage('warning', '请输入有效的调整值');
                return;
            }
            
            showLoading();
            
            setTimeout(() => {
                const ids = Array.from(selected).map(cb => parseInt(cb.value));
                ids.forEach(id => {
                    const discount = discountActivitiesData.find(d => d.id === id);
                    if (discount && discount.rules) {
                        // 这里简化处理，实际应该根据具体规则结构调整
                        // 仅作为示例，实际应用需要根据具体业务逻辑调整
                        console.log(`调整活动 ${discount.name} 的优惠力度: ${type} ${value}`);
                    }
                });
                
                renderDiscountTable();
                hideLoading();
                showMessage('success', `成功批量调整了 ${selected.length} 个活动的优惠力度`);
            }, 800);
        }
        
        function batchCopyDiscountActivities() {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要复制的满减活动');
                return;
            }
            
            const prefix = document.getElementById('copyPrefix').value || '副本_';
            const copyRules = document.getElementById('copyRules').checked;
            const copyScope = document.getElementById('copyScope').checked;
            const copySettings = document.getElementById('copySettings').checked;
            
            showLoading();
            
            setTimeout(() => {
                const ids = Array.from(selected).map(cb => parseInt(cb.value));
                const newActivities = [];
                
                ids.forEach(id => {
                    const discount = discountActivitiesData.find(d => d.id === id);
                    if (discount) {
                        const newDiscount = {
                            id: Date.now() + Math.random(),
                            name: prefix + discount.name,
                            type: discount.type,
                            description: discount.description,
                            startTime: discount.startTime,
                            endTime: discount.endTime,
                            status: 'draft',
                            participants: 0,
                            orders: 0,
                            revenue: 0,
                            totalSavings: 0,
                            rules: copyRules ? discount.rules : null,
                            scope: copyScope ? discount.scope : null,
                            settings: copySettings ? discount.settings : null
                        };
                        
                        newActivities.push(newDiscount);
                    }
                });
                
                discountActivitiesData.unshift(...newActivities);
                filteredDiscountData = [...discountActivitiesData];
                renderDiscountTable();
                setupDiscountPagination();
                hideLoading();
                closeModal('batchDiscountModal');
                showMessage('success', `成功复制了 ${newActivities.length} 个满减活动`);
            }, 1000);
        }
        
        function batchExportDiscountActivities() {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要导出的满减活动');
                return;
            }
            
            const format = document.getElementById('exportFormat').value;
            const exportBasic = document.getElementById('exportBasic').checked;
            const exportRules = document.getElementById('exportRules').checked;
            const exportStats = document.getElementById('exportStats').checked;
            const exportAdvanced = document.getElementById('exportAdvanced').checked;
            
            showLoading();
            
            setTimeout(() => {
                const ids = Array.from(selected).map(cb => parseInt(cb.value));
                const exportData = discountActivitiesData
                    .filter(d => ids.includes(d.id))
                    .map(discount => {
                        const data = {};
                        
                        if (exportBasic) {
                            data['活动名称'] = discount.name;
                            data['活动类型'] = getDiscountTypeText(discount.type);
                            data['活动描述'] = discount.description;
                            data['开始时间'] = discount.startTime;
                            data['结束时间'] = discount.endTime;
                            data['状态'] = getDiscountStatusText(discount.status);
                        }
                        
                        if (exportRules) {
                            data['优惠规则'] = discount.rules ? JSON.stringify(discount.rules) : '无';
                        }
                        
                        if (exportStats) {
                            data['参与人数'] = discount.participants;
                            data['完成订单'] = discount.orders;
                            data['营收金额'] = discount.revenue;
                            data['节省金额'] = discount.totalSavings;
                        }
                        
                        if (exportAdvanced) {
                            data['适用范围'] = discount.scope ? JSON.stringify(discount.scope) : '无';
                            data['高级设置'] = discount.settings ? JSON.stringify(discount.settings) : '无';
                        }
                        
                        return data;
                    });
                
                hideLoading();
                closeModal('batchDiscountModal');
                showMessage('success', `成功导出了 ${exportData.length} 个满减活动的数据（${format.toUpperCase()}格式）`);
                console.log('导出数据:', exportData);
            }, 1500);
        }
        
        function selectAllDiscountActivities() {
            const checkboxes = document.querySelectorAll('.discount-select');
            checkboxes.forEach(cb => cb.checked = true);
            document.getElementById('selectedDiscountCount').textContent = checkboxes.length;
        }
        
        function clearDiscountSelection() {
            const checkboxes = document.querySelectorAll('.discount-select');
            checkboxes.forEach(cb => cb.checked = false);
            document.getElementById('selectedDiscountCount').textContent = '0';
        }
        
        function toggleSelectAllDiscounts() {
            const selectAll = document.getElementById('selectAllDiscounts');
            const checkboxes = document.querySelectorAll('.discount-select');
            
            checkboxes.forEach(cb => {
                cb.checked = selectAll.checked;
            });
            
            const selectedCount = selectAll.checked ? checkboxes.length : 0;
            const countElement = document.getElementById('selectedDiscountCount');
            if (countElement) {
                countElement.textContent = selectedCount;
            }
        }
        
        function toggleDiscountSelection(id) {
            const checkbox = document.querySelector(`.discount-select[value="${id}"]`);
            if (checkbox) {
                // 更新选中数量显示
                const selectedCount = document.querySelectorAll('.discount-select:checked').length;
                const countElement = document.getElementById('selectedDiscountCount');
                if (countElement) {
                    countElement.textContent = selectedCount;
                }
                
                // 更新全选复选框状态
                const allCheckboxes = document.querySelectorAll('.discount-select');
                const checkedCheckboxes = document.querySelectorAll('.discount-select:checked');
                const selectAllCheckbox = document.getElementById('selectAllDiscounts');
                
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length && allCheckboxes.length > 0;
                    selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
                }
            }
        }
        
        function editDiscountActivity(id) {
            const discount = discountActivitiesData.find(d => d.id === id);
            if (!discount) return;
            
            // 这里应该打开编辑模态框并填充数据
            showMessage('info', `编辑满减活动: ${discount.name}`);
            console.log('编辑满减活动:', discount);
        }
        
        function viewDiscountDetails(id) {
            const discount = discountActivitiesData.find(d => d.id === id);
            if (!discount) return;
            
            // 显示活动详情
            let detailsHtml = `
                <div class="discount-details">
                    <h4>${discount.name}</h4>
                    <p><strong>类型:</strong> ${getDiscountTypeText(discount.type)}</p>
                    <p><strong>描述:</strong> ${discount.description}</p>
                    <p><strong>开始时间:</strong> ${discount.startTime}</p>
                    <p><strong>结束时间:</strong> ${discount.endTime}</p>
                    <p><strong>状态:</strong> ${getDiscountStatusText(discount.status)}</p>
                    <p><strong>参与人数:</strong> ${discount.participants.toLocaleString()}</p>
                    <p><strong>完成订单:</strong> ${discount.orders.toLocaleString()}</p>
                    <p><strong>营收金额:</strong> ¥${discount.revenue.toLocaleString()}</p>
                    <p><strong>节省金额:</strong> ¥${discount.totalSavings.toLocaleString()}</p>
                </div>
            `;
            
            showAlert('满减活动详情', detailsHtml);
        }
        
        function pauseDiscountActivity(id) {
            const discount = discountActivitiesData.find(d => d.id === id);
            if (!discount) return;
            
            showConfirm('暂停活动', `确定要暂停满减活动"${discount.name}"吗？`, () => {
                showLoading();
                setTimeout(() => {
                    discount.status = 'paused';
                    renderDiscountTable();
                    hideLoading();
                    showMessage('success', '满减活动已暂停');
                }, 500);
            });
        }
        
        function startDiscountActivity(id) {
            const discount = discountActivitiesData.find(d => d.id === id);
            if (!discount) return;
            
            showConfirm('启动活动', `确定要启动满减活动"${discount.name}"吗？`, () => {
                showLoading();
                setTimeout(() => {
                    discount.status = 'active';
                    renderDiscountTable();
                    hideLoading();
                    showMessage('success', '满减活动已启动');
                }, 500);
            });
        }
        
        function deleteDiscountActivity(id) {
            const discount = discountActivitiesData.find(d => d.id === id);
            if (!discount) return;
            
            showConfirm('删除活动', `确定要删除满减活动"${discount.name}"吗？此操作不可撤销。`, () => {
                showLoading();
                setTimeout(() => {
                    discountActivitiesData = discountActivitiesData.filter(d => d.id !== id);
                    filteredDiscountData = filteredDiscountData.filter(d => d.id !== id);
                    renderDiscountTable();
                    setupDiscountPagination();
                    hideLoading();
                    showMessage('success', '满减活动已删除');
                }, 500);
            });
        }
        
        function batchDeleteDiscounts() {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要删除的满减活动');
                return;
            }
            
            showConfirm(
                '批量删除确认',
                `确定要删除选中的 ${selected.length} 个满减活动吗？此操作不可撤销。`,
                () => {
                    const ids = Array.from(selected).map(cb => parseInt(cb.value));
                    discountActivitiesData = discountActivitiesData.filter(d => !ids.includes(d.id));
                    filteredDiscountData = filteredDiscountData.filter(d => !ids.includes(d.id));
                    renderDiscountTable();
                    setupDiscountPagination();
                    showMessage('success', `成功删除 ${selected.length} 个满减活动`);
                }
            );
        }
        
        function batchToggleDiscountStatus(status) {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要操作的满减活动');
                return;
            }
            
            showLoading();
            
            setTimeout(() => {
                const ids = Array.from(selected).map(cb => parseInt(cb.value));
                ids.forEach(id => {
                    const discount = discountActivitiesData.find(d => d.id === id);
                    if (discount) {
                        discount.status = status;
                    }
                });
                
                renderDiscountTable();
                hideLoading();
                showMessage('success', `成功${status === 'active' ? '启用' : '暂停'}了 ${selected.length} 个满减活动`);
            }, 1000);
        }
        
        function exportDiscountData() {
            const data = filteredDiscountData.map(discount => ({
                '活动名称': discount.name,
                '活动类型': getDiscountTypeText(discount.type),
                '开始时间': discount.startTime,
                '结束时间': discount.endTime,
                '状态': getDiscountStatusText(discount.status),
                '参与人数': discount.participants,
                '完成订单': discount.orders,
                '节省金额': discount.totalSavings
            }));
            
            // 模拟导出
            showLoading();
            setTimeout(() => {
                hideLoading();
                showMessage('success', '满减活动数据导出成功');
                console.log('导出数据:', data);
            }, 1500);
        }
        
        function importDiscountTemplate() {
            // 创建文件输入元素
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.xlsx,.xls,.csv';
            fileInput.style.display = 'none';
            
            fileInput.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) return;
                
                showLoading();
                
                // 模拟文件处理
                setTimeout(() => {
                    hideLoading();
                    showMessage('success', `成功导入满减活动模板: ${file.name}`);
                    
                    // 模拟添加几个新的满减活动
                    const newActivities = [
                        {
                            id: Date.now() + 1,
                            name: '导入活动示例1',
                            type: 'full-reduction',
                            description: '通过模板导入的满减活动',
                            startTime: '2024-01-01 10:00',
                            endTime: '2024-01-31 23:59',
                            status: 'draft',
                            participants: 0,
                            orders: 0,
                            revenue: 0,
                            totalSavings: 0,
                            rules: [{ threshold: 100, discount: 20 }],
                            scope: { type: 'all', details: [] },
                            settings: {}
                        }
                    ];
                    
                    discountActivitiesData.unshift(...newActivities);
                    filteredDiscountData = [...discountActivitiesData];
                    renderDiscountTable();
                    setupDiscountPagination();
                }, 2000);
            };
            
            document.body.appendChild(fileInput);
            fileInput.click();
            document.body.removeChild(fileInput);
        }
        
        // 批量操作时间调整功能
        function applyBatchTimeChange(type) {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要操作的满减活动');
                return;
            }
            
            const timeInput = type === 'start' ? 'batchStartTime' : 'batchEndTime';
            const newTime = document.getElementById(timeInput).value;
            
            if (!newTime) {
                showMessage('warning', '请选择要调整的时间');
                return;
            }
            
            showConfirm('批量时间调整', `确定要将选中的 ${selected.length} 个活动的${type === 'start' ? '开始' : '结束'}时间调整为 ${newTime} 吗？`, () => {
                showLoading();
                setTimeout(() => {
                    const ids = Array.from(selected).map(cb => parseInt(cb.value));
                    ids.forEach(id => {
                        const activity = discountActivitiesData.find(d => d.id === id);
                        if (activity) {
                            if (type === 'start') {
                                activity.startTime = newTime.replace('T', ' ');
                            } else {
                                activity.endTime = newTime.replace('T', ' ');
                            }
                        }
                    });
                    
                    renderDiscountTable();
                    hideLoading();
                    showMessage('success', `成功调整 ${selected.length} 个活动的${type === 'start' ? '开始' : '结束'}时间`);
                    document.getElementById(timeInput).value = '';
                }, 1000);
            });
        }
        
        // 批量优惠力度调整
        function applyBatchDiscountChange() {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要操作的满减活动');
                return;
            }
            
            const adjustType = document.getElementById('batchDiscountType').value;
            const adjustValue = parseFloat(document.getElementById('batchDiscountValue').value);
            
            if (!adjustValue || adjustValue <= 0) {
                showMessage('warning', '请输入有效的调整值');
                return;
            }
            
            showConfirm('批量优惠调整', `确定要对选中的 ${selected.length} 个活动进行优惠力度调整吗？`, () => {
                showLoading();
                setTimeout(() => {
                    const ids = Array.from(selected).map(cb => parseInt(cb.value));
                    ids.forEach(id => {
                        const activity = discountActivitiesData.find(d => d.id === id);
                        if (activity && activity.rules) {
                            activity.rules.forEach(rule => {
                                if (adjustType === 'increase') {
                                    rule.discount += adjustValue;
                                } else if (adjustType === 'decrease') {
                                    rule.discount = Math.max(0, rule.discount - adjustValue);
                                } else if (adjustType === 'multiply') {
                                    rule.discount *= adjustValue;
                                }
                                rule.discount = Math.round(rule.discount * 100) / 100; // 保留两位小数
                            });
                        }
                    });
                    
                    renderDiscountTable();
                    hideLoading();
                    showMessage('success', `成功调整 ${selected.length} 个活动的优惠力度`);
                    document.getElementById('batchDiscountValue').value = '';
                }, 1000);
            });
        }
        
        // 批量复制活动
        function batchCopyDiscountActivities() {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要复制的满减活动');
                return;
            }
            
            const prefix = document.getElementById('copyPrefix').value || '副本_';
            const copyRules = document.getElementById('copyRules').checked;
            const copyScope = document.getElementById('copyScope').checked;
            const copySettings = document.getElementById('copySettings').checked;
            
            showConfirm('批量复制活动', `确定要复制选中的 ${selected.length} 个活动吗？`, () => {
                showLoading();
                setTimeout(() => {
                    const ids = Array.from(selected).map(cb => parseInt(cb.value));
                    const newActivities = [];
                    
                    ids.forEach(id => {
                        const original = discountActivitiesData.find(d => d.id === id);
                        if (original) {
                            const copy = {
                                ...original,
                                id: Date.now() + Math.random(),
                                name: prefix + original.name,
                                status: 'draft',
                                participants: 0,
                                orders: 0,
                                revenue: 0,
                                totalSavings: 0
                            };
                            
                            if (!copyRules) copy.rules = [];
                            if (!copyScope) copy.scope = { type: 'all', details: [] };
                            if (!copySettings) copy.settings = {};
                            
                            newActivities.push(copy);
                        }
                    });
                    
                    discountActivitiesData.unshift(...newActivities);
                    filteredDiscountData = [...discountActivitiesData];
                    renderDiscountTable();
                    setupDiscountPagination();
                    hideLoading();
                    showMessage('success', `成功复制 ${newActivities.length} 个满减活动`);
                }, 1500);
            });
        }
        
        // 批量导出活动
        function batchExportDiscountActivities() {
            const selected = document.querySelectorAll('.discount-select:checked');
            if (selected.length === 0) {
                showMessage('warning', '请先选择要导出的满减活动');
                return;
            }
            
            const format = document.getElementById('exportFormat').value;
            const includeBasic = document.getElementById('exportBasic').checked;
            const includeRules = document.getElementById('exportRules').checked;
            const includeStats = document.getElementById('exportStats').checked;
            const includeAdvanced = document.getElementById('exportAdvanced').checked;
            
            showLoading();
            setTimeout(() => {
                const ids = Array.from(selected).map(cb => parseInt(cb.value));
                const exportData = discountActivitiesData.filter(d => ids.includes(d.id));
                
                hideLoading();
                showMessage('success', `成功导出 ${exportData.length} 个满减活动数据 (${format.toUpperCase()}格式)`);
                console.log('导出数据:', exportData, { includeBasic, includeRules, includeStats, includeAdvanced });
            }, 2000);
        }
        
        // 全选/取消全选
        function selectAllDiscountActivities() {
            const checkboxes = document.querySelectorAll('.discount-select');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            
            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
                toggleDiscountSelection(cb.value);
            });
        }
        
        function clearDiscountSelection() {
            const checkboxes = document.querySelectorAll('.discount-select');
            checkboxes.forEach(cb => {
                cb.checked = false;
            });
            
            const countElement = document.getElementById('selectedDiscountCount');
            if (countElement) {
                countElement.textContent = '0';
            }
            
            // 更新全选复选框状态
            const selectAllCheckbox = document.getElementById('selectAllDiscounts');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }
        }
        
        function toggleSelectAllDiscounts() {
            const selectAllCheckbox = document.getElementById('selectAllDiscounts');
            const checkboxes = document.querySelectorAll('.discount-select');
            
            checkboxes.forEach(cb => {
                cb.checked = selectAllCheckbox.checked;
            });
            
            // 更新选中数量显示
            const selectedCount = selectAllCheckbox.checked ? checkboxes.length : 0;
            const countElement = document.getElementById('selectedDiscountCount');
            if (countElement) {
                countElement.textContent = selectedCount;
            }
        }
        
        // 实时监控功能
        let discountMonitorActive = false;
        let discountMonitorTimer = null;
        
        function toggleDiscountMonitor() {
            const button = document.getElementById('discountMonitorToggle');
            
            if (discountMonitorActive) {
                // 停止监控
                discountMonitorActive = false;
                clearInterval(discountMonitorTimer);
                discountMonitorTimer = null;
                
                button.innerHTML = '<i class="fas fa-play"></i> 开始监控';
                button.className = 'btn btn-primary';
                showMessage('success', '满减活动监控已停止');
            } else {
                // 开始监控
                discountMonitorActive = true;
                startDiscountMonitoring();
                
                button.innerHTML = '<i class="fas fa-stop"></i> 停止监控';
                button.className = 'btn btn-danger';
                showMessage('success', '满减活动监控已启动');
            }
        }
        
        function startDiscountMonitoring() {
            // 立即更新一次数据
            updateDiscountMonitorData();
            
            // 设置定时器，每3秒更新一次
            discountMonitorTimer = setInterval(() => {
                if (discountMonitorActive) {
                    updateDiscountMonitorData();
                }
            }, 3000);
        }
        
        function updateDiscountMonitorData() {
            updateDiscountOverview();
            updateLiveDiscountActivities();
            updateDiscountAlerts();
            updateDiscountCharts();
        }
        
        function updateDiscountOverview() {
            const activeDiscounts = discountActivitiesData.filter(d => d.status === 'active');
            const totalParticipants = discountActivitiesData.reduce((sum, d) => sum + d.participants, 0);
            const totalOrders = discountActivitiesData.reduce((sum, d) => sum + d.orders, 0);
            const totalSavings = discountActivitiesData.reduce((sum, d) => sum + d.totalSavings, 0);
            
            // 模拟实时变化
            const randomChange = () => Math.floor(Math.random() * 10) - 5;
            
            document.getElementById('activeDiscountCount').textContent = activeDiscounts.length;
            document.getElementById('liveDiscountUsers').textContent = formatNumber(totalParticipants + randomChange());
            document.getElementById('discountOrderCount').textContent = formatNumber(totalOrders + Math.floor(Math.random() * 5));
            document.getElementById('totalSavings').textContent = '¥' + formatNumber(totalSavings + Math.floor(Math.random() * 1000));
            
            // 更新变化百分比
            document.getElementById('activeDiscountChange').textContent = `+${Math.floor(Math.random() * 5)}%`;
            document.getElementById('liveUsersChange').textContent = `+${Math.floor(Math.random() * 15)}%`;
            document.getElementById('orderCountChange').textContent = `+${Math.floor(Math.random() * 8)}%`;
            document.getElementById('savingsChange').textContent = `+${Math.floor(Math.random() * 12)}%`;
        }
        
        function updateLiveDiscountActivities() {
            const activeDiscounts = discountActivitiesData
                .filter(d => d.status === 'active')
                .sort((a, b) => b.participants - a.participants)
                .slice(0, 6);
            
            const container = document.getElementById('liveDiscountActivities');
            let html = '';
            
            if (activeDiscounts.length === 0) {
                html = '<div class="no-activities">暂无进行中的满减活动</div>';
            } else {
                activeDiscounts.forEach(discount => {
                    const participationRate = Math.min((discount.participants / 1000) * 100, 100);
                    const timeRemaining = calculateTimeRemaining(discount.endTime);
                    
                    html += `
                        <div class="live-activity-card">
                            <div class="activity-header">
                                <h5>${discount.name}</h5>
                                <span class="activity-type">${getDiscountTypeText(discount.type)}</span>
                            </div>
                            <div class="activity-stats">
                                <div class="stat-item">
                                    <span class="stat-label">参与人数</span>
                                    <span class="stat-value">${formatNumber(discount.participants)}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">完成订单</span>
                                    <span class="stat-value">${formatNumber(discount.orders)}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">剩余时间</span>
                                    <span class="stat-value">${timeRemaining}</span>
                                </div>
                            </div>
                            <div class="activity-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${participationRate}%"></div>
                                </div>
                                <span class="progress-text">参与度 ${participationRate.toFixed(1)}%</span>
                            </div>
                        </div>
                    `;
                });
            }
            
            container.innerHTML = html;
        }
        
        function updateDiscountAlerts() {
            const alerts = [];
            const now = new Date();
            
            // 检查即将结束的活动
            discountActivitiesData.forEach(discount => {
                if (discount.status === 'active') {
                    const endTime = new Date(discount.endTime);
                    const hoursRemaining = (endTime - now) / (1000 * 60 * 60);
                    
                    if (hoursRemaining < 2 && hoursRemaining > 0) {
                        alerts.push({
                            type: 'warning',
                            message: `满减活动"${discount.name}"将在 ${Math.floor(hoursRemaining * 60)} 分钟后结束`,
                            time: new Date().toLocaleTimeString()
                        });
                    }
                    
                    // 检查高参与度活动
                    if (discount.participants > 500) {
                        alerts.push({
                            type: 'info',
                            message: `满减活动"${discount.name}"参与度很高，当前 ${discount.participants} 人参与`,
                            time: new Date().toLocaleTimeString()
                        });
                    }
                }
            });
            
            // 随机添加一些系统提醒
            if (Math.random() < 0.3) {
                alerts.push({
                    type: 'success',
                    message: '系统运行正常，所有满减活动状态良好',
                    time: new Date().toLocaleTimeString()
                });
            }
            
            const container = document.getElementById('discountAlertList');
            let html = '';
            
            if (alerts.length === 0) {
                html = '<div class="no-alerts">暂无系统警报</div>';
            } else {
                alerts.forEach(alert => {
                    const iconClass = alert.type === 'warning' ? 'fa-exclamation-triangle' : 
                                     alert.type === 'info' ? 'fa-info-circle' : 'fa-check-circle';
                    
                    html += `
                        <div class="alert-item alert-${alert.type}">
                            <div class="alert-icon">
                                <i class="fas ${iconClass}"></i>
                            </div>
                            <div class="alert-content">
                                <div class="alert-message">${alert.message}</div>
                                <div class="alert-time">${alert.time}</div>
                            </div>
                        </div>
                    `;
                });
            }
            
            container.innerHTML = html;
        }
        
        function updateDiscountCharts() {
            // 更新图表占位符的状态
            const trendChart = document.getElementById('discountTrendChart');
            const distributionChart = document.getElementById('discountDistributionChart');
            
            if (trendChart) {
                trendChart.querySelector('small').textContent = `数据每5秒更新一次 - 最后更新: ${new Date().toLocaleTimeString()}`;
            }
            
            if (distributionChart) {
                distributionChart.querySelector('small').textContent = `显示不同满减档位的使用情况 - 最后更新: ${new Date().toLocaleTimeString()}`;
            }
        }
        
        function refreshDiscountMonitor() {
            showLoading();
            setTimeout(() => {
                updateDiscountMonitorData();
                hideLoading();
                showMessage('success', '监控数据已刷新');
            }, 800);
        }
        
        function exportDiscountReport() {
            const reportData = {
                timestamp: new Date().toISOString(),
                activeActivities: discountActivitiesData.filter(d => d.status === 'active').length,
                totalParticipants: discountActivitiesData.reduce((sum, d) => sum + d.participants, 0),
                totalOrders: discountActivitiesData.reduce((sum, d) => sum + d.orders, 0),
                totalSavings: discountActivitiesData.reduce((sum, d) => sum + d.totalSavings, 0),
                activities: discountActivitiesData.map(d => ({
                    name: d.name,
                    type: d.type,
                    status: d.status,
                    participants: d.participants,
                    orders: d.orders,
                    revenue: d.revenue,
                    savings: d.totalSavings
                }))
            };
            
            showLoading();
            setTimeout(() => {
                hideLoading();
                showMessage('success', '满减活动监控报告导出成功');
                console.log('监控报告:', reportData);
            }, 1000);
        }
        
        // 通用工具函数
        function formatNumber(num) {
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + '万';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'k';
            }
            return num.toString();
        }
        
        function calculateTimeRemaining(endTime) {
            const now = new Date();
            const end = new Date(endTime);
            const diff = end - now;
            
            if (diff <= 0) {
                return '已结束';
            }
            
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            
            if (days > 0) {
                return `${days}天${hours}小时`;
            } else if (hours > 0) {
                return `${hours}小时${minutes}分钟`;
            } else {
                return `${minutes}分钟`;
            }
        }
    </script>
</body>
</html> 