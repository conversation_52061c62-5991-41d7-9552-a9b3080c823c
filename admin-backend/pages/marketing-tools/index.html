<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营销工具中心 - TeleShop Admin</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .marketing-center {
            padding: 20px;
        }
        
        .marketing-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 40px;
            color: white;
            text-align: center;
            margin-bottom: 40px;
        }
        
        .hero-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .hero-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 30px;
        }
        
        .hero-stat {
            text-align: center;
        }
        
        .hero-stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .hero-stat-label {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .tools-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .category-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .category-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--category-color);
        }
        
        .category-card.promotional { --category-color: linear-gradient(90deg, #667eea, #764ba2); }
        .category-card.social { --category-color: linear-gradient(90deg, #f093fb, #f5576c); }
        .category-card.membership { --category-color: linear-gradient(90deg, #4facfe, #00f2fe); }
        .category-card.newretail { --category-color: linear-gradient(90deg, #43e97b, #38f9d7); }
        .category-card.analytics { --category-color: linear-gradient(90deg, #fa709a, #fee140); }
        
        .category-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .category-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: white;
            font-size: 24px;
        }
        
        .category-icon.promotional { background: linear-gradient(135deg, #667eea, #764ba2); }
        .category-icon.social { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .category-icon.membership { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .category-icon.newretail { background: linear-gradient(135deg, #43e97b, #38f9d7); }
        .category-icon.analytics { background: linear-gradient(135deg, #fa709a, #fee140); }
        
        .category-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .category-subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .category-desc {
            color: #555;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .tool-list {
            list-style: none;
            padding: 0;
            margin-bottom: 25px;
        }
        
        .tool-list li {
            padding: 8px 0;
            color: #666;
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .tool-list li:before {
            content: "●";
            color: var(--category-color, #667eea);
            margin-right: 10px;
            font-size: 12px;
        }
        
        .category-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8, #6b3fa0);
            transform: translateY(-2px);
        }
        
        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .quick-actions {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .quick-actions h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .quick-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .quick-btn {
            padding: 12px 24px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            color: #495057;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }
        
        .quick-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="marketing-center">
        <div class="marketing-hero">
            <div class="hero-title">营销工具中心</div>
            <div class="hero-subtitle">
                全面的营销工具套件，助力业务增长和用户获取
            </div>
            <div class="hero-stats">
                <div class="hero-stat">
                    <div class="hero-stat-value">156</div>
                    <div class="hero-stat-label">活跃营销活动</div>
                </div>
                <div class="hero-stat">
                    <div class="hero-stat-value">48.5K</div>
                    <div class="hero-stat-label">参与用户</div>
                </div>
                <div class="hero-stat">
                    <div class="hero-stat-value">3.42</div>
                    <div class="hero-stat-label">平均ROI</div>
                </div>
                <div class="hero-stat">
                    <div class="hero-stat-value">¥1.2M</div>
                    <div class="hero-stat-label">营销收入</div>
                </div>
            </div>
        </div>
        
        <div class="tools-categories">
            <div class="category-card promotional">
                <div class="category-header">
                    <div class="category-icon promotional">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div>
                        <div class="category-title">促销活动</div>
                        <div class="category-subtitle">24个活跃活动</div>
                    </div>
                </div>
                <div class="category-desc">
                    创建各类促销活动，通过优惠和限时抢购提升销量，包含优惠券、满减、秒杀等经典营销工具。
                </div>
                <ul class="tool-list">
                    <li>优惠券管理</li>
                    <li>限时抢购</li>
                    <li>满减活动</li>
                    <li>节日营销</li>
                </ul>
                <div class="category-actions">
                    <a href="promotional-activities.html" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i> 进入管理
                    </a>
                    <a href="#" class="btn btn-outline">
                        <i class="fas fa-plus"></i> 创建活动
                    </a>
                </div>
            </div>
            
            <div class="category-card social">
                <div class="category-header">
                    <div class="category-icon social">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <div>
                        <div class="category-title">社交营销</div>
                        <div class="category-subtitle">1,234个推广员</div>
                    </div>
                </div>
                <div class="category-desc">
                    利用社交裂变和用户互动扩大影响力，通过拼团、砍价、分销等方式实现病毒式传播。
                </div>
                <ul class="tool-list">
                    <li>拼团活动</li>
                    <li>砍价活动</li>
                    <li>邀请好友</li>
                    <li>分销系统</li>
                </ul>
                <div class="category-actions">
                    <a href="social-marketing.html" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i> 进入管理
                    </a>
                    <a href="#" class="btn btn-outline">
                        <i class="fas fa-rocket"></i> 启动裂变
                    </a>
                </div>
            </div>
            
            <div class="category-card membership">
                <div class="category-header">
                    <div class="category-icon membership">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div>
                        <div class="category-title">会员体系</div>
                        <div class="category-subtitle">46,832名会员</div>
                    </div>
                </div>
                <div class="category-desc">
                    构建完整的会员成长体系，通过积分、等级、专享权益等提升用户忠诚度和生命周期价值。
                </div>
                <ul class="tool-list">
                    <li>会员营销</li>
                    <li>积分系统</li>
                    <li>签到有礼</li>
                    <li>新用户礼包</li>
                </ul>
                <div class="category-actions">
                    <a href="membership-system.html" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i> 进入管理
                    </a>
                    <a href="#" class="btn btn-outline">
                        <i class="fas fa-user-plus"></i> 会员升级
                    </a>
                </div>
            </div>
            
            <div class="category-card newretail">
                <div class="category-header">
                    <div class="category-icon newretail">
                        <i class="fas fa-video"></i>
                    </div>
                    <div>
                        <div class="category-title">新零售</div>
                        <div class="category-subtitle">156场直播</div>
                    </div>
                </div>
                <div class="category-desc">
                    结合直播、短视频等新媒体形式，打造沉浸式购物体验，提升用户参与度和转化率。
                </div>
                <ul class="tool-list">
                    <li>预售活动</li>
                    <li>直播带货</li>
                    <li>短视频营销</li>
                    <li>AR试用体验</li>
                </ul>
                <div class="category-actions">
                    <a href="new-retail.html" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i> 进入管理
                    </a>
                    <a href="#" class="btn btn-outline">
                        <i class="fas fa-broadcast-tower"></i> 开始直播
                    </a>
                </div>
            </div>
            
            <div class="category-card analytics">
                <div class="category-header">
                    <div class="category-icon analytics">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div>
                        <div class="category-title">营销数据</div>
                        <div class="category-subtitle">实时统计分析</div>
                    </div>
                </div>
                <div class="category-desc">
                    全面分析营销活动效果，提供数据驱动的决策支持，优化营销策略和投资回报率。
                </div>
                <ul class="tool-list">
                    <li>活动统计</li>
                    <li>转化分析</li>
                    <li>用户行为</li>
                    <li>ROI计算</li>
                </ul>
                <div class="category-actions">
                    <a href="marketing-analytics.html" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i> 查看数据
                    </a>
                    <a href="#" class="btn btn-outline">
                        <i class="fas fa-download"></i> 导出报告
                    </a>
                </div>
            </div>
        </div>
        
        <div class="quick-actions">
            <h3><i class="fas fa-bolt"></i> 快速操作</h3>
            <div class="quick-buttons">
                <a href="#" class="quick-btn">
                    <i class="fas fa-plus"></i> 创建优惠券
                </a>
                <a href="#" class="quick-btn">
                    <i class="fas fa-users"></i> 发起拼团
                </a>
                <a href="#" class="quick-btn">
                    <i class="fas fa-bolt"></i> 开启秒杀
                </a>
                <a href="#" class="quick-btn">
                    <i class="fas fa-gift"></i> 设置签到
                </a>
                <a href="#" class="quick-btn">
                    <i class="fas fa-broadcast-tower"></i> 开始直播
                </a>
                <a href="#" class="quick-btn">
                    <i class="fas fa-chart-line"></i> 查看报表
                </a>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initMarketingCenter();
            bindQuickActions();
        });
        
        function initMarketingCenter() {
            console.log('Marketing Center initialized');
            // 加载营销中心数据
        }
        
        function bindQuickActions() {
            document.querySelectorAll('.quick-btn, .btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // 如果是链接到其他页面，让其正常跳转
                    if (this.href && this.href.includes('.html')) {
                        return;
                    }
                    
                    e.preventDefault();
                    const action = this.textContent.trim();
                    handleQuickAction(action);
                });
            });
        }
        
        function handleQuickAction(action) {
            switch(action) {
                case '创建优惠券':
                    alert('创建优惠券功能开发中...');
                    break;
                case '发起拼团':
                    alert('发起拼团功能开发中...');
                    break;
                case '开启秒杀':
                    alert('开启秒杀功能开发中...');
                    break;
                case '设置签到':
                    alert('设置签到功能开发中...');
                    break;
                case '开始直播':
                    alert('开始直播功能开发中...');
                    break;
                case '查看报表':
                    window.location.href = 'marketing-analytics.html';
                    break;
                default:
                    console.log('Quick action:', action);
            }
        }
    </script>
</body>
</html> 