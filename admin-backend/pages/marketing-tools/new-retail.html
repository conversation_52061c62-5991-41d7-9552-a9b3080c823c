<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新零售营销 - TeleShop Admin</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .newretail-dashboard {
            padding: 20px;
        }
        
        .newretail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
        }
        
        .newretail-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .tool-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .tool-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 20px;
        }
        
        .tool-icon.presale { background: linear-gradient(135deg, #ff758c 0%, #ff7eb3 100%); }
        .tool-icon.livestream { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .tool-icon.shortvideos { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .tool-icon.ar { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        
        .tool-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .tool-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .tool-features {
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
        }
        
        .tool-features li {
            padding: 5px 0;
            color: #555;
            font-size: 13px;
        }
        
        .tool-features li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .tool-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .activity-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-trending { background: #fff3cd; color: #856404; }
        .status-new { background: #d1ecf1; color: #0c5460; }
        .status-beta { background: #f8d7da; color: #721c24; }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #28a745;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="newretail-dashboard">
        <div class="newretail-header">
            <div>
                <h1><i class="fas fa-video"></i> 新零售营销</h1>
                <p>结合直播、短视频等新媒体渠道，打造全新的购物体验</p>
            </div>
            <button class="btn btn-primary">
                <i class="fas fa-broadcast-tower"></i> 开始直播
            </button>
        </div>
        
        <div class="newretail-stats">
            <div class="stat-card">
                <h3>直播场次</h3>
                <div class="stat-value">156</div>
                <div class="stat-change">本月新增 +23</div>
            </div>
            <div class="stat-card">
                <h3>直播观看人数</h3>
                <div class="stat-value">48.5K</div>
                <div class="stat-change">较上月 +35.8%</div>
            </div>
            <div class="stat-card">
                <h3>直播转化率</h3>
                <div class="stat-value">8.9%</div>
                <div class="stat-change">较上月 +2.1%</div>
            </div>
            <div class="stat-card">
                <h3>新零售GMV</h3>
                <div class="stat-value">¥1.2M</div>
                <div class="stat-change">较上月 +48.5%</div>
            </div>
        </div>
        
        <div class="tools-grid">
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon presale">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <div class="tool-title">预售活动</div>
                        <span class="activity-status status-active">8个进行中</span>
                    </div>
                </div>
                <div class="tool-desc">
                    通过预售模式提前锁定销量，降低库存风险，同时为新品发布造势。
                </div>
                <ul class="tool-features">
                    <li>预售商品和价格设置</li>
                    <li>定金支付和尾款规则</li>
                    <li>预售期限和发货时间</li>
                    <li>预售数量限制管理</li>
                    <li>预售订单跟踪处理</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-cog"></i> 创建预售
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-list"></i> 预售管理
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon livestream">
                        <i class="fas fa-video"></i>
                    </div>
                    <div>
                        <div class="tool-title">直播带货</div>
                        <span class="activity-status status-trending">热门趋势</span>
                    </div>
                </div>
                <div class="tool-desc">
                    整合直播功能，实现实时互动销售，提升用户参与度和购买转化率。
                </div>
                <ul class="tool-features">
                    <li>直播间创建和管理</li>
                    <li>商品上架和价格设置</li>
                    <li>实时互动和评论</li>
                    <li>直播数据统计分析</li>
                    <li>主播管理和分成</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-play"></i> 开始直播
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-chart-bar"></i> 直播数据
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon shortvideos">
                        <i class="fas fa-film"></i>
                    </div>
                    <div>
                        <div class="tool-title">短视频营销</div>
                        <span class="activity-status status-new">新功能</span>
                    </div>
                </div>
                <div class="tool-desc">
                    利用短视频展示商品特性，通过视觉冲击力提升商品吸引力和转化率。
                </div>
                <ul class="tool-features">
                    <li>商品短视频制作</li>
                    <li>视频模板和特效</li>
                    <li>视频分享和传播</li>
                    <li>视频数据统计</li>
                    <li>用户UGC内容管理</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i> 创建视频
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-folder"></i> 视频库
                    </button>
                </div>
            </div>
            
            <div class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon ar">
                        <i class="fas fa-cube"></i>
                    </div>
                    <div>
                        <div class="tool-title">AR试用体验</div>
                        <span class="activity-status status-beta">测试版</span>
                    </div>
                </div>
                <div class="tool-desc">
                    通过AR技术让用户虚拟试用商品，提升购物体验和降低退货率。
                </div>
                <ul class="tool-features">
                    <li>AR试用场景搭建</li>
                    <li>3D商品模型管理</li>
                    <li>虚拟试穿试戴</li>
                    <li>AR体验数据分析</li>
                    <li>移动端适配优化</li>
                </ul>
                <div class="tool-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-vr-cardboard"></i> AR设置
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-flask"></i> 体验测试
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initNewRetailData();
            bindEventListeners();
        });
        
        function initNewRetailData() {
            console.log('Loading new retail marketing data...');
        }
        
        function bindEventListeners() {
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.textContent.trim();
                    handleNewRetailAction(action, this);
                });
            });
        }
        
        function handleNewRetailAction(action, element) {
            switch(action) {
                case '开始直播':
                    startLiveStreaming();
                    break;
                case '创建预售':
                    createPresaleActivity();
                    break;
                case '开始直播':
                    startLiveBroadcast();
                    break;
                case '创建视频':
                    createShortVideo();
                    break;
                case 'AR设置':
                    setupARExperience();
                    break;
                default:
                    console.log('New retail action:', action);
            }
        }
        
        function startLiveStreaming() {
            alert('开始直播功能开发中...');
        }
        
        function createPresaleActivity() {
            alert('创建预售活动功能开发中...');
        }
        
        function startLiveBroadcast() {
            alert('直播带货功能开发中...');
        }
        
        function createShortVideo() {
            alert('短视频创建功能开发中...');
        }
        
        function setupARExperience() {
            alert('AR试用体验功能开发中...');
        }
    </script>
</body>
</html> 