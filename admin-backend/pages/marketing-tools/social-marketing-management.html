<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社交营销管理 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .social-marketing-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* 页面头部增强 */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 32px;
            border-radius: 16px;
            margin-bottom: 24px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 8px 0;
        }

        .page-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }

        .header-stats {
            display: flex;
            gap: 24px;
        }

        .header-stat {
            text-align: center;
            padding: 16px;
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 数据概览面板 */
        .data-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .overview-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .overview-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        }

        .overview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .card-group::before { background: linear-gradient(90deg, #667eea, #764ba2); }
        .card-bargain::before { background: linear-gradient(90deg, #ff6b6b, #feca57); }
        .card-referral::before { background: linear-gradient(90deg, #4ecdc4, #44a08d); }
        .card-share::before { background: linear-gradient(90deg, #a8edea, #fed6e3); }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .card-icon.group { background: linear-gradient(135deg, #667eea, #764ba2); }
        .card-icon.bargain { background: linear-gradient(135deg, #ff6b6b, #feca57); }
        .card-icon.referral { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
        .card-icon.share { background: linear-gradient(135deg, #a8edea, #fed6e3); }

        .card-value {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin: 8px 0;
        }

        .card-label {
            color: #7f8c8d;
            font-size: 14px;
            margin: 0;
        }

        .card-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            margin-top: 8px;
        }

        .trend-up { color: #27ae60; }
        .trend-down { color: #e74c3c; }

        .card-actions {
            display: flex;
            gap: 8px;
            margin-top: 16px;
        }

        .card-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            color: #666;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .card-btn:hover {
            background: #f8f9fa;
            border-color: #667eea;
            color: #667eea;
        }

        /* 快速操作面板 */
        .quick-actions {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .action-card {
            background: #f8f9fa;
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-card:hover {
            background: white;
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.15);
        }

        .action-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 20px;
            color: white;
        }

        .action-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .action-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        /* 主要内容区域 */
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }

        .content-left {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .content-right {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        /* 拼团管理增强 */
        .group-buy-panel {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .panel-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .panel-actions {
            display: flex;
            gap: 8px;
        }

        /* 标签页增强 */
        .enhanced-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .tab-item {
            flex: 1;
            padding: 12px 16px;
            text-align: center;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            position: relative;
        }

        .tab-item.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 表格增强 */
        .enhanced-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .filter-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            width: 200px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #374151;
            white-space: nowrap;
        }

        .data-table tbody tr:hover {
            background: #f8f9fa;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active { background: #e8f5e8; color: #2e8b57; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }

        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-action {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .btn-primary { background: #667eea; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }

        .btn-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* 图表容器 */
        .chart-container {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            height: 400px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        /* 智能推荐面板 */
        .ai-recommendation {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .ai-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .ai-suggestions {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .ai-suggestion {
            background: rgba(255,255,255,0.1);
            padding: 12px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .suggestion-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .suggestion-desc {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 活动模板 */
        .template-gallery {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .template-card {
            border: 1px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .template-card:hover {
            border-color: #667eea;
            box-shadow: 0 4px 16px rgba(102,126,234,0.15);
        }

        .template-image {
            height: 120px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
        }

        .template-info {
            padding: 16px;
        }

        .template-name {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .template-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .social-marketing-page {
                padding: 16px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .header-content {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }
            
            .header-stats {
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .data-overview {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .template-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="social-marketing-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title"><i class="fas fa-share-alt"></i> 社交营销管理</h1>
                    <p class="page-subtitle">全面管理拼团、砍价、分享等社交营销活动</p>
                </div>
                <div class="header-stats">
                    <div class="header-stat">
                        <div class="stat-number">48</div>
                        <div class="stat-label">活跃拼团</div>
                    </div>
                    <div class="header-stat">
                        <div class="stat-number">¥156.8万</div>
                        <div class="stat-label">社交销售额</div>
                    </div>
                    <div class="header-stat">
                        <div class="stat-number">89.2%</div>
                        <div class="stat-label">平均成团率</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据概览 -->
        <div class="data-overview">
            <div class="overview-card card-group">
                <div class="card-header">
                    <div class="card-icon group">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="card-value">2,847</div>
                <p class="card-label">总拼团活动</p>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span>本月 +23.5%</span>
                </div>
                <div class="card-actions">
                    <a href="../merchant-management/group-buy-management.html" class="card-btn">管理拼团</a>
                    <a href="#" class="card-btn" onclick="createGroupBuy()">创建活动</a>
                </div>
            </div>
            
            <div class="overview-card card-bargain">
                <div class="card-header">
                    <div class="card-icon bargain">
                        <i class="fas fa-cut"></i>
                    </div>
                </div>
                <div class="card-value">1,562</div>
                <p class="card-label">砍价活动</p>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span>本月 +18.2%</span>
                </div>
                <div class="card-actions">
                    <a href="#" class="card-btn">管理砍价</a>
                    <a href="#" class="card-btn">创建砍价</a>
                </div>
            </div>
            
            <div class="overview-card card-referral">
                <div class="card-header">
                    <div class="card-icon referral">
                        <i class="fas fa-gift"></i>
                    </div>
                </div>
                <div class="card-value">8,945</div>
                <p class="card-label">邀请奖励</p>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span>本月 +31.7%</span>
                </div>
                <div class="card-actions">
                    <a href="#" class="card-btn">奖励设置</a>
                    <a href="#" class="card-btn">邀请统计</a>
                </div>
            </div>
            
            <div class="overview-card card-share">
                <div class="card-header">
                    <div class="card-icon share">
                        <i class="fas fa-share-square"></i>
                    </div>
                </div>
                <div class="card-value">15,632</div>
                <p class="card-label">分享次数</p>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    <span>本月 +42.8%</span>
                </div>
                <div class="card-actions">
                    <a href="#" class="card-btn">分享设置</a>
                    <a href="#" class="card-btn">分享统计</a>
                </div>
            </div>
        </div>

        <!-- 快速操作面板 -->
        <div class="quick-actions">
            <div class="panel-header">
                <h3 class="panel-title"><i class="fas fa-rocket"></i> 快速操作</h3>
            </div>
            <div class="actions-grid">
                <div class="action-card" onclick="createGroupBuy()">
                    <div class="action-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="action-title">创建拼团</div>
                    <div class="action-desc">快速创建新的拼团活动</div>
                </div>
                
                <div class="action-card" onclick="createBargain()">
                    <div class="action-icon">
                        <i class="fas fa-cut"></i>
                    </div>
                    <div class="action-title">创建砍价</div>
                    <div class="action-desc">设置砍价活动吸引用户</div>
                </div>
                
                <div class="action-card" onclick="configureRewards()">
                    <div class="action-icon">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div class="action-title">奖励配置</div>
                    <div class="action-desc">设置邀请和分享奖励</div>
                </div>
                
                <div class="action-card" onclick="viewAnalytics()">
                    <div class="action-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="action-title">数据分析</div>
                    <div class="action-desc">查看详细营销数据</div>
                </div>
            </div>
        </div>

        <!-- AI智能推荐 -->
        <div class="ai-recommendation">
            <div class="ai-title">
                <i class="fas fa-robot"></i>
                AI智能推荐
            </div>
            <div class="ai-suggestions">
                <div class="ai-suggestion">
                    <div class="suggestion-title">🎯 建议提升拼团成功率</div>
                    <div class="suggestion-desc">根据数据分析，3人团成功率最高，建议将5人团调整为3人团</div>
                </div>
                <div class="ai-suggestion">
                    <div class="suggestion-title">⏰ 最佳活动时间</div>
                    <div class="suggestion-desc">晚上8-10点是用户最活跃时间，建议在此时段推送拼团活动</div>
                </div>
                <div class="ai-suggestion">
                    <div class="suggestion-title">💰 价格策略优化</div>
                    <div class="suggestion-desc">拼团价格设置为原价的75-85%时转化率最高</div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <div class="content-left">
                <!-- 拼团管理面板 -->
                <div class="group-buy-panel">
                    <div class="panel-header">
                        <h3 class="panel-title">拼团活动管理</h3>
                        <div class="panel-actions">
                            <button class="btn-action btn-secondary">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button class="btn-action btn-primary" onclick="createGroupBuy()">
                                <i class="fas fa-plus"></i> 创建拼团
                            </button>
                        </div>
                    </div>
                    
                    <div class="enhanced-tabs">
                        <button class="tab-item active" onclick="switchTab('active')">进行中 (24)</button>
                        <button class="tab-item" onclick="switchTab('pending')">待开始 (8)</button>
                        <button class="tab-item" onclick="switchTab('completed')">已完成 (156)</button>
                        <button class="tab-item" onclick="switchTab('analysis')">数据分析</button>
                    </div>
                    
                    <!-- 活动列表 -->
                    <div class="enhanced-table">
                        <div class="table-header">
                            <h4>活动列表</h4>
                            <div class="filter-actions">
                                <input type="search" placeholder="搜索活动..." class="search-input">
                                <select class="filter-select">
                                    <option>全部状态</option>
                                    <option>进行中</option>
                                    <option>已完成</option>
                                    <option>已暂停</option>
                                </select>
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>活动信息</th>
                                        <th>成团进度</th>
                                        <th>参与人数</th>
                                        <th>成团率</th>
                                        <th>剩余时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <img src="../../assets/images/product-default.jpg" alt="商品" style="width: 48px; height: 48px; border-radius: 8px;">
                                                <div>
                                                    <div style="font-weight: 600;">iPhone 15 Pro 拼团</div>
                                                    <div style="font-size: 12px; color: #666;">3人团 · ¥7,999</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="flex: 1; background: #f0f0f0; height: 8px; border-radius: 4px;">
                                                    <div style="width: 67%; background: #667eea; height: 100%; border-radius: 4px;"></div>
                                                </div>
                                                <span style="font-size: 12px;">67%</span>
                                            </div>
                                        </td>
                                        <td>285人</td>
                                        <td>
                                            <span style="color: #28a745; font-weight: 600;">89.2%</span>
                                        </td>
                                        <td>
                                            <span style="color: #dc3545; font-weight: 600;" id="countdown-1">12:34:56</span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-active">进行中</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action btn-primary" onclick="viewDetails(1)">详情</button>
                                                <button class="btn-action btn-secondary" onclick="editActivity(1)">编辑</button>
                                                <button class="btn-action btn-danger" onclick="pauseActivity(1)">暂停</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <img src="../../assets/images/product-default.jpg" alt="商品" style="width: 48px; height: 48px; border-radius: 8px;">
                                                <div>
                                                    <div style="font-weight: 600;">AirPods Pro 拼团</div>
                                                    <div style="font-size: 12px; color: #666;">5人团 · ¥1,599</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="flex: 1; background: #f0f0f0; height: 8px; border-radius: 4px;">
                                                    <div style="width: 100%; background: #28a745; height: 100%; border-radius: 4px;"></div>
                                                </div>
                                                <span style="font-size: 12px;">100%</span>
                                            </div>
                                        </td>
                                        <td>542人</td>
                                        <td>
                                            <span style="color: #28a745; font-weight: 600;">95.6%</span>
                                        </td>
                                        <td>
                                            <span style="color: #28a745; font-weight: 600;">已完成</span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-completed">已完成</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action btn-primary" onclick="viewDetails(2)">详情</button>
                                                <button class="btn-action btn-success" onclick="processOrders(2)">处理订单</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <img src="../../assets/images/product-default.jpg" alt="商品" style="width: 48px; height: 48px; border-radius: 8px;">
                                                <div>
                                                    <div style="font-weight: 600;">MacBook Air 拼团</div>
                                                    <div style="font-size: 12px; color: #666;">2人团 · ¥8,999</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="flex: 1; background: #f0f0f0; height: 8px; border-radius: 4px;">
                                                    <div style="width: 45%; background: #ffc107; height: 100%; border-radius: 4px;"></div>
                                                </div>
                                                <span style="font-size: 12px;">45%</span>
                                            </div>
                                        </td>
                                        <td>128人</td>
                                        <td>
                                            <span style="color: #ffc107; font-weight: 600;">72.5%</span>
                                        </td>
                                        <td>
                                            <span style="color: #dc3545; font-weight: 600;" id="countdown-3">06:15:23</span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-pending">待成团</span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn-action btn-primary" onclick="viewDetails(3)">详情</button>
                                                <button class="btn-action btn-secondary" onclick="promoteActivity(3)">推广</button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-right">
                <!-- 实时数据图表 -->
                <div class="chart-container">
                    <h4 class="chart-title">拼团数据趋势</h4>
                    <canvas id="groupBuyChart" style="width: 100%; height: 300px;"></canvas>
                </div>

                <!-- 活动模板 -->
                <div class="template-gallery">
                    <div class="panel-header">
                        <h3 class="panel-title"><i class="fas fa-layer-group"></i> 活动模板</h3>
                    </div>
                    <div class="template-grid">
                        <div class="template-card" onclick="useTemplate('group-buy')">
                            <div class="template-image">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="template-info">
                                <div class="template-name">拼团活动</div>
                                <div class="template-desc">多人拼团享优惠</div>
                            </div>
                        </div>
                        
                        <div class="template-card" onclick="useTemplate('bargain')">
                            <div class="template-image">
                                <i class="fas fa-cut"></i>
                            </div>
                            <div class="template-info">
                                <div class="template-name">砍价活动</div>
                                <div class="template-desc">邀请好友砍价</div>
                            </div>
                        </div>
                        
                        <div class="template-card" onclick="useTemplate('lottery')">
                            <div class="template-image">
                                <i class="fas fa-dice"></i>
                            </div>
                            <div class="template-info">
                                <div class="template-name">抽奖活动</div>
                                <div class="template-desc">分享抽奖增粉丝</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签切换功能
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 添加当前活动状态
            event.target.classList.add('active');
            
            console.log('切换到标签:', tabName);
            // 这里可以添加加载不同数据的逻辑
        }

        // 创建拼团活动
        function createGroupBuy() {
            window.location.href = '../merchant-management/group-buy-management.html';
        }

        // 创建砍价活动
        function createBargain() {
            alert('砍价活动创建功能开发中...');
        }

        // 配置奖励
        function configureRewards() {
            alert('奖励配置功能开发中...');
        }

        // 查看分析
        function viewAnalytics() {
            alert('数据分析功能开发中...');
        }

        // 查看详情
        function viewDetails(id) {
            console.log('查看详情:', id);
            window.open(`group-buy-detail.html?id=${id}`, '_blank');
        }

        // 编辑活动
        function editActivity(id) {
            console.log('编辑活动:', id);
        }

        // 暂停活动
        function pauseActivity(id) {
            if (confirm('确定要暂停这个拼团活动吗？')) {
                console.log('暂停活动:', id);
                // 显示成功消息
                showToast('活动已暂停', 'success');
            }
        }

        // 推广活动
        function promoteActivity(id) {
            console.log('推广活动:', id);
            alert('推广功能开发中...');
        }

        // 处理订单
        function processOrders(id) {
            console.log('处理订单:', id);
            window.location.href = '../order-management/index.html';
        }

        // 使用模板
        function useTemplate(type) {
            console.log('使用模板:', type);
            if (type === 'group-buy') {
                createGroupBuy();
            } else {
                alert(`${type}模板功能开发中...`);
            }
        }

        // 显示提示信息
        function showToast(message, type = 'info') {
            // 简单的提示实现
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'success' ? '#28a745' : '#667eea'};
                color: white;
                border-radius: 8px;
                z-index: 9999;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }

        // 倒计时功能
        function updateCountdowns() {
            const countdowns = document.querySelectorAll('[id^="countdown-"]');
            countdowns.forEach(countdown => {
                const timeText = countdown.textContent;
                if (timeText.includes(':')) {
                    const parts = timeText.split(':');
                    let hours = parseInt(parts[0]);
                    let minutes = parseInt(parts[1]);
                    let seconds = parseInt(parts[2]);
                    
                    seconds--;
                    if (seconds < 0) {
                        seconds = 59;
                        minutes--;
                        if (minutes < 0) {
                            minutes = 59;
                            hours--;
                            if (hours < 0) {
                                countdown.textContent = '已结束';
                                countdown.style.color = '#999';
                                return;
                            }
                        }
                    }
                    
                    countdown.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            });
        }

        // 初始化图表
        function initChart() {
            const canvas = document.getElementById('groupBuyChart');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制简单的柱状图
            const data = [120, 150, 180, 200, 175, 220, 195];
            const labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
            const maxValue = Math.max(...data);
            const chartHeight = 200;
            const chartWidth = canvas.width - 100;
            const barWidth = chartWidth / data.length - 20;
            
            // 绘制背景网格
            ctx.strokeStyle = '#e9ecef';
            ctx.lineWidth = 1;
            for (let i = 0; i <= 5; i++) {
                const y = 50 + (chartHeight / 5) * i;
                ctx.beginPath();
                ctx.moveTo(50, y);
                ctx.lineTo(50 + chartWidth, y);
                ctx.stroke();
            }
            
            // 绘制柱状图
            data.forEach((value, index) => {
                const barHeight = (value / maxValue) * chartHeight;
                const x = 50 + index * (barWidth + 20);
                const y = 50 + chartHeight - barHeight;
                
                // 渐变填充
                const gradient = ctx.createLinearGradient(0, y, 0, y + barHeight);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                
                ctx.fillStyle = gradient;
                ctx.fillRect(x, y, barWidth, barHeight);
                
                // 绘制数值
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(value, x + barWidth / 2, y - 5);
                
                // 绘制标签
                ctx.fillText(labels[index], x + barWidth / 2, 50 + chartHeight + 20);
            });
            
            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('拼团活动趋势 (本周)', 50, 30);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            
            // 启动倒计时
            setInterval(updateCountdowns, 1000);
            
            console.log('社交营销管理页面加载完成');
        });
    </script>
</body>
</html> 