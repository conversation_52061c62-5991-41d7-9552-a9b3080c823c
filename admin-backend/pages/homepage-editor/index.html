<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页可视化编辑器 - TeleShop</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
        }

        .editor-header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .editor-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .editor-subtitle {
            color: #64748b;
            font-size: 14px;
        }

        .editor-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .editor-body {
            display: flex;
            height: calc(100vh - 80px);
        }

        .editor-sidebar {
            width: 350px;
            background: white;
            border-right: 1px solid #e2e8f0;
            overflow-y: auto;
        }

        .sidebar-section {
            padding: 20px;
            border-bottom: 1px solid #f1f5f9;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .module-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .module-card {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .module-card:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }

        .module-card:active {
            transform: scale(0.95);
        }

        .module-card i {
            font-size: 24px;
            color: #6b7280;
            display: block;
            margin-bottom: 8px;
        }

        .module-card:hover i {
            color: #3b82f6;
        }

        .module-card span {
            font-size: 12px;
            color: #374151;
            font-weight: 500;
        }

        .editor-main {
            flex: 1;
            background: #f8fafc;
            overflow: auto;
            padding: 20px;
        }

        .preview-container {
            max-width: 375px;
            margin: 0 auto;
        }

        .phone-frame {
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            border: 2px solid #e5e7eb;
            overflow: hidden;
            height: 667px;
            transition: all 0.3s;
        }

        .phone-frame:hover {
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .phone-content {
            height: 100%;
            overflow-y: auto;
        }

        .page-section {
            border-bottom: 1px solid #f1f5f9;
            position: relative;
            cursor: pointer;
            transition: all 0.2s;
        }

        .page-section:hover {
            box-shadow: 0 0 0 2px #3b82f6;
            background: rgba(59, 130, 246, 0.05);
        }

        .page-section.selected {
            box-shadow: 0 0 0 2px #3b82f6;
            background: rgba(59, 130, 246, 0.05);
        }

        .component-toolbar {
            position: absolute;
            top: -40px;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 4px;
            display: none;
            gap: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .page-section:hover .component-toolbar,
        .page-section.selected .component-toolbar {
            display: flex;
        }

        .toolbar-btn {
            padding: 6px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s;
            color: #6b7280;
        }

        .toolbar-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .toolbar-btn.edit { color: #3b82f6; }
        .toolbar-btn.delete { color: #ef4444; }
        .toolbar-btn.copy { color: #10b981; }
        .toolbar-btn.move { color: #f59e0b; }

        .dragging {
            opacity: 0.5;
            transform: scale(0.95);
        }

        .drop-zone {
            height: 4px;
            background: #3b82f6;
            margin: 2px 0;
            border-radius: 2px;
            display: none;
        }

        .drop-zone.active {
            display: block;
        }

        .section-placeholder {
            padding: 40px 20px;
            text-align: center;
            color: #9ca3af;
            border: 2px dashed #d1d5db;
            margin: 10px;
            border-radius: 8px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            font-size: 13px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 12px;
        }

        .data-table th,
        .data-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            font-size: 12px;
            color: #374151;
        }

        .data-table td {
            font-size: 13px;
        }

        .action-btn {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            margin-right: 4px;
        }

        .switch {
            position: relative;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #3b82f6;
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .btn:active {
            transform: scale(0.98);
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6b7280;
            padding: 4px;
        }

        .modal-close:hover {
            color: #374151;
        }

        .form-row {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .image-upload {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .image-upload:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }

        .image-upload.has-image {
            border-style: solid;
            border-color: #10b981;
            background: #f0fdf4;
        }

        .image-preview {
            max-width: 100%;
            max-height: 200px;
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .component-editor {
            max-height: 400px;
            overflow-y: auto;
        }

        .data-item {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
            position: relative;
        }

        .data-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .data-item-title {
            font-weight: 500;
            color: #374151;
        }

        .data-item-actions {
            display: flex;
            gap: 4px;
        }

        .data-item-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .data-item-btn.edit {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .data-item-btn.delete {
            background: #fee2e2;
            color: #dc2626;
        }

        .data-item-btn.move {
            background: #fef3c7;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="editor-header">
        <div class="editor-title">
            <h1>首页可视化编辑器</h1>
            <div class="editor-subtitle">拖拽设计，实时预览，轻松打造专属首页</div>
        </div>
        <div class="editor-actions">
            <button class="btn btn-secondary" onclick="undo()" id="undoBtn" disabled>
                <i class="fas fa-undo"></i> 撤销
            </button>
            <button class="btn btn-secondary" onclick="redo()" id="redoBtn" disabled>
                <i class="fas fa-redo"></i> 重做
            </button>
            <div style="width: 1px; height: 20px; background: #e5e7eb; margin: 0 8px;"></div>
            <button class="btn btn-secondary" onclick="clearPage()">
                <i class="fas fa-trash"></i> 清空
            </button>
            <button class="btn btn-secondary" onclick="importConfig()">
                <i class="fas fa-upload"></i> 导入
            </button>
            <button class="btn btn-secondary" onclick="exportConfig()">
                <i class="fas fa-download"></i> 导出
            </button>
            <button class="btn btn-secondary" onclick="previewInNewWindow()">
                <i class="fas fa-external-link-alt"></i> 预览
            </button>
            <button class="btn btn-primary" onclick="saveHomepage()">
                <i class="fas fa-save"></i> 保存
            </button>
            <button class="btn btn-success" onclick="publishHomepage()">
                <i class="fas fa-rocket"></i> 发布上线
            </button>
        </div>
    </div>

    <div class="editor-body">
        <div class="editor-sidebar">
            <!-- 组件库 -->
            <div class="sidebar-section">
                <div class="section-title">
                    <i class="fas fa-cubes"></i>
                    组件库
                </div>
                <div class="module-grid">
                    <div class="module-card" onclick="addModule('banner')">
                        <i class="fas fa-images"></i>
                        <span>轮播Banner</span>
                    </div>
                    <div class="module-card" onclick="addModule('grid')">
                        <i class="fas fa-th-large"></i>
                        <span>分类导航</span>
                    </div>
                    <div class="module-card" onclick="addModule('groupbuy')">
                        <i class="fas fa-users"></i>
                        <span>拼团模块</span>
                    </div>
                    <div class="module-card" onclick="addModule('bargain')">
                        <i class="fas fa-cut"></i>
                        <span>砍价模块</span>
                    </div>
                    <div class="module-card" onclick="addModule('products')">
                        <i class="fas fa-shopping-bag"></i>
                        <span>商品列表</span>
                    </div>
                    <div class="module-card" onclick="addModule('notice')">
                        <i class="fas fa-bullhorn"></i>
                        <span>公告通知</span>
                    </div>
                    <div class="module-card" onclick="addModule('video')">
                        <i class="fas fa-play-circle"></i>
                        <span>视频展示</span>
                    </div>
                    <div class="module-card" onclick="addModule('coupon')">
                        <i class="fas fa-ticket-alt"></i>
                        <span>优惠券</span>
                    </div>
                    <div class="module-card" onclick="addModule('article')">
                        <i class="fas fa-newspaper"></i>
                        <span>文章资讯</span>
                    </div>
                    <div class="module-card" onclick="addModule('contact')">
                        <i class="fas fa-phone"></i>
                        <span>联系客服</span>
                    </div>
                </div>
            </div>

            <!-- 页面设置 -->
            <div class="sidebar-section">
                <div class="section-title">
                    <i class="fas fa-cog"></i>
                    页面设置
                </div>
                <div class="form-group">
                    <label>页面标题</label>
                    <input type="text" id="pageTitle" value="TeleShop - 首页" onchange="updateSetting('title', this.value)">
                </div>
                <div class="form-group">
                    <label>主题颜色</label>
                    <input type="color" id="themeColor" value="#3b82f6" onchange="updateSetting('color', this.value)">
                </div>
                <div class="form-group">
                    <label style="display: flex; align-items: center; justify-content: space-between;">
                        启用拼团功能
                        <label class="switch">
                            <input type="checkbox" id="enableGroupBuy" onchange="updateSetting('groupBuy', this.checked)" checked>
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>
                <div class="form-group">
                    <label style="display: flex; align-items: center; justify-content: space-between;">
                        启用砍价功能
                        <label class="switch">
                            <input type="checkbox" id="enableBargain" onchange="updateSetting('bargain', this.checked)" checked>
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>
            </div>

            <!-- 数据管理 -->
            <div class="sidebar-section">
                <div class="section-title">
                    <i class="fas fa-database"></i>
                    数据管理
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="font-size: 14px; margin-bottom: 8px; color: #374151;">Banner管理</h4>
                    <button class="btn btn-secondary" style="width: 100%; margin-bottom: 8px;" onclick="addBanner()">
                        <i class="fas fa-plus"></i> 添加Banner
                    </button>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>双十一活动</td>
                                <td>已上线</td>
                                <td>
                                    <button class="btn btn-secondary action-btn" onclick="editBanner(1)">编辑</button>
                                    <button class="btn btn-secondary action-btn" onclick="deleteBanner(1)">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4 style="font-size: 14px; margin-bottom: 8px; color: #374151;">拼团管理</h4>
                    <button class="btn btn-secondary" style="width: 100%; margin-bottom: 8px;" onclick="addGroupBuy()">
                        <i class="fas fa-plus"></i> 添加拼团
                    </button>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>商品</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>iPhone 15</td>
                                <td>进行中</td>
                                <td>
                                    <button class="btn btn-secondary action-btn" onclick="editGroupBuy(1)">编辑</button>
                                    <button class="btn btn-secondary action-btn" onclick="deleteGroupBuy(1)">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div>
                    <h4 style="font-size: 14px; margin-bottom: 8px; color: #374151;">砍价管理</h4>
                    <button class="btn btn-secondary" style="width: 100%; margin-bottom: 8px;" onclick="addBargain()">
                        <i class="fas fa-plus"></i> 添加砍价
                    </button>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>商品</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Apple Watch</td>
                                <td>进行中</td>
                                <td>
                                    <button class="btn btn-secondary action-btn" onclick="editBargain(1)">编辑</button>
                                    <button class="btn btn-secondary action-btn" onclick="deleteBargain(1)">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="editor-main">
            <div class="preview-container">
                <div class="phone-frame">
                    <div class="phone-content" id="previewContent">
                        <div class="section-placeholder">
                            <i class="fas fa-plus-circle" style="font-size: 48px; margin-bottom: 12px;"></i>
                            <p>从左侧拖拽组件到这里开始设计首页</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 组件编辑模态框 -->
    <div class="modal-overlay" id="componentModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">编辑组件</div>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="component-editor" id="componentEditor">
                <!-- 动态内容 -->
            </div>
            <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="saveComponentEdit()">保存</button>
            </div>
        </div>
    </div>

    <!-- 图片上传模态框 -->
    <div class="modal-overlay" id="imageModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">选择图片</div>
                <button class="modal-close" onclick="closeImageModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div>
                <div class="image-upload" onclick="triggerImageUpload()">
                    <i class="fas fa-cloud-upload-alt" style="font-size: 32px; color: #9ca3af; margin-bottom: 8px;"></i>
                    <p>点击上传图片或拖拽图片到此处</p>
                    <p style="font-size: 12px; color: #6b7280;">支持 JPG、PNG、GIF 格式，大小不超过 2MB</p>
                </div>
                <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
                <div id="imagePreview" style="margin-top: 16px; display: none;">
                    <img id="previewImg" class="image-preview" alt="预览">
                    <div style="text-align: center;">
                        <button class="btn btn-secondary" onclick="removeImage()">移除图片</button>
                    </div>
                </div>
            </div>
            <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                <button class="btn btn-secondary" onclick="closeImageModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmImageSelection()">确认选择</button>
            </div>
        </div>
    </div>

    <script>
        // 页面设置
        let pageSettings = {
            title: 'TeleShop - 首页',
            color: '#3b82f6',
            groupBuy: true,
            bargain: true
        };

        // 页面模块
        let pageModules = [];
        
        // 撤销重做历史
        let history = [];
        let historyIndex = -1;
        let selectedComponentId = null;
        let currentEditingComponent = null;
        let draggedComponent = null;

        // 添加历史记录
        function addToHistory() {
            const state = {
                modules: JSON.parse(JSON.stringify(pageModules)),
                settings: JSON.parse(JSON.stringify(pageSettings))
            };
            
            // 移除当前位置之后的历史
            history = history.slice(0, historyIndex + 1);
            history.push(state);
            historyIndex++;
            
            // 限制历史记录数量
            if (history.length > 50) {
                history.shift();
                historyIndex--;
            }
            
            updateUndoRedoButtons();
        }

        // 更新撤销重做按钮状态
        function updateUndoRedoButtons() {
            document.getElementById('undoBtn').disabled = historyIndex <= 0;
            document.getElementById('redoBtn').disabled = historyIndex >= history.length - 1;
        }

        // 撤销
        function undo() {
            if (historyIndex > 0) {
                historyIndex--;
                restoreState(history[historyIndex]);
                updateUndoRedoButtons();
                showToast('已撤销');
            }
        }

        // 重做
        function redo() {
            if (historyIndex < history.length - 1) {
                historyIndex++;
                restoreState(history[historyIndex]);
                updateUndoRedoButtons();
                showToast('已重做');
            }
        }

        // 恢复状态
        function restoreState(state) {
            pageModules = JSON.parse(JSON.stringify(state.modules));
            pageSettings = JSON.parse(JSON.stringify(state.settings));
            renderPage();
            updateFormFields();
        }

        // 添加模块
        function addModule(type) {
            addToHistory(); // 先保存当前状态
            
            const moduleId = Date.now();
            const moduleData = createDefaultModuleData(type);
            
            // 如果是第一个模块，清除占位符
            if (pageModules.length === 0) {
                document.getElementById('previewContent').innerHTML = '';
            }
            
            pageModules.push({ 
                type, 
                id: moduleId, 
                data: moduleData,
                visible: true 
            });
            
            renderPage();
            showToast(`${getModuleName(type)}已添加`);
        }

        // 创建默认模块数据
        function createDefaultModuleData(type) {
            switch(type) {
                case 'banner':
                    return {
                        items: [
                            { image: '', title: '轮播图1', link: '', description: '描述文字' },
                            { image: '', title: '轮播图2', link: '', description: '描述文字' }
                        ]
                    };
                case 'grid':
                    return {
                        items: [
                            { icon: 'fas fa-mobile-alt', title: '数码3C', link: '', color: '#3b82f6' },
                            { icon: 'fas fa-tshirt', title: '服装', link: '', color: '#ec4899' },
                            { icon: 'fas fa-heart', title: '美妆', link: '', color: '#a855f7' },
                            { icon: 'fas fa-home', title: '家居', link: '', color: '#22c55e' }
                        ]
                    };
                case 'groupbuy':
                    return {
                        title: '限时拼团',
                        subtitle: '火热',
                        items: [
                            { image: '', title: 'iPhone 15', price: '8999', originalPrice: '9999', groupSize: '2', remaining: '1' },
                            { image: '', title: 'AirPods', price: '1599', originalPrice: '1899', groupSize: '5', remaining: '2' }
                        ]
                    };
                case 'bargain':
                    return {
                        title: '疯狂砍价',
                        subtitle: '助力',
                        items: [
                            { image: '', title: 'Apple Watch', currentPrice: '999', originalPrice: '2999', progress: '67', label: '0元购', remaining: '3' },
                            { image: '', title: 'iPad Pro', currentPrice: '1', originalPrice: '8999', progress: '89', label: '1元购', remaining: '1' }
                        ]
                    };
                case 'products':
                    return {
                        title: '为你推荐',
                        items: [
                            { image: '', title: 'iPhone 15 Pro', price: '9999', originalPrice: '10999' },
                            { image: '', title: 'AirPods Pro', price: '1899', originalPrice: '2199' }
                        ]
                    };
                case 'notice':
                    return {
                        text: '双十一活动即将开始，敬请期待！',
                        link: '',
                        icon: 'fas fa-bullhorn'
                    };
                case 'video':
                    return {
                        title: '精彩视频',
                        videoUrl: '',
                        coverImage: '',
                        description: '观看精彩内容'
                    };
                case 'coupon':
                    return {
                        title: '优惠券中心',
                        items: [
                            { title: '满100减20', desc: '全场通用', color: '#ef4444', condition: '满100元可用' },
                            { title: '新用户专享', desc: '8折优惠', color: '#10b981', condition: '首次购买' }
                        ]
                    };
                case 'article':
                    return {
                        title: '热门资讯',
                        items: [
                            { title: '最新科技资讯', image: '', summary: '了解最新科技动态', link: '' },
                            { title: '购物指南', image: '', summary: '教你如何选购', link: '' }
                        ]
                    };
                case 'contact':
                    return {
                        title: '联系客服',
                        phone: '************',
                        wechat: 'teleshop_service',
                        qq: '123456789',
                        workTime: '9:00-21:00'
                    };
                default:
                    return {};
            }
        }

        // 渲染整个页面
        function renderPage() {
            const previewContent = document.getElementById('previewContent');
            previewContent.innerHTML = '';
            
            if (pageModules.length === 0) {
                previewContent.innerHTML = `
                    <div class="section-placeholder">
                        <i class="fas fa-plus-circle" style="font-size: 48px; margin-bottom: 12px;"></i>
                        <p>从左侧拖拽组件到这里开始设计首页</p>
                    </div>
                `;
                return;
            }
            
            pageModules.forEach((module, index) => {
                const moduleDiv = document.createElement('div');
                moduleDiv.className = 'page-section';
                moduleDiv.dataset.moduleId = module.id;
                moduleDiv.dataset.moduleIndex = index;
                
                if (!module.visible) {
                    moduleDiv.style.opacity = '0.5';
                }
                
                moduleDiv.innerHTML = `
                    <div class="component-toolbar">
                        <button class="toolbar-btn edit" onclick="editComponent(${module.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="toolbar-btn copy" onclick="copyComponent(${module.id})" title="复制">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="toolbar-btn move" onclick="moveComponentUp(${index})" title="上移" ${index === 0 ? 'disabled' : ''}>
                            <i class="fas fa-arrow-up"></i>
                        </button>
                        <button class="toolbar-btn move" onclick="moveComponentDown(${index})" title="下移" ${index === pageModules.length - 1 ? 'disabled' : ''}>
                            <i class="fas fa-arrow-down"></i>
                        </button>
                        <button class="toolbar-btn" onclick="toggleComponentVisibility(${module.id})" title="${module.visible ? '隐藏' : '显示'}">
                            <i class="fas fa-eye${module.visible ? '' : '-slash'}"></i>
                        </button>
                        <button class="toolbar-btn delete" onclick="deleteComponent(${module.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="drop-zone" data-index="${index}"></div>
                    ${generateModuleHTML(module.type, module.data)}
                `;
                
                // 添加点击选中功能
                moduleDiv.addEventListener('click', (e) => {
                    if (e.target.closest('.component-toolbar')) return;
                    selectComponent(module.id);
                });
                
                // 添加拖拽功能
                moduleDiv.draggable = true;
                moduleDiv.addEventListener('dragstart', (e) => handleDragStart(e, module.id));
                moduleDiv.addEventListener('dragover', handleDragOver);
                moduleDiv.addEventListener('drop', (e) => handleDrop(e, index));
                
                previewContent.appendChild(moduleDiv);
            });
        }

        // 组件操作函数
        function selectComponent(componentId) {
            // 移除之前的选中状态
            document.querySelectorAll('.page-section.selected').forEach(el => {
                el.classList.remove('selected');
            });
            
            // 添加新的选中状态
            const component = document.querySelector(`[data-module-id="${componentId}"]`);
            if (component) {
                component.classList.add('selected');
                selectedComponentId = componentId;
            }
        }

        function editComponent(componentId) {
            const module = pageModules.find(m => m.id === componentId);
            if (!module) return;
            
            currentEditingComponent = module;
            openComponentModal(module);
        }

        function copyComponent(componentId) {
            addToHistory();
            const module = pageModules.find(m => m.id === componentId);
            if (!module) return;
            
            const newModule = {
                ...JSON.parse(JSON.stringify(module)),
                id: Date.now()
            };
            
            pageModules.push(newModule);
            renderPage();
            showToast(`${getModuleName(module.type)}已复制`, 'success');
        }

        function deleteComponent(componentId) {
            if (confirm('确定要删除这个组件吗？')) {
                addToHistory();
                pageModules = pageModules.filter(m => m.id !== componentId);
                renderPage();
                showToast('组件已删除', 'success');
            }
        }

        function moveComponentUp(index) {
            if (index <= 0) return;
            addToHistory();
            [pageModules[index], pageModules[index - 1]] = [pageModules[index - 1], pageModules[index]];
            renderPage();
            showToast('组件已上移');
        }

        function moveComponentDown(index) {
            if (index >= pageModules.length - 1) return;
            addToHistory();
            [pageModules[index], pageModules[index + 1]] = [pageModules[index + 1], pageModules[index]];
            renderPage();
            showToast('组件已下移');
        }

        function toggleComponentVisibility(componentId) {
            addToHistory();
            const module = pageModules.find(m => m.id === componentId);
            if (module) {
                module.visible = !module.visible;
                renderPage();
                showToast(module.visible ? '组件已显示' : '组件已隐藏');
            }
        }

        // 拖拽功能
        function handleDragStart(e, componentId) {
            draggedComponent = componentId;
            e.target.classList.add('dragging');
        }

        function handleDragOver(e) {
            e.preventDefault();
            const dropZones = document.querySelectorAll('.drop-zone');
            dropZones.forEach(zone => zone.classList.remove('active'));
            
            const dropZone = e.target.closest('.page-section')?.querySelector('.drop-zone');
            if (dropZone) {
                dropZone.classList.add('active');
            }
        }

        function handleDrop(e, targetIndex) {
            e.preventDefault();
            if (!draggedComponent) return;
            
            addToHistory();
            const draggedIndex = pageModules.findIndex(m => m.id === draggedComponent);
            if (draggedIndex === -1) return;
            
            const draggedModule = pageModules.splice(draggedIndex, 1)[0];
            pageModules.splice(targetIndex, 0, draggedModule);
            
            renderPage();
            showToast('组件已移动');
            
            // 清理
            document.querySelectorAll('.dragging').forEach(el => el.classList.remove('dragging'));
            document.querySelectorAll('.drop-zone.active').forEach(el => el.classList.remove('active'));
            draggedComponent = null;
        }

        // 生成模块HTML
        function generateModuleHTML(type, data = {}) {
            switch(type) {
                case 'banner':
                    return `
                        <div style="height: 150px; background: linear-gradient(45deg, #3b82f6, #8b5cf6); position: relative; display: flex; align-items: center; justify-content: center;">
                            <div style="color: white; text-align: center;">
                                <div style="font-size: 18px; font-weight: bold;">轮播Banner</div>
                                <div style="font-size: 12px; opacity: 0.8;">促销活动展示</div>
                            </div>
                        </div>
                    `;
                case 'grid':
                    return `
                        <div style="padding: 16px; background: white;">
                            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px;">
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; background: #dbeafe; border-radius: 50%; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center; color: #3b82f6;">
                                        <i class="fas fa-mobile-alt"></i>
                                    </div>
                                    <div style="font-size: 12px;">数码3C</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; background: #fce7f3; border-radius: 50%; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center; color: #ec4899;">
                                        <i class="fas fa-tshirt"></i>
                                    </div>
                                    <div style="font-size: 12px;">服装</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; background: #f3e8ff; border-radius: 50%; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center; color: #a855f7;">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                    <div style="font-size: 12px;">美妆</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; background: #dcfce7; border-radius: 50%; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center; color: #22c55e;">
                                        <i class="fas fa-home"></i>
                                    </div>
                                    <div style="font-size: 12px;">家居</div>
                                </div>
                            </div>
                        </div>
                    `;
                case 'groupbuy':
                    return `
                        <div style="padding: 16px; background: white;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-users" style="color: #f97316;"></i>
                                    <span style="font-weight: 600; color: #1e293b;">限时拼团</span>
                                    <span style="background: #fee2e2; color: #dc2626; padding: 2px 8px; border-radius: 12px; font-size: 10px;">火热</span>
                                </div>
                                <span style="color: #f97316; font-size: 12px;">更多 ></span>
                            </div>
                            <div style="display: flex; gap: 12px; overflow-x: auto;">
                                <div style="flex-shrink: 0; width: 120px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div style="height: 80px; background: #f3f4f6; border-radius: 8px 8px 0 0; position: relative;">
                                        <div style="position: absolute; top: 4px; left: 4px; background: #dc2626; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px;">2人团</div>
                                    </div>
                                    <div style="padding: 8px;">
                                        <div style="font-size: 12px; font-weight: 500; margin-bottom: 4px;">iPhone 15</div>
                                        <div style="color: #dc2626; font-weight: bold; font-size: 14px;">¥8999</div>
                                        <div style="font-size: 10px; color: #6b7280;">还差1人</div>
                                    </div>
                                </div>
                                <div style="flex-shrink: 0; width: 120px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div style="height: 80px; background: #f3f4f6; border-radius: 8px 8px 0 0; position: relative;">
                                        <div style="position: absolute; top: 4px; left: 4px; background: #f97316; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px;">5人团</div>
                                    </div>
                                    <div style="padding: 8px;">
                                        <div style="font-size: 12px; font-weight: 500; margin-bottom: 4px;">AirPods</div>
                                        <div style="color: #dc2626; font-weight: bold; font-size: 14px;">¥1599</div>
                                        <div style="font-size: 10px; color: #6b7280;">还差2人</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                case 'bargain':
                    return `
                        <div style="padding: 16px; background: white;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-cut" style="color: #10b981;"></i>
                                    <span style="font-weight: 600; color: #1e293b;">疯狂砍价</span>
                                    <span style="background: #dcfce7; color: #16a34a; padding: 2px 8px; border-radius: 12px; font-size: 10px;">助力</span>
                                </div>
                                <span style="color: #10b981; font-size: 12px;">更多 ></span>
                            </div>
                            <div style="display: flex; gap: 12px; overflow-x: auto;">
                                <div style="flex-shrink: 0; width: 120px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div style="height: 80px; background: #f3f4f6; border-radius: 8px 8px 0 0; position: relative;">
                                        <div style="position: absolute; top: 4px; left: 4px; background: #10b981; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px;">0元购</div>
                                        <div style="position: absolute; bottom: 4px; left: 4px; right: 4px; background: rgba(0,0,0,0.5); color: white; padding: 2px; border-radius: 4px; font-size: 10px; text-align: center;">已砍67%</div>
                                    </div>
                                    <div style="padding: 8px;">
                                        <div style="font-size: 12px; font-weight: 500; margin-bottom: 4px;">Apple Watch</div>
                                        <div style="color: #10b981; font-weight: bold; font-size: 14px;">¥999</div>
                                        <div style="font-size: 10px; color: #6b7280;">再砍3刀</div>
                                    </div>
                                </div>
                                <div style="flex-shrink: 0; width: 120px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <div style="height: 80px; background: #f3f4f6; border-radius: 8px 8px 0 0; position: relative;">
                                        <div style="position: absolute; top: 4px; left: 4px; background: #3b82f6; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px;">1元购</div>
                                        <div style="position: absolute; bottom: 4px; left: 4px; right: 4px; background: rgba(0,0,0,0.5); color: white; padding: 2px; border-radius: 4px; font-size: 10px; text-align: center;">已砍89%</div>
                                    </div>
                                    <div style="padding: 8px;">
                                        <div style="font-size: 12px; font-weight: 500; margin-bottom: 4px;">iPad Pro</div>
                                        <div style="color: #10b981; font-weight: bold; font-size: 14px;">¥1</div>
                                        <div style="font-size: 10px; color: #6b7280;">最后1刀</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                case 'products':
                    return `
                        <div style="padding: 16px; background: white;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-weight: 600; color: #1e293b;">为你推荐</span>
                                <span style="color: #3b82f6; font-size: 12px;">换一批</span>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                                <div style="background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); padding: 8px;">
                                    <div style="height: 100px; background: #f3f4f6; border-radius: 6px; margin-bottom: 8px;"></div>
                                    <div style="font-size: 12px; font-weight: 500; margin-bottom: 4px;">iPhone 15 Pro</div>
                                    <div style="color: #dc2626; font-weight: bold; font-size: 14px;">¥9,999</div>
                                </div>
                                <div style="background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); padding: 8px;">
                                    <div style="height: 100px; background: #f3f4f6; border-radius: 6px; margin-bottom: 8px;"></div>
                                    <div style="font-size: 12px; font-weight: 500; margin-bottom: 4px;">AirPods Pro</div>
                                    <div style="color: #dc2626; font-weight: bold; font-size: 14px;">¥1,899</div>
                                </div>
                            </div>
                        </div>
                    `;
                case 'notice':
                    return `
                        <div style="padding: 12px 16px; background: #fefce8; border-left: 4px solid #eab308; margin: 8px 16px; border-radius: 4px;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-bullhorn" style="color: #ca8a04;"></i>
                                <div style="font-size: 12px; color: #713f12;">
                                    <span style="font-weight: 600;">系统公告：</span>
                                    双十一活动即将开始，敬请期待！
                                </div>
                            </div>
                        </div>
                    `;
                case 'video':
                    return `
                        <div style="padding: 16px; background: white;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-weight: 600; color: #1e293b;">精彩视频</span>
                            </div>
                            <div style="position: relative; background: #000; border-radius: 8px; overflow: hidden;">
                                <div style="height: 200px; background: linear-gradient(45deg, #374151, #1f2937); display: flex; align-items: center; justify-content: center;">
                                    <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.9); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                                        <i class="fas fa-play" style="color: #1f2937; margin-left: 4px; font-size: 20px;"></i>
                                    </div>
                                </div>
                                <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 16px 12px 12px;">
                                    <div style="font-size: 14px; font-weight: 500;">观看精彩内容</div>
                                    <div style="font-size: 12px; opacity: 0.8; margin-top: 4px;">点击播放视频</div>
                                </div>
                            </div>
                        </div>
                    `;
                case 'coupon':
                    return `
                        <div style="padding: 16px; background: white;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-weight: 600; color: #1e293b;">优惠券中心</span>
                                <span style="color: #3b82f6; font-size: 12px;">更多 ></span>
                            </div>
                            <div style="display: flex; gap: 12px; overflow-x: auto;">
                                <div style="flex-shrink: 0; width: 140px; height: 80px; background: linear-gradient(135deg, #ef4444, #dc2626); border-radius: 8px; color: white; padding: 12px; position: relative; overflow: hidden;">
                                    <div style="position: absolute; top: -10px; right: -10px; width: 30px; height: 30px; background: rgba(255,255,255,0.2); border-radius: 50%;"></div>
                                    <div style="font-size: 16px; font-weight: bold;">满100减20</div>
                                    <div style="font-size: 12px; opacity: 0.9;">全场通用</div>
                                    <div style="font-size: 10px; opacity: 0.8; margin-top: 4px;">满100元可用</div>
                                </div>
                                <div style="flex-shrink: 0; width: 140px; height: 80px; background: linear-gradient(135deg, #10b981, #059669); border-radius: 8px; color: white; padding: 12px; position: relative; overflow: hidden;">
                                    <div style="position: absolute; top: -10px; right: -10px; width: 30px; height: 30px; background: rgba(255,255,255,0.2); border-radius: 50%;"></div>
                                    <div style="font-size: 16px; font-weight: bold;">新用户专享</div>
                                    <div style="font-size: 12px; opacity: 0.9;">8折优惠</div>
                                    <div style="font-size: 10px; opacity: 0.8; margin-top: 4px;">首次购买</div>
                                </div>
                            </div>
                        </div>
                    `;
                case 'article':
                    return `
                        <div style="padding: 16px; background: white;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-weight: 600; color: #1e293b;">热门资讯</span>
                                <span style="color: #3b82f6; font-size: 12px;">更多 ></span>
                            </div>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; gap: 12px; align-items: center;">
                                    <div style="width: 80px; height: 60px; background: #f3f4f6; border-radius: 6px; flex-shrink: 0;"></div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 14px; font-weight: 500; color: #1e293b; margin-bottom: 4px;">最新科技资讯</div>
                                        <div style="font-size: 12px; color: #6b7280; line-height: 1.4;">了解最新科技动态，掌握行业趋势</div>
                                    </div>
                                </div>
                                <div style="display: flex; gap: 12px; align-items: center;">
                                    <div style="width: 80px; height: 60px; background: #f3f4f6; border-radius: 6px; flex-shrink: 0;"></div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 14px; font-weight: 500; color: #1e293b; margin-bottom: 4px;">购物指南</div>
                                        <div style="font-size: 12px; color: #6b7280; line-height: 1.4;">教你如何选购心仪商品</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                case 'contact':
                    return `
                        <div style="padding: 16px; background: white;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <span style="font-weight: 600; color: #1e293b;">联系客服</span>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                                <div style="display: flex; flex-direction: column; align-items: center; padding: 16px; background: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;">
                                    <i class="fas fa-phone" style="color: #3b82f6; font-size: 24px; margin-bottom: 8px;"></i>
                                    <div style="font-size: 12px; color: #6b7280;">客服热线</div>
                                    <div style="font-size: 14px; font-weight: 500; color: #1e293b;">************</div>
                                </div>
                                <div style="display: flex; flex-direction: column; align-items: center; padding: 16px; background: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;">
                                    <i class="fab fa-weixin" style="color: #10b981; font-size: 24px; margin-bottom: 8px;"></i>
                                    <div style="font-size: 12px; color: #6b7280;">微信客服</div>
                                    <div style="font-size: 14px; font-weight: 500; color: #1e293b;">teleshop_service</div>
                                </div>
                            </div>
                            <div style="text-align: center; font-size: 12px; color: #6b7280; padding: 8px; background: #f1f5f9; border-radius: 6px;">
                                <i class="fas fa-clock" style="margin-right: 4px;"></i>
                                服务时间：9:00-21:00
                            </div>
                        </div>
                    `;
                default:
                    return '<div>未知组件</div>';
            }
        }

        // 获取模块名称
        function getModuleName(type) {
            const names = {
                banner: '轮播Banner',
                grid: '分类导航',
                groupbuy: '拼团专区',
                bargain: '砍价活动',
                products: '商品展示',
                notice: '公告通知',
                video: '视频展示',
                coupon: '优惠券',
                article: '文章资讯',
                contact: '联系客服'
            };
            return names[type] || '未知组件';
        }

        // 更新设置
        function updateSetting(key, value) {
            pageSettings[key] = value;
            showToast(`${key}设置已更新`);
        }

        // 刷新预览
        function refreshPreview() {
            showToast('预览已刷新');
        }

        // 保存首页
        function saveHomepage() {
            const data = {
                settings: pageSettings,
                modules: pageModules,
                timestamp: new Date().toISOString()
            };
            localStorage.setItem('homepageData', JSON.stringify(data));
            showToast('首页设置已保存', 'success');
        }

        // 发布上线
        function publishHomepage() {
            saveHomepage();
            showToast('正在发布...', 'info');
            setTimeout(() => {
                showToast('首页已成功发布上线！', 'success');
                // 这里可以添加实际的发布逻辑，比如调用API
                console.log('首页发布数据:', {
                    settings: pageSettings,
                    modules: pageModules,
                    publishTime: new Date().toISOString()
                });
            }, 2000);
        }

        // 清空页面
        function clearPage() {
            if (confirm('确定要清空所有模块吗？')) {
                pageModules = [];
                document.getElementById('previewContent').innerHTML = `
                    <div class="section-placeholder">
                        <i class="fas fa-plus-circle" style="font-size: 48px; margin-bottom: 12px;"></i>
                        <p>从左侧拖拽组件到这里开始设计首页</p>
                    </div>
                `;
                showToast('页面已清空');
            }
        }

        // 预览在新窗口中打开
        function previewInNewWindow() {
            const previewWindow = window.open('', '_blank', 'width=375,height=667');
            const previewContent = document.getElementById('previewContent').innerHTML;
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>首页预览 - ${pageSettings.title}</title>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
                    <style>
                        body { margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
                        .section-placeholder { display: none; }
                    </style>
                </head>
                <body>
                    ${previewContent}
                </body>
                </html>
            `);
            previewWindow.document.close();
            showToast('预览窗口已打开', 'success');
        }

        // 导入配置
        function importConfig() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = JSON.parse(e.target.result);
                            pageSettings = data.settings || pageSettings;
                            pageModules = data.modules || [];
                            
                            // 重新渲染页面
                            const previewContent = document.getElementById('previewContent');
                            previewContent.innerHTML = '';
                            if (pageModules.length === 0) {
                                previewContent.innerHTML = `
                                    <div class="section-placeholder">
                                        <i class="fas fa-plus-circle" style="font-size: 48px; margin-bottom: 12px;"></i>
                                        <p>从左侧拖拽组件到这里开始设计首页</p>
                                    </div>
                                `;
                            } else {
                                                    pageModules.forEach((module) => {
                        const moduleHTML = generateModuleHTML(module.type);
                        const moduleDiv = document.createElement('div');
                        moduleDiv.className = 'page-section';
                        moduleDiv.dataset.moduleId = module.id;
                        moduleDiv.innerHTML = `
                            <div class="component-toolbar">
                                <button class="toolbar-btn edit" onclick="editComponent(${module.id})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="toolbar-btn copy" onclick="copyComponent(${module.id})" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="toolbar-btn up" onclick="moveComponent(${module.id}, 'up')" title="上移">
                                    <i class="fas fa-chevron-up"></i>
                                </button>
                                <button class="toolbar-btn down" onclick="moveComponent(${module.id}, 'down')" title="下移">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <button class="toolbar-btn toggle" onclick="toggleComponent(${module.id})" title="${module.visible !== false ? '隐藏' : '显示'}">
                                    <i class="fas fa-${module.visible !== false ? 'eye-slash' : 'eye'}"></i>
                                </button>
                                <button class="toolbar-btn delete" onclick="deleteComponent(${module.id})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            ${moduleHTML}
                        `;
                        
                        // 添加点击选中事件
                        moduleDiv.addEventListener('click', (e) => {
                            e.stopPropagation();
                            selectComponent(module.id);
                        });
                        
                        // 鼠标悬停显示工具栏
                        moduleDiv.addEventListener('mouseenter', () => {
                            moduleDiv.querySelector('.component-toolbar').style.display = 'flex';
                        });
                        
                        moduleDiv.addEventListener('mouseleave', () => {
                            if (!moduleDiv.classList.contains('selected')) {
                                moduleDiv.querySelector('.component-toolbar').style.display = 'none';
                            }
                        });
                        
                        previewContent.appendChild(moduleDiv);
                    });
                            }
                            
                            // 更新表单
                            document.getElementById('pageTitle').value = pageSettings.title;
                            document.getElementById('themeColor').value = pageSettings.color;
                            document.getElementById('enableGroupBuy').checked = pageSettings.groupBuy;
                            document.getElementById('enableBargain').checked = pageSettings.bargain;
                            
                            showToast('配置导入成功！', 'success');
                        } catch (error) {
                            showToast('配置文件格式错误！', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 导出配置
        function exportConfig() {
            const data = {
                settings: pageSettings,
                modules: pageModules,
                timestamp: new Date().toISOString(),
                version: '1.0'
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `homepage-config-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            showToast('配置文件已导出！', 'success');
        }

        // 数据管理函数
        function addBanner() { 
            showToast('添加Banner功能', 'info');
            // 这里可以打开添加Banner的弹窗
        }
        function editBanner(id) { 
            showToast(`编辑Banner ${id}`, 'info'); 
            // 这里可以打开编辑Banner的弹窗
        }
        function deleteBanner(id) { 
            if (confirm('确定要删除这个Banner吗？')) {
                showToast(`Banner ${id} 已删除`, 'success'); 
            }
        }
        function addGroupBuy() { 
            showToast('添加拼团功能', 'info'); 
            // 这里可以打开添加拼团的弹窗
        }
        function editGroupBuy(id) { 
            showToast(`编辑拼团 ${id}`, 'info'); 
            // 这里可以打开编辑拼团的弹窗
        }
        function deleteGroupBuy(id) { 
            if (confirm('确定要删除这个拼团活动吗？')) {
                showToast(`拼团活动 ${id} 已删除`, 'success'); 
            }
        }
        function addBargain() { 
            showToast('添加砍价功能', 'info'); 
            // 这里可以打开添加砍价的弹窗
        }
        function editBargain(id) { 
            showToast(`编辑砍价 ${id}`, 'info'); 
            // 这里可以打开编辑砍价的弹窗
        }
        function deleteBargain(id) { 
            if (confirm('确定要删除这个砍价活动吗？')) {
                showToast(`砍价活动 ${id} 已删除`, 'success'); 
            }
        }

        // 显示提示
        function showToast(message, type = 'info') {
            const colors = {
                info: '#3b82f6',
                success: '#10b981',
                error: '#ef4444'
            };
            
            const icons = {
                info: 'fas fa-info-circle',
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle'
            };
            
            const toast = document.createElement('div');
            toast.innerHTML = `<i class="${icons[type]}"></i> ${message}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                z-index: 10000;
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 8px;
                animation: slideIn 0.3s ease;
                max-width: 300px;
            `;
            
            document.body.appendChild(toast);
            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        saveHomepage();
                        break;
                    case 'p':
                        e.preventDefault();
                        publishHomepage();
                        break;
                    case 'r':
                        e.preventDefault();
                        refreshPreview();
                        break;
                }
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 加载保存的配置
            const savedData = localStorage.getItem('homepageData');
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    pageSettings = data.settings || pageSettings;
                    pageModules = data.modules || [];
                    
                    // 更新表单
                    document.getElementById('pageTitle').value = pageSettings.title;
                    document.getElementById('themeColor').value = pageSettings.color;
                    document.getElementById('enableGroupBuy').checked = pageSettings.groupBuy;
                    document.getElementById('enableBargain').checked = pageSettings.bargain;
                    
                    // 渲染页面
                    if (pageModules.length > 0) {
                        const previewContent = document.getElementById('previewContent');
                        previewContent.innerHTML = '';
                        pageModules.forEach((module) => {
                            const moduleHTML = generateModuleHTML(module.type);
                            const moduleDiv = document.createElement('div');
                            moduleDiv.className = 'page-section';
                            moduleDiv.innerHTML = moduleHTML;
                            previewContent.appendChild(moduleDiv);
                        });
                    }
                } catch (error) {
                    console.error('加载配置出错:', error);
                    showToast('加载配置出错', 'error');
                }
            }
            
            showToast('首页编辑器已就绪！', 'success');
        });

        // 模态框相关功能
        function openComponentModal(module) {
            const modal = document.getElementById('componentModal');
            const modalTitle = document.getElementById('modalTitle');
            const componentEditor = document.getElementById('componentEditor');
            
            modalTitle.textContent = `编辑${getModuleName(module.type)}`;
            componentEditor.innerHTML = generateComponentEditor(module);
            modal.classList.add('show');
            
            // 阻止背景滚动
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            const modal = document.getElementById('componentModal');
            modal.classList.remove('show');
            document.body.style.overflow = '';
            currentEditingComponent = null;
        }

        function saveComponentEdit() {
            if (!currentEditingComponent) return;
            
            addToHistory();
            const formData = collectFormData();
            currentEditingComponent.data = formData;
            renderPage();
            closeModal();
            showToast('组件已更新', 'success');
        }

        // 生成组件编辑器
        function generateComponentEditor(module) {
            switch(module.type) {
                case 'banner':
                    return generateBannerEditor(module.data);
                case 'notice':
                    return generateNoticeEditor(module.data);
                case 'video':
                    return generateVideoEditor(module.data);
                case 'coupon':
                    return generateCouponEditor(module.data);
                case 'article':
                    return generateArticleEditor(module.data);
                case 'contact':
                    return generateContactEditor(module.data);
                default:
                    return '<div>暂不支持编辑此组件类型，请等待后续版本更新</div>';
            }
        }

        // Banner编辑器
        function generateBannerEditor(data) {
            return `
                <div class="form-group">
                    <label>轮播图片管理</label>
                    <div id="bannerItems">
                        ${(data.items || []).map((item, index) => `
                            <div class="data-item" data-index="${index}">
                                <div class="data-item-header">
                                    <div class="data-item-title">轮播图 ${index + 1}</div>
                                    <div class="data-item-actions">
                                        <button class="data-item-btn delete" onclick="deleteBannerItem(${index})">删除</button>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>标题</label>
                                        <input type="text" value="${item.title || ''}" data-field="title" data-index="${index}">
                                    </div>
                                    <div class="form-group">
                                        <label>链接</label>
                                        <input type="text" value="${item.link || ''}" data-field="link" data-index="${index}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>图片URL</label>
                                    <input type="text" value="${item.image || ''}" data-field="image" data-index="${index}" placeholder="输入图片URL">
                                </div>
                                <div class="form-group">
                                    <label>描述</label>
                                    <textarea data-field="description" data-index="${index}">${item.description || ''}</textarea>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addBannerItem()">
                        <i class="fas fa-plus"></i> 添加轮播图
                    </button>
                </div>
            `;
        }

        // 公告编辑器
        function generateNoticeEditor(data) {
            return `
                <div class="form-group">
                    <label>公告内容</label>
                    <textarea id="noticeText" rows="3">${data.text || ''}</textarea>
                </div>
                <div class="form-group">
                    <label>跳转链接</label>
                    <input type="text" id="noticeLink" value="${data.link || ''}" placeholder="可选">
                </div>
                <div class="form-group">
                    <label>图标</label>
                    <input type="text" id="noticeIcon" value="${data.icon || 'fas fa-bullhorn'}" placeholder="FontAwesome图标类名">
                </div>
            `;
        }

        // 视频编辑器
        function generateVideoEditor(data) {
            return `
                <div class="form-group">
                    <label>视频标题</label>
                    <input type="text" id="videoTitle" value="${data.title || '精彩视频'}" placeholder="视频标题">
                </div>
                <div class="form-group">
                    <label>视频链接</label>
                    <input type="text" id="videoUrl" value="${data.videoUrl || ''}" placeholder="视频播放地址">
                </div>
                <div class="form-group">
                    <label>封面图片</label>
                    <input type="text" id="videoCover" value="${data.coverImage || ''}" placeholder="封面图片URL">
                </div>
                <div class="form-group">
                    <label>视频描述</label>
                    <textarea id="videoDesc" rows="2">${data.description || '观看精彩内容'}</textarea>
                </div>
            `;
        }

        // 优惠券编辑器
        function generateCouponEditor(data) {
            return `
                <div class="form-group">
                    <label>标题</label>
                    <input type="text" id="couponTitle" value="${data.title || '优惠券中心'}" placeholder="优惠券模块标题">
                </div>
                <div class="form-group">
                    <label>优惠券列表</label>
                    <div id="couponItems">
                        ${(data.items || []).map((item, index) => `
                            <div class="data-item" data-index="${index}">
                                <div class="data-item-header">
                                    <div class="data-item-title">${item.title || '优惠券'}</div>
                                    <div class="data-item-actions">
                                        <button class="data-item-btn delete" onclick="deleteCouponItem(${index})">删除</button>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>优惠券名称</label>
                                        <input type="text" value="${item.title || ''}" data-field="title" data-index="${index}">
                                    </div>
                                    <div class="form-group">
                                        <label>描述</label>
                                        <input type="text" value="${item.desc || ''}" data-field="desc" data-index="${index}">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>使用条件</label>
                                        <input type="text" value="${item.condition || ''}" data-field="condition" data-index="${index}">
                                    </div>
                                    <div class="form-group">
                                        <label>颜色</label>
                                        <input type="color" value="${item.color || '#ef4444'}" data-field="color" data-index="${index}">
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addCouponItem()">
                        <i class="fas fa-plus"></i> 添加优惠券
                    </button>
                </div>
            `;
        }

        // 文章编辑器
        function generateArticleEditor(data) {
            return `
                <div class="form-group">
                    <label>标题</label>
                    <input type="text" id="articleTitle" value="${data.title || '热门资讯'}" placeholder="文章模块标题">
                </div>
                <div class="form-group">
                    <label>文章列表</label>
                    <div id="articleItems">
                        ${(data.items || []).map((item, index) => `
                            <div class="data-item" data-index="${index}">
                                <div class="data-item-header">
                                    <div class="data-item-title">${item.title || '文章'}</div>
                                    <div class="data-item-actions">
                                        <button class="data-item-btn delete" onclick="deleteArticleItem(${index})">删除</button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>文章标题</label>
                                    <input type="text" value="${item.title || ''}" data-field="title" data-index="${index}">
                                </div>
                                <div class="form-group">
                                    <label>文章摘要</label>
                                    <textarea data-field="summary" data-index="${index}">${item.summary || ''}</textarea>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>图片URL</label>
                                        <input type="text" value="${item.image || ''}" data-field="image" data-index="${index}">
                                    </div>
                                    <div class="form-group">
                                        <label>文章链接</label>
                                        <input type="text" value="${item.link || ''}" data-field="link" data-index="${index}">
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addArticleItem()">
                        <i class="fas fa-plus"></i> 添加文章
                    </button>
                </div>
            `;
        }

        // 联系客服编辑器
        function generateContactEditor(data) {
            return `
                <div class="form-group">
                    <label>标题</label>
                    <input type="text" id="contactTitle" value="${data.title || '联系客服'}" placeholder="联系客服标题">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>客服电话</label>
                        <input type="text" id="contactPhone" value="${data.phone || '************'}" placeholder="客服热线">
                    </div>
                    <div class="form-group">
                        <label>微信号</label>
                        <input type="text" id="contactWechat" value="${data.wechat || 'teleshop_service'}" placeholder="微信客服号">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>QQ号</label>
                        <input type="text" id="contactQQ" value="${data.qq || '123456789'}" placeholder="QQ客服号">
                    </div>
                    <div class="form-group">
                        <label>工作时间</label>
                        <input type="text" id="contactWorkTime" value="${data.workTime || '9:00-21:00'}" placeholder="服务时间">
                    </div>
                </div>
            `;
        }

        // 数据收集函数
        function collectFormData() {
            if (!currentEditingComponent) return {};
            
            const type = currentEditingComponent.type;
            
            switch(type) {
                case 'banner':
                    return collectBannerData();
                case 'notice':
                    return {
                        text: document.getElementById('noticeText').value,
                        link: document.getElementById('noticeLink').value,
                        icon: document.getElementById('noticeIcon').value
                    };
                case 'video':
                    return {
                        title: document.getElementById('videoTitle').value,
                        videoUrl: document.getElementById('videoUrl').value,
                        coverImage: document.getElementById('videoCover').value,
                        description: document.getElementById('videoDesc').value
                    };
                case 'coupon':
                    return collectCouponData();
                case 'article':
                    return collectArticleData();
                case 'contact':
                    return {
                        title: document.getElementById('contactTitle').value,
                        phone: document.getElementById('contactPhone').value,
                        wechat: document.getElementById('contactWechat').value,
                        qq: document.getElementById('contactQQ').value,
                        workTime: document.getElementById('contactWorkTime').value
                    };
                default:
                    return {};
            }
        }

        function collectBannerData() {
            const items = [];
            document.querySelectorAll('#bannerItems .data-item').forEach((item) => {
                items.push({
                    title: item.querySelector('[data-field="title"]').value,
                    link: item.querySelector('[data-field="link"]').value,
                    description: item.querySelector('[data-field="description"]').value,
                    image: item.querySelector('[data-field="image"]').value
                });
            });
            return { items };
        }

        function collectCouponData() {
            const items = [];
            document.querySelectorAll('#couponItems .data-item').forEach((item) => {
                items.push({
                    title: item.querySelector('[data-field="title"]').value,
                    desc: item.querySelector('[data-field="desc"]').value,
                    condition: item.querySelector('[data-field="condition"]').value,
                    color: item.querySelector('[data-field="color"]').value
                });
            });
            return {
                title: document.getElementById('couponTitle').value,
                items
            };
        }

        function collectArticleData() {
            const items = [];
            document.querySelectorAll('#articleItems .data-item').forEach((item) => {
                items.push({
                    title: item.querySelector('[data-field="title"]').value,
                    summary: item.querySelector('[data-field="summary"]').value,
                    image: item.querySelector('[data-field="image"]').value,
                    link: item.querySelector('[data-field="link"]').value
                });
            });
            return {
                title: document.getElementById('articleTitle').value,
                items
            };
        }

        // Banner项目操作函数
        function addBannerItem() {
            const container = document.getElementById('bannerItems');
            const index = container.children.length;
            const itemHTML = `
                <div class="data-item" data-index="${index}">
                    <div class="data-item-header">
                        <div class="data-item-title">轮播图 ${index + 1}</div>
                        <div class="data-item-actions">
                            <button class="data-item-btn delete" onclick="deleteBannerItem(${index})">删除</button>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>标题</label>
                            <input type="text" value="" data-field="title" data-index="${index}">
                        </div>
                        <div class="form-group">
                            <label>链接</label>
                            <input type="text" value="" data-field="link" data-index="${index}">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>图片URL</label>
                        <input type="text" value="" data-field="image" data-index="${index}" placeholder="输入图片URL">
                    </div>
                    <div class="form-group">
                        <label>描述</label>
                        <textarea data-field="description" data-index="${index}"></textarea>
                    </div>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', itemHTML);
        }

        function deleteBannerItem(index) {
            if (confirm('确定要删除这个轮播图吗？')) {
                const container = document.getElementById('bannerItems');
                const item = container.querySelector(`[data-index="${index}"]`);
                if (item) item.remove();
                
                // 重新排序索引
                container.querySelectorAll('.data-item').forEach((item, newIndex) => {
                    item.dataset.index = newIndex;
                    item.querySelector('.data-item-title').textContent = `轮播图 ${newIndex + 1}`;
                    
                    // 更新所有data-index属性
                    item.querySelectorAll('[data-index]').forEach(el => {
                        el.dataset.index = newIndex;
                    });
                });
            }
        }

        // 优惠券项目操作函数
        function addCouponItem() {
            const container = document.getElementById('couponItems');
            const index = container.children.length;
            const itemHTML = `
                <div class="data-item" data-index="${index}">
                    <div class="data-item-header">
                        <div class="data-item-title">优惠券 ${index + 1}</div>
                        <div class="data-item-actions">
                            <button class="data-item-btn delete" onclick="deleteCouponItem(${index})">删除</button>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>优惠券名称</label>
                            <input type="text" value="" data-field="title" data-index="${index}">
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <input type="text" value="" data-field="desc" data-index="${index}">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>使用条件</label>
                            <input type="text" value="" data-field="condition" data-index="${index}">
                        </div>
                        <div class="form-group">
                            <label>颜色</label>
                            <input type="color" value="#ef4444" data-field="color" data-index="${index}">
                        </div>
                    </div>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', itemHTML);
        }

        function deleteCouponItem(index) {
            if (confirm('确定要删除这个优惠券吗？')) {
                const container = document.getElementById('couponItems');
                const item = container.querySelector(`[data-index="${index}"]`);
                if (item) item.remove();
                
                // 重新排序索引
                container.querySelectorAll('.data-item').forEach((item, newIndex) => {
                    item.dataset.index = newIndex;
                    item.querySelector('.data-item-title').textContent = `优惠券 ${newIndex + 1}`;
                    
                    // 更新所有data-index属性
                    item.querySelectorAll('[data-index]').forEach(el => {
                        el.dataset.index = newIndex;
                    });
                });
            }
        }

        // 文章项目操作函数
        function addArticleItem() {
            const container = document.getElementById('articleItems');
            const index = container.children.length;
            const itemHTML = `
                <div class="data-item" data-index="${index}">
                    <div class="data-item-header">
                        <div class="data-item-title">文章 ${index + 1}</div>
                        <div class="data-item-actions">
                            <button class="data-item-btn delete" onclick="deleteArticleItem(${index})">删除</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>文章标题</label>
                        <input type="text" value="" data-field="title" data-index="${index}">
                    </div>
                    <div class="form-group">
                        <label>文章摘要</label>
                        <textarea data-field="summary" data-index="${index}"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>图片URL</label>
                            <input type="text" value="" data-field="image" data-index="${index}">
                        </div>
                        <div class="form-group">
                            <label>文章链接</label>
                            <input type="text" value="" data-field="link" data-index="${index}">
                        </div>
                    </div>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', itemHTML);
        }

        function deleteArticleItem(index) {
            if (confirm('确定要删除这个文章吗？')) {
                const container = document.getElementById('articleItems');
                const item = container.querySelector(`[data-index="${index}"]`);
                if (item) item.remove();
                
                // 重新排序索引
                container.querySelectorAll('.data-item').forEach((item, newIndex) => {
                    item.dataset.index = newIndex;
                    item.querySelector('.data-item-title').textContent = `文章 ${newIndex + 1}`;
                    
                    // 更新所有data-index属性
                    item.querySelectorAll('[data-index]').forEach(el => {
                        el.dataset.index = newIndex;
                    });
                });
            }
        }

        // 组件操作函数
        function selectComponent(componentId) {
            // 移除之前的选中状态
            document.querySelectorAll('.page-section.selected').forEach(el => {
                el.classList.remove('selected');
                const toolbar = el.querySelector('.component-toolbar');
                if (toolbar) toolbar.style.display = 'none';
            });
            
            // 添加新的选中状态
            const component = document.querySelector(`[data-module-id="${componentId}"]`);
            if (component) {
                component.classList.add('selected');
                const toolbar = component.querySelector('.component-toolbar');
                if (toolbar) toolbar.style.display = 'flex';
                selectedComponentId = componentId;
            }
        }

        function editComponent(componentId) {
            const module = pageModules.find(m => m.id === componentId);
            if (!module) return;
            
            currentEditingComponent = module;
            openComponentModal(module);
        }

        function copyComponent(componentId) {
            const module = pageModules.find(m => m.id === componentId);
            if (!module) return;
            
            addToHistory();
            const newModule = {
                ...module,
                id: Date.now(),
                data: JSON.parse(JSON.stringify(module.data || {}))
            };
            
            // 在当前组件后面插入
            const currentIndex = pageModules.findIndex(m => m.id === componentId);
            pageModules.splice(currentIndex + 1, 0, newModule);
            
            renderPage();
            showToast('组件已复制', 'success');
        }

        function moveComponent(componentId, direction) {
            const currentIndex = pageModules.findIndex(m => m.id === componentId);
            if (currentIndex === -1) return;
            
            let newIndex;
            if (direction === 'up') {
                if (currentIndex === 0) return;
                newIndex = currentIndex - 1;
            } else {
                if (currentIndex === pageModules.length - 1) return;
                newIndex = currentIndex + 1;
            }
            
            addToHistory();
            const [module] = pageModules.splice(currentIndex, 1);
            pageModules.splice(newIndex, 0, module);
            
            renderPage();
            showToast(`组件已${direction === 'up' ? '上移' : '下移'}`, 'success');
        }

        function toggleComponent(componentId) {
            const module = pageModules.find(m => m.id === componentId);
            if (!module) return;
            
            addToHistory();
            module.visible = module.visible !== false ? false : true;
            
            renderPage();
            showToast(`组件已${module.visible ? '显示' : '隐藏'}`, 'success');
        }

        function deleteComponent(componentId) {
            if (!confirm('确定要删除这个组件吗？')) return;
            
            addToHistory();
            const index = pageModules.findIndex(m => m.id === componentId);
            if (index !== -1) {
                pageModules.splice(index, 1);
                renderPage();
                showToast('组件已删除', 'success');
            }
        }

        // 更新表单字段
        function updateFormFields() {
            document.getElementById('pageTitle').value = pageSettings.title;
            document.getElementById('themeColor').value = pageSettings.color;
            document.getElementById('enableGroupBuy').checked = pageSettings.groupBuy;
            document.getElementById('enableBargain').checked = pageSettings.bargain;
        }

        console.log('🎨 TeleShop 首页可视化编辑器初始化完成');
    </script>
</body>
</html> 