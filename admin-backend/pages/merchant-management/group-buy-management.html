<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拼团管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .group-buy-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
            margin-top: 4px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .stat-card.active::before { background: #10b981; }
        .stat-card.pending::before { background: #f59e0b; }
        .stat-card.completed::before { background: #3b82f6; }
        .stat-card.failed::before { background: #ef4444; }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .stat-card.active .stat-icon { background: #10b981; }
        .stat-card.pending .stat-icon { background: #f59e0b; }
        .stat-card.completed .stat-icon { background: #3b82f6; }
        .stat-card.failed .stat-icon { background: #ef4444; }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .stat-change {
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-change.positive {
            color: #10b981;
        }

        .stat-change.negative {
            color: #ef4444;
        }

        .main-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .content-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .content-controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-box {
            position: relative;
        }

        .search-input {
            padding: 8px 12px 8px 36px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            width: 250px;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }

        .group-buy-list {
            padding: 24px;
        }

        .group-buy-item {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.2s;
        }

        .group-buy-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .item-header {
            padding: 16px 20px;
            background: #f8fafc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
        }

        .product-details h3 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 4px 0;
        }

        .product-details p {
            font-size: 14px;
            color: #64748b;
            margin: 0;
        }

        .item-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.active {
            background: #dcfce7;
            color: #166534;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.completed {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge.failed {
            background: #fee2e2;
            color: #991b1b;
        }

        .item-content {
            padding: 20px;
        }

        .progress-section {
            margin-bottom: 20px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-info {
            font-size: 14px;
            color: #374151;
        }

        .progress-percentage {
            font-size: 14px;
            font-weight: 600;
            color: #3b82f6;
        }

        .progress-bar {
            height: 8px;
            background: #f1f5f9;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 4px;
            transition: width 0.3s;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .detail-label {
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #1e293b;
            font-weight: 600;
        }

        .item-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
            padding-top: 16px;
            border-top: 1px solid #f1f5f9;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .participants-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f1f5f9;
        }

        .participants-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .participants-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .participants-count {
            font-size: 12px;
            color: #64748b;
        }

        .participants-list {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .participant-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid #e2e8f0;
            object-fit: cover;
        }

        .more-participants {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f1f5f9;
            border: 2px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #64748b;
            font-weight: 600;
        }

        /* 增强的控制区域样式 */
        .control-row {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* 步骤指示器样式 */
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 0 20px;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 60%;
            width: 80%;
            height: 2px;
            background: #e2e8f0;
            z-index: 1;
        }

        .step.active:not(:last-child)::after {
            background: #3b82f6;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            color: #64748b;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
            transition: all 0.3s;
        }

        .step.active .step-number {
            background: #3b82f6;
            color: white;
        }

        .step.completed .step-number {
            background: #10b981;
            color: white;
        }

        .step-title {
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            text-align: center;
            transition: color 0.3s;
        }

        .step.active .step-title {
            color: #3b82f6;
        }

        .step.completed .step-title {
            color: #10b981;
        }

        /* 步骤内容样式 */
        .step-content {
            padding: 20px 0;
        }

        .step-content h4 {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f1f5f9;
        }

        /* 商品搜索样式 */
        .product-search-container {
            position: relative;
        }

        .product-search-box {
            display: flex;
            gap: 8px;
        }

        .product-search-box .form-input {
            flex: 1;
        }

        .product-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 4px;
        }

        .product-result-item {
            display: flex;
            align-items: center;
            padding: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .product-result-item:hover {
            background: #f8fafc;
        }

        .product-result-item img {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            margin-right: 12px;
            object-fit: cover;
        }

        .product-result-info {
            flex: 1;
        }

        .product-result-name {
            font-size: 14px;
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .product-result-sku {
            font-size: 12px;
            color: #64748b;
        }

        .product-result-price {
            font-size: 14px;
            font-weight: 600;
            color: #3b82f6;
        }

        /* 选中商品样式 */
        .selected-product {
            margin-top: 16px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            border: 2px solid #3b82f6;
        }

        .product-preview {
            display: flex;
            align-items: center;
            gap: 16px;
            position: relative;
        }

        .product-thumb {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover;
        }

        .product-info {
            flex: 1;
        }

        .product-info h5 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .product-sku {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 4px;
        }

        .product-price {
            font-size: 14px;
            color: #3b82f6;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .product-stock {
            font-size: 12px;
            color: #10b981;
        }

        .btn-remove {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #ef4444;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        /* 优惠信息样式 */
        .discount-info {
            font-size: 12px;
            margin-top: 4px;
            padding: 4px 8px;
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 4px;
            color: #1e40af;
        }

        /* 复选框组样式 */
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin: 0;
        }

        /* 佣金设置样式 */
        .commission-settings {
            margin-top: 16px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
        }

        /* 汇总信息样式 */
        .summary-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .summary-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .summary-card h5 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e2e8f0;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
        }

        .summary-item:last-child {
            margin-bottom: 0;
        }

        .summary-item label {
            font-weight: 500;
            color: #64748b;
        }

        .summary-item span {
            font-weight: 600;
            color: #1e293b;
        }

        /* 批量操作栏样式 */
        .batch-operations {
            background: #f8fafc;
            padding: 12px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .batch-info {
            font-size: 14px;
            color: #64748b;
        }

        .batch-actions {
            display: flex;
            gap: 8px;
        }

        .batch-actions .btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 表单帮助文本 */
        .form-help {
            font-size: 12px;
            color: #64748b;
            margin-top: 4px;
            display: block;
        }

        .control-row:last-child {
            margin-bottom: 0;
        }

        .advanced-search-btn, .sort-btn {
            white-space: nowrap;
        }

        .batch-selection-info {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 14px;
            color: #64748b;
        }

        .btn-link {
            background: none;
            border: none;
            color: #3b82f6;
            cursor: pointer;
            font-size: 14px;
            text-decoration: underline;
        }

        .btn-link:hover {
            color: #2563eb;
        }

        /* 项目复选框样式 */
        .item-checkbox {
            display: flex;
            align-items: center;
            margin-right: 12px;
        }

        .item-select {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        /* 快速操作按钮样式 */
        .item-actions-quick {
            display: flex;
            gap: 8px;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            background: #f8fafc;
            color: #64748b;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .btn-icon:hover {
            background: #e2e8f0;
            color: #374151;
        }

        /* 高级搜索模态框样式 */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .price-range, .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .price-range input, .date-range input {
            flex: 1;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid #e2e8f0;
        }

        /* 详情模态框样式 */
        .modal-large {
            max-width: 900px;
            width: 95%;
            max-height: 90vh;
        }

        .detail-overview {
            margin-bottom: 24px;
        }

        .overview-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
        }

        .overview-header {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
        }

        .detail-product-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover;
        }

        .overview-info {
            flex: 1;
        }

        .overview-info h4 {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 8px 0;
        }

        .overview-info p {
            color: #64748b;
            margin: 0 0 8px 0;
        }

        .status-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
        }

        /* 标签页样式 */
        .detail-tabs {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }

        .tab-headers {
            display: flex;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab-header {
            flex: 1;
            padding: 12px 16px;
            border: none;
            background: none;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s;
        }

        .tab-header.active {
            background: white;
            color: #3b82f6;
            border-bottom: 2px solid #3b82f6;
        }

        .tab-header:hover {
            background: #e2e8f0;
        }

        .tab-content {
            padding: 20px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        /* 参与者管理样式 */
        .participants-management {
            min-height: 300px;
        }

        .participants-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .participants-header h5 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .participants-actions {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 24px;
            padding: 16px 24px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .pagination-info {
            font-size: 14px;
            color: #64748b;
        }

        .pagination {
            display: flex;
            gap: 4px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .page-btn:hover:not(:disabled) {
            background: #f8fafc;
            border-color: #cbd5e1;
        }

        .page-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .page-btn:disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }

        /* Toast消息样式 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .toast {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 10px;
            transform: translateX(100%);
            transition: transform 0.3s;
            min-width: 300px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast-success { border-left: 4px solid #10b981; }
        .toast-error { border-left: 4px solid #ef4444; }
        .toast-warning { border-left: 4px solid #f59e0b; }
        .toast-info { border-left: 4px solid #3b82f6; }

        .toast-content {
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* 上下文菜单样式 */
        .context-menu {
            position: absolute;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 1px solid #e2e8f0;
            padding: 8px 0;
            min-width: 180px;
            z-index: 1000;
        }

        .action-menu-item {
            padding: 8px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .action-menu-item:hover {
            background: #f8fafc;
        }

        .action-menu-item.danger {
            color: #ef4444;
        }

        .action-menu-item.danger:hover {
            background: #fef2f2;
        }

        /* 确认对话框样式 */
        .confirm-modal .modal-content {
            max-width: 400px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 16px 24px;
            border-top: 1px solid #e2e8f0;
        }

        /* 分析预览样式 */
        .analytics-preview {
            padding: 16px 0;
        }

        .analytics-chart {
            margin-bottom: 24px;
        }

        .analytics-chart h4 {
            margin: 0 0 16px 0;
            color: #1e293b;
        }

        .chart-placeholder {
            height: 200px;
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            background: #f8fafc;
        }

        .stat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
        }

        .stat-card {
            text-align: center;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
            margin-top: 4px;
        }

        /* 推广链接样式 */
        .promotion-link {
            padding: 16px 0;
        }

        .link-display {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .link-display input {
            flex: 1;
        }

        .qr-code {
            text-align: center;
        }

        .qr-code h4 {
            margin: 0 0 12px 0;
            color: #1e293b;
        }

        .qr-placeholder {
            width: 150px;
            height: 150px;
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            color: #64748b;
            background: #f8fafc;
        }

        /* 参与者项目样式 */
        .participant-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin-bottom: 8px;
        }

        .participant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .participant-info {
            flex: 1;
        }

        .participant-name {
            font-weight: 500;
            color: #1e293b;
        }

        .participant-time {
            font-size: 12px;
            color: #64748b;
        }

        .participant-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .participant-status.paid {
            background: #dcfce7;
            color: #16a34a;
        }

        .participant-status.pending {
            background: #fef3c7;
            color: #d97706;
        }

        /* 时间线样式 */
        .timeline-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .timeline-item:last-child {
            border-bottom: none;
        }

        .timeline-time {
            font-size: 12px;
            color: #64748b;
            min-width: 120px;
        }

        .timeline-event {
            color: #1e293b;
        }

        /* 排序菜单样式 */
        .sort-menu {
            padding: 8px 0;
        }

        .sort-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .sort-option:hover {
            background: #f8fafc;
        }

        .sort-option.active {
            background: #eff6ff;
            color: #3b82f6;
        }

        /* 移动端响应式样式 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .page-header {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }

            .header-actions {
                flex-wrap: wrap;
                gap: 8px;
            }

            .content-controls {
                flex-direction: column;
                gap: 12px;
            }

            .control-row {
                flex-wrap: wrap;
                gap: 8px;
            }

            .search-box {
                width: 100%;
                max-width: none;
            }

            .filter-select {
                flex: 1;
                min-width: 120px;
            }

            .group-buy-item {
                padding: 16px;
            }

            .item-header {
                flex-direction: column;
                gap: 12px;
                align-items: flex-start;
            }

            .item-info {
                width: 100%;
            }

            .product-image {
                width: 50px;
                height: 50px;
            }

            .item-actions-quick {
                width: 100%;
                justify-content: flex-end;
            }

            .details-grid {
                grid-template-columns: 1fr !important;
                gap: 8px;
            }

            .detail-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .participants-list {
                justify-content: flex-start;
            }

            .item-actions {
                flex-direction: column;
                gap: 8px;
            }

            .modal-content {
                margin: 20px;
                max-width: calc(100vw - 40px);
                max-height: calc(100vh - 40px);
                overflow-y: auto;
            }

            .modal-body {
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .tab-headers {
                flex-wrap: wrap;
                gap: 8px;
            }

            .tab-header {
                padding: 8px 12px;
                font-size: 14px;
            }

            .batch-controls {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }

            .batch-actions {
                justify-content: center;
                flex-wrap: wrap;
            }

            .toast {
                margin: 0 10px;
                min-width: calc(100vw - 40px);
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            .container {
                padding: 12px;
            }

            .page-title {
                font-size: 24px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .btn {
                padding: 8px 12px;
                font-size: 12px;
            }

            .product-details h3 {
                font-size: 14px;
            }

            .product-details p {
                font-size: 12px;
            }
        }

        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: #0f172a;
                color: #e2e8f0;
            }

            .container {
                background: #1e293b;
            }

            .group-buy-item {
                background: #334155;
                border-color: #475569;
            }

            .modal-content {
                background: #1e293b;
                color: #e2e8f0;
            }

            .form-input,
            .filter-select {
                background: #334155;
                border-color: #475569;
                color: #e2e8f0;
            }

            .toast {
                background: #334155;
                color: #e2e8f0;
            }
        }

        /* 加载状态样式 */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 无障碍支持 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            .btn,
            .status-badge,
            .progress-fill {
                border: 2px solid currentColor;
            }
        }

        /* 减少动画模式支持 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: #64748b;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        @media (max-width: 768px) {
            .group-buy-page {
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .details-grid {
                grid-template-columns: 1fr;
            }

            .item-actions {
                flex-direction: column;
            }

            .content-controls {
                flex-direction: column;
                gap: 8px;
                align-items: stretch;
            }

            .search-input {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="group-buy-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title">拼团管理</h1>
                    <p class="page-subtitle">管理拼团活动，监控拼团进度和数据</p>
                </div>
                
                        <div class="header-actions">
            <button class="btn btn-secondary" data-action="refresh">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button class="btn btn-secondary" data-action="batch-export">
                <i class="fas fa-download"></i> 导出数据
            </button>
            <button class="btn btn-danger" data-action="batch-delete" disabled>
                <i class="fas fa-trash"></i> 批量删除
            </button>
            <button class="btn btn-primary" data-action="create">
                <i class="fas fa-plus"></i> 创建拼团
            </button>
        </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card active">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value">24</div>
                <div class="stat-label">进行中拼团</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +12% 较昨日
                </div>
            </div>

            <div class="stat-card pending">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value">8</div>
                <div class="stat-label">待开团</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +5% 较昨日
                </div>
            </div>

            <div class="stat-card completed">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-value">156</div>
                <div class="stat-label">已完成拼团</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +8% 较昨日
                </div>
            </div>

            <div class="stat-card failed">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
                <div class="stat-value">12</div>
                <div class="stat-label">拼团失败</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i>
                    -3% 较昨日
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <div class="content-header">
                <h2 class="content-title">拼团活动列表</h2>
                
                <div class="content-controls">
                    <div class="control-row">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="搜索拼团活动...">
                        </div>
                        
                        <select class="filter-select">
                            <option value="all">所有状态</option>
                            <option value="active">进行中</option>
                            <option value="pending">待开团</option>
                            <option value="completed">已完成</option>
                            <option value="failed">已失败</option>
                        </select>
                        
                        <select class="filter-select">
                            <option value="">所有商户</option>
                            <option value="merchant1">星河数码</option>
                            <option value="merchant2">优品生活</option>
                            <option value="merchant3">时尚潮牌</option>
                        </select>
                        
                        <button class="btn btn-secondary advanced-search-btn" data-action="advanced-search">
                            <i class="fas fa-filter"></i> 高级筛选
                        </button>
                        
                        <button class="btn btn-secondary sort-btn" data-action="sort">
                            <i class="fas fa-sort"></i> 排序
                        </button>
                    </div>
                    
                    <div class="control-row batch-controls" style="display: none;">
                        <div class="batch-selection-info">
                            <span class="selected-count">已选择 0 项</span>
                            <button class="btn-link select-all">全选</button>
                            <button class="btn-link clear-selection">清空</button>
                        </div>
                        
                        <div class="batch-actions">
                            <button class="btn btn-success" data-action="batch-start">
                                <i class="fas fa-play"></i> 批量启动
                            </button>
                            <button class="btn btn-warning" data-action="batch-pause">
                                <i class="fas fa-pause"></i> 批量暂停
                            </button>
                            <button class="btn btn-danger" data-action="batch-delete">
                                <i class="fas fa-trash"></i> 批量删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量操作条 -->
            <div class="bulk-actions">
                <div class="bulk-info">
                    <span class="selected-count">0</span> 个项目已选择
                </div>
                <div class="bulk-actions-row">
                    <button class="btn btn-success" data-action="batch-start">
                        <i class="fas fa-play"></i> 批量启动
                    </button>
                    <button class="btn btn-warning" data-action="batch-pause">
                        <i class="fas fa-pause"></i> 批量暂停
                    </button>
                    <button class="btn btn-info" data-action="batch-end">
                        <i class="fas fa-stop"></i> 批量结束
                    </button>
                    <button class="btn btn-danger" data-action="batch-delete">
                        <i class="fas fa-trash"></i> 批量删除
                    </button>
                    <button class="btn btn-secondary" onclick="GroupBuyManager.clearSelection()">
                        <i class="fas fa-times"></i> 取消选择
                    </button>
                </div>
            </div>
                </div>
            </div>

            <div class="group-buy-list">
                <!-- 拼团活动项目 1 -->
                <div class="group-buy-item" data-id="GB001234">
                    <div class="item-header">
                        <div class="item-checkbox">
                            <input type="checkbox" class="item-select" data-id="GB001234">
                        </div>
                        <div class="item-info">
                            <img src="https://via.placeholder.com/60x60" alt="商品图片" class="product-image">
                            <div class="product-details">
                                <h3>iPhone 15 Pro Max 256GB</h3>
                                <p>星河数码专营店 • 活动ID: GB001234</p>
                            </div>
                        </div>
                        
                        <div class="item-status">
                            <span class="status-badge active">进行中</span>
                            <div class="item-actions-quick">
                                <button class="btn-icon" title="查看详情" data-action="view-detail" data-id="GB001234">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-icon" title="查看参与者" data-action="view-participants" data-id="GB001234">
                                    <i class="fas fa-users"></i>
                                </button>
                                <button class="btn-icon" title="更多操作" data-action="more-actions" data-id="GB001234">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="item-content">
                        <div class="progress-section">
                            <div class="progress-header">
                                <span class="progress-info">拼团进度: 8/10人</span>
                                <span class="progress-percentage">80%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 80%"></div>
                            </div>
                        </div>
                        
                        <div class="details-grid">
                            <div class="detail-item">
                                <span class="detail-label">拼团价格</span>
                                <span class="detail-value">¥8,999</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">原价</span>
                                <span class="detail-value">¥9,999</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">开始时间</span>
                                <span class="detail-value">2024-12-20 10:00</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">结束时间</span>
                                <span class="detail-value">2024-12-22 23:59</span>
                            </div>
                        </div>
                        
                        <div class="participants-section">
                            <div class="participants-header">
                                <span class="participants-title">参团用户</span>
                                <span class="participants-count">8人已参团</span>
                            </div>
                            <div class="participants-list">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <div class="more-participants">+3</div>
                            </div>
                        </div>
                        
                        <div class="item-actions">
                            <button class="btn btn-secondary" data-action="view-detail" data-id="GB001234">
                                <i class="fas fa-eye"></i> 查看详情
                            </button>
                            <button class="btn btn-primary" data-action="edit" data-id="GB001234">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-success" data-action="promote" data-id="GB001234">
                                <i class="fas fa-share"></i> 推广
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 拼团活动项目 2 -->
                <div class="group-buy-item" data-id="GB001235">
                    <div class="item-header">
                        <div class="item-checkbox">
                            <input type="checkbox" class="item-select" data-id="GB001235">
                        </div>
                        <div class="item-info">
                            <img src="https://via.placeholder.com/60x60" alt="商品图片" class="product-image">
                            <div class="product-details">
                                <h3>小米14 Ultra 16GB+1TB</h3>
                                <p>优品生活馆 • 活动ID: GB001235</p>
                            </div>
                        </div>
                        
                        <div class="item-status">
                            <span class="status-badge completed">已完成</span>
                            <div class="item-actions-quick">
                                <button class="btn-icon" title="查看报告" data-action="view-report" data-id="GB001235">
                                    <i class="fas fa-chart-bar"></i>
                                </button>
                                <button class="btn-icon" title="查看参与者" data-action="view-participants" data-id="GB001235">
                                    <i class="fas fa-users"></i>
                                </button>
                                <button class="btn-icon" title="更多操作" data-action="more-actions" data-id="GB001235">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="item-content">
                        <div class="progress-section">
                            <div class="progress-header">
                                <span class="progress-info">拼团进度: 5/5人</span>
                                <span class="progress-percentage">100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%"></div>
                            </div>
                        </div>
                        
                        <div class="details-grid">
                            <div class="detail-item">
                                <span class="detail-label">拼团价格</span>
                                <span class="detail-value">¥5,999</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">原价</span>
                                <span class="detail-value">¥6,999</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">完成时间</span>
                                <span class="detail-value">2024-12-19 15:30</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">销售额</span>
                                <span class="detail-value">¥29,995</span>
                            </div>
                        </div>
                        
                        <div class="participants-section">
                            <div class="participants-header">
                                <span class="participants-title">参团用户</span>
                                <span class="participants-count">5人已参团</span>
                            </div>
                            <div class="participants-list">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                            </div>
                        </div>
                        
                        <div class="item-actions">
                            <button class="btn btn-secondary" data-action="view-report" data-id="GB001235">
                                <i class="fas fa-chart-bar"></i> 查看报告
                            </button>
                            <button class="btn btn-primary" data-action="copy" data-id="GB001235">
                                <i class="fas fa-copy"></i> 复制活动
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 拼团活动项目 3 -->
                <div class="group-buy-item" data-id="GB001236">
                    <div class="item-header">
                        <div class="item-checkbox">
                            <input type="checkbox" class="item-select" data-id="GB001236">
                        </div>
                        <div class="item-info">
                            <img src="https://via.placeholder.com/60x60" alt="商品图片" class="product-image">
                            <div class="product-details">
                                <h3>MacBook Pro 14英寸 M3芯片</h3>
                                <p>时尚潮牌屋 • 活动ID: GB001236</p>
                            </div>
                        </div>
                        
                        <div class="item-status">
                            <span class="status-badge pending">待开团</span>
                            <div class="item-actions-quick">
                                <button class="btn-icon" title="启动拼团" data-action="start" data-id="GB001236">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="btn-icon" title="编辑活动" data-action="edit" data-id="GB001236">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon" title="更多操作" data-action="more-actions" data-id="GB001236">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="item-content">
                        <div class="progress-section">
                            <div class="progress-header">
                                <span class="progress-info">拼团进度: 0/8人</span>
                                <span class="progress-percentage">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div class="details-grid">
                            <div class="detail-item">
                                <span class="detail-label">拼团价格</span>
                                <span class="detail-value">¥14,999</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">原价</span>
                                <span class="detail-value">¥16,999</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">开始时间</span>
                                <span class="detail-value">2024-12-21 09:00</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">结束时间</span>
                                <span class="detail-value">2024-12-23 23:59</span>
                            </div>
                        </div>
                        
                        <div class="item-actions">
                            <button class="btn btn-secondary" data-action="view-detail" data-id="GB001236">
                                <i class="fas fa-eye"></i> 查看详情
                            </button>
                            <button class="btn btn-primary" data-action="edit" data-id="GB001236">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-success" data-action="start" data-id="GB001236">
                                <i class="fas fa-play"></i> 启动
                            </button>
                            <button class="btn btn-danger" data-action="delete" data-id="GB001236">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建拼团模态框 -->
    <div class="modal-overlay" id="createModal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">创建拼团活动</h3>
                <button class="modal-close" onclick="GroupBuyManager.hideCreateModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <!-- 步骤指示器 -->
                <div class="step-indicator">
                    <div class="step active" data-step="1">
                        <div class="step-number">1</div>
                        <div class="step-title">基本信息</div>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-number">2</div>
                        <div class="step-title">商品设置</div>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-number">3</div>
                        <div class="step-title">营销配置</div>
                    </div>
                    <div class="step" data-step="4">
                        <div class="step-number">4</div>
                        <div class="step-title">确认创建</div>
                    </div>
                </div>

                <form id="createGroupBuyForm">
                    <!-- 第一步：基本信息 -->
                    <div class="step-content" id="step1">
                        <h4>基本信息设置</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">活动名称 *</label>
                                <input type="text" id="activityName" class="form-input" placeholder="请输入活动名称" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">所属商户 *</label>
                                <select id="merchant" class="form-select" required>
                                    <option value="">请选择商户</option>
                                    <option value="merchant1">苹果专营店</option>
                                    <option value="merchant2">小米官方旗舰店</option>
                                    <option value="merchant3">华为官方商城</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">商品分类 *</label>
                                <select id="category" class="form-select" required>
                                    <option value="">请选择分类</option>
                                    <option value="electronics">数码电子</option>
                                    <option value="clothing">服装鞋帽</option>
                                    <option value="home">家居用品</option>
                                    <option value="food">食品饮料</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">活动优先级</label>
                                <select id="priority" class="form-select">
                                    <option value="normal">普通</option>
                                    <option value="high">高</option>
                                    <option value="urgent">紧急</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">活动描述</label>
                            <textarea id="description" class="form-textarea" rows="3" placeholder="请输入活动描述..."></textarea>
                        </div>
                    </div>

                    <!-- 第二步：商品设置 -->
                    <div class="step-content" id="step2" style="display: none;">
                        <h4>商品信息设置</h4>
                        <div class="form-group">
                            <label class="form-label">选择商品 *</label>
                            <div class="product-search-container">
                                <div class="product-search-box">
                                    <input type="text" id="productSearch" class="form-input" placeholder="搜索商品名称或SKU">
                                    <button type="button" class="btn btn-primary" onclick="GroupBuyManager.searchProducts()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div class="product-results" id="productResults" style="display: none;"></div>
                            </div>
                            <div class="selected-product" id="selectedProduct" style="display: none;">
                                <div class="product-preview">
                                    <img src="" alt="商品图片" class="product-thumb">
                                    <div class="product-info">
                                        <h5 class="product-name"></h5>
                                        <div class="product-sku"></div>
                                        <div class="product-price">原价: ¥<span class="original-price"></span></div>
                                        <div class="product-stock">库存: <span class="stock-count"></span></div>
                                    </div>
                                    <button type="button" class="btn-remove" onclick="GroupBuyManager.removeSelectedProduct()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">商品原价 *</label>
                                <input type="number" id="originalPrice" class="form-input" placeholder="0.00" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">拼团价格 *</label>
                                <input type="number" id="groupPrice" class="form-input" placeholder="0.00" step="0.01" required>
                                <div class="discount-info" id="discountInfo"></div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">拼团人数 *</label>
                                <input type="number" id="groupSize" class="form-input" placeholder="2" min="2" max="20" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">活动库存 *</label>
                                <input type="number" id="totalStock" class="form-input" placeholder="100" min="1" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">用户限购</label>
                                <input type="number" id="limitPerUser" class="form-input" placeholder="不限制" min="1">
                                <small class="form-help">留空表示不限制单个用户购买数量</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">支付超时(分钟)</label>
                                <input type="number" id="paymentTimeout" class="form-input" placeholder="30" min="5" max="1440" value="30">
                            </div>
                        </div>
                    </div>

                    <!-- 第三步：营销配置 -->
                    <div class="step-content" id="step3" style="display: none;">
                        <h4>营销设置</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">活动开始时间 *</label>
                                <input type="datetime-local" id="startTime" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">活动结束时间 *</label>
                                <input type="datetime-local" id="endTime" class="form-input" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">推广设置</label>
                            <div class="checkbox-group">
                                <label class="checkbox-item">
                                    <input type="checkbox" id="enableShare" checked>
                                    <span>允许用户分享</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" id="enableCoupon">
                                    <span>支持优惠券</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" id="enableCommission">
                                    <span>启用佣金分销</span>
                                </label>
                            </div>
                        </div>
                        <div class="form-row commission-settings" style="display: none;">
                            <div class="form-group">
                                <label class="form-label">佣金比例(%)</label>
                                <input type="number" id="commissionRate" class="form-input" placeholder="5" min="0" max="50" step="0.1">
                            </div>
                            <div class="form-group">
                                <label class="form-label">佣金类型</label>
                                <select id="commissionType" class="form-select">
                                    <option value="percentage">按比例</option>
                                    <option value="fixed">固定金额</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">自动处理设置</label>
                            <div class="checkbox-group">
                                <label class="checkbox-item">
                                    <input type="checkbox" id="autoStart" checked>
                                    <span>到达开始时间自动启动</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" id="autoEnd" checked>
                                    <span>到达结束时间自动结束</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" id="autoRefund">
                                    <span>失败团自动退款</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 第四步：确认创建 -->
                    <div class="step-content" id="step4" style="display: none;">
                        <h4>确认信息</h4>
                        <div class="summary-section">
                            <div class="summary-card">
                                <h5>活动概览</h5>
                                <div class="summary-item">
                                    <label>活动名称：</label>
                                    <span id="summaryActivityName">-</span>
                                </div>
                                <div class="summary-item">
                                    <label>所属商户：</label>
                                    <span id="summaryMerchant">-</span>
                                </div>
                                <div class="summary-item">
                                    <label>商品分类：</label>
                                    <span id="summaryCategory">-</span>
                                </div>
                            </div>
                            <div class="summary-card">
                                <h5>商品信息</h5>
                                <div class="summary-item">
                                    <label>商品名称：</label>
                                    <span id="summaryProduct">-</span>
                                </div>
                                <div class="summary-item">
                                    <label>价格设置：</label>
                                    <span id="summaryPrice">-</span>
                                </div>
                                <div class="summary-item">
                                    <label>拼团设置：</label>
                                    <span id="summaryGroup">-</span>
                                </div>
                            </div>
                            <div class="summary-card">
                                <h5>时间设置</h5>
                                <div class="summary-item">
                                    <label>活动时间：</label>
                                    <span id="summaryTime">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="GroupBuyManager.hideCreateModal()">取消</button>
                <button type="button" class="btn btn-secondary" id="prevBtn" onclick="GroupBuyManager.prevStep()" style="display: none;">上一步</button>
                <button type="button" class="btn btn-primary" id="nextBtn" onclick="GroupBuyManager.nextStep()">下一步</button>
                <button type="button" class="btn btn-success" id="submitBtn" onclick="GroupBuyManager.submitActivity()" style="display: none;">
                    <i class="fas fa-check"></i> 创建活动
                </button>
            </div>
        </div>
    </div>

    <!-- 高级搜索模态框 -->
    <div id="advancedSearchModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">高级筛选</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="advancedSearchForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">价格区间</label>
                            <div class="price-range">
                                <input type="number" class="form-input" placeholder="最低价" name="minPrice">
                                <span>-</span>
                                <input type="number" class="form-input" placeholder="最高价" name="maxPrice">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">拼团人数</label>
                            <select class="form-select" name="groupSize">
                                <option value="">不限</option>
                                <option value="2-5">2-5人</option>
                                <option value="6-10">6-10人</option>
                                <option value="11-20">11-20人</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">创建时间</label>
                            <div class="date-range">
                                <input type="date" class="form-input" name="startDate">
                                <span>-</span>
                                <input type="date" class="form-input" name="endDate">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">进度区间</label>
                            <select class="form-select" name="progress">
                                <option value="">不限</option>
                                <option value="0-25">0-25%</option>
                                <option value="26-50">26-50%</option>
                                <option value="51-75">51-75%</option>
                                <option value="76-100">76-100%</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" data-action="reset">重置</button>
                        <button type="submit" class="btn btn-primary">应用筛选</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 拼团详情模态框 -->
    <div id="detailModal" class="modal-overlay">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3 class="modal-title">拼团详情</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="detail-content">
                    <div class="detail-overview">
                        <div class="overview-card">
                            <div class="overview-header">
                                <img src="" alt="商品图片" class="detail-product-image">
                                <div class="overview-info">
                                    <h4 class="product-title"></h4>
                                    <p class="merchant-name"></p>
                                    <div class="status-info">
                                        <span class="status-badge"></span>
                                        <span class="activity-id"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="overview-stats">
                                <div class="stat-item">
                                    <span class="stat-label">拼团价格</span>
                                    <span class="stat-value group-price"></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">原价</span>
                                    <span class="stat-value original-price"></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">优惠金额</span>
                                    <span class="stat-value discount-amount"></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">参团人数</span>
                                    <span class="stat-value participant-count"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-tabs">
                        <div class="tab-headers">
                            <button class="tab-header active" data-tab="participants">参与者</button>
                            <button class="tab-header" data-tab="timeline">活动时间线</button>
                            <button class="tab-header" data-tab="analytics">数据分析</button>
                            <button class="tab-header" data-tab="settings">设置</button>
                        </div>
                        
                        <div class="tab-content">
                            <div class="tab-pane active" id="participants-tab">
                                <div class="participants-management">
                                    <div class="participants-header">
                                        <h5>参与者列表</h5>
                                        <div class="participants-actions">
                                            <button class="btn btn-secondary btn-sm">导出列表</button>
                                            <button class="btn btn-primary btn-sm">发送通知</button>
                                        </div>
                                    </div>
                                    <div class="participants-list-detail">
                                        <!-- 参与者列表将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                            
                            <div class="tab-pane" id="timeline-tab">
                                <div class="timeline">
                                    <!-- 时间线内容将在这里动态生成 -->
                                </div>
                            </div>
                            
                            <div class="tab-pane" id="analytics-tab">
                                <div class="analytics-content">
                                    <div class="analytics-charts">
                                        <!-- 图表内容将在这里生成 -->
                                    </div>
                                </div>
                            </div>
                            
                            <div class="tab-pane" id="settings-tab">
                                <div class="settings-content">
                                    <!-- 设置内容将在这里生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container">
        <div class="pagination-info">
            显示第 <span class="page-start">1</span> - <span class="page-end">10</span> 项，共 <span class="total-items">45</span> 项
        </div>
        <div class="pagination">
            <button class="page-btn" data-page="prev" disabled>
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="page-btn active" data-page="1">1</button>
            <button class="page-btn" data-page="2">2</button>
            <button class="page-btn" data-page="3">3</button>
            <button class="page-btn" data-page="4">4</button>
            <button class="page-btn" data-page="5">5</button>
            <button class="page-btn" data-page="next">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <script>
        // 全局状态管理
        const GroupBuyManager = {
            selectedItems: new Set(),
            currentFilter: 'all',
            searchTerm: '',
            currentStep: 1,
            maxSteps: 4,
            formData: {},
            selectedProduct: null,
            
            // 初始化
            init() {
                this.bindEvents();
                this.updateStats();
                this.initFormValidation();
                this.bindBatchControls();
                console.log('GroupBuyManager 初始化完成');
            },

            // 绑定批量控制事件
            bindBatchControls() {
                // 全选和清空选择按钮
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('select-all')) {
                        e.preventDefault();
                        this.selectAll();
                    }
                    if (e.target.classList.contains('clear-selection')) {
                        e.preventDefault();
                        this.clearSelection();
                    }
                });
            },
            
            // 绑定事件
            bindEvents() {
                // 创建按钮
                document.querySelectorAll('[data-action="create"]').forEach(btn => {
                    btn.addEventListener('click', () => this.showCreateModal());
                });
                
                // 批量操作按钮
                document.querySelectorAll('[data-action="batch-delete"]').forEach(btn => {
                    btn.addEventListener('click', () => this.batchDelete());
                });
                
                document.querySelectorAll('[data-action="batch-export"]').forEach(btn => {
                    btn.addEventListener('click', () => this.exportData());
                });
                
                document.querySelectorAll('[data-action="refresh"]').forEach(btn => {
                    btn.addEventListener('click', () => this.refreshData());
                });
                
                // 项目操作按钮
                this.bindItemActions();
                
                // 搜索和筛选
                this.bindSearchAndFilter();
                
                // 表单提交
                this.bindFormSubmit();
                
                // 全选功能
                this.bindSelectAll();
                
                // 选择管理
                this.bindItemSelection();
            },
            
            // 绑定项目操作
            bindItemActions() {
                document.addEventListener('click', (e) => {
                    const btn = e.target.closest('[data-action]');
                    if (!btn) return;
                    
                    console.log('🖱️ 按钮点击事件:', {
                        button: btn,
                        action: btn.dataset.action,
                        id: btn.dataset.id,
                        element: btn.outerHTML
                    });
                    
                    const action = btn.dataset.action;
                    const itemId = btn.dataset.id || btn.closest('.group-buy-item')?.dataset.id;
                    
                    console.log(`🔧 执行动作: ${action}, 项目ID: ${itemId}`);
                    
                    // 阻止默认行为和事件冒泡
                    e.preventDefault();
                    e.stopPropagation();
                    
                    switch(action) {
                        case 'view':
                        case 'view-detail':
                            console.log('👁️ 调用查看详情功能');
                            this.viewDetails(itemId);
                            break;
                        case 'edit':
                            this.editItem(itemId);
                            break;
                        case 'start':
                            this.startActivity(itemId);
                            break;
                        case 'pause':
                            this.pauseActivity(itemId);
                            break;
                        case 'end':
                            this.endActivity(itemId);
                            break;
                        case 'delete':
                            this.deleteItem(itemId);
                            break;
                        case 'copy':
                            this.copyActivity(itemId);
                            break;
                        case 'report':
                        case 'view-report':
                            this.viewReport(itemId);
                            break;
                        case 'participants':
                        case 'view-participants':
                            this.viewParticipants(itemId);
                            break;
                        case 'promote':
                            this.promoteActivity(itemId);
                            break;
                        case 'more-actions':
                            this.showMoreActions(btn, itemId);
                            break;
                        case 'advanced-search':
                            this.showAdvancedSearch();
                            break;
                        case 'sort':
                            this.showSortMenu(btn);
                            break;
                        case 'batch-start':
                            this.batchStart();
                            break;
                        case 'batch-pause':
                            this.batchPause();
                            break;
                        case 'batch-end':
                            this.batchEnd();
                            break;
                    }
                });
            },

            // 绑定项目选择事件
            bindItemSelection() {
                // 监听复选框变化
                document.addEventListener('change', (e) => {
                    if (e.target.classList.contains('item-select')) {
                        const itemId = e.target.dataset.id;
                        if (e.target.checked) {
                            this.selectedItems.add(itemId);
                        } else {
                            this.selectedItems.delete(itemId);
                        }
                        this.updateSelectionUI();
                    }
                });
            },

            // 绑定全选功能
            bindSelectAll() {
                const selectAllCheckbox = document.querySelector('.select-all');
                if (selectAllCheckbox) {
                    selectAllCheckbox.addEventListener('change', (e) => {
                        const isChecked = e.target.checked;
                        document.querySelectorAll('.item-select').forEach(checkbox => {
                            checkbox.checked = isChecked;
                            const itemId = checkbox.dataset.id;
                            if (isChecked) {
                                this.selectedItems.add(itemId);
                            } else {
                                this.selectedItems.delete(itemId);
                            }
                        });
                        this.updateSelectionUI();
                    });
                }
            },
            
            // 显示创建模态框
            showCreateModal() {
                this.resetForm();
                document.getElementById('createModal').classList.add('show');
                document.body.style.overflow = 'hidden';
            },

            // 隐藏创建模态框
            hideCreateModal() {
                document.getElementById('createModal').classList.remove('show');
                document.body.style.overflow = 'auto';
                this.resetForm();
            },

            // 重置表单
            resetForm() {
                this.currentStep = 1;
                this.formData = {};
                this.selectedProduct = null;
                this.showStep(1);
                this.resetStepIndicator();
                document.getElementById('createGroupBuyForm').reset();
                document.getElementById('selectedProduct').style.display = 'none';
                document.getElementById('productResults').style.display = 'none';
            },

            // 下一步
            nextStep() {
                if (this.validateCurrentStep()) {
                    this.saveCurrentStepData();
                    if (this.currentStep < this.maxSteps) {
                        this.currentStep++;
                        this.showStep(this.currentStep);
                        this.updateStepIndicator();
                        this.updateButtons();
                        if (this.currentStep === 4) {
                            this.updateSummary();
                        }
                    }
                }
            },

            // 上一步
            prevStep() {
                if (this.currentStep > 1) {
                    this.currentStep--;
                    this.showStep(this.currentStep);
                    this.updateStepIndicator();
                    this.updateButtons();
                }
            },

            // 显示指定步骤
            showStep(stepNumber) {
                // 隐藏所有步骤内容
                document.querySelectorAll('.step-content').forEach(content => {
                    content.style.display = 'none';
                });
                
                // 显示当前步骤
                const currentStepContent = document.getElementById(`step${stepNumber}`);
                if (currentStepContent) {
                    currentStepContent.style.display = 'block';
                }
            },

            // 更新步骤指示器
            updateStepIndicator() {
                document.querySelectorAll('.step').forEach((step, index) => {
                    const stepNumber = index + 1;
                    step.classList.remove('active', 'completed');
                    
                    if (stepNumber < this.currentStep) {
                        step.classList.add('completed');
                    } else if (stepNumber === this.currentStep) {
                        step.classList.add('active');
                    }
                });
            },

            // 重置步骤指示器
            resetStepIndicator() {
                document.querySelectorAll('.step').forEach((step, index) => {
                    step.classList.remove('active', 'completed');
                    if (index === 0) {
                        step.classList.add('active');
                    }
                });
            },

            // 更新按钮显示
            updateButtons() {
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');
                const submitBtn = document.getElementById('submitBtn');

                if (this.currentStep === 1) {
                    prevBtn.style.display = 'none';
                    nextBtn.style.display = 'inline-flex';
                    submitBtn.style.display = 'none';
                } else if (this.currentStep === this.maxSteps) {
                    prevBtn.style.display = 'inline-flex';
                    nextBtn.style.display = 'none';
                    submitBtn.style.display = 'inline-flex';
                } else {
                    prevBtn.style.display = 'inline-flex';
                    nextBtn.style.display = 'inline-flex';
                    submitBtn.style.display = 'none';
                }
            },

            // 验证当前步骤
            validateCurrentStep() {
                switch (this.currentStep) {
                    case 1:
                        return this.validateStep1();
                    case 2:
                        return this.validateStep2();
                    case 3:
                        return this.validateStep3();
                    default:
                        return true;
                }
            },

            // 验证第一步
            validateStep1() {
                const activityName = document.getElementById('activityName').value.trim();
                const merchant = document.getElementById('merchant').value;
                const category = document.getElementById('category').value;

                if (!activityName) {
                    this.showError('activityName', '请输入活动名称');
                    return false;
                }
                if (!merchant) {
                    this.showError('merchant', '请选择商户');
                    return false;
                }
                if (!category) {
                    this.showError('category', '请选择商品分类');
                    return false;
                }

                this.clearErrors(['activityName', 'merchant', 'category']);
                return true;
            },

            // 验证第二步
            validateStep2() {
                if (!this.selectedProduct) {
                    this.showToast('请选择商品', 'error');
                    return false;
                }

                const originalPrice = parseFloat(document.getElementById('originalPrice').value);
                const groupPrice = parseFloat(document.getElementById('groupPrice').value);
                const groupSize = parseInt(document.getElementById('groupSize').value);
                const totalStock = parseInt(document.getElementById('totalStock').value);

                if (!originalPrice || originalPrice <= 0) {
                    this.showError('originalPrice', '请输入有效的原价');
                    return false;
                }
                if (!groupPrice || groupPrice <= 0) {
                    this.showError('groupPrice', '请输入有效的拼团价');
                    return false;
                }
                if (groupPrice >= originalPrice) {
                    this.showError('groupPrice', '拼团价必须低于原价');
                    return false;
                }
                if (!groupSize || groupSize < 2) {
                    this.showError('groupSize', '拼团人数至少为2人');
                    return false;
                }
                if (!totalStock || totalStock < 1) {
                    this.showError('totalStock', '库存必须大于0');
                    return false;
                }

                this.clearErrors(['originalPrice', 'groupPrice', 'groupSize', 'totalStock']);
                return true;
            },

            // 验证第三步
            validateStep3() {
                const startTime = document.getElementById('startTime').value;
                const endTime = document.getElementById('endTime').value;

                if (!startTime) {
                    this.showError('startTime', '请选择开始时间');
                    return false;
                }
                if (!endTime) {
                    this.showError('endTime', '请选择结束时间');
                    return false;
                }

                const start = new Date(startTime);
                const end = new Date(endTime);
                const now = new Date();

                if (start <= now) {
                    this.showError('startTime', '开始时间必须晚于当前时间');
                    return false;
                }
                if (end <= start) {
                    this.showError('endTime', '结束时间必须晚于开始时间');
                    return false;
                }

                this.clearErrors(['startTime', 'endTime']);
                return true;
            },

            // 显示错误信息
            showError(fieldId, message) {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.classList.add('error');
                    
                    // 移除之前的错误信息
                    const existingError = field.parentNode.querySelector('.field-error');
                    if (existingError) {
                        existingError.remove();
                    }
                    
                    // 添加错误信息
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'field-error';
                    errorDiv.textContent = message;
                    field.parentNode.appendChild(errorDiv);
                }
            },

            // 清除错误信息
            clearErrors(fieldIds) {
                fieldIds.forEach(id => {
                    const field = document.getElementById(id);
                    if (field) {
                        field.classList.remove('error');
                        const errorDiv = field.parentNode.querySelector('.field-error');
                        if (errorDiv) {
                            errorDiv.remove();
                        }
                    }
                });
            },

            // 保存当前步骤数据
            saveCurrentStepData() {
                switch (this.currentStep) {
                    case 1:
                        this.formData.activityName = document.getElementById('activityName').value;
                        this.formData.merchant = document.getElementById('merchant').value;
                        this.formData.category = document.getElementById('category').value;
                        this.formData.priority = document.getElementById('priority').value;
                        this.formData.description = document.getElementById('description').value;
                        break;
                    case 2:
                        this.formData.selectedProduct = this.selectedProduct;
                        this.formData.originalPrice = document.getElementById('originalPrice').value;
                        this.formData.groupPrice = document.getElementById('groupPrice').value;
                        this.formData.groupSize = document.getElementById('groupSize').value;
                        this.formData.totalStock = document.getElementById('totalStock').value;
                        this.formData.limitPerUser = document.getElementById('limitPerUser').value;
                        this.formData.paymentTimeout = document.getElementById('paymentTimeout').value;
                        break;
                    case 3:
                        this.formData.startTime = document.getElementById('startTime').value;
                        this.formData.endTime = document.getElementById('endTime').value;
                        this.formData.enableShare = document.getElementById('enableShare').checked;
                        this.formData.enableCoupon = document.getElementById('enableCoupon').checked;
                        this.formData.enableCommission = document.getElementById('enableCommission').checked;
                        this.formData.commissionRate = document.getElementById('commissionRate').value;
                        this.formData.commissionType = document.getElementById('commissionType').value;
                        this.formData.autoStart = document.getElementById('autoStart').checked;
                        this.formData.autoEnd = document.getElementById('autoEnd').checked;
                        this.formData.autoRefund = document.getElementById('autoRefund').checked;
                        break;
                }
            },

            // 搜索商品
            searchProducts() {
                const query = document.getElementById('productSearch').value.trim();
                if (!query) {
                    this.showToast('请输入搜索关键词', 'warning');
                    return;
                }

                // 模拟商品搜索结果
                const mockProducts = [
                    {
                        id: 1,
                        name: 'iPhone 15 Pro Max 256GB',
                        sku: 'IP15PM256',
                        price: 9999,
                        stock: 100,
                        image: 'https://via.placeholder.com/80x80'
                    },
                    {
                        id: 2,
                        name: '小米14 Ultra 16GB+1TB',
                        sku: 'MI14U1T',
                        price: 6999,
                        stock: 50,
                        image: 'https://via.placeholder.com/80x80'
                    },
                    {
                        id: 3,
                        name: 'MacBook Pro 14英寸 M3芯片',
                        sku: 'MBP14M3',
                        price: 14999,
                        stock: 30,
                        image: 'https://via.placeholder.com/80x80'
                    }
                ];

                const filteredProducts = mockProducts.filter(p => 
                    p.name.toLowerCase().includes(query.toLowerCase()) ||
                    p.sku.toLowerCase().includes(query.toLowerCase())
                );

                this.displayProductResults(filteredProducts);
            },

            // 显示商品搜索结果
            displayProductResults(products) {
                const resultsContainer = document.getElementById('productResults');
                
                if (products.length === 0) {
                    resultsContainer.innerHTML = '<div style="padding: 16px; text-align: center; color: #64748b;">未找到相关商品</div>';
                } else {
                    resultsContainer.innerHTML = products.map(product => `
                        <div class="product-result-item" onclick="GroupBuyManager.selectProduct(${JSON.stringify(product).replace(/"/g, '&quot;')})">
                            <img src="${product.image}" alt="${product.name}">
                            <div class="product-result-info">
                                <div class="product-result-name">${product.name}</div>
                                <div class="product-result-sku">SKU: ${product.sku}</div>
                            </div>
                            <div class="product-result-price">¥${product.price}</div>
                        </div>
                    `).join('');
                }
                
                resultsContainer.style.display = 'block';
            },

            // 选择商品
            selectProduct(product) {
                this.selectedProduct = product;
                
                // 更新选中商品显示
                const selectedProductDiv = document.getElementById('selectedProduct');
                selectedProductDiv.querySelector('.product-thumb').src = product.image;
                selectedProductDiv.querySelector('.product-name').textContent = product.name;
                selectedProductDiv.querySelector('.product-sku').textContent = `SKU: ${product.sku}`;
                selectedProductDiv.querySelector('.original-price').textContent = product.price;
                selectedProductDiv.querySelector('.stock-count').textContent = product.stock;
                selectedProductDiv.style.display = 'block';
                
                // 自动填充价格
                document.getElementById('originalPrice').value = product.price;
                
                // 隐藏搜索结果
                document.getElementById('productResults').style.display = 'none';
                document.getElementById('productSearch').value = product.name;
            },

            // 移除选中的商品
            removeSelectedProduct() {
                this.selectedProduct = null;
                document.getElementById('selectedProduct').style.display = 'none';
                document.getElementById('productSearch').value = '';
                document.getElementById('originalPrice').value = '';
            },

            // 更新汇总信息
            updateSummary() {
                const data = this.formData;
                
                document.getElementById('summaryActivityName').textContent = data.activityName || '-';
                
                const merchantSelect = document.getElementById('merchant');
                const merchantText = merchantSelect.options[merchantSelect.selectedIndex]?.text || '-';
                document.getElementById('summaryMerchant').textContent = merchantText;
                
                const categorySelect = document.getElementById('category');
                const categoryText = categorySelect.options[categorySelect.selectedIndex]?.text || '-';
                document.getElementById('summaryCategory').textContent = categoryText;
                
                document.getElementById('summaryProduct').textContent = this.selectedProduct ? this.selectedProduct.name : '-';
                
                const originalPrice = parseFloat(data.originalPrice || 0);
                const groupPrice = parseFloat(data.groupPrice || 0);
                const discount = originalPrice - groupPrice;
                const discountPercent = originalPrice > 0 ? Math.round((discount / originalPrice) * 100) : 0;
                document.getElementById('summaryPrice').textContent = 
                    `原价: ¥${originalPrice} → 拼团价: ¥${groupPrice} (优惠 ${discountPercent}%)`;
                
                document.getElementById('summaryGroup').textContent = 
                    `${data.groupSize}人拼团，库存${data.totalStock}件`;
                
                const startTime = new Date(data.startTime).toLocaleString();
                const endTime = new Date(data.endTime).toLocaleString();
                document.getElementById('summaryTime').textContent = `${startTime} - ${endTime}`;
            },

            // 提交活动
            submitActivity() {
                this.saveCurrentStepData();
                
                // 显示加载状态
                const submitBtn = document.getElementById('submitBtn');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 创建中...';
                submitBtn.disabled = true;
                
                // 模拟API调用
                setTimeout(() => {
                    this.showToast('拼团活动创建成功！', 'success');
                    this.hideCreateModal();
                    this.refreshData();
                    
                    // 恢复按钮状态
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 2000);
            },

            // 初始化表单验证
            initFormValidation() {
                // 价格变化时更新优惠信息
                document.getElementById('groupPrice').addEventListener('input', this.updateDiscountInfo.bind(this));
                document.getElementById('originalPrice').addEventListener('input', this.updateDiscountInfo.bind(this));
                
                // 佣金设置显示/隐藏
                document.getElementById('enableCommission').addEventListener('change', (e) => {
                    const commissionSettings = document.querySelector('.commission-settings');
                    commissionSettings.style.display = e.target.checked ? 'block' : 'none';
                });
            },

            // 更新优惠信息
            updateDiscountInfo() {
                const originalPrice = parseFloat(document.getElementById('originalPrice').value) || 0;
                const groupPrice = parseFloat(document.getElementById('groupPrice').value) || 0;
                const discountInfo = document.getElementById('discountInfo');
                
                if (originalPrice > 0 && groupPrice > 0) {
                    const discount = originalPrice - groupPrice;
                    const discountPercent = Math.round((discount / originalPrice) * 100);
                    
                    if (groupPrice >= originalPrice) {
                        discountInfo.textContent = '拼团价不能高于或等于原价';
                        discountInfo.style.color = '#ef4444';
                    } else {
                        discountInfo.textContent = `优惠 ¥${discount.toFixed(2)} (${discountPercent}%)`;
                        discountInfo.style.color = '#10b981';
                    }
                } else {
                    discountInfo.textContent = '';
                }
            },

            // 显示批量操作
            showBatchOperations() {
                const batchDiv = document.getElementById('batchOperations');
                batchDiv.style.display = batchDiv.style.display === 'none' ? 'flex' : 'none';
                
                // 添加复选框到列表项
                document.querySelectorAll('.group-buy-item').forEach(item => {
                    if (!item.querySelector('.item-checkbox')) {
                        const checkbox = document.createElement('div');
                        checkbox.className = 'item-checkbox';
                        checkbox.innerHTML = '<input type="checkbox" class="item-select" onchange="GroupBuyManager.handleItemSelect(this)">';
                        item.querySelector('.item-header').prepend(checkbox);
                    }
                });
            },

            // 处理项目选择
            handleItemSelect(checkbox) {
                const itemId = checkbox.closest('.group-buy-item').dataset.id;
                if (checkbox.checked) {
                    this.selectedItems.add(itemId);
                } else {
                    this.selectedItems.delete(itemId);
                }
                this.updateSelectedCount();
            },

            // 更新选中数量
            updateSelectedCount() {
                document.getElementById('selectedCount').textContent = this.selectedItems.size;
            },

            // 批量启动
            batchStart() {
                if (this.selectedItems.size === 0) {
                    this.showToast('请先选择要启动的活动', 'warning');
                    return;
                }
                this.showConfirm('批量启动', `确定要启动选中的 ${this.selectedItems.size} 个活动吗？`, () => {
                    this.selectedItems.forEach(id => this.updateItemStatus(id, 'active', '进行中'));
                    this.showToast(`已启动 ${this.selectedItems.size} 个活动`, 'success');
                    this.selectedItems.clear();
                    this.updateSelectedCount();
                });
            },

            // 批量暂停
            batchPause() {
                if (this.selectedItems.size === 0) {
                    this.showToast('请先选择要暂停的活动', 'warning');
                    return;
                }
                this.showConfirm('批量暂停', `确定要暂停选中的 ${this.selectedItems.size} 个活动吗？`, () => {
                    this.selectedItems.forEach(id => this.updateItemStatus(id, 'paused', '已暂停'));
                    this.showToast(`已暂停 ${this.selectedItems.size} 个活动`, 'warning');
                    this.selectedItems.clear();
                    this.updateSelectedCount();
                });
            },

            // 批量结束
            batchEnd() {
                if (this.selectedItems.size === 0) {
                    this.showToast('请先选择要结束的活动', 'warning');
                    return;
                }
                this.showConfirm('批量结束', `确定要结束选中的 ${this.selectedItems.size} 个活动吗？此操作不可撤销！`, () => {
                    this.selectedItems.forEach(id => this.updateItemStatus(id, 'completed', '已结束'));
                    this.showToast(`已结束 ${this.selectedItems.size} 个活动`, 'success');
                    this.selectedItems.clear();
                    this.updateSelectedCount();
                }, 'danger');
            },
            
            // 搜索和筛选功能
            bindSearchAndFilter() {
                const searchInput = document.querySelector('.search-input');
                const filterSelects = document.querySelectorAll('.filter-select');
                
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => {
                        this.searchTerm = e.target.value;
                        this.filterItems();
                    });
                }
                
                filterSelects.forEach(select => {
                    select.addEventListener('change', (e) => {
                        this.currentFilter = e.target.value;
                        this.filterItems();
                    });
                });
            },

            // 筛选项目
            filterItems() {
                const items = document.querySelectorAll('.group-buy-item');
                let visibleCount = 0;
                
                items.forEach(item => {
                    const shouldShow = this.shouldShowItem(item);
                    item.style.display = shouldShow ? 'block' : 'none';
                    if (shouldShow) visibleCount++;
                });
                
                this.updateResultCount(visibleCount);
            },

            // 判断项目是否应该显示
            shouldShowItem(item) {
                // 根据搜索词筛选
                if (this.searchTerm) {
                    const text = item.textContent.toLowerCase();
                    if (!text.includes(this.searchTerm.toLowerCase())) {
                        return false;
                    }
                }
                
                // 根据状态筛选
                if (this.currentFilter && this.currentFilter !== 'all') {
                    const statusBadge = item.querySelector('.status-badge');
                    if (!statusBadge.classList.contains(this.currentFilter)) {
                        return false;
                    }
                }
                
                return true;
            },

            // 更新结果计数
            updateResultCount(count) {
                const resultCount = document.querySelector('.result-count');
                if (resultCount) {
                    resultCount.textContent = `找到 ${count} 个结果`;
                }
            },

            // 绑定表单提交
            bindFormSubmit() {
                const form = document.getElementById('createGroupBuyForm');
                if (form) {
                    form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleFormSubmit();
                    });
                }
                
                document.getElementById('createModal')?.addEventListener('click', (e) => {
                    if (e.target.id === 'createModal') {
                        this.hideCreateModal();
                    }
                });
            },

            // 处理表单提交
            handleFormSubmit() {
                if (this.currentStep < this.maxSteps) {
                    this.nextStep();
                } else {
                    this.submitForm();
                }
            },

            // 提交表单
            submitForm() {
                this.showToast('正在创建拼团活动...', 'info');
                setTimeout(() => {
                    this.hideCreateModal();
                    this.showToast('拼团活动创建成功！', 'success');
                    // 这里可以重新加载列表
                }, 2000);
            },

            // 更新统计数据
            updateStats() {
                // 这里可以实现统计数据的更新逻辑
                console.log('更新统计数据');
            },

            // 初始化表单验证
            initFormValidation() {
                document.getElementById('groupPrice').addEventListener('input', this.updateDiscountInfo.bind(this));
                document.getElementById('originalPrice').addEventListener('input', this.updateDiscountInfo.bind(this));
                
                // 佣金设置联动
                document.getElementById('enableCommission').addEventListener('change', (e) => {
                    const commissionSettings = document.getElementById('commissionSettings');
                    commissionSettings.style.display = e.target.checked ? 'block' : 'none';
                });
            },

            // 更新折扣信息
            updateDiscountInfo() {
                const groupPrice = parseFloat(document.getElementById('groupPrice').value) || 0;
                const originalPrice = parseFloat(document.getElementById('originalPrice').value) || 0;
                
                if (originalPrice > 0 && groupPrice > 0) {
                    const discount = ((originalPrice - groupPrice) / originalPrice * 100).toFixed(1);
                    const savings = originalPrice - groupPrice;
                    
                    document.getElementById('discountPreview').innerHTML = `
                        <span class="discount-rate">${discount}% 折扣</span>
                        <span class="savings-amount">节省 ¥${savings.toFixed(2)}</span>
                    `;
                } else {
                    document.getElementById('discountPreview').innerHTML = '';
                }
            },

            // 显示吐司消息
            showToast(message, type = 'info') {
                let container = document.querySelector('.toast-container');
                if (!container) {
                    container = document.createElement('div');
                    container.className = 'toast-container';
                    document.body.appendChild(container);
                }

                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.innerHTML = `
                    <div class="toast-content">
                        <i class="fas fa-${this.getToastIcon(type)}"></i>
                        <span>${message}</span>
                    </div>
                `;

                container.appendChild(toast);

                // 显示动画
                setTimeout(() => toast.classList.add('show'), 100);

                // 自动移除
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            },

            // 获取吐司图标
            getToastIcon(type) {
                const icons = {
                    success: 'check-circle',
                    error: 'exclamation-circle',
                    warning: 'exclamation-triangle',
                    info: 'info-circle'
                };
                return icons[type] || 'info-circle';
            },

            // 显示确认对话框
            showConfirm(title, message, onConfirm, type = 'primary') {
                const modal = document.createElement('div');
                modal.className = 'modal-overlay confirm-modal';
                modal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">${title}</h3>
                        </div>
                        <div class="confirm-body">
                            <p>${message}</p>
                        </div>
                        <div class="confirm-actions">
                            <button class="btn btn-secondary" data-action="cancel">取消</button>
                            <button class="btn btn-${type === 'danger' ? 'danger' : 'primary'}" data-action="confirm">确定</button>
                        </div>
                    </div>
                `;

                // 绑定事件
                modal.querySelector('[data-action="cancel"]').addEventListener('click', () => {
                    modal.remove();
                });

                modal.querySelector('[data-action="confirm"]').addEventListener('click', () => {
                    onConfirm();
                    modal.remove();
                });

                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        modal.remove();
                    }
                });

                document.body.appendChild(modal);
                modal.classList.add('show');
            },

            // 显示模态框
            showModal(title, content) {
                const modal = document.createElement('div');
                modal.className = 'modal-overlay';
                modal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">${title}</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                    </div>
                `;

                // 绑定关闭事件
                modal.querySelector('.modal-close').addEventListener('click', () => {
                    modal.remove();
                });

                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        modal.remove();
                    }
                });

                document.body.appendChild(modal);
                modal.classList.add('show');
            },
            
            // 更新操作按钮
            updateActionButtons(container, status) {
                const buttons = {
                    pending: ['start', 'edit', 'delete'],
                    active: ['pause', 'end', 'view', 'report'],
                    paused: ['start', 'end', 'edit'],
                    completed: ['view', 'report', 'copy'],
                    failed: ['view', 'copy', 'delete']
                };
                
                const buttonConfigs = {
                    start: { icon: 'play', text: '启动', class: 'btn-success' },
                    pause: { icon: 'pause', text: '暂停', class: 'btn-warning' },
                    end: { icon: 'stop', text: '结束', class: 'btn-danger' },
                    view: { icon: 'eye', text: '查看', class: 'btn-secondary' },
                    edit: { icon: 'edit', text: '编辑', class: 'btn-primary' },
                    delete: { icon: 'trash', text: '删除', class: 'btn-danger' },
                    report: { icon: 'chart-bar', text: '报告', class: 'btn-secondary' },
                    copy: { icon: 'copy', text: '复制', class: 'btn-primary' }
                };
                
                const allowedActions = buttons[status] || [];
                container.innerHTML = allowedActions.map(action => {
                    const config = buttonConfigs[action];
                    return `<button class="btn ${config.class}" data-action="${action}">
                        <i class="fas fa-${config.icon}"></i> ${config.text}
                    </button>`;
                }).join('');
            },
            
            // 绑定搜索和筛选
            bindSearchAndFilter() {
                // 搜索功能
                const searchInput = document.querySelector('.search-input');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => {
                        this.searchTerm = e.target.value.toLowerCase();
                        this.filterItems();
                    });
                }
                
                // 状态筛选
                document.querySelectorAll('.filter-select').forEach(select => {
                    select.addEventListener('change', (e) => {
                        this.currentFilter = e.target.value;
                        this.filterItems();
                    });
                });
            },
            
            // 筛选项目
            filterItems() {
                const items = document.querySelectorAll('.group-buy-item');
                let visibleCount = 0;
                
                items.forEach(item => {
                    const productName = item.querySelector('.product-details h3')?.textContent.toLowerCase() || '';
                    const merchantName = item.querySelector('.product-details p')?.textContent.toLowerCase() || '';
                    const status = item.querySelector('.status-badge')?.classList.contains('active') ? 'active' :
                                 item.querySelector('.status-badge')?.classList.contains('pending') ? 'pending' :
                                 item.querySelector('.status-badge')?.classList.contains('completed') ? 'completed' : 'failed';
                    
                    const matchesSearch = productName.includes(this.searchTerm) || merchantName.includes(this.searchTerm);
                    const matchesFilter = this.currentFilter === 'all' || status === this.currentFilter;
                    
                    if (matchesSearch && matchesFilter) {
                        item.style.display = 'block';
                        visibleCount++;
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                // 显示结果统计
                this.updateResultCount(visibleCount);
            },
            
            // 更新结果统计
            updateResultCount(count) {
                let countElement = document.querySelector('.result-count');
                if (!countElement) {
                    countElement = document.createElement('div');
                    countElement.className = 'result-count';
                    document.querySelector('.content-header').appendChild(countElement);
                }
                countElement.textContent = `显示 ${count} 个结果`;
            },
            
            // 绑定表单提交
            bindFormSubmit() {
                const form = document.getElementById('createGroupBuyForm');
                if (form) {
                    form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.submitForm(form);
                    });
                }
                
                // 模态框外部点击关闭
                document.getElementById('createModal')?.addEventListener('click', (e) => {
                    if (e.target.id === 'createModal') {
                        this.hideCreateModal();
                    }
                });
            },
            
            // 提交表单
            submitForm(form) {
                // 表单验证
                if (!this.validateForm(form)) {
                    return;
                }
                
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                
                // 显示加载状态
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 创建中...';
                submitBtn.disabled = true;
                
                // 收集表单数据
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                
                // 模拟API调用
                setTimeout(() => {
                    this.showToast('拼团活动创建成功！', 'success');
                    this.hideCreateModal();
                    form.reset();
                    
                    // 恢复按钮状态
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    
                    // 可以在这里添加新项目到列表
                    this.addNewItem(data);
                    this.updateStats();
                }, 2000);
            },
            
            // 表单验证
            validateForm(form) {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        this.showFieldError(field, '此字段为必填项');
                        isValid = false;
                    } else {
                        this.clearFieldError(field);
                    }
                });
                
                // 自定义验证规则
                const groupCount = form.querySelector('input[type="number"]');
                if (groupCount && (groupCount.value < 2 || groupCount.value > 20)) {
                    this.showFieldError(groupCount, '拼团人数必须在2-20之间');
                    isValid = false;
                }
                
                return isValid;
            },
            
            // 显示字段错误
            showFieldError(field, message) {
                this.clearFieldError(field);
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
                field.classList.add('error');
            },
            
            // 清除字段错误
            clearFieldError(field) {
                const error = field.parentNode.querySelector('.field-error');
                if (error) error.remove();
                field.classList.remove('error');
            },
            
            // 更新统计数据
            updateStats() {
                const items = document.querySelectorAll('.group-buy-item');
                const stats = {
                    active: 0,
                    pending: 0,
                    completed: 0,
                    failed: 0
                };
                
                items.forEach(item => {
                    const statusBadge = item.querySelector('.status-badge');
                    if (statusBadge.classList.contains('active')) stats.active++;
                    else if (statusBadge.classList.contains('pending')) stats.pending++;
                    else if (statusBadge.classList.contains('completed')) stats.completed++;
                    else stats.failed++;
                });
                
                // 更新统计卡片
                document.querySelector('.stat-card.active .stat-value').textContent = stats.active;
                document.querySelector('.stat-card.pending .stat-value').textContent = stats.pending;
                document.querySelector('.stat-card.completed .stat-value').textContent = stats.completed;
                document.querySelector('.stat-card.failed .stat-value').textContent = stats.failed;
            },
            
            // 工具函数 - 显示确认对话框
            showConfirm(title, message, callback, type = 'primary') {
                const modal = document.createElement('div');
                modal.className = 'modal-overlay confirm-modal';
                modal.innerHTML = `
                    <div class="modal-content confirm-content">
                        <div class="confirm-header">
                            <h3>${title}</h3>
                        </div>
                        <div class="confirm-body">
                            <p>${message}</p>
                        </div>
                        <div class="confirm-actions">
                            <button class="btn btn-secondary cancel-btn">取消</button>
                            <button class="btn btn-${type} confirm-btn">确认</button>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                modal.classList.add('show');
                
                modal.querySelector('.cancel-btn').onclick = () => {
                    modal.remove();
                };
                
                modal.querySelector('.confirm-btn').onclick = () => {
                    callback();
                    modal.remove();
                };
                
                modal.onclick = (e) => {
                    if (e.target === modal) modal.remove();
                };
            },
            
            // 显示通用模态框
            showModal(title, content) {
                const modal = document.createElement('div');
                modal.className = 'modal-overlay';
                modal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>${title}</h3>
                            <button class="modal-close">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                modal.classList.add('show');
                
                modal.querySelector('.modal-close').onclick = () => {
                    modal.remove();
                };
                
                modal.onclick = (e) => {
                    if (e.target === modal) modal.remove();
                };
            },
            
            // 显示Toast通知
            showToast(message, type = 'info', duration = 3000) {
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.innerHTML = `
                    <div class="toast-content">
                        <i class="fas fa-${this.getToastIcon(type)}"></i>
                        <span>${message}</span>
                    </div>
                `;
                
                // 添加到页面
                let container = document.querySelector('.toast-container');
                if (!container) {
                    container = document.createElement('div');
                    container.className = 'toast-container';
                    document.body.appendChild(container);
                }
                
                container.appendChild(toast);
                
                // 显示动画
                setTimeout(() => toast.classList.add('show'), 100);
                
                // 自动移除
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => toast.remove(), 300);
                }, duration);
            },
            
            // 获取Toast图标
            getToastIcon(type) {
                const icons = {
                    success: 'check-circle',
                    error: 'exclamation-circle',
                    warning: 'exclamation-triangle',
                    info: 'info-circle'
                };
                return icons[type] || 'info-circle';
            },
            
            // 添加新项目到列表
            addNewItem(data) {
                // 这里可以根据表单数据创建新的拼团项目
                // 实际应用中会调用API获取完整数据
                console.log('新增拼团活动:', data);
            },

            // 初始化高级搜索
            initAdvancedSearch() {
                const advancedBtn = document.querySelector('.advanced-search-btn');
                const modal = document.getElementById('advancedSearchModal');
                const form = document.getElementById('advancedSearchForm');
                
                if (advancedBtn) {
                    advancedBtn.addEventListener('click', () => {
                        modal.classList.add('show');
                    });
                }
                
                if (form) {
                    form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleAdvancedSearch(form);
                    });
                    
                    form.querySelector('[data-action="reset"]').addEventListener('click', () => {
                        form.reset();
                        this.clearAdvancedFilters();
                    });
                }
            },

            // 处理高级搜索
            handleAdvancedSearch(form) {
                const formData = new FormData(form);
                const filters = Object.fromEntries(formData.entries());
                
                this.applyAdvancedFilters(filters);
                document.getElementById('advancedSearchModal').classList.remove('show');
                this.showToast('已应用高级筛选条件', 'success');
            },

            // 应用高级筛选
            applyAdvancedFilters(filters) {
                const items = document.querySelectorAll('.group-buy-item');
                let visibleCount = 0;
                
                items.forEach(item => {
                    let matches = true;
                    
                    // 价格筛选
                    if (filters.minPrice || filters.maxPrice) {
                        const priceText = item.querySelector('.detail-value').textContent;
                        const price = parseFloat(priceText.replace('¥', '').replace(',', ''));
                        
                        if (filters.minPrice && price < parseFloat(filters.minPrice)) matches = false;
                        if (filters.maxPrice && price > parseFloat(filters.maxPrice)) matches = false;
                    }
                    
                    // 其他筛选条件可以在这里添加
                    
                    if (matches) {
                        item.style.display = 'block';
                        visibleCount++;
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                this.updateResultCount(visibleCount);
            },

            // 清除高级筛选
            clearAdvancedFilters() {
                const items = document.querySelectorAll('.group-buy-item');
                items.forEach(item => {
                    item.style.display = 'block';
                });
                this.updateResultCount(items.length);
                this.showToast('已清除筛选条件', 'info');
            },

            // 初始化批量操作
            initBatchOperations() {
                // 绑定全选/清空
                const selectAll = document.querySelector('.select-all');
                const clearSelection = document.querySelector('.clear-selection');
                
                selectAll?.addEventListener('click', () => this.selectAllItems());
                clearSelection?.addEventListener('click', () => this.clearAllSelections());
                
                // 绑定复选框变化事件
                document.addEventListener('change', (e) => {
                    if (e.target.classList.contains('item-select')) {
                        this.handleItemSelection();
                    }
                });
                
                // 绑定批量操作按钮
                document.querySelectorAll('[data-action^="batch-"]').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const action = e.target.dataset.action;
                        this.handleBatchAction(action);
                    });
                });
            },

            // 处理项目选择
            handleItemSelection() {
                const checkboxes = document.querySelectorAll('.item-select');
                const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
                
                this.updateSelectionUI(selectedCount);
                this.updateSelectedItems();
            },

            // 更新选择UI
            updateSelectionUI(count) {
                const selectedCountSpan = document.querySelector('.selected-count');
                const batchControls = document.querySelector('.batch-controls');
                
                selectedCountSpan.textContent = `已选择 ${count} 项`;
                
                if (count > 0) {
                    batchControls.style.display = 'flex';
                } else {
                    batchControls.style.display = 'none';
                }
            },

            // 更新选中项目集合
            updateSelectedItems() {
                this.selectedItems.clear();
                document.querySelectorAll('.item-select:checked').forEach(cb => {
                    this.selectedItems.add(cb.dataset.id);
                });
            },

            // 全选项目
            selectAllItems() {
                const checkboxes = document.querySelectorAll('.item-select');
                checkboxes.forEach(cb => {
                    cb.checked = true;
                });
                this.handleItemSelection();
            },

            // 清空选择
            clearAllSelections() {
                const checkboxes = document.querySelectorAll('.item-select');
                checkboxes.forEach(cb => {
                    cb.checked = false;
                });
                this.handleItemSelection();
            },

            // 处理批量操作
            handleBatchAction(action) {
                if (this.selectedItems.size === 0) {
                    this.showToast('请先选择要操作的项目', 'warning');
                    return;
                }
                
                const actionMap = {
                    'batch-start': { title: '批量启动', message: '确定要启动选中的拼团活动吗？', type: 'success' },
                    'batch-pause': { title: '批量暂停', message: '确定要暂停选中的拼团活动吗？', type: 'warning' },
                    'batch-delete': { title: '批量删除', message: '确定要删除选中的拼团活动吗？此操作不可恢复！', type: 'danger' }
                };
                
                const config = actionMap[action];
                if (config) {
                    this.showConfirm(config.title, config.message, () => {
                        this.executeBatchAction(action);
                    }, config.type);
                }
            },

            // 执行批量操作
            executeBatchAction(action) {
                const selectedIds = Array.from(this.selectedItems);
                
                this.showToast(`正在执行批量操作...`, 'info');
                
                // 模拟API调用
                setTimeout(() => {
                    selectedIds.forEach(id => {
                        const item = document.querySelector(`[data-id="${id}"]`).closest('.group-buy-item');
                        
                        switch(action) {
                            case 'batch-start':
                                this.updateItemStatus(id, 'active', '进行中');
                                break;
                            case 'batch-pause':
                                this.updateItemStatus(id, 'pending', '待开团');
                                break;
                            case 'batch-delete':
                                item.remove();
                                break;
                        }
                    });
                    
                    this.clearAllSelections();
                    this.updateStats();
                    this.showToast('批量操作完成', 'success');
                }, 1500);
            },

            // 初始化分页
            initPagination() {
                document.querySelectorAll('.page-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const page = e.target.dataset.page;
                        this.handlePageChange(page);
                    });
                });
            },

            // 处理分页变化
            handlePageChange(page) {
                if (page === 'prev') {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                    }
                } else if (page === 'next') {
                    const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
                    if (this.currentPage < totalPages) {
                        this.currentPage++;
                    }
                } else {
                    this.currentPage = parseInt(page);
                }
                
                this.updatePagination();
                this.loadPageData();
            },

            // 更新分页UI
            updatePagination() {
                const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
                const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
                const endItem = Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
                
                document.querySelector('.page-start').textContent = startItem;
                document.querySelector('.page-end').textContent = endItem;
                document.querySelector('.total-items').textContent = this.totalItems;
                
                // 更新分页按钮状态
                document.querySelectorAll('.page-btn').forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.page === this.currentPage.toString()) {
                        btn.classList.add('active');
                    }
                });
                
                // 更新上一页/下一页按钮状态
                document.querySelector('[data-page="prev"]').disabled = this.currentPage === 1;
                document.querySelector('[data-page="next"]').disabled = this.currentPage === totalPages;
            },

            // 加载分页数据
            loadPageData() {
                // 这里应该调用API加载对应页面的数据
                console.log(`加载第 ${this.currentPage} 页数据`);
            },

            // 初始化排序
            initSorting() {
                const sortBtn = document.querySelector('.sort-btn');
                if (sortBtn) {
                    sortBtn.addEventListener('click', () => {
                        this.showSortMenu();
                    });
                }
            },

            // 显示排序菜单
            showSortMenu() {
                const sortOptions = [
                    { value: 'createTime', label: '创建时间', order: 'desc' },
                    { value: 'price', label: '价格', order: 'asc' },
                    { value: 'progress', label: '进度', order: 'desc' },
                    { value: 'participants', label: '参与人数', order: 'desc' }
                ];
                
                const menuHTML = sortOptions.map(option => `
                    <div class="sort-option ${this.sortField === option.value ? 'active' : ''}" 
                         data-field="${option.value}" data-order="${option.order}">
                        <span>${option.label}</span>
                        <i class="fas fa-sort-${option.order === 'asc' ? 'up' : 'down'}"></i>
                    </div>
                `).join('');
                
                this.showModal('排序选项', `
                    <div class="sort-menu">
                        ${menuHTML}
                    </div>
                `);
                
                // 绑定排序选项点击事件
                document.querySelectorAll('.sort-option').forEach(option => {
                    option.addEventListener('click', (e) => {
                        const field = e.currentTarget.dataset.field;
                        const order = e.currentTarget.dataset.order;
                        this.applySorting(field, order);
                        document.querySelector('.modal-overlay').remove();
                    });
                });
            },

            // 应用排序
            applySorting(field, order) {
                this.sortField = field;
                this.sortOrder = order;
                
                // 这里应该重新加载数据
                this.showToast(`已按${field}排序`, 'success');
                console.log(`排序: ${field} ${order}`);
            },

            // 开始实时更新
            startRealTimeUpdates() {
                // 模拟实时数据更新
                this.realTimeUpdateInterval = setInterval(() => {
                    this.updateRealTimeData();
                }, 30000); // 每30秒更新一次
            },

            // 更新实时数据
            updateRealTimeData() {
                // 模拟数据更新
                const progressBars = document.querySelectorAll('.progress-fill');
                progressBars.forEach(bar => {
                    const currentWidth = parseFloat(bar.style.width);
                    if (currentWidth < 100) {
                        const newWidth = Math.min(currentWidth + Math.random() * 5, 100);
                        bar.style.width = newWidth + '%';
                        
                        // 更新进度文本
                        const progressHeader = bar.closest('.progress-section').querySelector('.progress-header');
                        const progressText = progressHeader.querySelector('.progress-info');
                        const progressPercentage = progressHeader.querySelector('.progress-percentage');
                        
                        if (newWidth >= 100) {
                            progressText.textContent = '拼团已完成';
                            progressPercentage.textContent = '100%';
                        } else {
                            progressPercentage.textContent = Math.round(newWidth) + '%';
                        }
                    }
                });
            },

            // 加载活动日志
            loadActivityLog() {
                // 这里可以加载活动日志数据
                console.log('加载活动日志');
            },

            // 检查权限
            checkPermissions() {
                // 这里可以检查用户权限并相应地显示/隐藏功能
                console.log('检查用户权限');
            },

            // 查看详情
            viewDetail(id) {
                const modal = document.getElementById('detailModal');
                const item = document.querySelector(`[data-id="${id}"]`).closest('.group-buy-item');
                
                // 填充详情数据
                this.populateDetailModal(item);
                
                modal.classList.add('show');
                this.initDetailTabs();
            },

            // 填充详情模态框数据
            populateDetailModal(item) {
                const modal = document.getElementById('detailModal');
                const productTitle = item.querySelector('.product-details h3').textContent;
                const merchantName = item.querySelector('.product-details p').textContent;
                const statusBadge = item.querySelector('.status-badge');
                
                modal.querySelector('.product-title').textContent = productTitle;
                modal.querySelector('.merchant-name').textContent = merchantName;
                modal.querySelector('.status-badge').className = statusBadge.className;
                modal.querySelector('.status-badge').textContent = statusBadge.textContent;
                
                // 填充其他详情数据
                const details = item.querySelectorAll('.detail-value');
                modal.querySelector('.group-price').textContent = details[0]?.textContent || '';
                modal.querySelector('.original-price').textContent = details[1]?.textContent || '';
                
                // 加载参与者数据
                this.loadParticipants(modal);
            },

            // 初始化详情标签页
            initDetailTabs() {
                const tabHeaders = document.querySelectorAll('.tab-header');
                const tabPanes = document.querySelectorAll('.tab-pane');
                
                tabHeaders.forEach(header => {
                    header.addEventListener('click', (e) => {
                        const tabId = e.target.dataset.tab;
                        
                        // 更新标签页状态
                        tabHeaders.forEach(h => h.classList.remove('active'));
                        tabPanes.forEach(p => p.classList.remove('active'));
                        
                        e.target.classList.add('active');
                        document.getElementById(tabId + '-tab').classList.add('active');
                        
                        // 加载对应内容
                        this.loadTabContent(tabId);
                    });
                });
            },

            // 加载标签页内容
            loadTabContent(tabId) {
                switch(tabId) {
                    case 'participants':
                        this.loadParticipantsTab();
                        break;
                    case 'timeline':
                        this.loadTimelineTab();
                        break;
                    case 'analytics':
                        this.loadAnalyticsTab();
                        break;
                    case 'settings':
                        this.loadSettingsTab();
                        break;
                }
            },

            // 加载参与者标签页
            loadParticipantsTab() {
                const container = document.querySelector('.participants-list-detail');
                
                // 模拟参与者数据
                const participants = [
                    { name: '张三', avatar: 'https://via.placeholder.com/40x40', joinTime: '2024-12-20 10:30', status: '已支付' },
                    { name: '李四', avatar: 'https://via.placeholder.com/40x40', joinTime: '2024-12-20 11:15', status: '已支付' },
                    { name: '王五', avatar: 'https://via.placeholder.com/40x40', joinTime: '2024-12-20 12:00', status: '待支付' }
                ];
                
                container.innerHTML = participants.map(p => `
                    <div class="participant-item">
                        <img src="${p.avatar}" alt="用户头像" class="participant-avatar">
                        <div class="participant-info">
                            <div class="participant-name">${p.name}</div>
                            <div class="participant-time">${p.joinTime}</div>
                        </div>
                        <div class="participant-status ${p.status === '已支付' ? 'paid' : 'pending'}">
                            ${p.status}
                        </div>
                    </div>
                `).join('');
            },

            // 加载时间线标签页
            loadTimelineTab() {
                const container = document.querySelector('.timeline');
                
                const timelineData = [
                    { time: '2024-12-20 10:00', event: '拼团活动开始', type: 'start' },
                    { time: '2024-12-20 10:30', event: '第一位用户参团', type: 'join' },
                    { time: '2024-12-20 11:15', event: '第二位用户参团', type: 'join' },
                    { time: '2024-12-20 12:00', event: '第三位用户参团', type: 'join' }
                ];
                
                container.innerHTML = timelineData.map(item => `
                    <div class="timeline-item">
                        <div class="timeline-time">${item.time}</div>
                        <div class="timeline-event">${item.event}</div>
                    </div>
                `).join('');
            },

            // 加载分析标签页
            loadAnalyticsTab() {
                const container = document.querySelector('.analytics-charts');
                container.innerHTML = '<div class="chart-placeholder">数据分析图表将在这里显示</div>';
            },

            // 加载设置标签页
            loadSettingsTab() {
                const container = document.querySelector('.settings-content');
                container.innerHTML = '<div class="settings-placeholder">活动设置选项将在这里显示</div>';
            },

            // 显示更多操作菜单
            showMoreActions(id) {
                const actions = [
                    { label: '查看详情', icon: 'fas fa-eye', action: 'view-detail' },
                    { label: '编辑活动', icon: 'fas fa-edit', action: 'edit' },
                    { label: '复制活动', icon: 'fas fa-copy', action: 'copy' },
                    { label: '活动统计', icon: 'fas fa-chart-bar', action: 'analytics' },
                    { label: '推广链接', icon: 'fas fa-link', action: 'promotion' },
                    { label: '删除活动', icon: 'fas fa-trash', action: 'delete', class: 'danger' }
                ];
                
                const menuHTML = actions.map(action => `
                    <div class="action-menu-item ${action.class || ''}" data-action="${action.action}" data-id="${id}">
                        <i class="${action.icon}"></i>
                        <span>${action.label}</span>
                    </div>
                `).join('');
                
                this.showContextMenu(menuHTML);
            },

            // 显示上下文菜单
            showContextMenu(html) {
                const existing = document.querySelector('.context-menu');
                if (existing) existing.remove();
                
                const menu = document.createElement('div');
                menu.className = 'context-menu';
                menu.innerHTML = html;
                document.body.appendChild(menu);
                
                // 定位菜单
                const rect = event.target.getBoundingClientRect();
                menu.style.top = rect.bottom + 'px';
                menu.style.left = rect.left + 'px';
                
                // 绑定菜单项点击事件
                menu.querySelectorAll('.action-menu-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        const action = e.currentTarget.dataset.action;
                        const id = e.currentTarget.dataset.id;
                        this.handleAction(action, id);
                        menu.remove();
                    });
                });
                
                // 点击外部关闭菜单
                setTimeout(() => {
                    document.addEventListener('click', (e) => {
                        if (!menu.contains(e.target)) {
                            menu.remove();
                        }
                    }, { once: true });
                }, 100);
            },

            // 处理操作
            handleAction(action, id) {
                switch(action) {
                    case 'view-detail':
                        this.viewDetail(id);
                        break;
                    case 'edit':
                        this.editGroupBuy(id);
                        break;
                    case 'copy':
                        this.copyGroupBuy(id);
                        break;
                    case 'analytics':
                        this.showAnalytics(id);
                        break;
                    case 'promotion':
                        this.showPromotionLink(id);
                        break;
                    case 'delete':
                        this.deleteGroupBuy(id);
                        break;
                }
            },

            // 查看参与者
            viewParticipants(id) {
                this.viewDetail(id);
                // 自动切换到参与者标签页
                setTimeout(() => {
                    document.querySelector('[data-tab="participants"]').click();
                }, 100);
            },

            // 显示 Toast 消息
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.innerHTML = `
                    <div class="toast-content">
                        <i class="fas fa-${this.getToastIcon(type)}"></i>
                        <span>${message}</span>
                    </div>
                `;
                
                const container = document.querySelector('.toast-container') || this.createToastContainer();
                container.appendChild(toast);
                
                // 显示动画
                setTimeout(() => toast.classList.add('show'), 100);
                
                // 自动隐藏
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            },

            // 创建 Toast 容器
            createToastContainer() {
                const container = document.createElement('div');
                container.className = 'toast-container';
                document.body.appendChild(container);
                return container;
            },

            // 获取 Toast 图标
            getToastIcon(type) {
                const icons = {
                    success: 'check-circle',
                    error: 'exclamation-circle',
                    warning: 'exclamation-triangle',
                    info: 'info-circle'
                };
                return icons[type] || 'info-circle';
            },

            // 显示确认对话框
            showConfirm(title, message, callback, type = 'info') {
                const modal = document.createElement('div');
                modal.className = 'modal-overlay';
                modal.innerHTML = `
                    <div class="modal-content confirm-modal">
                        <div class="modal-header">
                            <h3 class="modal-title">${title}</h3>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary" data-action="cancel">取消</button>
                            <button class="btn btn-${type === 'danger' ? 'danger' : 'primary'}" data-action="confirm">确定</button>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                modal.classList.add('show');
                
                modal.querySelector('[data-action="cancel"]').addEventListener('click', () => {
                    modal.remove();
                });
                
                modal.querySelector('[data-action="confirm"]').addEventListener('click', () => {
                    callback();
                    modal.remove();
                });
            },

            // 显示通用模态框
            showModal(title, content) {
                const modal = document.createElement('div');
                modal.className = 'modal-overlay';
                modal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">${title}</h3>
                            <button class="modal-close">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                modal.classList.add('show');
                
                modal.querySelector('.modal-close').addEventListener('click', () => {
                    modal.remove();
                });
            },

            // 更新项目状态
            updateItemStatus(id, status, statusText) {
                const item = document.querySelector(`[data-id="${id}"]`).closest('.group-buy-item');
                const statusBadge = item.querySelector('.status-badge');
                
                statusBadge.className = `status-badge ${status}`;
                statusBadge.textContent = statusText;
            },

            // 更新结果数量
            updateResultCount(count) {
                const resultInfo = document.querySelector('.pagination-info');
                if (resultInfo) {
                    resultInfo.innerHTML = `显示 ${count} 项结果`;
                }
            },

            // 显示分析数据
            showAnalytics(id) {
                this.showModal('数据分析', `
                    <div class="analytics-preview">
                        <div class="analytics-chart">
                            <h4>拼团进度趋势</h4>
                            <div class="chart-placeholder">图表区域</div>
                        </div>
                        <div class="analytics-stats">
                            <div class="stat-grid">
                                <div class="stat-card">
                                    <div class="stat-value">1,234</div>
                                    <div class="stat-label">浏览量</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-value">89</div>
                                    <div class="stat-label">转化率</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-value">¥45,678</div>
                                    <div class="stat-label">成交金额</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `);
            },

            // 显示推广链接
            showPromotionLink(id) {
                const link = `https://example.com/group-buy/${id}`;
                this.showModal('推广链接', `
                    <div class="promotion-link">
                        <div class="link-display">
                            <input type="text" value="${link}" readonly class="form-input">
                            <button class="btn btn-primary" onclick="navigator.clipboard.writeText('${link}')">复制链接</button>
                        </div>
                        <div class="qr-code">
                            <h4>二维码分享</h4>
                            <div class="qr-placeholder">二维码将在这里显示</div>
                        </div>
                    </div>
                `);
            },

            // 键盘快捷键支持
            initKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    // Ctrl/Cmd + F: 聚焦搜索框
                    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                        e.preventDefault();
                        document.querySelector('.search-input').focus();
                    }
                    
                    // Ctrl/Cmd + A: 全选
                    if ((e.ctrlKey || e.metaKey) && e.key === 'a' && e.target.tagName !== 'INPUT') {
                        e.preventDefault();
                        this.selectAllItems();
                    }
                    
                    // Escape: 清空选择
                    if (e.key === 'Escape') {
                        this.clearAllSelections();
                        document.querySelectorAll('.modal-overlay').forEach(modal => modal.remove());
                    }
                    
                    // Delete: 删除选中项目
                    if (e.key === 'Delete' && this.selectedItems.size > 0) {
                        this.handleBatchAction('batch-delete');
                    }
                });
            },

            // 响应式布局调整
            handleResize() {
                const container = document.querySelector('.container');
                const width = container.offsetWidth;
                
                if (width < 768) {
                    // 移动端样式调整
                    document.body.classList.add('mobile-layout');
                    document.querySelectorAll('.details-grid').forEach(grid => {
                        grid.style.gridTemplateColumns = '1fr';
                    });
                } else {
                    // 桌面端样式调整
                    document.body.classList.remove('mobile-layout');
                    document.querySelectorAll('.details-grid').forEach(grid => {
                        grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
                    });
                }
            },

            // 初始化响应式支持
            initResponsive() {
                window.addEventListener('resize', () => {
                    this.handleResize();
                });
                this.handleResize(); // 初始调用
            },

            // 停止实时更新
            stopRealTimeUpdates() {
                if (this.realTimeUpdateInterval) {
                    clearInterval(this.realTimeUpdateInterval);
                    this.realTimeUpdateInterval = null;
                }
            },

            // 复制拼团活动
            copyGroupBuy(id) {
                this.showToast('正在复制活动...', 'info');
                
                // 模拟复制操作
                setTimeout(() => {
                    this.showToast('活动复制成功！', 'success');
                    console.log(`复制活动 ID: ${id}`);
                }, 1000);
            },

            // 删除拼团活动
            deleteGroupBuy(id) {
                this.showConfirm('确认删除', '确定要删除这个拼团活动吗？此操作不可恢复！', () => {
                    const item = document.querySelector(`[data-id="${id}"]`).closest('.group-buy-item');
                    item.remove();
                    this.updateStats();
                    this.showToast('活动已删除', 'success');
                }, 'danger');
            },

            // 编辑拼团活动
            editGroupBuy(id) {
                // 这里应该跳转到编辑页面或打开编辑模态框
                this.showToast('跳转到编辑页面...', 'info');
                console.log(`编辑活动 ID: ${id}`);
            },

            // 导出数据
            exportData(format = 'csv') {
                const stats = this.getActivityStats();
                this.showToast(`正在导出${format.toUpperCase()}格式数据...`, 'info');
                
                // 模拟导出操作
                setTimeout(() => {
                    this.showToast('数据导出成功！', 'success');
                }, 2000);
            },

            // 获取活动统计数据
            getActivityStats() {
                const items = document.querySelectorAll('.group-buy-item');
                const stats = {
                    total: items.length,
                    active: 0,
                    completed: 0,
                    pending: 0,
                    failed: 0
                };
                
                items.forEach(item => {
                    const statusBadge = item.querySelector('.status-badge');
                    if (statusBadge.classList.contains('active')) stats.active++;
                    else if (statusBadge.classList.contains('completed')) stats.completed++;
                    else if (statusBadge.classList.contains('pending')) stats.pending++;
                    else if (statusBadge.classList.contains('failed')) stats.failed++;
                });
                
                return stats;
            },

            // 增强的实时更新函数
            updateRealTimeData() {
                // 模拟数据更新
                const progressBars = document.querySelectorAll('.progress-fill');
                progressBars.forEach(bar => {
                    const currentWidth = parseFloat(bar.style.width || '0');
                    if (currentWidth < 100 && Math.random() < 0.3) {
                        const newWidth = Math.min(100, currentWidth + Math.random() * 10);
                        bar.style.width = newWidth + '%';
                        bar.parentElement.querySelector('.progress-percentage').textContent = Math.round(newWidth) + '%';
                        
                        // 更新参与人数
                        const progressInfo = bar.parentElement.querySelector('.progress-info');
                        const match = progressInfo.textContent.match(/(\d+)\/(\d+)人/);
                        if (match && newWidth > currentWidth) {
                            const current = parseInt(match[1]);
                            const total = parseInt(match[2]);
                            const newCurrent = Math.min(total, current + 1);
                            progressInfo.textContent = `拼团进度: ${newCurrent}/${total}人`;
                        }
                        
                        // 如果达到100%，更新状态
                        if (newWidth >= 100) {
                            const item = bar.closest('.group-buy-item');
                            const statusBadge = item.querySelector('.status-badge');
                            if (statusBadge.classList.contains('active')) {
                                statusBadge.className = 'status-badge completed';
                                statusBadge.textContent = '已完成';
                                this.showToast('拼团活动已完成！', 'success');
                            }
                        }
                    }
                });
                
                // 更新统计数据
                this.updateStats();
            },

            // 显示更多操作菜单
            showMoreActions(btn, itemId) {
                // 移除已存在的菜单
                const existingMenu = document.querySelector('.context-menu');
                if (existingMenu) existingMenu.remove();

                // 创建菜单
                const menu = document.createElement('div');
                menu.className = 'context-menu';
                menu.innerHTML = `
                    <div class="action-menu-item" data-action="view-detail" data-id="${itemId}">
                        <i class="fas fa-eye"></i> 查看详情
                    </div>
                    <div class="action-menu-item" data-action="edit" data-id="${itemId}">
                        <i class="fas fa-edit"></i> 编辑活动
                    </div>
                    <div class="action-menu-item" data-action="copy" data-id="${itemId}">
                        <i class="fas fa-copy"></i> 复制活动
                    </div>
                    <div class="action-menu-item" data-action="view-report" data-id="${itemId}">
                        <i class="fas fa-chart-bar"></i> 查看报告
                    </div>
                    <div class="action-menu-item danger" data-action="delete" data-id="${itemId}">
                        <i class="fas fa-trash"></i> 删除活动
                    </div>
                `;

                // 定位菜单
                const rect = btn.getBoundingClientRect();
                menu.style.position = 'fixed';
                menu.style.top = rect.bottom + 'px';
                menu.style.left = rect.left + 'px';
                menu.style.zIndex = '1000';

                document.body.appendChild(menu);

                // 点击其他地方关闭菜单
                setTimeout(() => {
                    document.addEventListener('click', function closeMenu(e) {
                        if (!menu.contains(e.target)) {
                            menu.remove();
                            document.removeEventListener('click', closeMenu);
                        }
                    });
                }, 100);
            },

            // 显示排序菜单
            showSortMenu(btn) {
                // 移除已存在的菜单
                const existingMenu = document.querySelector('.sort-menu');
                if (existingMenu) existingMenu.remove();

                // 创建排序菜单
                const menu = document.createElement('div');
                menu.className = 'context-menu sort-menu';
                menu.innerHTML = `
                    <div class="sort-option active" data-sort="created_desc">
                        <span>按创建时间（最新）</span>
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="sort-option" data-sort="created_asc">
                        <span>按创建时间（最早）</span>
                    </div>
                    <div class="sort-option" data-sort="status">
                        <span>按状态排序</span>
                    </div>
                    <div class="sort-option" data-sort="progress">
                        <span>按进度排序</span>
                    </div>
                    <div class="sort-option" data-sort="end_time">
                        <span>按结束时间</span>
                    </div>
                `;

                // 定位菜单
                const rect = btn.getBoundingClientRect();
                menu.style.position = 'fixed';
                menu.style.top = rect.bottom + 'px';
                menu.style.right = (window.innerWidth - rect.right) + 'px';
                menu.style.zIndex = '1000';

                document.body.appendChild(menu);

                // 绑定排序选项点击事件
                menu.querySelectorAll('.sort-option').forEach(option => {
                    option.addEventListener('click', () => {
                        const sortType = option.dataset.sort;
                        this.applySorting(sortType);
                        
                        // 更新激活状态
                        menu.querySelectorAll('.sort-option').forEach(opt => opt.classList.remove('active'));
                        option.classList.add('active');
                        
                        // 关闭菜单
                        setTimeout(() => menu.remove(), 150);
                    });
                });

                // 点击其他地方关闭菜单
                setTimeout(() => {
                    document.addEventListener('click', function closeMenu(e) {
                        if (!menu.contains(e.target) && e.target !== btn) {
                            menu.remove();
                            document.removeEventListener('click', closeMenu);
                        }
                    });
                }, 100);
            },

            // 应用排序
            applySorting(sortType) {
                const items = Array.from(document.querySelectorAll('.group-buy-item'));
                const container = document.querySelector('.group-buy-list');

                items.sort((a, b) => {
                    switch(sortType) {
                        case 'created_desc':
                            // 按创建时间降序（假设按DOM顺序）
                            return 0;
                        case 'created_asc':
                            // 按创建时间升序
                            return 0;
                        case 'status':
                            // 按状态排序：进行中 > 待开团 > 已完成 > 已失败
                            const statusOrder = {'active': 1, 'pending': 2, 'completed': 3, 'failed': 4};
                            const aStatus = a.querySelector('.status-badge').classList.contains('active') ? 'active' :
                                           a.querySelector('.status-badge').classList.contains('pending') ? 'pending' :
                                           a.querySelector('.status-badge').classList.contains('completed') ? 'completed' : 'failed';
                            const bStatus = b.querySelector('.status-badge').classList.contains('active') ? 'active' :
                                           b.querySelector('.status-badge').classList.contains('pending') ? 'pending' :
                                           b.querySelector('.status-badge').classList.contains('completed') ? 'completed' : 'failed';
                            return statusOrder[aStatus] - statusOrder[bStatus];
                        case 'progress':
                            // 按进度排序
                            const aProgress = parseFloat(a.querySelector('.progress-fill').style.width || '0');
                            const bProgress = parseFloat(b.querySelector('.progress-fill').style.width || '0');
                            return bProgress - aProgress;
                        default:
                            return 0;
                    }
                });

                // 重新排列DOM
                items.forEach(item => container.appendChild(item));
                
                this.showToast(`已按 ${this.getSortTypeName(sortType)} 排序`, 'info');
            },

            // 获取排序类型名称
            getSortTypeName(sortType) {
                const names = {
                    'created_desc': '创建时间（最新）',
                    'created_asc': '创建时间（最早）',
                    'status': '状态',
                    'progress': '进度',
                    'end_time': '结束时间'
                };
                return names[sortType] || '默认';
            },

            // 批量启动
            batchStart() {
                const selectedItems = this.getSelectedItems();
                if (selectedItems.length === 0) {
                    this.showToast('请先选择要启动的活动', 'warning');
                    return;
                }

                this.showConfirm(
                    '批量启动确认',
                    `确定要启动选中的 ${selectedItems.length} 个拼团活动吗？`,
                    () => {
                        selectedItems.forEach(itemId => {
                            this.startActivity(itemId, false); // false表示不显示单独的提示
                        });
                        this.showToast(`成功启动 ${selectedItems.length} 个拼团活动`, 'success');
                        this.clearSelection();
                    },
                    'success'
                );
            },

            // 批量暂停
            batchPause() {
                const selectedItems = this.getSelectedItems();
                if (selectedItems.length === 0) {
                    this.showToast('请先选择要暂停的活动', 'warning');
                    return;
                }

                this.showConfirm(
                    '批量暂停确认',
                    `确定要暂停选中的 ${selectedItems.length} 个拼团活动吗？`,
                    () => {
                        selectedItems.forEach(itemId => {
                            this.pauseActivity(itemId, false);
                        });
                        this.showToast(`成功暂停 ${selectedItems.length} 个拼团活动`, 'success');
                        this.clearSelection();
                    },
                    'warning'
                );
            },

            // 批量结束
            batchEnd() {
                const selectedItems = this.getSelectedItems();
                if (selectedItems.length === 0) {
                    this.showToast('请先选择要结束的活动', 'warning');
                    return;
                }

                this.showConfirm(
                    '批量结束确认',
                    `确定要结束选中的 ${selectedItems.length} 个拼团活动吗？此操作不可撤销！`,
                    () => {
                        selectedItems.forEach(itemId => {
                            this.endActivity(itemId, false);
                        });
                        this.showToast(`成功结束 ${selectedItems.length} 个拼团活动`, 'success');
                        this.clearSelection();
                    },
                    'danger'
                );
            },

            // 获取选中的项目
            getSelectedItems() {
                const selectedCheckboxes = document.querySelectorAll('.item-select:checked');
                return Array.from(selectedCheckboxes).map(checkbox => checkbox.dataset.id);
            },

            // 清除选择
            clearSelection() {
                document.querySelectorAll('.item-select').forEach(checkbox => {
                    checkbox.checked = false;
                });
                this.selectedItems.clear();
                this.updateSelectionUI();
            },

            // 更新选择界面
            updateSelectionUI() {
                const selectedCount = this.selectedItems.size;
                const batchActions = document.querySelector('.bulk-actions');
                const batchControls = document.querySelector('.batch-controls');
                const batchDeleteBtn = document.querySelector('[data-action="batch-delete"]');
                
                if (selectedCount > 0) {
                    if (batchActions) batchActions.classList.add('show');
                    if (batchControls) batchControls.style.display = 'flex';
                    if (batchDeleteBtn) batchDeleteBtn.disabled = false;
                    
                    // 更新选中数量显示
                    const countDisplays = document.querySelectorAll('.selected-count');
                    countDisplays.forEach(display => {
                        display.textContent = selectedCount;
                    });
                } else {
                    if (batchActions) batchActions.classList.remove('show');
                    if (batchControls) batchControls.style.display = 'none';
                    if (batchDeleteBtn) batchDeleteBtn.disabled = true;
                }
            },

            // 全选功能
            selectAll() {
                const checkboxes = document.querySelectorAll('.item-select');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = true;
                    this.selectedItems.add(checkbox.dataset.id);
                });
                this.updateSelectionUI();
            },

            // 查看详情
            viewDetails(itemId) {
                if (!itemId) {
                    this.showToast('项目ID不存在', 'error');
                    return;
                }
                console.log('查看详情:', itemId);
                this.showToast('查看详情功能', 'info');
                // 这里可以实现跳转到详情页面或显示详情模态框
            },

            // 编辑项目
            editItem(itemId) {
                if (!itemId) {
                    this.showToast('项目ID不存在', 'error');
                    return;
                }
                console.log('编辑项目:', itemId);
                this.showToast('编辑功能', 'info');
                // 这里可以实现编辑功能
            },

            // 启动活动
            startActivity(itemId, showToast = true) {
                if (!itemId) {
                    if (showToast) this.showToast('项目ID不存在', 'error');
                    return;
                }
                console.log('启动活动:', itemId);
                
                // 更新界面状态
                const item = document.querySelector(`[data-id="${itemId}"]`);
                if (item) {
                    const statusBadge = item.querySelector('.status-badge');
                    if (statusBadge) {
                        statusBadge.className = 'status-badge active';
                        statusBadge.textContent = '进行中';
                    }
                }
                
                if (showToast) this.showToast('活动已启动', 'success');
            },

            // 暂停活动
            pauseActivity(itemId, showToast = true) {
                if (!itemId) {
                    if (showToast) this.showToast('项目ID不存在', 'error');
                    return;
                }
                console.log('暂停活动:', itemId);
                
                // 更新界面状态
                const item = document.querySelector(`[data-id="${itemId}"]`);
                if (item) {
                    const statusBadge = item.querySelector('.status-badge');
                    if (statusBadge) {
                        statusBadge.className = 'status-badge paused';
                        statusBadge.textContent = '已暂停';
                    }
                }
                
                if (showToast) this.showToast('活动已暂停', 'warning');
            },

            // 结束活动
            endActivity(itemId, showToast = true) {
                if (!itemId) {
                    if (showToast) this.showToast('项目ID不存在', 'error');
                    return;
                }
                console.log('结束活动:', itemId);
                
                this.showConfirm(
                    '结束活动确认',
                    '确定要结束这个拼团活动吗？此操作不可撤销！',
                    () => {
                        // 更新界面状态
                        const item = document.querySelector(`[data-id="${itemId}"]`);
                        if (item) {
                            const statusBadge = item.querySelector('.status-badge');
                            if (statusBadge) {
                                statusBadge.className = 'status-badge completed';
                                statusBadge.textContent = '已完成';
                            }
                        }
                        
                        if (showToast) this.showToast('活动已结束', 'success');
                    },
                    'danger'
                );
            },

            // 删除项目
            deleteItem(itemId) {
                if (!itemId) {
                    this.showToast('项目ID不存在', 'error');
                    return;
                }
                console.log('删除项目:', itemId);
                
                this.showConfirm(
                    '删除确认',
                    '确定要删除这个拼团活动吗？此操作不可撤销！',
                    () => {
                        const item = document.querySelector(`[data-id="${itemId}"]`);
                        if (item) {
                            item.remove();
                        }
                        this.showToast('活动已删除', 'success');
                    },
                    'danger'
                );
            },

            // 复制活动
            copyActivity(itemId) {
                if (!itemId) {
                    this.showToast('项目ID不存在', 'error');
                    return;
                }
                console.log('复制活动:', itemId);
                this.showToast('复制功能', 'info');
                // 这里可以实现复制功能
            },

            // 查看报告
            viewReport(itemId) {
                if (!itemId) {
                    this.showToast('项目ID不存在', 'error');
                    return;
                }
                console.log('查看报告:', itemId);
                
                // 创建报告模态框
                const reportModal = this.createReportModal(itemId);
                document.body.appendChild(reportModal);
                reportModal.classList.add('show');
            },

            // 查看参与者
            viewParticipants(itemId) {
                if (!itemId) {
                    this.showToast('项目ID不存在', 'error');
                    return;
                }
                console.log('查看参与者:', itemId);
                
                // 创建参与者模态框
                const participantsModal = this.createParticipantsModal(itemId);
                document.body.appendChild(participantsModal);
                participantsModal.classList.add('show');
            },

            // 显示更多操作菜单
            showMoreActions(btn, itemId) {
                console.log('显示更多操作:', itemId);
                
                // 移除已存在的菜单
                const existingMenu = document.querySelector('.context-menu');
                if (existingMenu) {
                    existingMenu.remove();
                }
                
                // 创建菜单
                const menu = document.createElement('div');
                menu.className = 'context-menu';
                menu.innerHTML = `
                    <div class="action-menu-item" data-action="copy" data-id="${itemId}">
                        <i class="fas fa-copy"></i>
                        <span>复制活动</span>
                    </div>
                    <div class="action-menu-item" data-action="export" data-id="${itemId}">
                        <i class="fas fa-download"></i>
                        <span>导出数据</span>
                    </div>
                    <div class="action-menu-item danger" data-action="delete" data-id="${itemId}">
                        <i class="fas fa-trash"></i>
                        <span>删除活动</span>
                    </div>
                `;
                
                // 定位菜单
                document.body.appendChild(menu);
                const rect = btn.getBoundingClientRect();
                menu.style.position = 'fixed';
                menu.style.top = (rect.bottom + 5) + 'px';
                menu.style.left = rect.left + 'px';
                menu.style.zIndex = '1000';
                
                // 点击其他地方关闭菜单
                setTimeout(() => {
                    document.addEventListener('click', function closeMenu(e) {
                        if (!menu.contains(e.target)) {
                            menu.remove();
                            document.removeEventListener('click', closeMenu);
                        }
                    });
                }, 100);
            },

            // 显示排序菜单
            showSortMenu(btn) {
                console.log('显示排序菜单');
                
                // 移除已存在的菜单
                const existingMenu = document.querySelector('.sort-menu');
                if (existingMenu) {
                    existingMenu.remove();
                }
                
                // 创建排序菜单
                const menu = document.createElement('div');
                menu.className = 'context-menu sort-menu';
                menu.innerHTML = `
                    <div class="sort-option" data-sort="created_desc">
                        <i class="fas fa-clock"></i>
                        <span>创建时间（最新）</span>
                        <i class="fas fa-check" style="display: none;"></i>
                    </div>
                    <div class="sort-option" data-sort="created_asc">
                        <i class="fas fa-clock"></i>
                        <span>创建时间（最早）</span>
                        <i class="fas fa-check" style="display: none;"></i>
                    </div>
                    <div class="sort-option" data-sort="status">
                        <i class="fas fa-info-circle"></i>
                        <span>状态</span>
                        <i class="fas fa-check" style="display: none;"></i>
                    </div>
                    <div class="sort-option" data-sort="progress">
                        <i class="fas fa-chart-line"></i>
                        <span>进度</span>
                        <i class="fas fa-check" style="display: none;"></i>
                    </div>
                `;
                
                // 定位菜单
                document.body.appendChild(menu);
                const rect = btn.getBoundingClientRect();
                menu.style.position = 'fixed';
                menu.style.top = (rect.bottom + 5) + 'px';
                menu.style.left = rect.left + 'px';
                menu.style.zIndex = '1000';
                
                // 绑定排序事件
                menu.addEventListener('click', (e) => {
                    const sortOption = e.target.closest('.sort-option');
                    if (sortOption) {
                        const sortType = sortOption.dataset.sort;
                        this.applySorting(sortType);
                        menu.remove();
                    }
                });
                
                // 点击其他地方关闭菜单
                setTimeout(() => {
                    document.addEventListener('click', function closeMenu(e) {
                        if (!menu.contains(e.target) && !btn.contains(e.target)) {
                            menu.remove();
                            document.removeEventListener('click', closeMenu);
                        }
                    });
                }, 100);
            },

            // 创建报告模态框
            createReportModal(itemId) {
                const modal = document.createElement('div');
                modal.className = 'modal-overlay';
                modal.innerHTML = `
                    <div class="modal-content report-modal">
                        <div class="modal-header">
                            <h3>拼团活动报告</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="report-content">
                                <div class="report-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">总参与人数</span>
                                        <span class="stat-value">128</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">成功拼团</span>
                                        <span class="stat-value">45</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">总销售额</span>
                                        <span class="stat-value">¥12,580</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">转化率</span>
                                        <span class="stat-value">68%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary close-modal">关闭</button>
                            <button class="btn btn-primary">导出报告</button>
                        </div>
                    </div>
                `;
                
                // 绑定关闭事件
                modal.querySelector('.modal-close').addEventListener('click', () => {
                    modal.remove();
                });
                
                modal.querySelector('.close-modal').addEventListener('click', () => {
                    modal.remove();
                });
                
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        modal.remove();
                    }
                });
                
                return modal;
            },

            // 创建参与者模态框
            createParticipantsModal(itemId) {
                const modal = document.createElement('div');
                modal.className = 'modal-overlay';
                modal.innerHTML = `
                    <div class="modal-content participants-modal">
                        <div class="modal-header">
                            <h3>参与者列表</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="participants-content">
                                <div class="participant-list">
                                    <div class="participant-item">
                                        <img src="../assets/images/avatar-default.png" alt="用户头像">
                                        <div class="participant-info">
                                            <div class="name">张三</div>
                                            <div class="time">2024-01-15 14:30</div>
                                        </div>
                                        <div class="status">已支付</div>
                                    </div>
                                    <div class="participant-item">
                                        <img src="../assets/images/avatar-default.png" alt="用户头像">
                                        <div class="participant-info">
                                            <div class="name">李四</div>
                                            <div class="time">2024-01-15 15:20</div>
                                        </div>
                                        <div class="status">已支付</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary close-modal">关闭</button>
                            <button class="btn btn-primary">导出列表</button>
                        </div>
                    </div>
                `;
                
                // 绑定关闭事件
                modal.querySelector('.modal-close').addEventListener('click', () => {
                    modal.remove();
                });
                
                modal.querySelector('.close-modal').addEventListener('click', () => {
                    modal.remove();
                });
                
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        modal.remove();
                    }
                });
                
                                 return modal;
             },

             // 显示高级搜索
             showAdvancedSearch() {
                 console.log('显示高级搜索');
                 this.showToast('高级搜索功能', 'info');
                 // 实现显示高级搜索功能
                 const modal = document.getElementById('advancedSearchModal');
                 if (modal) {
                     modal.classList.add('show');
                 }
             },

             // 批量删除
             batchDelete() {
                 const selectedItems = this.getSelectedItems();
                 if (selectedItems.length === 0) {
                     this.showToast('请先选择要删除的活动', 'warning');
                     return;
                 }

                 this.showConfirm(
                     '批量删除确认',
                     `确定要删除选中的 ${selectedItems.length} 个拼团活动吗？此操作不可撤销！`,
                     () => {
                         selectedItems.forEach(itemId => {
                             const item = document.querySelector(`[data-id="${itemId}"]`);
                             if (item) {
                                 item.remove();
                             }
                         });
                         this.showToast(`成功删除 ${selectedItems.length} 个拼团活动`, 'success');
                         this.clearSelection();
                     },
                     'danger'
                 );
             },

             // 导出数据
             exportData() {
                 const selectedItems = this.getSelectedItems();
                 if (selectedItems.length === 0) {
                     this.showToast('导出所有数据', 'info');
                 } else {
                     this.showToast(`导出 ${selectedItems.length} 个活动的数据`, 'info');
                 }
                 console.log('导出数据功能');
             },

             // 刷新数据
             refreshData() {
                 console.log('刷新数据');
                 this.showToast('数据已刷新', 'success');
                 // 这里可以实现刷新功能
                 this.updateStats();
             },

             // 推广活动
             promoteActivity(itemId) {
                 console.log('推广活动:', itemId);
                 this.showToast(`开始推广拼团活动 ${itemId}`, 'success');
                 
                 // 模拟推广功能
                 const content = `
                     <div style="text-align: center; padding: 20px;">
                         <div style="margin-bottom: 20px;">
                             <i class="fas fa-share-alt" style="font-size: 48px; color: #10b981; margin-bottom: 16px;"></i>
                             <h3 style="margin: 0 0 8px 0; color: #1e293b;">推广活动</h3>
                             <p style="margin: 0; color: #64748b;">选择推广方式分享活动</p>
                         </div>
                         <div style="display: grid; gap: 12px; margin-bottom: 24px;">
                             <button class="btn btn-primary" onclick="alert('微信推广功能')">
                                 <i class="fab fa-weixin"></i> 微信推广
                             </button>
                             <button class="btn btn-primary" onclick="alert('朋友圈推广功能')">
                                 <i class="fas fa-share-alt"></i> 朋友圈推广
                             </button>
                             <button class="btn btn-primary" onclick="alert('社群推广功能')">
                                 <i class="fas fa-users"></i> 社群推广
                             </button>
                         </div>
                         <div style="text-align: center;">
                             <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').classList.remove('show')">关闭</button>
                         </div>
                     </div>
                 `;
                 
                 this.showModal('推广设置', content);
             },

             // 高级搜索初始化
             initAdvancedSearch() {
                 console.log('初始化高级搜索功能');
                 const advancedBtn = document.querySelector('[data-action="advanced-search"]');
                 if (advancedBtn) {
                     advancedBtn.addEventListener('click', () => this.showAdvancedSearch());
                 }
             },

             // 批量操作初始化
             initBatchOperations() {
                 console.log('初始化批量操作功能');
                 this.updateBatchUI();
             },

             // 更新批量操作UI
             updateBatchUI() {
                 const selectedCount = this.selectedItems.size;
                 const toolbar = document.querySelector('.bulk-actions-toolbar');
                 const infoText = document.querySelector('.bulk-actions-info');
                 
                 if (selectedCount > 0) {
                     if (toolbar) {
                         toolbar.classList.add('show');
                     }
                     if (infoText) {
                         infoText.textContent = `已选择 ${selectedCount} 项`;
                     }
                 } else {
                     if (toolbar) {
                         toolbar.classList.remove('show');
                     }
                 }
                 
                 // 更新全选复选框状态
                 const selectAllCheckbox = document.querySelector('.select-all');
                 if (selectAllCheckbox) {
                     const totalItems = document.querySelectorAll('.item-select').length;
                     selectAllCheckbox.checked = selectedCount === totalItems && totalItems > 0;
                     selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalItems;
                 }
             },

             // 排序功能初始化
             initSorting() {
                 console.log('初始化排序功能');
                 const sortBtn = document.querySelector('[data-action="sort"]');
                 if (sortBtn) {
                     sortBtn.addEventListener('click', (e) => this.showSortMenu(e.target));
                 }
             },

             // 启动实时更新
             startRealTimeUpdates() {
                 console.log('启动实时更新');
                 // 模拟实时数据更新
                 setInterval(() => {
                     this.updateStats();
                 }, 30000); // 每30秒更新一次
             },

             // 键盘快捷键初始化
             initKeyboardShortcuts() {
                 console.log('初始化键盘快捷键');
                 document.addEventListener('keydown', (e) => {
                     if (e.ctrlKey || e.metaKey) {
                         switch(e.key) {
                             case 'n':
                                 e.preventDefault();
                                 this.showCreateModal();
                                 break;
                             case 'r':
                                 e.preventDefault();
                                 this.refreshData();
                                 break;
                             case 'f':
                                 e.preventDefault();
                                 document.querySelector('.search-input')?.focus();
                                 break;
                         }
                     }
                 });
             },

             // 响应式布局初始化
             initResponsive() {
                 console.log('初始化响应式布局');
                 const handleResize = () => {
                     const width = window.innerWidth;
                     const isMobile = width < 768;
                     document.body.classList.toggle('mobile-view', isMobile);
                 };
                 
                 window.addEventListener('resize', handleResize);
                 handleResize(); // 初始调用
             },

             // 查看详情
             viewDetails(itemId) {
                 console.log('查看详情:', itemId);
                 this.showToast(`查看拼团活动 ${itemId} 的详情`, 'info');
                 
                 // 模拟获取详情数据
                 const detailData = {
                     id: itemId,
                     title: '夏季清凉饮品团购',
                     description: '精选夏季饮品，清凉一夏',
                     price: '¥29.90',
                     originalPrice: '¥39.90',
                     groupSize: 3,
                     status: 'active'
                 };
                 
                 this.showModal('活动详情', this.generateDetailContent(detailData));
             },

             // 编辑项目
             editItem(itemId) {
                 console.log('编辑项目:', itemId);
                 this.showToast(`编辑拼团活动 ${itemId}`, 'info');
                 this.showCreateModal(itemId); // 复用创建模态框，传入ID表示编辑模式
             },

             // 开始活动
             startActivity(itemId) {
                 console.log('开始活动:', itemId);
                 this.showConfirm(
                     '启动活动',
                     `确定要启动拼团活动 ${itemId} 吗？`,
                     () => {
                         this.showToast(`拼团活动 ${itemId} 已启动`, 'success');
                         // 更新UI状态
                         const item = document.querySelector(`[data-id="${itemId}"]`);
                         if (item) {
                             const statusBadge = item.querySelector('.status-badge');
                             if (statusBadge) {
                                 statusBadge.className = 'status-badge status-active';
                                 statusBadge.textContent = '进行中';
                             }
                         }
                     }
                 );
             },

             // 复制活动
             copyActivity(itemId) {
                 console.log('复制活动:', itemId);
                 this.showToast(`正在复制拼团活动 ${itemId}`, 'info');
                 
                 setTimeout(() => {
                     this.showToast(`拼团活动 ${itemId} 复制成功`, 'success');
                 }, 1000);
             },

             // 查看报告
             viewReport(itemId) {
                 console.log('查看报告:', itemId);
                 this.showToast(`查看拼团活动 ${itemId} 的报告`, 'info');
                 
                 const reportData = {
                     activityId: itemId,
                     totalViews: 1234,
                     totalParticipants: 89,
                     successRate: '78%',
                     revenue: '¥2,580.50'
                 };
                 
                 this.showModal('活动报告', this.generateReportContent(reportData));
             },

             // 查看参与者
             viewParticipants(itemId) {
                 console.log('查看参与者:', itemId);
                 this.showToast(`查看拼团活动 ${itemId} 的参与者`, 'info');
                 this.showModal('参与者列表', this.generateParticipantsContent(itemId));
             },

             // 显示更多操作菜单
             showMoreActions(button, itemId) {
                 console.log('显示更多操作:', itemId);
                 this.showContextMenu(button, [
                     { text: '设为推荐', action: () => this.showToast('设为推荐', 'success') },
                     { text: '取消推荐', action: () => this.showToast('取消推荐', 'success') },
                     { text: '导出数据', action: () => this.showToast('导出数据', 'info') },
                     { text: '查看日志', action: () => this.showToast('查看日志', 'info') }
                 ]);
             },

             // 显示高级搜索
             showAdvancedSearch() {
                 console.log('显示高级搜索');
                 const content = `
                     <div class="advanced-search-form">
                         <div class="form-row">
                             <div class="form-group">
                                 <label>活动名称</label>
                                 <input type="text" class="form-control" placeholder="搜索活动名称">
                             </div>
                             <div class="form-group">
                                 <label>活动状态</label>
                                 <select class="form-control">
                                     <option value="">全部状态</option>
                                     <option value="pending">待开始</option>
                                     <option value="active">进行中</option>
                                     <option value="completed">已完成</option>
                                     <option value="cancelled">已取消</option>
                                 </select>
                             </div>
                         </div>
                         <div class="form-row">
                             <div class="form-group">
                                 <label>价格范围</label>
                                 <div class="price-range">
                                     <input type="number" placeholder="最低价" class="form-control">
                                     <span>-</span>
                                     <input type="number" placeholder="最高价" class="form-control">
                                 </div>
                             </div>
                             <div class="form-group">
                                 <label>创建时间</label>
                                 <input type="date" class="form-control">
                             </div>
                         </div>
                         <div class="form-actions">
                             <button type="button" class="btn btn-secondary" onclick="GroupBuyManager.closeModal()">取消</button>
                             <button type="button" class="btn btn-primary" onclick="GroupBuyManager.performAdvancedSearch()">搜索</button>
                         </div>
                     </div>
                 `;
                 this.showModal('高级搜索', content);
             },

             // 执行高级搜索
             performAdvancedSearch() {
                 console.log('执行高级搜索');
                 this.showToast('执行高级搜索', 'info');
                 this.closeModal();
             },

             // 显示排序菜单
             showSortMenu(button) {
                 console.log('显示排序菜单');
                 this.showContextMenu(button, [
                     { text: '按创建时间排序', action: () => this.sortBy('created_at') },
                     { text: '按活动名称排序', action: () => this.sortBy('title') },
                     { text: '按价格排序', action: () => this.sortBy('price') },
                     { text: '按状态排序', action: () => this.sortBy('status') }
                 ]);
             },

             // 排序功能
             sortBy(field) {
                 console.log('排序字段:', field);
                 this.showToast(`按${field}排序`, 'info');
                 this.hideContextMenu();
             },

             // 批量开始
             batchStart() {
                 const selectedItems = this.getSelectedItems();
                 if (selectedItems.length === 0) {
                     this.showToast('请先选择要启动的活动', 'warning');
                     return;
                 }

                 this.showConfirm(
                     '批量启动',
                     `确定要启动选中的 ${selectedItems.length} 个活动吗？`,
                     () => {
                         this.showToast(`已启动 ${selectedItems.length} 个活动`, 'success');
                         this.clearSelection();
                     }
                 );
             },

             // 批量暂停
             batchPause() {
                 const selectedItems = this.getSelectedItems();
                 if (selectedItems.length === 0) {
                     this.showToast('请先选择要暂停的活动', 'warning');
                     return;
                 }

                 this.showConfirm(
                     '批量暂停',
                     `确定要暂停选中的 ${selectedItems.length} 个活动吗？`,
                     () => {
                         this.showToast(`已暂停 ${selectedItems.length} 个活动`, 'success');
                         this.clearSelection();
                     }
                 );
             },

             // 批量结束
             batchEnd() {
                 const selectedItems = this.getSelectedItems();
                 if (selectedItems.length === 0) {
                     this.showToast('请先选择要结束的活动', 'warning');
                     return;
                 }

                 this.showConfirm(
                     '批量结束',
                     `确定要结束选中的 ${selectedItems.length} 个活动吗？此操作不可撤销！`,
                     () => {
                         this.showToast(`已结束 ${selectedItems.length} 个活动`, 'success');
                         this.clearSelection();
                     },
                     'danger'
                 );
             },

             // 生成详情内容
             generateDetailContent(data) {
                 return `
                     <div class="detail-content">
                         <div class="detail-header">
                             <h3>${data.title}</h3>
                             <span class="status-badge status-${data.status}">${this.getStatusText(data.status)}</span>
                         </div>
                         <div class="detail-info">
                             <div class="info-item">
                                 <label>活动描述：</label>
                                 <span>${data.description}</span>
                             </div>
                             <div class="info-item">
                                 <label>团购价格：</label>
                                 <span class="price">${data.price}</span>
                             </div>
                             <div class="info-item">
                                 <label>原价：</label>
                                 <span class="original-price">${data.originalPrice}</span>
                             </div>
                             <div class="info-item">
                                 <label>成团人数：</label>
                                 <span>${data.groupSize}人</span>
                             </div>
                         </div>
                         <div class="detail-actions">
                             <button class="btn btn-primary" onclick="GroupBuyManager.editItem('${data.id}')">编辑活动</button>
                             <button class="btn btn-secondary" onclick="GroupBuyManager.viewReport('${data.id}')">查看报告</button>
                         </div>
                     </div>
                 `;
             },

             // 生成报告内容
             generateReportContent(data) {
                 return `
                     <div class="report-content">
                         <div class="report-stats">
                             <div class="stat-item">
                                 <div class="stat-value">${data.totalViews}</div>
                                 <div class="stat-label">总浏览量</div>
                             </div>
                             <div class="stat-item">
                                 <div class="stat-value">${data.totalParticipants}</div>
                                 <div class="stat-label">参与人数</div>
                             </div>
                             <div class="stat-item">
                                 <div class="stat-value">${data.successRate}</div>
                                 <div class="stat-label">成功率</div>
                             </div>
                             <div class="stat-item">
                                 <div class="stat-value">${data.revenue}</div>
                                 <div class="stat-label">总收入</div>
                             </div>
                         </div>
                         <div class="report-chart">
                             <div class="chart-placeholder">📊 图表数据将在这里显示</div>
                         </div>
                     </div>
                 `;
             },

             // 生成参与者内容
             generateParticipantsContent(itemId) {
                 return `
                     <div class="participants-content">
                         <div class="participants-list">
                             <div class="participant-item">
                                 <div class="participant-avatar">👤</div>
                                 <div class="participant-info">
                                     <div class="participant-name">张三</div>
                                     <div class="participant-time">2024-01-15 14:30</div>
                                 </div>
                                 <div class="participant-status">已支付</div>
                             </div>
                             <div class="participant-item">
                                 <div class="participant-avatar">👤</div>
                                 <div class="participant-info">
                                     <div class="participant-name">李四</div>
                                     <div class="participant-time">2024-01-15 15:45</div>
                                 </div>
                                 <div class="participant-status">待支付</div>
                             </div>
                         </div>
                     </div>
                 `;
             },

             // 获取状态文本
             getStatusText(status) {
                 const statusMap = {
                     'pending': '待开始',
                     'active': '进行中',
                     'completed': '已完成',
                     'cancelled': '已取消',
                     'paused': '已暂停'
                 };
                 return statusMap[status] || status;
             }
         };
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 开始初始化拼团管理页面...');
            
            try {
                // 基础检查
                if (typeof GroupBuyManager === 'undefined') {
                    throw new Error('GroupBuyManager 对象未定义');
                }
                
                console.log('✅ GroupBuyManager 对象检查通过');
                
                // 核心初始化
                console.log('🔧 开始核心初始化...');
                if (typeof GroupBuyManager.init === 'function') {
                    GroupBuyManager.init();
                    console.log('✅ 核心初始化完成');
                } else {
                    console.error('❌ GroupBuyManager.init 方法不存在');
                }
                
                // 高级搜索初始化
                console.log('🔍 初始化高级搜索...');
                if (typeof GroupBuyManager.initAdvancedSearch === 'function') {
                    GroupBuyManager.initAdvancedSearch();
                    console.log('✅ 高级搜索初始化完成');
                } else {
                    console.warn('⚠️ initAdvancedSearch 方法不存在，跳过');
                }
                
                // 批量操作初始化
                console.log('📦 初始化批量操作...');
                if (typeof GroupBuyManager.initBatchOperations === 'function') {
                    GroupBuyManager.initBatchOperations();
                    console.log('✅ 批量操作初始化完成');
                } else {
                    console.warn('⚠️ initBatchOperations 方法不存在，跳过');
                }
                
                // 排序功能初始化
                console.log('🔄 初始化排序功能...');
                if (typeof GroupBuyManager.initSorting === 'function') {
                    GroupBuyManager.initSorting();
                    console.log('✅ 排序功能初始化完成');
                } else {
                    console.warn('⚠️ initSorting 方法不存在，跳过');
                }
                
                // 实时更新
                console.log('🔄 启动实时更新...');
                if (typeof GroupBuyManager.startRealTimeUpdates === 'function') {
                    GroupBuyManager.startRealTimeUpdates();
                    console.log('✅ 实时更新启动完成');
                } else {
                    console.warn('⚠️ startRealTimeUpdates 方法不存在，跳过');
                }
                
                // 键盘快捷键
                console.log('⌨️ 初始化键盘快捷键...');
                if (typeof GroupBuyManager.initKeyboardShortcuts === 'function') {
                    GroupBuyManager.initKeyboardShortcuts();
                    console.log('✅ 键盘快捷键初始化完成');
                } else {
                    console.warn('⚠️ initKeyboardShortcuts 方法不存在，跳过');
                }
                
                // 响应式布局
                console.log('📱 初始化响应式布局...');
                if (typeof GroupBuyManager.initResponsive === 'function') {
                    GroupBuyManager.initResponsive();
                    console.log('✅ 响应式布局初始化完成');
                } else {
                    console.warn('⚠️ initResponsive 方法不存在，跳过');
                }
                
                console.log('🎉 拼团管理页面初始化完成！');
                
                // 验证按钮功能
                console.log('🔍 验证按钮功能...');
                const buttons = document.querySelectorAll('[data-action]');
                console.log(`找到 ${buttons.length} 个功能按钮:`, Array.from(buttons).map(btn => btn.dataset.action));
                
                // 显示成功消息
                setTimeout(function() {
                    if (typeof GroupBuyManager.showToast === 'function') {
                        GroupBuyManager.showToast('✅ 拼团管理页面加载成功！所有按钮功能已激活', 'success');
                    } else {
                        // 备用提示方式
                        const successMsg = document.createElement('div');
                        successMsg.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            background: #ecfdf5;
                            border: 1px solid #a7f3d0;
                            border-left: 4px solid #10b981;
                            color: #065f46;
                            padding: 16px 20px;
                            border-radius: 8px;
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                            z-index: 10000;
                            max-width: 400px;
                        `;
                        successMsg.innerHTML = `
                            <div style="display: flex; align-items: flex-start; gap: 8px;">
                                <span style="font-size: 16px;">✅</span>
                                <div>
                                    <div style="font-weight: bold; margin-bottom: 4px;">页面初始化成功</div>
                                    <div style="font-size: 12px; opacity: 0.8;">所有按钮功能已激活，可以正常使用</div>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(successMsg);
                        
                        setTimeout(function() {
                            if (successMsg.parentNode) {
                                successMsg.remove();
                            }
                        }, 5000);
                    }
                }, 500);
                
                // 添加全局点击监听器用于调试
                document.addEventListener('click', function(e) {
                    const btn = e.target.closest('[data-action]');
                    if (btn) {
                        console.log('🖱️ 按钮点击:', btn.dataset.action, btn);
                        
                        // 检查对应的处理函数是否存在
                        const action = btn.dataset.action;
                        let handlerExists = false;
                        
                        switch(action) {
                            case 'view':
                            case 'view-detail':
                                handlerExists = typeof GroupBuyManager.viewDetails === 'function';
                                break;
                            case 'edit':
                                handlerExists = typeof GroupBuyManager.editItem === 'function';
                                break;
                            case 'delete':
                                handlerExists = typeof GroupBuyManager.deleteItem === 'function';
                                break;
                            case 'start':
                                handlerExists = typeof GroupBuyManager.startActivity === 'function';
                                break;
                            case 'copy':
                                handlerExists = typeof GroupBuyManager.copyActivity === 'function';
                                break;
                            case 'report':
                            case 'view-report':
                                handlerExists = typeof GroupBuyManager.viewReport === 'function';
                                break;
                        }
                        
                        console.log(`🔧 ${action} 处理函数存在:`, handlerExists);
                    }
                });
                
            } catch (error) {
                console.error('❌ 页面初始化失败:', error);
                console.error('错误堆栈:', error.stack);
                
                // 显示详细错误信息
                setTimeout(function() {
                    const errorMsg = document.createElement('div');
                    errorMsg.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #fef2f2;
                        border: 1px solid #fecaca;
                        border-left: 4px solid #ef4444;
                        color: #991b1b;
                        padding: 16px 20px;
                        border-radius: 8px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                        z-index: 10000;
                        max-width: 400px;
                    `;
                    errorMsg.innerHTML = `
                        <div style="display: flex; align-items: flex-start; gap: 8px;">
                            <span style="font-size: 16px;">❌</span>
                            <div>
                                <div style="font-weight: bold; margin-bottom: 4px;">页面初始化失败</div>
                                <div style="font-size: 12px; opacity: 0.8; margin-bottom: 8px;">错误: ${error.message}</div>
                                <div style="font-size: 12px;">
                                    <button onclick="location.reload()" style="background: #ef4444; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-right: 8px;">🔄 刷新页面</button>
                                    <button onclick="console.log('错误详情:', '${error.stack}')" style="background: #6b7280; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">📋 查看详情</button>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(errorMsg);
                    
                    setTimeout(function() {
                        if (errorMsg.parentNode) {
                            errorMsg.remove();
                        }
                    }, 15000);
                }, 100);
            }
        });
        
        // 添加必要的CSS样式
        const additionalStyles = `
            <style>
                .field-error {
                    color: #ef4444;
                    font-size: 12px;
                    margin-top: 4px;
                }
                
                .form-input.error,
                .form-select.error {
                    border-color: #ef4444;
                }
                
                .toast-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1000;
                }
                
                .toast {
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    margin-bottom: 10px;
                    transform: translateX(100%);
                    transition: transform 0.3s;
                    min-width: 300px;
                }
                
                .toast.show {
                    transform: translateX(0);
                }
                
                .toast-success { border-left: 4px solid #10b981; }
                .toast-error { border-left: 4px solid #ef4444; }
                .toast-warning { border-left: 4px solid #f59e0b; }
                .toast-info { border-left: 4px solid #3b82f6; }
                
                .toast-content {
                    padding: 16px;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                }
                
                .confirm-modal .modal-content {
                    max-width: 400px;
                }
                
                .confirm-body {
                    padding: 20px 0;
                    text-align: center;
                }
                
                .confirm-actions {
                    display: flex;
                    gap: 12px;
                    justify-content: center;
                    padding-top: 20px;
                    border-top: 1px solid #e2e8f0;
                }
                
                .result-count {
                    font-size: 14px;
                    color: #64748b;
                    margin-left: 16px;
                }
                
                .report-content .report-stats {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 16px;
                    margin: 20px 0;
                }
                
                .stat-item {
                    text-align: center;
                    padding: 16px;
                    background: #f8fafc;
                    border-radius: 8px;
                }
                
                .stat-item .stat-label {
                    font-size: 12px;
                    color: #64748b;
                    display: block;
                    margin-bottom: 4px;
                }
                
                .stat-item .stat-value {
                    font-size: 18px;
                    font-weight: 600;
                    color: #1e293b;
                }
                
                .participants-content .participant-list {
                    max-height: 300px;
                    overflow-y: auto;
                }
                
                .participant-item {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    padding: 12px;
                    border-bottom: 1px solid #f1f5f9;
                }
                
                .participant-item:last-child {
                    border-bottom: none;
                }
                
                .participant-item img {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    object-fit: cover;
                }
                
                .participant-info {
                    flex: 1;
                }
                
                .participant-name {
                    font-weight: 500;
                    color: #1e293b;
                    margin-bottom: 4px;
                }
                
                .participant-time {
                    font-size: 12px;
                    color: #64748b;
                }
                
                .participant-status {
                    font-size: 12px;
                    padding: 4px 8px;
                    border-radius: 4px;
                    background: #ecfdf5;
                    color: #065f46;
                }
                
                /* 高级搜索表单样式 */
                .advanced-search-form {
                    padding: 8px 0;
                }
                
                .form-row {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 16px;
                    margin-bottom: 16px;
                }
                
                .form-group {
                    margin-bottom: 0;
                }
                
                .form-group label {
                    display: block;
                    font-weight: 500;
                    color: #374151;
                    margin-bottom: 6px;
                    font-size: 14px;
                }
                
                .form-control {
                    width: 100%;
                    padding: 8px 12px;
                    border: 1px solid #d1d5db;
                    border-radius: 6px;
                    font-size: 14px;
                    transition: border-color 0.15s;
                }
                
                .form-control:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
                
                .price-range {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
                
                .price-range input {
                    flex: 1;
                }
                
                .price-range span {
                    color: #64748b;
                    font-size: 14px;
                }
                
                .form-actions {
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                    padding-top: 16px;
                    border-top: 1px solid #e2e8f0;
                }
                
                /* 详情内容样式 */
                .detail-content {
                    padding: 8px 0;
                }
                
                .detail-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    padding-bottom: 16px;
                    border-bottom: 1px solid #e2e8f0;
                }
                
                .detail-header h3 {
                    margin: 0;
                    color: #1e293b;
                    font-size: 18px;
                }
                
                .detail-info {
                    margin-bottom: 24px;
                }
                
                .info-item {
                    display: flex;
                    margin-bottom: 12px;
                    align-items: center;
                }
                
                .info-item label {
                    font-weight: 500;
                    color: #374151;
                    min-width: 80px;
                    margin-right: 12px;
                }
                
                .info-item .price {
                    color: #ef4444;
                    font-weight: 600;
                    font-size: 16px;
                }
                
                .info-item .original-price {
                    color: #9ca3af;
                    text-decoration: line-through;
                }
                
                .detail-actions {
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                    padding-top: 16px;
                    border-top: 1px solid #e2e8f0;
                }
                
                /* 上下文菜单样式 */
                .context-menu {
                    position: absolute;
                    background: white;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10000;
                    min-width: 160px;
                    padding: 8px 0;
                    display: none;
                }
                
                .context-menu.show {
                    display: block;
                }
                
                .action-menu-item,
                .sort-option {
                    display: block;
                    width: 100%;
                    padding: 12px 16px;
                    border: none;
                    background: none;
                    text-align: left;
                    font-size: 14px;
                    color: #374151;
                    cursor: pointer;
                    transition: background 0.15s;
                }
                
                .action-menu-item:hover,
                .sort-option:hover {
                    background: #f3f4f6;
                }
                
                .action-menu-item i {
                    width: 16px;
                    margin-right: 8px;
                    font-size: 12px;
                }
                
                /* 批量操作工具栏 */
                .bulk-actions-toolbar {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 12px 16px;
                    margin-bottom: 16px;
                    display: none;
                    align-items: center;
                    justify-content: space-between;
                }
                
                .bulk-actions-toolbar.show {
                    display: flex;
                }
                
                .bulk-actions-info {
                    font-size: 14px;
                    color: #374151;
                }
                
                .bulk-actions-row {
                    display: flex;
                    gap: 8px;
                }
                
                .bulk-actions-row .btn {
                    font-size: 12px;
                    padding: 6px 12px;
                }
                
                /* 动画效果增强 */
                .group-buy-item {
                    transition: all 0.3s ease;
                }
                
                .group-buy-item:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                }
                
                .btn {
                    transition: all 0.15s ease;
                }
                
                .btn:hover {
                    transform: translateY(-1px);
                }
                
                /* 加载状态 */
                .loading {
                    display: inline-block;
                    width: 16px;
                    height: 16px;
                    border: 2px solid #f3f3f3;
                    border-top: 2px solid #3b82f6;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
                
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                
                /* 状态指示器 */
                .status-indicator {
                    display: inline-block;
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-right: 6px;
                }
                
                .status-indicator.active {
                    background: #10b981;
                    animation: pulse 2s infinite;
                }
                
                .status-indicator.pending {
                    background: #f59e0b;
                }
                
                .status-indicator.completed {
                    background: #3b82f6;
                }
                
                .status-indicator.cancelled {
                    background: #ef4444;
                }
                
                                 @keyframes pulse {
                     0% { opacity: 1; }
                     50% { opacity: 0.5; }
                     100% { opacity: 1; }
                 }
                 
                 /* 响应式优化 */
                 @media (max-width: 768px) {
                     .context-menu {
                         min-width: 140px;
                     }
                     
                     .action-menu-item,
                     .sort-option {
                         padding: 10px 12px;
                         font-size: 14px;
                     }
                     
                     .bulk-actions-row {
                         flex-direction: column;
                     }
                     
                     .bulk-actions-row .btn {
                         width: 100%;
                         justify-content: center;
                     }

                     .toast {
                         min-width: 280px;
                         margin: 0 12px 12px 12px;
                     }

                     .modal-content {
                         width: 95%;
                         margin: 20px;
                     }

                     .modal-header,
                     .modal-body,
                     .confirm-actions {
                         padding-left: 16px;
                         padding-right: 16px;
                     }

                     .confirm-actions {
                         flex-direction: column;
                     }

                     .confirm-actions .btn {
                         width: 100%;
                     }

                     .form-row {
                         grid-template-columns: 1fr;
                     }

                     .report-stats {
                         grid-template-columns: 1fr;
                     }

                     .detail-actions {
                         flex-direction: column;
                     }

                     .detail-actions .btn {
                         width: 100%;
                     }
                     
                     .group-buy-page {
                         padding: 16px;
                     }
                     
                     .stats-grid {
                         grid-template-columns: 1fr;
                         gap: 16px;
                     }
                     
                     .header-content {
                         flex-direction: column;
                         align-items: flex-start;
                         gap: 16px;
                     }
                     
                     .header-actions {
                         width: 100%;
                         justify-content: flex-start;
                     }
                     
                     .content-controls {
                         flex-direction: column;
                         align-items: stretch;
                         gap: 12px;
                     }
                     
                     .search-input {
                         width: 100%;
                     }
                 }

                 /* 触摸设备优化 */
                 @media (hover: none) and (pointer: coarse) {
                     .btn:hover {
                         transform: none;
                     }
                     
                     .group-buy-item:hover {
                         transform: none;
                         box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                     }
                 }

                 /* 打印样式 */
                 @media print {
                     .header-actions,
                     .bulk-actions-toolbar,
                     .context-menu,
                     .toast {
                         display: none !important;
                     }
                     
                     .group-buy-item {
                         break-inside: avoid;
                     }
                     
                     body {
                         background: white !important;
                     }
                     
                     .group-buy-page {
                         padding: 0;
                     }
                 }

                 /* 高对比度模式支持 */
                 @media (prefers-contrast: high) {
                     .btn {
                         border: 2px solid currentColor;
                     }
                     
                     .group-buy-item {
                         border: 2px solid #000;
                     }
                     
                     .status-badge {
                         border: 1px solid currentColor;
                     }
                 }

                 /* 减少动画模式支持 */
                 @media (prefers-reduced-motion: reduce) {
                     * {
                         animation-duration: 0.01ms !important;
                         animation-iteration-count: 1 !important;
                         transition-duration: 0.01ms !important;
                         scroll-behavior: auto !important;
                     }
                 }

                 /* 深色模式支持 */
                 @media (prefers-color-scheme: dark) {
                     .group-buy-page {
                         background: #1a202c;
                         color: #e2e8f0;
                     }
                     
                     .page-header,
                     .main-content,
                     .stat-card {
                         background: #2d3748;
                         border-color: #4a5568;
                     }
                     
                     .modal-content,
                     .context-menu {
                         background: #2d3748;
                         border-color: #4a5568;
                         color: #e2e8f0;
                     }
                     
                     .form-control {
                         background: #2d3748;
                         border-color: #4a5568;
                         color: #e2e8f0;
                     }
                     
                     .btn-secondary {
                         background: #4a5568;
                         color: #e2e8f0;
                     }
                 }
                 
                 /* 键盘导航增强 */
                 .btn:focus-visible,
                 .form-control:focus-visible {
                     outline: 2px solid #3b82f6;
                     outline-offset: 2px;
                 }
                 
                 .group-buy-item:focus-within {
                     box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
                 }
                 
                 /* 工具提示样式 */
                 .tooltip {
                     position: absolute;
                     background: #1e293b;
                     color: white;
                     padding: 8px 12px;
                     border-radius: 6px;
                     font-size: 12px;
                     white-space: nowrap;
                     z-index: 10001;
                     opacity: 0;
                     visibility: hidden;
                     transition: all 0.2s;
                     transform: translateY(-4px);
                 }
                 
                 .tooltip.show {
                     opacity: 1;
                     visibility: visible;
                     transform: translateY(0);
                 }
                 
                 .tooltip::after {
                     content: '';
                     position: absolute;
                     top: 100%;
                     left: 50%;
                     transform: translateX(-50%);
                     border: 4px solid transparent;
                     border-top-color: #1e293b;
                 }
                
                .participant-info .name {
                    font-weight: 500;
                    color: #1e293b;
                }
                
                .participant-info .time {
                    font-size: 12px;
                    color: #64748b;
                }
                
                .participant-item .status {
                    margin-left: auto;
                    padding: 2px 8px;
                    background: #10b981;
                    color: white;
                    border-radius: 12px;
                    font-size: 12px;
                }

                /* 上下文菜单样式 */
                .context-menu {
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    min-width: 160px;
                    animation: fadeInDown 0.15s ease-out;
                }

                @keyframes fadeInDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .action-menu-item,
                .sort-option {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 12px 16px;
                    cursor: pointer;
                    border-bottom: 1px solid #f1f5f9;
                    transition: background-color 0.15s;
                }

                .action-menu-item:last-child,
                .sort-option:last-child {
                    border-bottom: none;
                }

                .action-menu-item:hover,
                .sort-option:hover {
                    background-color: #f8fafc;
                }

                .action-menu-item.danger:hover {
                    background-color: #fef2f2;
                    color: #dc2626;
                }

                .sort-option.active {
                    background-color: #e0f2fe;
                    color: #0369a1;
                    font-weight: 500;
                }

                .sort-option.active .fas {
                    color: #0369a1;
                }

                .sort-option span {
                    flex: 1;
                }

                .sort-option .fas {
                    font-size: 12px;
                    color: #0369a1;
                }

                /* 批量操作界面样式增强 */
                .bulk-actions {
                    display: none;
                    padding: 16px;
                    background: #f8fafc;
                    border-top: 1px solid #e2e8f0;
                    border-bottom: 1px solid #e2e8f0;
                    margin-bottom: 16px;
                    border-radius: 8px;
                    animation: slideDown 0.3s ease-out;
                }

                .bulk-actions.show {
                    display: block;
                }

                @keyframes slideDown {
                    from {
                        opacity: 0;
                        max-height: 0;
                        padding-top: 0;
                        padding-bottom: 0;
                    }
                    to {
                        opacity: 1;
                        max-height: 100px;
                        padding-top: 16px;
                        padding-bottom: 16px;
                    }
                }

                .bulk-info {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                    margin-bottom: 12px;
                }

                .bulk-info .selected-count {
                    font-weight: 600;
                    color: #0369a1;
                }

                .bulk-actions-row {
                    display: flex;
                    gap: 8px;
                    flex-wrap: wrap;
                }

                .bulk-actions-row .btn {
                    font-size: 14px;
                    padding: 8px 16px;
                }

                /* 按钮悬停效果增强 */
                .btn-icon:hover {
                    background-color: #f1f5f9;
                    transform: scale(1.05);
                }

                .btn-icon:active {
                    transform: scale(0.95);
                }

                /* 选择框样式增强 */
                .item-select {
                    transform: scale(1.2);
                    accent-color: #0369a1;
                }

                .item-select:hover {
                    transform: scale(1.3);
                }

                /* 吐司消息样式 */
                .toast-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    pointer-events: none;
                }

                .toast {
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                    margin-bottom: 12px;
                    padding: 16px 20px;
                    border-left: 4px solid #3b82f6;
                    min-width: 300px;
                    transform: translateX(400px);
                    opacity: 0;
                    transition: all 0.3s ease-out;
                    pointer-events: auto;
                }

                .toast.show {
                    transform: translateX(0);
                    opacity: 1;
                }

                .toast-success {
                    border-left-color: #10b981;
                }

                .toast-error {
                    border-left-color: #ef4444;
                }

                .toast-warning {
                    border-left-color: #f59e0b;
                }

                .toast-info {
                    border-left-color: #3b82f6;
                }

                .toast-content {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    font-size: 14px;
                    font-weight: 500;
                }

                .toast-success .fas {
                    color: #10b981;
                }

                .toast-error .fas {
                    color: #ef4444;
                }

                .toast-warning .fas {
                    color: #f59e0b;
                }

                .toast-info .fas {
                    color: #3b82f6;
                }

                /* 模态框样式增强 */
                .modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 9999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease-out;
                }

                .modal-overlay.show {
                    opacity: 1;
                    visibility: visible;
                }

                .modal-content {
                    background: white;
                    border-radius: 12px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    transform: scale(0.9) translateY(-20px);
                    transition: transform 0.3s ease-out;
                }

                .modal-overlay.show .modal-content {
                    transform: scale(1) translateY(0);
                }

                .modal-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 20px 24px 16px;
                    border-bottom: 1px solid #e2e8f0;
                }

                .modal-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: #1e293b;
                    margin: 0;
                }

                .modal-close {
                    background: none;
                    border: none;
                    font-size: 24px;
                    color: #64748b;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 4px;
                    transition: all 0.15s;
                }

                .modal-close:hover {
                    background: #f1f5f9;
                    color: #1e293b;
                }

                .modal-body {
                    padding: 20px 24px;
                }

                /* 确认对话框样式 */
                .confirm-modal .modal-content {
                    max-width: 400px;
                }

                .confirm-body {
                    padding: 20px 24px;
                    text-align: center;
                }

                .confirm-body p {
                    margin: 0;
                    color: #475569;
                    line-height: 1.6;
                }

                .confirm-actions {
                    padding: 16px 24px;
                    display: flex;
                    gap: 12px;
                    justify-content: center;
                    border-top: 1px solid #e2e8f0;
                }

                .confirm-actions .btn {
                    min-width: 80px;
                }

                /* 响应式优化 */
                @media (max-width: 768px) {
                    .context-menu {
                        min-width: 140px;
                    }
                    
                    .action-menu-item,
                    .sort-option {
                        padding: 10px 12px;
                        font-size: 14px;
                    }
                    
                    .bulk-actions-row {
                        flex-direction: column;
                    }
                    
                    .bulk-actions-row .btn {
                        width: 100%;
                        justify-content: center;
                    }

                    .toast {
                        min-width: 280px;
                        margin: 0 12px 12px 12px;
                    }

                    .modal-content {
                        width: 95%;
                        margin: 20px;
                    }

                    .modal-header,
                    .modal-body,
                    .confirm-actions {
                        padding-left: 16px;
                        padding-right: 16px;
                    }

                    .confirm-actions {
                        flex-direction: column;
                    }

                    .confirm-actions .btn {
                        width: 100%;
                    }
                }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', additionalStyles);
    </script>
</body>
</html> 