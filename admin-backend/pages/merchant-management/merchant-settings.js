        function showSection(sectionId, navItem) {
            // 隐藏所有设置区域
            const sections = document.querySelectorAll('.settings-section');
            sections.forEach(section => section.classList.remove('active'));
            
            // 显示选中的设置区域
            document.getElementById(sectionId).classList.add('active');
            
            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            navItem.classList.add('active');
            
            // 更新标题和描述
            const titles = {
                commission: '佣金配置',
                settlement: '结算设置',
                business: '业务规则',
                permissions: '权限管理',
                notifications: '通知设置',
                api: 'API设置'
            };
            
            const descriptions = {
                commission: '配置平台佣金规则和费率标准',
                settlement: '设置商家结算周期和费用规则',
                business: '配置商家入驻和经营规则',
                permissions: '管理商家权限和功能开关',
                notifications: '配置系统通知和消息推送',
                api: '管理API接口和访问控制'
            };
            
            document.getElementById('sectionTitle').textContent = titles[sectionId];
            document.getElementById('sectionDesc').textContent = descriptions[sectionId];
        }
        
        function saveSettings() {
            alert('设置已保存');
        }
        
        function resetSettings() {
            if (confirm('确定要重置所有设置吗？此操作不可恢复！')) {
                alert('设置已重置');
            }
        }
        
        function saveAllSettings() {
            alert('所有设置已保存');
        }
        
        function showCategorySelector() {
            alert('打开类目选择器');
        }
