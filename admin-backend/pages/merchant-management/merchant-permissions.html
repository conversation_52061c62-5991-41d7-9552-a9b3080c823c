<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户权限管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="merchant-permissions.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="permissions-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div>
                    <h1 class="page-title">商户权限管理</h1>
                    <p class="page-subtitle">统一管理商户权限，支持批量操作和审批流程</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="exportPermissions()">
                        <i class="fas fa-download"></i>
                        导出配置
                    </button>
                    <button class="btn btn-secondary" onclick="showTemplateModal()">
                        <i class="fas fa-copy"></i>
                        权限模板
                    </button>
                    <button class="btn btn-warning" onclick="showApprovalModal()">
                        <i class="fas fa-clipboard-check"></i>
                        审批中心
                    </button>
                    <button class="btn btn-primary" onclick="savePermissions()">
                        <i class="fas fa-save"></i>
                        保存更改
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要布局 -->
        <div class="main-layout">
            <!-- 左侧商户列表 -->
            <div class="sidebar-panel">
                <div class="panel-header">
                    <h2 class="panel-title">商户列表</h2>
                    <span class="merchant-count" id="merchantCount">0</span>
                </div>

                <!-- 搜索区域 -->
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索商户名称或ID..." 
                           id="merchantSearch" oninput="searchMerchants(this.value)">
                </div>

                <!-- 筛选标签 -->
                <div class="filter-tabs">
                    <div class="filter-tab active" data-filter="all">全部</div>
                    <div class="filter-tab" data-filter="active">正常营业</div>
                    <div class="filter-tab" data-filter="pending">待审核</div>
                    <div class="filter-tab" data-filter="premium">高级商户</div>
                </div>

                <!-- 商户列表 -->
                <div class="merchants-list" id="merchantsList">
                    <!-- 通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 右侧权限内容 -->
            <div class="permissions-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div class="selected-merchant" id="selectedMerchantInfo" style="display: none;">
                        <div class="merchant-avatar" id="merchantAvatar">商</div>
                        <div class="merchant-details">
                            <h3 id="merchantName">未选择商户</h3>
                            <p id="merchantDesc">请从左侧列表选择一个商户来管理权限</p>
                        </div>
                    </div>

                    <div class="merchant-stats" id="merchantStats" style="display: none;">
                        <span>权限总数: <strong id="totalPermissions">0</strong></span>
                        <span>已启用: <strong id="enabledPermissions">0</strong></span>
                        <span>待审批: <strong id="pendingPermissions">0</strong></span>
                    </div>
                </div>

                <!-- 批量操作栏 -->
                <div class="bulk-toolbar" id="bulkActions" style="display: none;">
                    <div class="bulk-info">
                        <i class="fas fa-check-circle"></i>
                        已选择 <span id="selectedCount">0</span> 个商户
                    </div>
                    <div class="bulk-actions">
                        <button class="btn btn-sm btn-success" onclick="bulkEnablePermissions()">
                            <i class="fas fa-check"></i>
                            批量启用
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="bulkDisablePermissions()">
                            <i class="fas fa-times"></i>
                            批量禁用
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="bulkApplyTemplate()">
                            <i class="fas fa-copy"></i>
                            应用模板
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="clearSelection()">
                            <i class="fas fa-times-circle"></i>
                            清除选择
                        </button>
                    </div>
                </div>

                <!-- 权限选项卡 -->
                <div class="permission-tabs">
                    <button class="tab-button active" data-tab="matrix">权限矩阵</button>
                    <button class="tab-button" data-tab="templates">权限模板</button>
                    <button class="tab-button" data-tab="logs">操作日志</button>
                </div>

                <!-- 权限矩阵选项卡 -->
                <div class="tab-content active" id="matrixTab">
                    <div id="permissionsSections">
                        <div class="empty-state" id="emptyState">
                            <i class="fas fa-users-cog"></i>
                            <h3>请选择商户</h3>
                            <p>从左侧列表选择一个商户开始管理权限设置</p>
                            <button class="btn btn-primary" onclick="simulateSelectMerchant()" style="margin-top: 16px;">
                                <i class="fas fa-play"></i>
                                演示功能
                            </button>
                        </div>
                        
                        <!-- 权限分组示例 -->
                        <div class="permission-section" style="display: none;" id="permissionSections">
                            <div class="section-header">
                                <div class="section-title">
                                    <div class="section-icon products">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <span>商品管理</span>
                                </div>
                                <div class="section-toggle">
                                    <span>全选</span>
                                    <div class="toggle-switch" onclick="toggleSection('products')"></div>
                                </div>
                            </div>
                            <div class="permissions-grid">
                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-icon products">
                                            <i class="fas fa-eye"></i>
                                        </div>
                                        <h4 class="permission-name">查看商品</h4>
                                    </div>
                                    <p class="permission-description">浏览商品列表和详情</p>
                                    <ul class="permission-items">
                                        <li class="permission-item required">
                                            <span class="permission-label">商品列表</span>
                                            <input type="checkbox" class="permission-checkbox" checked disabled>
                                        </li>
                                        <li class="permission-item">
                                            <span class="permission-label">商品详情</span>
                                            <input type="checkbox" class="permission-checkbox" checked>
                                        </li>
                                    </ul>
                                </div>
                                
                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-icon products">
                                            <i class="fas fa-plus"></i>
                                        </div>
                                        <h4 class="permission-name">添加商品</h4>
                                    </div>
                                    <p class="permission-description">创建新的商品信息</p>
                                    <ul class="permission-items">
                                        <li class="permission-item">
                                            <span class="permission-label">创建商品</span>
                                            <input type="checkbox" class="permission-checkbox">
                                        </li>
                                        <li class="permission-item">
                                            <span class="permission-label">批量导入</span>
                                            <input type="checkbox" class="permission-checkbox">
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 订单管理权限分组 -->
                        <div class="permission-section" style="display: none;" id="orderPermissions">
                            <div class="section-header">
                                <div class="section-title">
                                    <div class="section-icon orders">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <span>订单管理</span>
                                </div>
                                <div class="section-toggle">
                                    <span>全选</span>
                                    <div class="toggle-switch" onclick="toggleSection('orders')"></div>
                                </div>
                            </div>
                            <div class="permissions-grid">
                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-icon orders">
                                            <i class="fas fa-list"></i>
                                        </div>
                                        <h4 class="permission-name">查看订单</h4>
                                    </div>
                                    <p class="permission-description">浏览订单列表和详情</p>
                                    <ul class="permission-items">
                                        <li class="permission-item required">
                                            <span class="permission-label">订单列表</span>
                                            <input type="checkbox" class="permission-checkbox" checked disabled>
                                        </li>
                                        <li class="permission-item">
                                            <span class="permission-label">订单详情</span>
                                            <input type="checkbox" class="permission-checkbox" checked>
                                        </li>
                                    </ul>
                                </div>
                                
                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-icon orders">
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <h4 class="permission-name">处理订单</h4>
                                    </div>
                                    <p class="permission-description">修改订单状态和信息</p>
                                    <ul class="permission-items">
                                        <li class="permission-item">
                                            <span class="permission-label">状态更新</span>
                                            <input type="checkbox" class="permission-checkbox">
                                        </li>
                                        <li class="permission-item">
                                            <span class="permission-label">发货处理</span>
                                            <input type="checkbox" class="permission-checkbox">
                                        </li>
                                    </ul>
                                </div>
                                
                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-icon orders">
                                            <i class="fas fa-undo"></i>
                                        </div>
                                        <h4 class="permission-name">退款处理</h4>
                                        <div class="approval-badge pending">审</div>
                                    </div>
                                    <p class="permission-description">处理退款和售后</p>
                                    <ul class="permission-items">
                                        <li class="permission-item">
                                            <span class="permission-label">退款审核</span>
                                            <input type="checkbox" class="permission-checkbox">
                                        </li>
                                        <li class="permission-item">
                                            <span class="permission-label">退货处理</span>
                                            <input type="checkbox" class="permission-checkbox">
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 客户管理权限分组 -->
                        <div class="permission-section" style="display: none;" id="customerPermissions">
                            <div class="section-header">
                                <div class="section-title">
                                    <div class="section-icon customers" style="background: #8b5cf6;">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <span>客户管理</span>
                                </div>
                                <div class="section-toggle">
                                    <span>全选</span>
                                    <div class="toggle-switch" onclick="toggleSection('customers')"></div>
                                </div>
                            </div>
                            <div class="permissions-grid">
                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-icon customers" style="background: #8b5cf6;">
                                            <i class="fas fa-address-book"></i>
                                        </div>
                                        <h4 class="permission-name">客户信息</h4>
                                    </div>
                                    <p class="permission-description">查看和管理客户信息</p>
                                    <ul class="permission-items">
                                        <li class="permission-item">
                                            <span class="permission-label">客户列表</span>
                                            <input type="checkbox" class="permission-checkbox">
                                        </li>
                                        <li class="permission-item">
                                            <span class="permission-label">客户详情</span>
                                            <input type="checkbox" class="permission-checkbox">
                                        </li>
                                    </ul>
                                </div>
                                
                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-icon customers" style="background: #8b5cf6;">
                                            <i class="fas fa-comments"></i>
                                        </div>
                                        <h4 class="permission-name">客服支持</h4>
                                    </div>
                                    <p class="permission-description">客服聊天和支持功能</p>
                                    <ul class="permission-items">
                                        <li class="permission-item">
                                            <span class="permission-label">在线客服</span>
                                            <input type="checkbox" class="permission-checkbox">
                                        </li>
                                        <li class="permission-item">
                                            <span class="permission-label">工单处理</span>
                                            <input type="checkbox" class="permission-checkbox">
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 权限模板选项卡 -->
                <div class="tab-content" id="templatesTab">
                    <div class="template-list">
                        <div class="template-header">
                            <h3>权限模板管理</h3>
                            <p>使用预设模板快速配置商户权限</p>
                        </div>
                        
                        <div class="templates-grid">
                            <div class="template-card" data-template="basic">
                                <div class="template-icon">
                                    <i class="fas fa-seedling"></i>
                                </div>
                                <div class="template-info">
                                    <h4>基础商户模板</h4>
                                    <p>适用于新入驻的小型商户，包含基本权限</p>
                                    <div class="template-permissions">
                                        <span class="permission-tag">查看商品</span>
                                        <span class="permission-tag">添加商品</span>
                                        <span class="permission-tag">查看订单</span>
                                    </div>
                                </div>
                                <div class="template-actions">
                                    <button class="btn btn-sm btn-secondary" onclick="previewTemplate('basic')">
                                        <i class="fas fa-eye"></i>
                                        预览
                                    </button>
                                    <button class="btn btn-sm btn-primary" onclick="applyTemplateToSelected('basic')">
                                        <i class="fas fa-check"></i>
                                        应用
                                    </button>
                                </div>
                            </div>

                            <div class="template-card" data-template="standard">
                                <div class="template-icon">
                                    <i class="fas fa-store"></i>
                                </div>
                                <div class="template-info">
                                    <h4>标准商户模板</h4>
                                    <p>适用于运营稳定的中型商户</p>
                                    <div class="template-permissions">
                                        <span class="permission-tag">商品管理</span>
                                        <span class="permission-tag">订单处理</span>
                                        <span class="permission-tag">客户管理</span>
                                        <span class="permission-tag">基础营销</span>
                                    </div>
                                </div>
                                <div class="template-actions">
                                    <button class="btn btn-sm btn-secondary" onclick="previewTemplate('standard')">
                                        <i class="fas fa-eye"></i>
                                        预览
                                    </button>
                                    <button class="btn btn-sm btn-primary" onclick="applyTemplateToSelected('standard')">
                                        <i class="fas fa-check"></i>
                                        应用
                                    </button>
                                </div>
                            </div>

                            <div class="template-card" data-template="premium">
                                <div class="template-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="template-info">
                                    <h4>高级商户模板</h4>
                                    <p>适用于大型企业商户，包含高级权限</p>
                                    <div class="template-permissions">
                                        <span class="permission-tag">全商品权限</span>
                                        <span class="permission-tag">高级营销</span>
                                        <span class="permission-tag">数据分析</span>
                                        <span class="permission-tag">财务管理</span>
                                    </div>
                                </div>
                                <div class="template-actions">
                                    <button class="btn btn-sm btn-secondary" onclick="previewTemplate('premium')">
                                        <i class="fas fa-eye"></i>
                                        预览
                                    </button>
                                    <button class="btn btn-sm btn-primary" onclick="applyTemplateToSelected('premium')">
                                        <i class="fas fa-check"></i>
                                        应用
                                    </button>
                                </div>
                            </div>

                            <div class="template-card" data-template="enterprise">
                                <div class="template-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="template-info">
                                    <h4>企业定制模板</h4>
                                    <p>为大型企业客户定制的完整权限包</p>
                                    <div class="template-permissions">
                                        <span class="permission-tag">全部权限</span>
                                        <span class="permission-tag">系统管理</span>
                                        <span class="permission-tag">API访问</span>
                                        <span class="permission-tag">高级分析</span>
                                    </div>
                                </div>
                                <div class="template-actions">
                                    <button class="btn btn-sm btn-secondary" onclick="previewTemplate('enterprise')">
                                        <i class="fas fa-eye"></i>
                                        预览
                                    </button>
                                    <button class="btn btn-sm btn-primary" onclick="applyTemplateToSelected('enterprise')">
                                        <i class="fas fa-check"></i>
                                        应用
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作日志选项卡 -->
                <div class="tab-content" id="logsTab">
                    <div class="logs-list">
                        <div class="log-item">
                            <div class="log-icon update">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="log-content">
                                <div class="log-title">权限更新</div>
                                <div class="log-details">管理员为"星辰数码专营店"启用了"高级营销"权限</div>
                            </div>
                            <div class="log-time">2分钟前</div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-icon create">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="log-content">
                                <div class="log-title">模板应用</div>
                                <div class="log-details">为"时尚生活馆"应用了"标准商户模板"</div>
                            </div>
                            <div class="log-time">5分钟前</div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-icon update">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="log-content">
                                <div class="log-title">权限审批</div>
                                <div class="log-details">通过了"优品食材供应"的"退款处理"权限申请</div>
                            </div>
                            <div class="log-time">10分钟前</div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-icon delete">
                                <i class="fas fa-times"></i>
                            </div>
                            <div class="log-content">
                                <div class="log-title">权限撤销</div>
                                <div class="log-details">撤销了"测试商户"的"批量操作"权限</div>
                            </div>
                            <div class="log-time">1小时前</div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-icon create">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="log-content">
                                <div class="log-title">商户入驻</div>
                                <div class="log-details">新商户"科技数码港"已入驻，应用基础模板</div>
                            </div>
                            <div class="log-time">2小时前</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 变更提示 -->
        <div class="changes-indicator" id="changesIndicator">
            <i class="fas fa-exclamation-triangle"></i>
            <span>您有未保存的更改</span>
            <button class="btn btn-sm btn-primary" onclick="savePermissions()">
                立即保存
            </button>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal-content" id="modalContent">
            <!-- 模态框内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notificationContainer"></div>

    <!-- JavaScript -->
    <script src="permission-manager.js"></script>
</body>
<script src="merchant-permissions.js"></script>
</html>
