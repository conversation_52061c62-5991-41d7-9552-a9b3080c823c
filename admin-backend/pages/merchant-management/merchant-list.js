        // 模拟商户数据
        const merchantsData = [
            {
                id: 'M001234',
                name: '华为旗舰专卖店',
                type: 'flagship',
                level: 'vip',
                status: 'active',
                contact: '张经理',
                phone: '138****8888',
                registerDate: '2024-01-15',
                productCount: 156,
                monthlyRevenue: 125600,
                avatar: '华'
            },
            {
                id: 'M001235',
                name: '小米生活馆',
                type: 'enterprise',
                level: 'premium',
                status: 'active',
                contact: '李先生',
                phone: '139****9999',
                registerDate: '2024-01-10',
                productCount: 89,
                monthlyRevenue: 89400,
                avatar: '小'
            },
            {
                id: 'M001236',
                name: '时尚潮牌屋',
                type: 'individual',
                level: 'standard',
                status: 'pending',
                contact: '王女士',
                phone: '137****7777',
                registerDate: '2024-01-20',
                productCount: 42,
                monthlyRevenue: 36800,
                avatar: '时'
            },
            {
                id: 'M001237',
                name: '美食天地',
                type: 'enterprise',
                level: 'standard',
                status: 'active',
                contact: '陈老板',
                phone: '136****6666',
                registerDate: '2024-01-08',
                productCount: 234,
                monthlyRevenue: 67200,
                avatar: '美'
            },
            {
                id: 'M001238',
                name: '数码科技',
                type: 'enterprise',
                level: 'basic',
                status: 'frozen',
                contact: '刘经理',
                phone: '135****5555',
                registerDate: '2024-01-05',
                productCount: 67,
                monthlyRevenue: 28900,
                avatar: '数'
            }
        ];

        let currentMerchants = [...merchantsData];
        let selectedMerchants = new Set();

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderMerchants();
            setupEventListeners();
        });

        // 渲染商户列表
        function renderMerchants() {
            const tbody = document.getElementById('merchantsTableBody');
            tbody.innerHTML = '';

            currentMerchants.forEach(merchant => {
                const row = createMerchantRow(merchant);
                tbody.appendChild(row);
            });

            updateSelectedCount();
        }

        // 创建商户行
        function createMerchantRow(merchant) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="checkbox-container">
                        <input type="checkbox" class="checkbox merchant-checkbox" value="${merchant.id}" onchange="toggleMerchantSelection(this, '${merchant.id}')">
                    </div>
                </td>
                <td>
                    <div class="merchant-info">
                        <div class="merchant-avatar">${merchant.avatar}</div>
                        <div class="merchant-details">
                            <h4 class="merchant-name">${merchant.name}</h4>
                            <div class="merchant-meta">ID: ${merchant.id} | ${merchant.contact}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="merchant-type ${merchant.type}">
                        <i class="fas ${getTypeIcon(merchant.type)}"></i>
                        ${getTypeText(merchant.type)}
                    </span>
                </td>
                <td>
                    <span class="level-badge ${merchant.level}">${getLevelText(merchant.level)}</span>
                </td>
                <td>
                    <span class="status-badge ${merchant.status}">
                        <i class="fas ${getStatusIcon(merchant.status)}"></i>
                        ${getStatusText(merchant.status)}
                    </span>
                </td>
                <td>${merchant.registerDate}</td>
                <td>${merchant.productCount}</td>
                <td>¥${merchant.monthlyRevenue.toLocaleString()}</td>
                <td>
                    <div class="actions-menu">
                        <button class="actions-btn" onclick="toggleActionsMenu(this)">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div class="actions-dropdown">
                            <a href="#" class="dropdown-item" onclick="viewMerchant('${merchant.id}')">
                                <i class="fas fa-eye"></i> 查看详情
                            </a>
                            <a href="#" class="dropdown-item" onclick="editMerchant('${merchant.id}')">
                                <i class="fas fa-edit"></i> 编辑信息
                            </a>
                            <a href="#" class="dropdown-item" onclick="manageMerchantPermissions('${merchant.id}')">
                                <i class="fas fa-key"></i> 权限管理
                            </a>
                            <a href="#" class="dropdown-item" onclick="viewMerchantStats('${merchant.id}')">
                                <i class="fas fa-chart-bar"></i> 数据统计
                            </a>
                            ${getStatusActions(merchant)}
                        </div>
                    </div>
                </td>
            `;
            return row;
        }

        // 获取类型图标
        function getTypeIcon(type) {
            const icons = {
                enterprise: 'fa-building',
                individual: 'fa-user',
                flagship: 'fa-crown'
            };
            return icons[type] || 'fa-store';
        }

        // 获取类型文本
        function getTypeText(type) {
            const texts = {
                enterprise: '企业商户',
                individual: '个人商户',
                flagship: '品牌旗舰店'
            };
            return texts[type] || '未知类型';
        }

        // 获取等级文本
        function getLevelText(level) {
            const texts = {
                basic: '基础商户',
                standard: '标准商户',
                premium: '高级商户',
                vip: 'VIP商户'
            };
            return texts[level] || '未知等级';
        }

        // 获取状态图标
        function getStatusIcon(status) {
            const icons = {
                active: 'fa-check-circle',
                pending: 'fa-clock',
                frozen: 'fa-ban',
                closed: 'fa-times-circle'
            };
            return icons[status] || 'fa-question-circle';
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                active: '正常营业',
                pending: '待审核',
                frozen: '已冻结',
                closed: '已关闭'
            };
            return texts[status] || '未知状态';
        }

        // 获取状态相关操作
        function getStatusActions(merchant) {
            let actions = '';
            
            if (merchant.status === 'pending') {
                actions += `
                    <a href="#" class="dropdown-item" onclick="approveMerchant('${merchant.id}')">
                        <i class="fas fa-check"></i> 审核通过
                    </a>
                    <a href="#" class="dropdown-item danger" onclick="rejectMerchant('${merchant.id}')">
                        <i class="fas fa-times"></i> 审核拒绝
                    </a>
                `;
            } else if (merchant.status === 'active') {
                actions += `
                    <a href="#" class="dropdown-item danger" onclick="freezeMerchant('${merchant.id}')">
                        <i class="fas fa-ban"></i> 冻结商户
                    </a>
                `;
            } else if (merchant.status === 'frozen') {
                actions += `
                    <a href="#" class="dropdown-item" onclick="unfreezemerchant('${merchant.id}')">
                        <i class="fas fa-unlock"></i> 解除冻结
                    </a>
                `;
            }
            
            actions += `
                <a href="#" class="dropdown-item danger" onclick="deleteMerchant('${merchant.id}')">
                    <i class="fas fa-trash"></i> 删除商户
                </a>
            `;
            
            return actions;
        }

        // 切换操作菜单
        function toggleActionsMenu(btn) {
            const dropdown = btn.nextElementSibling;
            
            // 关闭其他菜单
            document.querySelectorAll('.actions-dropdown').forEach(menu => {
                if (menu !== dropdown) {
                    menu.classList.remove('show');
                }
            });
            
            dropdown.classList.toggle('show');
        }

        // 商户选择相关函数
        function toggleSelectAll(checked) {
            const checkboxes = document.querySelectorAll('.merchant-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = checked;
                if (checked) {
                    selectedMerchants.add(checkbox.value);
                } else {
                    selectedMerchants.delete(checkbox.value);
                }
            });
            updateSelectedCount();
        }

        function toggleMerchantSelection(checkbox, merchantId) {
            if (checkbox.checked) {
                selectedMerchants.add(merchantId);
            } else {
                selectedMerchants.delete(merchantId);
            }
            updateSelectedCount();
            
            // 更新全选状态
            const allCheckboxes = document.querySelectorAll('.merchant-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.merchant-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAll');
            selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
        }

        function updateSelectedCount() {
            const count = selectedMerchants.size;
            document.getElementById('selectedCount').textContent = count;
            const bulkActions = document.getElementById('bulkActions');
            
            if (count > 0) {
                bulkActions.classList.remove('hidden');
            } else {
                bulkActions.classList.add('hidden');
            }
        }

        // 筛选函数
        function filterByStatus(status) {
            applyFilters();
        }

        function filterByType(type) {
            applyFilters();
        }

        function filterByLevel(level) {
            applyFilters();
        }

        function filterByTime(time) {
            applyFilters();
        }

        function searchMerchants(query) {
            applyFilters();
        }

        function applyFilters() {
            const statusFilter = document.querySelector('.filter-select').value;
            const typeFilter = document.querySelectorAll('.filter-select')[1].value;
            const levelFilter = document.querySelectorAll('.filter-select')[2].value;
            const timeFilter = document.querySelectorAll('.filter-select')[3].value;
            const searchQuery = document.querySelector('.search-input').value.toLowerCase();

            currentMerchants = merchantsData.filter(merchant => {
                const matchesStatus = !statusFilter || merchant.status === statusFilter;
                const matchesType = !typeFilter || merchant.type === typeFilter;
                const matchesLevel = !levelFilter || merchant.level === levelFilter;
                const matchesSearch = !searchQuery || 
                    merchant.name.toLowerCase().includes(searchQuery) ||
                    merchant.id.toLowerCase().includes(searchQuery) ||
                    merchant.contact.toLowerCase().includes(searchQuery);
                
                return matchesStatus && matchesType && matchesLevel && matchesSearch;
            });

            renderMerchants();
        }

        function resetFilters() {
            document.querySelectorAll('.filter-select').forEach(select => {
                select.value = '';
            });
            document.querySelector('.search-input').value = '';
            currentMerchants = [...merchantsData];
            renderMerchants();
        }

        // 事件监听器设置
        function setupEventListeners() {
            // 点击外部关闭菜单
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.actions-menu')) {
                    document.querySelectorAll('.actions-dropdown').forEach(menu => {
                        menu.classList.remove('show');
                    });
                }
            });
        }

        // 商户操作函数
        function viewMerchant(merchantId) {
            console.log('查看商户:', merchantId);
            window.location.href = `merchant-detail.html?id=${merchantId}`;
        }

        function editMerchant(merchantId) {
            console.log('编辑商户:', merchantId);
            window.location.href = `merchant-edit.html?id=${merchantId}`;
        }

        function manageMerchantPermissions(merchantId) {
            console.log('管理权限:', merchantId);
            window.location.href = `merchant-permissions.html?id=${merchantId}`;
        }

        function viewMerchantStats(merchantId) {
            console.log('查看统计:', merchantId);
            window.location.href = `merchant-stats.html?id=${merchantId}`;
        }

        function approveMerchant(merchantId) {
            if (confirm('确定要审核通过该商户吗？')) {
                console.log('审核通过:', merchantId);
                showNotification('商户审核通过', 'success');
                // 更新商户状态
                updateMerchantStatus(merchantId, 'active');
            }
        }

        function rejectMerchant(merchantId) {
            const reason = prompt('请输入拒绝理由：');
            if (reason) {
                console.log('审核拒绝:', merchantId, reason);
                showNotification('商户审核已拒绝', 'error');
                updateMerchantStatus(merchantId, 'rejected');
            }
        }

        function freezeMerchant(merchantId) {
            if (confirm('确定要冻结该商户吗？')) {
                console.log('冻结商户:', merchantId);
                showNotification('商户已冻结', 'warning');
                updateMerchantStatus(merchantId, 'frozen');
            }
        }

        function unfreezemerchant(merchantId) {
            if (confirm('确定要解除冻结该商户吗？')) {
                console.log('解除冻结:', merchantId);
                showNotification('商户已解除冻结', 'success');
                updateMerchantStatus(merchantId, 'active');
            }
        }

        function deleteMerchant(merchantId) {
            if (confirm('确定要删除该商户吗？此操作不可恢复！')) {
                console.log('删除商户:', merchantId);
                showNotification('商户已删除', 'error');
                // 从数据中移除
                const index = merchantsData.findIndex(m => m.id === merchantId);
                if (index > -1) {
                    merchantsData.splice(index, 1);
                    applyFilters();
                }
            }
        }

        function updateMerchantStatus(merchantId, newStatus) {
            const merchant = merchantsData.find(m => m.id === merchantId);
            if (merchant) {
                merchant.status = newStatus;
                applyFilters();
            }
        }

        // 批量操作函数
        function batchApprove() {
            if (selectedMerchants.size === 0) return;
            
            if (confirm(`确定要批量通过选中的 ${selectedMerchants.size} 个商户吗？`)) {
                console.log('批量通过:', Array.from(selectedMerchants));
                selectedMerchants.forEach(merchantId => {
                    updateMerchantStatus(merchantId, 'active');
                });
                selectedMerchants.clear();
                showNotification('批量操作完成', 'success');
            }
        }

        function batchFreeze() {
            if (selectedMerchants.size === 0) return;
            
            if (confirm(`确定要批量冻结选中的 ${selectedMerchants.size} 个商户吗？`)) {
                console.log('批量冻结:', Array.from(selectedMerchants));
                selectedMerchants.forEach(merchantId => {
                    updateMerchantStatus(merchantId, 'frozen');
                });
                selectedMerchants.clear();
                showNotification('批量冻结完成', 'warning');
            }
        }

        function batchDelete() {
            if (selectedMerchants.size === 0) return;
            
            if (confirm(`确定要批量删除选中的 ${selectedMerchants.size} 个商户吗？此操作不可恢复！`)) {
                console.log('批量删除:', Array.from(selectedMerchants));
                selectedMerchants.forEach(merchantId => {
                    const index = merchantsData.findIndex(m => m.id === merchantId);
                    if (index > -1) {
                        merchantsData.splice(index, 1);
                    }
                });
                selectedMerchants.clear();
                applyFilters();
                showNotification('批量删除完成', 'error');
            }
        }

        // 其他功能函数
        function exportMerchants() {
            console.log('导出商户数据');
            showNotification('数据导出中...', 'info');
        }

        function importMerchants() {
            console.log('批量导入商户');
            showNotification('功能开发中...', 'info');
        }

        function createMerchant() {
            console.log('新增商户');
            window.location.href = 'merchant-application.html';
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification-toast ${type} show`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
