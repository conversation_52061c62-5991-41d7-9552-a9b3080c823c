<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户等级管理 - TeleShop后台管理</title>
    <link rel="stylesheet" href="../../css/admin-styles.css">
    <link rel="stylesheet" href="../../css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .level-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .level-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .level-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: rotate(45deg);
        }

        .level-card.gold {
            background: linear-gradient(135deg, #f7b733 0%, #fc4a1a 100%);
        }

        .level-card.platinum {
            background: linear-gradient(135deg, #8360c3 0%, #2ebf91 100%);
        }

        .level-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .level-icon {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 20px;
        }

        .level-name {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }

        .level-count {
            font-size: 14px;
            opacity: 0.8;
        }

        .level-benefits {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }

        .level-benefits li {
            padding: 5px 0;
            display: flex;
            align-items: center;
        }

        .level-benefits li::before {
            content: '✓';
            margin-right: 10px;
            color: #4CAF50;
            font-weight: bold;
        }

        .level-requirements {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }

        .level-requirements h4 {
            margin: 0 0 10px 0;
            font-size: 16px;
        }

        .requirement-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 14px;
        }

        .merchant-levels-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        }

        .table-actions {
            display: flex;
            gap: 10px;
        }

        .merchant-table {
            width: 100%;
            border-collapse: collapse;
        }

        .merchant-table th,
        .merchant-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .merchant-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .merchant-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-normal { background: #e8f5e8; color: #2e7d32; }
        .status-gold { background: #fff3e0; color: #f57c00; }
        .status-platinum { background: #f3e5f5; color: #7b1fa2; }

        .action-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            margin: 0 2px;
        }

        .btn-upgrade {
            background: #4CAF50;
            color: white;
        }

        .btn-downgrade {
            background: #ff9800;
            color: white;
        }

        .btn-details {
            background: #2196F3;
            color: white;
        }

        .level-management-tools {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .tool-card {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .tool-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
        }

        .tool-icon {
            font-size: 40px;
            color: #667eea;
            margin-bottom: 15px;
        }

        .tool-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .tool-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 20px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .level-cards {
                grid-template-columns: 1fr;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .table-actions {
                flex-direction: column;
                gap: 5px;
            }
            
            .merchant-table {
                font-size: 12px;
            }
            
            .merchant-table th,
            .merchant-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> TeleShop</h2>
            </div>
            <ul class="sidebar-menu">
                <li><a href="../../index.html"><i class="fas fa-dashboard"></i> 仪表板</a></li>
                <li class="menu-section">
                    <span>商户管理</span>
                    <ul>
                        <li><a href="index.html"><i class="fas fa-store"></i> 商户总览</a></li>
                        <li><a href="merchant-application.html"><i class="fas fa-file-alt"></i> 入驻申请</a></li>
                        <li><a href="merchant-review.html"><i class="fas fa-clipboard-check"></i> 审核管理</a></li>
                        <li class="active"><a href="merchant-level-management.html"><i class="fas fa-star"></i> 等级管理</a></li>
                        <li><a href="merchant-deposit.html"><i class="fas fa-coins"></i> 保证金</a></li>
                        <li><a href="merchant-permissions.html"><i class="fas fa-key"></i> 权限管理</a></li>
                        <li><a href="commission-management.html"><i class="fas fa-percentage"></i> 佣金管理</a></li>
                    </ul>
                </li>
                <li><a href="../user-management/index.html"><i class="fas fa-users"></i> 用户管理</a></li>
                <li><a href="../product-management/index.html"><i class="fas fa-box"></i> 商品管理</a></li>
                <li><a href="../order-management/index.html"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                <li><a href="../financial-management/index.html"><i class="fas fa-chart-line"></i> 财务管理</a></li>
                <li><a href="../data-analytics/index.html"><i class="fas fa-analytics"></i> 数据分析</a></li>
            </ul>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <header class="content-header">
                <h1><i class="fas fa-star"></i> 商户等级管理</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="openLevelSettingsModal()">
                        <i class="fas fa-cog"></i> 等级设置
                    </button>
                    <button class="btn btn-success" onclick="openBatchUpgradeModal()">
                        <i class="fas fa-level-up-alt"></i> 批量升级
                    </button>
                </div>
            </header>

            <!-- 等级概览卡片 -->
            <div class="level-cards">
                <div class="level-card">
                    <div class="level-header">
                        <div class="level-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <div>
                            <h3 class="level-name">普通商户</h3>
                            <p class="level-count">892 个商户</p>
                        </div>
                    </div>
                    <ul class="level-benefits">
                        <li>基础商品发布</li>
                        <li>标准客服支持</li>
                        <li>基础数据统计</li>
                        <li>5% 平台佣金</li>
                    </ul>
                    <div class="level-requirements">
                        <h4>升级条件</h4>
                        <div class="requirement-item">
                            <span>月销售额</span>
                            <span>≥ ¥50,000</span>
                        </div>
                        <div class="requirement-item">
                            <span>好评率</span>
                            <span>≥ 95%</span>
                        </div>
                        <div class="requirement-item">
                            <span>入驻时长</span>
                            <span>≥ 3个月</span>
                        </div>
                    </div>
                </div>

                <div class="level-card gold">
                    <div class="level-header">
                        <div class="level-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                        <div>
                            <h3 class="level-name">金牌商户</h3>
                            <p class="level-count">298 个商户</p>
                        </div>
                    </div>
                    <ul class="level-benefits">
                        <li>优先商品推荐</li>
                        <li>专属客服支持</li>
                        <li>高级数据分析</li>
                        <li>4% 平台佣金</li>
                        <li>营销工具权限</li>
                    </ul>
                    <div class="level-requirements">
                        <h4>升级条件</h4>
                        <div class="requirement-item">
                            <span>月销售额</span>
                            <span>≥ ¥200,000</span>
                        </div>
                        <div class="requirement-item">
                            <span>好评率</span>
                            <span>≥ 98%</span>
                        </div>
                        <div class="requirement-item">
                            <span>入驻时长</span>
                            <span>≥ 6个月</span>
                        </div>
                    </div>
                </div>

                <div class="level-card platinum">
                    <div class="level-header">
                        <div class="level-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div>
                            <h3 class="level-name">官方旗舰店</h3>
                            <p class="level-count">57 个商户</p>
                        </div>
                    </div>
                    <ul class="level-benefits">
                        <li>首页展示位置</li>
                        <li>VIP客服支持</li>
                        <li>定制数据报告</li>
                        <li>3% 平台佣金</li>
                        <li>全部营销工具</li>
                        <li>品牌专区权限</li>
                    </ul>
                    <div class="level-requirements">
                        <h4>申请条件</h4>
                        <div class="requirement-item">
                            <span>月销售额</span>
                            <span>≥ ¥500,000</span>
                        </div>
                        <div class="requirement-item">
                            <span>好评率</span>
                            <span>≥ 99%</span>
                        </div>
                        <div class="requirement-item">
                            <span>品牌授权</span>
                            <span>必需</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 等级管理工具 -->
            <div class="level-management-tools">
                <h3>等级管理工具</h3>
                <div class="tools-grid">
                    <div class="tool-card" onclick="openAutoUpgradeModal()">
                        <div class="tool-icon">
                            <i class="fas fa-magic"></i>
                        </div>
                        <div class="tool-title">自动升级</div>
                        <div class="tool-description">设置自动升级规则，符合条件的商户自动升级</div>
                        <button class="btn btn-primary">设置规则</button>
                    </div>

                    <div class="tool-card" onclick="openLevelAnalysisModal()">
                        <div class="tool-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="tool-title">等级分析</div>
                        <div class="tool-description">查看各等级商户的表现数据和趋势分析</div>
                        <button class="btn btn-info">查看分析</button>
                    </div>

                    <div class="tool-card" onclick="openRewardsModal()">
                        <div class="tool-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <div class="tool-title">升级奖励</div>
                        <div class="tool-description">设置商户升级奖励和激励机制</div>
                        <button class="btn btn-success">设置奖励</button>
                    </div>

                    <div class="tool-card" onclick="exportLevelReport()">
                        <div class="tool-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="tool-title">导出报告</div>
                        <div class="tool-description">导出商户等级分布和变化报告</div>
                        <button class="btn btn-secondary">导出报告</button>
                    </div>
                </div>
            </div>

            <!-- 商户等级列表 -->
            <div class="merchant-levels-table">
                <div class="table-header">
                    <h3 class="table-title">商户等级详情</h3>
                    <div class="table-actions">
                        <select id="levelFilter" onchange="filterByLevel()">
                            <option value="">全部等级</option>
                            <option value="normal">普通商户</option>
                            <option value="gold">金牌商户</option>
                            <option value="platinum">官方旗舰店</option>
                        </select>
                        <input type="text" id="searchInput" placeholder="搜索商户..." onkeyup="searchMerchants()">
                    </div>
                </div>
                <table class="merchant-table">
                    <thead>
                        <tr>
                            <th>商户信息</th>
                            <th>当前等级</th>
                            <th>月销售额</th>
                            <th>好评率</th>
                            <th>入驻时长</th>
                            <th>升级进度</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="merchantTableBody">
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <img src="https://via.placeholder.com/40" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                                    <div>
                                        <div style="font-weight: bold;">华为官方旗舰店</div>
                                        <div style="color: #666; font-size: 12px;">ID: M001</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="status-badge status-platinum">官方旗舰店</span></td>
                            <td>¥1,250,000</td>
                            <td>99.8%</td>
                            <td>2年3个月</td>
                            <td>
                                <div style="background: #e0e0e0; border-radius: 10px; height: 8px; position: relative;">
                                    <div style="background: #4CAF50; height: 100%; width: 100%; border-radius: 10px;"></div>
                                </div>
                                <small>已达标</small>
                            </td>
                            <td>
                                <button class="action-btn btn-details" onclick="viewMerchantDetails('M001')">详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <img src="https://via.placeholder.com/40" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                                    <div>
                                        <div style="font-weight: bold;">小米生活馆</div>
                                        <div style="color: #666; font-size: 12px;">ID: M002</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="status-badge status-gold">金牌商户</span></td>
                            <td>¥380,000</td>
                            <td>98.5%</td>
                            <td>1年8个月</td>
                            <td>
                                <div style="background: #e0e0e0; border-radius: 10px; height: 8px; position: relative;">
                                    <div style="background: #ff9800; height: 100%; width: 76%; border-radius: 10px;"></div>
                                </div>
                                <small>76% 达到旗舰店</small>
                            </td>
                            <td>
                                <button class="action-btn btn-upgrade" onclick="upgradeMerchant('M002')">升级</button>
                                <button class="action-btn btn-details" onclick="viewMerchantDetails('M002')">详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <img src="https://via.placeholder.com/40" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                                    <div>
                                        <div style="font-weight: bold;">美食天下</div>
                                        <div style="color: #666; font-size: 12px;">ID: M003</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="status-badge status-normal">普通商户</span></td>
                            <td>¥45,000</td>
                            <td>96.2%</td>
                            <td>5个月</td>
                            <td>
                                <div style="background: #e0e0e0; border-radius: 10px; height: 8px; position: relative;">
                                    <div style="background: #2196F3; height: 100%; width: 90%; border-radius: 10px;"></div>
                                </div>
                                <small>90% 达到金牌</small>
                            </td>
                            <td>
                                <button class="action-btn btn-upgrade" onclick="upgradeMerchant('M003')">升级</button>
                                <button class="action-btn btn-details" onclick="viewMerchantDetails('M003')">详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <img src="https://via.placeholder.com/40" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                                    <div>
                                        <div style="font-weight: bold;">时尚潮流</div>
                                        <div style="color: #666; font-size: 12px;">ID: M004</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="status-badge status-gold">金牌商户</span></td>
                            <td>¥156,000</td>
                            <td>97.8%</td>
                            <td>10个月</td>
                            <td>
                                <div style="background: #e0e0e0; border-radius: 10px; height: 8px; position: relative;">
                                    <div style="background: #f44336; height: 100%; width: 31%; border-radius: 10px;"></div>
                                </div>
                                <small>31% 达到旗舰店</small>
                            </td>
                            <td>
                                <button class="action-btn btn-details" onclick="viewMerchantDetails('M004')">详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <img src="https://via.placeholder.com/40" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;">
                                    <div>
                                        <div style="font-weight: bold;">数码科技</div>
                                        <div style="color: #666; font-size: 12px;">ID: M005</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="status-badge status-normal">普通商户</span></td>
                            <td>¥28,000</td>
                            <td>94.5%</td>
                            <td>2个月</td>
                            <td>
                                <div style="background: #e0e0e0; border-radius: 10px; height: 8px; position: relative;">
                                    <div style="background: #f44336; height: 100%; width: 56%; border-radius: 10px;"></div>
                                </div>
                                <small>56% 达到金牌</small>
                            </td>
                            <td>
                                <button class="action-btn btn-details" onclick="viewMerchantDetails('M005')">详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <!-- 等级设置模态框 -->
    <div id="levelSettingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>等级设置</h3>
                <span class="close" onclick="closeLevelSettingsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>选择等级</label>
                    <select id="levelSelect">
                        <option value="normal">普通商户</option>
                        <option value="gold">金牌商户</option>
                        <option value="platinum">官方旗舰店</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>月销售额要求 (元)</label>
                    <input type="number" id="salesRequirement" placeholder="输入最低月销售额">
                </div>
                <div class="form-group">
                    <label>好评率要求 (%)</label>
                    <input type="number" id="ratingRequirement" min="0" max="100" placeholder="输入最低好评率">
                </div>
                <div class="form-group">
                    <label>入驻时长要求 (月)</label>
                    <input type="number" id="durationRequirement" placeholder="输入最低入驻时长">
                </div>
                <div class="form-group">
                    <label>平台佣金比例 (%)</label>
                    <input type="number" id="commissionRate" min="0" max="100" step="0.1" placeholder="输入佣金比例">
                </div>
                <div class="form-group">
                    <label>等级权益</label>
                    <textarea id="levelBenefits" rows="4" placeholder="输入等级权益说明，每行一个权益"></textarea>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button class="btn btn-secondary" onclick="closeLevelSettingsModal()">取消</button>
                    <button class="btn btn-primary" onclick="saveLevelSettings()">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量升级模态框 -->
    <div id="batchUpgradeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>批量升级商户</h3>
                <span class="close" onclick="closeBatchUpgradeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>升级条件</label>
                    <div style="margin: 10px 0;">
                        <label><input type="checkbox" id="autoUpgradeEnabled"> 启用自动升级</label>
                    </div>
                </div>
                <div class="form-group">
                    <label>目标等级</label>
                    <select id="targetLevel">
                        <option value="gold">升级到金牌商户</option>
                        <option value="platinum">升级到官方旗舰店</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>筛选条件</label>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 10px 0;">
                        <input type="number" id="minSales" placeholder="最低月销售额">
                        <input type="number" id="minRating" placeholder="最低好评率">
                    </div>
                </div>
                <div class="form-group">
                    <label>符合条件的商户</label>
                    <div id="eligibleMerchants" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 5px;">
                        <div style="padding: 5px 0; border-bottom: 1px solid #eee;">
                            <label><input type="checkbox" checked> 小米生活馆 (月销售额: ¥380,000, 好评率: 98.5%)</label>
                        </div>
                        <div style="padding: 5px 0; border-bottom: 1px solid #eee;">
                            <label><input type="checkbox" checked> 美食天下 (月销售额: ¥45,000, 好评率: 96.2%)</label>
                        </div>
                        <div style="padding: 5px 0;">
                            <label><input type="checkbox" checked> 时尚潮流 (月销售额: ¥156,000, 好评率: 97.8%)</label>
                        </div>
                    </div>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button class="btn btn-secondary" onclick="closeBatchUpgradeModal()">取消</button>
                    <button class="btn btn-success" onclick="executeBatchUpgrade()">执行升级</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模态框控制
        function openLevelSettingsModal() {
            document.getElementById('levelSettingsModal').style.display = 'block';
        }

        function closeLevelSettingsModal() {
            document.getElementById('levelSettingsModal').style.display = 'none';
        }

        function openBatchUpgradeModal() {
            document.getElementById('batchUpgradeModal').style.display = 'block';
        }

        function closeBatchUpgradeModal() {
            document.getElementById('batchUpgradeModal').style.display = 'none';
        }

        function openAutoUpgradeModal() {
            showAutoUpgradeRulesModal();
        }

        function openLevelAnalysisModal() {
            showLevelAnalysisModal();
        }

        function openRewardsModal() {
            showUpgradeRewardsModal();
        }

        // 功能函数
        function saveLevelSettings() {
            const level = document.getElementById('levelSelect').value;
            const sales = document.getElementById('salesRequirement').value;
            const rating = document.getElementById('ratingRequirement').value;
            const duration = document.getElementById('durationRequirement').value;
            const commission = document.getElementById('commissionRate').value;
            const benefits = document.getElementById('levelBenefits').value;

            if (!sales || !rating || !duration || !commission) {
                alert('请填写所有必需字段');
                return;
            }

            // 模拟保存
            alert('等级设置已保存');
            closeLevelSettingsModal();
        }

        function executeBatchUpgrade() {
            const targetLevel = document.getElementById('targetLevel').value;
            const checkboxes = document.querySelectorAll('#eligibleMerchants input[type="checkbox"]:checked');
            
            if (checkboxes.length === 0) {
                alert('请选择要升级的商户');
                return;
            }

            if (confirm(`确定要升级 ${checkboxes.length} 个商户吗？`)) {
                // 模拟批量升级
                alert(`已成功升级 ${checkboxes.length} 个商户`);
                closeBatchUpgradeModal();
                location.reload();
            }
        }

        function upgradeMerchant(merchantId) {
            if (confirm('确定要升级此商户等级吗？')) {
                alert(`商户 ${merchantId} 升级申请已提交`);
                // 这里可以添加实际的升级逻辑
            }
        }

        function viewMerchantDetails(merchantId) {
            alert(`查看商户 ${merchantId} 详细信息功能开发中...`);
        }

        function filterByLevel() {
            const filter = document.getElementById('levelFilter').value;
            const rows = document.querySelectorAll('#merchantTableBody tr');
            
            rows.forEach(row => {
                const levelBadge = row.querySelector('.status-badge');
                if (!filter || levelBadge.classList.contains(`status-${filter}`)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function searchMerchants() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const rows = document.querySelectorAll('#merchantTableBody tr');
            
            rows.forEach(row => {
                const merchantName = row.querySelector('div[style*="font-weight: bold"]').textContent.toLowerCase();
                if (merchantName.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function exportLevelReport() {
            alert('等级报告导出功能开发中...');
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const levelModal = document.getElementById('levelSettingsModal');
            const batchModal = document.getElementById('batchUpgradeModal');
            
            if (event.target === levelModal) {
                levelModal.style.display = 'none';
            }
            if (event.target === batchModal) {
                batchModal.style.display = 'none';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('商户等级管理页面加载完成');
        });

        // 自动升级规则设置功能实现
        function showAutoUpgradeRulesModal() {
            const modal = createModalElement('自动升级规则设置', `
                <div class="auto-upgrade-rules">
                    <div class="rule-section">
                        <h4>升级规则配置</h4>
                        <div class="form-group">
                            <label>启用自动升级：</label>
                            <label class="switch">
                                <input type="checkbox" id="autoUpgradeEnabled" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="upgrade-rules">
                            <div class="rule-item">
                                <h5>普通商户 → 金牌商户</h5>
                                <div class="rule-conditions">
                                    <div class="condition">
                                        <label>月销售额 ≥</label>
                                        <input type="number" id="goldSalesRule" value="50000" min="0">
                                        <span>元</span>
                                    </div>
                                    <div class="condition">
                                        <label>好评率 ≥</label>
                                        <input type="number" id="goldRatingRule" value="95" min="0" max="100" step="0.1">
                                        <span>%</span>
                                    </div>
                                    <div class="condition">
                                        <label>入驻时间 ≥</label>
                                        <input type="number" id="goldDurationRule" value="3" min="0">
                                        <span>个月</span>
                                    </div>
                                    <div class="condition">
                                        <label>违规次数 ≤</label>
                                        <input type="number" id="goldViolationRule" value="2" min="0">
                                        <span>次</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="rule-item">
                                <h5>金牌商户 → 官方旗舰店</h5>
                                <div class="rule-conditions">
                                    <div class="condition">
                                        <label>月销售额 ≥</label>
                                        <input type="number" id="platinumSalesRule" value="200000" min="0">
                                        <span>元</span>
                                    </div>
                                    <div class="condition">
                                        <label>好评率 ≥</label>
                                        <input type="number" id="platinumRatingRule" value="98" min="0" max="100" step="0.1">
                                        <span>%</span>
                                    </div>
                                    <div class="condition">
                                        <label>入驻时间 ≥</label>
                                        <input type="number" id="platinumDurationRule" value="6" min="0">
                                        <span>个月</span>
                                    </div>
                                    <div class="condition">
                                        <label>违规次数 ≤</label>
                                        <input type="number" id="platinumViolationRule" value="0" min="0">
                                        <span>次</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="auto-upgrade-settings">
                            <h4>自动升级设置</h4>
                            <div class="form-group">
                                <label>检查频率：</label>
                                <select id="checkFrequency">
                                    <option value="daily">每日检查</option>
                                    <option value="weekly">每周检查</option>
                                    <option value="monthly" selected>每月检查</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>升级生效时间：</label>
                                <select id="effectiveTime">
                                    <option value="immediate">立即生效</option>
                                    <option value="next_day">次日生效</option>
                                    <option value="next_month">下月生效</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>通知设置：</label>
                                <div class="checkbox-group">
                                    <label><input type="checkbox" checked> 升级通知商户</label>
                                    <label><input type="checkbox" checked> 升级通知管理员</label>
                                    <label><input type="checkbox"> 生成升级报告</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button onclick="testUpgradeRules()" class="btn btn-info">测试规则</button>
                    <button onclick="closeAutoUpgradeModal()" class="btn btn-secondary">取消</button>
                    <button onclick="saveAutoUpgradeRules()" class="btn btn-primary">保存规则</button>
                </div>
            `);
        }
        
        // 等级分析功能实现
        function showLevelAnalysisModal() {
            const modal = createModalElement('等级分析报告', `
                <div class="level-analysis">
                    <div class="analysis-tabs">
                        <button class="tab-btn active" onclick="switchAnalysisTab('overview')">概览统计</button>
                        <button class="tab-btn" onclick="switchAnalysisTab('trends')">升级趋势</button>
                        <button class="tab-btn" onclick="switchAnalysisTab('performance')">等级效益</button>
                        <button class="tab-btn" onclick="switchAnalysisTab('predictions')">升级预测</button>
                    </div>
                    
                    <div id="overviewTab" class="analysis-content active">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <h4>等级分布</h4>
                                <div class="chart-placeholder">
                                    <canvas id="levelDistributionChart" width="300" height="200"></canvas>
                                </div>
                                <div class="stats-summary">
                                    <div class="stat-item">
                                        <span class="label">普通商户:</span>
                                        <span class="value">156个 (52%)</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="label">金牌商户:</span>
                                        <span class="value">98个 (33%)</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="label">官方旗舰店:</span>
                                        <span class="value">46个 (15%)</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <h4>升级成功率</h4>
                                <div class="success-rate">
                                    <div class="rate-item">
                                        <span class="rate-label">普通→金牌</span>
                                        <div class="progress-bar">
                                            <div class="progress" style="width: 68%"></div>
                                        </div>
                                        <span class="rate-value">68%</span>
                                    </div>
                                    <div class="rate-item">
                                        <span class="rate-label">金牌→旗舰</span>
                                        <div class="progress-bar">
                                            <div class="progress" style="width: 42%"></div>
                                        </div>
                                        <span class="rate-value">42%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <h4>平均升级时间</h4>
                                <div class="time-stats">
                                    <div class="time-item">
                                        <span class="time-label">普通→金牌</span>
                                        <span class="time-value">4.2个月</span>
                                    </div>
                                    <div class="time-item">
                                        <span class="time-label">金牌→旗舰</span>
                                        <span class="time-value">8.6个月</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="trendsTab" class="analysis-content" style="display:none;">
                        <div class="trends-content">
                            <h4>升级趋势分析（近12个月）</h4>
                            <canvas id="upgradesTrendsChart" width="600" height="300"></canvas>
                            <div class="trend-insights">
                                <div class="insight-item">
                                    <i class="fas fa-arrow-up trend-up"></i>
                                    <span>升级申请数量同比增长 23%</span>
                                </div>
                                <div class="insight-item">
                                    <i class="fas fa-arrow-down trend-down"></i>
                                    <span>升级成功率较上月下降 5%</span>
                                </div>
                                <div class="insight-item">
                                    <i class="fas fa-chart-line trend-stable"></i>
                                    <span>官方旗舰店数量保持稳定增长</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="performanceTab" class="analysis-content" style="display:none;">
                        <div class="performance-content">
                            <h4>等级效益对比</h4>
                            <div class="performance-grid">
                                <div class="performance-card">
                                    <h5>平均月销售额</h5>
                                    <div class="performance-chart">
                                        <div class="bar-chart">
                                            <div class="bar" data-value="15000">
                                                <span class="bar-label">普通</span>
                                                <span class="bar-value">¥15,000</span>
                                            </div>
                                            <div class="bar" data-value="48000">
                                                <span class="bar-label">金牌</span>
                                                <span class="bar-value">¥48,000</span>
                                            </div>
                                            <div class="bar" data-value="185000">
                                                <span class="bar-label">旗舰</span>
                                                <span class="bar-value">¥185,000</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="performance-card">
                                    <h5>客户满意度</h5>
                                    <div class="satisfaction-rates">
                                        <div class="rate-circle" data-rate="92">
                                            <span class="rate-text">92%</span>
                                            <span class="rate-label">普通</span>
                                        </div>
                                        <div class="rate-circle" data-rate="96">
                                            <span class="rate-text">96%</span>
                                            <span class="rate-label">金牌</span>
                                        </div>
                                        <div class="rate-circle" data-rate="99">
                                            <span class="rate-text">99%</span>
                                            <span class="rate-label">旗舰</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="predictionsTab" class="analysis-content" style="display:none;">
                        <div class="predictions-content">
                            <h4>升级预测分析</h4>
                            <div class="prediction-cards">
                                <div class="prediction-card">
                                    <h5>预计下月升级</h5>
                                    <div class="prediction-number">28个商户</div>
                                    <div class="prediction-details">
                                        <span>普通→金牌: 18个</span>
                                        <span>金牌→旗舰: 10个</span>
                                    </div>
                                </div>
                                
                                <div class="prediction-card">
                                    <h5>潜在升级候选</h5>
                                    <div class="candidate-list">
                                        <div class="candidate-item">
                                            <span class="merchant-name">科技数码专营店</span>
                                            <span class="upgrade-probability">升级概率: 89%</span>
                                        </div>
                                        <div class="candidate-item">
                                            <span class="merchant-name">美食生活馆</span>
                                            <span class="upgrade-probability">升级概率: 76%</span>
                                        </div>
                                        <div class="candidate-item">
                                            <span class="merchant-name">时尚服饰店</span>
                                            <span class="upgrade-probability">升级概率: 65%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button onclick="exportAnalysisReport()" class="btn btn-info">导出报告</button>
                    <button onclick="closeLevelAnalysisModal()" class="btn btn-secondary">关闭</button>
                </div>
            `);
            
            // 初始化图表
            setTimeout(() => {
                initAnalysisCharts();
            }, 100);
        }
        
        // 升级奖励设置功能实现  
        function showUpgradeRewardsModal() {
            const modal = createModalElement('升级奖励设置', `
                <div class="upgrade-rewards">
                    <div class="rewards-section">
                        <h4>升级奖励配置</h4>
                        
                        <div class="reward-level">
                            <h5>普通商户 → 金牌商户奖励</h5>
                            <div class="reward-items">
                                <div class="reward-item">
                                    <label>现金奖励：</label>
                                    <div class="input-group">
                                        <input type="number" id="goldCashReward" value="500" min="0">
                                        <span>元</span>
                                    </div>
                                </div>
                                <div class="reward-item">
                                    <label>积分奖励：</label>
                                    <div class="input-group">
                                        <input type="number" id="goldPointsReward" value="1000" min="0">
                                        <span>积分</span>
                                    </div>
                                </div>
                                <div class="reward-item">
                                    <label>佣金减免：</label>
                                    <div class="input-group">
                                        <input type="number" id="goldCommissionDiscount" value="0.5" min="0" max="5" step="0.1">
                                        <span>%（3个月）</span>
                                    </div>
                                </div>
                                <div class="reward-item">
                                    <label>特权礼包：</label>
                                    <div class="checkbox-group">
                                        <label><input type="checkbox" checked> 专属客服支持</label>
                                        <label><input type="checkbox" checked> 优先推荐位</label>
                                        <label><input type="checkbox"> 免费营销工具</label>
                                        <label><input type="checkbox"> 数据分析报告</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="reward-level">
                            <h5>金牌商户 → 官方旗舰店奖励</h5>
                            <div class="reward-items">
                                <div class="reward-item">
                                    <label>现金奖励：</label>
                                    <div class="input-group">
                                        <input type="number" id="platinumCashReward" value="2000" min="0">
                                        <span>元</span>
                                    </div>
                                </div>
                                <div class="reward-item">
                                    <label>积分奖励：</label>
                                    <div class="input-group">
                                        <input type="number" id="platinumPointsReward" value="5000" min="0">
                                        <span>积分</span>
                                    </div>
                                </div>
                                <div class="reward-item">
                                    <label>佣金减免：</label>
                                    <div class="input-group">
                                        <input type="number" id="platinumCommissionDiscount" value="1.0" min="0" max="5" step="0.1">
                                        <span>%（6个月）</span>
                                    </div>
                                </div>
                                <div class="reward-item">
                                    <label>VIP特权：</label>
                                    <div class="checkbox-group">
                                        <label><input type="checkbox" checked> 品牌馆入驻</label>
                                        <label><input type="checkbox" checked> 首页展示位</label>
                                        <label><input type="checkbox" checked> 专属运营顾问</label>
                                        <label><input type="checkbox" checked> 绿色通道审核</label>
                                        <label><input type="checkbox"> 定制化推广方案</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="reward-settings">
                            <h4>奖励发放设置</h4>
                            <div class="form-group">
                                <label>发放方式：</label>
                                <select id="rewardDistribution">
                                    <option value="auto">自动发放</option>
                                    <option value="manual">手动审核发放</option>
                                    <option value="batch">批量发放</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>发放时间：</label>
                                <select id="rewardTiming">
                                    <option value="immediate">升级后立即发放</option>
                                    <option value="next_day">升级后次日发放</option>
                                    <option value="monthly">每月统一发放</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>有效期设置：</label>
                                <div class="input-group">
                                    <input type="number" id="rewardValidity" value="12" min="1">
                                    <span>个月</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="reward-history">
                            <h4>最近发放记录</h4>
                            <div class="history-list">
                                <div class="history-item">
                                    <span class="merchant">科技数码专营店</span>
                                    <span class="reward">金牌升级奖励</span>
                                    <span class="amount">¥500 + 1000积分</span>
                                    <span class="date">2023-12-10</span>
                                </div>
                                <div class="history-item">
                                    <span class="merchant">美食生活馆</span>
                                    <span class="reward">旗舰店升级奖励</span>
                                    <span class="amount">¥2000 + 5000积分</span>
                                    <span class="date">2023-12-08</span>
                                </div>
                                <div class="history-item">
                                    <span class="merchant">时尚服饰店</span>
                                    <span class="reward">金牌升级奖励</span>
                                    <span class="amount">¥500 + 1000积分</span>
                                    <span class="date">2023-12-05</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button onclick="previewRewards()" class="btn btn-info">预览奖励</button>
                    <button onclick="closeUpgradeRewardsModal()" class="btn btn-secondary">取消</button>
                    <button onclick="saveUpgradeRewards()" class="btn btn-primary">保存设置</button>
                </div>
            `);
        }

        // 通用功能辅助函数
        function createModalElement(title, content) {
            const modal = document.createElement('div');
            modal.className = 'advanced-modal';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.6); z-index: 1000;
                display: flex; align-items: center; justify-content: center;
                animation: fadeIn 0.3s ease;
            `;
            
            modal.innerHTML = `
                <div class="advanced-modal-content" style="
                    background: white; border-radius: 12px; max-width: 1000px; 
                    max-height: 90vh; overflow-y: auto; margin: 20px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
                ">
                    <div class="advanced-modal-header" style="
                        padding: 24px; border-bottom: 1px solid #e5e7eb;
                        display: flex; justify-content: space-between; align-items: center;
                    ">
                        <h3 style="margin: 0; color: #1f2937; font-size: 20px; font-weight: 600;">${title}</h3>
                        <button onclick="closeAdvancedModal()" style="
                            background: none; border: none; font-size: 24px; 
                            color: #6b7280; cursor: pointer; padding: 0;
                        ">&times;</button>
                    </div>
                    <div class="advanced-modal-body" style="padding: 30px;">
                        ${content}
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 添加样式到头部
            const style = document.createElement('style');
            style.textContent = `
                .advanced-modal {
                    animation: fadeIn 0.3s ease;
                }
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                .auto-upgrade-rules .rule-section {
                    margin-bottom: 30px;
                }
                .upgrade-rules .rule-item {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 20px;
                    margin-bottom: 20px;
                }
                .rule-conditions {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 15px;
                    margin-top: 15px;
                }
                .condition {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
                .condition input {
                    width: 100px;
                    padding: 8px;
                    border: 1px solid #d1d5db;
                    border-radius: 6px;
                }
                .switch {
                    position: relative;
                    display: inline-block;
                    width: 60px;
                    height: 34px;
                }
                .switch input {
                    opacity: 0;
                    width: 0;
                    height: 0;
                }
                .slider {
                    position: absolute;
                    cursor: pointer;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-color: #ccc;
                    transition: .4s;
                    border-radius: 34px;
                }
                .slider:before {
                    position: absolute;
                    content: "";
                    height: 26px;
                    width: 26px;
                    left: 4px;
                    bottom: 4px;
                    background-color: white;
                    transition: .4s;
                    border-radius: 50%;
                }
                input:checked + .slider {
                    background-color: #2196F3;
                }
                input:checked + .slider:before {
                    transform: translateX(26px);
                }
                .analysis-tabs {
                    display: flex;
                    border-bottom: 2px solid #e5e7eb;
                    margin-bottom: 30px;
                }
                .tab-btn {
                    padding: 12px 24px;
                    background: none;
                    border: none;
                    color: #6b7280;
                    cursor: pointer;
                    font-weight: 500;
                    transition: all 0.3s;
                }
                .tab-btn.active {
                    color: #3b82f6;
                    border-bottom: 2px solid #3b82f6;
                }
                .stats-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                }
                .stat-card {
                    background: #f9fafb;
                    border: 1px solid #e5e7eb;
                    border-radius: 12px;
                    padding: 20px;
                }
                .progress-bar {
                    width: 100%;
                    height: 8px;
                    background: #e5e7eb;
                    border-radius: 4px;
                    overflow: hidden;
                    margin: 5px 0;
                }
                .progress {
                    height: 100%;
                    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
                    transition: width 0.5s ease;
                }
                .rate-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin: 10px 0;
                }
                .reward-level {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 12px;
                    padding: 24px;
                    margin-bottom: 24px;
                }
                .reward-items {
                    display: grid;
                    gap: 20px;
                }
                .reward-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }
                .input-group {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
                .input-group input {
                    width: 120px;
                    padding: 8px 12px;
                    border: 1px solid #d1d5db;
                    border-radius: 6px;
                }
                .checkbox-group {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 10px;
                }
                .checkbox-group label {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 14px;
                }
                .history-list {
                    max-height: 200px;
                    overflow-y: auto;
                }
                .history-item {
                    display: grid;
                    grid-template-columns: 2fr 1fr 1fr 1fr;
                    gap: 15px;
                    padding: 12px 0;
                    border-bottom: 1px solid #e5e7eb;
                    font-size: 14px;
                }
                .history-item:last-child {
                    border-bottom: none;
                }
                .modal-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 12px;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #e5e7eb;
                }
                .trend-up { color: #10b981; }
                .trend-down { color: #ef4444; }
                .trend-stable { color: #3b82f6; }
                .insight-item {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    margin: 10px 0;
                    padding: 12px;
                    background: white;
                    border-radius: 8px;
                    border-left: 4px solid #3b82f6;
                }
            `;
            document.head.appendChild(style);
            
            return modal;
        }
        
        function closeAdvancedModal() {
            const modal = document.querySelector('.advanced-modal');
            if (modal) modal.remove();
        }
        
        function closeAutoUpgradeModal() {
            closeAdvancedModal();
        }
        
        function closeLevelAnalysisModal() {
            closeAdvancedModal();
        }
        
        function closeUpgradeRewardsModal() {
            closeAdvancedModal();
        }
        
        // 自动升级规则相关功能
        function testUpgradeRules() {
            const goldSales = document.getElementById('goldSalesRule').value;
            const goldRating = document.getElementById('goldRatingRule').value;
            
            alert(`测试升级规则:\n金牌商户要求 - 月销售额≥${goldSales}元，好评率≥${goldRating}%\n\n符合条件的商户: 8个\n预计升级商户: 3个`);
        }
        
        function saveAutoUpgradeRules() {
            const enabled = document.getElementById('autoUpgradeEnabled').checked;
            const frequency = document.getElementById('checkFrequency').value;
            const effectiveTime = document.getElementById('effectiveTime').value;
            
            if (confirm('确定保存自动升级规则设置吗？')) {
                alert('自动升级规则已保存');
                closeAutoUpgradeModal();
            }
        }
        
        // 等级分析相关功能
        function switchAnalysisTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.analysis-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // 移除所有active类
            document.querySelectorAll('.analysis-tabs .tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中内容
            document.getElementById(tabName + 'Tab').style.display = 'block';
            
            // 添加active类
            event.target.classList.add('active');
        }
        
        function initAnalysisCharts() {
            // 这里可以集成Chart.js等图表库来绘制真实图表
            console.log('初始化分析图表');
        }
        
        function exportAnalysisReport() {
            alert('正在导出等级分析报告...\n\n包含内容:\n- 等级分布统计\n- 升级趋势分析\n- 效益对比数据\n- 升级预测结果');
        }
        
        // 升级奖励相关功能
        function previewRewards() {
            const goldCash = document.getElementById('goldCashReward').value;
            const goldPoints = document.getElementById('goldPointsReward').value;
            const platinumCash = document.getElementById('platinumCashReward').value;
            const platinumPoints = document.getElementById('platinumPointsReward').value;
            
            alert(`奖励预览:\n\n金牌商户升级奖励:\n- 现金: ¥${goldCash}\n- 积分: ${goldPoints}分\n\n旗舰店升级奖励:\n- 现金: ¥${platinumCash}\n- 积分: ${platinumPoints}分`);
        }
        
        function saveUpgradeRewards() {
            const distribution = document.getElementById('rewardDistribution').value;
            const timing = document.getElementById('rewardTiming').value;
            const validity = document.getElementById('rewardValidity').value;
            
            if (confirm('确定保存升级奖励设置吗？')) {
                alert('升级奖励设置已保存');
                closeUpgradeRewardsModal();
            }
        }
        
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                padding: 15px 20px; border-radius: 8px; color: white;
                font-size: 14px; max-width: 300px;
                ${type === 'success' ? 'background: #10b981;' : 
                  type === 'error' ? 'background: #ef4444;' : 'background: #3b82f6;'}
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }
    </script>
</body>
</html>
