        // 全局变量
        let currentTab = 'pending';
        let applications = {
            pending: [
                {
                    id: 'MA202401150001',
                    name: '华为旗舰专卖店',
                    type: '企业商户',
                    contact: '张经理',
                    phone: '138****8888',
                    category: '电子数码',
                    status: 'pending',
                    date: '2024-01-15 14:30',
                    priority: 'high'
                },
                {
                    id: 'MA202401150002',
                    name: '小米生活专营店',
                    type: '个人商户',
                    contact: '李女士',
                    phone: '139****9999',
                    category: '电子数码',
                    status: 'pending',
                    date: '2024-01-15 16:20',
                    priority: 'normal'
                }
            ],
            reviewing: [
                {
                    id: 'MA202401140001',
                    name: '阿迪达斯官方旗舰店',
                    type: '品牌旗舰店',
                    contact: '王经理',
                    phone: '136****7777',
                    category: '服装鞋帽',
                    status: 'reviewing',
                    date: '2024-01-14 10:15',
                    reviewer: '审核员A',
                    priority: 'high'
                }
            ],
            approved: [],
            rejected: []
        };
        
        let filteredApplications = [];

        // 切换标签
        function switchTab(tabName) {
            currentTab = tabName;
            
            // 更新标签状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            
            // 加载对应状态的申请
            loadApplicationsByStatus(tabName);
        }

        // 根据状态加载申请
        function loadApplicationsByStatus(status) {
            showLoading();
            
            setTimeout(() => {
                filteredApplications = applications[status] || [];
                renderApplications(filteredApplications);
                hideLoading();
            }, 500);
        }

        // 渲染申请列表
        function renderApplications(apps) {
            const container = document.querySelector('.applications-container');
            
            if (apps.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; color: #64748b;">
                        <div style="font-size: 48px; margin-bottom: 16px;">📋</div>
                        <h3 style="margin: 0 0 8px 0;">暂无申请</h3>
                        <p style="margin: 0;">当前筛选条件下没有找到相关申请</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = apps.map(app => `
                <div class="application-card">
                    <div class="card-header">
                        <div class="merchant-info">
                            <h3 class="merchant-name">${app.name}</h3>
                            <span class="merchant-type">
                                <i class="fas ${getTypeIcon(app.type)}"></i>
                                ${app.type}
                            </span>
                            ${app.priority === 'high' ? '<span class="priority-badge">🔥 高优先级</span>' : ''}
                        </div>
                        <div class="application-meta">
                            <div class="application-date">${app.date}</div>
                            <div class="application-id">申请编号: ${app.id}</div>
                        </div>
                    </div>

                    <div class="card-content">
                        <div class="info-group">
                            <span class="info-label">联系人</span>
                            <span class="info-value">${app.contact}</span>
                        </div>
                        <div class="info-group">
                            <span class="info-label">联系电话</span>
                            <span class="info-value">${app.phone}</span>
                        </div>
                        <div class="info-group">
                            <span class="info-label">主营类目</span>
                            <span class="info-value">${app.category}</span>
                        </div>
                        <div class="info-group">
                            <span class="info-label">当前状态</span>
                            <span class="status-badge status-${app.status}">
                                <i class="fas ${getStatusIcon(app.status)}"></i>
                                ${getStatusText(app.status)}
                            </span>
                        </div>
                        ${app.reviewer ? `
                        <div class="info-group">
                            <span class="info-label">审核员</span>
                            <span class="info-value">${app.reviewer}</span>
                        </div>
                        ` : ''}
                    </div>

                    <div class="card-actions">
                        <button class="btn btn-outline" onclick="viewApplicationDetail('${app.id}')">
                            <i class="fas fa-eye"></i>
                            查看详情
                        </button>
                        ${getActionButtons(app)}
                    </div>
                </div>
            `).join('');
        }
        
        // 获取类型图标
        function getTypeIcon(type) {
            const icons = {
                '企业商户': 'fa-building',
                '个人商户': 'fa-user',
                '品牌旗舰店': 'fa-crown'
            };
            return icons[type] || 'fa-store';
        }
        
        // 获取状态图标
        function getStatusIcon(status) {
            const icons = {
                'pending': 'fa-clock',
                'reviewing': 'fa-search',
                'approved': 'fa-check',
                'rejected': 'fa-times'
            };
            return icons[status] || 'fa-question';
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'pending': '待审核',
                'reviewing': '审核中',
                'approved': '已通过',
                'rejected': '已拒绝'
            };
            return texts[status] || '未知';
        }
        
        // 获取操作按钮
        function getActionButtons(app) {
            switch(app.status) {
                case 'pending':
                    return `
                        <button class="btn btn-primary" onclick="startReview('${app.id}')">
                            <i class="fas fa-play"></i>
                            开始审核
                        </button>
                        <button class="btn btn-success btn-sm" onclick="quickApprove('${app.id}')">
                            <i class="fas fa-check"></i>
                            快速通过
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="quickReject('${app.id}')">
                            <i class="fas fa-times"></i>
                            快速拒绝
                        </button>
                    `;
                case 'reviewing':
                    return `
                        <button class="btn btn-primary" onclick="continueReview('${app.id}')">
                            <i class="fas fa-edit"></i>
                            继续审核
                        </button>
                        <button class="btn btn-secondary" onclick="reassignReviewer('${app.id}')">
                            <i class="fas fa-user-tag"></i>
                            重新分配
                        </button>
                    `;
                case 'approved':
                    return `
                        <button class="btn btn-info" onclick="viewMerchantStore('${app.id}')">
                            <i class="fas fa-store"></i>
                            查看店铺
                        </button>
                    `;
                case 'rejected':
                    return `
                        <button class="btn btn-warning" onclick="reconsiderApplication('${app.id}')">
                            <i class="fas fa-redo"></i>
                            重新考虑
                        </button>
                    `;
                default:
                    return '';
            }
        }

        // 筛选函数
        function filterByDate(value) {
            applyFilters();
        }

        function filterByType(value) {
            applyFilters();
        }

        function filterByCategory(value) {
            applyFilters();
        }

        function searchApplications(query) {
            applyFilters();
        }
        
        // 应用筛选
        function applyFilters() {
            const dateFilter = document.querySelector('.filter-select').value;
            const typeFilter = document.querySelectorAll('.filter-select')[1].value;
            const categoryFilter = document.querySelectorAll('.filter-select')[2].value;
            const searchQuery = document.querySelector('.filter-input').value.toLowerCase();
            
            let filtered = applications[currentTab] || [];
            
            // 按日期筛选
            if (dateFilter) {
                const now = new Date();
                filtered = filtered.filter(app => {
                    const appDate = new Date(app.date);
                    switch(dateFilter) {
                        case 'today':
                            return appDate.toDateString() === now.toDateString();
                        case 'week':
                            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                            return appDate >= weekAgo;
                        case 'month':
                            return appDate.getMonth() === now.getMonth() && appDate.getFullYear() === now.getFullYear();
                        default:
                            return true;
                    }
                });
            }
            
            // 按类型筛选
            if (typeFilter) {
                filtered = filtered.filter(app => app.type === typeFilter);
            }
            
            // 按类目筛选
            if (categoryFilter) {
                filtered = filtered.filter(app => app.category === categoryFilter);
            }
            
            // 按搜索关键词筛选
            if (searchQuery) {
                filtered = filtered.filter(app => 
                    app.name.toLowerCase().includes(searchQuery) ||
                    app.contact.toLowerCase().includes(searchQuery) ||
                    app.id.toLowerCase().includes(searchQuery)
                );
            }
            
            filteredApplications = filtered;
            renderApplications(filtered);
        }

        function resetFilters() {
            document.querySelectorAll('.filter-select, .filter-input').forEach(el => {
                el.value = '';
            });
            loadApplicationsByStatus(currentTab);
        }

        // 显示加载状态
        function showLoading() {
            const container = document.querySelector('.applications-container');
            container.innerHTML = `
                <div style="text-align: center; padding: 60px 20px; color: #64748b;">
                    <div class="loading-spinner" style="width: 40px; height: 40px; margin: 0 auto 16px; border: 4px solid #f3f4f6; border-top: 4px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    <p style="margin: 0;">加载中...</p>
                </div>
            `;
        }
        
        function hideLoading() {
            // 加载完成后会被renderApplications替换
        }

        // 查看申请详情
        function viewApplicationDetail(applicationId) {
            const app = findApplication(applicationId);
            if (!app) return;
            
            showDetailModal(app);
        }
        
        // 显示详情模态框
        function showDetailModal(app) {
            const modal = createModal('申请详情', `
                <div class="detail-content">
                    <div class="detail-header">
                        <h2 style="margin: 0 0 8px 0; color: #1e293b;">${app.name}</h2>
                        <div style="display: flex; gap: 12px; align-items: center;">
                            <span class="merchant-type">
                                <i class="fas ${getTypeIcon(app.type)}"></i>
                                ${app.type}
                            </span>
                            <span class="status-badge status-${app.status}">
                                <i class="fas ${getStatusIcon(app.status)}"></i>
                                ${getStatusText(app.status)}
                            </span>
                        </div>
                    </div>
                    
                    <div class="detail-sections">
                        <div class="detail-section">
                            <h4>基本信息</h4>
                            <div class="detail-grid">
                                <div><strong>申请编号:</strong> ${app.id}</div>
                                <div><strong>申请时间:</strong> ${app.date}</div>
                                <div><strong>联系人:</strong> ${app.contact}</div>
                                <div><strong>联系电话:</strong> ${app.phone}</div>
                                <div><strong>主营类目:</strong> ${app.category}</div>
                                <div><strong>商户类型:</strong> ${app.type}</div>
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h4>审核进度</h4>
                            <div class="review-progress">
                                <div class="progress-step ${app.status === 'pending' || app.status === 'reviewing' || app.status === 'approved' || app.status === 'rejected' ? 'completed' : ''}">
                                    <div class="step-icon">1</div>
                                    <div class="step-text">提交申请</div>
                                </div>
                                <div class="progress-step ${app.status === 'reviewing' || app.status === 'approved' || app.status === 'rejected' ? 'completed' : app.status === 'pending' ? 'current' : ''}">
                                    <div class="step-icon">2</div>
                                    <div class="step-text">资料审核</div>
                                </div>
                                <div class="progress-step ${app.status === 'approved' ? 'completed' : app.status === 'reviewing' ? 'current' : ''}">
                                    <div class="step-icon">3</div>
                                    <div class="step-text">审核通过</div>
                                </div>
                            </div>
                        </div>
                        
                        ${app.status === 'rejected' ? `
                        <div class="detail-section">
                            <h4>拒绝原因</h4>
                            <div class="reject-reason">
                                <p>资料不完整，请补充营业执照副本和法人身份证明。</p>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <button onclick="closeModal()" class="btn btn-secondary">关闭</button>
                    ${app.status === 'pending' ? `<button onclick="closeModal(); startReview('${app.id}')" class="btn btn-primary">开始审核</button>` : ''}
                </div>
            `);
        }

        // 开始审核
        function startReview(applicationId) {
            const app = findApplication(applicationId);
            if (!app) return;
            
            // 更新状态为审核中
            app.status = 'reviewing';
            app.reviewer = '当前审核员';
            
            // 移动到reviewing数组
            moveApplication(applicationId, 'pending', 'reviewing');
            
            showReviewModal(app);
        }

        // 继续审核
        function continueReview(applicationId) {
            const app = findApplication(applicationId);
            if (!app) return;
            
            showReviewModal(app);
        }
        
        // 显示审核模态框
        function showReviewModal(app) {
            const modal = createModal('商户审核', `
                <div class="review-content">
                    <div class="review-header">
                        <h3 style="margin: 0 0 8px 0;">${app.name}</h3>
                        <p style="margin: 0; color: #64748b;">申请编号: ${app.id}</p>
                    </div>
                    
                    <div class="review-sections">
                        <div class="review-section">
                            <h4>基本信息审核</h4>
                            <div class="review-checklist">
                                <label class="check-item">
                                    <input type="checkbox" checked> 商户名称规范
                                </label>
                                <label class="check-item">
                                    <input type="checkbox" checked> 联系信息真实有效
                                </label>
                                <label class="check-item">
                                    <input type="checkbox"> 主营类目匹配
                                </label>
                            </div>
                        </div>
                        
                        <div class="review-section">
                            <h4>资质文件审核</h4>
                            <div class="review-checklist">
                                <label class="check-item">
                                    <input type="checkbox" checked> 营业执照清晰完整
                                </label>
                                <label class="check-item">
                                    <input type="checkbox"> 法人身份证明
                                </label>
                                <label class="check-item">
                                    <input type="checkbox" checked> 授权委托书（如适用）
                                </label>
                            </div>
                        </div>
                        
                        <div class="review-section">
                            <h4>审核决定</h4>
                            <div class="review-decision">
                                <label class="radio-item">
                                    <input type="radio" name="decision" value="approve"> 
                                    <span class="radio-text">通过审核</span>
                                </label>
                                <label class="radio-item">
                                    <input type="radio" name="decision" value="reject">
                                    <span class="radio-text">拒绝申请</span>
                                </label>
                                <label class="radio-item">
                                    <input type="radio" name="decision" value="pending">
                                    <span class="radio-text">需补充材料</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="review-section">
                            <h4>审核备注</h4>
                            <textarea id="reviewNotes" placeholder="请输入审核意见和建议..." style="width: 100%; min-height: 100px; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; resize: vertical;"></textarea>
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <button onclick="closeModal()" class="btn btn-secondary">取消</button>
                    <button onclick="submitReview('${app.id}')" class="btn btn-primary">提交审核</button>
                </div>
            `);
        }
        
        // 提交审核
        function submitReview(applicationId) {
            const decision = document.querySelector('input[name="decision"]:checked');
            const notes = document.getElementById('reviewNotes').value;
            
            if (!decision) {
                showNotification('请选择审核决定', 'warning');
                return;
            }
            
            const app = findApplication(applicationId);
            if (!app) return;
            
            const newStatus = decision.value === 'approve' ? 'approved' : 
                             decision.value === 'reject' ? 'rejected' : 'pending';
            
            // 更新申请状态
            app.status = newStatus;
            app.reviewNotes = notes;
            app.reviewDate = new Date().toLocaleString();
            
            // 移动到相应数组
            const currentStatus = Object.keys(applications).find(status => 
                applications[status].some(a => a.id === applicationId)
            );
            
            if (currentStatus && currentStatus !== newStatus) {
                moveApplication(applicationId, currentStatus, newStatus);
            }
            
            closeModal();
            
            const statusText = newStatus === 'approved' ? '通过' : 
                              newStatus === 'rejected' ? '拒绝' : '待补充材料';
            
            showNotification(`申请已${statusText}`, newStatus === 'approved' ? 'success' : 
                           newStatus === 'rejected' ? 'error' : 'warning');
            
            // 刷新当前标签页
            loadApplicationsByStatus(currentTab);
        }

        // 快速通过
        function quickApprove(applicationId) {
            if (confirm('确定要快速通过该申请吗？')) {
                const app = findApplication(applicationId);
                if (!app) return;
                
                app.status = 'approved';
                app.reviewDate = new Date().toLocaleString();
                app.reviewer = '系统自动审核';
                
                moveApplication(applicationId, 'pending', 'approved');
                showNotification('申请已通过审核', 'success');
                
                // 刷新当前标签页
                loadApplicationsByStatus(currentTab);
            }
        }

        // 快速拒绝
        function quickReject(applicationId) {
            const reason = prompt('请输入拒绝原因：');
            if (reason) {
                const app = findApplication(applicationId);
                if (!app) return;
                
                app.status = 'rejected';
                app.reviewNotes = reason;
                app.reviewDate = new Date().toLocaleString();
                app.reviewer = '系统快速审核';
                
                moveApplication(applicationId, 'pending', 'rejected');
                showNotification('申请已拒绝', 'error');
                
                // 刷新当前标签页
                loadApplicationsByStatus(currentTab);
            }
        }

        // 重新分配审核员
        function reassignReviewer(applicationId) {
            const reviewers = ['审核员A', '审核员B', '审核员C', '高级审核员'];
            const currentReviewer = findApplication(applicationId)?.reviewer || '';
            
            showReassignModal(applicationId, reviewers, currentReviewer);
        }
        
        // 显示重新分配模态框
        function showReassignModal(applicationId, reviewers, currentReviewer) {
            const modal = createModal('重新分配审核员', `
                <div class="reassign-content">
                    <p style="margin: 0 0 16px 0; color: #64748b;">
                        当前审核员: <strong>${currentReviewer}</strong>
                    </p>
                    
                    <div class="reviewer-list">
                        <h4 style="margin: 0 0 12px 0;">选择新的审核员:</h4>
                        ${reviewers.filter(r => r !== currentReviewer).map(reviewer => `
                            <label class="reviewer-option">
                                <input type="radio" name="newReviewer" value="${reviewer}">
                                <span>${reviewer}</span>
                                <small style="color: #64748b;">当前工作量: ${Math.floor(Math.random() * 10) + 1} 个申请</small>
                            </label>
                        `).join('')}
                    </div>
                    
                    <div style="margin-top: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">分配原因:</label>
                        <textarea id="reassignReason" placeholder="请说明重新分配的原因..." style="width: 100%; min-height: 80px; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px;"></textarea>
                    </div>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <button onclick="closeModal()" class="btn btn-secondary">取消</button>
                    <button onclick="confirmReassign('${applicationId}')" class="btn btn-primary">确认分配</button>
                </div>
            `);
        }
        
        // 确认重新分配
        function confirmReassign(applicationId) {
            const newReviewer = document.querySelector('input[name="newReviewer"]:checked');
            const reason = document.getElementById('reassignReason').value;
            
            if (!newReviewer) {
                showNotification('请选择新的审核员', 'warning');
                return;
            }
            
            const app = findApplication(applicationId);
            if (app) {
                app.reviewer = newReviewer.value;
                app.reassignReason = reason;
                app.reassignDate = new Date().toLocaleString();
            }
            
            closeModal();
            showNotification(`已重新分配给 ${newReviewer.value}`, 'success');
            
            // 刷新当前标签页
            loadApplicationsByStatus(currentTab);
        }
        
        // 其他操作函数
        function viewMerchantStore(applicationId) {
            showNotification('正在打开商户店铺页面...', 'info');
            setTimeout(() => {
                window.open(`merchant-detail.html?id=${applicationId}`, '_blank');
            }, 1000);
        }
        
        function reconsiderApplication(applicationId) {
            if (confirm('确定要重新考虑该申请吗？申请将重新进入审核流程。')) {
                const app = findApplication(applicationId);
                if (!app) return;
                
                app.status = 'pending';
                app.reviewer = null;
                app.reconsiderDate = new Date().toLocaleString();
                
                moveApplication(applicationId, 'rejected', 'pending');
                showNotification('申请已重新进入审核流程', 'info');
                
                // 刷新当前标签页
                loadApplicationsByStatus(currentTab);
            }
        }

        // 关闭审核模态框
        function closeReviewModal() {
            document.getElementById('reviewModal').classList.remove('show');
        }

        // 通过申请
        function approveApplication() {
            console.log('Approve application');
            showNotification('申请已通过审核', 'success');
            closeReviewModal();
        }

        // 拒绝申请
        function rejectApplication() {
            console.log('Reject application');
            showNotification('申请已拒绝', 'error');
            closeReviewModal();
        }

        // 导出审核数据
        function exportReviewData() {
            showExportModal();
        }

        function showExportModal() {
            const modal = createModal('导出审核数据', `
                <div class="export-options">
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">导出格式:</label>
                        <select id="exportFormat" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                            <option value="xlsx">Excel 文件 (.xlsx)</option>
                            <option value="csv">CSV 文件 (.csv)</option>
                            <option value="pdf">PDF 报告 (.pdf)</option>
                        </select>
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">导出范围:</label>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="radio" name="exportRange" value="all" checked>
                                <span>所有申请</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="radio" name="exportRange" value="current">
                                <span>当前标签页</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="radio" name="exportRange" value="filtered">
                                <span>当前筛选结果</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500;">时间范围:</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <input type="date" id="exportStartDate" style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                            <input type="date" id="exportEndDate" style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <button onclick="closeModal()" class="btn btn-secondary">取消</button>
                    <button onclick="executeExport()" class="btn btn-primary">开始导出</button>
                </div>
            `, 'medium');
            
            // 设置默认日期
            const today = new Date();
            const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            document.getElementById('exportStartDate').value = lastMonth.toISOString().split('T')[0];
            document.getElementById('exportEndDate').value = today.toISOString().split('T')[0];
        }
        
        function executeExport() {
            const format = document.getElementById('exportFormat').value;
            const range = document.querySelector('input[name="exportRange"]:checked').value;
            
            closeModal();
            showNotification('正在导出数据...', 'info');
            
            setTimeout(() => {
                showNotification(`${format.toUpperCase()} 文件导出成功！`, 'success');
                
                // 模拟文件下载
                if (format === 'csv') {
                    const csvData = generateCSVData(range);
                    downloadCSV(csvData, `商户审核数据_${new Date().toISOString().split('T')[0]}.csv`);
                }
            }, 2000);
        }
        
        function generateCSVData(range) {
            const headers = ['申请编号', '商户名称', '商户类型', '联系人', '联系电话', '主营类目', '申请时间', '状态'];
            let data = [];
            
            if (range === 'all') {
                for (const status in applications) {
                    data = data.concat(applications[status]);
                }
            } else if (range === 'current') {
                data = applications[currentTab] || [];
            } else {
                data = filteredApplications;
            }
            
            const csvContent = [
                headers.join(','),
                ...data.map(app => [
                    app.id, app.name, app.type, app.contact, 
                    app.phone, app.category, app.date, getStatusText(app.status)
                ].join(','))
            ].join('\n');
            
            return csvContent;
        }
        
        function downloadCSV(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 显示审核设置
        function showReviewSettings() {
            const modal = createModal('审核设置', `
                <div class="settings-content">
                    <div class="setting-section">
                        <h4>自动审核规则</h4>
                        <div class="setting-item">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" checked>
                                <span>启用信用良好商户快速通道</span>
                            </label>
                        </div>
                        <div class="setting-item">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox">
                                <span>自动拒绝黑名单商户</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="setting-section">
                        <h4>审核时限</h4>
                        <div class="setting-item">
                            <label>普通申请处理时限（小时）:</label>
                            <input type="number" value="48" style="width: 100px; padding: 4px 8px; border: 1px solid #d1d5db; border-radius: 4px;">
                        </div>
                        <div class="setting-item">
                            <label>紧急申请处理时限（小时）:</label>
                            <input type="number" value="24" style="width: 100px; padding: 4px 8px; border: 1px solid #d1d5db; border-radius: 4px;">
                        </div>
                    </div>
                    
                    <div class="setting-section">
                        <h4>通知设置</h4>
                        <div class="setting-item">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" checked>
                                <span>审核完成后通知申请人</span>
                            </label>
                        </div>
                        <div class="setting-item">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" checked>
                                <span>超时未处理提醒管理员</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <button onclick="closeModal()" class="btn btn-secondary">取消</button>
                    <button onclick="saveSettings()" class="btn btn-primary">保存设置</button>
                </div>
            `, 'medium');
        }
        
        function saveSettings() {
            closeModal();
            showNotification('设置已保存', 'success');
        }

        // ==================== 辅助函数 ====================
        
        // 查找申请
        function findApplication(applicationId) {
            for (const status in applications) {
                const app = applications[status].find(a => a.id === applicationId);
                if (app) return app;
            }
            return null;
        }
        
        // 移动申请到不同状态
        function moveApplication(applicationId, fromStatus, toStatus) {
            const fromArray = applications[fromStatus];
            const toArray = applications[toStatus];
            
            const appIndex = fromArray.findIndex(a => a.id === applicationId);
            if (appIndex > -1) {
                const app = fromArray.splice(appIndex, 1)[0];
                toArray.push(app);
                
                // 更新标签页计数
                updateTabCounts();
            }
        }
        
        // 更新标签页计数
        function updateTabCounts() {
            document.querySelector('[data-tab="pending"] .badge').textContent = applications.pending.length;
            document.querySelector('[data-tab="reviewing"] .badge').textContent = applications.reviewing.length;
            document.querySelector('[data-tab="approved"] .badge').textContent = applications.approved.length;
            document.querySelector('[data-tab="rejected"] .badge').textContent = applications.rejected.length;
        }

        // ==================== 模态框系统 ====================
        
        function createModal(title, content, size = 'large') {
            const modal = document.createElement('div');
            modal.className = 'custom-modal';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.5); z-index: 1000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px;
            `;
            
            const modalSize = size === 'large' ? 'max-width: 900px;' : 
                             size === 'medium' ? 'max-width: 600px;' : 'max-width: 400px;';
            
            modal.innerHTML = `
                <div class="modal-content" style="background: white; border-radius: 12px; ${modalSize} max-height: 90vh; overflow-y: auto; width: 100%; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);">
                    <div class="modal-header" style="padding: 24px 24px 0 24px; border-bottom: 1px solid #e5e7eb; margin-bottom: 24px;">
                        <h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #1f2937; padding-bottom: 16px;">${title}</h3>
                    </div>
                    <div class="modal-body" style="padding: 0 24px 24px 24px;">
                        ${content}
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            return modal;
        }
        
        function closeModal() {
            const modal = document.querySelector('.custom-modal');
            if (modal) {
                modal.remove();
            }
        }

        // ==================== 页面初始化 ====================
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化加载待审核申请
            loadApplicationsByStatus('pending');
            
            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                .priority-badge {
                    background: #fef3c7;
                    color: #d97706;
                    padding: 2px 6px;
                    border-radius: 10px;
                    font-size: 11px;
                    margin-left: 8px;
                }
                
                .notification-toast {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 24px;
                    border-radius: 8px;
                    color: white;
                    font-weight: 500;
                    z-index: 1001;
                    animation: slideIn 0.3s ease;
                }
                
                .notification-toast.success { background: #10b981; }
                .notification-toast.error { background: #ef4444; }
                .notification-toast.warning { background: #f59e0b; }
                .notification-toast.info { background: #3b82f6; }
                
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                
                .detail-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 16px;
                    margin-top: 12px;
                }
                
                .detail-section {
                    margin-bottom: 24px;
                    padding-bottom: 16px;
                    border-bottom: 1px solid #f1f5f9;
                }
                
                .detail-section:last-child {
                    border-bottom: none;
                }
                
                .detail-section h4 {
                    margin: 0 0 12px 0;
                    color: #1e293b;
                    font-size: 16px;
                }
                
                .review-progress {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                    margin-top: 16px;
                }
                
                .progress-step {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 8px;
                    opacity: 0.5;
                }
                
                .progress-step.completed,
                .progress-step.current {
                    opacity: 1;
                }
                
                .step-icon {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    background: #e5e7eb;
                    color: #6b7280;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 600;
                }
                
                .progress-step.completed .step-icon {
                    background: #10b981;
                    color: white;
                }
                
                .progress-step.current .step-icon {
                    background: #3b82f6;
                    color: white;
                }
                
                .step-text {
                    font-size: 12px;
                    color: #6b7280;
                }
                
                .progress-step.completed .step-text,
                .progress-step.current .step-text {
                    color: #1e293b;
                    font-weight: 500;
                }
                
                .review-checklist {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                    margin-top: 12px;
                }
                
                .check-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    cursor: pointer;
                }
                
                .review-decision {
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                    margin-top: 12px;
                }
                
                .radio-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    cursor: pointer;
                }
                
                .radio-text {
                    font-weight: 500;
                }
                
                .reviewer-option {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    padding: 12px;
                    border: 1px solid #e5e7eb;
                    border-radius: 6px;
                    margin-bottom: 8px;
                    cursor: pointer;
                }
                
                .reviewer-option:hover {
                    background: #f9fafb;
                }
                
                .setting-section {
                    margin-bottom: 24px;
                    padding-bottom: 16px;
                    border-bottom: 1px solid #f1f5f9;
                }
                
                .setting-section:last-child {
                    border-bottom: none;
                }
                
                .setting-section h4 {
                    margin: 0 0 16px 0;
                    color: #1e293b;
                }
                
                .setting-item {
                    margin-bottom: 12px;
                }
                
                .setting-item label {
                    display: block;
                    margin-bottom: 4px;
                    font-weight: 500;
                    color: #374151;
                }
            `;
            document.head.appendChild(style);
        });
