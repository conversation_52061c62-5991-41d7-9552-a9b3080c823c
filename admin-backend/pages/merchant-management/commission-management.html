<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>佣金管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .commission-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.pending { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.paid { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-icon.rate { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
            margin-top: 4px;
        }

        .stat-change {
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 6px;
            margin-top: 8px;
        }

        .stat-change.positive {
            background: #dcfce7;
            color: #166534;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 32px;
        }

        .content-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .section-header {
            padding: 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab {
            padding: 16px 24px;
            cursor: pointer;
            color: #64748b;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
            background: #f8fafc;
        }

        .tab-content {
            padding: 24px;
        }

        .filter-bar {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
            text-transform: uppercase;
        }

        .filter-input, .filter-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            min-width: 140px;
        }

        .commission-table {
            width: 100%;
            border-collapse: collapse;
        }

        .commission-table th {
            background: #f8fafc;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e2e8f0;
        }

        .commission-table td {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .commission-table tr:hover {
            background: #f8fafc;
        }

        .merchant-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .merchant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #64748b;
        }

        .merchant-details h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .merchant-details p {
            margin: 2px 0 0 0;
            font-size: 12px;
            color: #64748b;
        }

        .commission-rate {
            font-size: 16px;
            font-weight: 600;
            color: #059669;
        }

        .amount {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .amount.pending {
            color: #f59e0b;
        }

        .amount.paid {
            color: #10b981;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.paid {
            background: #dcfce7;
            color: #166534;
        }

        .status-badge.processing {
            background: #dbeafe;
            color: #1e40af;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .commission-rules {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            padding: 24px;
        }

        .rules-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }

        .rule-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .rule-item:last-child {
            border-bottom: none;
        }

        .rule-category {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }

        .rule-rate {
            font-size: 14px;
            font-weight: 600;
            color: #059669;
        }

        .settlement-info {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            padding: 24px;
        }

        .settlement-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }

        .settlement-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .settlement-label {
            font-size: 14px;
            color: #64748b;
        }

        .settlement-value {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .settlement-actions {
            margin-top: 20px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 32px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #64748b;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .commission-page {
                padding: 16px;
            }
            
            .filter-bar {
                flex-direction: column;
            }
            
            .commission-table {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="commission-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">佣金管理</h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="openSettlementModal()">
                    <i class="fas fa-calculator"></i>
                    批量结算
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon total">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
                <div class="stat-value">¥156.8K</div>
                <div class="stat-label">总佣金收入</div>
                <div class="stat-change positive">+12% 本月</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value">¥23.4K</div>
                <div class="stat-label">待结算佣金</div>
                <div class="stat-change positive">+8% 本周</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon paid">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-value">¥133.4K</div>
                <div class="stat-label">已结算佣金</div>
                <div class="stat-change positive">+15% 本月</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon rate">
                        <i class="fas fa-percentage"></i>
                    </div>
                </div>
                <div class="stat-value">5.2%</div>
                <div class="stat-label">平均佣金率</div>
                <div class="stat-change positive">+0.3% 本月</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 佣金管理表格 -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">佣金明细</h2>
                    <div class="header-actions">
                        <button class="btn btn-secondary" onclick="exportCommissionData()">
                            <i class="fas fa-download"></i>
                            导出
                        </button>
                    </div>
                </div>

                <!-- 标签页 -->
                <div class="tabs">
                    <div class="tab active" onclick="switchTab('overview')">概览</div>
                    <div class="tab" onclick="switchTab('pending')">待结算</div>
                    <div class="tab" onclick="switchTab('settled')">已结算</div>
                    <div class="tab" onclick="switchTab('settings')">费率设置</div>
                </div>

                <!-- 标签内容 -->
                <div class="tab-content" id="tabContent">
                    <!-- 概览标签 -->
                    <div id="overview-content">
                        <div class="filter-bar">
                            <div class="filter-group">
                                <label class="filter-label">商户名称</label>
                                <input type="text" class="filter-input" placeholder="搜索商户">
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">时间范围</label>
                                <select class="filter-select">
                                    <option value="">全部时间</option>
                                    <option value="today">今日</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">状态</label>
                                <select class="filter-select">
                                    <option value="">全部状态</option>
                                    <option value="pending">待结算</option>
                                    <option value="processing">结算中</option>
                                    <option value="paid">已结算</option>
                                </select>
                            </div>
                            <button class="btn btn-primary" style="align-self: flex-end;">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                        </div>

                        <div style="overflow-x: auto;">
                            <table class="commission-table">
                                <thead>
                                    <tr>
                                        <th>商户信息</th>
                                        <th>佣金率</th>
                                        <th>本月销售额</th>
                                        <th>应收佣金</th>
                                        <th>待结算</th>
                                        <th>已结算</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="commissionTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 佣金规则 -->
                <div class="commission-rules">
                    <h3 class="rules-title">佣金费率</h3>
                    <div class="rule-item">
                        <span class="rule-category">电子数码</span>
                        <span class="rule-rate">3.5%</span>
                    </div>
                    <div class="rule-item">
                        <span class="rule-category">服装鞋帽</span>
                        <span class="rule-rate">5.0%</span>
                    </div>
                    <div class="rule-item">
                        <span class="rule-category">家居用品</span>
                        <span class="rule-rate">4.0%</span>
                    </div>
                    <div class="rule-item">
                        <span class="rule-category">美妆个护</span>
                        <span class="rule-rate">6.0%</span>
                    </div>
                    <div class="rule-item">
                        <span class="rule-category">运动户外</span>
                        <span class="rule-rate">4.5%</span>
                    </div>
                    <button class="btn btn-primary" style="width: 100%; margin-top: 16px;" onclick="editCommissionRules()">
                        <i class="fas fa-edit"></i>
                        编辑费率
                    </button>
                </div>

                <!-- 结算信息 -->
                <div class="settlement-info">
                    <h3 class="settlement-title">本期结算</h3>
                    <div class="settlement-item">
                        <span class="settlement-label">结算周期</span>
                        <span class="settlement-value">每月15日</span>
                    </div>
                    <div class="settlement-item">
                        <span class="settlement-label">下次结算</span>
                        <span class="settlement-value">2024-04-15</span>
                    </div>
                    <div class="settlement-item">
                        <span class="settlement-label">待结算金额</span>
                        <span class="settlement-value">¥23,456</span>
                    </div>
                    <div class="settlement-item">
                        <span class="settlement-label">涉及商户</span>
                        <span class="settlement-value">12个</span>
                    </div>
                    <div class="settlement-actions">
                        <button class="btn btn-success" onclick="previewSettlement()">
                            <i class="fas fa-eye"></i>
                            预览结算
                        </button>
                        <button class="btn btn-primary" onclick="openSettlementModal()">
                            <i class="fas fa-calculator"></i>
                            执行结算
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 结算模态框 -->
    <div id="settlementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">批量结算</h3>
                <button class="modal-close" onclick="closeModal('settlementModal')">&times;</button>
            </div>
            
            <div class="form-group">
                <label class="form-label">结算周期</label>
                <select class="form-select">
                    <option value="current">本期（2024-03-01 至 2024-03-31）</option>
                    <option value="previous">上期（2024-02-01 至 2024-02-29）</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">结算商户</label>
                <select class="form-select">
                    <option value="all">全部商户（12个）</option>
                    <option value="selected">选中商户（5个）</option>
                    <option value="threshold">达到阈值商户（8个）</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">结算金额</label>
                <input type="text" class="form-input" value="¥23,456.78" readonly>
            </div>
            
            <div class="form-group">
                <label class="form-label">结算说明</label>
                <textarea class="form-textarea" placeholder="请输入结算说明..."></textarea>
            </div>
            
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal('settlementModal')">取消</button>
                <button class="btn btn-success" onclick="executeSettlement()">确认结算</button>
            </div>
        </div>
    </div>

    <script>
        // 示例佣金数据
        const commissionData = [
            {
                id: 'M001',
                merchantName: '苹果官方旗舰店',
                logo: '../../assets/images/apple-logo.png',
                commissionRate: 3.5,
                monthlySales: 890000,
                totalCommission: 31150,
                pendingCommission: 8500,
                paidCommission: 22650,
                status: 'pending'
            },
            {
                id: 'M002',
                merchantName: 'Nike运动专营店',
                logo: '../../assets/images/nike-logo.png',
                commissionRate: 5.0,
                monthlySales: 456000,
                totalCommission: 22800,
                pendingCommission: 6200,
                paidCommission: 16600,
                status: 'processing'
            },
            {
                id: 'M003',
                merchantName: '小米科技专卖店',
                logo: '../../assets/images/xiaomi-logo.png',
                commissionRate: 4.0,
                monthlySales: 234000,
                totalCommission: 9360,
                pendingCommission: 2800,
                paidCommission: 6560,
                status: 'paid'
            },
            {
                id: 'M004',
                merchantName: '时尚服饰小店',
                logo: '../../assets/images/fashion-logo.png',
                commissionRate: 6.0,
                monthlySales: 123000,
                totalCommission: 7380,
                pendingCommission: 2100,
                paidCommission: 5280,
                status: 'pending'
            }
        ];

        let currentTab = 'overview';

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadCommissionData();
        });

        // 加载佣金数据
        function loadCommissionData() {
            const tbody = document.getElementById('commissionTableBody');
            tbody.innerHTML = '';

            commissionData.forEach(merchant => {
                const row = createCommissionRow(merchant);
                tbody.appendChild(row);
            });
        }

        // 创建佣金行
        function createCommissionRow(merchant) {
            const row = document.createElement('tr');
            
            const statusClass = {
                'pending': 'pending',
                'processing': 'processing',
                'paid': 'paid'
            }[merchant.status];

            const statusText = {
                'pending': '待结算',
                'processing': '结算中',
                'paid': '已结算'
            }[merchant.status];

            row.innerHTML = `
                <td>
                    <div class="merchant-info">
                        <div class="merchant-avatar">
                            <img src="${merchant.logo}" alt="${merchant.merchantName}" onerror="this.parentElement.innerHTML='<i class=&quot;fas fa-store&quot;></i>'" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
                        </div>
                        <div class="merchant-details">
                            <h4>${merchant.merchantName}</h4>
                            <p>ID: ${merchant.id}</p>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="commission-rate">${merchant.commissionRate}%</span>
                </td>
                <td>
                    <span class="amount">¥${merchant.monthlySales.toLocaleString()}</span>
                </td>
                <td>
                    <span class="amount">¥${merchant.totalCommission.toLocaleString()}</span>
                </td>
                <td>
                    <span class="amount pending">¥${merchant.pendingCommission.toLocaleString()}</span>
                </td>
                <td>
                    <span class="amount paid">¥${merchant.paidCommission.toLocaleString()}</span>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </td>
                <td>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-primary" onclick="viewCommissionDetail('${merchant.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${merchant.status === 'pending' ? `
                            <button class="btn btn-success" onclick="settleCommission('${merchant.id}')">
                                <i class="fas fa-calculator"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            `;

            return row;
        }

        // 切换标签
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签状态
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            
            // 更新内容
            const content = document.getElementById('tabContent');
            switch(tab) {
                case 'overview':
                    content.innerHTML = document.getElementById('overview-content').outerHTML;
                    loadCommissionData();
                    break;
                case 'pending':
                    showPendingCommissions();
                    break;
                case 'settled':
                    showSettledCommissions();
                    break;
                case 'settings':
                    showCommissionSettings();
                    break;
            }
        }

        // 显示待结算佣金
        function showPendingCommissions() {
            const content = document.getElementById('tabContent');
            content.innerHTML = `
                <div class="filter-bar">
                    <div class="filter-group">
                        <label class="filter-label">商户名称</label>
                        <input type="text" class="filter-input" placeholder="搜索商户">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">最小金额</label>
                        <input type="number" class="filter-input" placeholder="最小金额">
                    </div>
                    <button class="btn btn-primary" style="align-self: flex-end;">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                </div>
                <div style="overflow-x: auto;">
                    <table class="commission-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" onchange="toggleSelectAll()"></th>
                                <th>商户信息</th>
                                <th>待结算金额</th>
                                <th>结算周期</th>
                                <th>最后更新</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${commissionData.filter(m => m.status === 'pending').map(merchant => `
                                <tr>
                                    <td><input type="checkbox" value="${merchant.id}"></td>
                                    <td>
                                        <div class="merchant-info">
                                            <div class="merchant-avatar">
                                                <i class="fas fa-store"></i>
                                            </div>
                                            <div class="merchant-details">
                                                <h4>${merchant.merchantName}</h4>
                                                <p>佣金率: ${merchant.commissionRate}%</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="amount pending">¥${merchant.pendingCommission.toLocaleString()}</span></td>
                                    <td>2024-03</td>
                                    <td>2024-03-30</td>
                                    <td>
                                        <button class="btn btn-success" onclick="settleCommission('${merchant.id}')">
                                            <i class="fas fa-calculator"></i>
                                            结算
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                <div style="margin-top: 20px; text-align: right;">
                    <button class="btn btn-success" onclick="batchSettle()">
                        <i class="fas fa-calculator"></i>
                        批量结算选中项
                    </button>
                </div>
            `;
        }

        // 显示已结算佣金
        function showSettledCommissions() {
            const content = document.getElementById('tabContent');
            content.innerHTML = `
                <div class="filter-bar">
                    <div class="filter-group">
                        <label class="filter-label">结算时间</label>
                        <select class="filter-select">
                            <option value="">全部时间</option>
                            <option value="2024-03">2024年3月</option>
                            <option value="2024-02">2024年2月</option>
                            <option value="2024-01">2024年1月</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">商户名称</label>
                        <input type="text" class="filter-input" placeholder="搜索商户">
                    </div>
                    <button class="btn btn-primary" style="align-self: flex-end;">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                </div>
                <div style="overflow-x: auto;">
                    <table class="commission-table">
                        <thead>
                            <tr>
                                <th>商户信息</th>
                                <th>结算金额</th>
                                <th>结算周期</th>
                                <th>结算时间</th>
                                <th>结算单号</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${commissionData.map(merchant => `
                                <tr>
                                    <td>
                                        <div class="merchant-info">
                                            <div class="merchant-avatar">
                                                <i class="fas fa-store"></i>
                                            </div>
                                            <div class="merchant-details">
                                                <h4>${merchant.merchantName}</h4>
                                                <p>佣金率: ${merchant.commissionRate}%</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td><span class="amount paid">¥${merchant.paidCommission.toLocaleString()}</span></td>
                                    <td>2024-02</td>
                                    <td>2024-03-15 10:30</td>
                                    <td>ST202403150001</td>
                                    <td>
                                        <button class="btn btn-secondary" onclick="viewSettlementDetail('${merchant.id}')">
                                            <i class="fas fa-eye"></i>
                                            详情
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // 显示佣金设置
        function showCommissionSettings() {
            const content = document.getElementById('tabContent');
            content.innerHTML = `
                <div style="max-width: 600px;">
                    <h4 style="margin-bottom: 20px;">佣金费率设置</h4>
                    <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 24px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                            <div>
                                <label class="form-label">商品分类</label>
                                <select class="form-select">
                                    <option value="electronics">电子数码</option>
                                    <option value="clothing">服装鞋帽</option>
                                    <option value="home">家居用品</option>
                                    <option value="beauty">美妆个护</option>
                                    <option value="sports">运动户外</option>
                                </select>
                            </div>
                            <div>
                                <label class="form-label">佣金费率 (%)</label>
                                <input type="number" class="form-input" value="3.5" step="0.1" min="0" max="20">
                            </div>
                            <div style="display: flex; align-items: flex-end;">
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    添加
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: white; border: 1px solid #e2e8f0; border-radius: 8px;">
                        <div style="padding: 16px; border-bottom: 1px solid #e2e8f0; font-weight: 600;">
                            当前费率设置
                        </div>
                        <div style="padding: 16px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f1f5f9;">
                                <span>电子数码</span>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="color: #059669; font-weight: 600;">3.5%</span>
                                    <button class="btn btn-secondary" style="padding: 4px 8px;">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; color: #ef4444;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f1f5f9;">
                                <span>服装鞋帽</span>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="color: #059669; font-weight: 600;">5.0%</span>
                                    <button class="btn btn-secondary" style="padding: 4px 8px;">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; color: #ef4444;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f1f5f9;">
                                <span>家居用品</span>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="color: #059669; font-weight: 600;">4.0%</span>
                                    <button class="btn btn-secondary" style="padding: 4px 8px;">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; color: #ef4444;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 24px; text-align: right;">
                        <button class="btn btn-success">
                            <i class="fas fa-save"></i>
                            保存设置
                        </button>
                    </div>
                </div>
            `;
        }

        // 其他功能函数
        function viewCommissionDetail(merchantId) {
            alert(`查看商户 ${merchantId} 的佣金详情`);
        }

        function settleCommission(merchantId) {
            if (confirm('确定要结算该商户的佣金吗？')) {
                alert(`商户 ${merchantId} 佣金结算成功`);
                loadCommissionData();
            }
        }

        function openSettlementModal() {
            document.getElementById('settlementModal').classList.add('show');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        function executeSettlement() {
            alert('批量结算执行成功！');
            closeModal('settlementModal');
            loadCommissionData();
        }

        function previewSettlement() {
            alert('结算预览功能开发中...');
        }

        function editCommissionRules() {
            switchTab('settings');
            document.querySelector('.tab[onclick="switchTab(\'settings\')"]').click();
        }

        function exportCommissionData() {
            alert('导出佣金数据功能开发中...');
        }

        function toggleSelectAll() {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            const selectAll = event.target.checked;
            checkboxes.forEach(cb => cb.checked = selectAll);
        }

        function batchSettle() {
            const selected = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (selected.length === 0) {
                alert('请选择要结算的商户');
                return;
            }
            if (confirm(`确定要结算选中的 ${selected.length} 个商户的佣金吗？`)) {
                alert('批量结算成功！');
                loadCommissionData();
            }
        }

        function viewSettlementDetail(merchantId) {
            alert(`查看商户 ${merchantId} 的结算详情`);
        }
    </script>
</body>
</html> 