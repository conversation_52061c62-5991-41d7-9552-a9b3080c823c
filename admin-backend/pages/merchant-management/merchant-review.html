<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户审核管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="merchant-review.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="review-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-left">
                <h1>商户审核管理</h1>
                <div class="header-stats">
                    <div class="stat-item">
                        <div class="stat-number">23</div>
                        <div class="stat-label">待审核</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">156</div>
                        <div class="stat-label">本月通过</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">12</div>
                        <div class="stat-label">本月拒绝</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">92.5%</div>
                        <div class="stat-label">通过率</div>
                    </div>
                </div>
            </div>
            <div class="page-actions">
                <button class="btn btn-secondary" onclick="exportReviewData()">
                    <i class="fas fa-download"></i>
                    导出数据
                </button>
                <button class="btn btn-primary" onclick="showReviewSettings()">
                    <i class="fas fa-cog"></i>
                    审核设置
                </button>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="review-filters">
            <div class="filter-row">
                <div class="filter-group">
                    <label class="filter-label">申请时间</label>
                    <select class="filter-select" onchange="filterByDate(this.value)">
                        <option value="">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">商户类型</label>
                    <select class="filter-select" onchange="filterByType(this.value)">
                        <option value="">全部类型</option>
                        <option value="enterprise">企业商户</option>
                        <option value="individual">个人商户</option>
                        <option value="flagship">品牌旗舰店</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">主营类目</label>
                    <select class="filter-select" onchange="filterByCategory(this.value)">
                        <option value="">全部类目</option>
                        <option value="electronics">电子数码</option>
                        <option value="clothing">服装鞋帽</option>
                        <option value="home">家居用品</option>
                        <option value="beauty">美妆个护</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">搜索</label>
                    <input type="text" class="filter-input" placeholder="商户名称/联系人" oninput="searchApplications(this.value)">
                </div>
                <button class="btn btn-secondary" onclick="resetFilters()">
                    <i class="fas fa-refresh"></i>
                    重置
                </button>
            </div>
        </div>

        <!-- 审核标签 -->
        <div class="review-tabs">
            <button class="tab-btn active" onclick="switchTab('pending')" data-tab="pending">
                待审核 <span class="badge">23</span>
            </button>
            <button class="tab-btn" onclick="switchTab('reviewing')" data-tab="reviewing">
                审核中 <span class="badge">8</span>
            </button>
            <button class="tab-btn" onclick="switchTab('approved')" data-tab="approved">
                已通过 <span class="badge">156</span>
            </button>
            <button class="tab-btn" onclick="switchTab('rejected')" data-tab="rejected">
                已拒绝 <span class="badge">12</span>
            </button>
        </div>

        <!-- 申请列表 -->
        <div class="applications-container">
            <div class="application-card">
                <div class="card-header">
                    <div class="merchant-info">
                        <h3 class="merchant-name">华为旗舰专卖店</h3>
                        <span class="merchant-type">
                            <i class="fas fa-building"></i>
                            企业商户
                        </span>
                    </div>
                    <div class="application-meta">
                        <div class="application-date">2024-01-15 14:30</div>
                        <div class="application-id">申请编号: MA202401150001</div>
                    </div>
                </div>

                <div class="card-content">
                    <div class="info-group">
                        <span class="info-label">联系人</span>
                        <span class="info-value">张经理</span>
                    </div>
                    <div class="info-group">
                        <span class="info-label">联系电话</span>
                        <span class="info-value">138****8888</span>
                    </div>
                    <div class="info-group">
                        <span class="info-label">主营类目</span>
                        <span class="info-value">电子数码</span>
                    </div>
                    <div class="info-group">
                        <span class="info-label">当前状态</span>
                        <span class="status-badge status-pending">
                            <i class="fas fa-clock"></i>
                            待审核
                        </span>
                    </div>
                    <div class="info-group">
                        <span class="info-label">营业执照</span>
                        <span class="info-value">91110000MA001234XX</span>
                    </div>
                    <div class="info-group">
                        <span class="info-label">经营地址</span>
                        <span class="info-value">北京市朝阳区xxx路xxx号</span>
                    </div>
                </div>

                <div class="progress-indicator">
                    <span class="progress-text">审核进度：</span>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 25%"></div>
                    </div>
                    <span class="progress-text">25%</span>
                </div>

                <div class="card-actions">
                    <button class="btn btn-secondary" onclick="viewApplicationDetail('MA202401150001')">
                        <i class="fas fa-eye"></i>
                        详情
                    </button>
                    <button class="btn btn-primary" onclick="startReview('MA202401150001')">
                        <i class="fas fa-search"></i>
                        开始审核
                    </button>
                    <button class="btn btn-success" onclick="quickApprove('MA202401150001')">
                        <i class="fas fa-check"></i>
                        快速通过
                    </button>
                    <button class="btn btn-danger" onclick="quickReject('MA202401150001')">
                        <i class="fas fa-times"></i>
                        快速拒绝
                    </button>
                </div>
            </div>

            <div class="application-card">
                <div class="card-header">
                    <div class="merchant-info">
                        <h3 class="merchant-name">小米生活馆</h3>
                        <span class="merchant-type">
                            <i class="fas fa-user"></i>
                            个人商户
                        </span>
                    </div>
                    <div class="application-meta">
                        <div class="application-date">2024-01-15 10:20</div>
                        <div class="application-id">申请编号: MA202401150002</div>
                    </div>
                </div>

                <div class="card-content">
                    <div class="info-group">
                        <span class="info-label">联系人</span>
                        <span class="info-value">李先生</span>
                    </div>
                    <div class="info-group">
                        <span class="info-label">联系电话</span>
                        <span class="info-value">139****9999</span>
                    </div>
                    <div class="info-group">
                        <span class="info-label">主营类目</span>
                        <span class="info-value">电子数码</span>
                    </div>
                    <div class="info-group">
                        <span class="info-label">当前状态</span>
                        <span class="status-badge status-reviewing">
                            <i class="fas fa-search"></i>
                            审核中
                        </span>
                    </div>
                    <div class="info-group">
                        <span class="info-label">身份证号</span>
                        <span class="info-value">110101199001011234</span>
                    </div>
                    <div class="info-group">
                        <span class="info-label">审核员</span>
                        <span class="info-value">王审核员</span>
                    </div>
                </div>

                <div class="progress-indicator">
                    <span class="progress-text">审核进度：</span>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 60%"></div>
                    </div>
                    <span class="progress-text">60%</span>
                </div>

                <div class="card-actions">
                    <button class="btn btn-secondary" onclick="viewApplicationDetail('MA202401150002')">
                        <i class="fas fa-eye"></i>
                        详情
                    </button>
                    <button class="btn btn-primary" onclick="continueReview('MA202401150002')">
                        <i class="fas fa-edit"></i>
                        继续审核
                    </button>
                    <button class="btn btn-secondary" onclick="reassignReviewer('MA202401150002')">
                        <i class="fas fa-user-tag"></i>
                        重新分配
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核详情模态框 -->
    <div class="modal-overlay" id="reviewModal">
        <div class="review-modal">
            <div class="modal-header">
                <h3 class="modal-title">商户审核详情</h3>
                <button class="modal-close" onclick="closeReviewModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="review-sections">
                    <!-- 基本信息审核 -->
                    <div class="review-section">
                        <div class="section-header">
                            <span class="section-title">基本信息审核</span>
                            <span class="section-status status-badge status-pending">待审核</span>
                        </div>
                        <div class="section-content">
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <span class="detail-label">商户类型</span>
                                    <span class="detail-value">企业商户</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">店铺名称</span>
                                    <span class="detail-value">华为旗舰专卖店</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">联系人</span>
                                    <span class="detail-value">张经理</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">联系电话</span>
                                    <span class="detail-value">138****8888</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">联系邮箱</span>
                                    <span class="detail-value"><EMAIL></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">经营地址</span>
                                    <span class="detail-value">北京市朝阳区xxx路xxx号</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 资质认证审核 -->
                    <div class="review-section">
                        <div class="section-header">
                            <span class="section-title">资质认证审核</span>
                            <span class="section-status status-badge status-pending">待审核</span>
                        </div>
                        <div class="section-content">
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <span class="detail-label">营业执照号</span>
                                    <span class="detail-value">91110000MA001234XX</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">税务登记号</span>
                                    <span class="detail-value">91110000MA001234XX</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">法人姓名</span>
                                    <span class="detail-value">张三</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">法人身份证</span>
                                    <span class="detail-value">110101199001011234</span>
                                </div>
                            </div>
                            
                            <div style="margin-top: 16px;">
                                <h4 style="margin: 0 0 12px 0; font-size: 14px; color: #374151;">上传文件</h4>
                                <div class="document-grid">
                                    <div class="document-item">
                                        <div class="document-preview">
                                            <i class="fas fa-file-image fa-2x"></i>
                                        </div>
                                        <div class="document-name">营业执照.jpg</div>
                                    </div>
                                    <div class="document-item">
                                        <div class="document-preview">
                                            <i class="fas fa-file-image fa-2x"></i>
                                        </div>
                                        <div class="document-name">身份证正面.jpg</div>
                                    </div>
                                    <div class="document-item">
                                        <div class="document-preview">
                                            <i class="fas fa-file-image fa-2x"></i>
                                        </div>
                                        <div class="document-name">身份证反面.jpg</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 店铺设置审核 -->
                    <div class="review-section">
                        <div class="section-header">
                            <span class="section-title">店铺设置审核</span>
                            <span class="section-status status-badge status-pending">待审核</span>
                        </div>
                        <div class="section-content">
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <span class="detail-label">主营类目</span>
                                    <span class="detail-value">电子数码</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">期望佣金</span>
                                    <span class="detail-value">5%</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">客服电话</span>
                                    <span class="detail-value">400-123-4567</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">客服邮箱</span>
                                    <span class="detail-value"><EMAIL></span>
                                </div>
                            </div>
                            
                            <div style="margin-top: 16px;">
                                <h4 style="margin: 0 0 12px 0; font-size: 14px; color: #374151;">店铺简介</h4>
                                <p style="margin: 0; color: #64748b; line-height: 1.6;">
                                    华为官方授权专卖店，专业销售华为手机、平板、笔记本等数码产品，提供正品保障和专业售后服务。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审核表单 -->
                <div class="review-form">
                    <h4 style="margin: 0 0 16px 0; color: #1e293b;">审核意见</h4>
                    <div class="form-group">
                        <label class="form-label">审核结果</label>
                        <select class="filter-select" style="width: 200px;">
                            <option value="">请选择审核结果</option>
                            <option value="approve">通过</option>
                            <option value="reject">拒绝</option>
                            <option value="pending">需补充材料</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">审核备注</label>
                        <textarea class="form-textarea" placeholder="请输入审核意见和建议..."></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeReviewModal()">取消</button>
                <button class="btn btn-danger" onclick="rejectApplication()">
                    <i class="fas fa-times"></i>
                    拒绝申请
                </button>
                <button class="btn btn-success" onclick="approveApplication()">
                    <i class="fas fa-check"></i>
                    通过审核
                </button>
            </div>
        </div>
    </div>

</body>
<script src="merchant-review.js"></script>
</html> 
