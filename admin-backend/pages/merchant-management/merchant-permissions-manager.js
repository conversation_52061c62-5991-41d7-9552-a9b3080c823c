/**
 * 商家权限管理完整功能类
 * 包含权限矩阵、批量操作、审批流程、模板管理等功能
 */

class MerchantPermissionManager {
    constructor() {
        this.selectedMerchants = new Set();
        this.currentMerchant = null;
        this.hasChanges = false;
        this.permissionDependencies = {
            'add_products': ['view_products'],
            'edit_products': ['view_products'],
            'delete_products': ['edit_products'],
            'manage_inventory': ['view_products'],
            'bulk_operations': ['edit_products'],
            'process_orders': ['view_orders'],
            'cancel_orders': ['process_orders'],
            'refund_orders': ['process_orders'],
            'export_orders': ['view_orders'],
            'edit_customers': ['view_customers'],
            'export_customers': ['view_customers'],
            'customer_service': ['view_customers'],
            'basic_marketing': ['view_marketing'],
            'advanced_marketing': ['basic_marketing'],
            'create_ads': ['advanced_marketing'],
            'coupon_management': ['basic_marketing'],
            'export_analytics': ['view_analytics'],
            'advanced_analytics': ['view_analytics'],
            'manage_finance': ['view_finance'],
            'financial_reports': ['view_finance'],
            'api_access': ['advanced_features'],
            'webhook_management': ['api_access']
        };
        
        this.permissionCategories = {
            'products': {
                name: '商品管理',
                icon: 'fas fa-box',
                color: '#10b981',
                permissions: [
                    { id: 'view_products', name: '查看商品', description: '浏览商品列表和详情', required: true },
                    { id: 'add_products', name: '添加商品', description: '新增商品到店铺', depends: ['view_products'] },
                    { id: 'edit_products', name: '编辑商品', description: '修改商品信息', depends: ['view_products'] },
                    { id: 'delete_products', name: '删除商品', description: '删除店铺商品', depends: ['edit_products'] },
                    { id: 'manage_inventory', name: '库存管理', description: '管理商品库存', depends: ['view_products'] },
                    { id: 'bulk_operations', name: '批量操作', description: '批量编辑商品', depends: ['edit_products'] }
                ]
            },
            'orders': {
                name: '订单管理',
                icon: 'fas fa-shopping-cart',
                color: '#3b82f6',
                permissions: [
                    { id: 'view_orders', name: '查看订单', description: '浏览订单列表和详情', required: true },
                    { id: 'process_orders', name: '处理订单', description: '处理订单状态', depends: ['view_orders'] },
                    { id: 'cancel_orders', name: '取消订单', description: '取消订单操作', depends: ['process_orders'] },
                    { id: 'refund_orders', name: '退款处理', description: '处理退款申请', depends: ['process_orders'], requiresApproval: true },
                    { id: 'export_orders', name: '导出订单', description: '导出订单数据', depends: ['view_orders'] }
                ]
            },
            'customers': {
                name: '客户管理',
                icon: 'fas fa-users',
                color: '#8b5cf6',
                permissions: [
                    { id: 'view_customers', name: '查看客户', description: '浏览客户信息' },
                    { id: 'edit_customers', name: '编辑客户', description: '修改客户信息', depends: ['view_customers'] },
                    { id: 'export_customers', name: '导出客户', description: '导出客户数据', depends: ['view_customers'], requiresApproval: true },
                    { id: 'customer_service', name: '客服功能', description: '客户服务工具', depends: ['view_customers'] }
                ]
            },
            'marketing': {
                name: '营销推广',
                icon: 'fas fa-bullhorn',
                color: '#f59e0b',
                permissions: [
                    { id: 'view_marketing', name: '查看营销数据', description: '浏览营销统计' },
                    { id: 'basic_marketing', name: '基础营销', description: '基础营销工具', depends: ['view_marketing'] },
                    { id: 'advanced_marketing', name: '高级营销', description: '高级营销工具', depends: ['basic_marketing'], requiresApproval: true },
                    { id: 'create_ads', name: '创建广告', description: '创建推广广告', depends: ['advanced_marketing'], requiresApproval: true },
                    { id: 'coupon_management', name: '优惠券管理', description: '管理优惠券', depends: ['basic_marketing'] }
                ]
            },
            'analytics': {
                name: '数据分析',
                icon: 'fas fa-chart-bar',
                color: '#ef4444',
                permissions: [
                    { id: 'view_analytics', name: '查看分析', description: '浏览数据报表' },
                    { id: 'export_analytics', name: '导出分析', description: '导出分析数据', depends: ['view_analytics'] },
                    { id: 'advanced_analytics', name: '高级分析', description: '高级数据分析', depends: ['view_analytics'], requiresApproval: true }
                ]
            },
            'finance': {
                name: '财务管理',
                icon: 'fas fa-coins',
                color: '#06b6d4',
                permissions: [
                    { id: 'view_finance', name: '查看财务', description: '查看财务数据' },
                    { id: 'manage_finance', name: '财务管理', description: '管理财务操作', depends: ['view_finance'], requiresApproval: true },
                    { id: 'financial_reports', name: '财务报表', description: '生成财务报表', depends: ['view_finance'] }
                ]
            },
            'system': {
                name: '系统功能',
                icon: 'fas fa-cogs',
                color: '#6b7280',
                permissions: [
                    { id: 'advanced_features', name: '高级功能', description: '访问高级功能' },
                    { id: 'api_access', name: 'API访问', description: 'API接口权限', depends: ['advanced_features'], requiresApproval: true },
                    { id: 'webhook_management', name: 'Webhook管理', description: '管理Webhook', depends: ['api_access'], requiresApproval: true }
                ]
            }
        };

        this.permissionTemplates = {
            'basic': {
                name: '基础商户',
                description: '适用于新入驻商户',
                permissions: ['view_products', 'add_products', 'edit_products', 'manage_inventory', 'view_orders', 'process_orders']
            },
            'standard': {
                name: '标准商户',
                description: '适用于运营稳定的商户',
                permissions: ['view_products', 'add_products', 'edit_products', 'delete_products', 'manage_inventory', 'bulk_operations', 'view_orders', 'process_orders', 'cancel_orders', 'export_orders', 'view_customers', 'customer_service', 'view_marketing', 'basic_marketing']
            },
            'premium': {
                name: '高级商户',
                description: '适用于大型商户',
                permissions: ['view_products', 'add_products', 'edit_products', 'delete_products', 'manage_inventory', 'bulk_operations', 'view_orders', 'process_orders', 'cancel_orders', 'refund_orders', 'export_orders', 'view_customers', 'edit_customers', 'customer_service', 'view_marketing', 'basic_marketing', 'advanced_marketing', 'coupon_management', 'view_analytics', 'export_analytics']
            },
            'enterprise': {
                name: '企业商户',
                description: '适用于企业级商户',
                permissions: ['*'] // 所有权限
            }
        };

        this.merchants = [];
        this.pendingApprovals = new Map();
        this.init();
    }

    init() {
        this.loadMerchants();
        this.setupEventListeners();
        this.updateMerchantCount();
    }

    setupEventListeners() {
        // 商户选择事件
        document.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.closest('.merchant-item')) {
                this.handleMerchantCheckboxChange(e.target);
            }
        });

        // 权限复选框事件
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('permission-checkbox')) {
                this.handlePermissionChange(e.target);
            }
        });

        // 分类全选事件
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('category-toggle-checkbox')) {
                this.handleCategoryToggle(e.target);
            }
        });

        // 商户项点击事件
        document.addEventListener('click', (e) => {
            const merchantItem = e.target.closest('.merchant-item');
            if (merchantItem && !e.target.type === 'checkbox') {
                this.selectMerchant(merchantItem.dataset.merchantId);
            }
        });

        // 模态框关闭事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay') || e.target.classList.contains('modal-close')) {
                this.closeModal();
            }
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    loadMerchants() {
        // 模拟商户数据
        this.merchants = [
            {
                id: 'merchant_001',
                name: '星辰数码专营店',
                type: 'premium',
                status: 'active',
                joinDate: '2024-01-15',
                permissions: ['view_products', 'add_products', 'edit_products', 'manage_inventory', 'view_orders', 'process_orders', 'view_analytics'],
                pendingApprovals: ['refund_orders']
            },
            {
                id: 'merchant_002',
                name: '时尚生活馆',
                type: 'standard',
                status: 'active',
                joinDate: '2024-02-20',
                permissions: ['view_products', 'add_products', 'edit_products', 'view_orders', 'process_orders', 'view_customers'],
                pendingApprovals: []
            },
            {
                id: 'merchant_003',
                name: '优品食材供应',
                type: 'basic',
                status: 'pending',
                joinDate: '2024-03-10',
                permissions: ['view_products', 'view_orders'],
                pendingApprovals: ['add_products', 'edit_products']
            },
            {
                id: 'merchant_004',
                name: '科技创新工厂',
                type: 'enterprise',
                status: 'active',
                joinDate: '2023-12-01',
                permissions: ['*'],
                pendingApprovals: []
            },
            {
                id: 'merchant_005',
                name: '绿色农产品合作社',
                type: 'standard',
                status: 'suspended',
                joinDate: '2024-01-25',
                permissions: ['view_products', 'view_orders'],
                pendingApprovals: []
            }
        ];

        this.renderMerchantsList();
    }

    renderMerchantsList() {
        const container = document.getElementById('merchantsList');
        if (!container) return;

        container.innerHTML = this.merchants.map(merchant => `
            <div class="merchant-item" data-merchant-id="${merchant.id}">
                <div class="merchant-info">
                    <div class="merchant-name">${merchant.name}</div>
                    <div class="merchant-details">ID: ${merchant.id} | 加入: ${merchant.joinDate}</div>
                    <div class="merchant-badges">
                        <span class="merchant-status ${merchant.status}">${this.getStatusText(merchant.status)}</span>
                        <span class="merchant-type">${this.getTypeText(merchant.type)}</span>
                        ${merchant.pendingApprovals.length > 0 ? `<span class="merchant-status pending">${merchant.pendingApprovals.length}待审</span>` : ''}
                    </div>
                </div>
                <input type="checkbox" class="merchant-checkbox" value="${merchant.id}">
            </div>
        `).join('');
    }

    selectMerchant(merchantId) {
        // 移除之前的选中状态
        document.querySelectorAll('.merchant-item').forEach(item => {
            item.classList.remove('active');
        });

        // 添加当前选中状态
        const merchantItem = document.querySelector(`[data-merchant-id="${merchantId}"]`);
        if (merchantItem) {
            merchantItem.classList.add('active');
        }

        this.currentMerchant = this.merchants.find(m => m.id === merchantId);
        this.showMerchantPermissions();
    }

    showMerchantPermissions() {
        if (!this.currentMerchant) return;

        // 更新商户信息显示
        const merchantInfo = document.getElementById('selectedMerchantInfo');
        const merchantAvatar = document.getElementById('merchantAvatar');
        const merchantName = document.getElementById('merchantName');
        const merchantDesc = document.getElementById('merchantDesc');
        const merchantStats = document.getElementById('merchantStats');

        if (merchantInfo) {
            merchantInfo.style.display = 'flex';
            merchantAvatar.textContent = this.currentMerchant.name.charAt(0);
            merchantName.textContent = this.currentMerchant.name;
            merchantDesc.textContent = `${this.getTypeText(this.currentMerchant.type)} | ${this.getStatusText(this.currentMerchant.status)}`;
            merchantStats.style.display = 'block';
        }

        // 渲染权限矩阵
        this.renderPermissionMatrix();
        this.updatePermissionStats();
    }

    renderPermissionMatrix() {
        const container = document.getElementById('permissionsMatrix');
        if (!container || !this.currentMerchant) return;

        const html = Object.entries(this.permissionCategories).map(([categoryId, category]) => {
            const enabledCount = category.permissions.filter(p => 
                this.currentMerchant.permissions.includes(p.id) || 
                this.currentMerchant.permissions.includes('*')
            ).length;

            return `
                <div class="permission-category">
                    <div class="category-header">
                        <div class="category-title">
                            <div class="category-icon" style="background: ${category.color};">
                                <i class="${category.icon}"></i>
                            </div>
                            <span>${category.name}</span>
                            <small>(${enabledCount}/${category.permissions.length})</small>
                        </div>
                        <div class="category-toggle">
                            <input type="checkbox" class="category-toggle-checkbox" 
                                   data-category="${categoryId}" 
                                   ${enabledCount === category.permissions.length ? 'checked' : ''}>
                            <span>全选</span>
                        </div>
                    </div>
                    <div class="permissions-grid">
                        ${category.permissions.map(permission => this.renderPermissionItem(permission, categoryId)).join('')}
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = html;
    }

    renderPermissionItem(permission, categoryId) {
        const isEnabled = this.currentMerchant.permissions.includes(permission.id) || 
                         this.currentMerchant.permissions.includes('*');
        const isPending = this.currentMerchant.pendingApprovals.includes(permission.id);
        const isBlocked = this.isPermissionBlocked(permission);

        let statusIcon = '';
        if (isPending) {
            statusIcon = '<div class="approval-status pending">⏳</div>';
        } else if (permission.requiresApproval && isEnabled) {
            statusIcon = '<div class="approval-status approved">✓</div>';
        }

        return `
            <div class="permission-item ${isBlocked ? 'disabled' : ''}" data-permission="${permission.id}">
                ${statusIcon}
                <div class="permission-header">
                    <div class="permission-name">${permission.name}</div>
                    <input type="checkbox" class="permission-checkbox" 
                           data-permission="${permission.id}" 
                           data-category="${categoryId}"
                           ${isEnabled ? 'checked' : ''} 
                           ${isBlocked ? 'disabled' : ''}>
                </div>
                <div class="permission-description">${permission.description}</div>
                <div class="permission-meta">
                    ${permission.required ? '<span class="permission-tag required">必需</span>' : ''}
                    ${permission.requiresApproval ? '<span class="permission-tag approval">需审批</span>' : ''}
                    ${permission.depends ? '<span class="permission-tag dependency">有依赖</span>' : ''}
                </div>
            </div>
        `;
    }

    isPermissionBlocked(permission) {
        if (!permission.depends) return false;
        
        return permission.depends.some(depId => {
            return !this.currentMerchant.permissions.includes(depId) && 
                   !this.currentMerchant.permissions.includes('*');
        });
    }

    handleMerchantCheckboxChange(checkbox) {
        const merchantId = checkbox.value;
        
        if (checkbox.checked) {
            this.selectedMerchants.add(merchantId);
        } else {
            this.selectedMerchants.delete(merchantId);
        }

        this.updateBulkActionsBar();
    }

    handlePermissionChange(checkbox) {
        const permissionId = checkbox.dataset.permission;
        const isChecked = checkbox.checked;

        if (isChecked) {
            // 检查依赖
            if (!this.checkPermissionDependencies(permissionId)) {
                checkbox.checked = false;
                this.showNotification('请先启用相关依赖权限', 'warning');
                return;
            }

            // 检查是否需要审批
            const permission = this.findPermissionById(permissionId);
            if (permission && permission.requiresApproval) {
                this.submitForApproval(permissionId, 'enable');
                checkbox.checked = false;
                return;
            }

            this.currentMerchant.permissions.push(permissionId);
        } else {
            // 检查是否有其他权限依赖这个权限
            const dependentPermissions = this.findDependentPermissions(permissionId);
            if (dependentPermissions.length > 0) {
                if (!confirm(`禁用此权限将同时禁用依赖它的权限：${dependentPermissions.join(', ')}。确定继续吗？`)) {
                    checkbox.checked = true;
                    return;
                }
                
                // 同时禁用依赖权限
                dependentPermissions.forEach(depPermission => {
                    this.currentMerchant.permissions = this.currentMerchant.permissions.filter(p => p !== depPermission);
                    const depCheckbox = document.querySelector(`[data-permission="${depPermission}"]`);
                    if (depCheckbox) depCheckbox.checked = false;
                });
            }

            this.currentMerchant.permissions = this.currentMerchant.permissions.filter(p => p !== permissionId);
        }

        this.markAsChanged();
        this.updatePermissionStats();
        this.updateCategoryToggles();
    }

    handleCategoryToggle(checkbox) {
        const categoryId = checkbox.dataset.category;
        const category = this.permissionCategories[categoryId];
        const isChecked = checkbox.checked;

        category.permissions.forEach(permission => {
            const permissionCheckbox = document.querySelector(`[data-permission="${permission.id}"]`);
            if (permissionCheckbox && !permissionCheckbox.disabled) {
                permissionCheckbox.checked = isChecked;
                this.handlePermissionChange(permissionCheckbox);
            }
        });
    }

    checkPermissionDependencies(permissionId) {
        const dependencies = this.permissionDependencies[permissionId];
        if (!dependencies) return true;

        return dependencies.every(depId => 
            this.currentMerchant.permissions.includes(depId) || 
            this.currentMerchant.permissions.includes('*')
        );
    }

    findDependentPermissions(permissionId) {
        return Object.entries(this.permissionDependencies)
            .filter(([_, deps]) => deps.includes(permissionId))
            .map(([permId, _]) => permId)
            .filter(permId => this.currentMerchant.permissions.includes(permId));
    }

    findPermissionById(permissionId) {
        for (const category of Object.values(this.permissionCategories)) {
            const permission = category.permissions.find(p => p.id === permissionId);
            if (permission) return permission;
        }
        return null;
    }

    submitForApproval(permissionId, action) {
        if (!this.currentMerchant.pendingApprovals.includes(permissionId)) {
            this.currentMerchant.pendingApprovals.push(permissionId);
        }

        const permission = this.findPermissionById(permissionId);
        this.showNotification(`${permission.name} 已提交审批申请`, 'info');
        this.renderPermissionMatrix();
    }

    updateBulkActionsBar() {
        const bulkActions = document.getElementById('bulkActions');
        const selectedCount = document.getElementById('selectedCount');
        
        if (this.selectedMerchants.size > 0) {
            bulkActions.classList.add('show');
            selectedCount.textContent = this.selectedMerchants.size;
        } else {
            bulkActions.classList.remove('show');
        }
    }

    updatePermissionStats() {
        if (!this.currentMerchant) return;

        const totalPermissions = Object.values(this.permissionCategories)
            .reduce((sum, category) => sum + category.permissions.length, 0);
        
        const enabledCount = this.currentMerchant.permissions.includes('*') ? 
            totalPermissions : this.currentMerchant.permissions.length;
        
        const pendingCount = this.currentMerchant.pendingApprovals.length;

        document.getElementById('totalPermissions').textContent = totalPermissions;
        document.getElementById('enabledPermissions').textContent = enabledCount;
        document.getElementById('pendingPermissions').textContent = pendingCount;
    }

    updateCategoryToggles() {
        Object.entries(this.permissionCategories).forEach(([categoryId, category]) => {
            const enabledCount = category.permissions.filter(p => 
                this.currentMerchant.permissions.includes(p.id) || 
                this.currentMerchant.permissions.includes('*')
            ).length;

            const toggle = document.querySelector(`[data-category="${categoryId}"]`);
            if (toggle) {
                toggle.checked = enabledCount === category.permissions.length;
                toggle.indeterminate = enabledCount > 0 && enabledCount < category.permissions.length;
            }
        });
    }

    updateMerchantCount() {
        const countElement = document.getElementById('merchantCount');
        if (countElement) {
            countElement.textContent = this.merchants.length;
        }
    }

    // 工具函数
    getStatusText(status) {
        const statusMap = {
            'active': '正常营业',
            'pending': '待审核',
            'suspended': '已暂停',
            'rejected': '已拒绝'
        };
        return statusMap[status] || status;
    }

    getTypeText(type) {
        const typeMap = {
            'basic': '基础版',
            'standard': '标准版',
            'premium': '高级版',
            'enterprise': '企业版'
        };
        return typeMap[type] || type;
    }

    // 批量操作
    bulkEnablePermissions() {
        if (this.selectedMerchants.size === 0) {
            this.showNotification('请先选择商户', 'warning');
            return;
        }

        const permissions = prompt('请输入要启用的权限ID（多个用逗号分隔）：');
        if (!permissions) return;

        const permissionIds = permissions.split(',').map(p => p.trim());
        this.selectedMerchants.forEach(merchantId => {
            const merchant = this.merchants.find(m => m.id === merchantId);
            if (merchant) {
                permissionIds.forEach(permId => {
                    if (!merchant.permissions.includes(permId)) {
                        merchant.permissions.push(permId);
                    }
                });
            }
        });

        this.showNotification(`已为${this.selectedMerchants.size}个商户启用权限`, 'success');
        this.markAsChanged();
        if (this.currentMerchant) {
            this.renderPermissionMatrix();
        }
    }

    bulkDisablePermissions() {
        if (this.selectedMerchants.size === 0) {
            this.showNotification('请先选择商户', 'warning');
            return;
        }

        const permissions = prompt('请输入要禁用的权限ID（多个用逗号分隔）：');
        if (!permissions) return;

        const permissionIds = permissions.split(',').map(p => p.trim());
        this.selectedMerchants.forEach(merchantId => {
            const merchant = this.merchants.find(m => m.id === merchantId);
            if (merchant) {
                permissionIds.forEach(permId => {
                    merchant.permissions = merchant.permissions.filter(p => p !== permId);
                });
            }
        });

        this.showNotification(`已为${this.selectedMerchants.size}个商户禁用权限`, 'success');
        this.markAsChanged();
        if (this.currentMerchant) {
            this.renderPermissionMatrix();
        }
    }

    bulkApplyTemplate() {
        if (this.selectedMerchants.size === 0) {
            this.showNotification('请先选择商户', 'warning');
            return;
        }

        this.showTemplateSelector();
    }

    bulkApprove() {
        if (this.selectedMerchants.size === 0) {
            this.showNotification('请先选择商户', 'warning');
            return;
        }

        let totalApprovals = 0;
        this.selectedMerchants.forEach(merchantId => {
            const merchant = this.merchants.find(m => m.id === merchantId);
            if (merchant && merchant.pendingApprovals.length > 0) {
                merchant.pendingApprovals.forEach(permissionId => {
                    merchant.permissions.push(permissionId);
                });
                totalApprovals += merchant.pendingApprovals.length;
                merchant.pendingApprovals = [];
            }
        });

        if (totalApprovals > 0) {
            this.showNotification(`已批准${totalApprovals}个权限申请`, 'success');
            this.markAsChanged();
            if (this.currentMerchant) {
                this.renderPermissionMatrix();
            }
        } else {
            this.showNotification('没有待审批的权限', 'info');
        }
    }

    // 搜索和筛选
    searchMerchants(keyword) {
        const items = document.querySelectorAll('.merchant-item');
        items.forEach(item => {
            const name = item.querySelector('.merchant-name').textContent.toLowerCase();
            const id = item.dataset.merchantId.toLowerCase();
            
            if (name.includes(keyword.toLowerCase()) || id.includes(keyword.toLowerCase())) {
                item.style.display = 'block';
            } else {
                item.style.display = keyword ? 'none' : 'block';
            }
        });
    }

    filterMerchants(filter) {
        const items = document.querySelectorAll('.merchant-item');
        items.forEach(item => {
            const merchantId = item.dataset.merchantId;
            const merchant = this.merchants.find(m => m.id === merchantId);
            
            let show = true;
            switch (filter) {
                case 'active':
                    show = merchant.status === 'active';
                    break;
                case 'pending':
                    show = merchant.status === 'pending' || merchant.pendingApprovals.length > 0;
                    break;
                case 'premium':
                    show = merchant.type === 'premium' || merchant.type === 'enterprise';
                    break;
                case 'all':
                default:
                    show = true;
            }
            
            item.style.display = show ? 'block' : 'none';
        });
    }

    // 模板相关
    showTemplateSelector() {
        const modalContent = `
            <div class="modal-header">
                <h3 class="modal-title">选择权限模板</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="template-list">
                    ${Object.entries(this.permissionTemplates).map(([templateId, template]) => `
                        <div class="template-item" data-template="${templateId}">
                            <div class="template-header">
                                <h4>${template.name}</h4>
                                <button class="btn btn-sm btn-primary" onclick="merchantPermissionManager.applyTemplate('${templateId}')">
                                    应用模板
                                </button>
                            </div>
                            <p>${template.description}</p>
                            <div class="template-permissions">
                                <strong>包含权限：</strong>
                                ${template.permissions.includes('*') ? '所有权限' : `${template.permissions.length}个权限`}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        this.showModal(modalContent);
    }

    applyTemplate(templateId) {
        const template = this.permissionTemplates[templateId];
        if (!template) return;

        if (this.selectedMerchants.size > 0) {
            // 批量应用
            this.selectedMerchants.forEach(merchantId => {
                const merchant = this.merchants.find(m => m.id === merchantId);
                if (merchant) {
                    if (template.permissions.includes('*')) {
                        merchant.permissions = ['*'];
                    } else {
                        merchant.permissions = [...template.permissions];
                    }
                }
            });
            this.showNotification(`已为${this.selectedMerchants.size}个商户应用${template.name}模板`, 'success');
        } else if (this.currentMerchant) {
            // 单个应用
            if (template.permissions.includes('*')) {
                this.currentMerchant.permissions = ['*'];
            } else {
                this.currentMerchant.permissions = [...template.permissions];
            }
            this.showNotification(`已为${this.currentMerchant.name}应用${template.name}模板`, 'success');
            this.renderPermissionMatrix();
        }

        this.markAsChanged();
        this.closeModal();
    }

    // 保存和重置
    savePermissions() {
        // 模拟保存操作
        console.log('保存权限配置:', this.merchants);
        
        this.showNotification('权限配置已保存', 'success');
        this.hasChanges = false;
        this.hideChangesIndicator();
    }

    resetChanges() {
        if (confirm('确定要重置所有未保存的更改吗？')) {
            this.loadMerchants();
            this.showNotification('已重置更改', 'info');
            this.hasChanges = false;
            this.hideChangesIndicator();
            
            if (this.currentMerchant) {
                this.currentMerchant = this.merchants.find(m => m.id === this.currentMerchant.id);
                this.renderPermissionMatrix();
            }
        }
    }

    // 导出功能
    exportPermissions() {
        const data = {
            merchants: this.merchants,
            exportTime: new Date().toISOString(),
            exportBy: 'admin'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `merchant-permissions-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        this.showNotification('权限配置已导出', 'success');
    }

    // 模态框相关
    showModal(content) {
        const modal = document.getElementById('modalOverlay');
        const modalContent = document.getElementById('modalContent');
        
        modalContent.innerHTML = content;
        modal.classList.add('show');
    }

    closeModal() {
        const modal = document.getElementById('modalOverlay');
        modal.classList.remove('show');
    }

    showCompareModal() {
        // 权限对比功能
        this.showNotification('权限对比功能开发中...', 'info');
    }

    showApprovalModal() {
        // 审批中心功能
        const pendingApprovals = this.merchants.reduce((acc, merchant) => {
            merchant.pendingApprovals.forEach(permissionId => {
                acc.push({
                    merchantId: merchant.id,
                    merchantName: merchant.name,
                    permissionId,
                    permissionName: this.findPermissionById(permissionId)?.name || permissionId,
                    submitTime: new Date().toISOString()
                });
            });
            return acc;
        }, []);

        const modalContent = `
            <div class="modal-header">
                <h3 class="modal-title">审批中心</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                ${pendingApprovals.length === 0 ? 
                    '<p>没有待审批的权限申请</p>' :
                    `<div class="approval-list">
                        ${pendingApprovals.map(approval => `
                            <div class="approval-item">
                                <div class="approval-info">
                                    <h4>${approval.merchantName}</h4>
                                    <p>申请权限：${approval.permissionName}</p>
                                    <small>提交时间：${new Date(approval.submitTime).toLocaleString()}</small>
                                </div>
                                <div class="approval-actions">
                                    <button class="btn btn-sm btn-success" onclick="merchantPermissionManager.approvePermission('${approval.merchantId}', '${approval.permissionId}')">
                                        批准
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="merchantPermissionManager.rejectPermission('${approval.merchantId}', '${approval.permissionId}')">
                                        拒绝
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>`
                }
            </div>
        `;

        this.showModal(modalContent);
    }

    approvePermission(merchantId, permissionId) {
        const merchant = this.merchants.find(m => m.id === merchantId);
        if (merchant) {
            merchant.permissions.push(permissionId);
            merchant.pendingApprovals = merchant.pendingApprovals.filter(p => p !== permissionId);
            
            this.showNotification('权限申请已批准', 'success');
            this.markAsChanged();
            
            if (this.currentMerchant && this.currentMerchant.id === merchantId) {
                this.renderPermissionMatrix();
            }
            
            this.closeModal();
        }
    }

    rejectPermission(merchantId, permissionId) {
        const merchant = this.merchants.find(m => m.id === merchantId);
        if (merchant) {
            merchant.pendingApprovals = merchant.pendingApprovals.filter(p => p !== permissionId);
            
            this.showNotification('权限申请已拒绝', 'warning');
            this.markAsChanged();
            
            if (this.currentMerchant && this.currentMerchant.id === merchantId) {
                this.renderPermissionMatrix();
            }
            
            this.closeModal();
        }
    }

    // 变更管理
    markAsChanged() {
        this.hasChanges = true;
        this.showChangesIndicator();
    }

    showChangesIndicator() {
        const indicator = document.getElementById('changesIndicator');
        if (indicator) {
            indicator.classList.add('show');
        }
    }

    hideChangesIndicator() {
        const indicator = document.getElementById('changesIndicator');
        if (indicator) {
            indicator.classList.remove('show');
        }
    }

    // 通知系统
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // 显示通知
        setTimeout(() => notification.classList.add('show'), 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // 其他模态框功能的占位方法
    showTemplateModal() {
        this.showTemplateSelector();
    }

    showCreateTemplateModal() {
        this.showNotification('创建模板功能开发中...', 'info');
    }

    showDependencyInfo() {
        this.showNotification('权限依赖信息功能开发中...', 'info');
    }

    exportLogs() {
        this.showNotification('操作日志导出功能开发中...', 'info');
    }
} 