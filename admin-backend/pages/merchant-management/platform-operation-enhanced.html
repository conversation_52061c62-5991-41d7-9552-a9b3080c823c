<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平台运营管理中心 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* CSS变量定义 */
        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 12px;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* 全局样式 */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, var(--gray-50) 0%, #e8f2ff 100%);
            color: var(--gray-900);
            line-height: 1.6;
        }

        .operation-page {
            padding: 32px;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: linear-gradient(135deg, white 0%, #f8fafc 100%);
            padding: 32px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 32px;
            border: 1px solid var(--gray-200);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .page-title {
            font-size: 36px;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 0;
        }

        .page-subtitle {
            color: var(--gray-600);
            font-size: 16px;
            margin-top: 8px;
            font-weight: 500;
        }

        .header-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat-card {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            border-radius: var(--border-radius);
            color: white;
            min-width: 140px;
            box-shadow: var(--shadow);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }

        /* 主布局 */
        .operation-layout {
            display: grid;
            grid-template-columns: 320px 1fr;
            gap: 32px;
            align-items: start;
        }

        /* 侧边导航 */
        .operation-nav {
            background: white;
            border-radius: var(--border-radius);
            padding: 0;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            position: sticky;
            top: 32px;
            overflow: hidden;
        }

        .nav-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 700;
            margin: 0;
        }

        .nav-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 4px;
        }

        .nav-menu {
            padding: 16px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            margin-bottom: 8px;
            border-radius: 8px;
            color: var(--gray-700);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-item:hover {
            background: var(--gray-100);
            color: var(--primary-color);
        }

        .nav-item.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            box-shadow: var(--shadow);
        }

        .nav-item i {
            width: 20px;
            text-align: center;
        }

        /* 内容区域 */
        .operation-content {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }

        .content-header {
            padding: 32px;
            border-bottom: 2px solid var(--gray-200);
            background: linear-gradient(135deg, var(--gray-50) 0%, #f0f9ff 100%);
        }

        .content-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0 0 8px 0;
        }

        .content-desc {
            color: var(--gray-600);
            font-size: 16px;
            font-weight: 500;
        }

        .content-section {
            padding: 32px;
            display: none;
        }

        .content-section.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 功能模块卡片 */
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .module-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: var(--border-radius);
            padding: 24px;
            border: 2px solid var(--gray-100);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .module-card:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .module-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 16px;
        }

        .module-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 8px;
        }

        .module-desc {
            color: var(--gray-600);
            font-size: 14px;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .module-stats {
            display: flex;
            gap: 16px;
            font-size: 12px;
        }

        .module-stat {
            display: flex;
            align-items: center;
            gap: 4px;
            color: var(--gray-500);
        }

        /* 现代化按钮 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 2px solid var(--gray-300);
            box-shadow: var(--shadow);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            transform: translateY(-1px);
        }

        /* 快速操作区域 */
        .quick-actions {
            background: linear-gradient(135deg, var(--gray-50) 0%, #f0f9ff 100%);
            padding: 24px;
            border-radius: var(--border-radius);
            margin-bottom: 32px;
            border: 1px solid var(--gray-200);
        }

        .quick-actions h3 {
            font-size: 18px;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0 0 16px 0;
        }

        .actions-grid {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .operation-layout {
                grid-template-columns: 280px 1fr;
                gap: 24px;
            }
        }

        @media (max-width: 768px) {
            .operation-page {
                padding: 16px;
            }

            .operation-layout {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .operation-nav {
                position: relative;
                top: 0;
            }

            .content-header {
                padding: 24px;
            }

            .content-section {
                padding: 24px;
            }

            .module-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .header-stats {
                width: 100%;
                justify-content: center;
            }

            .stat-card {
                min-width: 120px;
            }
        }

        /* 数据表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .data-table th,
        .data-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .data-table th {
            background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-50) 100%);
            font-weight: 700;
            color: var(--gray-900);
            font-size: 14px;
        }

        .data-table tbody tr:hover {
            background: var(--gray-50);
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-completed {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 进度条 */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="operation-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div>
                    <h1 class="page-title">平台运营管理中心</h1>
                    <p class="page-subtitle">全方位运营管理、数据分析与业务优化平台</p>
                </div>
                <div class="header-stats">
                    <div class="stat-card">
                        <div class="stat-number">¥18.5M</div>
                        <div class="stat-label">月度GMV</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2,456</div>
                        <div class="stat-label">活跃商家</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">运营活动</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">94.5%</div>
                        <div class="stat-label">满意度</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="operation-layout">
            <!-- 左侧导航 -->
            <div class="operation-nav">
                <div class="nav-header">
                    <h3 class="nav-title">运营中心</h3>
                    <p class="nav-subtitle">8个核心模块</p>
                </div>
                <div class="nav-menu">
                    <div class="nav-item active" onclick="showSection('training', this)">
                        <i class="fas fa-graduation-cap"></i>
                        商家培训管理
                    </div>
                    <div class="nav-item" onclick="showSection('performance', this)">
                        <i class="fas fa-chart-line"></i>
                        绩效分析中心
                    </div>
                    <div class="nav-item" onclick="showSection('marketing', this)">
                        <i class="fas fa-bullhorn"></i>
                        营销工具平台
                    </div>
                    <div class="nav-item" onclick="showSection('activities', this)">
                        <i class="fas fa-calendar-alt"></i>
                        运营活动管理
                    </div>
                    <div class="nav-item" onclick="showSection('support', this)">
                        <i class="fas fa-headset"></i>
                        商家支持中心
                    </div>
                    <div class="nav-item" onclick="showSection('monitoring', this)">
                        <i class="fas fa-desktop"></i>
                        平台监控中心
                    </div>
                    <div class="nav-item" onclick="showSection('content', this)">
                        <i class="fas fa-edit"></i>
                        内容运营管理
                    </div>
                    <div class="nav-item" onclick="showSection('policy', this)">
                        <i class="fas fa-gavel"></i>
                        政策规则管理
                    </div>
                </div>
            </div>

            <!-- 右侧内容 -->
            <div class="operation-content">
                <div class="content-header">
                    <h2 class="content-title" id="sectionTitle">商家培训管理</h2>
                    <p class="content-desc" id="sectionDesc">为商家提供系统化培训体系，提升平台整体服务质量</p>
                </div>

                <!-- 商家培训管理模块 -->
                <div id="training" class="content-section active">
                    <div class="quick-actions">
                        <h3>快速操作</h3>
                        <div class="actions-grid">
                            <button class="btn btn-primary" onclick="createTraining()">
                                <i class="fas fa-plus"></i>
                                创建培训课程
                            </button>
                            <button class="btn btn-secondary" onclick="manageCertificates()">
                                <i class="fas fa-certificate"></i>
                                证书管理
                            </button>
                            <button class="btn btn-secondary" onclick="exportTrainingData()">
                                <i class="fas fa-download"></i>
                                导出培训数据
                            </button>
                            <button class="btn btn-success" onclick="scheduleTraining()">
                                <i class="fas fa-calendar-plus"></i>
                                安排培训计划
                            </button>
                        </div>
                    </div>

                    <div class="module-grid">
                        <div class="module-card" onclick="openTrainingModule('courses')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="module-title">培训课程库</div>
                            <div class="module-desc">管理所有培训课程，包括视频课程、直播培训、文档教程和在线测试</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-play-circle"></i>
                                    <span>156个课程</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-users"></i>
                                    <span>2,456位学员</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openTrainingModule('progress')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="module-title">学习进度跟踪</div>
                            <div class="module-desc">实时监控商家学习进度，分析培训效果，提供个性化学习建议</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-percentage"></i>
                                    <span>89%完成率</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.8分满意度</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openTrainingModule('certificates')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-award"></i>
                            </div>
                            <div class="module-title">证书认证系统</div>
                            <div class="module-desc">颁发培训证书，建立商家能力认证体系，提升平台专业度</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-certificate"></i>
                                    <span>1,234个证书</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-check-circle"></i>
                                    <span>95%通过率</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openTrainingModule('analytics')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                                <i class="fas fa-analytics"></i>
                            </div>
                            <div class="module-title">培训效果分析</div>
                            <div class="module-desc">深度分析培训数据，评估培训ROI，优化培训策略和内容</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+25%业绩提升</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-clock"></i>
                                    <span>平均2.5小时</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最新培训动态 -->
                    <div style="background: white; border-radius: 12px; padding: 24px; border: 1px solid #e5e7eb;">
                        <h3 style="margin: 0 0 16px 0; color: #374151;">最新培训动态</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>课程名称</th>
                                    <th>类型</th>
                                    <th>参与人数</th>
                                    <th>完成率</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>商品管理与优化</td>
                                    <td>视频课程</td>
                                    <td>456人</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 78%"></div>
                                        </div>
                                        <span style="font-size: 12px; color: #6b7280;">78%</span>
                                    </td>
                                    <td><span class="status-badge status-active">进行中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>客服技能提升</td>
                                    <td>直播培训</td>
                                    <td>234人</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 92%"></div>
                                        </div>
                                        <span style="font-size: 12px; color: #6b7280;">92%</span>
                                    </td>
                                    <td><span class="status-badge status-completed">已完成</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>营销策略实战</td>
                                    <td>在线测试</td>
                                    <td>189人</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 45%"></div>
                                        </div>
                                        <span style="font-size: 12px; color: #6b7280;">45%</span>
                                    </td>
                                    <td><span class="status-badge status-pending">计划中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 其他模块将在这里添加 -->
                <div id="performance" class="content-section">
                    <div class="quick-actions">
                        <h3>快速操作</h3>
                        <div class="actions-grid">
                            <button class="btn btn-primary" onclick="generatePerformanceReport()">
                                <i class="fas fa-chart-line"></i>
                                生成绩效报告
                            </button>
                            <button class="btn btn-secondary" onclick="setRewardRules()">
                                <i class="fas fa-trophy"></i>
                                设置奖励规则
                            </button>
                            <button class="btn btn-secondary" onclick="performKPIAnalysis()">
                                <i class="fas fa-balance-scale"></i>
                                KPI对比分析
                            </button>
                        </div>
                    </div>

                    <div class="module-grid">
                        <div class="module-card" onclick="openPerformanceModule('ranking')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="module-title">商家排行榜</div>
                            <div class="module-desc">多维度商家绩效排名，包括销售额、客户满意度、成长率等指标</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-medal"></i>
                                    <span>TOP 50排名</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>8项指标</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openPerformanceModule('kpi')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <div class="module-title">KPI管理</div>
                            <div class="module-desc">设定和跟踪关键绩效指标，实时监控商家和平台目标达成情况</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-target"></i>
                                    <span>15个KPI</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-percentage"></i>
                                    <span>92%达成率</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openPerformanceModule('incentive')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-gift"></i>
                            </div>
                            <div class="module-title">激励体系</div>
                            <div class="module-desc">设计和管理商家激励机制，包括佣金奖励、等级晋升、特权开放</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-coins"></i>
                                    <span>¥2.5M激励</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-users"></i>
                                    <span>456位受益</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openPerformanceModule('analytics')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div class="module-title">数据洞察</div>
                            <div class="module-desc">深度分析商家行为数据，提供业务优化建议和趋势预测</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-brain"></i>
                                    <span>AI洞察</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-trending-up"></i>
                                    <span>趋势预测</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 商家绩效排行榜 -->
                    <div style="background: white; border-radius: 12px; padding: 24px; border: 1px solid #e5e7eb; margin-top: 24px;">
                        <h3 style="margin: 0 0 16px 0; color: #374151;">月度商家绩效排行榜</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>商家名称</th>
                                    <th>销售额</th>
                                    <th>订单量</th>
                                    <th>转化率</th>
                                    <th>满意度</th>
                                    <th>等级</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><i class="fas fa-crown" style="color: #f59e0b;"></i> 1</td>
                                    <td>星光数码专营店</td>
                                    <td>¥985,600</td>
                                    <td>2,456</td>
                                    <td>8.5%</td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 4px;">
                                            <span>4.9</span>
                                            <i class="fas fa-star" style="color: #f59e0b; font-size: 12px;"></i>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-completed">钻石</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-medal" style="color: #6b7280;"></i> 2</td>
                                    <td>优品生活馆</td>
                                    <td>¥756,800</td>
                                    <td>1,892</td>
                                    <td>7.2%</td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 4px;">
                                            <span>4.8</span>
                                            <i class="fas fa-star" style="color: #f59e0b; font-size: 12px;"></i>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-active">黄金</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-medal" style="color: #cd7f32;"></i> 3</td>
                                    <td>时尚潮流站</td>
                                    <td>¥623,400</td>
                                    <td>1,567</td>
                                    <td>6.8%</td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 4px;">
                                            <span>4.7</span>
                                            <i class="fas fa-star" style="color: #f59e0b; font-size: 12px;"></i>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-active">黄金</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>健康美食坊</td>
                                    <td>¥445,200</td>
                                    <td>1,234</td>
                                    <td>5.9%</td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 4px;">
                                            <span>4.6</span>
                                            <i class="fas fa-star" style="color: #f59e0b; font-size: 12px;"></i>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-pending">白银</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="marketing" class="content-section">
                    <div class="quick-actions">
                        <h3>快速操作</h3>
                        <div class="actions-grid">
                            <button class="btn btn-primary" onclick="createMarketingCampaign()">
                                <i class="fas fa-rocket"></i>
                                创建营销活动
                            </button>
                            <button class="btn btn-secondary" onclick="manageCoupons()">
                                <i class="fas fa-ticket-alt"></i>
                                优惠券管理
                            </button>
                            <button class="btn btn-secondary" onclick="manageFlashSales()">
                                <i class="fas fa-bolt"></i>
                                秒杀活动
                            </button>
                        </div>
                    </div>

                    <div class="module-grid">
                        <div class="module-card" onclick="openMarketingModule('campaigns')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <div class="module-title">营销活动</div>
                            <div class="module-desc">策划和管理各类营销活动，包括节日促销、新品推广、清仓特卖</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>36个活动</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+128%转化</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openMarketingModule('coupons')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <div class="module-title">优惠券中心</div>
                            <div class="module-desc">创建和管理各类优惠券，支持满减、折扣、新人专享等多种类型</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-tags"></i>
                                    <span>156张券</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-percentage"></i>
                                    <span>78%使用率</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openMarketingModule('live')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="module-title">直播带货</div>
                            <div class="module-desc">支持商家开展直播带货，提供互动工具、数据分析和流量扶持</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-eye"></i>
                                    <span>10.5万观看</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>15%转化率</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openMarketingModule('analytics')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="module-title">营销分析</div>
                            <div class="module-desc">深度分析营销活动效果，优化投放策略，提升ROI</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-calculator"></i>
                                    <span>ROI 3.8</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-chart-pie"></i>
                                    <span>多维分析</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 营销活动列表 -->
                    <div style="background: white; border-radius: 12px; padding: 24px; border: 1px solid #e5e7eb; margin-top: 24px;">
                        <h3 style="margin: 0 0 16px 0; color: #374151;">当前营销活动</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>活动名称</th>
                                    <th>类型</th>
                                    <th>参与商家</th>
                                    <th>开始时间</th>
                                    <th>预算/效果</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>双12年终大促</td>
                                    <td>节日促销</td>
                                    <td>256家</td>
                                    <td>2024-12-01</td>
                                    <td>
                                        <div>
                                            <div>预算: ¥500万</div>
                                            <div style="font-size: 12px; color: #10b981;">已产生GMV: ¥1.2亿</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-active">进行中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;" onclick="manageMarketingActivity('double12-promotion')">管理</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>新品首发专场</td>
                                    <td>商品推广</td>
                                    <td>89家</td>
                                    <td>2024-11-25</td>
                                    <td>
                                        <div>
                                            <div>预算: ¥200万</div>
                                            <div style="font-size: 12px; color: #10b981;">新品销量: 15,678件</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-active">进行中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;" onclick="manageMarketingActivity('new-product-launch')">管理</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>会员专享日</td>
                                    <td>会员营销</td>
                                    <td>134家</td>
                                    <td>2024-11-15</td>
                                    <td>
                                        <div>
                                            <div>预算: ¥150万</div>
                                            <div style="font-size: 12px; color: #6b7280;">已结束</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-completed">已完成</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;" onclick="viewMarketingReport('member-exclusive-day')">查看报告</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="activities" class="content-section">
                    <div class="quick-actions">
                        <h3>快速操作</h3>
                        <div class="actions-grid">
                            <button class="btn btn-primary" onclick="createActivity()">
                                <i class="fas fa-calendar-plus"></i>
                                创建运营活动
                            </button>
                            <button class="btn btn-secondary" onclick="manageCollaboration()">
                                <i class="fas fa-users"></i>
                                活动协作
                            </button>
                            <button class="btn btn-secondary" onclick="reviewEffectiveness()">
                                <i class="fas fa-chart-pie"></i>
                                效果复盘
                            </button>
                        </div>
                    </div>

                    <div class="module-grid">
                        <div class="module-card" onclick="openActivityModule('events')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="module-title">活动策划</div>
                            <div class="module-desc">统一策划和管理各类运营活动，包括节日活动、主题活动、品类活动</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-clipboard-list"></i>
                                    <span>28个活动</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-users"></i>
                                    <span>156万参与</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openActivityModule('execution')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="module-title">执行管理</div>
                            <div class="module-desc">活动执行过程管理，任务分配、进度跟踪、质量把控</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-check-circle"></i>
                                    <span>95%完成率</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-clock"></i>
                                    <span>准时率98%</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openActivityModule('resources')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="module-title">资源配置</div>
                            <div class="module-desc">活动资源统一配置和分配，包括预算、人员、物料、渠道</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>¥850万预算</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-percentage"></i>
                                    <span>85%利用率</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openActivityModule('analysis')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="module-title">效果分析</div>
                            <div class="module-desc">活动效果深度分析，ROI计算、用户行为分析、优化建议</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-trending-up"></i>
                                    <span>ROI 4.2</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-lightbulb"></i>
                                    <span>智能洞察</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 运营活动日历 -->
                    <div style="background: white; border-radius: 12px; padding: 24px; border: 1px solid #e5e7eb; margin-top: 24px;">
                        <h3 style="margin: 0 0 16px 0; color: #374151;">活动执行日程</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>活动名称</th>
                                    <th>活动类型</th>
                                    <th>执行时间</th>
                                    <th>负责团队</th>
                                    <th>预期目标</th>
                                    <th>当前状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>年终感恩回馈</td>
                                    <td>感恩活动</td>
                                    <td>2024-12-20 至 2024-12-31</td>
                                    <td>运营部 + 市场部</td>
                                    <td>
                                        <div>
                                            <div>参与用户: 50万</div>
                                            <div style="font-size: 12px; color: #6b7280;">GMV目标: ¥5000万</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-pending">筹备中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>新年购物节</td>
                                    <td>节日促销</td>
                                    <td>2025-01-01 至 2025-01-07</td>
                                    <td>全体部门</td>
                                    <td>
                                        <div>
                                            <div>参与用户: 100万</div>
                                            <div style="font-size: 12px; color: #6b7280;">GMV目标: ¥1亿</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-pending">计划中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>黑五大促</td>
                                    <td>折扣促销</td>
                                    <td>2024-11-25 至 2024-11-29</td>
                                    <td>运营部</td>
                                    <td>
                                        <div>
                                            <div>参与用户: 80万</div>
                                            <div style="font-size: 12px; color: #10b981;">实际GMV: ¥8500万</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-completed">已完成</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看报告</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="support" class="content-section">
                    <div class="quick-actions">
                        <h3>快速操作</h3>
                        <div class="actions-grid">
                            <button class="btn btn-primary" onclick="createSupportTicket()">
                                <i class="fas fa-headset"></i>
                                创建支持工单
                            </button>
                            <button class="btn btn-secondary" onclick="manageKnowledgeBase()">
                                <i class="fas fa-book"></i>
                                知识库管理
                            </button>
                            <button class="btn btn-secondary" onclick="openOnlineSupport()">
                                <i class="fas fa-comments"></i>
                                在线客服
                            </button>
                        </div>
                    </div>

                    <div class="module-grid">
                        <div class="module-card" onclick="openSupportModule('tickets')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <div class="module-title">工单管理</div>
                            <div class="module-desc">统一处理商家各类支持请求，包括技术问题、业务咨询、投诉建议</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-inbox"></i>
                                    <span>156个工单</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-clock"></i>
                                    <span>2.5h响应</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openSupportModule('knowledge')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <div class="module-title">知识库</div>
                            <div class="module-desc">构建完善的帮助文档体系，提供自助服务和常见问题解答</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-file-alt"></i>
                                    <span>285篇文档</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-search"></i>
                                    <span>78%自助解决</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openSupportModule('onboarding')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="module-title">商家入驻</div>
                            <div class="module-desc">新商家入驻全流程支持，从申请审核到开店指导的一站式服务</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-user-plus"></i>
                                    <span>89个申请</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-percentage"></i>
                                    <span>95%通过率</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openSupportModule('satisfaction')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="module-title">满意度调研</div>
                            <div class="module-desc">持续收集商家反馈，评估服务质量，优化支持流程</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-star"></i>
                                    <span>4.8分满意度</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-chart-line"></i>
                                    <span>+12%提升</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 支持工单列表 -->
                    <div style="background: white; border-radius: 12px; padding: 24px; border: 1px solid #e5e7eb; margin-top: 24px;">
                        <h3 style="margin: 0 0 16px 0; color: #374151;">待处理工单</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>工单编号</th>
                                    <th>商家</th>
                                    <th>问题类型</th>
                                    <th>标题</th>
                                    <th>优先级</th>
                                    <th>创建时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#T2024001</td>
                                    <td>星光数码</td>
                                    <td>技术问题</td>
                                    <td>商品上传失败</td>
                                    <td><span class="status-badge" style="background: #fef3c7; color: #92400e;">高</span></td>
                                    <td>2024-11-28 14:30</td>
                                    <td><span class="status-badge status-pending">处理中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">处理</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#T2024002</td>
                                    <td>优品生活馆</td>
                                    <td>业务咨询</td>
                                    <td>佣金计算规则咨询</td>
                                    <td><span class="status-badge" style="background: #dbeafe; color: #1e40af;">中</span></td>
                                    <td>2024-11-28 15:45</td>
                                    <td><span class="status-badge status-pending">待分配</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">分配</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#T2024003</td>
                                    <td>时尚潮流站</td>
                                    <td>功能建议</td>
                                    <td>希望增加批量编辑功能</td>
                                    <td><span class="status-badge" style="background: #dcfce7; color: #166534;">低</span></td>
                                    <td>2024-11-28 16:20</td>
                                    <td><span class="status-badge status-pending">待处理</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">处理</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#T2024004</td>
                                    <td>健康美食坊</td>
                                    <td>账户问题</td>
                                    <td>提现审核异常</td>
                                    <td><span class="status-badge" style="background: #fef3c7; color: #92400e;">高</span></td>
                                    <td>2024-11-28 17:10</td>
                                    <td><span class="status-badge status-completed">已解决</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="monitoring" class="content-section">
                    <div class="quick-actions">
                        <h3>快速操作</h3>
                        <div class="actions-grid">
                            <button class="btn btn-primary" onclick="openMonitoringDashboard()">
                                <i class="fas fa-desktop"></i>
                                实时监控面板
                            </button>
                            <button class="btn btn-secondary" onclick="configureAlerts()">
                                <i class="fas fa-exclamation-triangle"></i>
                                预警设置
                            </button>
                            <button class="btn btn-secondary" onclick="performSystemCheck()">
                                <i class="fas fa-heartbeat"></i>
                                系统健康检查
                            </button>
                        </div>
                    </div>

                    <div class="module-grid">
                        <div class="module-card" onclick="openMonitoringModule('performance')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #059669 0%, #047857 100%);">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="module-title">性能监控</div>
                            <div class="module-desc">实时监控系统性能指标，包括响应时间、吞吐量、错误率等</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-clock"></i>
                                    <span>185ms响应</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-check-circle"></i>
                                    <span>99.8%正常</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openMonitoringModule('business')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="module-title">业务监控</div>
                            <div class="module-desc">监控关键业务指标，包括订单量、GMV、用户活跃度等核心数据</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>2.3万订单/日</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-users"></i>
                                    <span>15.6万DAU</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openMonitoringModule('security')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="module-title">安全监控</div>
                            <div class="module-desc">监控平台安全状况，包括异常登录、恶意攻击、数据泄露等风险</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>3个风险</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-ban"></i>
                                    <span>125次拦截</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openMonitoringModule('alerts')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div class="module-title">告警中心</div>
                            <div class="module-desc">统一管理各类系统告警，支持多渠道通知和智能过滤</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-inbox"></i>
                                    <span>12个待处理</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-check"></i>
                                    <span>98%解决率</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统状态监控 -->
                    <div style="background: white; border-radius: 12px; padding: 24px; border: 1px solid #e5e7eb; margin-top: 24px;">
                        <h3 style="margin: 0 0 16px 0; color: #374151;">系统实时状态</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                            <div style="background: #f8fafc; border-radius: 8px; padding: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                    <h4 style="margin: 0; color: #374151;">API服务</h4>
                                    <span class="status-badge status-completed">正常</span>
                                </div>
                                <div style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">平均响应时间: 185ms</div>
                                <div style="background: #e5e7eb; border-radius: 4px; height: 6px; overflow: hidden;">
                                    <div style="background: #10b981; height: 100%; width: 92%;"></div>
                                </div>
                            </div>

                            <div style="background: #f8fafc; border-radius: 8px; padding: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                    <h4 style="margin: 0; color: #374151;">数据库</h4>
                                    <span class="status-badge status-completed">正常</span>
                                </div>
                                <div style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">连接数: 156/500</div>
                                <div style="background: #e5e7eb; border-radius: 4px; height: 6px; overflow: hidden;">
                                    <div style="background: #10b981; height: 100%; width: 31%;"></div>
                                </div>
                            </div>

                            <div style="background: #f8fafc; border-radius: 8px; padding: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                    <h4 style="margin: 0; color: #374151;">缓存服务</h4>
                                    <span class="status-badge status-active">良好</span>
                                </div>
                                <div style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">命中率: 95.8%</div>
                                <div style="background: #e5e7eb; border-radius: 4px; height: 6px; overflow: hidden;">
                                    <div style="background: #f59e0b; height: 100%; width: 96%;"></div>
                                </div>
                            </div>

                            <div style="background: #f8fafc; border-radius: 8px; padding: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                    <h4 style="margin: 0; color: #374151;">消息队列</h4>
                                    <span class="status-badge status-pending">警告</span>
                                </div>
                                <div style="font-size: 14px; color: #6b7280; margin-bottom: 8px;">积压消息: 1,256条</div>
                                <div style="background: #e5e7eb; border-radius: 4px; height: 6px; overflow: hidden;">
                                    <div style="background: #ef4444; height: 100%; width: 76%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="content" class="content-section">
                    <div class="quick-actions">
                        <h3>快速操作</h3>
                        <div class="actions-grid">
                            <button class="btn btn-primary" onclick="publishAnnouncement()">
                                <i class="fas fa-edit"></i>
                                发布平台公告
                            </button>
                            <button class="btn btn-secondary" onclick="optimizeSEO()">
                                <i class="fas fa-search"></i>
                                SEO优化
                            </button>
                            <button class="btn btn-secondary" onclick="manageSocialMedia()">
                                <i class="fas fa-share-alt"></i>
                                社交媒体管理
                            </button>
                        </div>
                    </div>

                    <div class="module-grid">
                        <div class="module-card" onclick="openContentModule('announcements')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <div class="module-title">平台公告</div>
                            <div class="module-desc">发布和管理平台公告，包括系统维护、政策更新、活动通知等</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-newspaper"></i>
                                    <span>36条公告</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-eye"></i>
                                    <span>158万阅读</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openContentModule('seo')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="module-title">SEO管理</div>
                            <div class="module-desc">优化搜索引擎表现，管理关键词、页面元数据和结构化数据</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-chart-line"></i>
                                    <span>+25%流量</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-key"></i>
                                    <span>1,256关键词</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openContentModule('social')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                                <i class="fas fa-share-alt"></i>
                            </div>
                            <div class="module-title">社交媒体</div>
                            <div class="module-desc">统一管理各社交平台账号，发布动态，监控品牌声誉</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-heart"></i>
                                    <span>56万粉丝</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-comment"></i>
                                    <span>98%好评率</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openContentModule('moderation')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="module-title">内容审核</div>
                            <div class="module-desc">AI+人工审核商家内容，确保平台内容质量和合规性</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-robot"></i>
                                    <span>AI助手</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-check-circle"></i>
                                    <span>99.2%准确率</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 内容管理列表 -->
                    <div style="background: white; border-radius: 12px; padding: 24px; border: 1px solid #e5e7eb; margin-top: 24px;">
                        <h3 style="margin: 0 0 16px 0; color: #374151;">最新内容动态</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>内容类型</th>
                                    <th>标题</th>
                                    <th>发布渠道</th>
                                    <th>发布时间</th>
                                    <th>阅读/互动</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>平台公告</td>
                                    <td>双12活动规则说明</td>
                                    <td>站内消息 + 短信</td>
                                    <td>2024-11-28 10:00</td>
                                    <td>
                                        <div>
                                            <div>阅读: 45,678</div>
                                            <div style="font-size: 12px; color: #6b7280;">点赞: 2,156</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-active">已发布</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>营销文案</td>
                                    <td>新年购物攻略</td>
                                    <td>微信公众号</td>
                                    <td>2024-11-28 14:30</td>
                                    <td>
                                        <div>
                                            <div>阅读: 12,345</div>
                                            <div style="font-size: 12px; color: #6b7280;">转发: 856</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-active">已发布</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看数据</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>系统通知</td>
                                    <td>维护升级公告</td>
                                    <td>APP推送</td>
                                    <td>2024-11-29 09:00</td>
                                    <td>
                                        <div>
                                            <div>推送: 120万</div>
                                            <div style="font-size: 12px; color: #6b7280;">到达率: 95.8%</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-pending">定时发布</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">修改</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="policy" class="content-section">
                    <div class="quick-actions">
                        <h3>快速操作</h3>
                        <div class="actions-grid">
                            <button class="btn btn-primary" onclick="createNewRule()">
                                <i class="fas fa-gavel"></i>
                                制定新规则
                            </button>
                            <button class="btn btn-secondary" onclick="performComplianceCheck()">
                                <i class="fas fa-shield-alt"></i>
                                合规检查
                            </button>
                            <button class="btn btn-secondary" onclick="viewRuleHistory()">
                                <i class="fas fa-history"></i>
                                规则历史
                            </button>
                        </div>
                    </div>

                    <div class="module-grid">
                        <div class="module-card" onclick="openPolicyModule('rules')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);">
                                <i class="fas fa-gavel"></i>
                            </div>
                            <div class="module-title">规则制定</div>
                            <div class="module-desc">制定和管理平台运营规则，包括商家准入、商品规范、交易规则等</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-file-contract"></i>
                                    <span>156条规则</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-calendar"></i>
                                    <span>每月更新</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openPolicyModule('compliance')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="module-title">合规监管</div>
                            <div class="module-desc">实时监控平台合规状况，包括商品合规、交易合规、内容合规</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-check-circle"></i>
                                    <span>98.5%合规率</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>23个违规</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openPolicyModule('enforcement')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-hammer"></i>
                            </div>
                            <div class="module-title">执法处罚</div>
                            <div class="module-desc">处理违规行为，包括警告、限制、封禁等处罚措施的执行</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-ban"></i>
                                    <span>89次处罚</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-undo"></i>
                                    <span>12次申诉</span>
                                </div>
                            </div>
                        </div>

                        <div class="module-card" onclick="openPolicyModule('appeals')">
                            <div class="module-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <div class="module-title">申诉仲裁</div>
                            <div class="module-desc">处理商家申诉和争议，提供公平公正的仲裁服务</div>
                            <div class="module-stats">
                                <div class="module-stat">
                                    <i class="fas fa-hourglass-half"></i>
                                    <span>平均3天</span>
                                </div>
                                <div class="module-stat">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span>95%满意度</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 规则管理列表 -->
                    <div style="background: white; border-radius: 12px; padding: 24px; border: 1px solid #e5e7eb; margin-top: 24px;">
                        <h3 style="margin: 0 0 16px 0; color: #374151;">规则管理概览</h3>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>规则类型</th>
                                    <th>规则名称</th>
                                    <th>生效时间</th>
                                    <th>适用范围</th>
                                    <th>违规次数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>商品规范</td>
                                    <td>商品信息真实性规则</td>
                                    <td>2024-01-01</td>
                                    <td>全平台商家</td>
                                    <td>
                                        <div>
                                            <div>本月: 8次</div>
                                            <div style="font-size: 12px; color: #6b7280;">总计: 156次</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-active">生效中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">修订</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>交易规则</td>
                                    <td>售后服务时限规定</td>
                                    <td>2024-06-01</td>
                                    <td>实物商品</td>
                                    <td>
                                        <div>
                                            <div>本月: 3次</div>
                                            <div style="font-size: 12px; color: #6b7280;">总计: 45次</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-active">生效中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>营销规范</td>
                                    <td>价格促销活动规则</td>
                                    <td>2024-11-15</td>
                                    <td>参与促销商家</td>
                                    <td>
                                        <div>
                                            <div>本月: 12次</div>
                                            <div style="font-size: 12px; color: #6b7280;">总计: 78次</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-active">生效中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">修订</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>内容规范</td>
                                    <td>商品描述内容规范</td>
                                    <td>2025-01-01</td>
                                    <td>全平台商家</td>
                                    <td>
                                        <div>
                                            <div>预计影响: 1,200家</div>
                                            <div style="font-size: 12px; color: #6b7280;">准备就绪</div>
                                        </div>
                                    </td>
                                    <td><span class="status-badge status-pending">待生效</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">预览</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面标题和描述映射
        const sectionConfig = {
            'training': {
                title: '商家培训管理',
                desc: '为商家提供系统化培训体系，提升平台整体服务质量'
            },
            'performance': {
                title: '绩效分析中心',
                desc: '全方位商家绩效分析，提供数据驱动的业务优化建议'
            },
            'marketing': {
                title: '营销工具平台',
                desc: '丰富的营销工具集合，助力商家提升销售转化率'
            },
            'activities': {
                title: '运营活动管理',
                desc: '策划和执行各类运营活动，促进平台生态繁荣'
            },
            'support': {
                title: '商家支持中心',
                desc: '为商家提供全方位技术支持和业务咨询服务'
            },
            'monitoring': {
                title: '平台监控中心',
                desc: '实时监控平台运行状态，确保系统稳定可靠'
            },
            'content': {
                title: '内容运营管理',
                desc: '统一管理平台内容，优化用户体验和SEO效果'
            },
            'policy': {
                title: '政策规则管理',
                desc: '制定和维护平台运营规则，保障公平健康的商业环境'
            }
        };

        // 切换功能模块
        function showSection(sectionId, element) {
            // 隐藏所有内容区域
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // 移除所有导航项的活动状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的内容区域
            document.getElementById(sectionId).classList.add('active');

            // 添加活动状态到当前导航项
            element.classList.add('active');

            // 更新页面标题和描述
            const config = sectionConfig[sectionId];
            document.getElementById('sectionTitle').textContent = config.title;
            document.getElementById('sectionDesc').textContent = config.desc;

            // 平滑滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 培训相关功能
        function createTraining() {
            showNotification('跳转到培训课程创建页面', 'info');
        }

        function manageCertificates() {
            showNotification('打开证书管理界面', 'info');
        }

        function exportTrainingData() {
            showNotification('开始导出培训数据...', 'success');
        }

        function scheduleTraining() {
            showNotification('打开培训计划安排界面', 'info');
        }

        function openTrainingModule(moduleType) {
            const moduleNames = {
                'courses': '培训课程库',
                'progress': '学习进度跟踪',
                'certificates': '证书认证系统',
                'analytics': '培训效果分析'
            };
            showNotification(`打开${moduleNames[moduleType]}详细页面`, 'info');
        }

        // 绩效分析功能
        function generatePerformanceReport() {
            showNotification('正在生成绩效分析报告...', 'info');
        }

        function setRewardRules() {
            showNotification('打开奖励规则设置界面', 'info');
        }

        function performKPIAnalysis() {
            showNotification('启动KPI对比分析工具', 'info');
        }

        function setKPITargets() {
            showNotification('打开KPI目标设置界面', 'info');
        }

        function comparePerformance() {
            showNotification('启动绩效对比分析工具', 'info');
        }

        function openPerformanceModule(moduleType) {
            const moduleNames = {
                'ranking': '商家排行榜',
                'metrics': '平台指标中心',
                'reports': '深度分析报告',
                'forecasting': '业绩预测系统'
            };
            showNotification(`打开${moduleNames[moduleType]}详细页面`, 'info');
        }

        // 营销工具功能
        function createMarketingCampaign() {
            showNotification('正在跳转到营销活动创建页面...', 'info');
            setTimeout(() => {
                window.open('../merchant-management/advanced-marketing.html?action=create', '_blank');
            }, 1000);
        }

        function manageCoupons() {
            showNotification('正在跳转到优惠券管理中心...', 'info');
            setTimeout(() => {
                window.open('../financial-management/enhanced-financial-data.html?module=coupons', '_blank');
            }, 1000);
        }

        function manageFlashSales() {
            showNotification('正在跳转到秒杀活动管理页面...', 'info');
            setTimeout(() => {
                window.open('../merchant-management/advanced-marketing.html?module=flashsale', '_blank');
            }, 1000);
        }

        function launchCampaign() {
            showNotification('启动营销活动创建向导', 'info');
        }

        function createFlashSale() {
            showNotification('创建秒杀活动配置', 'info');
        }

        // 营销活动管理功能
        function manageMarketingActivity(activityId) {
            const activityNames = {
                'double12-promotion': '双12年终大促',
                'new-product-launch': '新品首发专场',
                'flash-sale-weekend': '周末秒杀活动'
            };
            
            const activityName = activityNames[activityId] || '营销活动';
            showNotification(`正在跳转到${activityName}管理页面...`, 'info');
            
            // 跳转到具体的活动管理页面
            setTimeout(() => {
                window.open(`../merchant-management/advanced-marketing.html?activity=${activityId}`, '_blank');
            }, 1000);
        }

        function viewMarketingReport(activityId) {
            const activityNames = {
                'member-exclusive-day': '会员专享日',
                'black-friday-sale': '黑五大促',
                'new-year-festival': '新年购物节'
            };
            
            const activityName = activityNames[activityId] || '营销活动';
            showNotification(`正在加载${activityName}业绩报告...`, 'success');
            
            // 跳转到业绩报告页面
            setTimeout(() => {
                window.open(`../reports-analytics/advanced-reports.html?report=marketing&activity=${activityId}`, '_blank');
            }, 1000);
        }

        function openMarketingModule(moduleType) {
            const moduleNames = {
                'campaigns': '营销活动',
                'coupons': '优惠券中心',
                'live': '直播带货',
                'analytics': '营销分析'
            };
            
            // 根据模块类型跳转到相应页面
            const pageUrls = {
                'campaigns': '../data-analytics/enhanced-analytics.html?module=marketing-campaigns',
                'coupons': '../financial-management/enhanced-financial-data.html?module=coupons',
                'live': '../content-management/moments-admin.html?module=live-streaming',
                'analytics': '../reports-analytics/advanced-reports.html?module=marketing-analytics'
            };
            
            if (pageUrls[moduleType]) {
                showNotification(`正在跳转到${moduleNames[moduleType]}页面...`, 'info');
                setTimeout(() => {
                    window.open(pageUrls[moduleType], '_blank');
                }, 1000);
            } else {
                showNotification(`打开${moduleNames[moduleType]}详细页面`, 'info');
            }
        }

        // 运营活动功能
        function createActivity() {
            showNotification('打开运营活动创建界面', 'info');
        }

        function manageCollaboration() {
            showNotification('进入活动协作管理中心', 'info');
        }

        function reviewEffectiveness() {
            showNotification('启动活动效果复盘分析', 'info');
        }

        function openActivityModule(moduleType) {
            const moduleNames = {
                'events': '活动策划',
                'execution': '执行管理',
                'resources': '资源配置',
                'analysis': '效果分析'
            };
            showNotification(`打开${moduleNames[moduleType]}详细页面`, 'info');
        }

        // 商家支持功能
        function createSupportTicket() {
            showNotification('创建新的支持工单', 'info');
        }

        function manageKnowledgeBase() {
            showNotification('进入知识库管理系统', 'info');
        }

        function openOnlineSupport() {
            showNotification('启动在线客服系统', 'info');
        }

        function openSupportModule(moduleType) {
            const moduleNames = {
                'tickets': '工单管理',
                'knowledge': '知识库',
                'onboarding': '商家入驻',
                'satisfaction': '满意度调研'
            };
            showNotification(`打开${moduleNames[moduleType]}详细页面`, 'info');
        }

        // 平台监控功能
        function openMonitoringDashboard() {
            showNotification('打开实时监控大屏', 'info');
        }

        function configureAlerts() {
            showNotification('进入告警配置设置', 'info');
        }

        function performSystemCheck() {
            showNotification('正在执行系统健康检查...', 'info');
        }

        function openMonitoringModule(moduleType) {
            const moduleNames = {
                'performance': '性能监控',
                'business': '业务监控',
                'security': '安全监控',
                'alerts': '告警中心'
            };
            showNotification(`打开${moduleNames[moduleType]}详细页面`, 'info');
        }

        // 内容运营功能
        function publishAnnouncement() {
            showNotification('创建新的平台公告', 'info');
        }

        function optimizeSEO() {
            showNotification('启动SEO优化工具', 'info');
        }

        function manageSocialMedia() {
            showNotification('进入社交媒体管理中心', 'info');
        }

        function openContentModule(moduleType) {
            const moduleNames = {
                'announcements': '平台公告',
                'seo': 'SEO管理',
                'social': '社交媒体',
                'moderation': '内容审核'
            };
            showNotification(`打开${moduleNames[moduleType]}详细页面`, 'info');
        }

        // 政策规则功能
        function createNewRule() {
            showNotification('启动新规则制定流程', 'info');
        }

        function performComplianceCheck() {
            showNotification('执行平台合规检查...', 'info');
        }

        function viewRuleHistory() {
            showNotification('查看规则变更历史', 'info');
        }

        function openPolicyModule(moduleType) {
            const moduleNames = {
                'rules': '规则制定',
                'compliance': '合规监管',
                'enforcement': '执法处罚',
                'appeals': '申诉仲裁'
            };
            showNotification(`打开${moduleNames[moduleType]}详细页面`, 'info');
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 16px 20px;
                background: ${type === 'success' ? '#10b981' : type === 'info' ? '#3b82f6' : '#f59e0b'};
                color: white;
                border-radius: 12px;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                font-weight: 600;
                transform: translateX(400px);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.style.transform = 'translateX(400px)';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('平台运营管理中心已加载');
            
            // 模拟实时数据更新
            setInterval(() => {
                updateRealTimeStats();
            }, 30000); // 每30秒更新一次
        });

        // 更新实时统计数据
        function updateRealTimeStats() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                // 模拟数据变化
                const currentValue = stat.textContent;
                if (currentValue.includes('¥')) {
                    // 模拟GMV增长
                    const value = parseFloat(currentValue.replace('¥', '').replace('M', ''));
                    const newValue = (value + Math.random() * 0.1).toFixed(1);
                    stat.textContent = `¥${newValue}M`;
                }
            });
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        document.querySelector('.nav-item').click();
                        break;
                    case 'n':
                        e.preventDefault();
                        createTraining();
                        break;
                }
            }
        });
    </script>
</body>
</html> 