<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平台运营管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .operation-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
            margin-top: 4px;
        }

        .header-stats {
            display: flex;
            gap: 32px;
        }

        .stat-card {
            text-align: center;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            min-width: 120px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .operation-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            margin-bottom: 24px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .tab-btn {
            flex: 1;
            padding: 16px 24px;
            border: none;
            background: white;
            color: #64748b;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .tab-btn.active {
            color: #3b82f6;
            background: #eff6ff;
            border-bottom: 3px solid #3b82f6;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .content-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .card-action {
            color: #3b82f6;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }

        .training-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .training-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .training-item:hover {
            background: #f1f5f9;
        }

        .training-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .training-icon.video { background: #ef4444; }
        .training-icon.document { background: #10b981; }
        .training-icon.live { background: #f59e0b; }
        .training-icon.quiz { background: #8b5cf6; }

        .training-info {
            flex: 1;
        }

        .training-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .training-meta {
            font-size: 12px;
            color: #64748b;
        }

        .training-progress {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
        }

        .progress-bar {
            flex: 1;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s;
        }

        .progress-text {
            font-size: 12px;
            color: #64748b;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .metric-card {
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            text-align: center;
        }

        .metric-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .metric-icon.sales { background: #10b981; }
        .metric-icon.conversion { background: #3b82f6; }
        .metric-icon.satisfaction { background: #f59e0b; }
        .metric-icon.growth { background: #8b5cf6; }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .metric-label {
            font-size: 14px;
            color: #64748b;
        }

        .metric-change {
            font-size: 12px;
            margin-top: 4px;
        }

        .metric-change.positive {
            color: #10b981;
        }

        .metric-change.negative {
            color: #ef4444;
        }

        .marketing-tools {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
        }

        .tool-card {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.2s;
            cursor: pointer;
        }

        .tool-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .tool-icon {
            width: 64px;
            height: 64px;
            border-radius: 12px;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .tool-icon.coupon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .tool-icon.promotion { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .tool-icon.flash { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .tool-icon.live { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .tool-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .tool-description {
            font-size: 14px;
            color: #64748b;
            line-height: 1.5;
        }

        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .activity-item {
            display: flex;
            gap: 16px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .activity-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .activity-description {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
        }

        .activity-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #64748b;
        }

        .activity-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-planned {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-ended {
            background: #f3f4f6;
            color: #374151;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: white;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .header-stats {
                flex-wrap: wrap;
                gap: 16px;
            }
        }

        @media (max-width: 768px) {
            .operation-page {
                padding: 16px;
            }

            .header-content {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }

            .operation-tabs {
                flex-direction: column;
            }

            .performance-metrics {
                grid-template-columns: 1fr 1fr;
            }

            .marketing-tools {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="operation-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title">平台运营管理</h1>
                    <p class="page-subtitle">商家培训、绩效分析、营销工具与运营活动管理</p>
                </div>
                
                <div class="header-stats">
                    <div class="stat-card">
                        <div class="stat-number">248</div>
                        <div class="stat-label">活跃商家</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">89%</div>
                        <div class="stat-label">培训完成率</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">运营活动</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能标签页 -->
        <div class="operation-tabs">
            <button class="tab-btn active" onclick="switchTab('training')">
                <i class="fas fa-graduation-cap"></i>
                商家培训
            </button>
            <button class="tab-btn" onclick="switchTab('performance')">
                <i class="fas fa-chart-line"></i>
                绩效分析
            </button>
            <button class="tab-btn" onclick="switchTab('marketing')">
                <i class="fas fa-bullhorn"></i>
                营销工具
            </button>
            <button class="tab-btn" onclick="switchTab('activities')">
                <i class="fas fa-calendar-alt"></i>
                运营活动
            </button>
        </div>

        <!-- 商家培训内容 -->
        <div class="tab-content active" id="training-content">
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">培训课程</h3>
                        <a href="#" class="card-action">管理课程</a>
                    </div>
                    <div class="training-list">
                        <div class="training-item">
                            <div class="training-icon video">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="training-info">
                                <div class="training-title">商品上架与管理</div>
                                <div class="training-meta">视频课程 • 45分钟 • 156人学习</div>
                                <div class="training-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 75%"></div>
                                    </div>
                                    <span class="progress-text">75%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="training-item">
                            <div class="training-icon live">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="training-info">
                                <div class="training-title">直播带货技巧</div>
                                <div class="training-meta">直播课程 • 今天 14:00 • 89人报名</div>
                                <div class="training-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%"></div>
                                    </div>
                                    <span class="progress-text">即将开始</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="training-item">
                            <div class="training-icon document">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="training-info">
                                <div class="training-title">平台政策解读</div>
                                <div class="training-meta">文档资料 • 12页 • 234人阅读</div>
                                <div class="training-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 100%"></div>
                                    </div>
                                    <span class="progress-text">已完成</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="training-item">
                            <div class="training-icon quiz">
                                <i class="fas fa-question-circle"></i>
                            </div>
                            <div class="training-info">
                                <div class="training-title">平台规则测试</div>
                                <div class="training-meta">在线测试 • 20题 • 178人参与</div>
                                <div class="training-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 60%"></div>
                                    </div>
                                    <span class="progress-text">进行中</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">培训统计</h3>
                        <a href="#" class="card-action">详细报告</a>
                    </div>
                    <div class="performance-metrics">
                        <div class="metric-card">
                            <div class="metric-icon sales">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="metric-value">248</div>
                            <div class="metric-label">参训商家</div>
                            <div class="metric-change positive">+12% 本月</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon conversion">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="metric-value">89%</div>
                            <div class="metric-label">完成率</div>
                            <div class="metric-change positive">+5% 本月</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon satisfaction">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="metric-value">4.8</div>
                            <div class="metric-label">满意度</div>
                            <div class="metric-change positive">+0.2 本月</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon growth">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="metric-value">156</div>
                            <div class="metric-label">课程总数</div>
                            <div class="metric-change positive">+8 本月</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-card full-width">
                <div class="card-header">
                    <h3 class="card-title">培训管理</h3>
                    <div style="display: flex; gap: 12px;">
                        <button class="btn btn-secondary">
                            <i class="fas fa-upload"></i>
                            上传课程
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            创建培训
                        </button>
                    </div>
                </div>
                <p style="color: #64748b; margin: 0;">通过系统化的培训体系，帮助商家快速掌握平台运营技巧，提升业务能力和服务质量。</p>
            </div>
        </div>

        <!-- 绩效分析内容 -->
        <div class="tab-content" id="performance-content">
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">商家绩效排行</h3>
                        <a href="#" class="card-action">完整排行</a>
                    </div>
                    <div class="training-list">
                        <div class="training-item">
                            <div class="training-icon" style="background: #ffd700; color: #1e293b;">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="training-info">
                                <div class="training-title">华为旗舰专卖店</div>
                                <div class="training-meta">月销售额: ¥2,456,789 • 好评率: 98.5%</div>
                                <div class="training-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 98%"></div>
                                    </div>
                                    <span class="progress-text">综合评分: 98分</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="training-item">
                            <div class="training-icon" style="background: #c0c0c0; color: #1e293b;">
                                <i class="fas fa-medal"></i>
                            </div>
                            <div class="training-info">
                                <div class="training-title">小米生活馆</div>
                                <div class="training-meta">月销售额: ¥1,876,543 • 好评率: 96.8%</div>
                                <div class="training-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 95%"></div>
                                    </div>
                                    <span class="progress-text">综合评分: 95分</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="training-item">
                            <div class="training-icon" style="background: #cd7f32; color: white;">
                                <i class="fas fa-award"></i>
                            </div>
                            <div class="training-info">
                                <div class="training-title">时尚潮牌屋</div>
                                <div class="training-meta">月销售额: ¥1,234,567 • 好评率: 94.2%</div>
                                <div class="training-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 92%"></div>
                                    </div>
                                    <span class="progress-text">综合评分: 92分</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">平台整体指标</h3>
                        <a href="#" class="card-action">详细分析</a>
                    </div>
                    <div class="performance-metrics">
                        <div class="metric-card">
                            <div class="metric-icon sales">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="metric-value">¥12.5M</div>
                            <div class="metric-label">月总销售额</div>
                            <div class="metric-change positive">+18.5% 环比</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon conversion">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="metric-value">3.2%</div>
                            <div class="metric-label">平均转化率</div>
                            <div class="metric-change positive">+0.5% 环比</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon satisfaction">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="metric-value">96.8%</div>
                            <div class="metric-label">平均好评率</div>
                            <div class="metric-change positive">+1.2% 环比</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon growth">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="metric-value">45,678</div>
                            <div class="metric-label">新增用户</div>
                            <div class="metric-change positive">+23% 环比</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 营销工具内容 -->
        <div class="tab-content" id="marketing-content">
            <div class="content-card full-width">
                <div class="card-header">
                    <h3 class="card-title">营销工具箱</h3>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        创建营销活动
                    </button>
                </div>
                <div class="marketing-tools">
                    <div class="tool-card" onclick="openMarketingTool('coupon')">
                        <div class="tool-icon coupon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="tool-name">优惠券管理</div>
                        <div class="tool-description">创建和管理各类优惠券，支持满减、折扣、新人专享等多种类型</div>
                    </div>
                    
                    <div class="tool-card" onclick="openMarketingTool('promotion')">
                        <div class="tool-icon promotion">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="tool-name">促销活动</div>
                        <div class="tool-description">设置限时促销、满赠活动、组合优惠等多样化促销方案</div>
                    </div>
                    
                    <div class="tool-card" onclick="openMarketingTool('flash')">
                        <div class="tool-icon flash">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="tool-name">秒杀活动</div>
                        <div class="tool-description">组织限时秒杀活动，提升商品曝光度和销售转化率</div>
                    </div>
                    
                    <div class="tool-card" onclick="openMarketingTool('live')">
                        <div class="tool-icon live">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="tool-name">直播带货</div>
                        <div class="tool-description">支持商家开展直播带货，提供互动工具和数据分析</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 运营活动内容 -->
        <div class="tab-content" id="activities-content">
            <div class="content-card full-width">
                <div class="card-header">
                    <h3 class="card-title">运营活动</h3>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        创建活动
                    </button>
                </div>
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-image">
                            <i class="fas fa-gift"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">双十一狂欢节</div>
                            <div class="activity-description">全平台大促活动，多重优惠叠加，千万补贴回馈用户</div>
                            <div class="activity-meta">
                                <span>活动时间: 2024-11-01 至 2024-11-11</span>
                                <span>参与商家: 156个</span>
                                <span>预计销售额: ¥50M</span>
                                <span class="activity-status status-active">进行中</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-image">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">新春年货节</div>
                            <div class="activity-description">春节特色商品专场，传统文化与现代购物完美结合</div>
                            <div class="activity-meta">
                                <span>活动时间: 2024-01-15 至 2024-02-15</span>
                                <span>参与商家: 89个</span>
                                <span>预计销售额: ¥25M</span>
                                <span class="activity-status status-planned">计划中</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-image">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">开学季优惠</div>
                            <div class="activity-description">学生用品、电子设备、生活用品等开学必需品大促销</div>
                            <div class="activity-meta">
                                <span>活动时间: 2023-08-15 至 2023-09-15</span>
                                <span>参与商家: 124个</span>
                                <span>实际销售额: ¥18.5M</span>
                                <span class="activity-status status-ended">已结束</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有活动状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的内容
            document.getElementById(tabName + '-content').classList.add('active');
            
            // 添加活动状态
            event.target.classList.add('active');
        }

        // 打开营销工具
        function openMarketingTool(toolType) {
            console.log('Opening marketing tool:', toolType);
            // 这里可以跳转到具体的营销工具页面
            switch(toolType) {
                case 'coupon':
                    alert('跳转到优惠券管理页面');
                    break;
                case 'promotion':
                    alert('跳转到促销活动页面');
                    break;
                case 'flash':
                    alert('跳转到秒杀活动页面');
                    break;
                case 'live':
                    alert('跳转到直播管理页面');
                    break;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('平台运营管理页面已加载');
        });
    </script>
</body>
</html>