<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级营销管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .marketing-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .marketing-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .metric-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .metric-title {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }
        
        .metric-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .metric-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .change-positive {
            color: #10b981;
        }
        
        .change-negative {
            color: #ef4444;
        }
        
        .campaigns-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 32px;
        }
        
        .campaign-item {
            padding: 20px 24px;
            border-bottom: 1px solid #f1f5f9;
            display: grid;
            grid-template-columns: auto 1fr auto auto;
            gap: 16px;
            align-items: center;
        }
        
        .campaign-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .campaign-status.active {
            background: #10b981;
        }
        
        .campaign-status.paused {
            background: #f59e0b;
        }
        
        .campaign-status.ended {
            background: #64748b;
        }
        
        .campaign-info h4 {
            color: #1e293b;
            margin: 0 0 4px 0;
            font-size: 16px;
        }
        
        .campaign-info p {
            color: #64748b;
            margin: 0;
            font-size: 14px;
        }
        
        .campaign-metrics {
            display: flex;
            gap: 24px;
        }
        
        .campaign-metric {
            text-align: center;
        }
        
        .campaign-metric-value {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .campaign-metric-label {
            font-size: 12px;
            color: #64748b;
        }
        
        .campaign-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn-sm {
            padding: 6px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            text-align: center;
            min-height: 32px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }
        
        /* 正常状态hover效果 */
        .action-btn-sm:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* 点击激活状态 */
        .action-btn-sm:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            background: #e2e8f0;
        }
        
        /* 主要按钮样式 */
        .action-btn-sm.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .action-btn-sm.primary:hover {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }

        .action-btn-sm.primary:active {
            background: #1d4ed8;
            border-color: #1d4ed8;
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        /* 成功按钮样式 */
        .action-btn-sm.success {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }

        .action-btn-sm.success:hover {
            background: #059669;
            border-color: #059669;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
        }

        .action-btn-sm.success:active {
            background: #047857;
            border-color: #047857;
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
        }

        /* 危险按钮样式 */
        .action-btn-sm.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }

        .action-btn-sm.danger:hover {
            background: #dc2626;
            border-color: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
        }

        .action-btn-sm.danger:active {
            background: #b91c1c;
            border-color: #b91c1c;
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }

        /* 禁用状态 */
        .action-btn-sm:disabled {
            background: #f1f5f9;
            color: #94a3b8;
            border-color: #e2e8f0;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        /* 点击涟漪效果 */
        .action-btn-sm::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }

        .action-btn-sm:active::before {
            width: 120%;
            height: 120%;
        }

        /* 加载状态 */
        .action-btn-sm.loading {
            pointer-events: none;
            position: relative;
        }

        .action-btn-sm.loading::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: button-loading 1s linear infinite;
        }

        @keyframes button-loading {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .campaign-item.hidden {
            display: none;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .tool-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #cbd5e1;
        }

        /* 点击状态 */
        .tool-card:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* 可点击卡片样式 */
        .tool-card[style*="cursor: pointer"] {
            cursor: pointer;
        }

        /* 卡片点击涟漪效果 */
        .tool-card[style*="cursor: pointer"]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.05), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .tool-card[style*="cursor: pointer"]:hover::before {
            opacity: 1;
        }

        /* 点击时的涟漪效果 */
        .tool-card[style*="cursor: pointer"]:active::before {
            background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            opacity: 1;
        }

        .tool-card[style*="cursor: pointer"] .tool-header {
            position: relative;
        }

        /* 悬停提示 */
        .tool-card[style*="cursor: pointer"] .tool-header::after {
            content: '点击进入管理';
            position: absolute;
            top: 0;
            right: 0;
            font-size: 12px;
            color: #64748b;
            background: rgba(255, 255, 255, 0.95);
            padding: 4px 8px;
            border-radius: 6px;
            opacity: 0;
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .tool-card[style*="cursor: pointer"]:hover .tool-header::after {
            opacity: 1;
            transform: translateY(0);
        }

        /* 卡片加载状态 */
        .tool-card.loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .tool-card.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 40px;
            height: 40px;
            margin: -20px 0 0 -20px;
            border: 3px solid rgba(59, 130, 246, 0.3);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: card-loading 1s linear infinite;
            z-index: 10;
        }

        .tool-card.loading::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            z-index: 5;
        }

        @keyframes card-loading {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .tool-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .tool-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .tool-description {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        
        .tool-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
        }
        
        .tool-stat {
            text-align: center;
        }
        
        .tool-stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .tool-stat-label {
            font-size: 11px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .tool-actions {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 6px;
            margin-top: 16px;
        }

        /* 新增的功能描述样式 */
        .desc-main {
            font-size: 14px;
            color: #4a5568;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .desc-features {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 16px;
        }

        .feature-tag {
            background: #f7fafc;
            color: #2d3748;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid #e2e8f0;
        }

        /* 状态指示器样式 */
        .tool-status {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding: 8px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy {
            background: #10b981;
        }

        .status-warning {
            background: #f59e0b;
        }

        .status-attention {
            background: #ef4444;
        }

        .status-text {
            flex: 1;
            font-size: 12px;
            color: #64748b;
        }

        .trend-indicator {
            display: flex;
            align-items: center;
            font-size: 11px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 8px;
        }

        .trend-up {
            background: #dcfce7;
            color: #166534;
        }

        .trend-down {
            background: #fef2f2;
            color: #dc2626;
        }

        /* 统计数据趋势 */
        .tool-stat-trend {
            font-size: 10px;
            font-weight: 600;
            margin-top: 2px;
            color: #10b981;
        }
        
        .performance-charts {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }
        
        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        @media (max-width: 768px) {
            .marketing-page {
                padding: 16px;
            }
            
            .marketing-overview {
                grid-template-columns: 1fr;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .performance-charts {
                grid-template-columns: 1fr;
            }
            
            .campaign-item {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .campaign-metrics {
                gap: 16px;
            }
        }

        /* 会员管理页面特有样式 */
        .level-management-header,
        .benefits-management-header,
        .activities-management-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .activities-filters {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .activities-filters select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            background: white;
            font-size: 14px;
            min-width: 120px;
        }

        .level-actions-top,
        .benefits-actions,
        .activities-actions {
            display: flex;
            gap: 10px;
        }

        .level-badge {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 20px;
            min-width: 60px;
        }

        .level-badge i {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .level-number {
            font-size: 12px;
            font-weight: 600;
            color: #6c757d;
        }

        .level-progress {
            margin-top: 10px;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .progress-info span:first-child {
            color: #6c757d;
        }

        .completion-rate {
            color: #007bff;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff 0%, #6610f2 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-fill.max-level {
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        }

        .level-statistics {
            display: flex;
            gap: 15px;
            margin-left: 20px;
        }

        .stat-mini {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            min-width: 80px;
        }

        .stat-mini .stat-value {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 2px;
        }

        .stat-mini .stat-label {
            font-size: 12px;
            color: #6c757d;
        }

        .benefits-summary {
            display: flex;
            gap: 30px;
        }

        .summary-stat {
            text-align: center;
        }

        .summary-stat .stat-value {
            display: block;
            font-size: 24px;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 5px;
        }

        .summary-stat .stat-label {
            font-size: 14px;
            color: #6c757d;
        }

        .benefit-icon {
            margin-bottom: 15px;
        }

        .benefit-icon i {
            font-size: 32px;
        }

        .benefit-content {
            flex: 1;
        }

        .benefit-config {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin: 10px 0;
        }

        .config-item {
            font-size: 13px;
            color: #6c757d;
            padding: 4px 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .benefit-usage {
            margin: 10px 0;
        }

        .usage-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .usage-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            border-radius: 3px;
        }

        .usage-text {
            font-size: 12px;
            color: #6c757d;
        }

        .benefit-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        /* 会员活动特有样式 */
        .activity-banner {
            position: relative;
            min-width: 120px;
            margin-right: 20px;
        }

        .activity-image {
            width: 120px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
        }

        .activity-type {
            position: absolute;
            top: 5px;
            left: 5px;
            padding: 2px 8px;
            background: rgba(0,0,0,0.7);
            color: white;
            font-size: 11px;
            border-radius: 12px;
        }

        .activity-schedule {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin: 10px 0;
        }

        .schedule-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #6c757d;
        }

        .schedule-item i {
            width: 14px;
            color: #007bff;
        }

        .activity-stats {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .activity-rewards,
        .activity-results,
        .activity-targets {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin: 10px 0;
        }

        .reward-item,
        .result-item,
        .target-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 13px;
        }

        .reward-name,
        .result-label,
        .target-name {
            color: #6c757d;
        }

        .reward-desc,
        .result-value,
        .target-desc {
            color: #007bff;
            font-weight: 600;
        }

        /* 其他样式增强 */
        .btn-outline {
            padding: 8px 16px;
            border: 1px solid #007bff;
            background: transparent;
            color: #007bff;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-outline:hover {
            background: #007bff;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="marketing-page">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-bullhorn"></i>
                高级营销管理
            </h1>
            <div class="page-actions">
                <button class="action-btn" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    刷新数据
                </button>
                <button class="action-btn primary" onclick="createCampaign()">
                    <i class="fas fa-plus"></i>
                    创建活动
                </button>
            </div>
        </div>

        <!-- 营销概览 -->
        <div class="marketing-overview">
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">活跃活动</div>
                    <div class="metric-icon" style="background: #dbeafe; color: #1d4ed8;">
                        <i class="fas fa-play-circle"></i>
                    </div>
                </div>
                <div class="metric-value">12</div>
                <div class="metric-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    较上月增加 3个
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">总转化率</div>
                    <div class="metric-icon" style="background: #dcfce7; color: #166534;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="metric-value">8.5%</div>
                <div class="metric-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    提升 1.2%
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">营销支出</div>
                    <div class="metric-icon" style="background: #fef3c7; color: #92400e;">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="metric-value">¥45,280</div>
                <div class="metric-change change-negative">
                    <i class="fas fa-arrow-down"></i>
                    较上月减少 8%
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">ROI</div>
                    <div class="metric-icon" style="background: #dcfce7; color: #166534;">
                        <i class="fas fa-trophy"></i>
                    </div>
                </div>
                <div class="metric-value">3.2x</div>
                <div class="metric-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    投资回报率
                </div>
            </div>
        </div>

        <!-- 营销活动列表 -->
        <div class="campaigns-section">
            <div class="content-header">
                <h2 class="content-title">当前营销活动</h2>
                <div class="search-filters">
                    <input type="text" class="search-input" placeholder="搜索活动名称..." onkeyup="searchCampaigns(this.value)">
                    <select class="filter-select" onchange="filterByStatus(this.value)">
                        <option value="">全部状态</option>
                        <option value="active">进行中</option>
                        <option value="paused">已暂停</option>
                        <option value="ended">已结束</option>
                    </select>
                    <button class="filter-toggle" onclick="toggleFilterPanel()">
                        <i class="fas fa-filter"></i> 高级筛选
                    </button>
                </div>
            </div>
            
            <div class="campaign-item" data-campaign-id="1" data-campaign-name="春季大促销" data-status="active">
                <div class="campaign-status active"></div>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级营销管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .marketing-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .marketing-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .metric-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .metric-title {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }
        
        .metric-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .metric-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .change-positive {
            color: #10b981;
        }
        
        .change-negative {
            color: #ef4444;
        }
        
        .campaigns-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 32px;
        }
        
        .campaign-item {
            padding: 20px 24px;
            border-bottom: 1px solid #f1f5f9;
            display: grid;
            grid-template-columns: auto 1fr auto auto;
            gap: 16px;
            align-items: center;
        }
        
        .campaign-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .campaign-status.active {
            background: #10b981;
        }
        
        .campaign-status.paused {
            background: #f59e0b;
        }
        
        .campaign-status.ended {
            background: #64748b;
        }
        
        .campaign-info h4 {
            color: #1e293b;
            margin: 0 0 4px 0;
            font-size: 16px;
        }
        
        .campaign-info p {
            color: #64748b;
            margin: 0;
            font-size: 14px;
        }
        
        .campaign-metrics {
            display: flex;
            gap: 24px;
        }
        
        .campaign-metric {
            text-align: center;
        }
        
        .campaign-metric-value {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .campaign-metric-label {
            font-size: 12px;
            color: #64748b;
        }
        
        .campaign-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn-sm {
            padding: 6px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            text-align: center;
            min-height: 32px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }
        
        /* 正常状态hover效果 */
        .action-btn-sm:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* 点击激活状态 */
        .action-btn-sm:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            background: #e2e8f0;
        }
        
        /* 主要按钮样式 */
        .action-btn-sm.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .action-btn-sm.primary:hover {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }

        .action-btn-sm.primary:active {
            background: #1d4ed8;
            border-color: #1d4ed8;
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        /* 成功按钮样式 */
        .action-btn-sm.success {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }

        .action-btn-sm.success:hover {
            background: #059669;
            border-color: #059669;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
        }

        .action-btn-sm.success:active {
            background: #047857;
            border-color: #047857;
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
        }

        /* 危险按钮样式 */
        .action-btn-sm.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }

        .action-btn-sm.danger:hover {
            background: #dc2626;
            border-color: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
        }

        .action-btn-sm.danger:active {
            background: #b91c1c;
            border-color: #b91c1c;
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }

        /* 禁用状态 */
        .action-btn-sm:disabled {
            background: #f1f5f9;
            color: #94a3b8;
            border-color: #e2e8f0;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        /* 点击涟漪效果 */
        .action-btn-sm::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }

        .action-btn-sm:active::before {
            width: 120%;
            height: 120%;
        }

        /* 加载状态 */
        .action-btn-sm.loading {
            pointer-events: none;
            position: relative;
        }

        .action-btn-sm.loading::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: button-loading 1s linear infinite;
        }

        @keyframes button-loading {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .campaign-item.hidden {
            display: none;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .tool-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #cbd5e1;
        }

        /* 点击状态 */
        .tool-card:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* 可点击卡片样式 */
        .tool-card[style*="cursor: pointer"] {
            cursor: pointer;
        }

        /* 卡片点击涟漪效果 */
        .tool-card[style*="cursor: pointer"]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.05), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .tool-card[style*="cursor: pointer"]:hover::before {
            opacity: 1;
        }

        /* 点击时的涟漪效果 */
        .tool-card[style*="cursor: pointer"]:active::before {
            background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            opacity: 1;
        }

        .tool-card[style*="cursor: pointer"] .tool-header {
            position: relative;
        }

        /* 悬停提示 */
        .tool-card[style*="cursor: pointer"] .tool-header::after {
            content: '点击进入管理';
            position: absolute;
            top: 0;
            right: 0;
            font-size: 12px;
            color: #64748b;
            background: rgba(255, 255, 255, 0.95);
            padding: 4px 8px;
            border-radius: 6px;
            opacity: 0;
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .tool-card[style*="cursor: pointer"]:hover .tool-header::after {
            opacity: 1;
            transform: translateY(0);
        }

        /* 卡片加载状态 */
        .tool-card.loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .tool-card.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 40px;
            height: 40px;
            margin: -20px 0 0 -20px;
            border: 3px solid rgba(59, 130, 246, 0.3);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: card-loading 1s linear infinite;
            z-index: 10;
        }

        .tool-card.loading::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            z-index: 5;
        }

        @keyframes card-loading {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .tool-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .tool-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .tool-description {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        
        .tool-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
        }
        
        .tool-stat {
            text-align: center;
        }
        
        .tool-stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .tool-stat-label {
            font-size: 11px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .tool-actions {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 6px;
            margin-top: 16px;
        }

        /* 新增的功能描述样式 */
        .desc-main {
            font-size: 14px;
            color: #4a5568;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .desc-features {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 16px;
        }

        .feature-tag {
            background: #f7fafc;
            color: #2d3748;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid #e2e8f0;
        }

        /* 状态指示器样式 */
        .tool-status {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding: 8px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy {
            background: #10b981;
        }

        .status-warning {
            background: #f59e0b;
        }

        .status-attention {
            background: #ef4444;
        }

        .status-text {
            flex: 1;
            font-size: 12px;
            color: #64748b;
        }

        .trend-indicator {
            display: flex;
            align-items: center;
            font-size: 11px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 8px;
        }

        .trend-up {
            background: #dcfce7;
            color: #166534;
        }

        .trend-down {
            background: #fef2f2;
            color: #dc2626;
        }

        /* 统计数据趋势 */
        .tool-stat-trend {
            font-size: 10px;
            font-weight: 600;
            margin-top: 2px;
            color: #10b981;
        }
        
        .performance-charts {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }
        
        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        @media (max-width: 768px) {
            .marketing-page {
                padding: 16px;
            }
            
            .marketing-overview {
                grid-template-columns: 1fr;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .performance-charts {
                grid-template-columns: 1fr;
            }
            
            .campaign-item {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .campaign-metrics {
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="marketing-page">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-bullhorn"></i>
                高级营销管理
            </h1>
            <div class="page-actions">
                <button class="action-btn" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    刷新数据
                </button>
                <button class="action-btn primary" onclick="createCampaign()">
                    <i class="fas fa-plus"></i>
                    创建活动
                </button>
            </div>
        </div>

        <!-- 营销概览 -->
        <div class="marketing-overview">
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">活跃活动</div>
                    <div class="metric-icon" style="background: #dbeafe; color: #1d4ed8;">
                        <i class="fas fa-play-circle"></i>
                    </div>
                </div>
                <div class="metric-value">12</div>
                <div class="metric-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    较上月增加 3个
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">总转化率</div>
                    <div class="metric-icon" style="background: #dcfce7; color: #166534;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="metric-value">8.5%</div>
                <div class="metric-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    提升 1.2%
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">营销支出</div>
                    <div class="metric-icon" style="background: #fef3c7; color: #92400e;">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="metric-value">¥45,280</div>
                <div class="metric-change change-negative">
                    <i class="fas fa-arrow-down"></i>
                    较上月减少 8%
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">ROI</div>
                    <div class="metric-icon" style="background: #dcfce7; color: #166534;">
                        <i class="fas fa-trophy"></i>
                    </div>
                </div>
                <div class="metric-value">3.2x</div>
                <div class="metric-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    投资回报率
                </div>
            </div>
        </div>

        <!-- 营销活动列表 -->
        <div class="campaigns-section">
            <div class="content-header">
                <h2 class="content-title">当前营销活动</h2>
                <div class="search-filters">
                    <input type="text" class="search-input" placeholder="搜索活动名称..." onkeyup="searchCampaigns(this.value)">
                    <select class="filter-select" onchange="filterByStatus(this.value)">
                        <option value="">全部状态</option>
                        <option value="active">进行中</option>
                        <option value="paused">已暂停</option>
                        <option value="ended">已结束</option>
                    </select>
                    <button class="filter-toggle" onclick="toggleFilterPanel()">
                        <i class="fas fa-filter"></i> 高级筛选
                    </button>
                </div>
            </div>
            
            <div class="campaign-item" data-campaign-id="1" data-campaign-name="春季大促销" data-status="active">
                <div class="campaign-status active"></div>
                <div class="campaign-info">
                    <h4>春季大促销</h4>
                    <p>全场商品5-8折优惠 | 进行中 | 剩余3天</p>
                </div>
                <div class="campaign-metrics">
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">2,840</div>
                        <div class="campaign-metric-label">参与用户</div>
                    </div>
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">12.5%</div>
                        <div class="campaign-metric-label">转化率</div>
                    </div>
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">¥18,600</div>
                        <div class="campaign-metric-label">收入</div>
                    </div>
                </div>
                <div class="campaign-actions">
                    <button class="action-btn-sm" onclick="viewCampaignDetails(1)">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                    <button class="action-btn-sm primary" onclick="editCampaign(1)">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn-sm" onclick="toggleCampaignStatus(1, 'pause')">
                        <i class="fas fa-pause"></i> 暂停
                    </button>
                    <button class="action-btn-sm danger" onclick="deleteCampaign(1)">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>

            <div class="campaign-item" data-campaign-id="2" data-campaign-name="新用户专享" data-status="active">
                <div class="campaign-status active"></div>
                <div class="campaign-info">
                    <h4>新用户专享</h4>
                    <p>注册即送优惠券 | 进行中 | 长期活动</p>
                </div>
                <div class="campaign-metrics">
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">456</div>
                        <div class="campaign-metric-label">新用户</div>
                    </div>
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">85.2%</div>
                        <div class="campaign-metric-label">激活率</div>
                    </div>
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">¥8,940</div>
                        <div class="campaign-metric-label">收入</div>
                    </div>
                </div>
                <div class="campaign-actions">
                    <button class="action-btn-sm" onclick="viewCampaignDetails(2)">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                    <button class="action-btn-sm primary" onclick="editCampaign(2)">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn-sm" onclick="toggleCampaignStatus(2, 'pause')">
                        <i class="fas fa-pause"></i> 暂停
                    </button>
                    <button class="action-btn-sm danger" onclick="deleteCampaign(2)">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>

            <div class="campaign-item" data-campaign-id="3" data-campaign-name="限时秒杀专场" data-status="paused">
                <div class="campaign-status paused"></div>
                <div class="campaign-info">
                    <h4>限时秒杀专场</h4>
                    <p>特价商品限时抢购 | 已暂停 | 待重启</p>
                </div>
                <div class="campaign-metrics">
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">1,256</div>
                        <div class="campaign-metric-label">参与用户</div>
                    </div>
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">95.8%</div>
                        <div class="campaign-metric-label">抢购率</div>
                    </div>
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">¥24,800</div>
                        <div class="campaign-metric-label">收入</div>
                    </div>
                </div>
                <div class="campaign-actions">
                    <button class="action-btn-sm" onclick="viewCampaignDetails(3)">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                    <button class="action-btn-sm primary" onclick="editCampaign(3)">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn-sm success" onclick="toggleCampaignStatus(3, 'resume')">
                        <i class="fas fa-play"></i> 恢复
                    </button>
                    <button class="action-btn-sm danger" onclick="deleteCampaign(3)">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>

            <div class="campaign-item">
                <div class="campaign-status paused"></div>
                <div class="campaign-info">
                    <h4>会员日活动</h4>
                    <p>VIP专享折扣 | 已暂停 | 准备重启</p>
                </div>
                <div class="campaign-metrics">
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">1,205</div>
                        <div class="campaign-metric-label">参与用户</div>
                    </div>
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">6.8%</div>
                        <div class="campaign-metric-label">转化率</div>
                    </div>
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">¥12,400</div>
                        <div class="campaign-metric-label">收入</div>
                    </div>
                </div>
                <div class="campaign-actions">
                    <button class="action-btn-sm">查看</button>
                    <button class="action-btn-sm primary">编辑</button>
                    <button class="action-btn-sm primary">启动</button>
                </div>
            </div>
        </div>

        <!-- 营销工具 -->
        <div class="tools-grid">
            <div class="tool-card" onclick="openCouponManagement()" style="cursor: pointer;">
                <div class="tool-header">
                    <div class="tool-icon" style="background: #dbeafe; color: #1d4ed8;">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="tool-title">优惠券管理</div>
                </div>
                <div class="tool-description">
                    <div class="desc-main">智能优惠券营销系统，支持多场景投放、效果追踪和自动优化</div>
                    <div class="desc-features">
                        <span class="feature-tag">智能投放</span>
                        <span class="feature-tag">A/B测试</span>
                        <span class="feature-tag">效果分析</span>
                        <span class="feature-tag">防刷机制</span>
                    </div>
                </div>
                <div class="tool-status">
                    <div class="status-indicator status-healthy"></div>
                    <span class="status-text">系统运行正常</span>
                    <div class="trend-indicator trend-up">
                        <i class="fas fa-arrow-up"></i> +15%
                    </div>
                </div>
                <div class="tool-stats">
                    <div class="tool-stat">
                        <div class="tool-stat-value">45</div>
                        <div class="tool-stat-label">活跃券</div>
                        <div class="tool-stat-trend">+8</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">2,840</div>
                        <div class="tool-stat-label">已使用</div>
                        <div class="tool-stat-trend">+15%</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">12.5%</div>
                        <div class="tool-stat-label">使用率</div>
                        <div class="tool-stat-trend">+2.3%</div>
                    </div>
                </div>
                <div class="tool-actions" onclick="event.stopPropagation()">
                    <button class="action-btn-sm primary" onclick="openCouponManagement()">
                        <i class="fas fa-cog"></i> 管理优惠券
                    </button>
                    <button class="action-btn-sm" onclick="openCreateCoupon()">
                        <i class="fas fa-plus"></i> 创建新券
                    </button>
                    <button class="action-btn-sm" onclick="viewCouponAnalytics()">
                        <i class="fas fa-chart-bar"></i> 数据分析
                    </button>
                    <button class="action-btn-sm" onclick="openCouponTemplates()">
                        <i class="fas fa-template"></i> 模板库
                    </button>
                    <button class="action-btn-sm" onclick="openCouponRiskControl()">
                        <i class="fas fa-shield-alt"></i> 风控设置
                    </button>
                    <button class="action-btn-sm" onclick="openCouponABTest()">
                        <i class="fas fa-flask"></i> A/B测试
                    </button>
                </div>
            </div>

            <div class="tool-card" onclick="openMemberMarketing()" style="cursor: pointer;">
                <div class="tool-header">
                    <div class="tool-icon" style="background: #dcfce7; color: #166534;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="tool-title">会员营销</div>
                </div>
                <div class="tool-description">
                    <div class="desc-main">全生命周期会员运营，包含成长体系、权益管理和个性化营销</div>
                    <div class="desc-features">
                        <span class="feature-tag">成长体系</span>
                        <span class="feature-tag">专属权益</span>
                        <span class="feature-tag">个性推荐</span>
                        <span class="feature-tag">生日营销</span>
                    </div>
                </div>
                <div class="tool-status">
                    <div class="status-indicator status-healthy"></div>
                    <span class="status-text">会员活跃度良好</span>
                    <div class="trend-indicator trend-up">
                        <i class="fas fa-arrow-up"></i> +8%
                    </div>
                </div>
                <div class="tool-stats">
                    <div class="tool-stat">
                        <div class="tool-stat-value">1,250</div>
                        <div class="tool-stat-label">VIP用户</div>
                        <div class="tool-stat-trend">+45</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">85%</div>
                        <div class="tool-stat-label">活跃度</div>
                        <div class="tool-stat-trend">+8%</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">3.2x</div>
                        <div class="tool-stat-label">消费倍数</div>
                        <div class="tool-stat-trend">+0.4x</div>
                    </div>
                </div>
                <div class="tool-actions" onclick="event.stopPropagation()">
                    <button class="action-btn-sm primary" onclick="openMemberMarketing()">
                        <i class="fas fa-users-cog"></i> 会员策略
                    </button>
                    <button class="action-btn-sm" onclick="openMemberLevelConfig()">
                        <i class="fas fa-layer-group"></i> 等级配置
                    </button>
                    <button class="action-btn-sm" onclick="openMemberBenefits()">
                        <i class="fas fa-crown"></i> 权益管理
                    </button>
                    <button class="action-btn-sm" onclick="openMemberAnalysis()">
                        <i class="fas fa-user-chart"></i> 用户分析
                    </button>
                    <button class="action-btn-sm" onclick="openMemberTagManagement()">
                        <i class="fas fa-tags"></i> 标签管理
                    </button>
                    <button class="action-btn-sm" onclick="openMemberChurnWarning()">
                        <i class="fas fa-exclamation-triangle"></i> 流失预警
                    </button>
                </div>
            </div>

            <div class="tool-card" onclick="openFlashSale()" style="cursor: pointer;">
                <div class="tool-header">
                    <div class="tool-icon" style="background: #fef3c7; color: #92400e;">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="tool-title">限时秒杀</div>
                </div>
                <div class="tool-description">
                    <div class="desc-main">高转化秒杀活动管理，支持预热推广、库存控制和防作弊机制</div>
                    <div class="desc-features">
                        <span class="feature-tag">预热推广</span>
                        <span class="feature-tag">库存控制</span>
                        <span class="feature-tag">防作弊</span>
                        <span class="feature-tag">排期管理</span>
                    </div>
                </div>
                <div class="tool-status">
                    <div class="status-indicator status-warning"></div>
                    <span class="status-text">3个活动即将开始</span>
                    <div class="trend-indicator trend-up">
                        <i class="fas fa-arrow-up"></i> +25%
                    </div>
                </div>
                <div class="tool-stats">
                    <div class="tool-stat">
                        <div class="tool-stat-value">8</div>
                        <div class="tool-stat-label">进行中</div>
                        <div class="tool-stat-trend">+3</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">95%</div>
                        <div class="tool-stat-label">抢购率</div>
                        <div class="tool-stat-trend">+5%</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">¥28,500</div>
                        <div class="tool-stat-label">总销售</div>
                        <div class="tool-stat-trend">+25%</div>
                    </div>
                </div>
                <div class="tool-actions" onclick="event.stopPropagation()">
                    <button class="action-btn-sm primary" onclick="openFlashSale()">
                        <i class="fas fa-bolt"></i> 管理秒杀
                    </button>
                    <button class="action-btn-sm" onclick="openAddFlashSaleProduct()">
                        <i class="fas fa-plus-circle"></i> 新增商品
                    </button>
                    <button class="action-btn-sm" onclick="openFlashSaleSchedule()">
                        <i class="fas fa-calendar-alt"></i> 活动排期
                    </button>
                    <button class="action-btn-sm" onclick="openFlashSaleAnalytics()">
                        <i class="fas fa-chart-line"></i> 效果分析
                    </button>
                    <button class="action-btn-sm" onclick="openFlashSaleInventoryWarning()">
                        <i class="fas fa-warehouse"></i> 库存预警
                    </button>
                    <button class="action-btn-sm" onclick="openFlashSaleAntiBot()">
                        <i class="fas fa-robot"></i> 防刷机制
                    </button>
                </div>
            </div>

            <div class="tool-card" onclick="openSocialShare()" style="cursor: pointer;">
                <div class="tool-header">
                    <div class="tool-icon" style="background: #f3e8ff; color: #7c3aed;">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <div class="tool-title">社交分享</div>
                </div>
                <div class="tool-description">
                    <div class="desc-main">社交裂变营销引擎，支持多平台分享、邀请奖励和病毒式传播</div>
                    <div class="desc-features">
                        <span class="feature-tag">多平台</span>
                        <span class="feature-tag">邀请奖励</span>
                        <span class="feature-tag">裂变传播</span>
                        <span class="feature-tag">分享模板</span>
                    </div>
                </div>
                <div class="tool-status">
                    <div class="status-indicator status-healthy"></div>
                    <span class="status-text">分享活跃度高</span>
                    <div class="trend-indicator trend-up">
                        <i class="fas fa-arrow-up"></i> +18%
                    </div>
                </div>
                <div class="tool-stats">
                    <div class="tool-stat">
                        <div class="tool-stat-value">5,680</div>
                        <div class="tool-stat-label">分享次数</div>
                        <div class="tool-stat-trend">+18%</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">1,240</div>
                        <div class="tool-stat-label">新用户</div>
                        <div class="tool-stat-trend">+186</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">22%</div>
                        <div class="tool-stat-label">转化率</div>
                        <div class="tool-stat-trend">+3.2%</div>
                    </div>
                </div>
                <div class="tool-actions" onclick="event.stopPropagation()">
                    <button class="action-btn-sm primary" onclick="openSocialShare()">
                        <i class="fas fa-share-alt"></i> 分享设置
                    </button>
                    <button class="action-btn-sm" onclick="openShareRewardConfig()">
                        <i class="fas fa-gift"></i> 奖励配置
                    </button>
                    <button class="action-btn-sm" onclick="openShareTemplates()">
                        <i class="fas fa-file-image"></i> 分享模板
                    </button>
                    <button class="action-btn-sm" onclick="openInviteSystem()">
                        <i class="fas fa-user-plus"></i> 邀请体系
                    </button>
                    <button class="action-btn-sm" onclick="openShareRealTimeMonitor()">
                        <i class="fas fa-eye"></i> 实时监控
                    </button>
                    <button class="action-btn-sm" onclick="openShareContentReview()">
                        <i class="fas fa-shield-check"></i> 内容审核
                    </button>
                </div>
            </div>

            <div class="tool-card" onclick="openPointsMall()" style="cursor: pointer;">
                <div class="tool-header">
                    <div class="tool-icon" style="background: #fce7f3; color: #be185d;">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div class="tool-title">积分商城</div>
                </div>
                <div class="tool-description">
                    <div class="desc-main">积分营销生态系统，涵盖积分获取、消耗、营销活动全流程</div>
                    <div class="desc-features">
                        <span class="feature-tag">积分获取</span>
                        <span class="feature-tag">等级权益</span>
                        <span class="feature-tag">营销活动</span>
                        <span class="feature-tag">到期提醒</span>
                    </div>
                </div>
                <div class="tool-status">
                    <div class="status-indicator status-healthy"></div>
                    <span class="status-text">积分活跃度良好</span>
                    <div class="trend-indicator trend-up">
                        <i class="fas fa-arrow-up"></i> +12%
                    </div>
                </div>
                <div class="tool-stats">
                    <div class="tool-stat">
                        <div class="tool-stat-value">156</div>
                        <div class="tool-stat-label">商品数</div>
                        <div class="tool-stat-trend">+23</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">890</div>
                        <div class="tool-stat-label">兑换次数</div>
                        <div class="tool-stat-trend">+12%</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">68%</div>
                        <div class="tool-stat-label">兑换率</div>
                        <div class="tool-stat-trend">+5%</div>
                    </div>
                </div>
                <div class="tool-actions" onclick="event.stopPropagation()">
                    <button class="action-btn-sm primary" onclick="openPointsMall()">
                        <i class="fas fa-store"></i> 商城管理
                    </button>
                    <button class="action-btn-sm" onclick="openPointsConfig()">
                        <i class="fas fa-cog"></i> 积分设置
                    </button>
                    <button class="action-btn-sm" onclick="openPointsActivity()">
                        <i class="fas fa-calendar-check"></i> 积分活动
                    </button>
                    <button class="action-btn-sm" onclick="openPointsAnalysis()">
                        <i class="fas fa-chart-pie"></i> 积分分析
                    </button>
                    <button class="action-btn-sm" onclick="openPointsAntiCheat()">
                        <i class="fas fa-user-shield"></i> 防刷保护
                    </button>
                    <button class="action-btn-sm" onclick="openPointsExpiryReminder()">
                        <i class="fas fa-bell"></i> 到期提醒
                    </button>
                </div>
            </div>

            <div class="tool-card" onclick="openTargetedPush()" style="cursor: pointer;">
                <div class="tool-header">
                    <div class="tool-icon" style="background: #ecfdf5; color: #059669;">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <div class="tool-title">精准推送</div>
                </div>
                <div class="tool-description">
                    <div class="desc-main">智能营销推送平台，基于用户画像实现多渠道个性化触达</div>
                    <div class="desc-features">
                        <span class="feature-tag">用户画像</span>
                        <span class="feature-tag">多渠道</span>
                        <span class="feature-tag">个性化</span>
                        <span class="feature-tag">时机优化</span>
                    </div>
                </div>
                <div class="tool-status">
                    <div class="status-indicator status-attention"></div>
                    <span class="status-text">推送效果需优化</span>
                    <div class="trend-indicator trend-down">
                        <i class="fas fa-arrow-down"></i> -2%
                    </div>
                </div>
                <div class="tool-stats">
                    <div class="tool-stat">
                        <div class="tool-stat-value">12,450</div>
                        <div class="tool-stat-label">推送量</div>
                        <div class="tool-stat-trend">+5%</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">28%</div>
                        <div class="tool-stat-label">打开率</div>
                        <div class="tool-stat-trend">-2%</div>
                    </div>
                    <div class="tool-stat">
                        <div class="tool-stat-value">8.5%</div>
                        <div class="tool-stat-label">点击率</div>
                        <div class="tool-stat-trend">-0.8%</div>
                    </div>
                </div>
                <div class="tool-actions" onclick="event.stopPropagation()">
                    <button class="action-btn-sm primary" onclick="openTargetedPush()">
                        <i class="fas fa-paper-plane"></i> 推送管理
                    </button>
                    <button class="action-btn-sm" onclick="openCreatePush()">
                        <i class="fas fa-plus"></i> 创建推送
                    </button>
                    <button class="action-btn-sm" onclick="openUserSegments()">
                        <i class="fas fa-users"></i> 用户分群
                    </button>
                    <button class="action-btn-sm" onclick="openPushAnalytics()">
                        <i class="fas fa-chart-area"></i> 推送分析
                    </button>
                    <button class="action-btn-sm" onclick="openPushTimingOptimization()">
                        <i class="fas fa-clock"></i> 时机优化
                    </button>
                    <button class="action-btn-sm" onclick="openPushFrequencyControl()">
                        <i class="fas fa-sliders-h"></i> 频率控制
                    </button>
                </div>
            </div>
        </div>

        <!-- 性能分析图表 -->
        <div class="performance-charts">
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">营销效果趋势</div>
                    <div class="chart-filters">
                        <button class="filter-btn active" onclick="setTimeRange('7d')">7天</button>
                        <button class="filter-btn" onclick="setTimeRange('30d')">30天</button>
                        <button class="filter-btn" onclick="setTimeRange('90d')">90天</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">渠道效果对比</div>
                </div>
                <div class="chart-container">
                    <canvas id="channelChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑活动模态框 -->
    <div id="campaignModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">创建营销活动</h3>
                <span class="close">&times;</span>
            </div>
            <form id="campaignForm" class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label>活动名称 *</label>
                        <input type="text" id="campaignName" required>
                    </div>
                    <div class="form-group">
                        <label>活动类型 *</label>
                        <select id="campaignType" required>
                            <option value="">请选择</option>
                            <option value="discount">折扣促销</option>
                            <option value="coupon">优惠券</option>
                            <option value="flash_sale">限时秒杀</option>
                            <option value="group_buy">团购</option>
                            <option value="member_special">会员专享</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>开始时间 *</label>
                        <input type="datetime-local" id="startTime" required>
                    </div>
                    <div class="form-group">
                        <label>结束时间 *</label>
                        <input type="datetime-local" id="endTime" required>
                    </div>
                    <div class="form-group full-width">
                        <label>活动描述</label>
                        <textarea id="campaignDescription" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>折扣类型</label>
                        <select id="discountType">
                            <option value="percentage">百分比折扣</option>
                            <option value="fixed">固定金额</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>折扣值</label>
                        <input type="number" id="discountValue" placeholder="如：20 表示8折或20元">
                    </div>
                    <div class="form-group">
                        <label>最低消费金额</label>
                        <input type="number" id="minAmount" placeholder="0表示无限制">
                    </div>
                    <div class="form-group">
                        <label>使用限制</label>
                        <input type="number" id="usageLimit" placeholder="0表示无限制">
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn-secondary" onclick="closeCampaignModal()">取消</button>
                    <button type="submit" class="btn-primary">保存活动</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 高级筛选面板 -->
    <div id="filterPanel" class="filter-panel">
        <div class="filter-content">
            <div class="filter-header">
                <h4>高级筛选</h4>
                <button class="close-filter" onclick="toggleFilterPanel()">&times;</button>
            </div>
            <div class="filter-body">
                <div class="filter-group">
                    <label>活动状态</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" value="active" checked> 进行中</label>
                        <label><input type="checkbox" value="paused"> 已暂停</label>
                        <label><input type="checkbox" value="ended"> 已结束</label>
                    </div>
                </div>
                <div class="filter-group">
                    <label>活动类型</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" value="discount"> 折扣促销</label>
                        <label><input type="checkbox" value="coupon"> 优惠券</label>
                        <label><input type="checkbox" value="flash_sale"> 限时秒杀</label>
                    </div>
                </div>
                <div class="filter-group">
                    <label>日期范围</label>
                    <div class="date-range">
                        <input type="date" id="dateFrom">
                        <span>至</span>
                        <input type="date" id="dateTo">
                    </div>
                </div>
                <div class="filter-actions">
                    <button class="btn-secondary" onclick="resetFilters()">重置</button>
                    <button class="btn-primary" onclick="applyFilters()">应用筛选</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .close {
            color: #64748b;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #1e293b;
        }

        .modal-body {
            padding: 24px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid #e2e8f0;
        }

        .btn-secondary {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 6px;
            cursor: pointer;
        }

        .btn-primary {
            padding: 8px 16px;
            border: none;
            background: #3b82f6;
            color: white;
            border-radius: 6px;
            cursor: pointer;
        }

        /* 筛选面板样式 */
        .filter-panel {
            display: none;
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            z-index: 999;
            transition: right 0.3s ease;
        }

        .filter-panel.active {
            right: 0;
        }

        .filter-content {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .filter-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .filter-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .filter-group {
            margin-bottom: 24px;
        }

        .filter-group label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            display: block;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: normal;
            margin-bottom: 0;
        }

        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-range input {
            flex: 1;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
        }

        .filter-actions {
            display: flex;
            gap: 12px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .search-filters {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            width: 250px;
        }

        .filter-toggle {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
        }

        /* 营销工具管理界面样式 */
        .tool-management .management-tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
        }

        .tool-management .tab-btn {
            padding: 10px 20px;
            border: none;
            background: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #6c757d;
            font-weight: 500;
        }

        .tool-management .tab-btn.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }

        .tool-management .tab-panel {
            display: none;
        }

        .tool-management .tab-panel.active {
            display: block;
        }

        /* 平台配置样式 */
        .platform-list,
        .invite-list,
        .points-product-list,
        .rule-list,
        .order-list,
        .campaign-list,
        .segment-list,
        .template-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .platform-item,
        .invite-item,
        .product-item,
        .rule-item,
        .order-item,
        .campaign-item,
        .segment-item,
        .template-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .platform-info,
        .invite-info,
        .product-info,
        .rule-info,
        .order-info,
        .campaign-info,
        .segment-info,
        .template-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .platform-info {
            flex-direction: row;
            align-items: center;
            gap: 10px;
        }

        .platform-info i {
            font-size: 24px;
            color: #28a745;
        }

        .platform-status,
        .invite-status,
        .campaign-status,
        .order-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            min-width: 80px;
        }

        .platform-status.active,
        .invite-status.active {
            background: #d4edda;
            color: #155724;
        }

        .platform-status.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .campaign-status.scheduled {
            background: #fff3cd;
            color: #856404;
        }

        .campaign-status.completed {
            background: #d4edda;
            color: #155724;
        }

        .order-status.completed {
            background: #d4edda;
            color: #155724;
        }

        .order-status.pending {
            background: #fff3cd;
            color: #856404;
        }

        /* 配置区域样式 */
        .config-section,
        .rules-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .config-section h4,
        .rules-section h4 {
            margin-bottom: 15px;
            color: #495057;
        }

        .reward-config {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        /* 统计数据样式 */
        .invite-stats,
        .product-stats,
        .campaign-stats,
        .segment-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .order-time,
        .campaign-schedule {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .campaign-schedule {
            color: #007bff;
        }

        /* 列表操作样式 */
        .list-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .list-actions select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }

        /* 操作按钮样式 */
        .product-actions,
        .rule-actions,
        .order-actions,
        .campaign-actions,
        .segment-actions,
        .template-actions {
            display: flex;
            gap: 8px;
        }

        /* 重复定义已移除，统一使用上方的样式定义 */

        /* 图表区域样式 */
        .chart-section {
            margin-top: 25px;
        }

        .chart-section h4 {
            margin-bottom: 15px;
            color: #495057;
        }

        /* 模态框表单样式 */
        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group textarea {
            min-height: 80px;
            resize: vertical;
        }

        .form-group select[multiple] {
            min-height: 100px;
        }

        .date-range {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .date-range input {
            flex: 1;
        }

        .date-range span {
            color: #6b7280;
            font-weight: 500;
        }

        .benefit-settings {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .benefit-settings label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: normal;
            margin-bottom: 0;
        }

        .benefit-settings input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .template-card {
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .template-card:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .template-card h4 {
            margin: 0 0 8px 0;
            color: #1f2937;
        }

        .template-card p {
            margin: 0;
            color: #6b7280;
            font-size: 13px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
        }

        .stat-card .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .stat-card .stat-label {
            font-size: 12px;
            color: #64748b;
        }

        .stats-overview {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .stats-overview .stat-item {
            text-align: center;
        }

        .stats-overview .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 5px;
        }

        .stats-overview .stat-label {
            font-size: 12px;
            color: #64748b;
        }

        .analytics-container,
        .benefit-analytics,
        .activity-analytics {
            min-height: 300px;
        }

        .chart-container {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
            color: #6b7280;
        }

        .user-management {
            max-height: 400px;
        }

        .search-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .search-filters input,
        .search-filters select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .search-filters input {
            flex: 1;
        }

        .user-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .user-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .user-item:last-child {
            border-bottom: none;
        }

        .user-info h4 {
            margin: 0 0 4px 0;
            color: #1f2937;
        }

        .user-info p {
            margin: 0;
            color: #6b7280;
            font-size: 12px;
        }

        .user-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .user-status.active {
            background: #dcfce7;
            color: #16a34a;
        }

        .user-actions {
            display: flex;
            gap: 5px;
        }

        .calendar-container {
            min-height: 300px;
        }

        .calendar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .calendar-header button {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            margin-bottom: 15px;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #e5e7eb;
            background: white;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .calendar-day:hover {
            background: #f3f4f6;
        }

        .calendar-day.empty {
            background: transparent;
            border: none;
            cursor: default;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn.primary {
            background: #3b82f6;
            color: white;
        }

        .btn.primary:hover {
            background: #2563eb;
        }

        .btn:not(.primary) {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn:not(.primary):hover {
            background: #e5e7eb;
        }

        /* 会员管理相关样式 */
        .level-list,
        .benefits-grid,
        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
        }

        .level-item,
        .benefit-card,
        .activity-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .level-item:hover,
        .benefit-card:hover,
        .activity-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .benefit-card {
            flex-direction: column;
            align-items: flex-start;
            text-align: left;
        }

        .level-info,
        .activity-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .level-info h4,
        .benefit-card h4,
        .activity-info h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
        }

        .level-info p,
        .benefit-card p,
        .activity-info p {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
            line-height: 1.4;
        }

        .level-benefits {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 10px 0;
        }

        .benefit-tag {
            padding: 4px 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .level-actions,
        .activity-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .action-btn-sm {
            padding: 6px 12px;
            border: 1px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn-sm:hover {
            background: #007bff;
            color: white;
            transform: translateY(-1px);
        }

        .action-btn-sm.danger {
            border-color: #dc3545;
            color: #dc3545;
        }

        .action-btn-sm.danger:hover {
            background: #dc3545;
            color: white;
        }

        .benefit-status,
        .activity-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            margin: 10px 0;
        }

        .benefit-status.active,
        .activity-status.active {
            background: #d4edda;
            color: #155724;
        }

        .benefit-status.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .activity-status.upcoming {
            background: #fff3cd;
            color: #856404;
        }

        /* 统计数据网格样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            padding: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            text-align: center;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            display: block;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }

        /* 优惠券管理样式 */
        .coupon-filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .search-box {
            position: relative;
            min-width: 250px;
        }

        .search-box i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }

        .search-box input {
            width: 100%;
            padding: 10px 10px 10px 35px;
            border: 1px solid #ced4da;
            border-radius: 8px;
            font-size: 14px;
        }

        .filter-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .coupon-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .coupon-item {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .coupon-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.12);
        }

        .coupon-checkbox {
            display: flex;
            align-items: center;
        }

        .coupon-preview {
            min-width: 120px;
        }

        .coupon-visual {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .coupon-amount {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .coupon-condition {
            font-size: 12px;
            opacity: 0.9;
        }

        .coupon-details {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .coupon-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .coupon-header h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
        }

        .coupon-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .coupon-status.active {
            background: #d4edda;
            color: #155724;
        }

        .coupon-info {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .info-item {
            display: flex;
            gap: 5px;
            font-size: 14px;
        }

        .info-item .label {
            color: #6c757d;
            font-weight: 500;
        }

        .info-item .value {
            color: #2c3e50;
        }

        .coupon-stats {
            display: flex;
            gap: 20px;
            padding-top: 10px;
            border-top: 1px solid #e9ecef;
        }

        .coupon-stats .stat-item {
            text-align: center;
        }

        .coupon-stats .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #007bff;
            display: block;
        }

        .coupon-stats .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }

        /* 秒杀活动样式 */
        .flashsale-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .flashsale-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .flashsale-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .flashsale-info h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
        }

        .flashsale-countdown {
            color: #dc3545;
            font-weight: 600;
            font-size: 14px;
        }

        .flashsale-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            margin: 0 15px;
        }

        .flashsale-status.active {
            background: #d4edda;
            color: #155724;
        }

        .flashsale-status.upcoming {
            background: #fff3cd;
            color: #856404;
        }

        .flashsale-actions {
            display: flex;
            gap: 8px;
        }

        .chart-section canvas {
            max-height: 300px;
        }

        /* 产品图片样式 */
        .product-item img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            margin-right: 15px;
        }

        /* 统计卡片网格样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-card {
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-card .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-card .stat-label {
            font-size: 14px;
            color: #6c757d;
        }

        /* 优惠券管理增强样式 */
        .coupon-filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
        }

        .search-box {
            position: relative;
            flex: 1;
        }

        .search-box i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }

        .search-box input {
            padding: 10px 10px 10px 35px;
            border: 1px solid #ddd;
            border-radius: 6px;
            width: 100%;
            background: white;
        }

        .filter-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .coupon-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .coupon-item.enhanced {
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .coupon-item.enhanced:hover {
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0,123,255,0.15);
            transform: translateY(-2px);
        }

        .coupon-checkbox {
            margin-right: 15px;
        }

        .coupon-checkbox input[type="checkbox"] {
            transform: scale(1.2);
        }

        .coupon-preview {
            margin-right: 20px;
        }

        .coupon-visual {
            width: 120px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
        }

        .coupon-amount {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .coupon-condition {
            font-size: 12px;
            opacity: 0.9;
        }

        .coupon-details {
            flex: 1;
            margin-right: 20px;
        }

        .coupon-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .coupon-header h4 {
            margin: 0;
            font-size: 16px;
            color: #333;
        }

        .coupon-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .coupon-status.active {
            background: #d4edda;
            color: #155724;
        }

        .coupon-status.paused {
            background: #fff3cd;
            color: #856404;
        }

        .coupon-status.expired {
            background: #f8d7da;
            color: #721c24;
        }

        .coupon-status.draft {
            background: #e2e3e5;
            color: #495057;
        }

        .coupon-info {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .info-item .label {
            font-size: 12px;
            color: #6c757d;
        }

        .info-item .value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .coupon-stats {
            display: flex;
            gap: 20px;
        }

        .coupon-stats .stat-item {
            text-align: center;
        }

        .coupon-stats .stat-value {
            font-size: 14px;
            font-weight: bold;
            color: #007bff;
        }

        .coupon-stats .stat-label {
            font-size: 12px;
            color: #6c757d;
        }

        .coupon-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 32px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #f8f9fa;
        }

        .action-btn.primary {
            border-color: #007bff;
            color: #007bff;
        }

        .action-btn.primary:hover {
            background: #007bff;
            color: white;
        }

        .action-btn.warning {
            border-color: #ffc107;
            color: #ffc107;
        }

        .action-btn.warning:hover {
            background: #ffc107;
            color: white;
        }

        .action-btn.danger {
            border-color: #dc3545;
            color: #dc3545;
        }

        .action-btn.danger:hover {
            background: #dc3545;
            color: white;
        }

        .action-btn.success {
            border-color: #28a745;
            color: #28a745;
        }

        .action-btn.success:hover {
            background: #28a745;
            color: white;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .page-btn:hover {
            background: #f8f9fa;
        }

        .page-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .page-info {
            color: #6c757d;
            font-size: 14px;
            margin-left: 20px;
        }

        /* 创建优惠券表单样式 */
        .create-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }

        .enhanced-coupon-form {
            background: white;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .form-section h4 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 18px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group label {
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .form-group input,
        .form-group select {
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .form-hint {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }

        .advanced-options {
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
            margin-top: 20px;
        }

        .advanced-options h5 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }

        .checkbox-group label {
            flex-direction: row;
            align-items: center;
            gap: 8px;
            font-weight: normal;
        }

        .checkbox-group input[type="checkbox"] {
            margin: 0;
            transform: scale(1.1);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        /* 实时预览样式 */
        .preview-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            position: sticky;
            top: 20px;
        }

        .preview-section h4 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 16px;
        }

        .coupon-preview-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .preview-coupon {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            color: white;
        }

        .preview-amount {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .preview-condition {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 15px;
        }

        .preview-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .preview-validity {
            font-size: 12px;
            opacity: 0.8;
        }

        .preview-stats h5 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 14px;
        }

        .estimated-stats {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .estimated-stats .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .estimated-stats .label {
            font-size: 13px;
            color: #6c757d;
        }

        .estimated-stats .value {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }

        /* 数据统计增强样式 */
        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .stats-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .stats-overview {
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card.highlight {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .stat-content {
            flex: 1;
        }

        .stat-content .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .stat-content .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 4px;
        }

        .stat-trend {
            font-size: 12px;
            font-weight: 500;
        }

        .stat-trend.up {
            color: #28a745;
        }

        .stat-trend.down {
            color: #dc3545;
        }

        /* 图表区域样式 */
        .stats-charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
        }

        .chart-section h5 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 16px;
        }

        /* 热门优惠券排行榜样式 */
        .top-coupons {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
        }

        .top-coupons h5 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 16px;
        }

        .ranking-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .ranking-item .rank {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .ranking-item .coupon-name {
            flex: 1;
            font-weight: 500;
            color: #333;
        }

        .ranking-item .usage-rate {
            color: #28a745;
            font-weight: 500;
            font-size: 14px;
        }

        .ranking-item .usage-count {
            color: #6c757d;
            font-size: 14px;
        }

        /* 模板库样式 */
        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .template-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .template-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0,123,255,0.15);
            transform: translateY(-2px);
        }

        .template-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin: 0 auto 15px;
        }

        .template-card h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }

        .template-card p {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
        }

        .template-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ff6b6b;
            color: white;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
    </style>

    <script>
        let currentCampaignId = null;

        function refreshData() {
            showLoading();
            // 模拟API调用
            setTimeout(() => {
                hideLoading();
                updateMetrics();
                updateCampaignList();
                showNotification('数据已刷新', 'success');
            }, 1500);
        }

        function createCampaign() {
            currentCampaignId = null;
            document.getElementById('modalTitle').textContent = '创建营销活动';
            document.getElementById('campaignForm').reset();
            showCampaignModal();
        }

        function editCampaign(id) {
            currentCampaignId = id;
            document.getElementById('modalTitle').textContent = '编辑营销活动';
            // 加载现有活动数据
            loadCampaignData(id);
            showCampaignModal();
        }

        function showCampaignModal() {
            document.getElementById('campaignModal').style.display = 'block';
        }

        function closeCampaignModal() {
            document.getElementById('campaignModal').style.display = 'none';
        }

        function toggleCampaignStatus(id, action) {
            const statusActions = {
                'pause': '暂停',
                'resume': '恢复',
                'stop': '停止'
            };
            
            if (confirm(`确定要${statusActions[action]}这个活动吗？`)) {
                showLoading();
                // 模拟API调用
                setTimeout(() => {
                    hideLoading();
                    updateCampaignStatus(id, action);
                    showNotification(`活动已${statusActions[action]}`, 'success');
                }, 800);
            }
        }

        function deleteCampaign(id) {
            if (confirm('确定要删除这个活动吗？此操作不可恢复。')) {
                showLoading();
                // 模拟API调用
                setTimeout(() => {
                    hideLoading();
                    removeCampaignFromList(id);
                    showNotification('活动已删除', 'success');
                }, 800);
            }
        }

        function loadCampaignData(id) {
            // 模拟加载活动数据
            const campaignData = {
                name: '春季大促销',
                type: 'discount',
                description: '全场商品5-8折优惠',
                startTime: '2024-03-01T00:00',
                endTime: '2024-03-31T23:59',
                discountType: 'percentage',
                discountValue: 20,
                minAmount: 100,
                usageLimit: 1000
            };

            document.getElementById('campaignName').value = campaignData.name;
            document.getElementById('campaignType').value = campaignData.type;
            document.getElementById('campaignDescription').value = campaignData.description;
            document.getElementById('startTime').value = campaignData.startTime;
            document.getElementById('endTime').value = campaignData.endTime;
            document.getElementById('discountType').value = campaignData.discountType;
            document.getElementById('discountValue').value = campaignData.discountValue;
            document.getElementById('minAmount').value = campaignData.minAmount;
            document.getElementById('usageLimit').value = campaignData.usageLimit;
        }

        function toggleFilterPanel() {
            const panel = document.getElementById('filterPanel');
            panel.style.display = panel.style.display === 'block' ? 'none' : 'block';
            panel.classList.toggle('active');
        }

        function applyFilters() {
            const filters = getFilterValues();
            showLoading();
            // 模拟API调用应用筛选
            setTimeout(() => {
                hideLoading();
                updateCampaignList(filters);
                toggleFilterPanel();
                showNotification('筛选已应用', 'info');
            }, 500);
        }

        function resetFilters() {
            document.querySelectorAll('#filterPanel input[type="checkbox"]').forEach(cb => cb.checked = false);
            document.getElementById('dateFrom').value = '';
            document.getElementById('dateTo').value = '';
        }

        function getFilterValues() {
            const statuses = Array.from(document.querySelectorAll('#filterPanel input[type="checkbox"]:checked'))
                .map(cb => cb.value);
            return {
                statuses,
                dateFrom: document.getElementById('dateFrom').value,
                dateTo: document.getElementById('dateTo').value
            };
        }

        function updateCampaignStatus(id, action) {
            // 更新页面上的活动状态
            const campaignElement = document.querySelector(`[data-campaign-id="${id}"]`);
            if (campaignElement) {
                const statusElement = campaignElement.querySelector('.campaign-status');
                switch(action) {
                    case 'pause':
                        statusElement.className = 'campaign-status paused';
                        break;
                    case 'resume':
                        statusElement.className = 'campaign-status active';
                        break;
                    case 'stop':
                        statusElement.className = 'campaign-status ended';
                        break;
                }
            }
        }

        function removeCampaignFromList(id) {
            const campaignElement = document.querySelector(`[data-campaign-id="${id}"]`);
            if (campaignElement) {
                campaignElement.remove();
            }
        }

        function updateMetrics() {
            // 模拟更新指标数据
            const metrics = {
                activeCampaigns: Math.floor(Math.random() * 20) + 5,
                conversionRate: (Math.random() * 10 + 5).toFixed(1),
                spending: Math.floor(Math.random() * 50000) + 30000,
                roi: (Math.random() * 2 + 2).toFixed(1)
            };

            document.querySelector('.metric-card:nth-child(1) .metric-value').textContent = metrics.activeCampaigns;
            document.querySelector('.metric-card:nth-child(2) .metric-value').textContent = metrics.conversionRate + '%';
            document.querySelector('.metric-card:nth-child(3) .metric-value').textContent = '¥' + metrics.spending.toLocaleString();
            document.querySelector('.metric-card:nth-child(4) .metric-value').textContent = metrics.roi + 'x';
        }

        function updateCampaignList(filters = {}) {
            // 模拟更新活动列表
            console.log('更新活动列表，筛选条件：', filters);
        }

        function showLoading() {
            // 创建加载指示器
            if (!document.getElementById('loadingOverlay')) {
                const overlay = document.createElement('div');
                overlay.id = 'loadingOverlay';
                overlay.innerHTML = `
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>加载中...</span>
                    </div>
                `;
                overlay.style.cssText = `
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.3); z-index: 9999;
                    display: flex; align-items: center; justify-content: center;
                `;
                overlay.querySelector('.loading-spinner').style.cssText = `
                    background: white; padding: 20px 30px; border-radius: 8px;
                    display: flex; align-items: center; gap: 10px;
                    font-size: 14px; color: #374151;
                `;
                document.body.appendChild(overlay);
            }
        }

        function hideLoading() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.remove();
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                info: 'fas fa-info-circle'
            };
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                info: '#3b82f6'
            };

            notification.innerHTML = `
                <i class="${icons[type]}"></i>
                <span>${message}</span>
            `;
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                background: white; padding: 12px 20px; border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                display: flex; align-items: center; gap: 8px;
                border-left: 4px solid ${colors[type]};
                font-size: 14px; color: #374151;
            `;

            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }

        // 标签页切换函数
        function switchToolTab(button, tabId) {
            // 移除所有按钮的active状态
            button.parentNode.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 移除所有面板的active状态
            const tabContent = button.parentNode.nextElementSibling;
            tabContent.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            
            // 激活当前按钮和面板
            button.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }

        // 营销工具相关函数
        function configPlatform(platform) {
            showNotification(`正在配置${platform}分享平台`, 'info');
        }

        function editInviteRule(id) {
            showNotification(`编辑邀请规则 #${id}`, 'info');
        }

        function createInviteRule() {
            showNotification('创建新邀请规则', 'info');
        }

        function addPointsProduct() {
            showNotification('添加积分商品', 'info');
        }

        function editPointsProduct(id) {
            showNotification(`编辑积分商品 #${id}`, 'info');
        }

        function viewProductStats(id) {
            showNotification(`查看商品数据 #${id}`, 'info');
        }

        function removePointsProduct(id) {
            if (confirm('确定要下架这个商品吗？')) {
                showNotification(`商品 #${id} 已下架`, 'success');
            }
        }

        function filterPointsProducts(category) {
            showNotification(`按分类筛选: ${category || '全部'}`, 'info');
        }

        function editRule(type) {
            showNotification(`编辑${type}规则`, 'info');
        }

        function viewOrderDetail(orderId) {
            showNotification(`查看订单详情: ${orderId}`, 'info');
        }

        function createPushCampaign() {
            showNotification('创建推送活动', 'info');
        }

        function editPushCampaign(id) {
            showNotification(`编辑推送活动 #${id}`, 'info');
        }

        function previewPush(id) {
            showNotification(`预览推送 #${id}`, 'info');
        }

        function cancelPush(id) {
            if (confirm('确定要取消这个推送吗？')) {
                showNotification(`推送 #${id} 已取消`, 'success');
            }
        }

        function viewPushStats(id) {
            showNotification(`查看推送数据 #${id}`, 'info');
        }

        function copyPushCampaign(id) {
            showNotification(`推送活动 #${id} 已复制`, 'success');
        }

        function filterPushCampaigns(status) {
            showNotification(`筛选推送状态: ${status || '全部'}`, 'info');
        }

        function editSegment(id) {
            showNotification(`编辑用户群 #${id}`, 'info');
        }

        function viewSegmentUsers(id) {
            showNotification(`查看用户群 #${id} 用户列表`, 'info');
        }

        function pushToSegment(id) {
            showNotification(`向用户群 #${id} 推送消息`, 'info');
        }

        function createUserSegment() {
            showNotification('创建用户群', 'info');
        }

        function editTemplate(id) {
            showNotification(`编辑模板 #${id}`, 'info');
        }

        function previewTemplate(id) {
            showNotification(`预览模板 #${id}`, 'info');
        }

        function useTemplate(id) {
            showNotification(`使用模板 #${id} 创建推送`, 'info');
        }

        function createTemplate() {
            showNotification('创建新模板', 'info');
        }

        function setTimeRange(range) {
            const buttons = document.querySelectorAll('.filter-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 更新图表数据
            updateChart(range);
        }

        function updateChart(timeRange) {
            // 根据时间范围更新图表数据
            const sampleData = {
                '7d': {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    conversion: [8.2, 9.1, 8.5, 10.2, 12.5, 15.8, 11.3],
                    clicks: [25.5, 28.2, 24.8, 32.1, 35.6, 42.3, 38.9]
                },
                '30d': {
                    labels: ['第1周', '第2周', '第3周', '第4周'],
                    conversion: [9.5, 11.2, 10.8, 12.3],
                    clicks: [32.1, 35.6, 33.2, 38.9]
                },
                '90d': {
                    labels: ['第1月', '第2月', '第3月'],
                    conversion: [10.1, 11.5, 12.2],
                    clicks: [34.5, 37.2, 39.8]
                }
            };

            const data = sampleData[timeRange] || sampleData['7d'];
            
            // 更新现有图表
            if (window.performanceChart) {
                window.performanceChart.data.labels = data.labels;
                window.performanceChart.data.datasets[0].data = data.conversion;
                window.performanceChart.data.datasets[1].data = data.clicks;
                window.performanceChart.update();
                         }
         }

        function searchCampaigns(query) {
            const campaigns = document.querySelectorAll('.campaign-item');
            campaigns.forEach(campaign => {
                const name = campaign.getAttribute('data-campaign-name').toLowerCase();
                const matchesSearch = name.includes(query.toLowerCase());
                campaign.classList.toggle('hidden', !matchesSearch);
            });
        }

        function filterByStatus(status) {
            const campaigns = document.querySelectorAll('.campaign-item');
            campaigns.forEach(campaign => {
                const campaignStatus = campaign.getAttribute('data-status');
                const matchesFilter = !status || campaignStatus === status;
                campaign.classList.toggle('hidden', !matchesFilter);
            });
        }

        function viewCampaignDetails(id) {
            // 模拟打开活动详情页面
            showCampaignDetailsModal(id);
        }

        function showCampaignDetailsModal(id) {
            const detailsModal = document.createElement('div');
            detailsModal.className = 'modal';
            detailsModal.innerHTML = `
                <div class="modal-content" style="max-width: 800px;">
                    <div class="modal-header">
                        <h3>活动详情</h3>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="details-grid">
                            <div class="detail-section">
                                <h4>基本信息</h4>
                                <div class="detail-item">
                                    <label>活动名称:</label>
                                    <span>春季大促销</span>
                                </div>
                                <div class="detail-item">
                                    <label>活动状态:</label>
                                    <span class="status-badge active">进行中</span>
                                </div>
                                <div class="detail-item">
                                    <label>活动时间:</label>
                                    <span>2024-03-01 至 2024-03-31</span>
                                </div>
                            </div>
                            <div class="detail-section">
                                <h4>效果数据</h4>
                                <div class="metrics-grid">
                                    <div class="metric-box">
                                        <div class="metric-label">参与用户</div>
                                        <div class="metric-value">2,840</div>
                                    </div>
                                    <div class="metric-box">
                                        <div class="metric-label">转化率</div>
                                        <div class="metric-value">12.5%</div>
                                    </div>
                                    <div class="metric-box">
                                        <div class="metric-label">总收入</div>
                                        <div class="metric-value">¥18,600</div>
                                    </div>
                                    <div class="metric-box">
                                        <div class="metric-label">ROI</div>
                                        <div class="metric-value">3.2x</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="chart-section">
                            <h4>效果趋势</h4>
                            <canvas id="detailChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                        <button class="btn-primary" onclick="editCampaign(${id})">编辑活动</button>
                    </div>
                </div>
            `;

            // 添加详情模态框样式
            const style = document.createElement('style');
            style.textContent = `
                .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px; }
                .detail-section h4 { margin: 0 0 16px 0; color: #1e293b; font-size: 16px; }
                .detail-item { display: flex; justify-content: space-between; margin-bottom: 8px; }
                .detail-item label { font-weight: 500; color: #64748b; }
                .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
                .status-badge.active { background: #dcfce7; color: #166534; }
                .metrics-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; }
                .metric-box { text-align: center; padding: 12px; background: #f8fafc; border-radius: 8px; }
                .metric-label { font-size: 12px; color: #64748b; margin-bottom: 4px; }
                .metric-value { font-size: 18px; font-weight: 600; color: #1e293b; }
                .chart-section h4 { margin: 0 0 16px 0; color: #1e293b; }
            `;
            document.head.appendChild(style);
            document.body.appendChild(detailsModal);
            detailsModal.style.display = 'block';

            // 初始化详情图表
            setTimeout(() => {
                const ctx = document.getElementById('detailChart');
                if (ctx) {
                    new Chart(ctx.getContext('2d'), {
                        type: 'line',
                        data: {
                            labels: ['第1天', '第2天', '第3天', '第4天', '第5天', '第6天', '第7天'],
                            datasets: [{
                                label: '参与用户数',
                                data: [120, 190, 300, 500, 420, 680, 840],
                                borderColor: '#3b82f6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                }
            }, 100);
        }

        // 表单提交处理
        document.addEventListener('DOMContentLoaded', function() {
            const campaignForm = document.getElementById('campaignForm');
            if (campaignForm) {
                campaignForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    handleCampaignSubmit();
                });
            }

            // 模态框关闭事件
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal')) {
                    e.target.style.display = 'none';
                }
                if (e.target.classList.contains('close')) {
                    e.target.closest('.modal').style.display = 'none';
                }
            });

            // 权限控制
            checkUserPermissions();
        });

        function handleCampaignSubmit() {
            const formData = {
                name: document.getElementById('campaignName').value,
                type: document.getElementById('campaignType').value,
                description: document.getElementById('campaignDescription').value,
                startTime: document.getElementById('startTime').value,
                endTime: document.getElementById('endTime').value,
                discountType: document.getElementById('discountType').value,
                discountValue: document.getElementById('discountValue').value,
                minAmount: document.getElementById('minAmount').value,
                usageLimit: document.getElementById('usageLimit').value
            };

            // 验证表单
            if (!formData.name || !formData.type || !formData.startTime || !formData.endTime) {
                showNotification('请填写必填字段', 'error');
                return;
            }

            if (new Date(formData.startTime) >= new Date(formData.endTime)) {
                showNotification('结束时间必须晚于开始时间', 'error');
                return;
            }

            showLoading();
            
            // 模拟API提交
            setTimeout(() => {
                hideLoading();
                closeCampaignModal();
                
                if (currentCampaignId) {
                    showNotification('活动已更新', 'success');
                } else {
                    showNotification('活动已创建', 'success');
                    addNewCampaignToList(formData);
                }
                
                updateMetrics();
            }, 1000);
        }

        function addNewCampaignToList(campaignData) {
            const campaignsList = document.querySelector('.campaigns-section');
            const newCampaign = document.createElement('div');
            const campaignId = Date.now(); // 临时ID
            
            newCampaign.className = 'campaign-item';
            newCampaign.setAttribute('data-campaign-id', campaignId);
            newCampaign.setAttribute('data-campaign-name', campaignData.name);
            newCampaign.setAttribute('data-status', 'active');
            
            newCampaign.innerHTML = `
                <div class="campaign-status active"></div>
                <div class="campaign-info">
                    <h4>${campaignData.name}</h4>
                    <p>${campaignData.description} | 进行中 | 刚刚创建</p>
                </div>
                <div class="campaign-metrics">
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">0</div>
                        <div class="campaign-metric-label">参与用户</div>
                    </div>
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">0%</div>
                        <div class="campaign-metric-label">转化率</div>
                    </div>
                    <div class="campaign-metric">
                        <div class="campaign-metric-value">¥0</div>
                        <div class="campaign-metric-label">收入</div>
                    </div>
                </div>
                <div class="campaign-actions">
                    <button class="action-btn-sm" onclick="viewCampaignDetails(${campaignId})">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                    <button class="action-btn-sm primary" onclick="editCampaign(${campaignId})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn-sm" onclick="toggleCampaignStatus(${campaignId}, 'pause')">
                        <i class="fas fa-pause"></i> 暂停
                    </button>
                    <button class="action-btn-sm danger" onclick="deleteCampaign(${campaignId})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            `;
            
            campaignsList.appendChild(newCampaign);
        }

        function checkUserPermissions() {
            // 模拟权限检查
            const userRole = 'admin'; // 可以从后端获取
            const permissions = {
                admin: ['create', 'edit', 'delete', 'view'],
                manager: ['create', 'edit', 'view'],
                viewer: ['view']
            };

            const userPermissions = permissions[userRole] || ['view'];
            
            // 根据权限显示/隐藏功能
            if (!userPermissions.includes('create')) {
                const createBtn = document.querySelector('[onclick="createCampaign()"]');
                if (createBtn) createBtn.style.display = 'none';
            }

            if (!userPermissions.includes('delete')) {
                const deleteButtons = document.querySelectorAll('.action-btn-sm.danger');
                deleteButtons.forEach(btn => btn.style.display = 'none');
            }

            if (!userPermissions.includes('edit')) {
                const editButtons = document.querySelectorAll('.action-btn-sm.primary');
                editButtons.forEach(btn => btn.style.display = 'none');
            }
        }

        // 全局变量存储图表实例
        window.performanceChart = null;
        window.channelChart = null;

        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 营销效果趋势图
            const performanceCtx = document.getElementById('performanceChart').getContext('2d');
            window.performanceChart = new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [{
                        label: '转化率',
                        data: [8.2, 9.1, 8.5, 10.2, 12.5, 15.8, 11.3],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: '点击率',
                        data: [25.5, 28.2, 24.8, 32.1, 35.6, 42.3, 38.9],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });

            // 渠道效果对比图
            const channelCtx = document.getElementById('channelChart').getContext('2d');
            window.channelChart = new Chart(channelCtx, {
                type: 'doughnut',
                data: {
                    labels: ['微信', '微博', 'APP推送', '短信', '邮件'],
                    datasets: [{
                        data: [35, 25, 20, 12, 8],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6'
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${label}: ${value}% (${percentage}%占比)`;
                                }
                            }
                        }
                    }
                }
            });

            // 初始化完成后的操作
            initializeMarketingTools();
        });

        // 初始化营销工具交互
        function initializeMarketingTools() {
            // 为所有营销工具卡片添加点击事件
            const toolCards = document.querySelectorAll('.tool-card');
            toolCards.forEach(card => {
                const title = card.querySelector('.tool-title').textContent;
                card.addEventListener('click', function(e) {
                    if (!e.target.closest('button')) {
                        showMarketingToolModal(title);
                    }
                });
            });

            // 设置定时刷新数据（可选）
            setInterval(() => {
                if (document.visibilityState === 'visible') {
                    updateMetrics();
                }
            }, 30000); // 每30秒刷新一次数据
        }

        function showMarketingToolModal(toolName, specificAction = null) {
            let modalContent = '';
            
            switch(toolName) {
                case '优惠券管理':
                    modalContent = getCouponManagementModal();
                    break;
                case '会员营销':
                    modalContent = getMemberMarketingModal();
                    break;
                case '限时秒杀':
                    modalContent = getFlashSaleModal();
                    break;
                case '社交分享':
                    modalContent = getSocialSharingModal();
                    break;
                case '积分商城':
                    modalContent = getPointsMallModal();
                    break;
                case '精准推送':
                    modalContent = getPrecisionPushModal();
                    break;
                default:
                    modalContent = getDefaultToolModal(toolName);
            }
            
            const toolModal = document.createElement('div');
            toolModal.className = 'modal';
            toolModal.innerHTML = modalContent;

            // 移除冲突的动态样式定义，使用统一的CSS样式
            document.body.appendChild(toolModal);
            toolModal.style.display = 'block';

            // 根据specificAction执行特定操作
            if (specificAction) {
                setTimeout(() => {
                    executeSpecificAction(toolName, specificAction);
                }, 100);
            }
        }

        // 执行特定操作
        function executeSpecificAction(toolName, action) {
            switch(toolName) {
                case '优惠券管理':
                    if (action === 'create') {
                        const createTab = document.querySelector('[onclick="switchToolTab(this, \'coupon-create\')"]');
                        if (createTab) {
                            switchToolTab(createTab, 'coupon-create');
                        }
                    }
                    break;
                case '会员营销':
                    if (action === 'levels') {
                        const levelsTab = document.querySelector('[onclick="switchToolTab(this, \'member-levels\')"]');
                        if (levelsTab) {
                            switchToolTab(levelsTab, 'member-levels');
                        }
                    }
                    break;
                case '限时秒杀':
                    if (action === 'add-product') {
                        const addTab = document.querySelector('[onclick="switchToolTab(this, \'flash-products\')"]');
                        if (addTab) {
                            switchToolTab(addTab, 'flash-products');
                        }
                    }
                    break;
                case '社交分享':
                    if (action === 'rewards') {
                        const rewardsTab = document.querySelector('[onclick="switchToolTab(this, \'social-rewards\')"]');
                        if (rewardsTab) {
                            switchToolTab(rewardsTab, 'social-rewards');
                        }
                    }
                    break;
                case '积分商城':
                    if (action === 'config') {
                        const configTab = document.querySelector('[onclick="switchToolTab(this, \'points-config\')"]');
                        if (configTab) {
                            switchToolTab(configTab, 'points-config');
                        }
                    }
                    break;
                case '精准推送':
                    if (action === 'create') {
                        const createTab = document.querySelector('[onclick="switchToolTab(this, \'push-create\')"]');
                        if (createTab) {
                            switchToolTab(createTab, 'push-create');
                        }
                    }
                    break;
            }
        }

        function switchTab(button, tabId) {
            // 移除所有活跃状态
            button.parentElement.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
            
            // 设置当前活跃状态
            button.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }

        // 优惠券管理模态框
        function getCouponManagementModal() {
            return `
                <div class="modal-content" style="max-width: 1200px;">
                    <div class="modal-header">
                        <h3>优惠券管理</h3>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body" style="max-height: 700px; overflow-y: auto;">
                        <div class="tool-management">
                            <div class="management-tabs">
                                <button class="tab-btn active" onclick="switchToolTab(this, 'coupon-list')">优惠券列表</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'coupon-create')">创建优惠券</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'coupon-stats')">数据统计</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'coupon-template')">模板库</button>
                            </div>
                            <div class="tab-content">
                                <div id="coupon-list" class="tab-panel active">
                                    <!-- 增强的搜索和筛选区域 -->
                                    <div class="coupon-filters">
                                        <div class="filter-row">
                                            <div class="search-box">
                                                <i class="fas fa-search"></i>
                                                <input type="text" id="couponSearch" placeholder="搜索优惠券名称..." oninput="filterCoupons()">
                                            </div>
                                            <select id="statusFilter" onchange="filterCoupons()">
                                                <option value="">全部状态</option>
                                                <option value="active">生效中</option>
                                                <option value="paused">已暂停</option>
                                                <option value="expired">已过期</option>
                                                <option value="draft">草稿</option>
                                            </select>
                                            <select id="typeFilter" onchange="filterCoupons()">
                                                <option value="">全部类型</option>
                                                <option value="percentage">折扣券</option>
                                                <option value="fixed">满减券</option>
                                                <option value="free_shipping">免邮券</option>
                                            </select>
                                            <button class="btn-outline" onclick="showAdvancedFilter()">高级筛选</button>
                                        </div>
                                        <div class="filter-actions">
                                            <button class="btn-primary" onclick="showCreateCouponModal()">
                                                <i class="fas fa-plus"></i> 新建优惠券
                                            </button>
                                            <button class="btn-secondary" onclick="showBatchOperationModal()">
                                                <i class="fas fa-tasks"></i> 批量操作
                                            </button>
                                            <button class="btn-outline" onclick="exportCoupons()">
                                                <i class="fas fa-download"></i> 导出数据
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 优惠券列表 -->
                                    <div class="coupon-list" id="couponListContainer">
                                        <div class="coupon-item enhanced" data-id="1" data-status="active" data-type="fixed">
                                            <div class="coupon-checkbox">
                                                <input type="checkbox" class="coupon-select" value="1">
                                            </div>
                                            <div class="coupon-preview">
                                                <div class="coupon-visual">
                                                    <div class="coupon-amount">¥20</div>
                                                    <div class="coupon-condition">满100可用</div>
                                                </div>
                                            </div>
                                            <div class="coupon-details">
                                                <div class="coupon-header">
                                                    <h4>新用户专享券</h4>
                                                    <div class="coupon-status active">生效中</div>
                                                </div>
                                                <div class="coupon-info">
                                                    <div class="info-item">
                                                        <span class="label">类型:</span>
                                                        <span class="value">满减券</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <span class="label">有效期:</span>
                                                        <span class="value">2024-01-01 至 2024-12-31</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <span class="label">使用情况:</span>
                                                        <span class="value">已领取 580/1000 (58%)</span>
                                                    </div>
                                                </div>
                                                <div class="coupon-stats">
                                                    <div class="stat-item">
                                                        <span class="stat-value">2.8万</span>
                                                        <span class="stat-label">浏览量</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-value">580</span>
                                                        <span class="stat-label">领取量</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-value">248</span>
                                                        <span class="stat-label">使用量</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-value">42.8%</span>
                                                        <span class="stat-label">转化率</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="coupon-actions">
                                                <button class="action-btn primary" onclick="editCoupon(1)" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn" onclick="copyCoupon(1)" title="复制">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button class="action-btn" onclick="viewCouponStats(1)" title="数据详情">
                                                    <i class="fas fa-chart-bar"></i>
                                                </button>
                                                <button class="action-btn warning" onclick="pauseCoupon(1)" title="暂停">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                                <button class="action-btn danger" onclick="deleteCoupon(1)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <div class="coupon-item enhanced" data-id="2" data-status="active" data-type="percentage">
                                            <div class="coupon-checkbox">
                                                <input type="checkbox" class="coupon-select" value="2">
                                            </div>
                                            <div class="coupon-preview">
                                                <div class="coupon-visual">
                                                    <div class="coupon-amount">8.5折</div>
                                                    <div class="coupon-condition">无门槛</div>
                                                </div>
                                            </div>
                                            <div class="coupon-details">
                                                <div class="coupon-header">
                                                    <h4>春节大促券</h4>
                                                    <div class="coupon-status active">生效中</div>
                                                </div>
                                                <div class="coupon-info">
                                                    <div class="info-item">
                                                        <span class="label">类型:</span>
                                                        <span class="value">折扣券</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <span class="label">有效期:</span>
                                                        <span class="value">2024-02-01 至 2024-03-31</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <span class="label">使用情况:</span>
                                                        <span class="value">已领取 1200/2000 (60%)</span>
                                                    </div>
                                                </div>
                                                <div class="coupon-stats">
                                                    <div class="stat-item">
                                                        <span class="stat-value">5.2万</span>
                                                        <span class="stat-label">浏览量</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-value">1200</span>
                                                        <span class="stat-label">领取量</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-value">680</span>
                                                        <span class="stat-label">使用量</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-value">56.7%</span>
                                                        <span class="stat-label">转化率</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="coupon-actions">
                                                <button class="action-btn primary" onclick="editCoupon(2)" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn" onclick="copyCoupon(2)" title="复制">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button class="action-btn" onclick="viewCouponStats(2)" title="数据详情">
                                                    <i class="fas fa-chart-bar"></i>
                                                </button>
                                                <button class="action-btn warning" onclick="pauseCoupon(2)" title="暂停">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                                <button class="action-btn danger" onclick="deleteCoupon(2)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <div class="coupon-item enhanced" data-id="3" data-status="paused" data-type="free_shipping">
                                            <div class="coupon-checkbox">
                                                <input type="checkbox" class="coupon-select" value="3">
                                            </div>
                                            <div class="coupon-preview">
                                                <div class="coupon-visual">
                                                    <div class="coupon-amount">免邮</div>
                                                    <div class="coupon-condition">满50可用</div>
                                                </div>
                                            </div>
                                            <div class="coupon-details">
                                                <div class="coupon-header">
                                                    <h4>免邮券</h4>
                                                    <div class="coupon-status paused">已暂停</div>
                                                </div>
                                                <div class="coupon-info">
                                                    <div class="info-item">
                                                        <span class="label">类型:</span>
                                                        <span class="value">免邮券</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <span class="label">有效期:</span>
                                                        <span class="value">2024-01-15 至 2024-06-30</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <span class="label">使用情况:</span>
                                                        <span class="value">已领取 320/500 (64%)</span>
                                                    </div>
                                                </div>
                                                <div class="coupon-stats">
                                                    <div class="stat-item">
                                                        <span class="stat-value">1.5万</span>
                                                        <span class="stat-label">浏览量</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-value">320</span>
                                                        <span class="stat-label">领取量</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-value">180</span>
                                                        <span class="stat-label">使用量</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-value">56.3%</span>
                                                        <span class="stat-label">转化率</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="coupon-actions">
                                                <button class="action-btn primary" onclick="editCoupon(3)" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn" onclick="copyCoupon(3)" title="复制">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button class="action-btn" onclick="viewCouponStats(3)" title="数据详情">
                                                    <i class="fas fa-chart-bar"></i>
                                                </button>
                                                <button class="action-btn success" onclick="resumeCoupon(3)" title="恢复">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                <button class="action-btn danger" onclick="deleteCoupon(3)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 分页组件 -->
                                    <div class="pagination">
                                        <button class="page-btn" onclick="changePage(1)">1</button>
                                        <button class="page-btn active" onclick="changePage(2)">2</button>
                                        <button class="page-btn" onclick="changePage(3)">3</button>
                                        <span class="page-info">共 3 页 / 25 条记录</span>
                                    </div>
                                </div>

                                <div id="coupon-create" class="tab-panel">
                                    <div class="create-container">
                                        <div class="form-section">
                                            <h4>基本信息</h4>
                                            <form class="enhanced-coupon-form" id="couponCreateForm">
                                                <div class="form-grid">
                                                    <div class="form-group">
                                                        <label>优惠券名称 *</label>
                                                        <input type="text" id="couponName" name="name" placeholder="如：新用户专享券" required 
                                                               oninput="validateCouponForm(); updatePreview()">
                                                        <span class="form-hint">建议20字以内，清晰描述优惠内容</span>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>优惠类型 *</label>
                                                        <select id="couponType" name="type" required onchange="updateCouponTypeOptions(); updatePreview()">
                                                            <option value="">请选择优惠类型</option>
                                                            <option value="percentage">百分比折扣</option>
                                                            <option value="fixed">固定金额减免</option>
                                                            <option value="free_shipping">免运费</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group" id="discountValueGroup">
                                                        <label id="discountValueLabel">优惠额度 *</label>
                                                        <input type="number" id="discountValue" name="value" placeholder="输入优惠额度" 
                                                               required oninput="validateCouponForm(); updatePreview()" step="0.01" min="0">
                                                        <span class="form-hint" id="discountHint">请输入有效的优惠额度</span>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>使用门槛</label>
                                                        <input type="number" id="minAmount" name="minAmount" placeholder="最低消费金额，0表示无门槛" 
                                                               value="0" oninput="validateCouponForm(); updatePreview()" min="0">
                                                    </div>
                                                    <div class="form-group">
                                                        <label>发放总量 *</label>
                                                        <input type="number" id="totalQuantity" name="quantity" placeholder="如：1000" 
                                                               required oninput="validateCouponForm()" min="1">
                                                    </div>
                                                    <div class="form-group">
                                                        <label>每人限领</label>
                                                        <input type="number" id="limitPerUser" name="limitPerUser" value="1" 
                                                               placeholder="每人最多领取数量" min="1" oninput="validateCouponForm()">
                                                    </div>
                                                    <div class="form-group">
                                                        <label>有效期开始 *</label>
                                                        <input type="datetime-local" id="startDate" name="startDate" required 
                                                               onchange="validateCouponForm(); updatePreview()">
                                                    </div>
                                                    <div class="form-group">
                                                        <label>有效期结束 *</label>
                                                        <input type="datetime-local" id="endDate" name="endDate" required 
                                                               onchange="validateCouponForm(); updatePreview()">
                                                    </div>
                                                </div>
                                                
                                                <div class="advanced-options">
                                                    <h5>高级设置</h5>
                                                    <div class="form-grid">
                                                        <div class="form-group">
                                                            <label>适用商品</label>
                                                            <select id="applicableProducts" name="applicableProducts">
                                                                <option value="all">全部商品</option>
                                                                <option value="category">指定分类</option>
                                                                <option value="specific">指定商品</option>
                                                                <option value="exclude">排除商品</option>
                                                            </select>
                                                        </div>
                                                        <div class="form-group">
                                                            <label>用户群体</label>
                                                            <select id="targetUsers" name="targetUsers">
                                                                <option value="all">所有用户</option>
                                                                <option value="new">新用户</option>
                                                                <option value="vip">VIP用户</option>
                                                                <option value="active">活跃用户</option>
                                                            </select>
                                                        </div>
                                                        <div class="form-group">
                                                            <label>发放方式</label>
                                                            <select id="distributionMethod" name="distributionMethod">
                                                                <option value="manual">手动领取</option>
                                                                <option value="auto">自动发放</option>
                                                                <option value="conditional">条件触发</option>
                                                            </select>
                                                        </div>
                                                        <div class="form-group checkbox-group">
                                                            <label>
                                                                <input type="checkbox" id="stackable" name="stackable">
                                                                允许与其他优惠叠加
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="form-actions">
                                                    <button type="button" class="btn-outline" onclick="saveCouponDraft()">保存草稿</button>
                                                    <button type="button" class="btn-secondary" onclick="previewCoupon()">预览效果</button>
                                                    <button type="submit" class="btn-primary" id="createCouponBtn" disabled>创建并发布</button>
                                                </div>
                                            </form>
                                        </div>

                                        <!-- 实时预览区域 -->
                                        <div class="preview-section">
                                            <h4>优惠券预览</h4>
                                            <div class="coupon-preview-card" id="couponPreviewCard">
                                                <div class="preview-coupon">
                                                    <div class="preview-amount">-</div>
                                                    <div class="preview-condition">-</div>
                                                    <div class="preview-name">优惠券名称</div>
                                                    <div class="preview-validity">有效期：-</div>
                                                </div>
                                            </div>
                                            <div class="preview-stats">
                                                <h5>预估效果</h5>
                                                <div class="estimated-stats">
                                                    <div class="stat-item">
                                                        <span class="label">预计领取率</span>
                                                        <span class="value" id="estimatedClaimRate">-</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="label">预计使用率</span>
                                                        <span class="value" id="estimatedUsageRate">-</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="label">预计成本</span>
                                                        <span class="value" id="estimatedCost">-</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="coupon-stats" class="tab-panel">
                                    <div class="stats-header">
                                        <div class="stats-controls">
                                            <select id="statsTimeRange" onchange="updateCouponStats()">
                                                <option value="7">最近7天</option>
                                                <option value="30" selected>最近30天</option>
                                                <option value="90">最近90天</option>
                                                <option value="365">最近一年</option>
                                            </select>
                                            <button class="btn-outline" onclick="exportStatsData()">导出报表</button>
                                        </div>
                                    </div>
                                    
                                    <div class="stats-overview">
                                        <div class="stats-grid">
                                            <div class="stat-card highlight">
                                                <div class="stat-icon">
                                                    <i class="fas fa-ticket-alt"></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-value">28</div>
                                                    <div class="stat-label">活跃券种</div>
                                                    <div class="stat-trend up">↑ 12%</div>
                                                </div>
                                            </div>
                                            <div class="stat-card highlight">
                                                <div class="stat-icon">
                                                    <i class="fas fa-share"></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-value">15,240</div>
                                                    <div class="stat-label">总发放量</div>
                                                    <div class="stat-trend up">↑ 25%</div>
                                                </div>
                                            </div>
                                            <div class="stat-card highlight">
                                                <div class="stat-icon">
                                                    <i class="fas fa-check-circle"></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-value">8,650</div>
                                                    <div class="stat-label">已使用</div>
                                                    <div class="stat-trend up">↑ 18%</div>
                                                </div>
                                            </div>
                                            <div class="stat-card highlight">
                                                <div class="stat-icon">
                                                    <i class="fas fa-percentage"></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-value">56.8%</div>
                                                    <div class="stat-label">使用率</div>
                                                    <div class="stat-trend down">↓ 3%</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="stats-charts">
                                        <div class="chart-section">
                                            <h5>优惠券使用趋势</h5>
                                            <canvas id="couponUsageChart" width="400" height="200"></canvas>
                                        </div>
                                        <div class="chart-section">
                                            <h5>优惠券类型分布</h5>
                                            <canvas id="couponTypeChart" width="400" height="200"></canvas>
                                        </div>
                                    </div>

                                    <div class="top-coupons">
                                        <h5>热门优惠券 TOP 5</h5>
                                        <div class="ranking-list">
                                            <div class="ranking-item">
                                                <div class="rank">1</div>
                                                <div class="coupon-name">新用户专享券</div>
                                                <div class="usage-rate">58% 使用率</div>
                                                <div class="usage-count">580 次使用</div>
                                            </div>
                                            <div class="ranking-item">
                                                <div class="rank">2</div>
                                                <div class="coupon-name">春节大促券</div>
                                                <div class="usage-rate">60% 使用率</div>
                                                <div class="usage-count">1200 次使用</div>
                                            </div>
                                            <div class="ranking-item">
                                                <div class="rank">3</div>
                                                <div class="coupon-name">免邮券</div>
                                                <div class="usage-rate">64% 使用率</div>
                                                <div class="usage-count">320 次使用</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="coupon-template" class="tab-panel">
                                    <div class="template-grid">
                                        <div class="template-card" onclick="applyCouponTemplate('newuser')">
                                            <div class="template-icon">
                                                <i class="fas fa-user-plus"></i>
                                            </div>
                                            <h4>新用户专享</h4>
                                            <p>适合新用户首次购买的满减券模板</p>
                                            <div class="template-badge">热门</div>
                                        </div>
                                        <div class="template-card" onclick="applyCouponTemplate('discount')">
                                            <div class="template-icon">
                                                <i class="fas fa-percent"></i>
                                            </div>
                                            <h4>折扣优惠</h4>
                                            <p>通用折扣券模板，支持全场或分类</p>
                                        </div>
                                        <div class="template-card" onclick="applyCouponTemplate('shipping')">
                                            <div class="template-icon">
                                                <i class="fas fa-shipping-fast"></i>
                                            </div>
                                            <h4>免运费券</h4>
                                            <p>提升转化率的免运费券模板</p>
                                        </div>
                                        <div class="template-card" onclick="applyCouponTemplate('festival')">
                                            <div class="template-icon">
                                                <i class="fas fa-gift"></i>
                                            </div>
                                            <h4>节日促销</h4>
                                            <p>节假日专用的大额优惠券模板</p>
                                        </div>
                                        <div class="template-card" onclick="applyCouponTemplate('vip')">
                                            <div class="template-icon">
                                                <i class="fas fa-crown"></i>
                                            </div>
                                            <h4>VIP专享</h4>
                                            <p>会员等级专属优惠券模板</p>
                                        </div>
                                        <div class="template-card" onclick="applyCouponTemplate('flash')">
                                            <div class="template-icon">
                                                <i class="fas fa-bolt"></i>
                                            </div>
                                            <h4>限时抢购</h4>
                                            <p>营造紧迫感的限时优惠券模板</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                    </div>
                </div>
            `;
        }

        // 会员营销管理模态框
        function getMemberMarketingModal() {
            return `
                <div class="modal-content" style="max-width: 1000px;">
                    <div class="modal-header">
                        <h3>会员营销管理</h3>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body" style="max-height: 600px; overflow-y: auto;">
                        <div class="tool-management">
                            <div class="management-tabs">
                                <button class="tab-btn active" onclick="switchToolTab(this, 'member-levels')">会员等级</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'member-benefits')">会员权益</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'member-activities')">会员活动</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'member-analytics')">数据分析</button>
                            </div>
                            <div class="tab-content">
                                <div id="member-levels" class="tab-panel active">
                                    <div class="level-management-header">
                                        <div class="search-box">
                                            <i class="fas fa-search"></i>
                                            <input type="text" id="levelSearch" placeholder="搜索会员等级..." oninput="filterMemberLevels()">
                                        </div>
                                        <div class="level-actions-top">
                                            <button class="btn-primary" onclick="addMemberLevel()">
                                                <i class="fas fa-plus"></i> 新增等级
                                            </button>
                                            <button class="btn-secondary" onclick="exportMemberLevels()">
                                                <i class="fas fa-download"></i> 导出数据
                                            </button>
                                            <button class="btn-outline" onclick="showLevelSettings()">
                                                <i class="fas fa-cog"></i> 等级设置
                                            </button>
                                        </div>
                                    </div>
                                    <div class="level-list" id="memberLevelsList">
                                        <div class="level-item" data-level="1">
                                            <div class="level-badge">
                                                <i class="fas fa-user" style="color: #95a5a6;"></i>
                                                <span class="level-number">Lv.1</span>
                                            </div>
                                            <div class="level-info">
                                                <h4>普通会员</h4>
                                                <p>注册即可成为 | 当前人数：12,450人 | 成长值要求：0</p>
                                                <div class="level-progress">
                                                    <div class="progress-info">
                                                        <span>升级条件：累计消费满1000元或成长值达500</span>
                                                        <span class="completion-rate">完成度：75%</span>
                                                    </div>
                                                    <div class="progress-bar">
                                                        <div class="progress-fill" style="width: 75%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="level-benefits">
                                                <span class="benefit-tag">积分返现 1%</span>
                                                <span class="benefit-tag">生日优惠券</span>
                                                <span class="benefit-tag">新人专享价</span>
                                            </div>
                                            <div class="level-statistics">
                                                <div class="stat-mini">
                                                    <span class="stat-value">¥320</span>
                                                    <span class="stat-label">平均消费</span>
                                                </div>
                                                <div class="stat-mini">
                                                    <span class="stat-value">62%</span>
                                                    <span class="stat-label">活跃度</span>
                                                </div>
                                            </div>
                                            <div class="level-actions">
                                                <button class="action-btn-sm" onclick="editMemberLevel(1)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="action-btn-sm" onclick="viewLevelAnalytics(1)">
                                                    <i class="fas fa-chart-bar"></i> 数据
                                                </button>
                                                <button class="action-btn-sm" onclick="manageLevelUsers(1)">
                                                    <i class="fas fa-users"></i> 用户
                                                </button>
                                            </div>
                                        </div>
                                        <div class="level-item" data-level="2">
                                            <div class="level-badge">
                                                <i class="fas fa-medal" style="color: #c0c0c0;"></i>
                                                <span class="level-number">Lv.2</span>
                                            </div>
                                            <div class="level-info">
                                                <h4>银牌会员</h4>
                                                <p>累计消费满1000元 | 当前人数：3,280人 | 成长值要求：500</p>
                                                <div class="level-progress">
                                                    <div class="progress-info">
                                                        <span>升级条件：累计消费满5000元或成长值达2000</span>
                                                        <span class="completion-rate">完成度：45%</span>
                                                    </div>
                                                    <div class="progress-bar">
                                                        <div class="progress-fill" style="width: 45%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="level-benefits">
                                                <span class="benefit-tag">9.5折优惠</span>
                                                <span class="benefit-tag">免运费券</span>
                                                <span class="benefit-tag">专属客服</span>
                                                <span class="benefit-tag">积分返现 1.5%</span>
                                            </div>
                                            <div class="level-statistics">
                                                <div class="stat-mini">
                                                    <span class="stat-value">¥890</span>
                                                    <span class="stat-label">平均消费</span>
                                                </div>
                                                <div class="stat-mini">
                                                    <span class="stat-value">78%</span>
                                                    <span class="stat-label">活跃度</span>
                                                </div>
                                            </div>
                                            <div class="level-actions">
                                                <button class="action-btn-sm" onclick="editMemberLevel(2)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="action-btn-sm" onclick="viewLevelAnalytics(2)">
                                                    <i class="fas fa-chart-bar"></i> 数据
                                                </button>
                                                <button class="action-btn-sm" onclick="manageLevelUsers(2)">
                                                    <i class="fas fa-users"></i> 用户
                                                </button>
                                            </div>
                                        </div>
                                        <div class="level-item" data-level="3">
                                            <div class="level-badge">
                                                <i class="fas fa-crown" style="color: #f39c12;"></i>
                                                <span class="level-number">Lv.3</span>
                                            </div>
                                            <div class="level-info">
                                                <h4>金牌会员</h4>
                                                <p>累计消费满5000元 | 当前人数：860人 | 成长值要求：2000</p>
                                                <div class="level-progress">
                                                    <div class="progress-info">
                                                        <span>升级条件：累计消费满20000元或成长值达8000</span>
                                                        <span class="completion-rate">完成度：28%</span>
                                                    </div>
                                                    <div class="progress-bar">
                                                        <div class="progress-fill" style="width: 28%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="level-benefits">
                                                <span class="benefit-tag">9折优惠</span>
                                                <span class="benefit-tag">免运费</span>
                                                <span class="benefit-tag">专属客服</span>
                                                <span class="benefit-tag">优先发货</span>
                                                <span class="benefit-tag">积分返现 2%</span>
                                            </div>
                                            <div class="level-statistics">
                                                <div class="stat-mini">
                                                    <span class="stat-value">¥2,350</span>
                                                    <span class="stat-label">平均消费</span>
                                                </div>
                                                <div class="stat-mini">
                                                    <span class="stat-value">89%</span>
                                                    <span class="stat-label">活跃度</span>
                                                </div>
                                            </div>
                                            <div class="level-actions">
                                                <button class="action-btn-sm" onclick="editMemberLevel(3)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="action-btn-sm" onclick="viewLevelAnalytics(3)">
                                                    <i class="fas fa-chart-bar"></i> 数据
                                                </button>
                                                <button class="action-btn-sm" onclick="manageLevelUsers(3)">
                                                    <i class="fas fa-users"></i> 用户
                                                </button>
                                            </div>
                                        </div>
                                        <div class="level-item" data-level="4">
                                            <div class="level-badge">
                                                <i class="fas fa-gem" style="color: #e74c3c;"></i>
                                                <span class="level-number">Lv.4</span>
                                            </div>
                                            <div class="level-info">
                                                <h4>钻石会员</h4>
                                                <p>累计消费满20000元 | 当前人数：125人 | 成长值要求：8000</p>
                                                <div class="level-progress">
                                                    <div class="progress-info">
                                                        <span>最高等级会员，享受全站最高权益</span>
                                                        <span class="completion-rate">已达最高等级</span>
                                                    </div>
                                                    <div class="progress-bar">
                                                        <div class="progress-fill max-level" style="width: 100%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="level-benefits">
                                                <span class="benefit-tag">8.5折优惠</span>
                                                <span class="benefit-tag">全场免运费</span>
                                                <span class="benefit-tag">VIP专线客服</span>
                                                <span class="benefit-tag">优先发货</span>
                                                <span class="benefit-tag">积分返现 3%</span>
                                                <span class="benefit-tag">专属礼品</span>
                                            </div>
                                            <div class="level-statistics">
                                                <div class="stat-mini">
                                                    <span class="stat-value">¥6,820</span>
                                                    <span class="stat-label">平均消费</span>
                                                </div>
                                                <div class="stat-mini">
                                                    <span class="stat-value">95%</span>
                                                    <span class="stat-label">活跃度</span>
                                                </div>
                                            </div>
                                            <div class="level-actions">
                                                <button class="action-btn-sm" onclick="editMemberLevel(4)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="action-btn-sm" onclick="viewLevelAnalytics(4)">
                                                    <i class="fas fa-chart-bar"></i> 数据
                                                </button>
                                                <button class="action-btn-sm" onclick="manageLevelUsers(4)">
                                                    <i class="fas fa-users"></i> 用户
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="member-benefits" class="tab-panel">
                                    <div class="benefits-management-header">
                                        <div class="benefits-summary">
                                            <div class="summary-stat">
                                                <span class="stat-value">8</span>
                                                <span class="stat-label">权益项目</span>
                                            </div>
                                            <div class="summary-stat">
                                                <span class="stat-value">6</span>
                                                <span class="stat-label">已启用</span>
                                            </div>
                                            <div class="summary-stat">
                                                <span class="stat-value">85%</span>
                                                <span class="stat-label">使用率</span>
                                            </div>
                                        </div>
                                        <div class="benefits-actions">
                                            <button class="btn-primary" onclick="addMemberBenefit()">
                                                <i class="fas fa-plus"></i> 新增权益
                                            </button>
                                            <button class="btn-secondary" onclick="showBenefitTemplate()">
                                                <i class="fas fa-list"></i> 权益模板
                                            </button>
                                        </div>
                                    </div>
                                    <div class="benefits-grid">
                                        <div class="benefit-card" data-type="points">
                                            <div class="benefit-icon">
                                                <i class="fas fa-coins" style="color: #f39c12;"></i>
                                            </div>
                                            <div class="benefit-content">
                                                <h4>积分返现</h4>
                                                <p>每消费1元返1积分，100积分=1元</p>
                                                <div class="benefit-config">
                                                    <span class="config-item">返现比例：1%</span>
                                                    <span class="config-item">兑换比例：100:1</span>
                                                </div>
                                                <div class="benefit-usage">
                                                    <div class="usage-bar">
                                                        <div class="usage-fill" style="width: 78%"></div>
                                                    </div>
                                                    <span class="usage-text">78% 会员使用</span>
                                                </div>
                                            </div>
                                            <div class="benefit-status active">已启用</div>
                                            <div class="benefit-actions">
                                                <button class="action-btn-sm" onclick="editBenefit('points')">
                                                    <i class="fas fa-cog"></i> 设置
                                                </button>
                                                <button class="action-btn-sm" onclick="viewBenefitData('points')">
                                                    <i class="fas fa-chart-line"></i> 数据
                                                </button>
                                            </div>
                                        </div>
                                        <div class="benefit-card" data-type="birthday">
                                            <div class="benefit-icon">
                                                <i class="fas fa-birthday-cake" style="color: #e74c3c;"></i>
                                            </div>
                                            <div class="benefit-content">
                                                <h4>生日特权</h4>
                                                <p>生日当月享受专属折扣和礼品</p>
                                                <div class="benefit-config">
                                                    <span class="config-item">折扣额度：8.5折</span>
                                                    <span class="config-item">生日礼品：随机抽取</span>
                                                </div>
                                                <div class="benefit-usage">
                                                    <div class="usage-bar">
                                                        <div class="usage-fill" style="width: 92%"></div>
                                                    </div>
                                                    <span class="usage-text">92% 会员使用</span>
                                                </div>
                                            </div>
                                            <div class="benefit-status active">已启用</div>
                                            <div class="benefit-actions">
                                                <button class="action-btn-sm" onclick="editBenefit('birthday')">
                                                    <i class="fas fa-cog"></i> 设置
                                                </button>
                                                <button class="action-btn-sm" onclick="viewBenefitData('birthday')">
                                                    <i class="fas fa-chart-line"></i> 数据
                                                </button>
                                            </div>
                                        </div>
                                        <div class="benefit-card" data-type="discount">
                                            <div class="benefit-icon">
                                                <i class="fas fa-percentage" style="color: #27ae60;"></i>
                                            </div>
                                            <div class="benefit-content">
                                                <h4>专享折扣</h4>
                                                <p>会员专享商品折扣</p>
                                                <div class="benefit-config">
                                                    <span class="config-item">折扣范围：5%-20%</span>
                                                    <span class="config-item">适用商品：精选商品</span>
                                                </div>
                                                <div class="benefit-usage">
                                                    <div class="usage-bar">
                                                        <div class="usage-fill" style="width: 65%"></div>
                                                    </div>
                                                    <span class="usage-text">65% 会员使用</span>
                                                </div>
                                            </div>
                                            <div class="benefit-status active">已启用</div>
                                            <div class="benefit-actions">
                                                <button class="action-btn-sm" onclick="editBenefit('discount')">
                                                    <i class="fas fa-cog"></i> 设置
                                                </button>
                                                <button class="action-btn-sm" onclick="viewBenefitData('discount')">
                                                    <i class="fas fa-chart-line"></i> 数据
                                                </button>
                                            </div>
                                        </div>
                                        <div class="benefit-card" data-type="shipping">
                                            <div class="benefit-icon">
                                                <i class="fas fa-shipping-fast" style="color: #3498db;"></i>
                                            </div>
                                            <div class="benefit-content">
                                                <h4>免运费特权</h4>
                                                <p>满足条件免运费</p>
                                                <div class="benefit-config">
                                                    <span class="config-item">免邮门槛：满99元</span>
                                                    <span class="config-item">适用范围：全国包邮</span>
                                                </div>
                                                <div class="benefit-usage">
                                                    <div class="usage-bar">
                                                        <div class="usage-fill" style="width: 0%"></div>
                                                    </div>
                                                    <span class="usage-text">0% 会员使用</span>
                                                </div>
                                            </div>
                                            <div class="benefit-status inactive">未启用</div>
                                            <div class="benefit-actions">
                                                <button class="action-btn-sm" onclick="editBenefit('shipping')">
                                                    <i class="fas fa-cog"></i> 设置
                                                </button>
                                                <button class="action-btn-sm" onclick="enableBenefit('shipping')">
                                                    <i class="fas fa-power-off"></i> 启用
                                                </button>
                                            </div>
                                        </div>
                                        <div class="benefit-card" data-type="priority">
                                            <div class="benefit-icon">
                                                <i class="fas fa-rocket" style="color: #9b59b6;"></i>
                                            </div>
                                            <div class="benefit-content">
                                                <h4>优先发货</h4>
                                                <p>享受优先处理和发货服务</p>
                                                <div class="benefit-config">
                                                    <span class="config-item">优先级：VIP优先</span>
                                                    <span class="config-item">处理时间：缩短50%</span>
                                                </div>
                                                <div class="benefit-usage">
                                                    <div class="usage-bar">
                                                        <div class="usage-fill" style="width: 43%"></div>
                                                    </div>
                                                    <span class="usage-text">43% 会员使用</span>
                                                </div>
                                            </div>
                                            <div class="benefit-status active">已启用</div>
                                            <div class="benefit-actions">
                                                <button class="action-btn-sm" onclick="editBenefit('priority')">
                                                    <i class="fas fa-cog"></i> 设置
                                                </button>
                                                <button class="action-btn-sm" onclick="viewBenefitData('priority')">
                                                    <i class="fas fa-chart-line"></i> 数据
                                                </button>
                                            </div>
                                        </div>
                                        <div class="benefit-card" data-type="customer-service">
                                            <div class="benefit-icon">
                                                <i class="fas fa-headset" style="color: #34495e;"></i>
                                            </div>
                                            <div class="benefit-content">
                                                <h4>专属客服</h4>
                                                <p>享受VIP客服绿色通道服务</p>
                                                <div class="benefit-config">
                                                    <span class="config-item">服务时间：7x24小时</span>
                                                    <span class="config-item">响应时间：1分钟内</span>
                                                </div>
                                                <div class="benefit-usage">
                                                    <div class="usage-bar">
                                                        <div class="usage-fill" style="width: 56%"></div>
                                                    </div>
                                                    <span class="usage-text">56% 会员使用</span>
                                                </div>
                                            </div>
                                            <div class="benefit-status active">已启用</div>
                                            <div class="benefit-actions">
                                                <button class="action-btn-sm" onclick="editBenefit('customer-service')">
                                                    <i class="fas fa-cog"></i> 设置
                                                </button>
                                                <button class="action-btn-sm" onclick="viewBenefitData('customer-service')">
                                                    <i class="fas fa-chart-line"></i> 数据
                                                </button>
                                            </div>
                                        </div>
                                        <div class="benefit-card" data-type="exclusive-products">
                                            <div class="benefit-icon">
                                                <i class="fas fa-star" style="color: #f1c40f;"></i>
                                            </div>
                                            <div class="benefit-content">
                                                <h4>专属商品</h4>
                                                <p>专享限量版和定制商品购买权</p>
                                                <div class="benefit-config">
                                                    <span class="config-item">商品类型：限量版</span>
                                                    <span class="config-item">提前时间：3天</span>
                                                </div>
                                                <div class="benefit-usage">
                                                    <div class="usage-bar">
                                                        <div class="usage-fill" style="width: 38%"></div>
                                                    </div>
                                                    <span class="usage-text">38% 会员使用</span>
                                                </div>
                                            </div>
                                            <div class="benefit-status active">已启用</div>
                                            <div class="benefit-actions">
                                                <button class="action-btn-sm" onclick="editBenefit('exclusive-products')">
                                                    <i class="fas fa-cog"></i> 设置
                                                </button>
                                                <button class="action-btn-sm" onclick="viewBenefitData('exclusive-products')">
                                                    <i class="fas fa-chart-line"></i> 数据
                                                </button>
                                            </div>
                                        </div>
                                        <div class="benefit-card" data-type="cashback">
                                            <div class="benefit-icon">
                                                <i class="fas fa-money-bill-wave" style="color: #16a085;"></i>
                                            </div>
                                            <div class="benefit-content">
                                                <h4>消费返现</h4>
                                                <p>根据会员等级享受不同比例返现</p>
                                                <div class="benefit-config">
                                                    <span class="config-item">返现比例：1%-3%</span>
                                                    <span class="config-item">结算周期：每月15号</span>
                                                </div>
                                                <div class="benefit-usage">
                                                    <div class="usage-bar">
                                                        <div class="usage-fill" style="width: 0%"></div>
                                                    </div>
                                                    <span class="usage-text">0% 会员使用</span>
                                                </div>
                                            </div>
                                            <div class="benefit-status inactive">未启用</div>
                                            <div class="benefit-actions">
                                                <button class="action-btn-sm" onclick="editBenefit('cashback')">
                                                    <i class="fas fa-cog"></i> 设置
                                                </button>
                                                <button class="action-btn-sm" onclick="enableBenefit('cashback')">
                                                    <i class="fas fa-power-off"></i> 启用
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="member-activities" class="tab-panel">
                                    <div class="activities-management-header">
                                        <div class="activities-filters">
                                            <div class="search-box">
                                                <i class="fas fa-search"></i>
                                                <input type="text" id="activitySearch" placeholder="搜索活动名称..." oninput="filterActivities()">
                                            </div>
                                            <select id="activityStatusFilter" onchange="filterActivities()">
                                                <option value="">全部状态</option>
                                                <option value="active">进行中</option>
                                                <option value="upcoming">即将开始</option>
                                                <option value="paused">已暂停</option>
                                                <option value="completed">已结束</option>
                                            </select>
                                            <select id="activityTypeFilter" onchange="filterActivities()">
                                                <option value="">全部类型</option>
                                                <option value="promotion">促销活动</option>
                                                <option value="reward">奖励活动</option>
                                                <option value="engagement">互动活动</option>
                                                <option value="loyalty">忠诚度活动</option>
                                            </select>
                                        </div>
                                        <div class="activities-actions">
                                            <button class="btn-primary" onclick="createMemberActivity()">
                                                <i class="fas fa-plus"></i> 创建活动
                                            </button>
                                            <button class="btn-secondary" onclick="showActivityTemplate()">
                                                <i class="fas fa-list"></i> 活动模板
                                            </button>
                                            <button class="btn-outline" onclick="showActivityCalendar()">
                                                <i class="fas fa-calendar"></i> 活动日历
                                            </button>
                                        </div>
                                    </div>
                                    <div class="activity-list" id="memberActivitiesList">
                                        <div class="activity-item" data-type="promotion" data-status="active">
                                            <div class="activity-banner">
                                                <img src="/api/placeholder/120/80" alt="活动横幅" class="activity-image">
                                                <div class="activity-type">促销活动</div>
                                            </div>
                                            <div class="activity-info">
                                                <h4>春季会员日大促</h4>
                                                <p>每月15日会员专享活动，全场9折起，限时三天</p>
                                                <div class="activity-schedule">
                                                    <div class="schedule-item">
                                                        <i class="fas fa-calendar-start"></i>
                                                        <span>开始时间：2024-03-15 00:00</span>
                                                    </div>
                                                    <div class="schedule-item">
                                                        <i class="fas fa-calendar-end"></i>
                                                        <span>结束时间：2024-03-17 23:59</span>
                                                    </div>
                                                    <div class="schedule-item">
                                                        <i class="fas fa-clock"></i>
                                                        <span>剩余时间：2天8小时</span>
                                                    </div>
                                                </div>
                                                <div class="activity-stats">
                                                    <div class="stat-mini">
                                                        <span class="stat-value">2,840</span>
                                                        <span class="stat-label">参与人数</span>
                                                    </div>
                                                    <div class="stat-mini">
                                                        <span class="stat-value">¥156,430</span>
                                                        <span class="stat-label">销售额</span>
                                                    </div>
                                                    <div class="stat-mini">
                                                        <span class="stat-value">18.5%</span>
                                                        <span class="stat-label">转化率</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="activity-status active">进行中</div>
                                            <div class="activity-actions">
                                                <button class="action-btn-sm" onclick="editActivity(1)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="action-btn-sm" onclick="viewActivityData(1)">
                                                    <i class="fas fa-chart-bar"></i> 数据
                                                </button>
                                                <button class="action-btn-sm" onclick="pauseActivity(1)">
                                                    <i class="fas fa-pause"></i> 暂停
                                                </button>
                                                <button class="action-btn-sm" onclick="copyActivity(1)">
                                                    <i class="fas fa-copy"></i> 复制
                                                </button>
                                            </div>
                                        </div>
                                        <div class="activity-item" data-type="reward" data-status="upcoming">
                                            <div class="activity-banner">
                                                <img src="/api/placeholder/120/80" alt="活动横幅" class="activity-image">
                                                <div class="activity-type">奖励活动</div>
                                            </div>
                                            <div class="activity-info">
                                                <h4>会员签到奖励升级</h4>
                                                <p>连续签到送豪礼，累计签到获得专属徽章和折扣券</p>
                                                <div class="activity-schedule">
                                                    <div class="schedule-item">
                                                        <i class="fas fa-calendar-start"></i>
                                                        <span>开始时间：2024-04-01 00:00</span>
                                                    </div>
                                                    <div class="schedule-item">
                                                        <i class="fas fa-calendar-end"></i>
                                                        <span>结束时间：2024-04-30 23:59</span>
                                                    </div>
                                                    <div class="schedule-item">
                                                        <i class="fas fa-hourglass-start"></i>
                                                        <span>开始倒计时：15天6小时</span>
                                                    </div>
                                                </div>
                                                <div class="activity-rewards">
                                                    <div class="reward-item">
                                                        <span class="reward-name">7天签到</span>
                                                        <span class="reward-desc">5元优惠券</span>
                                                    </div>
                                                    <div class="reward-item">
                                                        <span class="reward-name">15天签到</span>
                                                        <span class="reward-desc">专属徽章</span>
                                                    </div>
                                                    <div class="reward-item">
                                                        <span class="reward-name">30天签到</span>
                                                        <span class="reward-desc">8.5折券</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="activity-status upcoming">即将开始</div>
                                            <div class="activity-actions">
                                                <button class="action-btn-sm" onclick="editActivity(2)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="action-btn-sm" onclick="previewActivity(2)">
                                                    <i class="fas fa-eye"></i> 预览
                                                </button>
                                                <button class="action-btn-sm" onclick="startActivity(2)">
                                                    <i class="fas fa-play"></i> 启动
                                                </button>
                                                <button class="action-btn-sm" onclick="copyActivity(2)">
                                                    <i class="fas fa-copy"></i> 复制
                                                </button>
                                            </div>
                                        </div>
                                        <div class="activity-item" data-type="engagement" data-status="completed">
                                            <div class="activity-banner">
                                                <img src="/api/placeholder/120/80" alt="活动横幅" class="activity-image">
                                                <div class="activity-type">互动活动</div>
                                            </div>
                                            <div class="activity-info">
                                                <h4>会员评价有礼</h4>
                                                <p>分享购买体验，撰写商品评价获得积分奖励</p>
                                                <div class="activity-schedule">
                                                    <div class="schedule-item">
                                                        <i class="fas fa-calendar-start"></i>
                                                        <span>开始时间：2024-02-01 00:00</span>
                                                    </div>
                                                    <div class="schedule-item">
                                                        <i class="fas fa-calendar-end"></i>
                                                        <span>结束时间：2024-02-29 23:59</span>
                                                    </div>
                                                    <div class="schedule-item">
                                                        <i class="fas fa-check-circle"></i>
                                                        <span>活动已结束</span>
                                                    </div>
                                                </div>
                                                <div class="activity-results">
                                                    <div class="result-item">
                                                        <span class="result-value">4,560</span>
                                                        <span class="result-label">评价数量</span>
                                                    </div>
                                                    <div class="result-item">
                                                        <span class="result-value">1,890</span>
                                                        <span class="result-label">参与用户</span>
                                                    </div>
                                                    <div class="result-item">
                                                        <span class="result-value">4.8</span>
                                                        <span class="result-label">平均评分</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="activity-status completed">已结束</div>
                                            <div class="activity-actions">
                                                <button class="action-btn-sm" onclick="viewActivityReport(3)">
                                                    <i class="fas fa-file-alt"></i> 报告
                                                </button>
                                                <button class="action-btn-sm" onclick="viewActivityData(3)">
                                                    <i class="fas fa-chart-bar"></i> 数据
                                                </button>
                                                <button class="action-btn-sm" onclick="copyActivity(3)">
                                                    <i class="fas fa-copy"></i> 复制
                                                </button>
                                                <button class="action-btn-sm" onclick="archiveActivity(3)">
                                                    <i class="fas fa-archive"></i> 归档
                                                </button>
                                            </div>
                                        </div>
                                        <div class="activity-item" data-type="loyalty" data-status="paused">
                                            <div class="activity-banner">
                                                <img src="/api/placeholder/120/80" alt="活动横幅" class="activity-image">
                                                <div class="activity-type">忠诚度活动</div>
                                            </div>
                                            <div class="activity-info">
                                                <h4>老客户专享回馈</h4>
                                                <p>注册满1年的会员享受专属折扣和积分翻倍奖励</p>
                                                <div class="activity-schedule">
                                                    <div class="schedule-item">
                                                        <i class="fas fa-calendar-start"></i>
                                                        <span>开始时间：2024-03-01 00:00</span>
                                                    </div>
                                                    <div class="schedule-item">
                                                        <i class="fas fa-calendar-end"></i>
                                                        <span>结束时间：2024-05-31 23:59</span>
                                                    </div>
                                                    <div class="schedule-item">
                                                        <i class="fas fa-pause-circle"></i>
                                                        <span>活动已暂停</span>
                                                    </div>
                                                </div>
                                                <div class="activity-targets">
                                                    <div class="target-item">
                                                        <span class="target-name">目标用户</span>
                                                        <span class="target-desc">注册满1年会员</span>
                                                    </div>
                                                    <div class="target-item">
                                                        <span class="target-name">预计参与</span>
                                                        <span class="target-desc">8,500人</span>
                                                    </div>
                                                    <div class="target-item">
                                                        <span class="target-name">已参与</span>
                                                        <span class="target-desc">1,250人</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="activity-status paused">已暂停</div>
                                            <div class="activity-actions">
                                                <button class="action-btn-sm" onclick="editActivity(4)">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="action-btn-sm" onclick="resumeActivity(4)">
                                                    <i class="fas fa-play"></i> 恢复
                                                </button>
                                                <button class="action-btn-sm" onclick="viewActivityData(4)">
                                                    <i class="fas fa-chart-bar"></i> 数据
                                                </button>
                                                <button class="action-btn-sm danger" onclick="stopActivity(4)">
                                                    <i class="fas fa-stop"></i> 结束
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="member-analytics" class="tab-panel">
                                    <div class="stats-grid">
                                        <div class="stat-card">
                                            <div class="stat-value">16,590</div>
                                            <div class="stat-label">总会员数</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">68.5%</div>
                                            <div class="stat-label">会员活跃度</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">¥2,450</div>
                                            <div class="stat-label">平均消费</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">85.2%</div>
                                            <div class="stat-label">会员复购率</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                    </div>
                </div>
            `;
        }

        // 限时秒杀管理模态框
        function getFlashSaleModal() {
            return `
                <div class="modal-content" style="max-width: 1000px;">
                    <div class="modal-header">
                        <h3>限时秒杀管理</h3>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body" style="max-height: 600px; overflow-y: auto;">
                        <div class="tool-management">
                            <div class="management-tabs">
                                <button class="tab-btn active" onclick="switchToolTab(this, 'flashsale-list')">秒杀活动</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'flashsale-products')">秒杀商品</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'flashsale-schedule')">时段设置</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'flashsale-data')">数据分析</button>
                            </div>
                            <div class="tab-content">
                                <div id="flashsale-list" class="tab-panel active">
                                    <div class="list-actions">
                                        <button class="btn-primary" onclick="createFlashSale()">创建秒杀</button>
                                        <select onchange="filterFlashSales(this.value)">
                                            <option value="">全部状态</option>
                                            <option value="upcoming">即将开始</option>
                                            <option value="active">进行中</option>
                                            <option value="ended">已结束</option>
                                        </select>
                                    </div>
                                    <div class="flashsale-list">
                                        <div class="flashsale-item">
                                            <div class="flashsale-info">
                                                <h4>春季大促秒杀</h4>
                                                <p>今日14:00-16:00 | 15件商品参与</p>
                                                <div class="flashsale-countdown">剩余时间：02:45:30</div>
                                            </div>
                                            <div class="flashsale-status active">进行中</div>
                                            <div class="flashsale-actions">
                                                <button class="action-btn-sm" onclick="editFlashSale(1)">编辑</button>
                                                <button class="action-btn-sm" onclick="viewFlashSaleData(1)">数据</button>
                                                <button class="action-btn-sm danger" onclick="stopFlashSale(1)">结束</button>
                                            </div>
                                        </div>
                                        <div class="flashsale-item">
                                            <div class="flashsale-info">
                                                <h4>周末特惠秒杀</h4>
                                                <p>明日10:00-12:00 | 22件商品参与</p>
                                                <div class="flashsale-countdown">开始倒计时：18:32:15</div>
                                            </div>
                                            <div class="flashsale-status upcoming">即将开始</div>
                                            <div class="flashsale-actions">
                                                <button class="action-btn-sm" onclick="editFlashSale(2)">编辑</button>
                                                <button class="action-btn-sm" onclick="previewFlashSale(2)">预览</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="flashsale-products" class="tab-panel">
                                    <div class="product-list">
                                        <div class="product-item">
                                            <img src="/api/placeholder/60/60" alt="商品">
                                            <div class="product-info">
                                                <h4>iPhone 15 Pro</h4>
                                                <p>原价：¥8999 | 秒杀价：¥7999 | 库存：50件</p>
                                            </div>
                                            <div class="product-actions">
                                                <button class="action-btn-sm" onclick="editFlashProduct(1)">编辑</button>
                                                <button class="action-btn-sm danger" onclick="removeFlashProduct(1)">移除</button>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn-primary" onclick="addFlashProducts()">添加秒杀商品</button>
                                </div>
                                <div id="flashsale-schedule" class="tab-panel">
                                    <div class="schedule-list">
                                        <div class="schedule-item">
                                            <div class="time-slot">10:00 - 12:00</div>
                                            <div class="schedule-status active">已启用</div>
                                            <button class="action-btn-sm" onclick="editTimeSlot('10-12')">编辑</button>
                                        </div>
                                        <div class="schedule-item">
                                            <div class="time-slot">14:00 - 16:00</div>
                                            <div class="schedule-status active">已启用</div>
                                            <button class="action-btn-sm" onclick="editTimeSlot('14-16')">编辑</button>
                                        </div>
                                        <div class="schedule-item">
                                            <div class="time-slot">20:00 - 22:00</div>
                                            <div class="schedule-status inactive">未启用</div>
                                            <button class="action-btn-sm" onclick="editTimeSlot('20-22')">编辑</button>
                                        </div>
                                    </div>
                                </div>
                                <div id="flashsale-data" class="tab-panel">
                                    <div class="stats-grid">
                                        <div class="stat-card">
                                            <div class="stat-value">156</div>
                                            <div class="stat-label">总秒杀场次</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">89.5%</div>
                                            <div class="stat-label">平均售罄率</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">¥486,520</div>
                                            <div class="stat-label">秒杀总销售额</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">2.8万</div>
                                            <div class="stat-label">参与用户数</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                    </div>
                </div>
            `;
        }

        // 社交分享管理模态框
        function getSocialSharingModal() {
            return `
                <div class="modal-content" style="max-width: 1000px;">
                    <div class="modal-header">
                        <h3>社交分享管理</h3>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body" style="max-height: 600px; overflow-y: auto;">
                        <div class="tool-management">
                            <div class="management-tabs">
                                <button class="tab-btn active" onclick="switchToolTab(this, 'share-config')">分享配置</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'invite-rewards')">邀请奖励</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'share-analytics')">分享数据</button>
                            </div>
                            <div class="tab-content">
                                <div id="share-config" class="tab-panel active">
                                    <div class="config-section">
                                        <h4>分享平台配置</h4>
                                        <div class="platform-list">
                                            <div class="platform-item">
                                                <div class="platform-info">
                                                    <i class="fab fa-weixin"></i>
                                                    <span>微信</span>
                                                </div>
                                                <div class="platform-status active">已启用</div>
                                                <button class="action-btn-sm" onclick="configPlatform('wechat')">配置</button>
                                            </div>
                                            <div class="platform-item">
                                                <div class="platform-info">
                                                    <i class="fab fa-weibo"></i>
                                                    <span>微博</span>
                                                </div>
                                                <div class="platform-status active">已启用</div>
                                                <button class="action-btn-sm" onclick="configPlatform('weibo')">配置</button>
                                            </div>
                                            <div class="platform-item">
                                                <div class="platform-info">
                                                    <i class="fab fa-qq"></i>
                                                    <span>QQ空间</span>
                                                </div>
                                                <div class="platform-status inactive">未启用</div>
                                                <button class="action-btn-sm" onclick="configPlatform('qq')">配置</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="config-section">
                                        <h4>分享奖励设置</h4>
                                        <div class="reward-config">
                                            <div class="form-group">
                                                <label>分享者奖励</label>
                                                <select>
                                                    <option value="points">积分奖励</option>
                                                    <option value="coupon">优惠券</option>
                                                    <option value="cash">现金奖励</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label>奖励数额</label>
                                                <input type="number" value="10" placeholder="奖励数额">
                                            </div>
                                            <div class="form-group">
                                                <label>被邀请者奖励</label>
                                                <select>
                                                    <option value="coupon">新人优惠券</option>
                                                    <option value="points">欢迎积分</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="invite-rewards" class="tab-panel">
                                    <div class="invite-list">
                                        <div class="invite-item">
                                            <div class="invite-info">
                                                <h4>邀请好友注册</h4>
                                                <p>每成功邀请1人注册奖励20积分</p>
                                            </div>
                                            <div class="invite-status active">已启用</div>
                                            <div class="invite-stats">
                                                <span>已邀请：1,250人</span>
                                                <span>转化率：68%</span>
                                            </div>
                                            <button class="action-btn-sm" onclick="editInviteRule(1)">编辑</button>
                                        </div>
                                        <div class="invite-item">
                                            <div class="invite-info">
                                                <h4>邀请好友消费</h4>
                                                <p>好友首次消费，双方各得50积分</p>
                                            </div>
                                            <div class="invite-status active">已启用</div>
                                            <div class="invite-stats">
                                                <span>已邀请：3,580人</span>
                                                <span>转化率：45%</span>
                                            </div>
                                            <button class="action-btn-sm" onclick="editInviteRule(2)">编辑</button>
                                        </div>
                                    </div>
                                    <button class="btn-primary" onclick="createInviteRule()">新建邀请规则</button>
                                </div>
                                <div id="share-analytics" class="tab-panel">
                                    <div class="stats-grid">
                                        <div class="stat-card">
                                            <div class="stat-value">28,450</div>
                                            <div class="stat-label">总分享次数</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">12.5%</div>
                                            <div class="stat-label">分享转化率</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">8,650</div>
                                            <div class="stat-label">新增用户</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">¥186,520</div>
                                            <div class="stat-label">分享带来销售额</div>
                                        </div>
                                    </div>
                                    <div class="chart-section">
                                        <h4>分享渠道效果对比</h4>
                                        <canvas id="shareChannelChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                    </div>
                </div>
            `;
        }

        // 积分商城管理模态框
        function getPointsMallModal() {
            return `
                <div class="modal-content" style="max-width: 1000px;">
                    <div class="modal-header">
                        <h3>积分商城管理</h3>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body" style="max-height: 600px; overflow-y: auto;">
                        <div class="tool-management">
                            <div class="management-tabs">
                                <button class="tab-btn active" onclick="switchToolTab(this, 'points-products')">积分商品</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'points-rules')">积分规则</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'points-orders')">兑换订单</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'points-analytics')">积分分析</button>
                            </div>
                            <div class="tab-content">
                                <div id="points-products" class="tab-panel active">
                                    <div class="list-actions">
                                        <button class="btn-primary" onclick="addPointsProduct()">添加商品</button>
                                        <select onchange="filterPointsProducts(this.value)">
                                            <option value="">全部分类</option>
                                            <option value="virtual">虚拟商品</option>
                                            <option value="physical">实物商品</option>
                                            <option value="coupon">优惠券</option>
                                        </select>
                                    </div>
                                    <div class="points-product-list">
                                        <div class="product-item">
                                            <img src="/api/placeholder/60/60" alt="商品">
                                            <div class="product-info">
                                                <h4>10元现金券</h4>
                                                <p>兑换所需：1000积分 | 库存：500张</p>
                                            </div>
                                            <div class="product-stats">
                                                <span>已兑换：120次</span>
                                            </div>
                                            <div class="product-actions">
                                                <button class="action-btn-sm" onclick="editPointsProduct(1)">编辑</button>
                                                <button class="action-btn-sm" onclick="viewProductStats(1)">数据</button>
                                                <button class="action-btn-sm danger" onclick="removePointsProduct(1)">下架</button>
                                            </div>
                                        </div>
                                        <div class="product-item">
                                            <img src="/api/placeholder/60/60" alt="商品">
                                            <div class="product-info">
                                                <h4>品牌鼠标垫</h4>
                                                <p>兑换所需：800积分 | 库存：200个</p>
                                            </div>
                                            <div class="product-stats">
                                                <span>已兑换：86次</span>
                                            </div>
                                            <div class="product-actions">
                                                <button class="action-btn-sm" onclick="editPointsProduct(2)">编辑</button>
                                                <button class="action-btn-sm" onclick="viewProductStats(2)">数据</button>
                                                <button class="action-btn-sm danger" onclick="removePointsProduct(2)">下架</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="points-rules" class="tab-panel">
                                    <div class="rules-section">
                                        <h4>积分获取规则</h4>
                                        <div class="rule-list">
                                            <div class="rule-item">
                                                <div class="rule-info">
                                                    <h5>购物返积分</h5>
                                                    <p>每消费1元返还1积分</p>
                                                </div>
                                                <div class="rule-status active">已启用</div>
                                                <button class="action-btn-sm" onclick="editRule('purchase')">编辑</button>
                                            </div>
                                            <div class="rule-item">
                                                <div class="rule-info">
                                                    <h5>签到积分</h5>
                                                    <p>每日签到奖励5积分</p>
                                                </div>
                                                <div class="rule-status active">已启用</div>
                                                <button class="action-btn-sm" onclick="editRule('checkin')">编辑</button>
                                            </div>
                                            <div class="rule-item">
                                                <div class="rule-info">
                                                    <h5>分享奖励</h5>
                                                    <p>分享商品奖励10积分</p>
                                                </div>
                                                <div class="rule-status active">已启用</div>
                                                <button class="action-btn-sm" onclick="editRule('share')">编辑</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="rules-section">
                                        <h4>积分有效期设置</h4>
                                        <div class="form-group">
                                            <label>积分有效期</label>
                                            <select>
                                                <option value="365">1年</option>
                                                <option value="730">2年</option>
                                                <option value="0">永久有效</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div id="points-orders" class="tab-panel">
                                    <div class="order-list">
                                        <div class="order-item">
                                            <div class="order-info">
                                                <h4>订单 #PE20240315001</h4>
                                                <p>用户：张三 | 商品：10元现金券 | 消耗积分：1000</p>
                                            </div>
                                            <div class="order-status completed">已完成</div>
                                            <div class="order-time">2024-03-15 14:30</div>
                                            <button class="action-btn-sm" onclick="viewOrderDetail('PE20240315001')">详情</button>
                                        </div>
                                        <div class="order-item">
                                            <div class="order-info">
                                                <h4>订单 #PE20240315002</h4>
                                                <p>用户：李四 | 商品：品牌鼠标垫 | 消耗积分：800</p>
                                            </div>
                                            <div class="order-status pending">待发货</div>
                                            <div class="order-time">2024-03-15 15:20</div>
                                            <button class="action-btn-sm" onclick="viewOrderDetail('PE20240315002')">详情</button>
                                        </div>
                                    </div>
                                </div>
                                <div id="points-analytics" class="tab-panel">
                                    <div class="stats-grid">
                                        <div class="stat-card">
                                            <div class="stat-value">2.8万</div>
                                            <div class="stat-label">活跃积分用户</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">186万</div>
                                            <div class="stat-label">总积分发放</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">92万</div>
                                            <div class="stat-label">总积分消耗</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">49.5%</div>
                                            <div class="stat-label">积分使用率</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                    </div>
                </div>
            `;
        }

        // 精准推送管理模态框
        function getPrecisionPushModal() {
            return `
                <div class="modal-content" style="max-width: 1000px;">
                    <div class="modal-header">
                        <h3>精准推送管理</h3>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body" style="max-height: 600px; overflow-y: auto;">
                        <div class="tool-management">
                            <div class="management-tabs">
                                <button class="tab-btn active" onclick="switchToolTab(this, 'push-campaigns')">推送活动</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'user-segments')">用户分群</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'push-templates')">消息模板</button>
                                <button class="tab-btn" onclick="switchToolTab(this, 'push-analytics')">推送分析</button>
                            </div>
                            <div class="tab-content">
                                <div id="push-campaigns" class="tab-panel active">
                                    <div class="list-actions">
                                        <button class="btn-primary" onclick="createPushCampaign()">创建推送</button>
                                        <select onchange="filterPushCampaigns(this.value)">
                                            <option value="">全部状态</option>
                                            <option value="draft">草稿</option>
                                            <option value="scheduled">已计划</option>
                                            <option value="sending">发送中</option>
                                            <option value="completed">已完成</option>
                                        </select>
                                    </div>
                                    <div class="campaign-list">
                                        <div class="campaign-item">
                                            <div class="campaign-info">
                                                <h4>春季新品推荐</h4>
                                                <p>目标用户：活跃用户群 | 计划发送：5,240人</p>
                                                <div class="campaign-schedule">发送时间：2024-03-16 10:00</div>
                                            </div>
                                            <div class="campaign-status scheduled">已计划</div>
                                            <div class="campaign-actions">
                                                <button class="action-btn-sm" onclick="editPushCampaign(1)">编辑</button>
                                                <button class="action-btn-sm" onclick="previewPush(1)">预览</button>
                                                <button class="action-btn-sm danger" onclick="cancelPush(1)">取消</button>
                                            </div>
                                        </div>
                                        <div class="campaign-item">
                                            <div class="campaign-info">
                                                <h4>限时优惠提醒</h4>
                                                <p>目标用户：潜在用户群 | 已发送：8,650人</p>
                                                <div class="campaign-stats">打开率：28% | 点击率：8.5%</div>
                                            </div>
                                            <div class="campaign-status completed">已完成</div>
                                            <div class="campaign-actions">
                                                <button class="action-btn-sm" onclick="viewPushStats(2)">数据</button>
                                                <button class="action-btn-sm" onclick="copyPushCampaign(2)">复制</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="user-segments" class="tab-panel">
                                    <div class="segment-list">
                                        <div class="segment-item">
                                            <div class="segment-info">
                                                <h4>活跃用户群</h4>
                                                <p>最近30天有购买行为的用户</p>
                                                <div class="segment-stats">用户数：5,240人 | 更新时间：2小时前</div>
                                            </div>
                                            <div class="segment-actions">
                                                <button class="action-btn-sm" onclick="editSegment(1)">编辑</button>
                                                <button class="action-btn-sm" onclick="viewSegmentUsers(1)">查看用户</button>
                                                <button class="action-btn-sm primary" onclick="pushToSegment(1)">推送消息</button>
                                            </div>
                                        </div>
                                        <div class="segment-item">
                                            <div class="segment-info">
                                                <h4>潜在用户群</h4>
                                                <p>浏览但未购买的用户</p>
                                                <div class="segment-stats">用户数：12,580人 | 更新时间：1小时前</div>
                                            </div>
                                            <div class="segment-actions">
                                                <button class="action-btn-sm" onclick="editSegment(2)">编辑</button>
                                                <button class="action-btn-sm" onclick="viewSegmentUsers(2)">查看用户</button>
                                                <button class="action-btn-sm primary" onclick="pushToSegment(2)">推送消息</button>
                                            </div>
                                        </div>
                                        <div class="segment-item">
                                            <div class="segment-info">
                                                <h4>流失用户群</h4>
                                                <p>超过60天未活跃的用户</p>
                                                <div class="segment-stats">用户数：3,890人 | 更新时间：6小时前</div>
                                            </div>
                                            <div class="segment-actions">
                                                <button class="action-btn-sm" onclick="editSegment(3)">编辑</button>
                                                <button class="action-btn-sm" onclick="viewSegmentUsers(3)">查看用户</button>
                                                <button class="action-btn-sm primary" onclick="pushToSegment(3)">推送消息</button>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn-primary" onclick="createUserSegment()">创建用户群</button>
                                </div>
                                <div id="push-templates" class="tab-panel">
                                    <div class="template-list">
                                        <div class="template-item">
                                            <div class="template-info">
                                                <h4>促销活动模板</h4>
                                                <p>适用于商品促销、优惠活动推送</p>
                                            </div>
                                            <div class="template-actions">
                                                <button class="action-btn-sm" onclick="editTemplate(1)">编辑</button>
                                                <button class="action-btn-sm" onclick="previewTemplate(1)">预览</button>
                                                <button class="action-btn-sm primary" onclick="useTemplate(1)">使用</button>
                                            </div>
                                        </div>
                                        <div class="template-item">
                                            <div class="template-info">
                                                <h4>新品推荐模板</h4>
                                                <p>适用于新品上市、推荐商品推送</p>
                                            </div>
                                            <div class="template-actions">
                                                <button class="action-btn-sm" onclick="editTemplate(2)">编辑</button>
                                                <button class="action-btn-sm" onclick="previewTemplate(2)">预览</button>
                                                <button class="action-btn-sm primary" onclick="useTemplate(2)">使用</button>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn-primary" onclick="createTemplate()">新建模板</button>
                                </div>
                                <div id="push-analytics" class="tab-panel">
                                    <div class="stats-grid">
                                        <div class="stat-card">
                                            <div class="stat-value">12,450</div>
                                            <div class="stat-label">本月推送量</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">28%</div>
                                            <div class="stat-label">平均打开率</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">8.5%</div>
                                            <div class="stat-label">平均点击率</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value">3.2%</div>
                                            <div class="stat-label">平均转化率</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                    </div>
                </div>
            `;
        }

        // 默认工具模态框（备用）
        function getDefaultToolModal(toolName) {
            return `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3>${toolName}管理</h3>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <p>这里是${toolName}的详细管理界面，功能开发中...</p>
                    </div>
                    <div class="modal-actions">
                        <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                    </div>
                </div>
            `;
        }

        // API服务模拟
        const MarketingAPI = {
            async getCampaigns(filters = {}) {
                // 模拟API调用
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve({
                            data: [],
                            total: 0,
                            page: 1
                        });
                    }, 500);
                });
            },

            async createCampaign(data) {
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve({ success: true, id: Date.now() });
                    }, 800);
                });
            },

            async updateCampaign(id, data) {
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve({ success: true });
                    }, 800);
                });
            },

            async deleteCampaign(id) {
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve({ success: true });
                    }, 500);
                });
            }
        };

        // 导出功能（可选）
        function exportCampaignData() {
            const data = [];
            document.querySelectorAll('.campaign-item').forEach(item => {
                const name = item.querySelector('h4').textContent;
                const status = item.getAttribute('data-status');
                data.push({ name, status });
            });

            const csv = 'data:text/csv;charset=utf-8,' + 
                '活动名称,状态\n' + 
                data.map(row => `${row.name},${row.status}`).join('\n');
            
            const link = document.createElement('a');
            link.setAttribute('href', encodeURI(csv));
            link.setAttribute('download', '营销活动数据.csv');
            link.click();
        }

        // 通用的工具打开函数
        function openTool(toolType, specificAction = null) {
            console.log('打开营销工具:', toolType, specificAction);
            
            // 显示加载提示
            const loadingToast = showLoadingToast('加载管理界面中...');
            
            const toolNames = {
                'coupon': '优惠券管理',
                'member': '会员营销',
                'flash-sale': '限时秒杀',
                'social': '社交分享',
                'points': '积分商城',
                'push': '精准推送'
            };
            
            const toolName = toolNames[toolType];
            if (toolName) {
                // 模拟加载延迟，给用户反馈
                setTimeout(() => {
                    hideLoadingToast(loadingToast);
                    showMarketingToolModal(toolName, specificAction);
                    showNotification(`已打开${toolName}`, 'success');
                }, 300);
            } else {
                hideLoadingToast(loadingToast);
                console.error('未知的工具类型:', toolType);
                showNotification('工具类型不存在', 'error');
            }
        }

        // 显示加载提示
        function showLoadingToast(message) {
            const toast = document.createElement('div');
            toast.className = 'loading-toast';
            toast.innerHTML = `
                <div class="loading-spinner"></div>
                <span>${message}</span>
            `;
            
            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                .loading-toast {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: white;
                    padding: 12px 16px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    border: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    z-index: 10000;
                    font-size: 14px;
                    color: #1e293b;
                }
                .loading-spinner {
                    width: 16px;
                    height: 16px;
                    border: 2px solid #f3f4f6;
                    border-top: 2px solid #3b82f6;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            
            if (!document.querySelector('.loading-toast-style')) {
                style.classList.add('loading-toast-style');
                document.head.appendChild(style);
            }
            
            document.body.appendChild(toast);
            return toast;
        }

        // 隐藏加载提示
        function hideLoadingToast(toast) {
            if (toast && toast.parentNode) {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100px)';
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }

        // 营销工具卡片点击事件
        function openCouponManagement() {
            console.log('打开优惠券管理');
            openTool('coupon');
        }

        function openCreateCoupon() {
            console.log('打开创建优惠券');
            openTool('coupon', 'create');
        }

        function openMemberMarketing() {
            console.log('打开会员营销');
            // 添加按钮加载状态
            const button = event.target;
            addButtonLoading(button, '正在打开...');
            
            // 显示操作反馈
            showNotification('正在打开会员营销管理...', 'info');
            
            setTimeout(() => {
                openTool('member');
                removeButtonLoading(button, '会员策略');
                showNotification('会员营销管理已打开', 'success');
            }, 800);
        }

        function openMemberLevelConfig() {
            console.log('打开会员等级配置');
            const button = event.target;
            addButtonLoading(button, '配置中...');
            
            showNotification('正在打开会员等级配置...', 'info');
            
            setTimeout(() => {
                openTool('member', 'levels');
                removeButtonLoading(button, '等级配置');
                showNotification('会员等级配置已打开', 'success');
            }, 600);
        }

        function openMemberBenefits() {
            console.log('打开权益管理');
            const button = event.target;
            addButtonLoading(button, '加载中...');
            
            showNotification('正在打开权益管理...', 'info');
            
            setTimeout(() => {
                openTool('member', 'benefits');
                removeButtonLoading(button, '权益管理');
                showNotification('权益管理已打开', 'success');
            }, 600);
        }

        function openMemberAnalysis() {
            console.log('打开用户分析');
            const button = event.target;
            addButtonLoading(button, '分析中...');
            
            showNotification('正在打开用户分析...', 'info');
            
            setTimeout(() => {
                openTool('member', 'analysis');
                removeButtonLoading(button, '用户分析');
                showNotification('用户分析已打开', 'success');
            }, 700);
        }

        function openMemberTagManagement() {
            console.log('打开标签管理');
            const button = event.target;
            addButtonLoading(button, '管理中...');
            
            showNotification('正在打开标签管理...', 'info');
            
            setTimeout(() => {
                openTool('member', 'tags');
                removeButtonLoading(button, '标签管理');
                showNotification('标签管理已打开', 'success');
            }, 600);
        }

        function openMemberChurnWarning() {
            console.log('打开流失预警');
            const button = event.target;
            addButtonLoading(button, '预警中...');
            
            showNotification('正在打开流失预警...', 'info');
            
            setTimeout(() => {
                openTool('member', 'churn');
                removeButtonLoading(button, '流失预警');
                showNotification('流失预警已打开', 'success');
            }, 600);
        }

        // 辅助函数：添加按钮加载状态
        function addButtonLoading(button, loadingText) {
            if (!button) return;
            
            button.classList.add('loading');
            button.disabled = true;
            button.setAttribute('data-original-text', button.innerHTML);
            
            const icon = button.querySelector('i');
            if (icon) {
                icon.style.display = 'none';
            }
            
            button.innerHTML = `<span style="margin-right: 8px;">${loadingText}</span>`;
        }

        // 辅助函数：移除按钮加载状态
        function removeButtonLoading(button, defaultText) {
            if (!button) return;
            
            button.classList.remove('loading');
            button.disabled = false;
            
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            } else {
                // 恢复默认文本和图标
                const iconClass = getButtonIconClass(defaultText);
                button.innerHTML = `<i class="${iconClass}"></i> ${defaultText}`;
            }
            
            const icon = button.querySelector('i');
            if (icon) {
                icon.style.display = '';
            }
        }

        // 辅助函数：获取按钮图标类
        function getButtonIconClass(text) {
            const iconMap = {
                '会员策略': 'fas fa-users-cog',
                '等级配置': 'fas fa-layer-group',
                '权益管理': 'fas fa-crown',
                '用户分析': 'fas fa-user-chart',
                '标签管理': 'fas fa-tags',
                '流失预警': 'fas fa-exclamation-triangle'
            };
            return iconMap[text] || 'fas fa-cog';
        }

        function openFlashSale() {
            console.log('打开限时秒杀管理');
            openTool('flash-sale');
        }

        function openAddFlashSaleProduct() {
            console.log('打开新增秒杀商品');
            openTool('flash-sale', 'add-product');
        }

        function openSocialShare() {
            console.log('打开社交分享管理');
            openTool('social');
        }

        function openShareRewardConfig() {
            console.log('打开分享奖励配置');
            openTool('social', 'rewards');
        }

        function openPointsMall() {
            console.log('打开积分商城管理');
            openTool('points');
        }

        function openPointsConfig() {
            console.log('打开积分设置');
            openTool('points', 'config');
        }

        function openTargetedPush() {
            console.log('打开精准推送管理');
            openTool('push');
        }

        function openCreatePush() {
            console.log('打开创建推送');
            openTool('push', 'create');
        }

        // 优惠券管理增强功能
        function filterCoupons() {
            const searchTerm = document.getElementById('couponSearch').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            
            const coupons = document.querySelectorAll('.coupon-item.enhanced');
            
            coupons.forEach(coupon => {
                const name = coupon.querySelector('h4').textContent.toLowerCase();
                const status = coupon.getAttribute('data-status');
                const type = coupon.getAttribute('data-type');
                
                const matchesSearch = name.includes(searchTerm);
                const matchesStatus = !statusFilter || status === statusFilter;
                const matchesType = !typeFilter || type === typeFilter;
                
                coupon.style.display = (matchesSearch && matchesStatus && matchesType) ? 'flex' : 'none';
            });
        }

        function showAdvancedFilter() {
            // 显示高级筛选面板的逻辑
            alert('高级筛选功能正在开发中...');
        }

        function showCreateCouponModal() {
            // 切换到创建优惠券标签页
            const createTab = document.querySelector('[onclick="switchToolTab(this, \'coupon-create\')"]');
            if (createTab) {
                switchToolTab(createTab, 'coupon-create');
            }
        }

        function showBatchOperationModal() {
            const selectedCoupons = document.querySelectorAll('.coupon-select:checked');
            if (selectedCoupons.length === 0) {
                alert('请先选择要操作的优惠券');
                return;
            }
            
            const operations = ['批量启用', '批量暂停', '批量删除', '批量导出'];
            const operation = prompt('选择批量操作：\n1. 批量启用\n2. 批量暂停\n3. 批量删除\n4. 批量导出\n\n请输入序号：');
            
            if (operation && operation >= 1 && operation <= 4) {
                console.log(`执行${operations[operation-1]}，影响${selectedCoupons.length}个优惠券`);
                showNotification(`${operations[operation-1]}操作已完成`, 'success');
            }
        }

        function exportCoupons() {
            const coupons = [];
            document.querySelectorAll('.coupon-item.enhanced').forEach(item => {
                const name = item.querySelector('h4').textContent;
                const status = item.getAttribute('data-status');
                const type = item.getAttribute('data-type');
                coupons.push({ name, status, type });
            });

            const csv = 'data:text/csv;charset=utf-8,' + 
                '优惠券名称,状态,类型\n' + 
                coupons.map(row => `${row.name},${row.status},${row.type}`).join('\n');
            
            const link = document.createElement('a');
            link.setAttribute('href', encodeURI(csv));
            link.setAttribute('download', '优惠券数据.csv');
            link.click();
        }

        function editCoupon(id) {
            console.log('编辑优惠券:', id);
            // 切换到创建优惠券标签页并加载数据
            showCreateCouponModal();
            // 这里可以加载优惠券数据到表单
            showNotification('正在加载优惠券数据...', 'info');
        }

        function copyCoupon(id) {
            console.log('复制优惠券:', id);
            showNotification('优惠券已复制', 'success');
        }

        function viewCouponStats(id) {
            console.log('查看优惠券数据:', id);
            // 切换到数据统计标签页
            const statsTab = document.querySelector('[onclick="switchToolTab(this, \'coupon-stats\')"]');
            if (statsTab) {
                switchToolTab(statsTab, 'coupon-stats');
            }
        }

        function pauseCoupon(id) {
            if (confirm('确定要暂停这个优惠券吗？')) {
                console.log('暂停优惠券:', id);
                // 更新优惠券状态显示
                const coupon = document.querySelector(`[data-id="${id}"]`);
                if (coupon) {
                    const statusElement = coupon.querySelector('.coupon-status');
                    statusElement.textContent = '已暂停';
                    statusElement.className = 'coupon-status paused';
                    coupon.setAttribute('data-status', 'paused');
                }
                showNotification('优惠券已暂停', 'success');
            }
        }

        function resumeCoupon(id) {
            if (confirm('确定要恢复这个优惠券吗？')) {
                console.log('恢复优惠券:', id);
                // 更新优惠券状态显示
                const coupon = document.querySelector(`[data-id="${id}"]`);
                if (coupon) {
                    const statusElement = coupon.querySelector('.coupon-status');
                    statusElement.textContent = '生效中';
                    statusElement.className = 'coupon-status active';
                    coupon.setAttribute('data-status', 'active');
                }
                showNotification('优惠券已恢复', 'success');
            }
        }

        function deleteCoupon(id) {
            if (confirm('确定要删除这个优惠券吗？此操作不可恢复。')) {
                console.log('删除优惠券:', id);
                const coupon = document.querySelector(`[data-id="${id}"]`);
                if (coupon) {
                    coupon.remove();
                }
                showNotification('优惠券已删除', 'success');
            }
        }

        function changePage(page) {
            console.log('切换到第', page, '页');
            // 更新分页按钮状态
            document.querySelectorAll('.page-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            showNotification(`已切换到第${page}页`, 'info');
        }

        // 创建优惠券表单相关功能
        function validateCouponForm() {
            const form = document.getElementById('couponCreateForm');
            if (!form) return;

            const name = document.getElementById('couponName').value;
            const type = document.getElementById('couponType').value;
            const value = document.getElementById('discountValue').value;
            const quantity = document.getElementById('totalQuantity').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            const isValid = name && type && value && quantity && startDate && endDate;
            
            const submitBtn = document.getElementById('createCouponBtn');
            if (submitBtn) {
                submitBtn.disabled = !isValid;
            }

            return isValid;
        }

        function updateCouponTypeOptions() {
            const type = document.getElementById('couponType').value;
            const valueGroup = document.getElementById('discountValueGroup');
            const valueLabel = document.getElementById('discountValueLabel');
            const valueInput = document.getElementById('discountValue');
            const hint = document.getElementById('discountHint');

            if (!valueGroup || !valueLabel || !valueInput || !hint) return;

            switch(type) {
                case 'percentage':
                    valueLabel.textContent = '折扣百分比 *';
                    valueInput.placeholder = '输入折扣百分比(如:15表示85折)';
                    valueInput.max = '100';
                    hint.textContent = '输入0-100之间的数字，如15表示85折';
                    break;
                case 'fixed':
                    valueLabel.textContent = '减免金额 *';
                    valueInput.placeholder = '输入减免金额';
                    valueInput.removeAttribute('max');
                    hint.textContent = '输入具体的减免金额';
                    break;
                case 'free_shipping':
                    valueLabel.textContent = '免运费';
                    valueInput.value = '0';
                    valueInput.disabled = true;
                    hint.textContent = '免运费券无需设置金额';
                    break;
                default:
                    valueInput.disabled = false;
                    hint.textContent = '请先选择优惠类型';
            }
        }

        function updatePreview() {
            const name = document.getElementById('couponName').value || '优惠券名称';
            const type = document.getElementById('couponType').value;
            const value = document.getElementById('discountValue').value;
            const minAmount = document.getElementById('minAmount').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            const previewAmount = document.querySelector('.preview-amount');
            const previewCondition = document.querySelector('.preview-condition');
            const previewName = document.querySelector('.preview-name');
            const previewValidity = document.querySelector('.preview-validity');

            if (!previewAmount || !previewCondition || !previewName || !previewValidity) return;

            // 更新预览内容
            let amountText = '-';
            let conditionText = '-';

            if (type && value) {
                switch(type) {
                    case 'percentage':
                        amountText = `${100 - parseFloat(value)}折`;
                        break;
                    case 'fixed':
                        amountText = `¥${value}`;
                        break;
                    case 'free_shipping':
                        amountText = '免邮';
                        break;
                }
            }

            if (minAmount && parseFloat(minAmount) > 0) {
                conditionText = `满${minAmount}可用`;
            } else {
                conditionText = '无门槛';
            }

            previewAmount.textContent = amountText;
            previewCondition.textContent = conditionText;
            previewName.textContent = name;

            if (startDate && endDate) {
                const start = new Date(startDate).toLocaleDateString();
                const end = new Date(endDate).toLocaleDateString();
                previewValidity.textContent = `有效期：${start} 至 ${end}`;
            } else {
                previewValidity.textContent = '有效期：-';
            }

            // 更新预估数据
            updateEstimatedStats();
        }

        function updateEstimatedStats() {
            const type = document.getElementById('couponType').value;
            const value = document.getElementById('discountValue').value;
            const quantity = document.getElementById('totalQuantity').value;

            const claimRate = document.getElementById('estimatedClaimRate');
            const usageRate = document.getElementById('estimatedUsageRate');
            const cost = document.getElementById('estimatedCost');

            if (!claimRate || !usageRate || !cost) return;

            // 模拟预估计算
            if (type && value && quantity) {
                const estimatedClaim = Math.round(quantity * 0.6);
                const estimatedUsage = Math.round(estimatedClaim * 0.4);
                let estimatedCost = 0;

                switch(type) {
                    case 'percentage':
                        estimatedCost = estimatedUsage * 50 * (parseFloat(value) / 100);
                        break;
                    case 'fixed':
                        estimatedCost = estimatedUsage * parseFloat(value);
                        break;
                    case 'free_shipping':
                        estimatedCost = estimatedUsage * 10;
                        break;
                }

                claimRate.textContent = `${Math.round((estimatedClaim / quantity) * 100)}%`;
                usageRate.textContent = `${Math.round((estimatedUsage / estimatedClaim) * 100)}%`;
                cost.textContent = `¥${estimatedCost.toLocaleString()}`;
            } else {
                claimRate.textContent = '-';
                usageRate.textContent = '-';
                cost.textContent = '-';
            }
        }

        function saveCouponDraft() {
            console.log('保存优惠券草稿');
            showNotification('草稿已保存', 'success');
        }

        function previewCoupon() {
            console.log('预览优惠券效果');
            showNotification('预览功能开发中...', 'info');
        }

        // 优惠券表单提交
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('couponCreateForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    if (!validateCouponForm()) {
                        showNotification('请填写完整的优惠券信息', 'error');
                        return;
                    }

                    // 模拟创建优惠券
                    showLoading();
                    setTimeout(() => {
                        hideLoading();
                        showNotification('优惠券创建成功！', 'success');
                        form.reset();
                        updatePreview();
                        
                        // 可以在这里添加新创建的优惠券到列表
                        addNewCouponToList();
                    }, 1000);
                });
            }
        });

        function addNewCouponToList() {
            // 添加新优惠券到列表的逻辑
            console.log('添加新优惠券到列表');
        }

        // 数据统计相关功能
        function updateCouponStats() {
            const timeRange = document.getElementById('statsTimeRange').value;
            console.log('更新优惠券统计数据，时间范围：', timeRange, '天');
            showNotification('统计数据已更新', 'info');
        }

        function exportStatsData() {
            console.log('导出统计报表');
            showNotification('报表导出中...', 'info');
            setTimeout(() => {
                showNotification('报表导出完成', 'success');
            }, 2000);
        }

        // 模板库功能
        function applyCouponTemplate(templateId) {
            console.log('应用优惠券模板:', templateId);
            
            // 模板数据
            const templates = {
                newuser: {
                    name: '新用户专享券',
                    type: 'fixed',
                    value: 20,
                    minAmount: 100,
                    quantity: 1000,
                    limitPerUser: 1
                },
                discount: {
                    name: '全场通用折扣券',
                    type: 'percentage',
                    value: 15,
                    minAmount: 0,
                    quantity: 2000,
                    limitPerUser: 2
                },
                shipping: {
                    name: '免运费券',
                    type: 'free_shipping',
                    value: 0,
                    minAmount: 50,
                    quantity: 5000,
                    limitPerUser: 3
                },
                festival: {
                    name: '节日促销券',
                    type: 'fixed',
                    value: 50,
                    minAmount: 300,
                    quantity: 500,
                    limitPerUser: 1
                },
                vip: {
                    name: 'VIP专享券',
                    type: 'percentage',
                    value: 20,
                    minAmount: 200,
                    quantity: 200,
                    limitPerUser: 1
                },
                flash: {
                    name: '限时抢购券',
                    type: 'fixed',
                    value: 30,
                    minAmount: 150,
                    quantity: 300,
                    limitPerUser: 1
                }
            };

            const template = templates[templateId];
            if (template) {
                // 切换到创建标签页
                showCreateCouponModal();
                
                // 填充表单数据
                setTimeout(() => {
                    document.getElementById('couponName').value = template.name;
                    document.getElementById('couponType').value = template.type;
                    document.getElementById('discountValue').value = template.value;
                    document.getElementById('minAmount').value = template.minAmount;
                    document.getElementById('totalQuantity').value = template.quantity;
                    document.getElementById('limitPerUser').value = template.limitPerUser;
                    
                    updateCouponTypeOptions();
                    updatePreview();
                    validateCouponForm();
                }, 100);
                
                showNotification('模板已应用，请完善其他信息', 'success');
            }
        }

        // 新增营销工具功能函数
        // ============ 优惠券相关增强功能 ============
        function viewCouponAnalytics() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开优惠券数据分析界面...', 'info');
                console.log('查看优惠券分析');
                // 这里可以添加跳转到分析页面的逻辑
            }, 800);
        }

        function openCouponTemplates() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开优惠券模板库...', 'info');
                console.log('打开优惠券模板');
                // 切换到模板标签页
                const templateTab = document.querySelector('[onclick="switchToolTab(this, \'coupon-templates\')"]');
                if (templateTab) {
                    switchToolTab(templateTab, 'coupon-templates');
                }
            }, 800);
        }

        // ============ 会员营销相关增强功能 ============
        function openMemberBenefits() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开会员权益管理界面...', 'info');
                console.log('管理会员权益');
                // 这里可以添加跳转到会员权益页面的逻辑
            }, 800);
        }

        function openMemberAnalysis() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开会员用户分析界面...', 'info');
                console.log('分析会员用户');
                // 这里可以添加跳转到会员分析页面的逻辑
            }, 800);
        }

        // ============ 秒杀相关增强功能 ============
        function openFlashSaleSchedule() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开秒杀活动排期界面...', 'info');
                console.log('管理秒杀排期');
                // 这里可以添加跳转到排期管理页面的逻辑
            }, 800);
        }

        function openFlashSaleAnalytics() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开秒杀效果分析界面...', 'info');
                console.log('分析秒杀效果');
                // 这里可以添加跳转到秒杀分析页面的逻辑
            }, 800);
        }

        // ============ 社交分享相关增强功能 ============
        function openShareTemplates() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开分享模板管理界面...', 'info');
                console.log('管理分享模板');
                // 这里可以添加跳转到分享模板页面的逻辑
            }, 800);
        }

        function openInviteSystem() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开邀请体系管理界面...', 'info');
                console.log('管理邀请体系');
                // 这里可以添加跳转到邀请体系页面的逻辑
            }, 800);
        }

        // ============ 积分商城相关增强功能 ============
        function openPointsActivity() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开积分活动管理界面...', 'info');
                console.log('管理积分活动');
                // 这里可以添加跳转到积分活动页面的逻辑
            }, 800);
        }

        function openPointsAnalysis() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开积分数据分析界面...', 'info');
                console.log('分析积分数据');
                // 这里可以添加跳转到积分分析页面的逻辑
            }, 800);
        }

        // ============ 精准推送相关增强功能 ============
        function openUserSegments() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开用户分群管理界面...', 'info');
                console.log('管理用户分群');
                // 这里可以添加跳转到用户分群页面的逻辑
            }, 800);
        }

        function openPushAnalytics() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开推送效果分析界面...', 'info');
                console.log('分析推送效果');
                // 这里可以添加跳转到推送分析页面的逻辑
            }, 800);
        }

        // ============ 新增高级功能函数 ============
        
        // 优惠券高级功能
        function openCouponRiskControl() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开优惠券风控设置界面...', 'info');
                console.log('打开优惠券风控设置');
                // 这里可以添加跳转到风控设置页面的逻辑
            }, 800);
        }

        function openCouponABTest() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开优惠券A/B测试界面...', 'info');
                console.log('打开优惠券A/B测试');
                // 这里可以添加跳转到A/B测试页面的逻辑
            }, 800);
        }

        // 会员营销高级功能
        function openMemberTagManagement() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开会员标签管理界面...', 'info');
                console.log('打开会员标签管理');
                // 这里可以添加跳转到标签管理页面的逻辑
            }, 800);
        }

        function openMemberChurnWarning() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开会员流失预警界面...', 'info');
                console.log('打开会员流失预警');
                // 这里可以添加跳转到流失预警页面的逻辑
            }, 800);
        }

        // 限时秒杀高级功能
        function openFlashSaleInventoryWarning() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开秒杀库存预警界面...', 'info');
                console.log('打开秒杀库存预警');
                // 这里可以添加跳转到库存预警页面的逻辑
            }, 800);
        }

        function openFlashSaleAntiBot() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开秒杀防刷机制管理界面...', 'info');
                console.log('打开秒杀防刷机制');
                // 这里可以添加跳转到防刷机制页面的逻辑
            }, 800);
        }

        // 社交分享高级功能
        function openShareRealTimeMonitor() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开分享实时监控界面...', 'info');
                console.log('打开分享实时监控');
                // 这里可以添加跳转到实时监控页面的逻辑
            }, 800);
        }

        function openShareContentReview() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开分享内容审核界面...', 'info');
                console.log('打开分享内容审核');
                // 这里可以添加跳转到内容审核页面的逻辑
            }, 800);
        }

        // 积分商城高级功能
        function openPointsAntiCheat() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开积分防刷保护界面...', 'info');
                console.log('打开积分防刷保护');
                // 这里可以添加跳转到防刷保护页面的逻辑
            }, 800);
        }

        function openPointsExpiryReminder() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开积分到期提醒设置界面...', 'info');
                console.log('打开积分到期提醒');
                // 这里可以添加跳转到到期提醒页面的逻辑
            }, 800);
        }

        // 精准推送高级功能
        function openPushTimingOptimization() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开推送时机优化界面...', 'info');
                console.log('打开推送时机优化');
                // 这里可以添加跳转到时机优化页面的逻辑
            }, 800);
        }

        function openPushFrequencyControl() {
            showLoading();
            setTimeout(() => {
                hideLoading();
                showNotification('正在打开推送频率控制界面...', 'info');
                console.log('打开推送频率控制');
                // 这里可以添加跳转到频率控制页面的逻辑
            }, 800);
        }

        // ============ 通用加载和提示功能 ============
        function showLoading() {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'loading-indicator';
            loadingIndicator.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; z-index: 9999;">
                    <div style="background: white; padding: 20px; border-radius: 8px; text-align: center;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #3b82f6; margin-bottom: 10px;"></i>
                        <div>加载中...</div>
                    </div>
                </div>
            `;
            document.body.appendChild(loadingIndicator);
        }

        function hideLoading() {
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
        }

        // ============ 会员管理相关功能 ============

        // 会员等级管理
        function addMemberLevel() {
            showModal('新增会员等级', `
                <div class="form-group">
                    <label>等级名称</label>
                    <input type="text" id="levelName" placeholder="请输入等级名称">
                </div>
                <div class="form-group">
                    <label>成长值要求</label>
                    <input type="number" id="levelRequirement" placeholder="升级所需成长值">
                </div>
                <div class="form-group">
                    <label>等级图标</label>
                    <select id="levelIcon">
                        <option value="fas fa-user">用户</option>
                        <option value="fas fa-medal">勋章</option>
                        <option value="fas fa-crown">皇冠</option>
                        <option value="fas fa-gem">钻石</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>等级颜色</label>
                    <input type="color" id="levelColor" value="#3b82f6">
                </div>
            `, [
                { text: '确定', action: 'saveMemberLevel()', class: 'primary' },
                { text: '取消', action: 'closeModal()' }
            ]);
        }

        function editMemberLevel(levelId) {
            const levels = {
                1: { name: '普通会员', requirement: 0, icon: 'fas fa-user', color: '#64748b' },
                2: { name: '银牌会员', requirement: 500, icon: 'fas fa-medal', color: '#c0c0c0' },
                3: { name: '金牌会员', requirement: 2000, icon: 'fas fa-crown', color: '#f39c12' },
                4: { name: '钻石会员', requirement: 8000, icon: 'fas fa-gem', color: '#e74c3c' }
            };
            
            const level = levels[levelId];
            showModal('编辑会员等级', `
                <div class="form-group">
                    <label>等级名称</label>
                    <input type="text" id="levelName" value="${level.name}">
                </div>
                <div class="form-group">
                    <label>成长值要求</label>
                    <input type="number" id="levelRequirement" value="${level.requirement}">
                </div>
                <div class="form-group">
                    <label>等级图标</label>
                    <select id="levelIcon">
                        <option value="fas fa-user" ${level.icon === 'fas fa-user' ? 'selected' : ''}>用户</option>
                        <option value="fas fa-medal" ${level.icon === 'fas fa-medal' ? 'selected' : ''}>勋章</option>
                        <option value="fas fa-crown" ${level.icon === 'fas fa-crown' ? 'selected' : ''}>皇冠</option>
                        <option value="fas fa-gem" ${level.icon === 'fas fa-gem' ? 'selected' : ''}>钻石</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>等级颜色</label>
                    <input type="color" id="levelColor" value="${level.color}">
                </div>
            `, [
                { text: '保存', action: `updateMemberLevel(${levelId})`, class: 'primary' },
                { text: '取消', action: 'closeModal()' }
            ]);
        }

        function viewLevelAnalytics(levelId) {
            showModal('会员等级数据分析', `
                <div class="analytics-container">
                    <div class="stats-overview">
                        <div class="stat-item">
                            <div class="stat-value">3,280</div>
                            <div class="stat-label">当前人数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">+158</div>
                            <div class="stat-label">本月新增</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥890</div>
                            <div class="stat-label">平均消费</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">78%</div>
                            <div class="stat-label">活跃度</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="levelChart"></canvas>
                    </div>
                </div>
            `, [
                { text: '导出数据', action: `exportLevelData(${levelId})`, class: 'primary' },
                { text: '关闭', action: 'closeModal()' }
            ]);
        }

        function manageLevelUsers(levelId) {
            showModal('管理会员用户', `
                <div class="user-management">
                    <div class="search-filters">
                        <input type="text" placeholder="搜索用户..." onkeyup="searchLevelUsers(this.value)">
                        <select onchange="filterLevelUsers(this.value)">
                            <option value="">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">非活跃</option>
                        </select>
                    </div>
                    <div class="user-list" id="levelUsersList">
                        <div class="user-item">
                            <div class="user-info">
                                <h4>张三</h4>
                                <p>注册时间：2023-05-15 | 累计消费：¥2,350</p>
                            </div>
                            <div class="user-status active">活跃</div>
                            <div class="user-actions">
                                <button class="action-btn-sm" onclick="viewUserProfile(1)">详情</button>
                                <button class="action-btn-sm" onclick="sendUserMessage(1)">消息</button>
                            </div>
                        </div>
                    </div>
                </div>
            `, [
                { text: '批量操作', action: 'showBatchUserActions()', class: 'primary' },
                { text: '关闭', action: 'closeModal()' }
            ]);
        }

        function saveMemberLevel() {
            const levelName = document.getElementById('levelName').value;
            const levelRequirement = document.getElementById('levelRequirement').value;
            
            if (!levelName || !levelRequirement) {
                alert('请填写完整信息');
                return;
            }
            
            closeModal();
            showToast('会员等级已保存');
        }

        function updateMemberLevel(levelId) {
            closeModal();
            showToast('会员等级已更新');
        }

        // 会员权益管理
        function addMemberBenefit() {
            showModal('新增会员权益', `
                <div class="form-group">
                    <label>权益名称</label>
                    <input type="text" id="benefitName" placeholder="请输入权益名称">
                </div>
                <div class="form-group">
                    <label>权益类型</label>
                    <select id="benefitType">
                        <option value="discount">折扣优惠</option>
                        <option value="points">积分返现</option>
                        <option value="shipping">免运费</option>
                        <option value="service">专属服务</option>
                        <option value="product">专属商品</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>权益描述</label>
                    <textarea id="benefitDescription" placeholder="请输入权益详细描述"></textarea>
                </div>
                <div class="form-group">
                    <label>适用等级</label>
                    <select id="benefitLevels" multiple>
                        <option value="1">普通会员</option>
                        <option value="2">银牌会员</option>
                        <option value="3">金牌会员</option>
                        <option value="4">钻石会员</option>
                    </select>
                </div>
            `, [
                { text: '确定', action: 'saveMemberBenefit()', class: 'primary' },
                { text: '取消', action: 'closeModal()' }
            ]);
        }

        function editBenefit(benefitType) {
            showModal('编辑会员权益', `
                <div class="form-group">
                    <label>权益名称</label>
                    <input type="text" id="benefitName" value="积分返现">
                </div>
                <div class="form-group">
                    <label>权益描述</label>
                    <textarea id="benefitDescription">每消费1元返1积分，100积分=1元</textarea>
                </div>
                <div class="form-group">
                    <label>权益设置</label>
                    <div class="benefit-settings">
                        <label><input type="checkbox" checked> 启用权益</label>
                        <label><input type="checkbox"> 自动发放</label>
                        <label><input type="checkbox" checked> 显示在会员中心</label>
                    </div>
                </div>
            `, [
                { text: '保存', action: `updateBenefit('${benefitType}')`, class: 'primary' },
                { text: '取消', action: 'closeModal()' }
            ]);
        }

        function viewBenefitData(benefitType) {
            showModal('权益使用数据', `
                <div class="benefit-analytics">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">78%</div>
                            <div class="stat-label">使用率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">2,840</div>
                            <div class="stat-label">使用人数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">¥12,560</div>
                            <div class="stat-label">节省金额</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">4.2</div>
                            <div class="stat-label">满意度评分</div>
                        </div>
                    </div>
                </div>
            `, [
                { text: '导出报告', action: `exportBenefitReport('${benefitType}')`, class: 'primary' },
                { text: '关闭', action: 'closeModal()' }
            ]);
        }

        function enableBenefit(benefitType) {
            if (confirm('确定要启用此权益吗？')) {
                showToast('权益已启用');
            }
        }

        function saveMemberBenefit() {
            const benefitName = document.getElementById('benefitName').value;
            const benefitDescription = document.getElementById('benefitDescription').value;
            
            if (!benefitName || !benefitDescription) {
                alert('请填写完整信息');
                return;
            }
            
            closeModal();
            showToast('会员权益已保存');
        }

        function updateBenefit(benefitType) {
            closeModal();
            showToast('权益设置已更新');
        }

        function showBenefitTemplate() {
            showModal('权益模板', `
                <div class="template-grid">
                    <div class="template-card" onclick="applyBenefitTemplate('vip')">
                        <h4>VIP权益包</h4>
                        <p>包含：专享折扣、免运费、优先客服、生日特权</p>
                    </div>
                    <div class="template-card" onclick="applyBenefitTemplate('loyalty')">
                        <h4>忠诚度奖励</h4>
                        <p>包含：积分返现、消费返现、推荐奖励</p>
                    </div>
                    <div class="template-card" onclick="applyBenefitTemplate('premium')">
                        <h4>高级会员</h4>
                        <p>包含：专属商品、优先发货、专属客服、定制服务</p>
                    </div>
                </div>
            `, [
                { text: '关闭', action: 'closeModal()' }
            ]);
        }

        // 会员活动管理
        function createMemberActivity() {
            showModal('创建会员活动', `
                <div class="form-group">
                    <label>活动名称</label>
                    <input type="text" id="activityName" placeholder="请输入活动名称">
                </div>
                <div class="form-group">
                    <label>活动类型</label>
                    <select id="activityType">
                        <option value="promotion">促销活动</option>
                        <option value="reward">奖励活动</option>
                        <option value="engagement">互动活动</option>
                        <option value="loyalty">忠诚度活动</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>活动时间</label>
                    <div class="date-range">
                        <input type="datetime-local" id="activityStartTime">
                        <span>至</span>
                        <input type="datetime-local" id="activityEndTime">
                    </div>
                </div>
                <div class="form-group">
                    <label>目标用户</label>
                    <select id="activityTarget" multiple>
                        <option value="all">全部会员</option>
                        <option value="level1">普通会员</option>
                        <option value="level2">银牌会员</option>
                        <option value="level3">金牌会员</option>
                        <option value="level4">钻石会员</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>活动描述</label>
                    <textarea id="activityDescription" placeholder="请输入活动详细描述"></textarea>
                </div>
            `, [
                { text: '创建', action: 'saveMemberActivity()', class: 'primary' },
                { text: '取消', action: 'closeModal()' }
            ]);
        }

        function editActivity(activityId) {
            showModal('编辑会员活动', `
                <div class="activity-edit-form">
                    <div class="form-group">
                        <label>活动名称</label>
                        <input type="text" id="activityName" value="春季会员日大促">
                    </div>
                    <div class="form-group">
                        <label>活动状态</label>
                        <select id="activityStatus">
                            <option value="active">进行中</option>
                            <option value="paused">已暂停</option>
                            <option value="upcoming">即将开始</option>
                            <option value="completed">已结束</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>活动规则</label>
                        <textarea id="activityRules">全场9折起，限时三天</textarea>
                    </div>
                </div>
            `, [
                { text: '保存', action: `updateActivity(${activityId})`, class: 'primary' },
                { text: '取消', action: 'closeModal()' }
            ]);
        }

        function viewActivityData(activityId) {
            showModal('活动数据分析', `
                <div class="activity-analytics">
                    <div class="stats-overview">
                        <div class="stat-item">
                            <div class="stat-value">2,840</div>
                            <div class="stat-label">参与人数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥156,430</div>
                            <div class="stat-label">销售额</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">18.5%</div>
                            <div class="stat-label">转化率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">4.2</div>
                            <div class="stat-label">满意度</div>
                        </div>
                    </div>
                </div>
            `, [
                { text: '导出报告', action: `exportActivityReport(${activityId})`, class: 'primary' },
                { text: '关闭', action: 'closeModal()' }
            ]);
        }

        function pauseActivity(activityId) {
            if (confirm('确定要暂停此活动吗？')) {
                showToast('活动已暂停');
            }
        }

        function copyActivity(activityId) {
            showToast('活动已复制，请在创建活动中查看');
        }

        function archiveActivity(activityId) {
            if (confirm('确定要归档此活动吗？归档后将无法恢复。')) {
                showToast('活动已归档');
            }
        }

        function saveMemberActivity() {
            const activityName = document.getElementById('activityName').value;
            const activityDescription = document.getElementById('activityDescription').value;
            
            if (!activityName || !activityDescription) {
                alert('请填写完整信息');
                return;
            }
            
            closeModal();
            showToast('会员活动已创建');
        }

        function updateActivity(activityId) {
            closeModal();
            showToast('活动信息已更新');
        }

        function filterActivities() {
            const searchTerm = document.getElementById('activitySearch')?.value.toLowerCase() || '';
            const statusFilter = document.getElementById('activityStatusFilter')?.value || '';
            const typeFilter = document.getElementById('activityTypeFilter')?.value || '';
            
            const activities = document.querySelectorAll('.activity-item');
            
            activities.forEach(activity => {
                const name = activity.querySelector('h4')?.textContent.toLowerCase() || '';
                const status = activity.getAttribute('data-status') || '';
                const type = activity.getAttribute('data-type') || '';
                
                const matchesSearch = !searchTerm || name.includes(searchTerm);
                const matchesStatus = !statusFilter || status === statusFilter;
                const matchesType = !typeFilter || type === typeFilter;
                
                activity.style.display = (matchesSearch && matchesStatus && matchesType) ? 'flex' : 'none';
            });
        }

        function showActivityTemplate() {
            showModal('活动模板', `
                <div class="template-grid">
                    <div class="template-card" onclick="applyActivityTemplate('member-day')">
                        <h4>会员日活动</h4>
                        <p>每月固定时间的会员专享促销活动</p>
                    </div>
                    <div class="template-card" onclick="applyActivityTemplate('sign-in')">
                        <h4>签到奖励</h4>
                        <p>连续签到获得积分和奖励的活动</p>
                    </div>
                    <div class="template-card" onclick="applyActivityTemplate('level-upgrade')">
                        <h4>等级升级奖励</h4>
                        <p>会员等级提升时的奖励活动</p>
                    </div>
                </div>
            `, [
                { text: '关闭', action: 'closeModal()' }
            ]);
        }

        function showActivityCalendar() {
            showModal('活动日历', `
                <div class="calendar-container">
                    <div class="calendar-header">
                        <button onclick="previousMonth()">&lt;</button>
                        <h3 id="calendarMonth">2024年3月</h3>
                        <button onclick="nextMonth()">&gt;</button>
                    </div>
                    <div class="calendar-grid" id="calendarGrid">
                        <div class="calendar-day">1</div>
                        <div class="calendar-day">2</div>
                        <div class="calendar-day">3</div>
                        <!-- 更多日期... -->
                    </div>
                </div>
            `, [
                { text: '关闭', action: 'closeModal()' }
            ]);
        }

        // 通用工具函数
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        function showModal(title, content, actions = []) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;
            
            let actionsHTML = '';
            actions.forEach(action => {
                actionsHTML += `<button class="btn ${action.class || ''}" onclick="${action.action}">${action.text}</button>`;
            });
            
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto; background: white; border-radius: 12px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center;">
                        <h3 style="margin: 0; color: #1e293b;">${title}</h3>
                        <span class="close" onclick="this.closest('.modal').remove()" style="cursor: pointer; font-size: 24px; color: #64748b;">&times;</span>
                    </div>
                    <div class="modal-body" style="padding: 20px;">
                        ${content}
                    </div>
                    <div class="modal-actions" style="padding: 20px; border-top: 1px solid #e2e8f0; display: flex; gap: 10px; justify-content: flex-end;">
                        ${actionsHTML}
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }

        function closeModal() {
            const modal = document.querySelector('.modal');
            if (modal) {
                modal.remove();
            }
        }
    </script>
</body>
</html> 