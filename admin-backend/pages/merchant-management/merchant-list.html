<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户列表管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="merchant-list.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="merchant-list-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title">商户列表管理</h1>
                    <p class="page-subtitle">管理所有入驻商户的信息和状态</p>
                </div>
                
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="exportMerchants()">
                        <i class="fas fa-download"></i> 导出商户
                    </button>
                    <button class="btn btn-success" onclick="importMerchants()">
                        <i class="fas fa-upload"></i> 批量导入
                    </button>
                    <button class="btn btn-primary" onclick="createMerchant()">
                        <i class="fas fa-plus"></i> 新增商户
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon total">
                    <i class="fas fa-store"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">1,248</div>
                    <div class="stat-label">商户总数</div>
                    <div class="stat-change positive">+12% 相比上月</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon active">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">1,156</div>
                    <div class="stat-label">正常营业</div>
                    <div class="stat-change positive">+8% 相比上月</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">43</div>
                    <div class="stat-label">待审核</div>
                    <div class="stat-change negative">-2% 相比上月</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon frozen">
                    <i class="fas fa-ban"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">49</div>
                    <div class="stat-label">冻结/关闭</div>
                    <div class="stat-change positive">-15% 相比上月</div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filters-section">
            <div class="filters-row">
                <div class="filter-group">
                    <label class="filter-label">商户状态</label>
                    <select class="filter-select" onchange="filterByStatus(this.value)">
                        <option value="">全部状态</option>
                        <option value="active">正常营业</option>
                        <option value="pending">待审核</option>
                        <option value="frozen">已冻结</option>
                        <option value="closed">已关闭</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">商户类型</label>
                    <select class="filter-select" onchange="filterByType(this.value)">
                        <option value="">全部类型</option>
                        <option value="enterprise">企业商户</option>
                        <option value="individual">个人商户</option>
                        <option value="flagship">品牌旗舰店</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">商户等级</label>
                    <select class="filter-select" onchange="filterByLevel(this.value)">
                        <option value="">全部等级</option>
                        <option value="basic">基础商户</option>
                        <option value="standard">标准商户</option>
                        <option value="premium">高级商户</option>
                        <option value="vip">VIP商户</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">注册时间</label>
                    <select class="filter-select" onchange="filterByTime(this.value)">
                        <option value="">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="quarter">本季度</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">搜索</label>
                    <input type="text" class="search-input" placeholder="商户名称/ID/联系人" oninput="searchMerchants(this.value)">
                </div>
                
                <button class="btn btn-secondary" onclick="resetFilters()">
                    <i class="fas fa-refresh"></i> 重置
                </button>
            </div>
        </div>

        <!-- 商户表格 -->
        <div class="merchants-table-container">
            <div class="table-header">
                <h3 class="table-title">商户列表</h3>
                <div class="bulk-actions hidden" id="bulkActions">
                    <span class="bulk-info">已选择 <span id="selectedCount">0</span> 个商户</span>
                    <button class="btn btn-success" onclick="batchApprove()">批量通过</button>
                    <button class="btn btn-warning" onclick="batchFreeze()">批量冻结</button>
                    <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                </div>
            </div>
            
            <table class="merchants-table">
                <thead>
                    <tr>
                        <th>
                            <div class="checkbox-container">
                                <input type="checkbox" class="checkbox" id="selectAll" onchange="toggleSelectAll(this.checked)">
                                <label for="selectAll">全选</label>
                            </div>
                        </th>
                        <th>商户信息</th>
                        <th>商户类型</th>
                        <th>商户等级</th>
                        <th>状态</th>
                        <th>注册时间</th>
                        <th>商品数量</th>
                        <th>月销售额</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="merchantsTableBody">
                    <!-- 商户数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
            
            <div class="pagination">
                <div class="pagination-info">
                    显示第 1-20 条，共 1,248 条记录
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>上一页</button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn">...</button>
                    <button class="pagination-btn">63</button>
                    <button class="pagination-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

</body>
<script src="merchant-list.js"></script>
</html> 
