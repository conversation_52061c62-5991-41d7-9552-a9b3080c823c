<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户权限管理 - 增强版 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: var(--gray-50);
        }

        .permissions-page {
            padding: 24px;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .page-subtitle {
            color: var(--gray-500);
            font-size: 16px;
            margin-top: 4px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 筛选和工具栏 */
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .toolbar-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .toolbar-row:last-child {
            margin-bottom: 0;
        }

        .search-group {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-input {
            width: 280px;
            padding: 10px 14px 10px 40px;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            font-size: 14px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="%23999"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>') no-repeat 12px center;
            background-size: 16px;
        }

        .filter-toggle {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .filter-toggle.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 高级筛选面板 */
        .filter-panel {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            display: none;
        }

        .filter-panel.show {
            display: block;
        }

        .filter-row {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
            align-items: center;
        }

        .filter-row:last-child {
            margin-bottom: 0;
        }

        .filter-label {
            min-width: 80px;
            font-size: 14px;
            color: var(--gray-700);
            font-weight: 500;
        }

        .filter-select {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--gray-300);
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        /* 批量操作工具栏 */
        .bulk-toolbar {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 20px;
            display: none;
            align-items: center;
            gap: 12px;
        }

        .bulk-toolbar.show {
            display: flex;
        }

        .bulk-info {
            font-size: 14px;
            color: #1e40af;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
            margin-left: auto;
        }

        /* 主要布局 */
        .main-layout {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 24px;
        }

        .sidebar-panel {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .panel-header {
            padding: 20px;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .badge {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 商户列表 */
        .merchants-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .merchant-item {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-100);
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .merchant-item:hover {
            background: var(--gray-50);
        }

        .merchant-item.active {
            background: #eff6ff;
            border-left: 3px solid var(--primary-color);
        }

        .merchant-item.selected {
            background: #f0f9ff;
        }

        .merchant-item:last-child {
            border-bottom: none;
        }

        .merchant-name {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 4px;
        }

        .merchant-info {
            font-size: 12px;
            color: var(--gray-500);
        }

        .merchant-status {
            display: inline-flex;
            align-items: center;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
            margin-top: 4px;
        }

        .merchant-status.active {
            background: #dcfce7;
            color: #166534;
        }

        .merchant-status.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .merchant-status.suspended {
            background: #fecaca;
            color: #dc2626;
        }

        /* 商户选择复选框 */
        .merchant-checkbox {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 18px;
            height: 18px;
            opacity: 0;
            cursor: pointer;
        }

        .merchant-item.selectable::before {
            content: '';
            position: absolute;
            top: 14px;
            right: 14px;
            width: 16px;
            height: 16px;
            border: 2px solid var(--gray-300);
            border-radius: 3px;
            background: white;
        }

        .merchant-item.selected::before {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .merchant-item.selected::after {
            content: '✓';
            position: absolute;
            top: 13px;
            right: 16px;
            color: white;
            font-size: 11px;
            font-weight: 600;
        }

        /* 权限内容区域 */
        .permissions-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .content-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .selected-merchant {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .merchant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .merchant-details h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .merchant-details p {
            font-size: 14px;
            color: var(--gray-500);
            margin: 2px 0 0 0;
        }

        /* 权限标签页 */
        .permission-tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid var(--gray-200);
        }

        .tab-button {
            padding: 16px 24px;
            border: none;
            background: none;
            color: var(--gray-500);
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .tab-button.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            padding: 24px;
        }

        /* 权限部分 */
        .permission-section {
            margin-bottom: 32px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .section-toggle {
            position: relative;
            display: inline-flex;
            align-items: center;
            cursor: pointer;
        }

        .toggle-switch {
            width: 48px;
            height: 24px;
            background: var(--gray-200);
            border-radius: 12px;
            position: relative;
            transition: background 0.2s;
        }

        .toggle-switch.active {
            background: var(--primary-color);
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
        }

        .toggle-switch.active::after {
            transform: translateX(24px);
        }

        /* 权限网格 */
        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
        }

        .permission-card {
            border: 1px solid var(--gray-200);
            border-radius: 12px;
            padding: 20px;
            background: white;
            transition: all 0.2s;
        }

        .permission-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .permission-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .permission-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .permission-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .permission-icon.products { background: #10b981; }
        .permission-icon.orders { background: #3b82f6; }
        .permission-icon.customers { background: #8b5cf6; }
        .permission-icon.marketing { background: #f59e0b; }
        .permission-icon.analytics { background: #ef4444; }
        .permission-icon.finance { background: #06b6d4; }
        .permission-icon.settings { background: #6b7280; }

        .permission-name {
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .permission-items {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--gray-100);
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-label {
            font-size: 14px;
            color: var(--gray-700);
            flex: 1;
        }

        .permission-checkbox {
            position: relative;
            width: 20px;
            height: 20px;
            margin: 0;
            cursor: pointer;
        }

        .permission-checkbox::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 20px;
            height: 20px;
            border: 2px solid var(--gray-300);
            border-radius: 4px;
            background: white;
            transition: all 0.2s;
        }

        .permission-checkbox:checked::before {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .permission-checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 2px;
            left: 4px;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        /* 权限依赖关系 */
        .permission-item.dependent {
            background: #fef3c7;
            border-left: 3px solid #f59e0b;
            padding-left: 12px;
            margin-left: -12px;
        }

        .permission-item.blocked {
            background: #fecaca;
            border-left: 3px solid #ef4444;
            padding-left: 12px;
            margin-left: -12px;
            opacity: 0.6;
        }

        .permission-dependency {
            font-size: 11px;
            color: #92400e;
            margin-top: 4px;
        }

        /* 审批状态 */
        .approval-status {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-left: 8px;
        }

        .approval-status.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .approval-status.approved {
            background: #dcfce7;
            color: #166534;
        }

        .approval-status.rejected {
            background: #fecaca;
            color: #dc2626;
        }

        /* 通知系统 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            min-width: 300px;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left: 4px solid var(--success-color);
        }

        .notification.error {
            border-left: 4px solid var(--error-color);
        }

        .notification.warning {
            border-left: 4px solid var(--warning-color);
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .notification-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .notification.success .notification-icon {
            background: var(--success-color);
        }

        .notification.error .notification-icon {
            background: var(--error-color);
        }

        .notification.warning .notification-icon {
            background: var(--warning-color);
        }

        /* 模态框 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--gray-500);
            cursor: pointer;
        }

        /* 底部操作栏 */
        .actions-bar {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 20px 24px;
            border-top: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .changes-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            color: var(--warning-color);
            font-size: 14px;
        }

        .changes-indicator.show {
            display: flex;
        }

        /* 加载状态 */
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid var(--gray-200);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
            }

            .sidebar-panel {
                order: 1;
            }

            .permissions-content {
                order: 0;
            }
        }

        @media (max-width: 768px) {
            .permissions-page {
                padding: 16px;
            }

            .permissions-grid {
                grid-template-columns: 1fr;
            }

            .toolbar-row {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .search-input {
                width: 100%;
            }

            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-label {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="permissions-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title">商户权限管理</h1>
                    <p class="page-subtitle">配置商户功能权限和访问控制 - 增强版</p>
                </div>
                
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="exportPermissions()">
                        <i class="fas fa-download"></i> 导出权限报告
                    </button>
                    <button class="btn btn-secondary" onclick="showCompareModal()">
                        <i class="fas fa-balance-scale"></i> 权限对比
                    </button>
                    <button class="btn btn-primary" onclick="showTemplateModal()">
                        <i class="fas fa-plus"></i> 创建权限模板
                    </button>
                </div>
            </div>
        </div>

        <!-- 工具栏和筛选 -->
        <div class="toolbar">
            <div class="toolbar-row">
                <div class="search-group">
                    <input type="text" class="search-input" placeholder="搜索商户名称、ID或类型..." id="searchInput">
                    <button class="btn btn-secondary filter-toggle" onclick="toggleFilterPanel()">
                        <i class="fas fa-filter"></i> 高级筛选
                    </button>
                    <button class="btn btn-secondary" onclick="toggleBulkMode()">
                        <i class="fas fa-check-square"></i> 批量操作
                    </button>
                </div>
                
                <div class="view-options">
                    <button class="btn btn-secondary" onclick="showApprovalModal()">
                        <i class="fas fa-clock"></i> 审批管理 <span class="badge">3</span>
                    </button>
                </div>
            </div>
            
            <!-- 高级筛选面板 -->
            <div class="filter-panel" id="filterPanel">
                <div class="filter-row">
                    <label class="filter-label">商户类型</label>
                    <select class="filter-select" id="merchantTypeFilter">
                        <option value="">全部类型</option>
                        <option value="enterprise">企业商户</option>
                        <option value="individual">个人商户</option>
                        <option value="brand">品牌商户</option>
                    </select>
                    
                    <label class="filter-label">商户状态</label>
                    <select class="filter-select" id="merchantStatusFilter">
                        <option value="">全部状态</option>
                        <option value="active">正常营业</option>
                        <option value="pending">审核中</option>
                        <option value="suspended">已暂停</option>
                    </select>
                </div>
                
                <div class="filter-row">
                    <label class="filter-label">权限数量</label>
                    <select class="filter-select" id="permissionCountFilter">
                        <option value="">全部权限</option>
                        <option value="basic">基础权限 (1-10)</option>
                        <option value="standard">标准权限 (11-20)</option>
                        <option value="advanced">高级权限 (21-30)</option>
                        <option value="full">完整权限 (30+)</option>
                    </select>
                    
                    <label class="filter-label">加入时间</label>
                    <select class="filter-select" id="joinDateFilter">
                        <option value="">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="quarter">本季度</option>
                    </select>
                </div>
                
                <div class="filter-row">
                    <button class="btn btn-secondary" onclick="resetFilters()">重置筛选</button>
                    <button class="btn btn-primary" onclick="applyFilters()">应用筛选</button>
                </div>
            </div>
        </div>

        <!-- 批量操作工具栏 -->
        <div class="bulk-toolbar" id="bulkToolbar">
            <div class="bulk-info">
                已选择 <span id="selectedCount">0</span> 个商户
            </div>
            <div class="bulk-actions">
                <button class="btn btn-small btn-secondary" onclick="bulkEnablePermissions()">
                    <i class="fas fa-check"></i> 批量启用权限
                </button>
                <button class="btn btn-small btn-secondary" onclick="bulkDisablePermissions()">
                    <i class="fas fa-times"></i> 批量禁用权限
                </button>
                <button class="btn btn-small btn-primary" onclick="bulkApplyTemplate()">
                    <i class="fas fa-magic"></i> 应用模板
                </button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-layout">
            <!-- 商户列表侧栏 -->
            <div class="sidebar-panel">
                <div class="panel-header">
                    <h3 class="panel-title">商户列表</h3>
                    <span class="badge" id="merchantCount">248</span>
                </div>
                
                <div class="merchants-list" id="merchantsList">
                    <!-- 商户列表项将通过 JavaScript 动态生成 -->
                </div>
            </div>
            
            <!-- 权限配置内容 -->
            <div class="permissions-content">
                <!-- 选中商户信息 -->
                <div class="content-header">
                    <div class="selected-merchant" id="selectedMerchantInfo">
                        <div class="merchant-avatar">选</div>
                        <div class="merchant-details">
                            <h3>请选择商户</h3>
                            <p>从左侧列表中选择一个商户来配置权限</p>
                        </div>
                    </div>
                </div>
                
                <!-- 权限标签页 -->
                <div class="permission-tabs">
                    <button class="tab-button active" onclick="switchTab(event, 'functional')">功能权限</button>
                    <button class="tab-button" onclick="switchTab(event, 'template')">权限模板</button>
                    <button class="tab-button" onclick="switchTab(event, 'approval')">审批管理</button>
                    <button class="tab-button" onclick="switchTab(event, 'activity')">操作日志</button>
                </div>
                
                <!-- 功能权限内容 -->
                <div class="tab-content" id="functional-content">
                    <div class="permission-section">
                        <div class="section-header">
                            <h3 class="section-title">权限配置</h3>
                            <button class="btn btn-secondary" onclick="showDependencyInfo()">
                                <i class="fas fa-info-circle"></i> 权限依赖说明
                            </button>
                        </div>
                        
                        <div class="permissions-grid" id="permissionsGrid">
                            <!-- 权限卡片将通过 JavaScript 动态生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 权限模板内容 -->
                <div class="tab-content" id="template-content" style="display: none;">
                    <div class="permission-section">
                        <div class="section-header">
                            <h3 class="section-title">权限模板</h3>
                            <button class="btn btn-primary" onclick="showCreateTemplateModal()">
                                <i class="fas fa-plus"></i> 创建自定义模板
                            </button>
                        </div>
                        
                        <div class="permissions-grid" id="templatesGrid">
                            <!-- 权限模板将通过 JavaScript 动态生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 审批管理内容 -->
                <div class="tab-content" id="approval-content" style="display: none;">
                    <div class="permission-section">
                        <div class="section-header">
                            <h3 class="section-title">待审批权限</h3>
                            <button class="btn btn-primary" onclick="bulkApprove()">
                                <i class="fas fa-check-double"></i> 批量审批
                            </button>
                        </div>
                        
                        <div id="approvalList">
                            <!-- 审批列表将通过 JavaScript 动态生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 操作日志内容 -->
                <div class="tab-content" id="activity-content" style="display: none;">
                    <div class="permission-section">
                        <div class="section-header">
                            <h3 class="section-title">权限变更日志</h3>
                            <button class="btn btn-secondary" onclick="exportLogs()">
                                <i class="fas fa-download"></i> 导出日志
                            </button>
                        </div>
                        
                        <div id="activityLog">
                            <!-- 日志列表将通过 JavaScript 动态生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 底部操作栏 -->
                <div class="actions-bar">
                    <div class="changes-indicator" id="changesIndicator">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>您有未保存的权限变更</span>
                    </div>
                    <div style="flex: 1;"></div>
                    <button class="btn btn-secondary" onclick="resetChanges()">重置</button>
                    <button class="btn btn-primary" onclick="savePermissions()">
                        <span class="save-text">保存权限配置</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知组件 -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <div class="notification-icon">
                <i class="fas fa-check"></i>
            </div>
            <div class="notification-text">
                权限配置已保存
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">标题</h3>
                <button class="modal-close" onclick="hideModal()">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 模态框内容 -->
            </div>
        </div>
    </div>

    <!-- JavaScript 脚本 -->
    <script src="enhanced-permissions.js"></script>
</body>
</html> 