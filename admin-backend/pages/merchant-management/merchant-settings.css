        .settings-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }
        
        .settings-nav {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 32px;
        }
        
        .nav-sidebar {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            height: fit-content;
        }
        
        .nav-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 4px;
            color: #64748b;
        }
        
        .nav-item:hover {
            background: #f1f5f9;
            color: #1e293b;
        }
        
        .nav-item.active {
            background: #3b82f6;
            color: white;
        }
        
        .settings-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .content-header {
            padding: 24px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .content-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 8px 0;
        }
        
        .content-desc {
            color: #64748b;
            font-size: 14px;
        }
        
        .settings-section {
            padding: 24px;
            display: none;
        }
        
        .settings-section.active {
            display: block;
        }
        
        .setting-group {
            margin-bottom: 32px;
            padding-bottom: 32px;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .setting-group:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .group-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #f8fafc;
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-label {
            flex: 1;
        }
        
        .label-title {
            font-size: 14px;
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .label-desc {
            font-size: 12px;
            color: #64748b;
        }
        
        .setting-control {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .form-input, .form-select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .toggle-switch {
            position: relative;
            width: 48px;
            height: 24px;
        }
        
        .toggle-input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #cbd5e1;
            transition: 0.4s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background: white;
            transition: 0.4s;
            border-radius: 50%;
        }
        
        .toggle-input:checked + .toggle-slider {
            background: #3b82f6;
        }
        
        .toggle-input:checked + .toggle-slider:before {
            transform: translateX(24px);
        }
        
        .commission-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }
        
        .commission-table th,
        .commission-table td {
            padding: 12px;
            text-align: left;
            border: 1px solid #e2e8f0;
        }
        
        .commission-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }
        
        .action-buttons {
            padding: 24px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }
        
        .btn {
            padding: 10px 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .btn.success {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }
        
        .category-tree {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .tree-item {
            padding: 8px 12px;
            border-bottom: 1px solid #f8fafc;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tree-checkbox {
            width: 16px;
            height: 16px;
        }
        
        @media (max-width: 768px) {
            .settings-page { padding: 16px; }
            .settings-nav { grid-template-columns: 1fr; gap: 16px; }
            .nav-sidebar { padding: 16px; }
            .content-header { padding: 20px; }
            .settings-section { padding: 20px; }
        }
