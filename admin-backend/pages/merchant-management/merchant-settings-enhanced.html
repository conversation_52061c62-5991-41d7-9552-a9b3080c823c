<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家设置管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* CSS变量定义 */
        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 12px;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* 全局样式 */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, var(--gray-50) 0%, #e8f2ff 100%);
            color: var(--gray-900);
            line-height: 1.6;
        }

        .settings-page {
            padding: 32px;
            min-height: 100vh;
        }
        
        /* 页面头部优化 */
        .page-header {
            background: linear-gradient(135deg, white 0%, #f8fafc 100%);
            padding: 32px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 32px;
            border: 1px solid var(--gray-200);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .page-title {
            font-size: 36px;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 0;
        }

        .page-subtitle {
            color: var(--gray-600);
            font-size: 16px;
            margin-top: 8px;
            font-weight: 500;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        /* 现代化按钮设计 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 2px solid var(--gray-300);
            box-shadow: var(--shadow);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            transform: translateY(-1px);
        }

        /* 主布局优化 */
        .settings-nav {
            display: grid;
            grid-template-columns: 320px 1fr;
            gap: 32px;
            align-items: start;
        }
        
        /* 侧边栏优化 */
        .nav-sidebar {
            background: white;
            border-radius: var(--border-radius);
            padding: 0;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            position: sticky;
            top: 32px;
            overflow: hidden;
        }

        .nav-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: 700;
            margin: 0;
        }

        .nav-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 4px;
        }

        .nav-menu {
            padding: 16px;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 8px;
            color: var(--gray-700);
            font-weight: 500;
            border: 2px solid transparent;
        }
        
        .nav-item:hover {
            background: linear-gradient(135deg, var(--gray-50) 0%, #f0f9ff 100%);
            color: var(--primary-color);
            transform: translateX(4px);
        }
        
        .nav-item.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            border-color: var(--primary-hover);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .nav-item i {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        /* 内容区域优化 */
        .settings-content {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }
        
        .content-header {
            background: linear-gradient(135deg, var(--gray-50) 0%, #f0f9ff 100%);
            padding: 32px;
            border-bottom: 2px solid var(--gray-200);
        }
        
        .content-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0 0 8px 0;
        }
        
        .content-desc {
            color: var(--gray-600);
            font-size: 16px;
            font-weight: 500;
        }
        
        /* 设置区域优化 */
        .settings-section {
            padding: 32px;
            display: none;
        }
        
        .settings-section.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .setting-group {
            margin-bottom: 40px;
            padding: 24px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: var(--border-radius);
            border: 2px solid var(--gray-100);
            transition: all 0.3s ease;
        }

        .setting-group:hover {
            border-color: var(--gray-200);
            box-shadow: var(--shadow);
        }
        
        .group-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .group-title i {
            color: var(--primary-color);
            font-size: 18px;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-bottom: 1px solid var(--gray-100);
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-label {
            flex: 1;
            margin-right: 24px;
        }
        
        .label-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 4px;
        }
        
        .label-desc {
            font-size: 14px;
            color: var(--gray-600);
            line-height: 1.5;
        }
        
        .setting-control {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        /* 表单控件优化 */
        .form-input, .form-select, .form-textarea {
            padding: 12px 16px;
            border: 2px solid var(--gray-300);
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input.error, .form-select.error, .form-textarea.error {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-input.success, .form-select.success, .form-textarea.success {
            border-color: var(--success-color);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        /* 开关按钮优化 */
        .toggle-switch {
            position: relative;
            width: 56px;
            height: 28px;
        }
        
        .toggle-input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gray-300);
            transition: 0.4s;
            border-radius: 28px;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 3px;
            bottom: 3px;
            background: white;
            transition: 0.4s;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .toggle-input:checked + .toggle-slider {
            background: var(--primary-color);
        }
        
        .toggle-input:checked + .toggle-slider:before {
            transform: translateX(28px);
        }

        /* 表格优化 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }
        
        .data-table th,
        .data-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .data-table th {
            background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-50) 100%);
            font-weight: 700;
            color: var(--gray-900);
            font-size: 14px;
        }

        .data-table tbody tr:hover {
            background: var(--gray-50);
        }

        /* 色彩选择器 */
        .color-palette {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .color-option {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            cursor: pointer;
            border: 3px solid transparent;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .color-option:hover {
            transform: scale(1.1);
            border-color: var(--gray-300);
        }

        .color-option.selected {
            border-color: var(--gray-600);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* 权限状态样式 */
        .permission-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .permission-status.enabled {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
        }

        .permission-status.disabled {
            background: var(--gray-200);
            color: var(--gray-600);
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            color: white;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: var(--shadow-lg);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        }

        .notification.error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
        }

        .notification.info {
            background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%);
        }

        /* 移动端响应式优化 */
        @media (max-width: 1024px) {
            .settings-nav {
                grid-template-columns: 280px 1fr;
                gap: 24px;
            }
            
            .settings-page {
                padding: 24px;
            }
        }

        @media (max-width: 768px) {
            .settings-nav {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .nav-sidebar {
                position: static;
                order: 2;
                margin-top: 20px;
            }
            
            .settings-content {
                order: 1;
            }
            
            .settings-page {
                padding: 16px;
            }
            
            .page-header {
                padding: 24px;
            }
            
            .page-title {
                font-size: 28px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: stretch;
            }
            
            .btn {
                flex: 1;
                justify-content: center;
            }
            
            .setting-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
            
            .setting-control {
                width: 100%;
                justify-content: flex-start;
            }
            
            .notification {
                right: 10px;
                left: 10px;
                right: 10px;
            }
        }

        @media (max-width: 480px) {
            .nav-menu {
                padding: 12px;
            }
            
            .nav-item {
                padding: 12px 16px;
                font-size: 14px;
            }
            
            .content-header {
                padding: 24px;
            }
            
            .content-title {
                font-size: 24px;
            }
            
            .settings-section {
                padding: 24px;
            }
            
            .setting-group {
                padding: 20px;
                margin-bottom: 24px;
            }
            
            .group-title {
                font-size: 18px;
            }
            
            .label-title {
                font-size: 15px;
            }
            
            .label-desc {
                font-size: 13px;
            }
        }

        /* 操作按钮区域 */
        .action-buttons {
            padding: 32px;
            border-top: 2px solid var(--gray-200);
            background: linear-gradient(135deg, var(--gray-50) 0%, #f0f9ff 100%);
            display: flex;
            gap: 16px;
            justify-content: flex-end;
            flex-wrap: wrap;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .settings-nav {
                grid-template-columns: 280px 1fr;
                gap: 24px;
            }
        }

        @media (max-width: 768px) {
            .settings-page { 
                padding: 16px; 
            }
            
            .settings-nav { 
                grid-template-columns: 1fr; 
                gap: 16px; 
            }
            
            .nav-sidebar { 
                position: relative;
                top: 0;
            }
            
            .content-header { 
                padding: 24px; 
            }
            
            .settings-section { 
                padding: 24px; 
            }

            .setting-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .setting-label {
                margin-right: 0;
            }
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 24px;
            border-radius: var(--border-radius);
            color: white;
            font-weight: 600;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: var(--danger-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }
    </style>
</head>
<body>
    <div class="settings-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div>
                    <h1 class="page-title">商家设置管理</h1>
                    <p class="page-subtitle">配置商家入驻规则、业务参数和系统设置</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="exportSettings()">
                        <i class="fas fa-download"></i>
                        导出配置
                    </button>
                    <button class="btn btn-secondary" onclick="importSettings()">
                        <i class="fas fa-upload"></i>
                        导入配置
                    </button>
                    <button class="btn btn-primary" onclick="saveAllSettings()">
                        <i class="fas fa-save"></i>
                        保存所有设置
                    </button>
                </div>
            </div>
        </div>

        <div class="settings-nav">
            <!-- 左侧导航 -->
            <div class="nav-sidebar">
                <div class="nav-header">
                    <h3 class="nav-title">设置中心</h3>
                    <p class="nav-subtitle">9个配置模块</p>
                </div>
                <div class="nav-menu">
                    <div class="nav-item active" onclick="showSection('basic-info', this)">
                        <i class="fas fa-store"></i>
                        商家基本信息
                    </div>
                    <div class="nav-item" onclick="showSection('store-decoration', this)">
                        <i class="fas fa-palette"></i>
                        店铺装修设置
                    </div>
                    <div class="nav-item" onclick="showSection('business-hours', this)">
                        <i class="fas fa-clock"></i>
                        营业时间设置
                    </div>
                    <div class="nav-item" onclick="showSection('category-management', this)">
                        <i class="fas fa-tags"></i>
                        经营类目管理
                    </div>
                    <div class="nav-item" onclick="showSection('delivery-settings', this)">
                        <i class="fas fa-truck"></i>
                        配送设置
                    </div>
                    <div class="nav-item" onclick="showSection('commission', this)">
                        <i class="fas fa-percentage"></i>
                        佣金费率配置
                    </div>
                    <div class="nav-item" onclick="showSection('settlement', this)">
                        <i class="fas fa-credit-card"></i>
                        结算设置
                    </div>
                    <div class="nav-item" onclick="showSection('permissions', this)">
                        <i class="fas fa-shield-alt"></i>
                        权限管理
                    </div>
                    <div class="nav-item" onclick="showSection('notifications', this)">
                        <i class="fas fa-bell"></i>
                        通知设置
                    </div>
                </div>
            </div>

            <!-- 右侧内容 -->
            <div class="settings-content">
                <div class="content-header">
                    <h2 class="content-title" id="sectionTitle">商家基本信息设置</h2>
                    <p class="content-desc" id="sectionDesc">配置商家入驻时的基本信息要求和验证规则</p>
                </div>

                <!-- 商家基本信息设置 -->
                <div id="basic-info" class="settings-section active">
                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-building"></i>
                            企业信息要求
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">企业名称验证</div>
                                <div class="label-desc">是否需要验证企业名称的真实性</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">营业执照要求</div>
                                <div class="label-desc">商家必须上传有效的营业执照</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">法人身份证要求</div>
                                <div class="label-desc">是否需要上传法人身份证信息</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">税务登记证要求</div>
                                <div class="label-desc">是否需要上传税务登记证</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-user-tie"></i>
                            联系人信息要求
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">手机号验证</div>
                                <div class="label-desc">联系人手机号必须通过短信验证</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">邮箱验证</div>
                                <div class="label-desc">联系人邮箱必须通过邮件验证</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">座机号码要求</div>
                                <div class="label-desc">是否必须提供座机号码</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-map-marker-alt"></i>
                            地址信息要求
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">详细地址验证</div>
                                <div class="label-desc">验证商家经营地址的真实性</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">GPS定位要求</div>
                                <div class="label-desc">要求提供准确的GPS坐标</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">实地验证</div>
                                <div class="label-desc">是否需要实地验证商家地址</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 店铺装修设置 -->
                <div id="store-decoration" class="settings-section">
                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-paint-brush"></i>
                            店铺模板管理
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">默认模板选择</div>
                                <div class="label-desc">商家可以选择的默认店铺装修模板</div>
                            </div>
                            <div class="setting-control">
                                <select class="form-select" style="width: 200px;">
                                    <option value="modern">现代简约</option>
                                    <option value="classic">经典商务</option>
                                    <option value="colorful">炫彩时尚</option>
                                    <option value="minimal">极简风格</option>
                                </select>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">自定义模板上传</div>
                                <div class="label-desc">允许商家上传自定义装修模板</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">模板预览功能</div>
                                <div class="label-desc">提供模板预览和实时编辑功能</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-palette"></i>
                            品牌元素设置
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">Logo尺寸限制</div>
                                <div class="label-desc">店铺Logo的最大尺寸限制</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="200" min="50" max="500" style="width: 100px;">
                                <span style="margin-left: 8px;">x</span>
                                <input type="number" class="form-input" value="80" min="50" max="200" style="width: 100px;">
                                <span style="margin-left: 8px;">像素</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">横幅图片要求</div>
                                <div class="label-desc">店铺横幅图片的规格要求</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="1200" min="800" max="1920" style="width: 100px;">
                                <span style="margin-left: 8px;">x</span>
                                <input type="number" class="form-input" value="400" min="200" max="600" style="width: 100px;">
                                <span style="margin-left: 8px;">像素</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">主题色彩自定义</div>
                                <div class="label-desc">允许商家自定义店铺主题色彩</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">主题色彩预设</div>
                                <div class="label-desc">提供常用的主题色彩方案</div>
                            </div>
                            <div class="setting-control">
                                <div class="color-palette">
                                    <div class="color-option" style="background: #3b82f6;" title="经典蓝"></div>
                                    <div class="color-option" style="background: #10b981;" title="翠绿色"></div>
                                    <div class="color-option" style="background: #f59e0b;" title="橙黄色"></div>
                                    <div class="color-option" style="background: #ef4444;" title="活力红"></div>
                                    <div class="color-option" style="background: #8b5cf6;" title="紫罗兰"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-mobile-alt"></i>
                            移动端适配
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">响应式设计检查</div>
                                <div class="label-desc">自动检测店铺装修的移动端兼容性</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">移动端预览</div>
                                <div class="label-desc">提供移动端效果实时预览</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 营业时间设置 -->
                <div id="business-hours" class="settings-section">
                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-clock"></i>
                            基础营业时间
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">营业时间要求</div>
                                <div class="label-desc">商家必须设置营业时间</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">24小时营业支持</div>
                                <div class="label-desc">允许商家设置24小时营业</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">分时段营业</div>
                                <div class="label-desc">支持上午、下午分别设置营业时间</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">默认营业时间</div>
                                <div class="label-desc">新商家的默认营业时间设置</div>
                            </div>
                            <div class="setting-control">
                                <input type="time" class="form-input" value="09:00" style="width: 120px;">
                                <span style="margin: 0 8px;">至</span>
                                <input type="time" class="form-input" value="22:00" style="width: 120px;">
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-calendar-alt"></i>
                            特殊时间管理
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">节假日设置</div>
                                <div class="label-desc">支持单独设置节假日营业时间</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">临时闭店功能</div>
                                <div class="label-desc">商家可设置临时闭店时间</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">提前通知时间</div>
                                <div class="label-desc">营业时间变更提前通知客户的时间</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="24" min="1" max="168" style="width: 100px;">
                                <span style="margin-left: 8px;">小时</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 经营类目管理 -->
                <div id="category-management" class="settings-section">
                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-list-ul"></i>
                            类目结构管理
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">最大类目层级</div>
                                <div class="label-desc">商家可创建的商品类目最大层级数</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="3" min="2" max="5" style="width: 100px;">
                                <span style="margin-left: 8px;">级</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">类目数量限制</div>
                                <div class="label-desc">单个商家最多可创建的类目数量</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="50" min="10" max="200" style="width: 100px;">
                                <span style="margin-left: 8px;">个</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">类目审核要求</div>
                                <div class="label-desc">新增类目需要平台审核</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">类目关联商品限制</div>
                                <div class="label-desc">每个类目下最多可关联的商品数量</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="1000" min="100" max="10000" style="width: 120px;">
                                <span style="margin-left: 8px;">个</span>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-shield-alt"></i>
                            类目权限控制
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">禁用类目管理</div>
                                <div class="label-desc">平台可禁用特定商家的类目管理权限</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">类目资质要求</div>
                                <div class="label-desc">特殊类目需要相关资质证明</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">批量操作权限</div>
                                <div class="label-desc">允许商家批量编辑类目信息</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-tags"></i>
                            类目标签管理
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">标签自动生成</div>
                                <div class="label-desc">根据商品信息自动生成类目标签</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">热门标签推荐</div>
                                <div class="label-desc">向商家推荐热门类目标签</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配送设置 -->
                <div id="delivery-settings" class="settings-section">
                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-shipping-fast"></i>
                            配送方式管理
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">自配送服务</div>
                                <div class="label-desc">商家可以使用自己的配送团队</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">第三方配送</div>
                                <div class="label-desc">接入外部配送服务商</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">到店自提</div>
                                <div class="label-desc">客户可选择到店自提商品</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-map-marked-alt"></i>
                            配送区域设置
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">配送范围限制</div>
                                <div class="label-desc">商家可设置配送服务范围</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">默认配送半径</div>
                                <div class="label-desc">新商家的默认配送范围</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="10" min="1" max="100" style="width: 100px;">
                                <span style="margin-left: 8px;">公里</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">跨区域配送费</div>
                                <div class="label-desc">超出配送范围的额外费用设置</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-clock"></i>
                            时效管理
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">标准配送时效</div>
                                <div class="label-desc">普通商品的配送时效要求</div>
                            </div>
                            <div class="setting-control">
                                <select class="form-select" style="width: 150px;">
                                    <option value="30">30分钟</option>
                                    <option value="60" selected>1小时</option>
                                    <option value="120">2小时</option>
                                    <option value="1440">当日达</option>
                                </select>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">特殊商品时效</div>
                                <div class="label-desc">生鲜等特殊商品的配送时效</div>
                            </div>
                            <div class="setting-control">
                                <select class="form-select" style="width: 150px;">
                                    <option value="15">15分钟</option>
                                    <option value="30" selected>30分钟</option>
                                    <option value="60">1小时</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 佣金费率配置 -->
                <div id="commission" class="settings-section">
                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-calculator"></i>
                            基础费率设置
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">平台基础佣金率</div>
                                <div class="label-desc">所有商家的基础佣金比例</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="5.0" min="1" max="15" step="0.1" style="width: 100px;">
                                <span style="margin-left: 8px;">%</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">分类目费率</div>
                                <div class="label-desc">不同商品类目使用不同佣金率</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">阶梯式费率</div>
                                <div class="label-desc">根据销售额采用阶梯式佣金费率</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-gift"></i>
                            优惠政策
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">新商家优惠期</div>
                                <div class="label-desc">新入驻商家享受优惠佣金费率</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">优惠期费率</div>
                                <div class="label-desc">新商家优惠期间的佣金费率</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="2.0" min="0" max="10" step="0.1" style="width: 100px;">
                                <span style="margin-left: 8px;">%</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">优惠期时长</div>
                                <div class="label-desc">新商家优惠政策持续时间</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="90" min="30" max="365" style="width: 100px;">
                                <span style="margin-left: 8px;">天</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">VIP商家费率</div>
                                <div class="label-desc">VIP等级商家的优惠佣金费率</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="3.5" min="1" max="8" step="0.1" style="width: 100px;">
                                <span style="margin-left: 8px;">%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结算设置 -->
                <div id="settlement" class="settings-section">
                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-calendar-check"></i>
                            结算周期管理
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">默认结算周期</div>
                                <div class="label-desc">平台默认的资金结算周期</div>
                            </div>
                            <div class="setting-control">
                                <select class="form-select" style="width: 150px;">
                                    <option value="7" selected>每周结算</option>
                                    <option value="15">半月结算</option>
                                    <option value="30">月度结算</option>
                                    <option value="1">日结算</option>
                                </select>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">最小结算金额</div>
                                <div class="label-desc">触发结算的最小金额门槛</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="100" min="10" max="1000" style="width: 120px;">
                                <span style="margin-left: 8px;">元</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">自动结算功能</div>
                                <div class="label-desc">达到条件时自动进行资金结算</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-university"></i>
                            结算方式配置
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">银行转账</div>
                                <div class="label-desc">支持银行卡转账结算</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">支付宝结算</div>
                                <div class="label-desc">支持支付宝账户结算</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">微信结算</div>
                                <div class="label-desc">支持微信钱包结算</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="3.0" min="0" max="30" step="0.1" style="width: 100px;">
                                <span style="margin-left: 8px;">%</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">分类目费率</div>
                                <div class="label-desc">不同商品类目使用不同佣金率</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">阶梯式费率</div>
                                <div class="label-desc">根据销售额设置不同费率档次</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-award"></i>
                            优惠政策
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">新商家优惠期</div>
                                <div class="label-desc">新入驻商家的费率优惠期限</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="90" min="0" max="365" style="width: 100px;">
                                <span style="margin-left: 8px;">天</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">优惠期费率</div>
                                <div class="label-desc">优惠期内的特殊佣金率</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="1.5" min="0" max="10" step="0.1" style="width: 100px;">
                                <span style="margin-left: 8px;">%</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">VIP商家费率</div>
                                <div class="label-desc">高等级商家享受的费率优惠</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结算设置 -->
                <div id="settlement" class="settings-section">
                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-money-check-alt"></i>
                            结算周期管理
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">默认结算周期</div>
                                <div class="label-desc">新商家的默认资金结算周期</div>
                            </div>
                            <div class="setting-control">
                                <select class="form-select" style="width: 150px;">
                                    <option value="daily">日结</option>
                                    <option value="weekly" selected>周结</option>
                                    <option value="monthly">月结</option>
                                </select>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">最小结算金额</div>
                                <div class="label-desc">触发结算的最低金额门槛</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="100" min="1" max="10000" style="width: 120px;">
                                <span style="margin-left: 8px;">元</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">自动结算功能</div>
                                <div class="label-desc">系统自动处理商家资金结算</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-university"></i>
                            结算方式配置
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">银行转账</div>
                                <div class="label-desc">支持银行卡转账结算</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">支付宝结算</div>
                                <div class="label-desc">支持支付宝账户结算</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">微信结算</div>
                                <div class="label-desc">支持微信账户结算</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-file-invoice-dollar"></i>
                            手续费设置
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">银行转账手续费</div>
                                <div class="label-desc">银行转账方式的手续费率</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="0.1" min="0" max="5" step="0.01" style="width: 100px;">
                                <span style="margin-left: 8px;">%</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">电子钱包手续费</div>
                                <div class="label-desc">支付宝/微信结算的手续费率</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="form-input" value="0.05" min="0" max="3" step="0.01" style="width: 100px;">
                                <span style="margin-left: 8px;">%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 权限管理 -->
                <div id="permissions" class="settings-section">
                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-users-cog"></i>
                            商家角色权限
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">店主权限</div>
                                <div class="label-desc">店铺所有者的完整管理权限</div>
                            </div>
                            <div class="setting-control">
                                <span class="permission-status enabled">已启用</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">管理员权限</div>
                                <div class="label-desc">店铺管理员的部分管理权限</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">客服权限</div>
                                <div class="label-desc">客服人员的基础操作权限</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-cogs"></i>
                            功能模块权限
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">商品管理权限</div>
                                <div class="label-desc">商品添加、编辑、删除权限</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">订单管理权限</div>
                                <div class="label-desc">订单处理、发货、退款权限</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">财务管理权限</div>
                                <div class="label-desc">资金提现、对账、报表权限</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">营销管理权限</div>
                                <div class="label-desc">优惠券、活动、推广权限</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-database"></i>
                            数据访问权限
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">客户数据访问</div>
                                <div class="label-desc">查看和导出客户信息的权限</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">销售数据权限</div>
                                <div class="label-desc">查看详细销售数据和报表</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 通知设置 -->
                <div id="notifications" class="settings-section">
                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-envelope"></i>
                            系统通知管理
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">订单通知</div>
                                <div class="label-desc">新订单、订单状态变更通知</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">库存预警通知</div>
                                <div class="label-desc">商品库存不足时的提醒通知</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">财务通知</div>
                                <div class="label-desc">结算、提现等财务操作通知</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">系统维护通知</div>
                                <div class="label-desc">平台维护、升级等重要通知</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-mobile-alt"></i>
                            推送渠道配置
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">短信通知</div>
                                <div class="label-desc">重要事件通过短信通知商家</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">邮件通知</div>
                                <div class="label-desc">通过邮件发送详细通知信息</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">微信通知</div>
                                <div class="label-desc">通过微信公众号推送通知</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">App推送</div>
                                <div class="label-desc">通过移动端App推送通知</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-clock"></i>
                            通知频率控制
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">免打扰时间</div>
                                <div class="label-desc">设置不发送通知的时间段</div>
                            </div>
                            <div class="setting-control">
                                <input type="time" class="form-input" value="22:00" style="width: 120px;">
                                <span style="margin: 0 8px;">至</span>
                                <input type="time" class="form-input" value="08:00" style="width: 120px;">
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">通知合并</div>
                                <div class="label-desc">将同类型通知合并发送以减少打扰</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">合并时间间隔</div>
                                <div class="label-desc">同类型通知的合并发送间隔</div>
                            </div>
                            <div class="setting-control">
                                <select class="form-select" style="width: 150px;">
                                    <option value="15">15分钟</option>
                                    <option value="30" selected>30分钟</option>
                                    <option value="60">1小时</option>
                                    <option value="120">2小时</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-mobile-alt"></i>
                            推送渠道配置
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">短信通知</div>
                                <div class="label-desc">重要事件通过短信推送</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">邮件通知</div>
                                <div class="label-desc">详细信息通过邮件发送</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">微信通知</div>
                                <div class="label-desc">通过微信服务号推送消息</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">App推送</div>
                                <div class="label-desc">移动应用内推送通知</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3 class="group-title">
                            <i class="fas fa-clock"></i>
                            通知频率控制
                        </h3>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">免打扰时间</div>
                                <div class="label-desc">设置不发送通知的时间段</div>
                            </div>
                            <div class="setting-control">
                                <select class="form-select" style="width: 180px;">
                                    <option value="none">无限制</option>
                                    <option value="22-08" selected>22:00-08:00</option>
                                    <option value="23-07">23:00-07:00</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">通知合并</div>
                                <div class="label-desc">相同类型通知在指定时间内合并发送</div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" class="toggle-input" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="setting-item">
                            <div class="setting-label">
                                <div class="label-title">合并时间间隔</div>
                                <div class="label-desc">通知合并的时间窗口</div>
                            </div>
                            <div class="setting-control">
                                <select class="form-select" style="width: 120px;">
                                    <option value="5">5分钟</option>
                                    <option value="15" selected>15分钟</option>
                                    <option value="30">30分钟</option>
                                    <option value="60">1小时</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSettings();
        });

        // 显示指定设置区域
        function showSection(sectionId, navItem) {
            // 隐藏所有设置区域
            const sections = document.querySelectorAll('.settings-section');
            sections.forEach(section => section.classList.remove('active'));
            
            // 显示选中的设置区域
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }
            
            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            navItem.classList.add('active');
            
            // 更新标题和描述
            updateSectionHeader(sectionId);
        }

        // 更新区域标题和描述
        function updateSectionHeader(sectionId) {
            const titles = {
                'basic-info': '商家基本信息设置',
                'store-decoration': '店铺装修设置',
                'business-hours': '营业时间设置',
                'category-management': '经营类目管理',
                'delivery-settings': '配送设置',
                'commission': '佣金费率配置',
                'settlement': '结算设置',
                'permissions': '权限管理',
                'notifications': '通知设置'
            };
            
            const descriptions = {
                'basic-info': '配置商家入驻时的基本信息要求和验证规则',
                'store-decoration': '设置店铺装修模板和自定义选项',
                'business-hours': '配置商家营业时间和特殊时间段',
                'category-management': '管理商家可经营的商品类目',
                'delivery-settings': '配置配送方式、区域和费用',
                'commission': '设置平台佣金规则和费率标准',
                'settlement': '配置商家结算周期和费用规则',
                'permissions': '管理商家权限和功能开关',
                'notifications': '配置系统通知和消息推送'
            };
            
            document.getElementById('sectionTitle').textContent = titles[sectionId] || '未知设置';
            document.getElementById('sectionDesc').textContent = descriptions[sectionId] || '暂无描述';
        }

        // 初始化设置
        function initializeSettings() {
            console.log('商家设置管理系统已初始化');
            
            // 绑定表单验证
            bindFormValidation();
            
            // 初始化开关状态
            initializeToggles();
            
            // 初始化色彩选择器
            initializeColorPalette();
        }

        // 绑定表单验证
        function bindFormValidation() {
            const inputs = document.querySelectorAll('.form-input, .form-select, .form-textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', validateField);
                input.addEventListener('input', clearValidation);
            });
        }

        // 验证字段
        function validateField(event) {
            const field = event.target;
            const value = field.value.trim();
            
            // 移除之前的验证状态
            field.classList.remove('error', 'success');
            
            // 验证规则
            if (field.hasAttribute('required') && !value) {
                showFieldError(field, '此字段为必填项');
                return false;
            }
            
            if (field.type === 'email' && value && !isValidEmail(value)) {
                showFieldError(field, '邮箱格式不正确');
                return false;
            }
            
            if (field.type === 'number' && value) {
                const min = parseFloat(field.getAttribute('min'));
                const max = parseFloat(field.getAttribute('max'));
                const numValue = parseFloat(value);
                
                if (!isNaN(min) && numValue < min) {
                    showFieldError(field, `值不能小于 ${min}`);
                    return false;
                }
                
                if (!isNaN(max) && numValue > max) {
                    showFieldError(field, `值不能大于 ${max}`);
                    return false;
                }
            }
            
            // 验证通过
            showFieldSuccess(field);
            return true;
        }

        // 清除验证状态
        function clearValidation(event) {
            const field = event.target;
            field.classList.remove('error', 'success');
            
            // 移除错误消息
            const errorMsg = field.parentNode.querySelector('.error-message');
            if (errorMsg) {
                errorMsg.remove();
            }
        }

        // 显示字段错误
        function showFieldError(field, message) {
            field.classList.add('error');
            
            // 移除之前的错误消息
            const existingError = field.parentNode.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }
            
            // 添加错误消息
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.style.color = 'var(--danger-color)';
            errorDiv.style.fontSize = '12px';
            errorDiv.style.marginTop = '4px';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        }

        // 显示字段成功
        function showFieldSuccess(field) {
            field.classList.add('success');
        }

        // 验证邮箱格式
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // 初始化开关状态
        function initializeToggles() {
            const toggles = document.querySelectorAll('.toggle-input');
            toggles.forEach(toggle => {
                toggle.addEventListener('change', function() {
                    const setting = this.closest('.setting-item');
                    const label = setting.querySelector('.label-title').textContent;
                    console.log(`${label}: ${this.checked ? '开启' : '关闭'}`);
                });
            });
        }

        // 初始化色彩选择器
        function initializeColorPalette() {
            const colorOptions = document.querySelectorAll('.color-option');
            colorOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // 移除其他选项的selected状态
                    colorOptions.forEach(opt => opt.classList.remove('selected'));
                    // 为当前选项添加selected状态
                    this.classList.add('selected');
                    
                    const color = this.style.backgroundColor;
                    console.log(`选择主题色彩: ${color}`);
                    showNotification(`主题色彩已设置为 ${this.title}`, 'success');
                });
            });
            
            // 默认选择第一个色彩
            if (colorOptions.length > 0) {
                colorOptions[0].classList.add('selected');
            }
        }

        // 保存所有设置
        function saveAllSettings() {
            const saveButton = event.target;
            const originalText = saveButton.innerHTML;
            
            // 显示加载状态
            saveButton.innerHTML = '<span class="loading"></span> 保存中...';
            saveButton.disabled = true;
            
            // 模拟保存过程
            setTimeout(() => {
                saveButton.innerHTML = originalText;
                saveButton.disabled = false;
                showNotification('所有设置已成功保存', 'success');
            }, 2000);
        }

        // 导出设置
        function exportSettings() {
            const settings = collectAllSettings();
            const dataStr = JSON.stringify(settings, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'merchant-settings-' + new Date().toISOString().split('T')[0] + '.json';
            link.click();
            
            showNotification('设置配置已导出', 'success');
        }

        // 导入设置
        function importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const settings = JSON.parse(e.target.result);
                            applySettings(settings);
                            showNotification('设置配置已导入', 'success');
                        } catch (error) {
                            showNotification('导入失败：文件格式错误', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 收集所有设置
        function collectAllSettings() {
            const settings = {};
            
            // 收集开关设置
            const toggles = document.querySelectorAll('.toggle-input');
            toggles.forEach(toggle => {
                const settingItem = toggle.closest('.setting-item');
                const label = settingItem.querySelector('.label-title').textContent;
                settings[label] = toggle.checked;
            });
            
            // 收集输入框设置
            const inputs = document.querySelectorAll('.form-input, .form-select, .form-textarea');
            inputs.forEach(input => {
                if (input.id) {
                    settings[input.id] = input.value;
                }
            });
            
            return settings;
        }

        // 应用设置
        function applySettings(settings) {
            // 应用开关设置
            const toggles = document.querySelectorAll('.toggle-input');
            toggles.forEach(toggle => {
                const settingItem = toggle.closest('.setting-item');
                const label = settingItem.querySelector('.label-title').textContent;
                if (settings.hasOwnProperty(label)) {
                    toggle.checked = settings[label];
                }
            });
            
            // 应用输入框设置
            Object.keys(settings).forEach(key => {
                const input = document.getElementById(key);
                if (input) {
                    input.value = settings[key];
                }
            });
        }

        // 显示通知
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                ${message}
            `;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => notification.classList.add('show'), 100);
            
            // 自动隐藏
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    </script>
</body>
</html> 