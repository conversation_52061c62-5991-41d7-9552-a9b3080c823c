<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>砍价管理 - TeleShop Admin</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background: #f8fafc;
            color: #1e293b;
        }

        .bargain-page {
            padding: 24px;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 24px;
            color: white;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left h1 {
            margin: 0 0 8px 0;
            font-size: 28px;
            font-weight: 600;
        }

        .header-left p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #ef4444;
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-card:nth-child(1) { border-left-color: #ef4444; }
        .stat-card:nth-child(2) { border-left-color: #f59e0b; }
        .stat-card:nth-child(3) { border-left-color: #10b981; }
        .stat-card:nth-child(4) { border-left-color: #3b82f6; }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .stat-title {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }

        .stat-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .stat-change {
            font-size: 14px;
            color: #10b981;
        }

        /* 内容区域 */
        .content-area {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .content-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            background: #f8fafc;
        }

        .content-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .search-input {
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            min-width: 300px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.15s;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-icon {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            color: #64748b;
            transition: all 0.15s;
        }

        .btn-icon:hover {
            background: #f1f5f9;
            color: #1e293b;
        }

        /* 砍价活动列表 */
        .bargain-list {
            padding: 24px;
        }

        .bargain-item {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.15s;
        }

        .bargain-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .item-header {
            display: flex;
            align-items: center;
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .item-checkbox {
            margin-right: 16px;
        }

        .item-info {
            display: flex;
            align-items: center;
            flex: 1;
            gap: 16px;
        }

        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
        }

        .product-details h3 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .product-details p {
            margin: 0;
            font-size: 14px;
            color: #64748b;
        }

        .item-status {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-completed {
            background: #dbeafe;
            color: #1e40af;
        }

        .item-actions-quick {
            display: flex;
            gap: 4px;
        }

        .item-content {
            padding: 20px;
        }

        .progress-section {
            margin-bottom: 20px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-info {
            font-size: 14px;
            color: #64748b;
        }

        .progress-percentage {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .progress-bar {
            height: 8px;
            background: #f1f5f9;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ef4444, #f59e0b);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .detail-item {
            text-align: center;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .detail-label {
            display: block;
            font-size: 12px;
            color: #64748b;
            margin-bottom: 4px;
        }

        .detail-value {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .price {
            color: #ef4444;
            font-size: 18px;
        }

        .participants-section {
            margin-bottom: 20px;
        }

        .participants-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .participants-title {
            font-size: 14px;
            font-weight: 500;
            color: #1e293b;
        }

        .participants-count {
            font-size: 12px;
            color: #64748b;
        }

        .participants-list {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .participant-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .more-participants {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
        }

        .item-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <div class="bargain-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title">砍价管理</h1>
                    <p class="page-subtitle">管理砍价活动，监控砍价进度和用户参与数据</p>
                </div>
                
                <div class="header-actions">
                    <button class="btn btn-secondary" data-action="refresh">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button class="btn btn-secondary" data-action="batch-export">
                        <i class="fas fa-download"></i> 导出数据
                    </button>
                    <button class="btn btn-danger" data-action="batch-delete" disabled>
                        <i class="fas fa-trash"></i> 批量删除
                    </button>
                    <button class="btn btn-primary" data-action="create">
                        <i class="fas fa-plus"></i> 创建砍价
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">活动总数</span>
                    <div class="stat-icon" style="background: #ef4444;">
                        <i class="fas fa-cut"></i>
                    </div>
                </div>
                <div class="stat-value" id="totalActivities">156</div>
                <div class="stat-change">较上月 +12.5%</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">参与人次</span>
                    <div class="stat-icon" style="background: #f59e0b;">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value" id="totalParticipants">18,642</div>
                <div class="stat-change">较上月 +28.3%</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">成功砍价</span>
                    <div class="stat-icon" style="background: #10b981;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-value" id="successfulBargains">1,245</div>
                <div class="stat-change">较上月 +15.8%</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">转化收入</span>
                    <div class="stat-icon" style="background: #3b82f6;">
                        <i class="fas fa-yen-sign"></i>
                    </div>
                </div>
                <div class="stat-value" id="totalRevenue">¥423,789</div>
                <div class="stat-change">较上月 +22.1%</div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-area">
            <div class="content-header">
                <div class="content-controls">
                    <div>
                        <input type="text" class="search-input" placeholder="搜索砍价活动..." id="searchInput">
                    </div>
                    <div style="display: flex; gap: 12px; align-items: center;">
                        <button class="btn btn-secondary" data-action="advanced-search">
                            <i class="fas fa-filter"></i> 高级筛选
                        </button>
                        <button class="btn btn-secondary" data-action="sort">
                            <i class="fas fa-sort"></i> 排序
                        </button>
                        <button class="btn btn-secondary" data-action="view-mode">
                            <i class="fas fa-list"></i> 视图
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 砍价活动列表 -->
            <div class="bargain-list">
                <!-- 砍价活动项目 1 -->
                <div class="bargain-item" data-id="BG001234">
                    <div class="item-header">
                        <div class="item-checkbox">
                            <input type="checkbox" class="item-select" data-id="BG001234">
                        </div>
                        <div class="item-info">
                            <img src="https://via.placeholder.com/60x60" alt="商品图片" class="product-image">
                            <div class="product-details">
                                <h3>苹果iPhone 15 Pro Max 256GB</h3>
                                <p>苹果专营店 • 活动ID: BG001234</p>
                            </div>
                        </div>
                        
                        <div class="item-status">
                            <span class="status-badge active">进行中</span>
                            <div class="item-actions-quick">
                                <button class="btn-icon" title="查看详情" data-action="view-detail" data-id="BG001234">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-icon" title="查看参与者" data-action="view-participants" data-id="BG001234">
                                    <i class="fas fa-users"></i>
                                </button>
                                <button class="btn-icon" title="更多操作" data-action="more-actions" data-id="BG001234">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="item-content">
                        <div class="progress-section">
                            <div class="progress-header">
                                <span class="progress-info">砍价进度: ¥8,999 → ¥7,299 (已砍¥1,700)</span>
                                <span class="progress-percentage">68%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 68%"></div>
                            </div>
                        </div>
                        
                        <div class="details-grid">
                            <div class="detail-item">
                                <span class="detail-label">原价</span>
                                <span class="detail-value price">¥8,999</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">底价</span>
                                <span class="detail-value price">¥7,299</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">开始时间</span>
                                <span class="detail-value">12-20 10:00</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">结束时间</span>
                                <span class="detail-value">12-22 23:59</span>
                            </div>
                        </div>
                        
                        <div class="participants-section">
                            <div class="participants-header">
                                <span class="participants-title">参与砍价</span>
                                <span class="participants-count">156人已参与</span>
                            </div>
                            <div class="participants-list">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <div class="more-participants">+151</div>
                            </div>
                        </div>
                        
                        <div class="item-actions">
                            <button class="btn btn-secondary" data-action="view-detail" data-id="BG001234">
                                <i class="fas fa-eye"></i> 查看详情
                            </button>
                            <button class="btn btn-primary" data-action="edit" data-id="BG001234">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-success" data-action="promote" data-id="BG001234">
                                <i class="fas fa-share"></i> 推广
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 砍价活动项目 2 -->
                <div class="bargain-item" data-id="BG001235">
                    <div class="item-header">
                        <div class="item-checkbox">
                            <input type="checkbox" class="item-select" data-id="BG001235">
                        </div>
                        <div class="item-info">
                            <img src="https://via.placeholder.com/60x60" alt="商品图片" class="product-image">
                            <div class="product-details">
                                <h3>小米14 Ultra 16GB+1TB</h3>
                                <p>小米官方旗舰店 • 活动ID: BG001235</p>
                            </div>
                        </div>
                        
                        <div class="item-status">
                            <span class="status-badge completed">已完成</span>
                            <div class="item-actions-quick">
                                <button class="btn-icon" title="查看报告" data-action="view-report" data-id="BG001235">
                                    <i class="fas fa-chart-bar"></i>
                                </button>
                                <button class="btn-icon" title="查看参与者" data-action="view-participants" data-id="BG001235">
                                    <i class="fas fa-users"></i>
                                </button>
                                <button class="btn-icon" title="更多操作" data-action="more-actions" data-id="BG001235">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="item-content">
                        <div class="progress-section">
                            <div class="progress-header">
                                <span class="progress-info">砍价完成: ¥5,999 → ¥4,899 (已砍¥1,100)</span>
                                <span class="progress-percentage">100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%"></div>
                            </div>
                        </div>
                        
                        <div class="details-grid">
                            <div class="detail-item">
                                <span class="detail-label">原价</span>
                                <span class="detail-value price">¥5,999</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">成交价</span>
                                <span class="detail-value price">¥4,899</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">完成时间</span>
                                <span class="detail-value">12-19 15:30</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">销售额</span>
                                <span class="detail-value">¥4,899</span>
                            </div>
                        </div>
                        
                        <div class="participants-section">
                            <div class="participants-header">
                                <span class="participants-title">参与砍价</span>
                                <span class="participants-count">89人参与</span>
                            </div>
                            <div class="participants-list">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <img src="https://via.placeholder.com/32x32" alt="用户头像" class="participant-avatar">
                                <div class="more-participants">+84</div>
                            </div>
                        </div>
                        
                        <div class="item-actions">
                            <button class="btn btn-secondary" data-action="view-report" data-id="BG001235">
                                <i class="fas fa-chart-bar"></i> 查看报告
                            </button>
                            <button class="btn btn-primary" data-action="copy" data-id="BG001235">
                                <i class="fas fa-copy"></i> 复制活动
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 砍价活动项目 3 -->
                <div class="bargain-item" data-id="BG001236">
                    <div class="item-header">
                        <div class="item-checkbox">
                            <input type="checkbox" class="item-select" data-id="BG001236">
                        </div>
                        <div class="item-info">
                            <img src="https://via.placeholder.com/60x60" alt="商品图片" class="product-image">
                            <div class="product-details">
                                <h3>华为Mate 60 Pro 12GB+512GB</h3>
                                <p>华为官方商城 • 活动ID: BG001236</p>
                            </div>
                        </div>
                        
                        <div class="item-status">
                            <span class="status-badge pending">待开始</span>
                            <div class="item-actions-quick">
                                <button class="btn-icon" title="启动砍价" data-action="start" data-id="BG001236">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="btn-icon" title="编辑活动" data-action="edit" data-id="BG001236">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon" title="更多操作" data-action="more-actions" data-id="BG001236">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="item-content">
                        <div class="progress-section">
                            <div class="progress-header">
                                <span class="progress-info">砍价设置: ¥6,999 → ¥5,799 (目标砍价¥1,200)</span>
                                <span class="progress-percentage">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div class="details-grid">
                            <div class="detail-item">
                                <span class="detail-label">原价</span>
                                <span class="detail-value price">¥6,999</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">底价</span>
                                <span class="detail-value price">¥5,799</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">计划开始</span>
                                <span class="detail-value">12-23 09:00</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">活动时长</span>
                                <span class="detail-value">72小时</span>
                            </div>
                        </div>
                        
                        <div class="participants-section">
                            <div class="participants-header">
                                <span class="participants-title">参与砍价</span>
                                <span class="participants-count">0人参与</span>
                            </div>
                            <div class="participants-list">
                                <div style="color: #64748b; font-size: 14px; font-style: italic;">活动尚未开始</div>
                            </div>
                        </div>
                        
                        <div class="item-actions">
                            <button class="btn btn-secondary" data-action="view-detail" data-id="BG001236">
                                <i class="fas fa-eye"></i> 查看详情
                            </button>
                            <button class="btn btn-primary" data-action="edit" data-id="BG001236">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-success" data-action="start" data-id="BG001236">
                                <i class="fas fa-play"></i> 启动
                            </button>
                            <button class="btn btn-danger" data-action="delete" data-id="BG001236">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建砍价模态框 -->
    <div class="modal-overlay" id="createModal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3 class="modal-title">创建砍价活动</h3>
                <button class="modal-close" onclick="BargainManager.hideModal('createModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <form id="createBargainForm">
                    <div class="form-group">
                        <label class="form-label">活动名称 *</label>
                        <input type="text" id="activityName" class="form-input" placeholder="请输入砍价活动名称" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">所属商户 *</label>
                            <select id="merchant" class="form-select" required>
                                <option value="">请选择商户</option>
                                <option value="merchant1">苹果专营店</option>
                                <option value="merchant2">小米官方旗舰店</option>
                                <option value="merchant3">华为官方商城</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">商品分类 *</label>
                            <select id="category" class="form-select" required>
                                <option value="">请选择分类</option>
                                <option value="electronics">数码电子</option>
                                <option value="clothing">服装鞋帽</option>
                                <option value="home">家居用品</option>
                                <option value="food">食品饮料</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">商品原价 *</label>
                            <input type="number" id="originalPrice" class="form-input" placeholder="0.00" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">砍价底价 *</label>
                            <input type="number" id="floorPrice" class="form-input" placeholder="0.00" step="0.01" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">活动开始时间 *</label>
                            <input type="datetime-local" id="startTime" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">活动结束时间 *</label>
                            <input type="datetime-local" id="endTime" class="form-input" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">活动描述</label>
                        <textarea id="description" class="form-textarea" rows="3" placeholder="请输入活动描述..."></textarea>
                    </div>
                </form>
            </div>
            
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="BargainManager.hideModal('createModal')">取消</button>
                <button class="btn btn-primary" onclick="BargainManager.createBargain()">创建砍价</button>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal-overlay" id="detailModal">
        <div class="modal-content" style="max-width: 1000px;">
            <div class="modal-header">
                <h3 class="modal-title">砍价活动详情</h3>
                <button class="modal-close" onclick="BargainManager.hideModal('detailModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body" id="detailContent">
                <!-- 详情内容将通过JavaScript动态填充 -->
            </div>
            
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="BargainManager.hideModal('detailModal')">关闭</button>
                <button class="btn btn-primary" onclick="BargainManager.editFromDetail()">编辑活动</button>
            </div>
        </div>
    </div>

    <!-- Toast消息提示 -->
    <div id="toastContainer" style="position: fixed; top: 20px; right: 20px; z-index: 10000;"></div>

    <script>
        // 砍价管理器
        const BargainManager = {
            selectedItems: new Set(),
            
            // 初始化
            init() {
                this.bindEvents();
                this.loadBargainData();
                this.showToast('砍价管理页面加载完成！', 'success');
                console.log('砍价管理系统初始化完成');
            },
            
            // 加载砍价数据
            loadBargainData() {
                console.log('加载砍价数据');
                // 这里可以调用API或使用模拟数据
                this.renderBargainStats();
            },
            
            // 渲染砍价统计数据
            renderBargainStats() {
                // 如果需要从API获取，这里可以异步更新
                console.log('渲染砍价统计数据');
            },

            // 初始化高级搜索
            initAdvancedSearch() {
                console.log('初始化高级搜索功能');
                // 高级搜索逻辑实现
            },

            // 初始化批量操作
            initBatchOperations() {
                console.log('初始化批量操作功能');
                // 批量操作逻辑实现
            },

            // 绑定事件
            bindEvents() {
                this.initAdvancedSearch();
                this.initBatchOperations();
                
                // 按钮点击事件
                document.addEventListener('click', (e) => {
                    const btn = e.target.closest('[data-action]');
                    if (!btn) return;
                    
                    e.preventDefault();
                    e.stopPropagation();
                    
                    const action = btn.dataset.action;
                    const itemId = btn.dataset.id;
                    
                    console.log('🖱️ 砍价按钮点击:', { action, itemId });
                    
                    switch(action) {
                        case 'create':
                            this.showCreateModal();
                            break;
                        case 'view-detail':
                            this.viewDetail(itemId);
                            break;
                        case 'edit':
                            this.editBargain(itemId);
                            break;
                        case 'start':
                            this.startBargain(itemId);
                            break;
                        case 'delete':
                            this.deleteBargain(itemId);
                            break;
                        case 'promote':
                            this.promoteBargain(itemId);
                            break;
                        case 'copy':
                            this.copyBargain(itemId);
                            break;
                        case 'view-report':
                            this.viewReport(itemId);
                            break;
                        case 'view-participants':
                            this.viewParticipants(itemId);
                            break;
                        case 'refresh':
                            this.refreshData();
                            break;
                        case 'batch-export':
                            this.batchExport();
                            break;
                        case 'batch-delete':
                            this.batchDelete();
                            break;
                        case 'advanced-search':
                            this.showAdvancedSearch();
                            break;
                        case 'sort':
                            this.showSortOptions();
                            break;
                        case 'view-mode':
                            this.toggleViewMode();
                            break;
                        case 'more-actions':
                            this.showMoreActions(btn, itemId);
                            break;
                    }
                });

                // 复选框选择
                document.addEventListener('change', (e) => {
                    if (e.target.classList.contains('item-select')) {
                        this.updateSelection(e.target);
                    }
                });

                // 搜索功能
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => {
                        this.searchBargains(e.target.value);
                    });
                }
            },

            // 显示创建模态框
            showCreateModal() {
                console.log('显示创建砍价模态框');
                this.showModal('createModal');
                this.showToast('打开创建砍价活动表单', 'info');
            },

            // 创建砍价
            createBargain() {
                console.log('创建砍价活动');
                this.showToast('砍价活动创建成功！', 'success');
                this.hideModal('createModal');
                // 模拟添加新项目
                setTimeout(() => {
                    this.updateStats();
                }, 500);
            },

            // 查看详情
            viewDetail(itemId) {
                console.log('查看砍价详情:', itemId);
                this.showToast(`查看砍价活动 ${itemId} 的详情`, 'info');
                
                // 模拟详情数据
                const detailData = {
                    id: itemId,
                    title: '苹果iPhone 15 Pro Max 256GB',
                    merchant: '苹果专营店',
                    originalPrice: '¥8,999',
                    floorPrice: '¥7,299',
                    currentPrice: '¥8,299',
                    progress: '68%',
                    participants: 156,
                    startTime: '2024-12-20 10:00:00',
                    endTime: '2024-12-22 23:59:59',
                    status: '进行中'
                };
                
                const content = this.generateDetailContent(detailData);
                this.showModalWithContent('detailModal', '砍价活动详情', content);
            },

            // 生成详情内容
            generateDetailContent(data) {
                return `
                    <div style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: auto 1fr; gap: 16px; margin-bottom: 24px;">
                            <img src="https://via.placeholder.com/120x120" alt="商品图片" style="width: 120px; height: 120px; border-radius: 8px; object-fit: cover;">
                            <div>
                                <h3 style="margin: 0 0 8px 0; color: #1e293b;">${data.title}</h3>
                                <p style="margin: 0 0 8px 0; color: #64748b;">商户：${data.merchant}</p>
                                <p style="margin: 0 0 8px 0; color: #64748b;">活动ID：${data.id}</p>
                                <div style="margin-top: 12px;">
                                    <span style="background: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${data.status}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 16px; margin-bottom: 24px;">
                            <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-size: 12px; color: #64748b; margin-bottom: 4px;">原价</div>
                                <div style="font-size: 18px; font-weight: 600; color: #ef4444;">${data.originalPrice}</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-size: 12px; color: #64748b; margin-bottom: 4px;">当前价格</div>
                                <div style="font-size: 18px; font-weight: 600; color: #f59e0b;">${data.currentPrice}</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-size: 12px; color: #64748b; margin-bottom: 4px;">底价</div>
                                <div style="font-size: 18px; font-weight: 600; color: #10b981;">${data.floorPrice}</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-size: 12px; color: #64748b; margin-bottom: 4px;">参与人数</div>
                                <div style="font-size: 18px; font-weight: 600; color: #3b82f6;">${data.participants}人</div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 24px;">
                            <h4 style="margin: 0 0 12px 0; color: #1e293b;">砍价进度</h4>
                            <div style="background: #f1f5f9; height: 12px; border-radius: 6px; overflow: hidden;">
                                <div style="width: ${data.progress}; height: 100%; background: linear-gradient(90deg, #ef4444, #f59e0b); border-radius: 6px;"></div>
                            </div>
                            <div style="margin-top: 8px; font-size: 14px; color: #64748b;">进度：${data.progress}</div>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                            <div>
                                <h5 style="margin: 0 0 8px 0; color: #1e293b;">开始时间</h5>
                                <p style="margin: 0; color: #64748b;">${data.startTime}</p>
                            </div>
                            <div>
                                <h5 style="margin: 0 0 8px 0; color: #1e293b;">结束时间</h5>
                                <p style="margin: 0; color: #64748b;">${data.endTime}</p>
                            </div>
                        </div>
                    </div>
                `;
            },

            // 编辑砍价
            editBargain(itemId) {
                console.log('编辑砍价:', itemId);
                this.showToast(`编辑砍价活动 ${itemId}`, 'info');
                
                // 模拟获取数据并显示编辑表单
                const data = {
                    id: itemId,
                    title: '苹果iPhone 15 Pro Max 256GB',
                    merchant: 'merchant1',
                    category: 'electronics',
                    originalPrice: 8999,
                    floorPrice: 7299,
                    startTime: '2024-12-20T10:00',
                    endTime: '2024-12-22T23:59',
                    description: '限时砍价活动，邀请好友助力获得更低价格！'
                };
                
                // 填充表单数据
                setTimeout(() => {
                    document.getElementById('productName').value = data.title;
                    document.getElementById('merchant').value = data.merchant;
                    document.getElementById('category').value = data.category;
                    document.getElementById('originalPrice').value = data.originalPrice;
                    document.getElementById('floorPrice').value = data.floorPrice;
                    document.getElementById('startTime').value = data.startTime;
                    document.getElementById('endTime').value = data.endTime;
                    document.getElementById('description').value = data.description;
                    
                    this.showModal('createModal');
                    document.querySelector('#createModal .modal-title').textContent = '编辑砍价活动';
                    document.querySelector('#createModal .modal-footer .btn-primary').textContent = '保存修改';
                    document.querySelector('#createModal .modal-footer .btn-primary').onclick = () => this.updateBargain(itemId);
                }, 300);
            },
            
            // 更新砍价活动
            updateBargain(itemId) {
                console.log('更新砍价活动:', itemId);
                this.showToast(`砍价活动 ${itemId} 已更新`, 'success');
                this.hideModal('createModal');
            },

            // 启动砍价
            startBargain(itemId) {
                console.log('启动砍价:', itemId);
                this.showToast(`砍价活动 ${itemId} 已启动`, 'success');
                // 更新状态
                this.updateItemStatus(itemId, 'active');
            },

            // 删除砍价
            deleteBargain(itemId) {
                if (confirm('确定要删除这个砍价活动吗？')) {
                    console.log('删除砍价:', itemId);
                    this.showToast(`砍价活动 ${itemId} 已删除`, 'warning');
                    // 移除项目
                    const item = document.querySelector(`[data-id="${itemId}"]`);
                    if (item && item.classList.contains('bargain-item')) {
                        item.remove();
                    }
                }
            },

            // 推广砍价
            promoteBargain(itemId) {
                console.log('推广砍价:', itemId);
                this.showToast(`开始推广砍价活动 ${itemId}`, 'success');
                
                const content = `
                    <div style="text-align: center; padding: 20px;">
                        <div style="margin-bottom: 20px;">
                            <i class="fas fa-share-alt" style="font-size: 48px; color: #ef4444; margin-bottom: 16px;"></i>
                            <h3 style="margin: 0 0 8px 0; color: #1e293b;">推广砍价活动</h3>
                            <p style="margin: 0; color: #64748b;">选择推广方式分享砍价活动</p>
                        </div>
                        <div style="display: grid; gap: 12px; margin-bottom: 24px;">
                            <button class="btn btn-primary" onclick="alert('微信推广功能')">
                                <i class="fab fa-weixin"></i> 微信推广
                            </button>
                            <button class="btn btn-primary" onclick="alert('朋友圈推广功能')">
                                <i class="fas fa-share-alt"></i> 朋友圈推广
                            </button>
                            <button class="btn btn-primary" onclick="alert('社群推广功能')">
                                <i class="fas fa-users"></i> 社群推广
                            </button>
                        </div>
                        <div style="text-align: center;">
                            <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').classList.remove('show')">关闭</button>
                        </div>
                    </div>
                `;
                
                this.showModalWithContent('detailModal', '推广设置', content);
            },

            // 复制砍价
            copyBargain(itemId) {
                console.log('复制砍价:', itemId);
                this.showToast(`砍价活动 ${itemId} 已复制`, 'success');
            },

            // 查看报告
            viewReport(itemId) {
                console.log('查看砍价报告:', itemId);
                this.showToast(`查看砍价活动 ${itemId} 的数据报告`, 'info');
                
                const content = `
                    <div style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 16px; margin-bottom: 24px;">
                            <div style="text-align: center; padding: 16px; background: #fef2f2; border-radius: 8px;">
                                <div style="font-size: 12px; color: #ef4444; margin-bottom: 4px;">浏览量</div>
                                <div style="font-size: 24px; font-weight: 600; color: #ef4444;">2,456</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #fefbf2; border-radius: 8px;">
                                <div style="font-size: 12px; color: #f59e0b; margin-bottom: 4px;">参与人数</div>
                                <div style="font-size: 24px; font-weight: 600; color: #f59e0b;">156</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #f0fdf4; border-radius: 8px;">
                                <div style="font-size: 12px; color: #10b981; margin-bottom: 4px;">成功率</div>
                                <div style="font-size: 24px; font-weight: 600; color: #10b981;">68%</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #eff6ff; border-radius: 8px;">
                                <div style="font-size: 12px; color: #3b82f6; margin-bottom: 4px;">预计收入</div>
                                <div style="font-size: 24px; font-weight: 600; color: #3b82f6;">¥8,299</div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 24px;">
                            <h4 style="margin: 0 0 12px 0; color: #1e293b;">砍价趋势</h4>
                            <div style="background: #f8fafc; padding: 20px; border-radius: 8px; text-align: center;">
                                <i class="fas fa-chart-line" style="font-size: 48px; color: #64748b; margin-bottom: 16px;"></i>
                                <p style="margin: 0; color: #64748b;">砍价趋势图表区域</p>
                            </div>
                        </div>
                        
                        <div>
                            <h4 style="margin: 0 0 12px 0; color: #1e293b;">参与明细</h4>
                            <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e2e8f0;">
                                    <span style="color: #1e293b;">用户A</span>
                                    <span style="color: #ef4444;">砍掉 ¥50</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e2e8f0;">
                                    <span style="color: #1e293b;">用户B</span>
                                    <span style="color: #ef4444;">砍掉 ¥25</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                    <span style="color: #1e293b;">用户C</span>
                                    <span style="color: #ef4444;">砍掉 ¥35</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                this.showModalWithContent('detailModal', '砍价数据报告', content);
            },

            // 查看参与者
            viewParticipants(itemId) {
                console.log('查看参与者:', itemId);
                this.showToast(`查看砍价活动 ${itemId} 的参与者`, 'info');
                
                const content = `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 20px;">
                            <h4 style="margin: 0 0 12px 0; color: #1e293b;">参与用户 (156人)</h4>
                            <div style="display: grid; gap: 12px;">
                                ${Array.from({length: 10}, (_, i) => `
                                    <div style="display: flex; align-items: center; padding: 12px; background: #f8fafc; border-radius: 8px;">
                                        <img src="https://via.placeholder.com/40x40" alt="用户头像" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 12px;">
                                        <div style="flex: 1;">
                                            <div style="font-weight: 500; color: #1e293b; margin-bottom: 4px;">用户${i + 1}</div>
                                            <div style="font-size: 12px; color: #64748b;">参与时间: 12-20 ${10 + i}:30</div>
                                        </div>
                                        <div style="text-align: right;">
                                            <div style="font-weight: 600; color: #ef4444;">砍掉 ¥${20 + Math.floor(Math.random() * 50)}</div>
                                            <div style="font-size: 12px; color: #10b981;">已砍价</div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div style="text-align: center;">
                            <button class="btn btn-secondary" onclick="alert('加载更多参与者')">加载更多</button>
                        </div>
                    </div>
                `;
                
                this.showModalWithContent('detailModal', '参与者列表', content);
            },

            // 刷新数据
            refreshData() {
                console.log('刷新砍价数据');
                this.showToast('数据已刷新', 'success');
                this.updateStats();
            },

            // 批量导出
            batchExport() {
                console.log('批量导出砍价数据');
                this.showToast('正在导出砍价数据...', 'info');
                setTimeout(() => {
                    this.showToast('砍价数据导出完成！', 'success');
                }, 2000);
            },

            // 批量删除
            batchDelete() {
                if (this.selectedItems.size === 0) {
                    this.showToast('请先选择要删除的项目', 'warning');
                    return;
                }
                
                if (confirm(`确定要删除选中的 ${this.selectedItems.size} 个砍价活动吗？`)) {
                    console.log('批量删除砍价:', Array.from(this.selectedItems));
                    this.showToast(`已删除 ${this.selectedItems.size} 个砍价活动`, 'warning');
                    this.selectedItems.clear();
                    this.updateBatchUI();
                }
            },

            // 高级搜索
            showAdvancedSearch() {
                console.log('显示高级搜索');
                
                const content = `
                    <div style="padding: 20px;">
                        <form id="advancedSearchForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">活动名称</label>
                                    <input type="text" class="form-input" placeholder="输入活动名称关键词">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">活动状态</label>
                                    <select class="form-select">
                                        <option value="">全部状态</option>
                                        <option value="active">进行中</option>
                                        <option value="pending">待开始</option>
                                        <option value="completed">已完成</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">价格范围</label>
                                    <div style="display: flex; gap: 8px; align-items: center;">
                                        <input type="number" class="form-input" placeholder="最低价" style="width: 45%">
                                        <span>-</span>
                                        <input type="number" class="form-input" placeholder="最高价" style="width: 45%">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">时间范围</label>
                                    <div style="display: flex; gap: 8px; align-items: center;">
                                        <input type="date" class="form-input" style="width: 45%">
                                        <span>-</span>
                                        <input type="date" class="form-input" style="width: 45%">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">商户</label>
                                <select class="form-select">
                                    <option value="">全部商户</option>
                                    <option value="merchant1">苹果专营店</option>
                                    <option value="merchant2">小米官方旗舰店</option>
                                    <option value="merchant3">华为官方商城</option>
                                </select>
                            </div>
                        </form>
                    </div>
                `;
                
                this.showModalWithContent('detailModal', '高级搜索', content);
                
                // 添加搜索按钮
                const footer = document.querySelector('#detailModal .modal-footer');
                if (footer) {
                    footer.innerHTML = `
                        <button class="btn btn-secondary" onclick="BargainManager.hideModal('detailModal')">取消</button>
                        <button class="btn btn-primary" onclick="BargainManager.performAdvancedSearch()">搜索</button>
                    `;
                }
            },
            
            // 执行高级搜索
            performAdvancedSearch() {
                console.log('执行高级搜索');
                this.showToast('高级搜索已执行', 'info');
                this.hideModal('detailModal');
            },

            // 排序选项
            showSortOptions() {
                console.log('显示排序选项');
                this.showToast('排序功能', 'info');
            },

            // 切换视图模式
            toggleViewMode() {
                console.log('切换视图模式');
                this.showToast('切换视图模式', 'info');
            },

            // 更多操作
            showMoreActions(btn, itemId) {
                console.log('显示更多操作:', itemId);
                this.showToast('更多操作菜单', 'info');
            },

            // 搜索砍价
            searchBargains(query) {
                console.log('搜索砍价:', query);
                if (query.trim()) {
                    this.showToast(`搜索: ${query}`, 'info');
                }
            },

            // 更新选择
            updateSelection(checkbox) {
                const itemId = checkbox.dataset.id;
                if (checkbox.checked) {
                    this.selectedItems.add(itemId);
                } else {
                    this.selectedItems.delete(itemId);
                }
                this.updateBatchUI();
            },

            // 更新批量操作UI
            updateBatchUI() {
                const batchDeleteBtn = document.querySelector('[data-action="batch-delete"]');
                if (batchDeleteBtn) {
                    batchDeleteBtn.disabled = this.selectedItems.size === 0;
                    batchDeleteBtn.textContent = this.selectedItems.size > 0 
                        ? `批量删除 (${this.selectedItems.size})` 
                        : '批量删除';
                }
            },

            // 更新项目状态
            updateItemStatus(itemId, status) {
                const item = document.querySelector(`[data-id="${itemId}"]`);
                if (item) {
                    const statusBadge = item.querySelector('.status-badge');
                    if (statusBadge) {
                        statusBadge.className = `status-badge status-${status}`;
                        statusBadge.textContent = status === 'active' ? '进行中' : status === 'completed' ? '已完成' : '待开始';
                    }
                }
            },

            // 更新统计数据
            updateStats() {
                const stats = {
                    totalActivities: document.getElementById('totalActivities'),
                    totalParticipants: document.getElementById('totalParticipants'),
                    successfulBargains: document.getElementById('successfulBargains'),
                    totalRevenue: document.getElementById('totalRevenue')
                };

                // 模拟数据更新
                if (stats.totalActivities) {
                    const current = parseInt(stats.totalActivities.textContent);
                    stats.totalActivities.textContent = current + 1;
                }
            },

            // 显示模态框
            showModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.add('show');
                    modal.style.display = 'flex';
                }
            },

            // 隐藏模态框
            hideModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                }
            },

            // 显示带内容的模态框
            showModalWithContent(modalId, title, content) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    const titleElement = modal.querySelector('.modal-title');
                    const contentElement = modal.querySelector('.modal-body');
                    
                    if (titleElement) titleElement.textContent = title;
                    if (contentElement) contentElement.innerHTML = content;
                    
                    this.showModal(modalId);
                }
            },

            // Toast消息
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.style.cssText = `
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    margin-bottom: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                    font-size: 14px;
                    max-width: 300px;
                    word-wrap: break-word;
                `;
                
                toast.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                        <span>${message}</span>
                    </div>
                `;
                
                const container = document.getElementById('toastContainer');
                if (container) {
                    container.appendChild(toast);
                    
                    requestAnimationFrame(() => {
                        toast.style.opacity = '1';
                        toast.style.transform = 'translateX(0)';
                    });
                    
                    setTimeout(() => {
                        toast.style.opacity = '0';
                        toast.style.transform = 'translateX(100%)';
                        setTimeout(() => {
                            if (toast.parentNode) {
                                toast.parentNode.removeChild(toast);
                            }
                        }, 300);
                    }, 3000);
                }
            }
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            BargainManager.init();
        });
    </script>

    <style>
        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .modal-overlay.show .modal-content {
            transform: scale(1);
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8fafc;
        }

        .modal-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #64748b;
            padding: 4px;
        }

        .modal-close:hover {
            color: #1e293b;
        }

        .modal-body {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background: #f8fafc;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #1e293b;
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.15s;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .details-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</body>
</html> 