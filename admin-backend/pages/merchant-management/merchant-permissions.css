        /* CSS变量定义 */
        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 8px;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* 全局样式重置 */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: var(--gray-50);
            color: var(--gray-900);
            line-height: 1.6;
        }

        /* 页面容器 */
        .permissions-page {
            padding: 24px;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            margin-bottom: 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .page-subtitle {
            color: var(--gray-500);
            font-size: 16px;
            margin-top: 4px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 主布局 */
        .main-layout {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 24px;
            align-items: start;
        }

        /* 侧边栏 */
        .sidebar-panel {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            overflow: hidden;
            position: sticky;
            top: 24px;
        }

        .panel-header {
            padding: 20px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .merchant-count {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 搜索区域 */
        .search-box {
            margin: 16px 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 14px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="%23999"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>') no-repeat 16px center;
            background-size: 16px;
            transition: border-color 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 筛选器 */
        .filter-tabs {
            display: flex;
            gap: 8px;
            margin: 16px 20px;
            flex-wrap: wrap;
        }

        .filter-tab {
            padding: 6px 12px;
            background: var(--gray-100);
            border: 1px solid var(--gray-300);
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .filter-tab:hover:not(.active) {
            background: var(--gray-200);
        }

        /* 商户列表 */
        .merchants-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .merchant-item {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-100);
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .merchant-item:hover {
            background: var(--gray-50);
        }

        .merchant-item.active {
            background: #eff6ff;
            border-left: 4px solid var(--primary-color);
        }

        .merchant-item:last-child {
            border-bottom: none;
        }

        .merchant-checkbox {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 16px;
            height: 16px;
        }

        .merchant-info {
            padding-right: 24px;
        }

        .merchant-name {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 4px;
            font-size: 14px;
        }

        .merchant-details {
            font-size: 12px;
            color: var(--gray-500);
            margin-bottom: 8px;
        }

        .merchant-badges {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .merchant-status {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .merchant-status.active {
            background: #dcfce7;
            color: #166534;
        }

        .merchant-status.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .merchant-status.suspended {
            background: #fecaca;
            color: #dc2626;
        }

        .merchant-type {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
            background: var(--gray-200);
            color: var(--gray-700);
        }

        /* 内容区域 */
        .permissions-content {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .content-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .selected-merchant {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .merchant-avatar {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 18px;
        }

        .merchant-details h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0 0 4px 0;
        }

        .merchant-details p {
            font-size: 14px;
            color: var(--gray-500);
            margin: 0;
        }

        .merchant-stats {
            display: flex;
            gap: 24px;
            font-size: 12px;
            color: var(--gray-600);
        }

        /* 批量操作栏 */
        .bulk-toolbar {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border: 1px solid #bfdbfe;
            padding: 12px 24px;
            display: none;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }

        .bulk-toolbar.show {
            display: flex;
        }

        .bulk-info {
            font-size: 14px;
            color: #1e40af;
            font-weight: 500;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        /* 权限选项卡 */
        .permission-tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid var(--gray-200);
        }

        .tab-button {
            padding: 16px 24px;
            border: none;
            background: none;
            color: var(--gray-500);
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .tab-button.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-button:hover:not(.active) {
            color: var(--gray-700);
            background: var(--gray-50);
        }

        /* 选项卡内容 */
        .tab-content {
            padding: 24px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 权限部分 */
        .permission-section {
            margin-bottom: 32px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 12px 16px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-200);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .section-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .section-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--gray-600);
        }

        .toggle-switch {
            position: relative;
            width: 48px;
            height: 24px;
            background: var(--gray-300);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .toggle-switch.active {
            background: var(--primary-color);
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active::after {
            transform: translateX(24px);
        }

        /* 权限网格 */
        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
        }

        .permission-card {
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 20px;
            background: white;
            transition: all 0.2s ease;
            position: relative;
        }

        .permission-card:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .permission-card.disabled {
            opacity: 0.5;
            pointer-events: none;
            background: var(--gray-50);
        }

        .permission-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .permission-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            margin-right: 12px;
        }

        .permission-icon.products { background: var(--success-color); }
        .permission-icon.orders { background: var(--primary-color); }
        .permission-icon.customers { background: #8b5cf6; }
        .permission-icon.marketing { background: var(--warning-color); }
        .permission-icon.analytics { background: var(--danger-color); }
        .permission-icon.finance { background: #06b6d4; }
        .permission-icon.settings { background: var(--gray-500); }

        .permission-name {
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
            flex: 1;
            font-size: 16px;
        }

        .permission-description {
            font-size: 14px;
            color: var(--gray-600);
            margin-bottom: 16px;
            line-height: 1.5;
        }

        /* 权限项目列表 */
        .permission-items {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--gray-100);
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-label {
            font-size: 14px;
            color: var(--gray-700);
            font-weight: 500;
        }

        .permission-checkbox {
            width: 20px;
            height: 20px;
            margin: 0;
            cursor: pointer;
            accent-color: var(--primary-color);
        }

        /* 权限项目状态 */
        .permission-item.required {
            background: #f0f9ff;
            border-left: 3px solid var(--primary-color);
            padding-left: 12px;
            border-radius: 4px;
        }

        .permission-item.required .permission-label::after {
            content: '*';
            color: var(--danger-color);
            margin-left: 4px;
        }

        /* 权限状态指示器 */
        .permission-item.dependent {
            background: #fef3c7;
            border-left: 3px solid var(--warning-color);
            padding-left: 12px;
            border-radius: 4px;
        }
        
        .permission-item.blocked {
            background: #fecaca;
            border-left: 3px solid var(--danger-color);
            padding-left: 12px;
            border-radius: 4px;
            opacity: 0.6;
        }
        
        .permission-dependency {
            font-size: 11px;
            color: #92400e;
            margin-top: 2px;
            font-style: italic;
        }

        /* 审批状态 */
        .approval-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
            border: 2px solid white;
        }

        .approval-badge.pending {
            background: var(--warning-color);
            color: white;
        }

        .approval-badge.approved {
            background: var(--success-color);
            color: white;
        }

        .approval-badge.rejected {
            background: var(--danger-color);
            color: white;
        }

        /* 模态框 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            max-width: 600px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .modal-header {
            padding: 24px 24px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--gray-400);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.2s ease;
        }

        .modal-close:hover {
            color: var(--gray-600);
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 0 24px 24px;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--gray-400);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--gray-200);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 通知系统 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1100;
            padding: 16px 20px;
            border-radius: var(--border-radius);
            color: white;
            font-size: 14px;
            font-weight: 500;
            min-width: 320px;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            box-shadow: var(--shadow-md);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }

        .notification.error {
            background: var(--danger-color);
        }

        .notification.info {
            background: var(--primary-color);
        }

        /* 变更指示器 */
        .changes-indicator {
            position: fixed;
            bottom: 24px;
            right: 24px;
            background: var(--warning-color);
            color: white;
            padding: 16px 20px;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 500;
            display: none;
            align-items: center;
            gap: 12px;
            box-shadow: var(--shadow-md);
        }

        .changes-indicator.show {
            display: flex;
        }

        /* 通知系统 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            padding: 16px 20px;
            max-width: 400px;
            z-index: 1001;
            border-left: 4px solid var(--primary-color);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left-color: var(--success-color);
        }

        .notification.warning {
            border-left-color: var(--warning-color);
        }

        .notification.error {
            border-left-color: var(--danger-color);
        }

        .notification-content {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .notification-icon {
            flex-shrink: 0;
            width: 20px;
            height: 20px;
            margin-top: 2px;
        }

        .notification-message {
            flex: 1;
            font-size: 14px;
            line-height: 1.5;
        }

        .notification-close {
            background: none;
            border: none;
            color: var(--gray-400);
            cursor: pointer;
            padding: 0;
            font-size: 16px;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 64px;
            color: var(--gray-300);
            margin-bottom: 16px;
        }

        .empty-state h3 {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-700);
            margin: 0 0 8px 0;
        }

        .empty-state p {
            font-size: 16px;
            margin: 0;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 300px 1fr;
            }
        }

        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .sidebar-panel {
                position: static;
                order: 2;
            }

            .permissions-content {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .permissions-page {
                padding: 16px;
            }

            .header-content {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .header-actions {
                flex-direction: column;
                width: 100%;
            }

            .permissions-grid {
                grid-template-columns: 1fr;
            }

            .bulk-toolbar {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            .bulk-actions {
                justify-content: center;
                flex-wrap: wrap;
            }

            .permission-tabs {
                overflow-x: auto;
                white-space: nowrap;
            }

            .permission-tabs::-webkit-scrollbar {
                height: 4px;
            }

            .permission-tabs::-webkit-scrollbar-track {
                background: var(--gray-100);
            }

            .permission-tabs::-webkit-scrollbar-thumb {
                background: var(--gray-300);
                border-radius: 2px;
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .notification {
                left: 16px;
                right: 16px;
                max-width: none;
            }
        }

        @media (max-width: 480px) {
            .permissions-page {
                padding: 12px;
            }

            .page-header {
                padding: 16px;
            }

            .tab-content {
                padding: 16px;
            }

            .permission-card {
                padding: 16px;
            }

            .modal-content {
                margin: 16px;
                max-height: 85vh;
            }
        }

        /* 滚动条样式 */
        .merchants-list::-webkit-scrollbar {
            width: 6px;
        }

        .merchants-list::-webkit-scrollbar-track {
            background: var(--gray-100);
        }

        .merchants-list::-webkit-scrollbar-thumb {
            background: var(--gray-300);
            border-radius: 3px;
        }

        .merchants-list::-webkit-scrollbar-thumb:hover {
            background: var(--gray-400);
        }

        /* 权限模板样式 */
        .template-header {
            text-align: center;
            margin-bottom: 32px;
            padding: 20px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
        }

        .template-header h3 {
            font-size: 24px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0 0 8px 0;
        }

        .template-header p {
            color: var(--gray-600);
            margin: 0;
            font-size: 16px;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
        }

        .template-card {
            background: white;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .template-card:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
            transform: translateY(-4px);
        }

        .template-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--primary-color), #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-bottom: 16px;
        }

        .template-info h4 {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0 0 8px 0;
        }

        .template-info p {
            color: var(--gray-600);
            font-size: 14px;
            line-height: 1.5;
            margin: 0 0 16px 0;
        }

        .template-permissions {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 20px;
        }

        .permission-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            background: var(--gray-100);
            color: var(--gray-700);
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .template-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        /* 日志样式 */
        .logs-list {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .log-item {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-100);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .log-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
        }

        .log-icon.create { background: var(--success-color); }
        .log-icon.update { background: var(--primary-color); }
        .log-icon.delete { background: var(--danger-color); }

        .log-content {
            flex: 1;
        }

        .log-title {
            font-weight: 500;
            color: var(--gray-900);
            margin: 0 0 4px 0;
            font-size: 14px;
        }

        .log-details {
            color: var(--gray-600);
            font-size: 12px;
        }

        .log-time {
            color: var(--gray-400);
            font-size: 12px;
        }
