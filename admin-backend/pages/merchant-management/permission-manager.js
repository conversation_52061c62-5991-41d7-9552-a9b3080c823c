/**
 * 商家权限管理功能类
 */
class MerchantPermissionManager {
    constructor() {
        this.selectedMerchants = new Set();
        this.currentMerchant = null;
        this.hasChanges = false;
        
        // 权限依赖关系
        this.permissionDependencies = {
            'add_products': ['view_products'],
            'edit_products': ['view_products'],
            'delete_products': ['edit_products'],
            'manage_inventory': ['view_products'],
            'bulk_operations': ['edit_products']
        };
        
        // 权限分类
        this.permissionCategories = {
            'products': {
                name: '商品管理',
                icon: 'fas fa-box',
                color: '#10b981',
                permissions: [
                    { id: 'view_products', name: '查看商品', description: '浏览商品列表', required: true },
                    { id: 'add_products', name: '添加商品', description: '新增商品', depends: ['view_products'] },
                    { id: 'edit_products', name: '编辑商品', description: '修改商品', depends: ['view_products'] },
                    { id: 'delete_products', name: '删除商品', description: '删除商品', depends: ['edit_products'] },
                    { id: 'manage_inventory', name: '库存管理', description: '管理库存', depends: ['view_products'] },
                    { id: 'bulk_operations', name: '批量操作', description: '批量编辑', depends: ['edit_products'] }
                ]
            },
            'orders': {
                name: '订单管理',
                icon: 'fas fa-shopping-cart',
                color: '#3b82f6',
                permissions: [
                    { id: 'view_orders', name: '查看订单', description: '浏览订单列表', required: true },
                    { id: 'process_orders', name: '处理订单', description: '处理订单状态', depends: ['view_orders'] },
                    { id: 'cancel_orders', name: '取消订单', description: '取消订单', depends: ['process_orders'] },
                    { id: 'refund_orders', name: '退款处理', description: '处理退款', depends: ['process_orders'], requiresApproval: true },
                    { id: 'export_orders', name: '导出订单', description: '导出订单数据', depends: ['view_orders'] }
                ]
            }
        };

        // 权限模板
        this.permissionTemplates = {
            'basic': {
                name: '基础商户',
                description: '适用于新入驻商户',
                permissions: ['view_products', 'add_products', 'view_orders']
            },
            'standard': {
                name: '标准商户', 
                description: '适用于运营稳定的商户',
                permissions: ['view_products', 'add_products', 'edit_products', 'view_orders', 'process_orders']
            },
            'premium': {
                name: '高级商户',
                description: '适用于大型商户',
                permissions: ['view_products', 'add_products', 'edit_products', 'delete_products', 'manage_inventory', 'view_orders', 'process_orders', 'cancel_orders']
            }
        };

        this.merchants = [];
        this.init();
    }

    init() {
        this.loadMerchants();
        this.setupEventListeners();
        this.updateMerchantCount();
    }

    loadMerchants() {
        this.merchants = [
            {
                id: 'merchant_001',
                name: '星辰数码专营店',
                type: 'premium',
                status: 'active',
                joinDate: '2024-01-15',
                permissions: ['view_products', 'add_products', 'edit_products'],
                pendingApprovals: ['refund_orders']
            },
            {
                id: 'merchant_002',
                name: '时尚生活馆',
                type: 'standard', 
                status: 'active',
                joinDate: '2024-02-20',
                permissions: ['view_products', 'add_products'],
                pendingApprovals: []
            },
            {
                id: 'merchant_003',
                name: '优品食材供应',
                type: 'basic',
                status: 'pending',
                joinDate: '2024-03-10',
                permissions: ['view_products'],
                pendingApprovals: ['add_products']
            }
        ];

        this.renderMerchantsList();
    }

    setupEventListeners() {
        // 商户选择
        document.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.closest('.merchant-item')) {
                this.handleMerchantCheckboxChange(e.target);
            }
        });

        // 权限变更
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('permission-checkbox')) {
                this.handlePermissionChange(e.target);
            }
        });

        // 商户点击
        document.addEventListener('click', (e) => {
            const merchantItem = e.target.closest('.merchant-item');
            if (merchantItem && e.target.type !== 'checkbox') {
                this.selectMerchant(merchantItem.dataset.merchantId);
            }
        });

        // 模态框关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay') || e.target.classList.contains('modal-close')) {
                this.closeModal();
            }
        });
    }

    renderMerchantsList() {
        const container = document.getElementById('merchantsList');
        if (!container) return;

        container.innerHTML = this.merchants.map(merchant => `
            <div class="merchant-item" data-merchant-id="${merchant.id}">
                <div class="merchant-info">
                    <div class="merchant-name">${merchant.name}</div>
                    <div class="merchant-details">ID: ${merchant.id} | 加入: ${merchant.joinDate}</div>
                    <div class="merchant-badges">
                        <span class="merchant-status ${merchant.status}">${this.getStatusText(merchant.status)}</span>
                        <span class="merchant-type">${this.getTypeText(merchant.type)}</span>
                        ${merchant.pendingApprovals.length > 0 ? `<span class="merchant-status pending">${merchant.pendingApprovals.length}待审</span>` : ''}
                    </div>
                </div>
                <input type="checkbox" class="merchant-checkbox" value="${merchant.id}">
            </div>
        `).join('');
    }

    selectMerchant(merchantId) {
        // 清除之前选择
        document.querySelectorAll('.merchant-item').forEach(item => {
            item.classList.remove('active');
        });

        // 设置当前选择
        const merchantItem = document.querySelector(`[data-merchant-id="${merchantId}"]`);
        if (merchantItem) {
            merchantItem.classList.add('active');
        }

        this.currentMerchant = this.merchants.find(m => m.id === merchantId);
        this.showMerchantPermissions();
    }

    showMerchantPermissions() {
        if (!this.currentMerchant) return;

        // 更新商户信息显示
        const merchantInfo = document.getElementById('selectedMerchantInfo');
        const merchantAvatar = document.getElementById('merchantAvatar');
        const merchantName = document.getElementById('merchantName');
        const merchantDesc = document.getElementById('merchantDesc');
        const merchantStats = document.getElementById('merchantStats');

        if (merchantInfo) {
            merchantInfo.style.display = 'flex';
            merchantAvatar.textContent = this.currentMerchant.name.charAt(0);
            merchantName.textContent = this.currentMerchant.name;
            merchantDesc.textContent = `${this.getTypeText(this.currentMerchant.type)} | ${this.getStatusText(this.currentMerchant.status)}`;
            merchantStats.style.display = 'block';
        }

        this.renderPermissionMatrix();
        this.updatePermissionStats();
    }

    renderPermissionMatrix() {
        const container = document.getElementById('permissionsMatrix');
        if (!container || !this.currentMerchant) return;

        const html = Object.entries(this.permissionCategories).map(([categoryId, category]) => {
            const enabledCount = category.permissions.filter(p => 
                this.currentMerchant.permissions.includes(p.id)
            ).length;

            return `
                <div class="permission-category">
                    <div class="category-header">
                        <div class="category-title">
                            <div class="category-icon" style="background: ${category.color};">
                                <i class="${category.icon}"></i>
                            </div>
                            <span>${category.name}</span>
                            <small>(${enabledCount}/${category.permissions.length})</small>
                        </div>
                        <div class="category-toggle">
                            <input type="checkbox" class="category-toggle-checkbox" 
                                   data-category="${categoryId}" 
                                   ${enabledCount === category.permissions.length ? 'checked' : ''}>
                            <span>全选</span>
                        </div>
                    </div>
                    <div class="permissions-grid">
                        ${category.permissions.map(permission => this.renderPermissionItem(permission, categoryId)).join('')}
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = html;
    }

    renderPermissionItem(permission, categoryId) {
        const isEnabled = this.currentMerchant.permissions.includes(permission.id);
        const isPending = this.currentMerchant.pendingApprovals.includes(permission.id);
        const isBlocked = this.isPermissionBlocked(permission);

        let statusIcon = '';
        if (isPending) {
            statusIcon = '<div class="approval-status pending">⏳</div>';
        } else if (permission.requiresApproval && isEnabled) {
            statusIcon = '<div class="approval-status approved">✓</div>';
        }

        return `
            <div class="permission-item ${isBlocked ? 'disabled' : ''}" data-permission="${permission.id}">
                ${statusIcon}
                <div class="permission-header">
                    <div class="permission-name">${permission.name}</div>
                    <input type="checkbox" class="permission-checkbox" 
                           data-permission="${permission.id}" 
                           data-category="${categoryId}"
                           ${isEnabled ? 'checked' : ''} 
                           ${isBlocked ? 'disabled' : ''}>
                </div>
                <div class="permission-description">${permission.description}</div>
                <div class="permission-meta">
                    ${permission.required ? '<span class="permission-tag required">必需</span>' : ''}
                    ${permission.requiresApproval ? '<span class="permission-tag approval">需审批</span>' : ''}
                    ${permission.depends ? '<span class="permission-tag dependency">有依赖</span>' : ''}
                </div>
            </div>
        `;
    }

    isPermissionBlocked(permission) {
        if (!permission.depends) return false;
        
        return permission.depends.some(depId => {
            return !this.currentMerchant.permissions.includes(depId);
        });
    }

    handleMerchantCheckboxChange(checkbox) {
        const merchantId = checkbox.value;
        
        if (checkbox.checked) {
            this.selectedMerchants.add(merchantId);
        } else {
            this.selectedMerchants.delete(merchantId);
        }

        this.updateBulkActionsBar();
    }

    handlePermissionChange(checkbox) {
        const permissionId = checkbox.dataset.permission;
        const isChecked = checkbox.checked;

        if (isChecked) {
            if (!this.checkPermissionDependencies(permissionId)) {
                checkbox.checked = false;
                this.showNotification('请先启用相关依赖权限', 'warning');
                return;
            }

            const permission = this.findPermissionById(permissionId);
            if (permission && permission.requiresApproval) {
                this.submitForApproval(permissionId);
                checkbox.checked = false;
                return;
            }

            this.currentMerchant.permissions.push(permissionId);
        } else {
            this.currentMerchant.permissions = this.currentMerchant.permissions.filter(p => p !== permissionId);
        }

        this.markAsChanged();
        this.updatePermissionStats();
    }

    checkPermissionDependencies(permissionId) {
        const dependencies = this.permissionDependencies[permissionId];
        if (!dependencies) return true;

        return dependencies.every(depId => 
            this.currentMerchant.permissions.includes(depId)
        );
    }

    findPermissionById(permissionId) {
        for (const category of Object.values(this.permissionCategories)) {
            const permission = category.permissions.find(p => p.id === permissionId);
            if (permission) return permission;
        }
        return null;
    }

    submitForApproval(permissionId) {
        if (!this.currentMerchant.pendingApprovals.includes(permissionId)) {
            this.currentMerchant.pendingApprovals.push(permissionId);
        }

        const permission = this.findPermissionById(permissionId);
        this.showNotification(`${permission.name} 已提交审批申请`, 'info');
        this.renderPermissionMatrix();
    }

    updateBulkActionsBar() {
        const bulkActions = document.getElementById('bulkActions');
        const selectedCount = document.getElementById('selectedCount');
        
        if (this.selectedMerchants.size > 0) {
            bulkActions.classList.add('show');
            selectedCount.textContent = this.selectedMerchants.size;
        } else {
            bulkActions.classList.remove('show');
        }
    }

    updatePermissionStats() {
        if (!this.currentMerchant) return;

        const totalPermissions = Object.values(this.permissionCategories)
            .reduce((sum, category) => sum + category.permissions.length, 0);
        
        const enabledCount = this.currentMerchant.permissions.length;
        const pendingCount = this.currentMerchant.pendingApprovals.length;

        document.getElementById('totalPermissions').textContent = totalPermissions;
        document.getElementById('enabledPermissions').textContent = enabledCount;
        document.getElementById('pendingPermissions').textContent = pendingCount;
    }

    updateMerchantCount() {
        const countElement = document.getElementById('merchantCount');
        if (countElement) {
            countElement.textContent = this.merchants.length;
        }
    }

    getStatusText(status) {
        const statusMap = {
            'active': '正常营业',
            'pending': '待审核',
            'suspended': '已暂停'
        };
        return statusMap[status] || status;
    }

    getTypeText(type) {
        const typeMap = {
            'basic': '基础版',
            'standard': '标准版',
            'premium': '高级版'
        };
        return typeMap[type] || type;
    }

    // 批量操作
    bulkEnablePermissions() {
        if (this.selectedMerchants.size === 0) {
            this.showNotification('请先选择商户', 'warning');
            return;
        }
        this.showNotification(`已为${this.selectedMerchants.size}个商户启用权限`, 'success');
    }

    bulkDisablePermissions() {
        if (this.selectedMerchants.size === 0) {
            this.showNotification('请先选择商户', 'warning');
            return;
        }
        this.showNotification(`已为${this.selectedMerchants.size}个商户禁用权限`, 'success');
    }

    bulkApplyTemplate() {
        if (this.selectedMerchants.size === 0) {
            this.showNotification('请先选择商户', 'warning');
            return;
        }
        this.showTemplateSelector();
    }

    bulkApprove() {
        if (this.selectedMerchants.size === 0) {
            this.showNotification('请先选择商户', 'warning');
            return;
        }
        this.showNotification('批量审批功能开发中...', 'info');
    }

    searchMerchants(keyword) {
        const items = document.querySelectorAll('.merchant-item');
        items.forEach(item => {
            const name = item.querySelector('.merchant-name').textContent.toLowerCase();
            const id = item.dataset.merchantId.toLowerCase();
            
            if (name.includes(keyword.toLowerCase()) || id.includes(keyword.toLowerCase())) {
                item.style.display = 'block';
            } else {
                item.style.display = keyword ? 'none' : 'block';
            }
        });
    }

    filterMerchants(filter) {
        const items = document.querySelectorAll('.merchant-item');
        items.forEach(item => {
            const merchantId = item.dataset.merchantId;
            const merchant = this.merchants.find(m => m.id === merchantId);
            
            let show = true;
            switch (filter) {
                case 'active':
                    show = merchant.status === 'active';
                    break;
                case 'pending':
                    show = merchant.status === 'pending' || merchant.pendingApprovals.length > 0;
                    break;
                case 'premium':
                    show = merchant.type === 'premium';
                    break;
                case 'all':
                default:
                    show = true;
            }
            
            item.style.display = show ? 'block' : 'none';
        });
    }

    showTemplateSelector() {
        const modalContent = `
            <div class="modal-header">
                <h3 class="modal-title">选择权限模板</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="template-list">
                    ${Object.entries(this.permissionTemplates).map(([templateId, template]) => `
                        <div class="template-item" style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px; margin-bottom: 12px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <h4 style="margin: 0; font-size: 16px;">${template.name}</h4>
                                <button class="btn btn-sm btn-primary" onclick="merchantPermissionManager.applyTemplate('${templateId}')">
                                    应用模板
                                </button>
                            </div>
                            <p style="margin: 0 0 8px 0; color: #6b7280;">${template.description}</p>
                            <div style="font-size: 12px;">
                                包含权限：${template.permissions.length}个
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        this.showModal(modalContent);
    }

    applyTemplate(templateId) {
        const template = this.permissionTemplates[templateId];
        if (!template) return;

        if (this.currentMerchant) {
            this.currentMerchant.permissions = [...template.permissions];
            this.showNotification(`已应用${template.name}模板`, 'success');
            this.renderPermissionMatrix();
        }

        this.markAsChanged();
        this.closeModal();
    }

    savePermissions() {
        this.showNotification('权限配置已保存', 'success');
        this.hasChanges = false;
        this.hideChangesIndicator();
    }

    resetChanges() {
        if (confirm('确定要重置所有未保存的更改吗？')) {
            this.loadMerchants();
            this.showNotification('已重置更改', 'info');
            this.hasChanges = false;
            this.hideChangesIndicator();
        }
    }

    exportPermissions() {
        const data = {
            merchants: this.merchants,
            exportTime: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `merchant-permissions-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        this.showNotification('权限配置已导出', 'success');
    }

    showModal(content) {
        const modal = document.getElementById('modalOverlay');
        const modalContent = document.getElementById('modalContent');
        
        modalContent.innerHTML = content;
        modal.classList.add('show');
    }

    closeModal() {
        const modal = document.getElementById('modalOverlay');
        modal.classList.remove('show');
    }

    showCompareModal() {
        this.showNotification('权限对比功能开发中...', 'info');
    }

    showApprovalModal() {
        this.showNotification('审批中心功能开发中...', 'info');
    }

    showTemplateModal() {
        this.showTemplateSelector();
    }

    markAsChanged() {
        this.hasChanges = true;
        this.showChangesIndicator();
    }

    showChangesIndicator() {
        const indicator = document.getElementById('changesIndicator');
        if (indicator) {
            indicator.classList.add('show');
        }
    }

    hideChangesIndicator() {
        const indicator = document.getElementById('changesIndicator');
        if (indicator) {
            indicator.classList.remove('show');
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
} 