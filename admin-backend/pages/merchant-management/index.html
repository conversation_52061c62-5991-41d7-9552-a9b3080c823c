<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .merchant-management {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
            margin-top: 4px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.2s;
        }

        .stat-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-icon.merchants { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.pending { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.revenue { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-icon.commission { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
            margin-top: 4px;
        }

        .stat-change {
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 6px;
            margin-top: 8px;
            display: inline-block;
        }

        .stat-change.positive {
            background: #dcfce7;
            color: #166534;
        }

        .stat-change.negative {
            background: #fef2f2;
            color: #dc2626;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 32px;
        }

        .content-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .section-header {
            padding: 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .filter-bar {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            background: #f8fafc;
        }

        .filter-row {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
            text-transform: uppercase;
        }

        .filter-input, .filter-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            min-width: 140px;
        }

        .merchant-table {
            width: 100%;
            border-collapse: collapse;
        }

        .merchant-table th {
            background: #f8fafc;
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e2e8f0;
        }

        .merchant-table td {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .merchant-table tr:hover {
            background: #f8fafc;
        }

        .merchant-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .merchant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #64748b;
            overflow: hidden;
        }

        .merchant-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .merchant-details h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .merchant-details p {
            margin: 2px 0 0 0;
            font-size: 12px;
            color: #64748b;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.active {
            background: #dcfce7;
            color: #166534;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.suspended {
            background: #fef2f2;
            color: #dc2626;
        }

        .status-badge.rejected {
            background: #f3f4f6;
            color: #6b7280;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .quick-actions {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            padding: 24px;
        }

        .quick-actions h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .action-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .action-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid #e2e8f0;
        }

        .action-item:hover {
            background: #f8fafc;
            border-color: #3b82f6;
        }

        .action-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .action-icon.add { background: #10b981; }
        .action-icon.review { background: #f59e0b; }
        .action-icon.export { background: #6366f1; }
        .action-icon.settings { background: #8b5cf6; }

        .action-text h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .action-text p {
            margin: 2px 0 0 0;
            font-size: 12px;
            color: #64748b;
        }

        .recent-activities {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .activity-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
        }

        .activity-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .activity-list {
            padding: 16px 24px;
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            flex-shrink: 0;
        }

        .activity-icon.join { background: #10b981; }
        .activity-icon.approve { background: #3b82f6; }
        .activity-icon.reject { background: #ef4444; }
        .activity-icon.suspend { background: #f59e0b; }

        .activity-content {
            flex: 1;
        }

        .activity-content h4 {
            margin: 0;
            font-size: 13px;
            font-weight: 600;
            color: #1e293b;
        }

        .activity-content p {
            margin: 2px 0 0 0;
            font-size: 12px;
            color: #64748b;
        }

        .activity-time {
            font-size: 11px;
            color: #9ca3af;
            margin-top: 4px;
        }

        .pagination {
            padding: 20px 24px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .pagination-info {
            color: #64748b;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .page-btn:hover {
            background: #f9fafb;
        }

        .page-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .merchant-management {
                padding: 16px;
            }
            
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                width: 100%;
            }
            
            .merchant-table {
                font-size: 12px;
            }
            
            .merchant-table th,
            .merchant-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="merchant-management">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">商户管理</h1>
                <p class="page-subtitle">管理平台商户，审核入驻申请，监控商户运营状况</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="openMerchantModal('add')">
                    <i class="fas fa-plus"></i>
                    添加商户
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon merchants">
                        <i class="fas fa-store"></i>
                    </div>
                </div>
                <div class="stat-value">1,247</div>
                <div class="stat-label">总商户数</div>
                <div class="stat-change positive">+12% 本月</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value">23</div>
                <div class="stat-label">待审核</div>
                <div class="stat-change positive">+5 今日</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon revenue">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="stat-value">¥2.4M</div>
                <div class="stat-label">平台收入</div>
                <div class="stat-change positive">+18% 本月</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon commission">
                        <i class="fas fa-percentage"></i>
                    </div>
                </div>
                <div class="stat-value">¥156K</div>
                <div class="stat-label">佣金收入</div>
                <div class="stat-change positive">+24% 本月</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 商户列表 -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title">商户列表</h2>
                    <div class="header-actions">
                        <button class="btn btn-secondary" onclick="exportMerchants()">
                            <i class="fas fa-download"></i>
                            导出
                        </button>
                    </div>
                </div>

                <!-- 筛选栏 -->
                <div class="filter-bar">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label class="filter-label">商户名称</label>
                            <input type="text" class="filter-input" placeholder="搜索商户名称" id="merchantNameFilter">
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">状态</label>
                            <select class="filter-select" id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="active">正常营业</option>
                                <option value="pending">待审核</option>
                                <option value="suspended">已暂停</option>
                                <option value="rejected">已拒绝</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">商户类型</label>
                            <select class="filter-select" id="typeFilter">
                                <option value="">全部类型</option>
                                <option value="enterprise">企业商户</option>
                                <option value="individual">个人商户</option>
                                <option value="flagship">旗舰店</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">入驻时间</label>
                            <select class="filter-select" id="timeFilter">
                                <option value="">全部时间</option>
                                <option value="today">今日</option>
                                <option value="week">本周</option>
                                <option value="month">本月</option>
                                <option value="quarter">本季度</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="filterMerchants()">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                    </div>
                </div>

                <!-- 商户表格 -->
                <div style="overflow-x: auto;">
                    <table class="merchant-table">
                        <thead>
                            <tr>
                                <th>商户信息</th>
                                <th>商户类型</th>
                                <th>状态</th>
                                <th>入驻时间</th>
                                <th>商品数量</th>
                                <th>月销售额</th>
                                <th>佣金比例</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="merchantTableBody">
                            <!-- 商户数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <div class="pagination-info">
                        显示第 <span id="pageStart">1</span>-<span id="pageEnd">20</span> 条，共 <span id="totalCount">1,247</span> 条记录
                    </div>
                    <div class="pagination-controls" id="paginationControls">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 快速操作 -->
                <div class="quick-actions">
                    <h3>快速操作</h3>
                    <div class="action-list">
                        <div class="action-item" onclick="openMerchantModal('add')">
                            <div class="action-icon add">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="action-text">
                                <h4>添加商户</h4>
                                <p>手动添加新商户</p>
                            </div>
                        </div>
                        <div class="action-item" onclick="showPendingReviews()">
                            <div class="action-icon review">
                                <i class="fas fa-clipboard-check"></i>
                            </div>
                            <div class="action-text">
                                <h4>待审核商户</h4>
                                <p>查看待审核申请</p>
                            </div>
                        </div>
                        <div class="action-item" onclick="exportMerchants()">
                            <div class="action-icon export">
                                <i class="fas fa-file-export"></i>
                            </div>
                            <div class="action-text">
                                <h4>导出数据</h4>
                                <p>导出商户数据</p>
                            </div>
                        </div>
                        <div class="action-item" onclick="openMerchantSettings()">
                            <div class="action-icon settings">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="action-text">
                                <h4>商户设置</h4>
                                <p>管理商户规则</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="recent-activities">
                    <div class="activity-header">
                        <h3>最近活动</h3>
                    </div>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon join">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="activity-content">
                                <h4>新商户申请</h4>
                                <p>苹果官方旗舰店申请入驻</p>
                                <div class="activity-time">2分钟前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon approve">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="activity-content">
                                <h4>商户审核通过</h4>
                                <p>Nike官方旗舰店审核通过</p>
                                <div class="activity-time">15分钟前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon reject">
                                <i class="fas fa-times"></i>
                            </div>
                            <div class="activity-content">
                                <h4>商户申请被拒</h4>
                                <p>某商户资质不符被拒绝</p>
                                <div class="activity-time">1小时前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon suspend">
                                <i class="fas fa-pause"></i>
                            </div>
                            <div class="activity-content">
                                <h4>商户被暂停</h4>
                                <p>某商户违规被暂停营业</p>
                                <div class="activity-time">2小时前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../assets/js/image-error-handler.js"></script>
    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 20;
        let totalMerchants = 1247;
        let currentFilter = {};

        // 示例商户数据
        const sampleMerchants = [
            {
                id: 'M001',
                name: '苹果官方旗舰店',
                logo: '../../assets/images/apple-logo.png',
                type: 'flagship',
                status: 'active',
                joinDate: '2024-01-15',
                productCount: 156,
                monthlySales: 890000,
                commissionRate: 3.5,
                contact: '<EMAIL>',
                phone: '************'
            },
            {
                id: 'M002',
                name: 'Nike运动专营店',
                logo: '../../assets/images/nike-logo.png',
                type: 'enterprise',
                status: 'active',
                joinDate: '2024-01-20',
                productCount: 89,
                monthlySales: 456000,
                commissionRate: 5.0,
                contact: '<EMAIL>',
                phone: '************'
            },
            {
                id: 'M003',
                name: '小米科技专卖店',
                logo: '../../assets/images/xiaomi-logo.png',
                type: 'enterprise',
                status: 'pending',
                joinDate: '2024-03-10',
                productCount: 45,
                monthlySales: 0,
                commissionRate: 4.0,
                contact: '<EMAIL>',
                phone: '************'
            },
            {
                id: 'M004',
                name: '时尚服饰小店',
                logo: '../../assets/images/fashion-logo.png',
                type: 'individual',
                status: 'suspended',
                joinDate: '2024-02-05',
                productCount: 23,
                monthlySales: 12000,
                commissionRate: 8.0,
                contact: '<EMAIL>',
                phone: '138-0000-1234'
            }
        ];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadMerchants();
            initPagination();
        });

        // 加载商户数据
        function loadMerchants() {
            const tbody = document.getElementById('merchantTableBody');
            tbody.innerHTML = '';

            sampleMerchants.forEach(merchant => {
                const row = createMerchantRow(merchant);
                tbody.appendChild(row);
            });

            updatePaginationInfo();
        }

        // 创建商户行
        function createMerchantRow(merchant) {
            const row = document.createElement('tr');
            
            const statusClass = {
                'active': 'active',
                'pending': 'pending',
                'suspended': 'suspended',
                'rejected': 'rejected'
            }[merchant.status];

            const statusText = {
                'active': '正常营业',
                'pending': '待审核',
                'suspended': '已暂停',
                'rejected': '已拒绝'
            }[merchant.status];

            const typeText = {
                'flagship': '旗舰店',
                'enterprise': '企业商户',
                'individual': '个人商户'
            }[merchant.type];

            row.innerHTML = `
                <td>
                    <div class="merchant-info">
                        <div class="merchant-avatar">
                            <img src="${merchant.logo}" alt="${merchant.name}" onerror="this.parentElement.innerHTML='<i class=&quot;fas fa-store&quot;></i>'">
                        </div>
                        <div class="merchant-details">
                            <h4>${merchant.name}</h4>
                            <p>ID: ${merchant.id}</p>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="status-badge ${merchant.type}">${typeText}</span>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </td>
                <td>${merchant.joinDate}</td>
                <td>${merchant.productCount}</td>
                <td>¥${merchant.monthlySales.toLocaleString()}</td>
                <td>${merchant.commissionRate}%</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="viewMerchant('${merchant.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-secondary" onclick="editMerchant('${merchant.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${merchant.status === 'pending' ? `
                            <button class="btn btn-primary" onclick="approveMerchant('${merchant.id}')">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-danger" onclick="rejectMerchant('${merchant.id}')">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : `
                            <button class="btn btn-danger" onclick="suspendMerchant('${merchant.id}')">
                                <i class="fas fa-ban"></i>
                            </button>
                        `}
                    </div>
                </td>
            `;

            return row;
        }

        // 筛选商户
        function filterMerchants() {
            const nameFilter = document.getElementById('merchantNameFilter').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const timeFilter = document.getElementById('timeFilter').value;

            let filteredMerchants = sampleMerchants.filter(merchant => {
                const matchName = !nameFilter || merchant.name.toLowerCase().includes(nameFilter);
                const matchStatus = !statusFilter || merchant.status === statusFilter;
                const matchType = !typeFilter || merchant.type === typeFilter;
                // 时间筛选逻辑可以根据需要实现
                
                return matchName && matchStatus && matchType;
            });

            // 重新渲染表格
            const tbody = document.getElementById('merchantTableBody');
            tbody.innerHTML = '';
            filteredMerchants.forEach(merchant => {
                const row = createMerchantRow(merchant);
                tbody.appendChild(row);
            });
        }

        // 商户操作函数
        function viewMerchant(merchantId) {
            alert(`查看商户详情: ${merchantId}`);
            // 这里可以打开商户详情页面或模态框
        }

        function editMerchant(merchantId) {
            alert(`编辑商户: ${merchantId}`);
            // 这里可以打开编辑商户页面或模态框
        }

        function approveMerchant(merchantId) {
            if (confirm('确定要审核通过该商户吗？')) {
                alert(`商户 ${merchantId} 审核通过`);
                loadMerchants();
            }
        }

        function rejectMerchant(merchantId) {
            if (confirm('确定要拒绝该商户申请吗？')) {
                alert(`商户 ${merchantId} 申请被拒绝`);
                loadMerchants();
            }
        }

        function suspendMerchant(merchantId) {
            if (confirm('确定要暂停该商户营业吗？')) {
                alert(`商户 ${merchantId} 已暂停营业`);
                loadMerchants();
            }
        }

        // 其他功能函数
        function openMerchantModal(action) {
            alert(`打开商户${action === 'add' ? '添加' : '编辑'}模态框`);
        }

        function showPendingReviews() {
            document.getElementById('statusFilter').value = 'pending';
            filterMerchants();
        }

        function exportMerchants() {
            alert('导出商户数据功能开发中...');
        }

        function openMerchantSettings() {
            alert('商户设置功能开发中...');
        }

        // 分页相关函数
        function initPagination() {
            const controls = document.getElementById('paginationControls');
            controls.innerHTML = `
                <button class="page-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>
                <button class="page-btn active">1</button>
                <button class="page-btn" onclick="changePage(2)">2</button>
                <button class="page-btn" onclick="changePage(3)">3</button>
                <button class="page-btn" onclick="changePage(${currentPage + 1})">下一页</button>
            `;
        }

        function changePage(page) {
            if (page < 1) return;
            currentPage = page;
            loadMerchants();
            initPagination();
        }

        function updatePaginationInfo() {
            const start = (currentPage - 1) * pageSize + 1;
            const end = Math.min(currentPage * pageSize, totalMerchants);
            
            document.getElementById('pageStart').textContent = start;
            document.getElementById('pageEnd').textContent = end;
            document.getElementById('totalCount').textContent = totalMerchants;
        }
    </script>
</body>
</html> 