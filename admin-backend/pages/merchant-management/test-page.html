<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级营销管理测试页面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            margin-bottom: 24px;
            text-align: center;
        }
        .test-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        .test-description {
            color: #64748b;
            font-size: 14px;
        }
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .test-btn.primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        .test-btn.secondary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .test-btn.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        .test-results {
            background: #f8fafc;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .test-results h3 {
            margin: 0 0 12px 0;
            color: #1e293b;
            font-size: 16px;
        }
        .test-log {
            background: #1e293b;
            color: #f8fafc;
            padding: 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
        .status-info { background: #3b82f6; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">
                <i class="fas fa-vial"></i>
                高级营销管理功能测试
            </h1>
            <p class="test-description">
                点击下面的按钮测试各营销工具的点击功能是否正常工作
            </p>
        </div>

        <div class="test-buttons">
            <button class="test-btn primary" onclick="testFunction('openCouponManagement')">
                <i class="fas fa-tags"></i>
                测试优惠券管理
            </button>
            <button class="test-btn primary" onclick="testFunction('openCreateCoupon')">
                <i class="fas fa-plus"></i>
                测试创建优惠券
            </button>
            <button class="test-btn secondary" onclick="testFunction('openMemberMarketing')">
                <i class="fas fa-users"></i>
                测试会员营销
            </button>
            <button class="test-btn secondary" onclick="testFunction('openMemberLevelConfig')">
                <i class="fas fa-layer-group"></i>
                测试会员等级配置
            </button>
            <button class="test-btn warning" onclick="testFunction('openFlashSale')">
                <i class="fas fa-bolt"></i>
                测试限时秒杀
            </button>
            <button class="test-btn warning" onclick="testFunction('openAddFlashSaleProduct')">
                <i class="fas fa-plus-circle"></i>
                测试新增秒杀商品
            </button>
            <button class="test-btn" onclick="testFunction('openSocialShare')">
                <i class="fas fa-share-alt"></i>
                测试社交分享
            </button>
            <button class="test-btn" onclick="testFunction('openShareRewardConfig')">
                <i class="fas fa-gift"></i>
                测试分享奖励配置
            </button>
            <button class="test-btn primary" onclick="testFunction('openPointsMall')">
                <i class="fas fa-shopping-cart"></i>
                测试积分商城
            </button>
            <button class="test-btn primary" onclick="testFunction('openPointsConfig')">
                <i class="fas fa-cog"></i>
                测试积分设置
            </button>
            <button class="test-btn secondary" onclick="testFunction('openTargetedPush')">
                <i class="fas fa-bullseye"></i>
                测试精准推送
            </button>
            <button class="test-btn secondary" onclick="testFunction('openCreatePush')">
                <i class="fas fa-paper-plane"></i>
                测试创建推送
            </button>
        </div>

        <div class="test-results">
            <h3>测试结果</h3>
            <div class="test-log" id="testLog">
                <div>等待测试...</div>
            </div>
        </div>

        <div style="margin-top: 24px; text-align: center;">
            <button class="test-btn" onclick="clearLog()">
                <i class="fas fa-trash"></i>
                清空日志
            </button>
            <button class="test-btn primary" onclick="openMarketingPage()">
                <i class="fas fa-external-link-alt"></i>
                打开高级营销管理页面
            </button>
        </div>
    </div>

    <script>
        // 模拟营销管理函数
        window.openCouponManagement = function() {
            logResult('success', 'openCouponManagement', '优惠券管理功能正常');
        };

        window.openCreateCoupon = function() {
            logResult('success', 'openCreateCoupon', '创建优惠券功能正常');
        };

        window.openMemberMarketing = function() {
            logResult('success', 'openMemberMarketing', '会员营销功能正常');
        };

        window.openMemberLevelConfig = function() {
            logResult('success', 'openMemberLevelConfig', '会员等级配置功能正常');
        };

        window.openFlashSale = function() {
            logResult('success', 'openFlashSale', '限时秒杀功能正常');
        };

        window.openAddFlashSaleProduct = function() {
            logResult('success', 'openAddFlashSaleProduct', '新增秒杀商品功能正常');
        };

        window.openSocialShare = function() {
            logResult('success', 'openSocialShare', '社交分享功能正常');
        };

        window.openShareRewardConfig = function() {
            logResult('success', 'openShareRewardConfig', '分享奖励配置功能正常');
        };

        window.openPointsMall = function() {
            logResult('success', 'openPointsMall', '积分商城功能正常');
        };

        window.openPointsConfig = function() {
            logResult('success', 'openPointsConfig', '积分设置功能正常');
        };

        window.openTargetedPush = function() {
            logResult('success', 'openTargetedPush', '精准推送功能正常');
        };

        window.openCreatePush = function() {
            logResult('success', 'openCreatePush', '创建推送功能正常');
        };

        function testFunction(functionName) {
            logResult('info', '测试开始', `正在测试 ${functionName} 函数...`);
            
            try {
                if (typeof window[functionName] === 'function') {
                    window[functionName]();
                } else {
                    logResult('error', functionName, '函数未定义或不存在');
                }
            } catch (error) {
                logResult('error', functionName, `执行错误: ${error.message}`);
            }
        }

        function logResult(type, functionName, message) {
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'success' ? 'status-success' : 
                               type === 'error' ? 'status-error' : 'status-info';
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `
                <span class="status-indicator ${statusClass}"></span>
                [${timestamp}] ${functionName}: ${message}
            `;
            
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '<div>日志已清空，等待测试...</div>';
        }

        function openMarketingPage() {
            window.open('advanced-marketing.html', '_blank');
        }

        // 初始化日志
        logResult('info', '系统', '测试页面已加载，可以开始测试');
    </script>
</body>
</html> 