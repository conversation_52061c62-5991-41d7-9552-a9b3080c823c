        function switchTab(tabId, buttonElement) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.style.display = 'none';
            });
            
            // 移除所有按钮的激活状态
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabId).style.display = 'block';
            
            // 激活选中的按钮
            buttonElement.classList.add('active');
        }
        
        function editMerchant() {
            alert('编辑商家资料功能');
        }
        
        function suspendMerchant() {
            if (confirm('确定要暂停该商家营业吗？')) {
                alert('商家已暂停营业');
            }
        }
        
        function freezeMerchant() {
            if (confirm('确定要冻结该商家账户吗？此操作需要谨慎！')) {
                alert('商家账户已冻结');
            }
        }
