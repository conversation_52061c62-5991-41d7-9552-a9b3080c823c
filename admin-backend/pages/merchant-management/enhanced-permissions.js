/**
 * 增强版商户权限管理系统
 * Enhanced Merchant Permission Management System
 */

class EnhancedPermissionManager {
    constructor() {
        this.selectedMerchants = new Set();
        this.currentMerchant = null;
        this.permissionMatrix = {};
        this.permissionDependencies = {};
        this.permissionTemplates = {};
        this.isBulkMode = false;
        this.hasUnsavedChanges = false;
        this.filterPanel = null;
        
        this.init();
    }

    init() {
        this.initData();
        this.initElements();
        this.bindEvents();
        this.renderMerchantsList();
    }

    // 初始化数据
    initData() {
        // 权限依赖关系定义
        this.permissionDependencies = {
            'product_edit': ['product_view'],
            'product_delete': ['product_edit'],
            'inventory_management': ['product_view'],
            'price_management': ['product_edit'],
            'order_process': ['order_view'],
            'shipping_management': ['order_process'],
            'refund_process': ['order_view', 'finance_view'],
            'customer_export': ['customer_view'],
            'advanced_marketing': ['basic_marketing'],
            'api_access': ['advanced_analytics'],
            'financial_export': ['financial_view']
        };

        // 权限矩阵定义
        this.permissionMatrix = {
            'products': {
                name: '商品管理',
                icon: 'box',
                permissions: {
                    'product_add': { name: '商品发布', needsApproval: false },
                    'product_view': { name: '商品查看', needsApproval: false },
                    'product_edit': { name: '商品编辑', needsApproval: false },
                    'product_delete': { name: '商品删除', needsApproval: true },
                    'inventory_management': { name: '库存管理', needsApproval: false },
                    'price_management': { name: '价格管理', needsApproval: true }
                }
            },
            'orders': {
                name: '订单管理',
                icon: 'shopping-cart',
                permissions: {
                    'order_view': { name: '查看订单', needsApproval: false },
                    'order_process': { name: '处理订单', needsApproval: false },
                    'shipping_management': { name: '发货管理', needsApproval: false },
                    'refund_process': { name: '退款处理', needsApproval: true },
                    'after_sales': { name: '售后服务', needsApproval: false }
                }
            },
            'customers': {
                name: '客户管理',
                icon: 'users',
                permissions: {
                    'customer_view': { name: '查看客户信息', needsApproval: false },
                    'customer_group': { name: '客户分组', needsApproval: false },
                    'customer_tag': { name: '客户标签', needsApproval: true },
                    'customer_chat': { name: '客服聊天', needsApproval: false },
                    'customer_export': { name: '客户导出', needsApproval: true }
                }
            },
            'marketing': {
                name: '营销推广',
                icon: 'bullhorn',
                permissions: {
                    'coupon_create': { name: '优惠券创建', needsApproval: false },
                    'activity_plan': { name: '活动策划', needsApproval: false },
                    'live_streaming': { name: '直播带货', needsApproval: true },
                    'ads_placement': { name: '广告投放', needsApproval: true },
                    'social_share': { name: '社交分享', needsApproval: false }
                }
            },
            'analytics': {
                name: '数据分析',
                icon: 'chart-bar',
                permissions: {
                    'sales_report': { name: '销售报表', needsApproval: false },
                    'traffic_analysis': { name: '流量分析', needsApproval: false },
                    'product_analysis': { name: '商品分析', needsApproval: false },
                    'customer_analysis': { name: '客户分析', needsApproval: true },
                    'competitor_analysis': { name: '竞品分析', needsApproval: true }
                }
            },
            'finance': {
                name: '财务管理',
                icon: 'coins',
                permissions: {
                    'income_view': { name: '收入查看', needsApproval: false },
                    'withdrawal_apply': { name: '提现申请', needsApproval: false },
                    'bill_detail': { name: '账单明细', needsApproval: false },
                    'tax_management': { name: '税务管理', needsApproval: true },
                    'financial_export': { name: '财务导出', needsApproval: true }
                }
            },
            'settings': {
                name: '系统设置',
                icon: 'cog',
                permissions: {
                    'basic_settings': { name: '基础设置', needsApproval: false },
                    'notification_settings': { name: '通知设置', needsApproval: false },
                    'security_settings': { name: '安全设置', needsApproval: true },
                    'api_access': { name: 'API接口', needsApproval: true },
                    'advanced_config': { name: '高级配置', needsApproval: true }
                }
            }
        };

        // 权限模板定义
        this.permissionTemplates = {
            'basic': {
                name: '基础商户',
                description: '基本商品管理和订单处理',
                permissions: ['product_add', 'product_view', 'product_edit', 'inventory_management', 'order_view', 'order_process']
            },
            'standard': {
                name: '标准商户',
                description: '完整商品管理、营销和数据分析',
                permissions: ['product_add', 'product_view', 'product_edit', 'inventory_management', 'order_view', 'order_process', 'shipping_management', 'customer_view', 'customer_group', 'coupon_create', 'activity_plan', 'sales_report', 'traffic_analysis']
            },
            'advanced': {
                name: '高级商户',
                description: '所有功能包括高级分析和API',
                permissions: ['product_add', 'product_view', 'product_edit', 'product_delete', 'inventory_management', 'price_management', 'order_view', 'order_process', 'shipping_management', 'refund_process', 'customer_view', 'customer_group', 'customer_tag', 'customer_chat', 'coupon_create', 'activity_plan', 'live_streaming', 'ads_placement', 'social_share', 'sales_report', 'traffic_analysis', 'product_analysis', 'customer_analysis', 'income_view', 'withdrawal_apply', 'bill_detail']
            },
            'enterprise': {
                name: '企业定制',
                description: '企业级功能和定制化服务',
                permissions: Object.keys(this.getAllPermissions())
            }
        };

        // 模拟商户数据
        this.merchantsData = [
            { id: 'M001234', name: '星河数码专营店', type: 'enterprise', status: 'active', joinDate: '2024-01-15', permissionCount: 25 },
            { id: 'M001235', name: '优品生活馆', type: 'individual', status: 'active', joinDate: '2024-02-10', permissionCount: 15 },
            { id: 'M001236', name: '时尚潮牌屋', type: 'enterprise', status: 'pending', joinDate: '2024-03-05', permissionCount: 8 },
            { id: 'M001237', name: '美食天地', type: 'enterprise', status: 'active', joinDate: '2024-01-20', permissionCount: 30 },
            { id: 'M001238', name: '健康生活', type: 'individual', status: 'active', joinDate: '2024-03-12', permissionCount: 12 },
            { id: 'M001239', name: '科技前沿', type: 'brand', status: 'active', joinDate: '2024-02-28', permissionCount: 28 },
            { id: 'M001240', name: '家居精品', type: 'enterprise', status: 'suspended', joinDate: '2024-01-08', permissionCount: 20 }
        ];
    }

    // 初始化DOM元素
    initElements() {
        this.filterPanel = document.getElementById('filterPanel');
        this.bulkToolbar = document.getElementById('bulkToolbar');
        this.merchantsList = document.getElementById('merchantsList');
        this.permissionsGrid = document.getElementById('permissionsGrid');
        this.searchInput = document.getElementById('searchInput');
        this.selectedMerchantInfo = document.getElementById('selectedMerchantInfo');
        this.changesIndicator = document.getElementById('changesIndicator');
        this.notification = document.getElementById('notification');
        this.modalOverlay = document.getElementById('modalOverlay');
    }

    // 绑定事件
    bindEvents() {
        // 搜索功能
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.searchMerchants(e.target.value);
            });
        }

        // 筛选器变化事件
        ['merchantTypeFilter', 'merchantStatusFilter', 'permissionCountFilter', 'joinDateFilter'].forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', () => {
                    this.applyFilters();
                });
            }
        });
    }

    // 获取所有权限
    getAllPermissions() {
        const permissions = {};
        Object.values(this.permissionMatrix).forEach(category => {
            Object.assign(permissions, category.permissions);
        });
        return permissions;
    }

    // 渲染商户列表
    renderMerchantsList() {
        if (!this.merchantsList) return;

        const html = this.merchantsData.map(merchant => `
            <div class="merchant-item ${this.isBulkMode ? 'selectable' : ''}" 
                 data-merchant-id="${merchant.id}" 
                 onclick="permissionManager.selectMerchant('${merchant.id}')">
                ${this.isBulkMode ? `<input type="checkbox" class="merchant-checkbox" onchange="permissionManager.toggleMerchantSelection('${merchant.id}', event)">` : ''}
                <div class="merchant-name">${merchant.name}</div>
                <div class="merchant-info">ID: ${merchant.id} | ${this.getMerchantTypeText(merchant.type)}</div>
                <div class="merchant-status ${merchant.status}">${this.getStatusText(merchant.status)}</div>
            </div>
        `).join('');

        this.merchantsList.innerHTML = html;
    }

    // 渲染权限网格
    renderPermissionsGrid() {
        if (!this.permissionsGrid || !this.currentMerchant) return;

        const html = Object.entries(this.permissionMatrix).map(([categoryId, category]) => `
            <div class="permission-card">
                <div class="permission-header">
                    <div class="permission-info">
                        <div class="permission-icon ${categoryId}">
                            <i class="fas fa-${category.icon}"></i>
                        </div>
                        <h4 class="permission-name">${category.name}</h4>
                    </div>
                    <div class="section-toggle">
                        <div class="toggle-switch ${this.isCategoryEnabled(categoryId) ? 'active' : ''}" 
                             onclick="permissionManager.toggleCategory('${categoryId}')"></div>
                    </div>
                </div>
                <ul class="permission-items">
                    ${Object.entries(category.permissions).map(([permId, permission]) => `
                        <li class="permission-item ${this.getPermissionClasses(permId)}">
                            <div class="permission-label">
                                ${permission.name}
                                ${permission.needsApproval ? '<span class="approval-status pending">需审批</span>' : ''}
                                ${this.getPermissionDependencyText(permId)}
                            </div>
                            <input type="checkbox" class="permission-checkbox" 
                                   ${this.isPermissionEnabled(permId) ? 'checked' : ''}
                                   ${this.isPermissionBlocked(permId) ? 'disabled' : ''}
                                   onchange="permissionManager.togglePermission('${permId}')">
                        </li>
                    `).join('')}
                </ul>
            </div>
        `).join('');

        this.permissionsGrid.innerHTML = html;
    }

    // 选择商户
    selectMerchant(merchantId) {
        if (this.isBulkMode) return;

        const merchant = this.merchantsData.find(m => m.id === merchantId);
        if (!merchant) return;

        this.currentMerchant = merchant;

        // 更新选中状态
        document.querySelectorAll('.merchant-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-merchant-id="${merchantId}"]`).classList.add('active');

        // 更新商户信息显示
        this.updateSelectedMerchantInfo(merchant);

        // 渲染权限网格
        this.renderPermissionsGrid();
    }

    // 更新选中商户信息
    updateSelectedMerchantInfo(merchant) {
        if (!this.selectedMerchantInfo) return;

        this.selectedMerchantInfo.innerHTML = `
            <div class="merchant-avatar">${merchant.name.charAt(0)}</div>
            <div class="merchant-details">
                <h3>${merchant.name}</h3>
                <p>${this.getMerchantTypeText(merchant.type)} • ID: ${merchant.id} • 注册时间: ${merchant.joinDate}</p>
            </div>
        `;
    }

    // 切换标签页
    switchTab(event, tabName) {
        // 隐藏所有内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.style.display = 'none';
        });

        // 移除所有活动状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });

        // 显示选中的内容
        const targetContent = document.getElementById(tabName + '-content');
        if (targetContent) {
            targetContent.style.display = 'block';
        }

        // 添加活动状态
        event.target.classList.add('active');

        // 根据标签页渲染对应内容
        switch (tabName) {
            case 'template':
                this.renderTemplatesGrid();
                break;
            case 'approval':
                this.renderApprovalList();
                break;
            case 'activity':
                this.renderActivityLog();
                break;
        }
    }

    // 切换筛选面板
    toggleFilterPanel() {
        if (!this.filterPanel) return;
        
        const isVisible = this.filterPanel.classList.contains('show');
        this.filterPanel.classList.toggle('show', !isVisible);
        
        const toggleBtn = document.querySelector('.filter-toggle');
        if (toggleBtn) {
            toggleBtn.classList.toggle('active', !isVisible);
        }
    }

    // 切换批量模式
    toggleBulkMode() {
        this.isBulkMode = !this.isBulkMode;
        
        if (this.isBulkMode) {
            this.bulkToolbar.classList.add('show');
        } else {
            this.bulkToolbar.classList.remove('show');
            this.selectedMerchants.clear();
        }
        
        this.renderMerchantsList();
        this.updateSelectedCount();
    }

    // 切换商户选择
    toggleMerchantSelection(merchantId, event) {
        event.stopPropagation();
        
        if (this.selectedMerchants.has(merchantId)) {
            this.selectedMerchants.delete(merchantId);
        } else {
            this.selectedMerchants.add(merchantId);
        }
        
        this.updateSelectedCount();
        this.updateMerchantSelectionUI();
    }

    // 更新选中数量显示
    updateSelectedCount() {
        const selectedCountEl = document.getElementById('selectedCount');
        if (selectedCountEl) {
            selectedCountEl.textContent = this.selectedMerchants.size;
        }
    }

    // 更新商户选择UI
    updateMerchantSelectionUI() {
        this.selectedMerchants.forEach(merchantId => {
            const item = document.querySelector(`[data-merchant-id="${merchantId}"]`);
            if (item) {
                item.classList.add('selected');
            }
        });
    }

    // 应用筛选
    applyFilters() {
        const typeFilter = document.getElementById('merchantTypeFilter')?.value;
        const statusFilter = document.getElementById('merchantStatusFilter')?.value;
        const permissionFilter = document.getElementById('permissionCountFilter')?.value;
        const dateFilter = document.getElementById('joinDateFilter')?.value;

        let filteredData = [...this.merchantsData];

        if (typeFilter) {
            filteredData = filteredData.filter(m => m.type === typeFilter);
        }

        if (statusFilter) {
            filteredData = filteredData.filter(m => m.status === statusFilter);
        }

        if (permissionFilter) {
            filteredData = filteredData.filter(m => {
                switch (permissionFilter) {
                    case 'basic': return m.permissionCount <= 10;
                    case 'standard': return m.permissionCount > 10 && m.permissionCount <= 20;
                    case 'advanced': return m.permissionCount > 20 && m.permissionCount <= 30;
                    case 'full': return m.permissionCount > 30;
                    default: return true;
                }
            });
        }

        // 这里可以添加日期筛选逻辑

        this.renderFilteredMerchants(filteredData);
    }

    // 渲染筛选后的商户
    renderFilteredMerchants(merchants) {
        if (!this.merchantsList) return;

        const html = merchants.map(merchant => `
            <div class="merchant-item ${this.isBulkMode ? 'selectable' : ''}" 
                 data-merchant-id="${merchant.id}" 
                 onclick="permissionManager.selectMerchant('${merchant.id}')">
                ${this.isBulkMode ? `<input type="checkbox" class="merchant-checkbox" onchange="permissionManager.toggleMerchantSelection('${merchant.id}', event)">` : ''}
                <div class="merchant-name">${merchant.name}</div>
                <div class="merchant-info">ID: ${merchant.id} | ${this.getMerchantTypeText(merchant.type)}</div>
                <div class="merchant-status ${merchant.status}">${this.getStatusText(merchant.status)}</div>
            </div>
        `).join('');

        this.merchantsList.innerHTML = html;
        
        // 更新商户数量
        const merchantCount = document.getElementById('merchantCount');
        if (merchantCount) {
            merchantCount.textContent = merchants.length;
        }
    }

    // 搜索商户
    searchMerchants(keyword) {
        if (!keyword.trim()) {
            this.renderMerchantsList();
            return;
        }

        const filteredData = this.merchantsData.filter(merchant => 
            merchant.name.toLowerCase().includes(keyword.toLowerCase()) ||
            merchant.id.toLowerCase().includes(keyword.toLowerCase()) ||
            this.getMerchantTypeText(merchant.type).includes(keyword)
        );

        this.renderFilteredMerchants(filteredData);
    }

    // 保存权限配置
    savePermissions() {
        if (!this.currentMerchant) {
            this.showNotification('请先选择商户', 'warning');
            return;
        }

        const saveBtn = document.querySelector('.actions-bar .btn-primary .save-text');
        if (saveBtn) {
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        }

        // 模拟保存过程
        setTimeout(() => {
            this.hasUnsavedChanges = false;
            this.changesIndicator.classList.remove('show');
            
            if (saveBtn) {
                saveBtn.innerHTML = '<i class="fas fa-check"></i> 保存成功';
            }
            
            this.showNotification('权限配置已保存', 'success');
            
            setTimeout(() => {
                if (saveBtn) {
                    saveBtn.textContent = '保存权限配置';
                }
            }, 2000);
        }, 1500);
    }

    // 显示通知
    showNotification(message, type = 'success') {
        if (!this.notification) return;

        const iconMap = {
            success: 'check',
            error: 'times',
            warning: 'exclamation-triangle',
            info: 'info'
        };

        this.notification.className = `notification ${type}`;
        this.notification.querySelector('.notification-icon i').className = `fas fa-${iconMap[type]}`;
        this.notification.querySelector('.notification-text').textContent = message;
        
        this.notification.classList.add('show');
        
        setTimeout(() => {
            this.notification.classList.remove('show');
        }, 3000);
    }

    // 工具方法
    getMerchantTypeText(type) {
        const typeMap = {
            'enterprise': '企业商户',
            'individual': '个人商户',
            'brand': '品牌商户'
        };
        return typeMap[type] || type;
    }

    getStatusText(status) {
        const statusMap = {
            'active': '正常营业',
            'pending': '审核中',
            'suspended': '已暂停'
        };
        return statusMap[status] || status;
    }

    isCategoryEnabled(categoryId) {
        // 这里应该根据当前商户的权限配置来判断
        return Math.random() > 0.3; // 模拟数据
    }

    isPermissionEnabled(permissionId) {
        // 这里应该根据当前商户的权限配置来判断
        return Math.random() > 0.4; // 模拟数据
    }

    isPermissionBlocked(permissionId) {
        // 检查权限依赖关系
        const dependencies = this.permissionDependencies[permissionId];
        if (!dependencies) return false;
        
        return dependencies.some(dep => !this.isPermissionEnabled(dep));
    }

    getPermissionClasses(permissionId) {
        let classes = '';
        
        if (this.isPermissionBlocked(permissionId)) {
            classes += ' blocked';
        } else if (this.permissionDependencies[permissionId]) {
            classes += ' dependent';
        }
        
        return classes;
    }

    getPermissionDependencyText(permissionId) {
        const dependencies = this.permissionDependencies[permissionId];
        if (!dependencies) return '';
        
        return `<div class="permission-dependency">依赖: ${dependencies.join(', ')}</div>`;
    }

    // 标记有未保存的变更
    markAsChanged() {
        this.hasUnsavedChanges = true;
        this.changesIndicator.classList.add('show');
    }

    // 重置变更
    resetChanges() {
        this.hasUnsavedChanges = false;
        this.changesIndicator.classList.remove('show');
        this.renderPermissionsGrid();
        this.showNotification('已重置所有变更', 'info');
    }
}

// 全局函数定义
let permissionManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    permissionManager = new EnhancedPermissionManager();
    
    // 设置全局函数引用
    window.permissionManager = permissionManager;
});

// 全局函数
function switchTab(event, tabName) {
    permissionManager.switchTab(event, tabName);
}

function toggleFilterPanel() {
    permissionManager.toggleFilterPanel();
}

function toggleBulkMode() {
    permissionManager.toggleBulkMode();
}

function applyFilters() {
    permissionManager.applyFilters();
}

function resetFilters() {
    // 重置所有筛选器
    ['merchantTypeFilter', 'merchantStatusFilter', 'permissionCountFilter', 'joinDateFilter'].forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) filter.value = '';
    });
    
    permissionManager.renderMerchantsList();
    permissionManager.showNotification('筛选条件已重置', 'info');
}

function savePermissions() {
    permissionManager.savePermissions();
}

function resetChanges() {
    permissionManager.resetChanges();
}

function exportPermissions() {
    permissionManager.showNotification('权限报告导出中...', 'info');
    // 实际导出逻辑
}

function showCompareModal() {
    permissionManager.showNotification('权限对比功能开发中...', 'info');
}

function showTemplateModal() {
    permissionManager.showNotification('权限模板创建功能开发中...', 'info');
}

function showApprovalModal() {
    permissionManager.showNotification('审批管理功能开发中...', 'info');
}

function bulkEnablePermissions() {
    if (permissionManager.selectedMerchants.size === 0) {
        permissionManager.showNotification('请先选择商户', 'warning');
        return;
    }
    permissionManager.showNotification(`已为 ${permissionManager.selectedMerchants.size} 个商户批量启用权限`, 'success');
}

function bulkDisablePermissions() {
    if (permissionManager.selectedMerchants.size === 0) {
        permissionManager.showNotification('请先选择商户', 'warning');
        return;
    }
    permissionManager.showNotification(`已为 ${permissionManager.selectedMerchants.size} 个商户批量禁用权限`, 'success');
}

function bulkApplyTemplate() {
    if (permissionManager.selectedMerchants.size === 0) {
        permissionManager.showNotification('请先选择商户', 'warning');
        return;
    }
    permissionManager.showNotification(`已为 ${permissionManager.selectedMerchants.size} 个商户应用权限模板`, 'success');
}

function showDependencyInfo() {
    permissionManager.showNotification('权限依赖关系：某些权限需要先启用其依赖权限才能使用', 'info');
}

function exportLogs() {
    permissionManager.showNotification('操作日志导出中...', 'info');
}

function bulkApprove() {
    permissionManager.showNotification('批量审批功能开发中...', 'info');
}

function hideModal() {
    const modal = document.getElementById('modalOverlay');
    if (modal) {
        modal.classList.remove('show');
    }
}

function showCreateTemplateModal() {
    permissionManager.showNotification('创建自定义模板功能开发中...', 'info');
} 