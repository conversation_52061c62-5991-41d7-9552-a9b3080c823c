<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户权限管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 8px;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: var(--gray-50);
            line-height: 1.5;
        }

        .permissions-page {
            padding: 24px;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            margin-bottom: 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .page-subtitle {
            color: var(--gray-500);
            font-size: 16px;
            margin-top: 4px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 主要布局 */
        .main-container {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 24px;
        }

        /* 侧边栏 */
        .sidebar {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            height: fit-content;
            position: sticky;
            top: 24px;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .merchant-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 搜索和筛选 */
        .search-section {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-200);
        }

        .search-input {
            width: 100%;
            padding: 10px 14px 10px 40px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 14px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="%23999"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>') no-repeat 12px center;
            background-size: 16px;
            transition: border-color 0.2s;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .filter-options {
            margin-top: 12px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .filter-tag {
            padding: 4px 8px;
            background: var(--gray-100);
            border: 1px solid var(--gray-300);
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-tag.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 商户列表 */
        .merchants-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .merchant-item {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-100);
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .merchant-item:hover {
            background: var(--gray-50);
        }

        .merchant-item.active {
            background: #eff6ff;
            border-left: 3px solid var(--primary-color);
        }

        .merchant-item:last-child {
            border-bottom: none;
        }

        .merchant-checkbox {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 16px;
            height: 16px;
        }

        .merchant-info {
            padding-right: 24px;
        }

        .merchant-name {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 4px;
            font-size: 14px;
        }

        .merchant-details {
            font-size: 12px;
            color: var(--gray-500);
            margin-bottom: 8px;
        }

        .merchant-badges {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .merchant-status {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
        }

        .merchant-status.active {
            background: #dcfce7;
            color: #166534;
        }

        .merchant-status.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .merchant-status.suspended {
            background: #fecaca;
            color: #dc2626;
        }

        .merchant-type {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
            background: var(--gray-200);
            color: var(--gray-700);
        }

        /* 权限内容区 */
        .permissions-content {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .content-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .selected-merchant-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .merchant-avatar {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 18px;
        }

        .merchant-details-panel h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0 0 4px 0;
        }

        .merchant-details-panel p {
            font-size: 14px;
            color: var(--gray-500);
            margin: 0;
        }

        /* 批量操作栏 */
        .bulk-actions {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            padding: 12px 24px;
            display: none;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }

        .bulk-actions.show {
            display: flex;
        }

        .bulk-info {
            font-size: 14px;
            color: #1e40af;
            font-weight: 500;
        }

        .bulk-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        /* 权限矩阵 */
        .permissions-matrix {
            padding: 24px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: var(--gray-300);
        }

        .permission-category {
            margin-bottom: 32px;
        }

        .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding: 12px 16px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-200);
        }

        .category-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-900);
        }

        .category-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .category-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--gray-600);
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 12px;
        }

        .permission-item {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 16px;
            transition: all 0.2s;
            position: relative;
        }

        .permission-item:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }

        .permission-item.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .permission-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .permission-name {
            font-weight: 500;
            color: var(--gray-900);
            font-size: 14px;
        }

        .permission-checkbox {
            width: 18px;
            height: 18px;
        }

        .permission-description {
            font-size: 12px;
            color: var(--gray-600);
            margin-bottom: 8px;
        }

        .permission-meta {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .permission-tag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
        }

        .permission-tag.required {
            background: #fef3c7;
            color: #92400e;
        }

        .permission-tag.approval {
            background: #fef2f2;
            color: #dc2626;
        }

        .permission-tag.dependency {
            background: #e0e7ff;
            color: #3730a3;
        }

        /* 审批状态 */
        .approval-status {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
        }

        .approval-status.pending {
            background: var(--warning-color);
            color: white;
        }

        .approval-status.approved {
            background: var(--success-color);
            color: white;
        }

        .approval-status.rejected {
            background: var(--error-color);
            color: white;
        }

        /* 模态框 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            max-width: 600px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-md);
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--gray-400);
            cursor: pointer;
            padding: 4px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--gray-200);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 通知 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1100;
            padding: 16px 20px;
            border-radius: var(--border-radius);
            color: white;
            font-size: 14px;
            font-weight: 500;
            min-width: 300px;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }

        .notification.error {
            background: var(--error-color);
        }

        .notification.info {
            background: var(--primary-color);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .main-container {
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .permissions-page {
                padding: 16px;
            }

            .header-content {
                flex-direction: column;
                align-items: stretch;
            }

            .permissions-grid {
                grid-template-columns: 1fr;
            }

            .bulk-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .bulk-buttons {
                justify-content: center;
            }
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px;
            color: var(--gray-500);
        }

        .loading i {
            animation: spin 1s linear infinite;
            font-size: 24px;
            margin-bottom: 12px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 变更指示器 */
        .changes-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--warning-color);
            color: white;
            padding: 12px 20px;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 500;
            display: none;
            align-items: center;
            gap: 8px;
            box-shadow: var(--shadow-md);
        }

        .changes-indicator.show {
            display: flex;
        }
    </style>
</head>
<body>
    <div class="permissions-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div>
                    <h1 class="page-title">商户权限管理</h1>
                    <p class="page-subtitle">统一管理商户权限，支持批量操作和审批流程</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="exportPermissions()">
                        <i class="fas fa-download"></i>
                        导出配置
                    </button>
                    <button class="btn btn-secondary" onclick="showTemplateModal()">
                        <i class="fas fa-copy"></i>
                        权限模板
                    </button>
                    <button class="btn btn-secondary" onclick="showCompareModal()">
                        <i class="fas fa-balance-scale"></i>
                        权限对比
                    </button>
                    <button class="btn btn-warning" onclick="showApprovalModal()">
                        <i class="fas fa-clipboard-check"></i>
                        审批中心
                    </button>
                    <button class="btn btn-primary" onclick="savePermissions()">
                        <i class="fas fa-save"></i>
                        保存更改
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <h2 class="sidebar-title">商户列表</h2>
                    <span class="merchant-count" id="merchantCount">0</span>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-section">
                    <input type="text" class="search-input" placeholder="搜索商户名称或ID..." 
                           id="merchantSearch" oninput="searchMerchants(this.value)">
                    
                    <div class="filter-options">
                        <div class="filter-tag active" data-filter="all">全部</div>
                        <div class="filter-tag" data-filter="active">正常营业</div>
                        <div class="filter-tag" data-filter="pending">待审核</div>
                        <div class="filter-tag" data-filter="premium">高级商户</div>
                    </div>
                </div>

                <!-- 商户列表 -->
                <div class="merchants-list" id="merchantsList">
                    <!-- 商户项将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 权限内容 -->
            <div class="permissions-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div class="selected-merchant-info" id="selectedMerchantInfo" style="display: none;">
                        <div class="merchant-avatar" id="merchantAvatar">商</div>
                        <div class="merchant-details-panel">
                            <h3 id="merchantName">未选择商户</h3>
                            <p id="merchantDesc">请从左侧列表选择一个商户来管理权限</p>
                        </div>
                    </div>

                    <div class="merchant-stats" id="merchantStats" style="display: none;">
                        <div style="display: flex; gap: 24px; font-size: 12px; color: var(--gray-600);">
                            <span>权限总数: <strong id="totalPermissions">0</strong></span>
                            <span>已启用: <strong id="enabledPermissions">0</strong></span>
                            <span>待审批: <strong id="pendingPermissions">0</strong></span>
                        </div>
                    </div>
                </div>

                <!-- 批量操作栏 -->
                <div class="bulk-actions" id="bulkActions">
                    <div class="bulk-info">
                        已选择 <span id="selectedCount">0</span> 个商户
                    </div>
                    <div class="bulk-buttons">
                        <button class="btn btn-sm btn-primary" onclick="bulkEnablePermissions()">
                            <i class="fas fa-check"></i>
                            批量启用
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="bulkDisablePermissions()">
                            <i class="fas fa-times"></i>
                            批量禁用
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="bulkApplyTemplate()">
                            <i class="fas fa-copy"></i>
                            应用模板
                        </button>
                        <button class="btn btn-sm btn-success" onclick="bulkApprove()">
                            <i class="fas fa-check-circle"></i>
                            批量审批
                        </button>
                    </div>
                </div>

                <!-- 权限矩阵 -->
                <div class="permissions-matrix" id="permissionsMatrix">
                    <div class="empty-state">
                        <i class="fas fa-users-cog"></i>
                        <h3>请选择商户</h3>
                        <p>从左侧列表选择一个商户开始管理权限</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 变更指示器 -->
        <div class="changes-indicator" id="changesIndicator">
            <i class="fas fa-exclamation-triangle"></i>
            <span>您有未保存的更改</span>
            <button class="btn btn-sm btn-primary" onclick="savePermissions()" style="margin-left: 12px;">
                保存
            </button>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal-content" id="modalContent">
            <!-- 模态框内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <script src="merchant-permissions-enhanced.js"></script>
    <script>
        // 初始化权限管理系统
        const merchantPermissionManager = new MerchantPermissionManager();
        
        // 全局函数
        function switchTab(event, tabName) {
            merchantPermissionManager.switchTab(event, tabName);
        }
        
        function toggleFilterPanel() {
            merchantPermissionManager.toggleFilterPanel();
        }
        
        function toggleBulkMode() {
            merchantPermissionManager.toggleBulkMode();
        }
        
        function applyFilters() {
            merchantPermissionManager.applyFilters();
        }
        
        function resetFilters() {
            merchantPermissionManager.resetFilters();
        }
        
        function savePermissions() {
            merchantPermissionManager.savePermissions();
        }
        
        function resetChanges() {
            merchantPermissionManager.resetChanges();
        }
        
        function exportPermissions() {
            merchantPermissionManager.exportPermissions();
        }
        
        function showCompareModal() {
            merchantPermissionManager.showCompareModal();
        }
        
        function showTemplateModal() {
            merchantPermissionManager.showTemplateModal();
        }
        
        function showApprovalModal() {
            merchantPermissionManager.showApprovalModal();
        }
        
        function bulkEnablePermissions() {
            merchantPermissionManager.bulkEnablePermissions();
        }
        
        function bulkDisablePermissions() {
            merchantPermissionManager.bulkDisablePermissions();
        }
        
        function bulkApplyTemplate() {
            merchantPermissionManager.bulkApplyTemplate();
        }
        
        function showDependencyInfo() {
            merchantPermissionManager.showDependencyInfo();
        }
        
        function exportLogs() {
            merchantPermissionManager.exportLogs();
        }
        
        function bulkApprove() {
            merchantPermissionManager.bulkApprove();
        }
        
        function hideModal() {
            merchantPermissionManager.hideModal();
        }
        
        function searchMerchants(keyword) {
            merchantPermissionManager.searchMerchants(keyword);
        }
        
        function showCreateTemplateModal() {
            merchantPermissionManager.showCreateTemplateModal();
        }

        // 筛选事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter-tag')) {
                document.querySelectorAll('.filter-tag').forEach(tag => tag.classList.remove('active'));
                e.target.classList.add('active');
                const filter = e.target.dataset.filter;
                merchantPermissionManager.filterMerchants(filter);
            }
        });
    </script>
</body>
</html> 