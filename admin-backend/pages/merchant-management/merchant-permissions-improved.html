<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户权限管理 - TeleShop B2B2C</title>
    
    <!-- 引入现有样式 -->
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- 权限管理专用样式 -->
    <style>
        /* CSS变量定义 */
        :root {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-500: #6b7280;
            --gray-700: #374151;
            --gray-900: #111827;
            --radius: 8px;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        /* 全局重置 */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gray-50);
            color: var(--gray-900);
            line-height: 1.6;
        }

        /* 页面容器 */
        .permission-container {
            padding: 24px;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 24px;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 24px;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0 0 8px 0;
        }

        .page-subtitle {
            color: var(--gray-500);
            font-size: 16px;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* 按钮组件 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            border: none;
            border-radius: var(--radius);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: white;
        }

        .btn-danger {
            background: var(--danger);
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 主要布局 */
        .main-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 24px;
            align-items: start;
        }

        /* 侧边栏样式 */
        .sidebar {
            background: white;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            position: sticky;
            top: 24px;
        }

        .sidebar-header {
            padding: 20px;
            background: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .merchant-count-badge {
            background: var(--primary);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 搜索区域 */
        .search-section {
            padding: 20px;
            border-bottom: 1px solid var(--gray-200);
        }

        .search-input-wrapper {
            position: relative;
            margin-bottom: 16px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius);
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 14px;
        }

        .filter-options {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .filter-option {
            padding: 6px 12px;
            background: var(--gray-100);
            border: 1px solid var(--gray-300);
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-option.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .filter-option:hover:not(.active) {
            background: var(--gray-200);
        }

        /* 商户列表 */
        .merchants-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .merchant-item {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-100);
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .merchant-item:hover {
            background: var(--gray-50);
        }

        .merchant-item.active {
            background: #eff6ff;
            border-left: 4px solid var(--primary);
        }

        .merchant-item:last-child {
            border-bottom: none;
        }

        .merchant-checkbox {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .merchant-info {
            padding-right: 24px;
        }

        .merchant-name {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 4px;
            font-size: 14px;
        }

        .merchant-details {
            font-size: 12px;
            color: var(--gray-500);
            margin-bottom: 8px;
        }

        .merchant-status-badges {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .status-badge {
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-badge.active {
            background: #dcfce7;
            color: #166534;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.suspended {
            background: #fecaca;
            color: #dc2626;
        }

        .status-badge.premium {
            background: #e0e7ff;
            color: #3730a3;
        }

        /* 主内容区域 */
        .content-panel {
            background: white;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .content-header {
            padding: 24px;
            background: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
        }

        .selected-merchant-display {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .merchant-avatar {
            width: 48px;
            height: 48px;
            border-radius: var(--radius);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 18px;
        }

        .merchant-info-panel h3 {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0 0 4px 0;
        }

        .merchant-info-panel p {
            font-size: 14px;
            color: var(--gray-500);
            margin: 0;
        }

        .merchant-metrics {
            display: flex;
            gap: 32px;
            font-size: 14px;
            color: var(--gray-600);
        }

        .metric-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary);
        }

        .metric-label {
            font-size: 12px;
            color: var(--gray-500);
        }

        /* 批量操作栏 */
        .bulk-operations {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border: 1px solid #bfdbfe;
            padding: 16px 24px;
            display: none;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .bulk-operations.visible {
            display: flex;
        }

        .bulk-info {
            font-size: 14px;
            color: #1e40af;
            font-weight: 500;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        /* 权限矩阵区域 */
        .permissions-matrix {
            padding: 24px;
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: var(--gray-500);
        }

        .empty-state .icon {
            font-size: 64px;
            color: var(--gray-300);
            margin-bottom: 16px;
        }

        .empty-state h3 {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-700);
            margin: 0 0 8px 0;
        }

        .empty-state p {
            font-size: 16px;
            margin: 0;
        }

        /* 权限分类 */
        .permission-category {
            margin-bottom: 32px;
        }

        .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding: 16px 20px;
            background: var(--gray-50);
            border-radius: var(--radius);
            border: 1px solid var(--gray-200);
        }

        .category-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .category-icon {
            width: 36px;
            height: 36px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .category-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
        }

        .category-count {
            font-size: 12px;
            color: var(--gray-500);
            margin-left: 8px;
        }

        .category-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--gray-600);
        }

        .toggle-checkbox {
            width: 18px;
            height: 18px;
        }

        /* 权限网格 */
        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
        }

        .permission-card {
            background: white;
            border: 2px solid var(--gray-200);
            border-radius: var(--radius);
            padding: 20px;
            transition: all 0.2s ease;
            position: relative;
        }

        .permission-card:hover {
            border-color: var(--primary);
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .permission-card.disabled {
            opacity: 0.5;
            pointer-events: none;
            background: var(--gray-50);
        }

        .permission-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .permission-title {
            font-weight: 600;
            color: var(--gray-900);
            font-size: 16px;
            margin: 0;
        }

        .permission-checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .permission-description {
            font-size: 14px;
            color: var(--gray-600);
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .permission-metadata {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .metadata-tag {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .metadata-tag.required {
            background: #fef3c7;
            color: #92400e;
        }

        .metadata-tag.approval-needed {
            background: #fecaca;
            color: #dc2626;
        }

        .metadata-tag.has-dependencies {
            background: #e0e7ff;
            color: #3730a3;
        }

        /* 审批状态指示器 */
        .approval-indicator {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            border: 2px solid white;
        }

        .approval-indicator.pending {
            background: var(--warning);
            color: white;
        }

        .approval-indicator.approved {
            background: var(--success);
            color: white;
        }

        .approval-indicator.rejected {
            background: var(--danger);
            color: white;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: var(--radius);
            max-width: 600px;
            width: 100%;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--gray-400);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        .modal-body {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--gray-200);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 通知系统 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1100;
            padding: 16px 20px;
            border-radius: var(--radius);
            color: white;
            font-size: 14px;
            font-weight: 500;
            min-width: 320px;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            box-shadow: var(--shadow-lg);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success);
        }

        .notification.warning {
            background: var(--warning);
        }

        .notification.error {
            background: var(--danger);
        }

        .notification.info {
            background: var(--primary);
        }

        /* 变更指示器 */
        .changes-indicator {
            position: fixed;
            bottom: 24px;
            right: 24px;
            background: var(--warning);
            color: white;
            padding: 16px 20px;
            border-radius: var(--radius);
            font-size: 14px;
            font-weight: 500;
            display: none;
            align-items: center;
            gap: 12px;
            box-shadow: var(--shadow-lg);
            animation: slideInUp 0.3s ease;
        }

        .changes-indicator.show {
            display: flex;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(100px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 300px 1fr;
            }
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .sidebar {
                position: static;
            }

            .permissions-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .permission-container {
                padding: 16px;
            }

            .header-top {
                flex-direction: column;
                gap: 16px;
            }

            .header-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .permissions-grid {
                grid-template-columns: 1fr;
            }

            .bulk-operations {
                flex-direction: column;
                align-items: stretch;
            }

            .bulk-actions {
                justify-content: center;
            }

            .merchant-metrics {
                gap: 16px;
            }
        }

        /* 加载状态 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 滚动条样式 */
        .merchants-list::-webkit-scrollbar,
        .modal-body::-webkit-scrollbar {
            width: 6px;
        }

        .merchants-list::-webkit-scrollbar-track,
        .modal-body::-webkit-scrollbar-track {
            background: var(--gray-100);
        }

        .merchants-list::-webkit-scrollbar-thumb,
        .modal-body::-webkit-scrollbar-thumb {
            background: var(--gray-300);
            border-radius: 3px;
        }

        .merchants-list::-webkit-scrollbar-thumb:hover,
        .modal-body::-webkit-scrollbar-thumb:hover {
            background: var(--gray-400);
        }
    </style>
</head>
<body>
    <div class="permission-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-top">
                <div>
                    <h1 class="page-title">商户权限管理</h1>
                    <p class="page-subtitle">统一管理商户权限，支持批量操作和审批流程</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="exportPermissions()">
                        <i class="fas fa-download"></i>
                        导出配置
                    </button>
                    <button class="btn btn-secondary" onclick="showTemplateModal()">
                        <i class="fas fa-copy"></i>
                        权限模板
                    </button>
                    <button class="btn btn-warning" onclick="showApprovalModal()">
                        <i class="fas fa-clipboard-check"></i>
                        审批中心
                    </button>
                    <button class="btn btn-primary" onclick="savePermissions()">
                        <i class="fas fa-save"></i>
                        保存更改
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧商户列表 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <h2 class="sidebar-title">商户列表</h2>
                    <span class="merchant-count-badge" id="merchantCount">0</span>
                </div>

                <!-- 搜索和筛选区域 -->
                <div class="search-section">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索商户名称或ID..." 
                               id="merchantSearch" oninput="searchMerchants(this.value)">
                    </div>
                    
                    <div class="filter-options">
                        <div class="filter-option active" data-filter="all">全部</div>
                        <div class="filter-option" data-filter="active">正常</div>
                        <div class="filter-option" data-filter="pending">待审</div>
                        <div class="filter-option" data-filter="premium">高级</div>
                    </div>
                </div>

                <!-- 商户列表 -->
                <div class="merchants-list" id="merchantsList">
                    <!-- JavaScript动态生成 -->
                </div>
            </div>

            <!-- 右侧权限管理区域 -->
            <div class="content-panel">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div class="selected-merchant-display" id="selectedMerchantInfo" style="display: none;">
                        <div class="merchant-avatar" id="merchantAvatar">商</div>
                        <div class="merchant-info-panel">
                            <h3 id="merchantName">未选择商户</h3>
                            <p id="merchantDesc">请从左侧列表选择一个商户来管理权限</p>
                        </div>
                    </div>

                    <div class="merchant-metrics" id="merchantStats" style="display: none;">
                        <div class="metric-item">
                            <div class="metric-value" id="totalPermissions">0</div>
                            <div class="metric-label">权限总数</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="enabledPermissions">0</div>
                            <div class="metric-label">已启用</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value" id="pendingPermissions">0</div>
                            <div class="metric-label">待审批</div>
                        </div>
                    </div>
                </div>

                <!-- 批量操作栏 -->
                <div class="bulk-operations" id="bulkActions">
                    <div class="bulk-info">
                        已选择 <strong id="selectedCount">0</strong> 个商户
                    </div>
                    <div class="bulk-actions">
                        <button class="btn btn-sm btn-success" onclick="bulkEnablePermissions()">
                            <i class="fas fa-check"></i>
                            批量启用
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="bulkDisablePermissions()">
                            <i class="fas fa-times"></i>
                            批量禁用
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="bulkApplyTemplate()">
                            <i class="fas fa-copy"></i>
                            应用模板
                        </button>
                    </div>
                </div>

                <!-- 权限矩阵内容 -->
                <div class="permissions-matrix" id="permissionsMatrix">
                    <div class="empty-state">
                        <div class="icon">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <h3>请选择商户</h3>
                        <p>从左侧列表选择一个商户开始管理权限设置</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 变更提示 -->
        <div class="changes-indicator" id="changesIndicator">
            <i class="fas fa-exclamation-triangle"></i>
            <span>您有未保存的更改</span>
            <button class="btn btn-sm btn-primary" onclick="savePermissions()">
                立即保存
            </button>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal-content" id="modalContent">
            <!-- JavaScript动态生成内容 -->
        </div>
    </div>

    <!-- JavaScript -->
    <script src="permission-manager.js"></script>
    <script>
        // 初始化权限管理系统
        const merchantPermissionManager = new MerchantPermissionManager();
        
        // 全局函数绑定
        function savePermissions() {
            merchantPermissionManager.savePermissions();
        }
        
        function exportPermissions() {
            merchantPermissionManager.exportPermissions();
        }
        
        function showTemplateModal() {
            merchantPermissionManager.showTemplateModal();
        }
        
        function showApprovalModal() {
            merchantPermissionManager.showApprovalModal();
        }
        
        function bulkEnablePermissions() {
            merchantPermissionManager.bulkEnablePermissions();
        }
        
        function bulkDisablePermissions() {
            merchantPermissionManager.bulkDisablePermissions();
        }
        
        function bulkApplyTemplate() {
            merchantPermissionManager.bulkApplyTemplate();
        }
        
        function searchMerchants(keyword) {
            merchantPermissionManager.searchMerchants(keyword);
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选选项点击事件
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('filter-option')) {
                    document.querySelectorAll('.filter-option').forEach(option => {
                        option.classList.remove('active');
                    });
                    e.target.classList.add('active');
                    
                    const filter = e.target.dataset.filter;
                    merchantPermissionManager.filterMerchants(filter);
                }
            });

            // 页面卸载前提示保存
            window.addEventListener('beforeunload', function(e) {
                if (merchantPermissionManager.hasChanges) {
                    e.preventDefault();
                    e.returnValue = '您有未保存的更改，确定要离开吗？';
                }
            });
        });
    </script>
</body>
</html> 