<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分销推广系统 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .distribution-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .dashboard-card.revenue::before { background: #10b981; }
        .dashboard-card.distributors::before { background: #667eea; }
        .dashboard-card.orders::before { background: #f59e0b; }
        .dashboard-card.conversion::before { background: #ef4444; }

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
        }

        .dashboard-card.revenue .card-icon { background: #10b981; }
        .dashboard-card.distributors .card-icon { background: #667eea; }
        .dashboard-card.orders .card-icon { background: #f59e0b; }
        .dashboard-card.conversion .card-icon { background: #ef4444; }

        .card-number {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .card-label {
            color: #64748b;
            font-size: 14px;
        }

        .card-trend {
            font-size: 12px;
            margin-top: 8px;
        }

        .trend-up {
            color: #10b981;
        }

        .trend-down {
            color: #ef4444;
        }

        .content-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-button {
            flex: 1;
            padding: 12px 20px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            color: #64748b;
            transition: all 0.3s;
        }

        .tab-button.active {
            background: #667eea;
            color: white;
        }

        .content-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .section-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .distributor-table {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        th {
            background: #f8fafc;
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        td {
            color: #374151;
            font-size: 14px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-details h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .user-details p {
            margin: 0;
            font-size: 12px;
            color: #64748b;
        }

        .level-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .level-bronze {
            background: #fef3c7;
            color: #92400e;
        }

        .level-silver {
            background: #e5e7eb;
            color: #374151;
        }

        .level-gold {
            background: #fef3c7;
            color: #92400e;
        }

        .level-diamond {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-suspended {
            background: #fecaca;
            color: #991b1b;
        }

        .commission-chart {
            padding: 20px;
            height: 400px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #64748b;
        }

        .commission-rules {
            padding: 20px;
        }

        .rule-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .rule-info {
            flex: 1;
        }

        .rule-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .rule-description {
            font-size: 14px;
            color: #64748b;
        }

        .rule-rate {
            font-size: 18px;
            font-weight: 700;
            color: #667eea;
        }

        .application-form {
            padding: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #1e293b;
        }

        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
        }

        .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
        }

        .application-item {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
        }

        .application-header {
            padding: 16px;
            background: #f8fafc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .application-content {
            padding: 16px;
        }

        .application-actions {
            padding: 16px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            display: flex;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .distribution-dashboard {
                grid-template-columns: 1fr;
            }

            .content-tabs {
                flex-direction: column;
            }

            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .section-actions {
                justify-content: center;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>分销推广系统</h1>
            <p>管理分销商权限、佣金结算和推广效果分析</p>
            
            <!-- 快捷导航 -->
            <div class="quick-nav">
                <button class="btn btn-primary">
                    <i class="fas fa-plus-circle"></i> 创建推广活动
                </button>
                <button class="btn btn-success">
                    <i class="fas fa-file-export"></i> 导出数据报表
                </button>
                <button class="btn btn-warning" onclick="location.href='../marketing-tools/social-marketing.html'">
                    <i class="fas fa-arrow-left"></i> 返回社交营销
                </button>
            </div>
        </div>
        
        <style>
            .quick-nav {
                display: flex;
                gap: 10px;
                margin-top: 15px;
            }
            
            .admin-header {
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 12px;
                color: white;
                margin-bottom: 20px;
            }
            
            .search-filter {
                display: flex;
                gap: 10px;
                margin-bottom: 15px;
                flex-wrap: wrap;
            }
            
            .search-filter .form-input {
                max-width: 200px;
            }
            
            .pagination {
                display: flex;
                justify-content: center;
                margin-top: 20px;
                gap: 5px;
            }
            
            .pagination button {
                padding: 5px 10px;
                border: 1px solid #e2e8f0;
                background: white;
                border-radius: 4px;
            }
            
            .pagination button.active {
                background: #667eea;
                color: white;
                border-color: #667eea;
            }
        </style>

        <!-- 数据概览 -->
        <div class="distribution-dashboard">
            <div class="dashboard-card revenue">
                <div class="card-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="card-number">¥2.8M</div>
                <div class="card-label">总佣金收入</div>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +18.5%
                </div>
            </div>

            <div class="dashboard-card distributors">
                <div class="card-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="card-number">1,247</div>
                <div class="card-label">活跃分销商</div>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +12.3%
                </div>
            </div>

            <div class="dashboard-card orders">
                <div class="card-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="card-number">5,689</div>
                <div class="card-label">推广订单</div>
                <div class="card-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +25.7%
                </div>
            </div>

            <div class="dashboard-card conversion">
                <div class="card-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="card-number">3.2%</div>
                <div class="card-label">转化率</div>
                <div class="card-trend trend-down">
                    <i class="fas fa-arrow-down"></i> -0.5%
                </div>
            </div>
        </div>

        <!-- 功能标签页 -->
        <div class="content-tabs">
            <button class="tab-button active" data-tab="distributors">分销商管理</button>
            <button class="tab-button" data-tab="links">推广链接</button>
            <button class="tab-button" data-tab="commissions">佣金管理</button>
            <button class="tab-button" data-tab="withdrawals">提现管理</button>
            <button class="tab-button" data-tab="materials">素材库</button>
            <button class="tab-button" data-tab="training">培训系统</button>
            <button class="tab-button" data-tab="applications">申请审核</button>
            <button class="tab-button" data-tab="analytics">数据分析</button>
            <button class="tab-button" data-tab="settings">系统设置</button>
        </div>

        <!-- 推广链接管理 -->
        <div id="links-content" class="content-section" style="display: none;">
            <div class="section-header">
                <h3 class="section-title">推广链接管理</h3>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="showCreateLinkModal()">
                        <i class="fas fa-plus"></i>
                        创建推广链接
                    </button>
                    <button class="btn btn-success" onclick="batchGenerateLinks()">
                        <i class="fas fa-magic"></i>
                        批量生成
                    </button>
                    <button class="btn btn-warning" onclick="showLinkAnalytics()">
                        <i class="fas fa-chart-line"></i>
                        链接分析
                    </button>
                </div>
            </div>

            <!-- 链接统计 -->
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">2,456</div>
                    <div class="stat-label">总推广链接</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">1,234</div>
                    <div class="stat-label">活跃链接</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">45,678</div>
                    <div class="stat-label">总点击次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">3.2%</div>
                    <div class="stat-label">平均转化率</div>
                </div>
            </div>

            <!-- 搜索和过滤 -->
            <div class="search-filter">
                <input type="text" class="form-input" placeholder="搜索分销商或商品..." id="link-search">
                <select class="form-input" id="link-status">
                    <option value="">所有状态</option>
                    <option value="active">活跃</option>
                    <option value="expired">已过期</option>
                    <option value="suspended">已暂停</option>
                </select>
                <select class="form-input" id="link-type">
                    <option value="">所有类型</option>
                    <option value="product">商品推广</option>
                    <option value="category">分类推广</option>
                    <option value="store">店铺推广</option>
                    <option value="event">活动推广</option>
                </select>
                <button class="btn btn-primary" onclick="searchLinks()">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </div>

            <!-- 推广链接列表 -->
            <div class="distributor-table">
                <table>
                    <thead>
                        <tr>
                            <th>推广链接</th>
                            <th>分销商</th>
                            <th>推广内容</th>
                            <th>点击次数</th>
                            <th>转化订单</th>
                            <th>佣金收入</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <code style="background: #f1f5f9; padding: 4px 8px; border-radius: 4px; font-size: 12px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                        https://teleshop.com/p/123?ref=DS001
                                    </code>
                                    <button class="btn btn-sm" onclick="copyLink('https://teleshop.com/p/123?ref=DS001')" style="padding: 4px 8px;">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/32x32/667eea/ffffff?text=A" alt="头像" class="user-avatar" style="width: 32px; height: 32px;">
                                    <div class="user-details">
                                        <h4 style="margin: 0; font-size: 13px;">张小明</h4>
                                        <p style="margin: 0; font-size: 11px; color: #64748b;">DS001</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>iPhone 15 Pro</strong><br>
                                    <span style="font-size: 12px; color: #64748b;">商品推广</span>
                                </div>
                            </td>
                            <td>1,234</td>
                            <td>45</td>
                            <td>¥2,450</td>
                            <td>
                                <span class="status-badge status-active">活跃</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="viewLinkStats(1)">
                                        <i class="fas fa-chart-bar"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="editLink(1)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger" onclick="suspendLink(1)">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <code style="background: #f1f5f9; padding: 4px 8px; border-radius: 4px; font-size: 12px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                        https://teleshop.com/c/electronics?ref=DS002
                                    </code>
                                    <button class="btn btn-sm" onclick="copyLink('https://teleshop.com/c/electronics?ref=DS002')" style="padding: 4px 8px;">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/32x32/764ba2/ffffff?text=B" alt="头像" class="user-avatar" style="width: 32px; height: 32px;">
                                    <div class="user-details">
                                        <h4 style="margin: 0; font-size: 13px;">李小红</h4>
                                        <p style="margin: 0; font-size: 11px; color: #64748b;">DS002</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>数码电子分类</strong><br>
                                    <span style="font-size: 12px; color: #64748b;">分类推广</span>
                                </div>
                            </td>
                            <td>856</td>
                            <td>28</td>
                            <td>¥1,680</td>
                            <td>
                                <span class="status-badge status-active">活跃</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="viewLinkStats(2)">
                                        <i class="fas fa-chart-bar"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="editLink(2)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger" onclick="suspendLink(2)">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <code style="background: #f1f5f9; padding: 4px 8px; border-radius: 4px; font-size: 12px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                        https://teleshop.com/event/618?ref=DS003
                                    </code>
                                    <button class="btn btn-sm" onclick="copyLink('https://teleshop.com/event/618?ref=DS003')" style="padding: 4px 8px;">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </td>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/32x32/2ed573/ffffff?text=C" alt="头像" class="user-avatar" style="width: 32px; height: 32px;">
                                    <div class="user-details">
                                        <h4 style="margin: 0; font-size: 13px;">王大力</h4>
                                        <p style="margin: 0; font-size: 11px; color: #64748b;">DS003</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>618购物节</strong><br>
                                    <span style="font-size: 12px; color: #64748b;">活动推广</span>
                                </div>
                            </td>
                            <td>2,156</td>
                            <td>89</td>
                            <td>¥5,670</td>
                            <td>
                                <span class="status-badge status-pending">已过期</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="viewLinkStats(3)">
                                        <i class="fas fa-chart-bar"></i>
                                    </button>
                                    <button class="btn btn-success" onclick="renewLink(3)">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="btn btn-danger" onclick="deleteLink(3)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分销商管理 -->
        <div id="distributors-content" class="content-section">
            <div class="section-header">
                <h3 class="section-title">分销商管理</h3>
                <div class="section-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        添加分销商
                    </button>
                    <button class="btn btn-success">
                        <i class="fas fa-download"></i>
                        导出数据
                    </button>
                </div>
            </div>
            
            <!-- 搜索和过滤 -->
            <div class="search-filter">
                <input type="text" class="form-input" placeholder="搜索分销商..." id="distributor-search">
                <select class="form-input" id="distributor-level">
                    <option value="">所有等级</option>
                    <option value="bronze">铜牌分销商</option>
                    <option value="silver">银牌分销商</option>
                    <option value="gold">黄金分销商</option>
                    <option value="diamond">钻石分销商</option>
                </select>
                <select class="form-input" id="distributor-status">
                    <option value="">所有状态</option>
                    <option value="active">正常</option>
                    <option value="pending">待审核</option>
                    <option value="suspended">已暂停</option>
                </select>
                <button class="btn btn-primary" id="distributor-search-btn">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <button class="btn btn-secondary" id="distributor-reset-btn">
                    <i class="fas fa-redo"></i> 重置
                </button>
            </div>

            <div class="distributor-table">
                <table>
                    <thead>
                        <tr>
                            <th>分销商信息</th>
                            <th>等级</th>
                            <th>推广订单</th>
                            <th>累计佣金</th>
                            <th>本月佣金</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/40x40/667eea/ffffff?text=A" alt="用户头像" class="user-avatar">
                                    <div class="user-details">
                                        <h4>张小明</h4>
                                        <p>ID: DS001 | 注册时间: 2024-01-15</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="level-badge level-gold">黄金分销商</span>
                            </td>
                            <td>1,234</td>
                            <td>¥45,678</td>
                            <td>¥8,900</td>
                            <td>
                                <span class="status-badge status-active">正常</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="viewDistributor(1)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="editDistributor(1)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger" onclick="suspendDistributor(1)">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/40x40/764ba2/ffffff?text=B" alt="用户头像" class="user-avatar">
                                    <div class="user-details">
                                        <h4>李小红</h4>
                                        <p>ID: DS002 | 注册时间: 2024-01-12</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="level-badge level-silver">银牌分销商</span>
                            </td>
                            <td>856</td>
                            <td>¥32,100</td>
                            <td>¥6,500</td>
                            <td>
                                <span class="status-badge status-active">正常</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="viewDistributor(2)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="editDistributor(2)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger" onclick="suspendDistributor(2)">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/40x40/2ed573/ffffff?text=C" alt="用户头像" class="user-avatar">
                                    <div class="user-details">
                                        <h4>王大力</h4>
                                        <p>ID: DS003 | 注册时间: 2024-01-10</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="level-badge level-diamond">钻石分销商</span>
                            </td>
                            <td>2,156</td>
                            <td>¥78,900</td>
                            <td>¥15,600</td>
                            <td>
                                <span class="status-badge status-active">正常</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="viewDistributor(3)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="editDistributor(3)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger" onclick="suspendDistributor(3)">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/40x40/ff8a80/ffffff?text=D" alt="用户头像" class="user-avatar">
                                    <div class="user-details">
                                        <h4>赵小云</h4>
                                        <p>ID: DS004 | 注册时间: 2024-01-05</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="level-badge level-bronze">铜牌分销商</span>
                            </td>
                            <td>423</td>
                            <td>¥12,450</td>
                            <td>¥3,250</td>
                            <td>
                                <span class="status-badge status-pending">待审核</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="viewDistributor(4)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="editDistributor(4)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger" onclick="suspendDistributor(4)">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/40x40/fed6e3/000000?text=E" alt="用户头像" class="user-avatar">
                                    <div class="user-details">
                                        <h4>刘小华</h4>
                                        <p>ID: DS005 | 注册时间: 2023-12-28</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="level-badge level-diamond">钻石分销商</span>
                            </td>
                            <td>1,857</td>
                            <td>¥65,320</td>
                            <td>¥12,480</td>
                            <td>
                                <span class="status-badge status-suspended">已暂停</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="viewDistributor(5)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="editDistributor(5)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-success" onclick="activateDistributor(5)">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页控件 -->
            <div class="pagination">
                <button><i class="fas fa-angle-double-left"></i></button>
                <button><i class="fas fa-angle-left"></i></button>
                <button class="active">1</button>
                <button>2</button>
                <button>3</button>
                <button>4</button>
                <button>5</button>
                <button><i class="fas fa-angle-right"></i></button>
                <button><i class="fas fa-angle-double-right"></i></button>
            </div>
        </div>

        <!-- 佣金管理 -->
        <div id="commissions-content" class="content-section" style="display: none;">
            <div class="section-header">
                <h3 class="section-title">佣金管理</h3>
                <div class="section-actions">
                    <button class="btn btn-success">
                        <i class="fas fa-calculator"></i>
                        批量结算
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-cog"></i>
                        佣金设置
                    </button>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">¥156,789</div>
                    <div class="stat-label">待结算佣金</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">¥2,345,678</div>
                    <div class="stat-label">已结算佣金</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">¥45,678</div>
                    <div class="stat-label">本月佣金</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">15.8%</div>
                    <div class="stat-label">平均佣金率</div>
                </div>
            </div>

            <div class="commission-chart">
                <canvas id="commissionChart"></canvas>
            </div>
        </div>

        <!-- 提现管理 -->
        <div id="withdrawals-content" class="content-section" style="display: none;">
            <div class="section-header">
                <h3 class="section-title">提现管理</h3>
                <div class="section-actions">
                    <button class="btn btn-success" onclick="batchApproveWithdrawals()">
                        <i class="fas fa-check-double"></i>
                        批量通过
                    </button>
                    <button class="btn btn-warning" onclick="exportWithdrawalReport()">
                        <i class="fas fa-file-excel"></i>
                        导出报表
                    </button>
                    <button class="btn btn-primary" onclick="showWithdrawalSettings()">
                        <i class="fas fa-cog"></i>
                        提现设置
                    </button>
                </div>
            </div>

            <!-- 提现统计 -->
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">15</div>
                    <div class="stat-label">待审核申请</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">¥89,456</div>
                    <div class="stat-label">待提现金额</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">¥1,256,789</div>
                    <div class="stat-label">本月已提现</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">2.5天</div>
                    <div class="stat-label">平均处理时间</div>
                </div>
            </div>

            <!-- 搜索和过滤 -->
            <div class="search-filter">
                <input type="text" class="form-input" placeholder="搜索分销商..." id="withdrawal-search">
                <select class="form-input" id="withdrawal-status">
                    <option value="">所有状态</option>
                    <option value="pending">待审核</option>
                    <option value="approved">已通过</option>
                    <option value="processing">处理中</option>
                    <option value="completed">已完成</option>
                    <option value="rejected">已拒绝</option>
                </select>
                <input type="date" class="form-input" id="withdrawal-date-from" placeholder="开始日期">
                <input type="date" class="form-input" id="withdrawal-date-to" placeholder="结束日期">
                <button class="btn btn-primary" onclick="searchWithdrawals()">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </div>

            <!-- 提现申请列表 -->
            <div class="distributor-table">
                <table>
                    <thead>
                        <tr>
                            <th>分销商</th>
                            <th>申请金额</th>
                            <th>手续费</th>
                            <th>实际到账</th>
                            <th>提现方式</th>
                            <th>申请时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/32x32/667eea/ffffff?text=A" alt="头像" class="user-avatar" style="width: 32px; height: 32px;">
                                    <div class="user-details">
                                        <h4 style="margin: 0; font-size: 13px;">张小明</h4>
                                        <p style="margin: 0; font-size: 11px; color: #64748b;">DS001 | 可提现: ¥8,900</p>
                                    </div>
                                </div>
                            </td>
                            <td><strong>¥5,000</strong></td>
                            <td>¥25</td>
                            <td><strong>¥4,975</strong></td>
                            <td>
                                <div>
                                    <i class="fas fa-university" style="color: #667eea;"></i>
                                    银行卡<br>
                                    <span style="font-size: 11px; color: #64748b;">****1234</span>
                                </div>
                            </td>
                            <td>2024-01-15<br><span style="font-size: 11px; color: #64748b;">14:30:25</span></td>
                            <td>
                                <span class="status-badge status-pending">待审核</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-success" onclick="approveWithdrawal(1)">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-danger" onclick="rejectWithdrawal(1)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button class="btn btn-primary" onclick="viewWithdrawalDetail(1)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/32x32/764ba2/ffffff?text=B" alt="头像" class="user-avatar" style="width: 32px; height: 32px;">
                                    <div class="user-details">
                                        <h4 style="margin: 0; font-size: 13px;">李小红</h4>
                                        <p style="margin: 0; font-size: 11px; color: #64748b;">DS002 | 可提现: ¥6,500</p>
                                    </div>
                                </div>
                            </td>
                            <td><strong>¥3,000</strong></td>
                            <td>¥15</td>
                            <td><strong>¥2,985</strong></td>
                            <td>
                                <div>
                                    <i class="fab fa-alipay" style="color: #1890ff;"></i>
                                    支付宝<br>
                                    <span style="font-size: 11px; color: #64748b;">****@qq.com</span>
                                </div>
                            </td>
                            <td>2024-01-14<br><span style="font-size: 11px; color: #64748b;">16:45:12</span></td>
                            <td>
                                <span class="status-badge status-active">处理中</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-success" onclick="completeWithdrawal(2)">
                                        <i class="fas fa-check-double"></i>
                                    </button>
                                    <button class="btn btn-primary" onclick="viewWithdrawalDetail(2)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="contactDistributor(2)">
                                        <i class="fas fa-phone"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/32x32/2ed573/ffffff?text=C" alt="头像" class="user-avatar" style="width: 32px; height: 32px;">
                                    <div class="user-details">
                                        <h4 style="margin: 0; font-size: 13px;">王大力</h4>
                                        <p style="margin: 0; font-size: 11px; color: #64748b;">DS003 | 可提现: ¥15,600</p>
                                    </div>
                                </div>
                            </td>
                            <td><strong>¥10,000</strong></td>
                            <td>¥50</td>
                            <td><strong>¥9,950</strong></td>
                            <td>
                                <div>
                                    <i class="fab fa-weixin" style="color: #07c160;"></i>
                                    微信<br>
                                    <span style="font-size: 11px; color: #64748b;">wangdali***</span>
                                </div>
                            </td>
                            <td>2024-01-13<br><span style="font-size: 11px; color: #64748b;">10:20:33</span></td>
                            <td>
                                <span class="status-badge status-active">已完成</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="viewWithdrawalDetail(3)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-success" onclick="downloadReceipt(3)">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="https://via.placeholder.com/32x32/ff8a80/ffffff?text=D" alt="头像" class="user-avatar" style="width: 32px; height: 32px;">
                                    <div class="user-details">
                                        <h4 style="margin: 0; font-size: 13px;">赵小云</h4>
                                        <p style="margin: 0; font-size: 11px; color: #64748b;">DS004 | 可提现: ¥3,250</p>
                                    </div>
                                </div>
                            </td>
                            <td><strong>¥2,000</strong></td>
                            <td>¥10</td>
                            <td><strong>¥1,990</strong></td>
                            <td>
                                <div>
                                    <i class="fas fa-university" style="color: #667eea;"></i>
                                    银行卡<br>
                                    <span style="font-size: 11px; color: #64748b;">****5678</span>
                                </div>
                            </td>
                            <td>2024-01-12<br><span style="font-size: 11px; color: #64748b;">09:15:47</span></td>
                            <td>
                                <span class="status-badge status-suspended">已拒绝</span>
                            </td>
                            <td>
                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="viewWithdrawalDetail(4)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="viewRejectionReason(4)">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 素材库 -->
        <div id="materials-content" class="content-section" style="display: none;">
            <div class="section-header">
                <h3 class="section-title">推广素材库</h3>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="uploadMaterial()">
                        <i class="fas fa-upload"></i>
                        上传素材
                    </button>
                    <button class="btn btn-success" onclick="createMaterialTemplate()">
                        <i class="fas fa-palette"></i>
                        创建模板
                    </button>
                    <button class="btn btn-warning" onclick="manageMaterialCategories()">
                        <i class="fas fa-folder"></i>
                        分类管理
                    </button>
                </div>
            </div>

            <!-- 素材统计 -->
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">456</div>
                    <div class="stat-label">总素材数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">12,345</div>
                    <div class="stat-label">本月下载次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">89</div>
                    <div class="stat-label">热门素材</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">23</div>
                    <div class="stat-label">素材分类</div>
                </div>
            </div>

            <!-- 素材分类筛选 -->
            <div class="search-filter">
                <select class="form-input" id="material-category">
                    <option value="">所有分类</option>
                    <option value="banner">横幅广告</option>
                    <option value="poster">海报</option>
                    <option value="video">视频</option>
                    <option value="text">文案</option>
                    <option value="template">模板</option>
                </select>
                <select class="form-input" id="material-size">
                    <option value="">所有尺寸</option>
                    <option value="square">方形 (1:1)</option>
                    <option value="horizontal">横版 (16:9)</option>
                    <option value="vertical">竖版 (9:16)</option>
                    <option value="story">故事板</option>
                </select>
                <select class="form-input" id="material-status">
                    <option value="">所有状态</option>
                    <option value="active">可用</option>
                    <option value="pending">审核中</option>
                    <option value="disabled">已禁用</option>
                </select>
                <input type="text" class="form-input" placeholder="搜索素材..." id="material-search">
                <button class="btn btn-primary" onclick="searchMaterials()">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </div>

            <!-- 素材展示网格 -->
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px; padding: 20px;">
                <!-- 素材卡片 1 -->
                <div style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); transition: transform 0.3s;">
                    <div style="position: relative; height: 180px; background: linear-gradient(45deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center;">
                        <img src="https://via.placeholder.com/250x150/667eea/ffffff?text=iPhone+15+Pro" style="max-width: 80%; max-height: 80%; border-radius: 8px;">
                        <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                            横幅
                        </div>
                    </div>
                    <div style="padding: 15px;">
                        <h4 style="margin: 0 0 8px 0; font-size: 16px; color: #1e293b;">iPhone 15 Pro 推广横幅</h4>
                        <p style="margin: 0 0 12px 0; font-size: 13px; color: #64748b;">尺寸: 1200x600px | 格式: PNG</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; gap: 8px;">
                                <span style="background: #dcfce7; color: #166534; padding: 2px 6px; border-radius: 4px; font-size: 11px;">热门</span>
                                <span style="background: #dbeafe; color: #1e40af; padding: 2px 6px; border-radius: 4px; font-size: 11px;">可用</span>
                            </div>
                            <div style="display: flex; gap: 5px;">
                                <button class="btn btn-sm" onclick="previewMaterial(1)" style="padding: 6px 10px;">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" onclick="downloadMaterial(1)" style="padding: 6px 10px;">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="editMaterial(1)" style="padding: 6px 10px;">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        <div style="margin-top: 10px; font-size: 12px; color: #64748b;">
                            <i class="fas fa-download"></i> 234次下载 | 
                            <i class="fas fa-eye"></i> 1,456次查看
                        </div>
                    </div>
                </div>

                <!-- 素材卡片 2 -->
                <div style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); transition: transform 0.3s;">
                    <div style="position: relative; height: 180px; background: linear-gradient(45deg, #10b981, #059669); display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center; color: white; padding: 20px;">
                            <h3 style="margin: 0 0 10px 0;">双11狂欢</h3>
                            <p style="margin: 0; font-size: 14px;">全场5折起</p>
                        </div>
                        <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                            文案
                        </div>
                    </div>
                    <div style="padding: 15px;">
                        <h4 style="margin: 0 0 8px 0; font-size: 16px; color: #1e293b;">双11活动推广文案</h4>
                        <p style="margin: 0 0 12px 0; font-size: 13px; color: #64748b;">包含: 朋友圈文案、私聊话术</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; gap: 8px;">
                                <span style="background: #fef3c7; color: #92400e; padding: 2px 6px; border-radius: 4px; font-size: 11px;">新品</span>
                                <span style="background: #dbeafe; color: #1e40af; padding: 2px 6px; border-radius: 4px; font-size: 11px;">可用</span>
                            </div>
                            <div style="display: flex; gap: 5px;">
                                <button class="btn btn-sm" onclick="previewMaterial(2)" style="padding: 6px 10px;">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" onclick="downloadMaterial(2)" style="padding: 6px 10px;">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="editMaterial(2)" style="padding: 6px 10px;">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        <div style="margin-top: 10px; font-size: 12px; color: #64748b;">
                            <i class="fas fa-download"></i> 89次下载 | 
                            <i class="fas fa-eye"></i> 567次查看
                        </div>
                    </div>
                </div>

                <!-- 素材卡片 3 -->
                <div style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); transition: transform 0.3s;">
                    <div style="position: relative; height: 180px; background: linear-gradient(45deg, #f59e0b, #d97706); display: flex; align-items: center; justify-content: center;">
                        <div style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 20px; text-align: center; color: white;">
                            <i class="fas fa-play" style="font-size: 36px;"></i>
                            <p style="margin: 10px 0 0 0; font-size: 14px;">30秒推广视频</p>
                        </div>
                        <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                            视频
                        </div>
                    </div>
                    <div style="padding: 15px;">
                        <h4 style="margin: 0 0 8px 0; font-size: 16px; color: #1e293b;">产品推广短视频</h4>
                        <p style="margin: 0 0 12px 0; font-size: 13px; color: #64748b;">时长: 30秒 | 格式: MP4</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; gap: 8px;">
                                <span style="background: #dcfce7; color: #166534; padding: 2px 6px; border-radius: 4px; font-size: 11px;">推荐</span>
                                <span style="background: #dbeafe; color: #1e40af; padding: 2px 6px; border-radius: 4px; font-size: 11px;">可用</span>
                            </div>
                            <div style="display: flex; gap: 5px;">
                                <button class="btn btn-sm" onclick="previewMaterial(3)" style="padding: 6px 10px;">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" onclick="downloadMaterial(3)" style="padding: 6px 10px;">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="editMaterial(3)" style="padding: 6px 10px;">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                        <div style="margin-top: 10px; font-size: 12px; color: #64748b;">
                            <i class="fas fa-download"></i> 145次下载 | 
                            <i class="fas fa-eye"></i> 892次查看
                        </div>
                    </div>
                </div>

                <!-- 素材卡片 4 -->
                <div style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); transition: transform 0.3s;">
                    <div style="position: relative; height: 180px; background: linear-gradient(45deg, #ef4444, #dc2626); display: flex; align-items: center; justify-content: center;">
                        <img src="https://via.placeholder.com/150x100/ef4444/ffffff?text=Template" style="max-width: 80%; max-height: 80%; border-radius: 8px;">
                        <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                            模板
                        </div>
                    </div>
                    <div style="padding: 15px;">
                        <h4 style="margin: 0 0 8px 0; font-size: 16px; color: #1e293b;">朋友圈海报模板</h4>
                        <p style="margin: 0 0 12px 0; font-size: 13px; color: #64748b;">可编辑PSD模板</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; gap: 8px;">
                                <span style="background: #fecaca; color: #991b1b; padding: 2px 6px; border-radius: 4px; font-size: 11px;">审核中</span>
                            </div>
                            <div style="display: flex; gap: 5px;">
                                <button class="btn btn-sm" onclick="previewMaterial(4)" style="padding: 6px 10px;">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-success" onclick="approveMaterial(4)" style="padding: 6px 10px;">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="rejectMaterial(4)" style="padding: 6px 10px;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div style="margin-top: 10px; font-size: 12px; color: #64748b;">
                            <i class="fas fa-download"></i> 0次下载 | 
                            <i class="fas fa-eye"></i> 23次查看
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 培训系统 -->
        <div id="training-content" class="content-section" style="display: none;">
            <div class="section-header">
                <h3 class="section-title">分销商培训系统</h3>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="createTrainingCourse()">
                        <i class="fas fa-plus"></i>
                        创建课程
                    </button>
                    <button class="btn btn-success" onclick="uploadTrainingMaterial()">
                        <i class="fas fa-upload"></i>
                        上传资料
                    </button>
                    <button class="btn btn-warning" onclick="viewTrainingAnalytics()">
                        <i class="fas fa-chart-line"></i>
                        培训统计
                    </button>
                </div>
            </div>

            <!-- 培训统计 -->
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">24</div>
                    <div class="stat-label">培训课程</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">1,247</div>
                    <div class="stat-label">参训人数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">85.6%</div>
                    <div class="stat-label">完成率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">4.8</div>
                    <div class="stat-label">平均评分</div>
                </div>
            </div>

            <!-- 课程分类 -->
            <div style="display: flex; gap: 15px; margin: 20px; flex-wrap: wrap;">
                <button class="btn btn-primary active" onclick="filterTrainingByCategory('all')" style="border-radius: 20px;">
                    全部课程
                </button>
                <button class="btn btn-secondary" onclick="filterTrainingByCategory('basic')" style="border-radius: 20px;">
                    基础入门
                </button>
                <button class="btn btn-secondary" onclick="filterTrainingByCategory('advanced')" style="border-radius: 20px;">
                    进阶技巧
                </button>
                <button class="btn btn-secondary" onclick="filterTrainingByCategory('marketing')" style="border-radius: 20px;">
                    营销推广
                </button>
                <button class="btn btn-secondary" onclick="filterTrainingByCategory('policy')" style="border-radius: 20px;">
                    政策规范
                </button>
            </div>

            <!-- 培训课程列表 -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; padding: 20px;">
                <!-- 课程卡片 1 -->
                <div style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div style="position: relative; height: 200px; background: linear-gradient(45deg, #667eea, #764ba2);">
                        <div style="position: absolute; inset: 0; display: flex; align-items: center; justify-content: center; color: white;">
                            <div style="text-align: center;">
                                <i class="fas fa-graduation-cap" style="font-size: 48px; margin-bottom: 10px;"></i>
                                <h3>分销基础培训</h3>
                            </div>
                        </div>
                        <div style="position: absolute; top: 15px; right: 15px; background: rgba(0,0,0,0.7); color: white; padding: 6px 12px; border-radius: 15px; font-size: 12px;">
                            基础入门
                        </div>
                        <div style="position: absolute; bottom: 15px; left: 15px; color: white; font-size: 13px;">
                            <i class="fas fa-clock"></i> 2小时 | <i class="fas fa-users"></i> 1,234人学习
                        </div>
                    </div>
                    <div style="padding: 20px;">
                        <h4 style="margin: 0 0 10px 0; font-size: 18px; color: #1e293b;">分销商基础培训课程</h4>
                        <p style="margin: 0 0 15px 0; font-size: 14px; color: #64748b; line-height: 1.5;">
                            涵盖分销政策、佣金结算、推广技巧等基础知识，帮助新分销商快速上手。
                        </p>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div style="display: flex; gap: 8px;">
                                <span style="background: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 12px; font-size: 12px;">必修</span>
                                <span style="background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 12px; font-size: 12px;">免费</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 5px; color: #f59e0b;">
                                <i class="fas fa-star"></i>
                                <span style="font-weight: 600;">4.9</span>
                            </div>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-primary" onclick="viewCourseDetail(1)" style="flex: 1;">
                                <i class="fas fa-eye"></i> 查看详情
                            </button>
                            <button class="btn btn-warning" onclick="editCourse(1)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-success" onclick="viewStudentProgress(1)">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 课程卡片 2 -->
                <div style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div style="position: relative; height: 200px; background: linear-gradient(45deg, #10b981, #059669);">
                        <div style="position: absolute; inset: 0; display: flex; align-items: center; justify-content: center; color: white;">
                            <div style="text-align: center;">
                                <i class="fas fa-bullhorn" style="font-size: 48px; margin-bottom: 10px;"></i>
                                <h3>营销推广技巧</h3>
                            </div>
                        </div>
                        <div style="position: absolute; top: 15px; right: 15px; background: rgba(0,0,0,0.7); color: white; padding: 6px 12px; border-radius: 15px; font-size: 12px;">
                            营销推广
                        </div>
                        <div style="position: absolute; bottom: 15px; left: 15px; color: white; font-size: 13px;">
                            <i class="fas fa-clock"></i> 3.5小时 | <i class="fas fa-users"></i> 856人学习
                        </div>
                    </div>
                    <div style="padding: 20px;">
                        <h4 style="margin: 0 0 10px 0; font-size: 18px; color: #1e293b;">高效营销推广策略</h4>
                        <p style="margin: 0 0 15px 0; font-size: 14px; color: #64748b; line-height: 1.5;">
                            学习如何制定推广策略、选择推广渠道、优化转化率，提升推广效果。
                        </p>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div style="display: flex; gap: 8px;">
                                <span style="background: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 12px; font-size: 12px;">进阶</span>
                                <span style="background: #fee2e2; color: #991b1b; padding: 4px 8px; border-radius: 12px; font-size: 12px;">付费</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 5px; color: #f59e0b;">
                                <i class="fas fa-star"></i>
                                <span style="font-weight: 600;">4.7</span>
                            </div>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-primary" onclick="viewCourseDetail(2)" style="flex: 1;">
                                <i class="fas fa-eye"></i> 查看详情
                            </button>
                            <button class="btn btn-warning" onclick="editCourse(2)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-success" onclick="viewStudentProgress(2)">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 课程卡片 3 -->
                <div style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div style="position: relative; height: 200px; background: linear-gradient(45deg, #f59e0b, #d97706);">
                        <div style="position: absolute; inset: 0; display: flex; align-items: center; justify-content: center; color: white;">
                            <div style="text-align: center;">
                                <i class="fas fa-balance-scale" style="font-size: 48px; margin-bottom: 10px;"></i>
                                <h3>合规政策培训</h3>
                            </div>
                        </div>
                        <div style="position: absolute; top: 15px; right: 15px; background: rgba(0,0,0,0.7); color: white; padding: 6px 12px; border-radius: 15px; font-size: 12px;">
                            政策规范
                        </div>
                        <div style="position: absolute; bottom: 15px; left: 15px; color: white; font-size: 13px;">
                            <i class="fas fa-clock"></i> 1.5小时 | <i class="fas fa-users"></i> 1,147人学习
                        </div>
                    </div>
                    <div style="padding: 20px;">
                        <h4 style="margin: 0 0 10px 0; font-size: 18px; color: #1e293b;">分销合规政策解读</h4>
                        <p style="margin: 0 0 15px 0; font-size: 14px; color: #64748b; line-height: 1.5;">
                            了解分销行业法规、平台规则、违规风险，确保合规经营。
                        </p>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div style="display: flex; gap: 8px;">
                                <span style="background: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 12px; font-size: 12px;">必修</span>
                                <span style="background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 12px; font-size: 12px;">免费</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 5px; color: #f59e0b;">
                                <i class="fas fa-star"></i>
                                <span style="font-weight: 600;">4.6</span>
                            </div>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-primary" onclick="viewCourseDetail(3)" style="flex: 1;">
                                <i class="fas fa-eye"></i> 查看详情
                            </button>
                            <button class="btn btn-warning" onclick="editCourse(3)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-success" onclick="viewStudentProgress(3)">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申请审核 -->
        <div id="applications-content" class="content-section" style="display: none;">
            <div class="section-header">
                <h3 class="section-title">分销商申请审核</h3>
                <div class="section-actions">
                    <button class="btn btn-success">
                        <i class="fas fa-check"></i>
                        批量通过
                    </button>
                </div>
            </div>

            <div class="application-form">
                <div class="application-item">
                    <div class="application-header">
                        <div class="user-info">
                            <img src="https://via.placeholder.com/40x40/ff6b6b/ffffff?text=D" alt="用户头像" class="user-avatar">
                            <div class="user-details">
                                <h4>赵小花</h4>
                                <p>申请时间: 2024-01-15 14:30:25</p>
                            </div>
                        </div>
                        <span class="status-badge status-pending">待审核</span>
                    </div>

                    <div class="application-content">
                        <div class="form-grid">
                            <div>
                                <strong>联系方式:</strong> 13800138000<br>
                                <strong>微信号:</strong> zhaoxiaohua123<br>
                                <strong>推广渠道:</strong> 朋友圈、微信群
                            </div>
                            <div>
                                <strong>预期月销售:</strong> ¥50,000<br>
                                <strong>推广经验:</strong> 2年电商推广经验<br>
                                <strong>粉丝数量:</strong> 5,000+
                            </div>
                        </div>
                        <div>
                            <strong>申请理由:</strong><br>
                            我有丰富的社交电商推广经验，拥有稳定的客户群体，希望能成为贵平台的分销商，共同发展业务。
                        </div>
                    </div>

                    <div class="application-actions">
                        <button class="btn btn-success" onclick="approveApplication(1)">
                            <i class="fas fa-check"></i>
                            通过
                        </button>
                        <button class="btn btn-danger" onclick="rejectApplication(1)">
                            <i class="fas fa-times"></i>
                            拒绝
                        </button>
                        <button class="btn btn-primary" onclick="contactApplicant(1)">
                            <i class="fas fa-phone"></i>
                            联系申请人
                        </button>
                    </div>
                </div>

                <div class="application-item">
                    <div class="application-header">
                        <div class="user-info">
                            <img src="https://via.placeholder.com/40x40/4ecdc4/ffffff?text=E" alt="用户头像" class="user-avatar">
                            <div class="user-details">
                                <h4>孙小伟</h4>
                                <p>申请时间: 2024-01-15 13:45:18</p>
                            </div>
                        </div>
                        <span class="status-badge status-pending">待审核</span>
                    </div>

                    <div class="application-content">
                        <div class="form-grid">
                            <div>
                                <strong>联系方式:</strong> 13900139000<br>
                                <strong>微信号:</strong> sunxiaowei456<br>
                                <strong>推广渠道:</strong> 抖音、小红书
                            </div>
                            <div>
                                <strong>预期月销售:</strong> ¥80,000<br>
                                <strong>推广经验:</strong> 3年直播带货经验<br>
                                <strong>粉丝数量:</strong> 10,000+
                            </div>
                        </div>
                        <div>
                            <strong>申请理由:</strong><br>
                            专业从事直播带货3年，有稳定的粉丝基础和销售渠道，希望与平台合作推广优质商品。
                        </div>
                    </div>

                    <div class="application-actions">
                        <button class="btn btn-success" onclick="approveApplication(2)">
                            <i class="fas fa-check"></i>
                            通过
                        </button>
                        <button class="btn btn-danger" onclick="rejectApplication(2)">
                            <i class="fas fa-times"></i>
                            拒绝
                        </button>
                        <button class="btn btn-primary" onclick="contactApplicant(2)">
                            <i class="fas fa-phone"></i>
                            联系申请人
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据分析 -->
        <div id="analytics-content" class="content-section" style="display: none;">
            <div class="section-header">
                <h3 class="section-title">推广效果分析</h3>
                <div class="section-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-download"></i>
                        导出报表
                    </button>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">45,678</div>
                    <div class="stat-label">总推广访问</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">1,456</div>
                    <div class="stat-label">转化订单</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">3.19%</div>
                    <div class="stat-label">转化率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">¥186</div>
                    <div class="stat-label">平均客单价</div>
                </div>
            </div>

            <div class="commission-chart">
                <canvas id="analyticsChart"></canvas>
            </div>
        </div>

        <!-- 系统设置 -->
        <div id="settings-content" class="content-section" style="display: none;">
            <div class="section-header">
                <h3 class="section-title">分销设置</h3>
                <div class="section-actions">
                    <button class="btn btn-success">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                </div>
            </div>

            <div class="commission-rules">
                <h4 style="margin-bottom: 20px;">佣金比例设置</h4>
                
                <div class="rule-item">
                    <div class="rule-info">
                        <div class="rule-title">铜牌分销商</div>
                        <div class="rule-description">新注册分销商默认等级</div>
                    </div>
                    <div class="rule-rate">5%</div>
                </div>

                <div class="rule-item">
                    <div class="rule-info">
                        <div class="rule-title">银牌分销商</div>
                        <div class="rule-description">月销售额达到 ¥10,000</div>
                    </div>
                    <div class="rule-rate">8%</div>
                </div>

                <div class="rule-item">
                    <div class="rule-info">
                        <div class="rule-title">黄金分销商</div>
                        <div class="rule-description">月销售额达到 ¥50,000</div>
                    </div>
                    <div class="rule-rate">12%</div>
                </div>

                <div class="rule-item">
                    <div class="rule-info">
                        <div class="rule-title">钻石分销商</div>
                        <div class="rule-description">月销售额达到 ¥100,000</div>
                    </div>
                    <div class="rule-rate">15%</div>
                </div>
                
                <!-- 新增佣金规则编辑功能 -->
                <div class="add-rule" style="margin-top: 20px;">
                    <h4 style="margin-bottom: 15px;">编辑佣金规则</h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">等级名称</label>
                            <select class="form-input" id="edit-level">
                                <option value="bronze">铜牌分销商</option>
                                <option value="silver">银牌分销商</option>
                                <option value="gold">黄金分销商</option>
                                <option value="diamond">钻石分销商</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">佣金比例 (%)</label>
                            <input type="number" class="form-input" id="edit-rate" min="0" max="100" step="0.1">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">升级条件</label>
                            <input type="number" class="form-input" id="edit-condition" placeholder="月销售额(元)">
                        </div>
                        
                        <div class="form-group" style="display: flex; align-items: flex-end;">
                            <button class="btn btn-primary" id="save-commission-rule" style="margin-top: 10px;">
                                <i class="fas fa-save"></i> 保存规则
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label class="form-label">分销商申请审核</label>
                    <select class="form-input">
                        <option>需要人工审核</option>
                        <option>自动通过</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">佣金结算周期</label>
                    <select class="form-input">
                        <option>每月结算</option>
                        <option>每周结算</option>
                        <option>实时结算</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">最低提现金额</label>
                    <input type="number" class="form-input" value="100" placeholder="请输入最低提现金额">
                </div>

                <div class="form-group">
                    <label class="form-label">推广链接有效期(天)</label>
                    <input type="number" class="form-input" value="30" placeholder="请输入有效期天数">
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const tab = this.dataset.tab;
                
                // 更新标签页状态
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // 显示对应内容
                document.querySelectorAll('.content-section').forEach(content => {
                    content.style.display = 'none';
                });
                
                document.getElementById(tab + '-content').style.display = 'block';
                
                // 初始化图表
                if (tab === 'commissions') {
                    initCommissionChart();
                } else if (tab === 'analytics') {
                    initAnalyticsChart();
                }
            });
        });

        // 分销商操作
        function viewDistributor(id) {
            alert(`查看分销商 DS00${id} 的详细信息`);
        }

        function editDistributor(id) {
            alert(`编辑分销商 DS00${id} 的信息`);
        }

        function suspendDistributor(id) {
            if (confirm('确认暂停此分销商？')) {
                alert(`分销商 DS00${id} 已暂停`);
                // 在实际项目中这里会发送API请求
                // 然后更新UI状态
            }
        }
        
        function activateDistributor(id) {
            if (confirm('确认恢复此分销商？')) {
                alert(`分销商 DS00${id} 已恢复正常状态`);
                // 在实际项目中这里会发送API请求
                // 然后更新UI状态
            }
        }
        
        // 搜索和过滤功能
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定搜索事件
            const searchBtn = document.getElementById('distributor-search-btn');
            if (searchBtn) {
                searchBtn.addEventListener('click', searchDistributors);
            }
            
            // 绑定重置事件
            const resetBtn = document.getElementById('distributor-reset-btn');
            if (resetBtn) {
                resetBtn.addEventListener('click', resetDistributorSearch);
            }
            
            // 为分页按钮添加事件
            const paginationButtons = document.querySelectorAll('.pagination button');
            paginationButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除现有活动状态
                    paginationButtons.forEach(btn => btn.classList.remove('active'));
                    // 添加新的活动状态
                    this.classList.add('active');
                    
                    // 这里可以添加页面切换逻辑
                    console.log('切换到页码:', this.textContent);
                });
            });
        });
        
        function searchDistributors() {
            const searchTerm = document.getElementById('distributor-search').value.trim().toLowerCase();
            const selectedLevel = document.getElementById('distributor-level').value;
            const selectedStatus = document.getElementById('distributor-status').value;
            
            console.log('搜索条件:', {
                searchTerm: searchTerm,
                level: selectedLevel,
                status: selectedStatus
            });
            
            // 在实际项目中这里会发送API请求
            // 然后根据返回结果更新表格
            
            // 模拟搜索结果
            alert(`搜索分销商: ${searchTerm || '全部'}, 等级: ${selectedLevel || '全部'}, 状态: ${selectedStatus || '全部'}`);
        }
        
        function resetDistributorSearch() {
            document.getElementById('distributor-search').value = '';
            document.getElementById('distributor-level').value = '';
            document.getElementById('distributor-status').value = '';
            
            // 重置后可以自动搜索显示所有结果
            console.log('已重置搜索条件');
            
            // 在实际项目中这里会重新加载所有数据
        }

        // 申请审核操作
        function approveApplication(id) {
            if (confirm('确认通过此申请？')) {
                alert(`申请 ${id} 已通过`);
            }
        }

        function rejectApplication(id) {
            const reason = prompt('请输入拒绝理由：');
            if (reason) {
                alert(`申请 ${id} 已拒绝，理由：${reason}`);
            }
        }

        function contactApplicant(id) {
            alert(`联系申请人 ${id}`);
        }

        // 初始化佣金图表
        function initCommissionChart() {
            const ctx = document.getElementById('commissionChart');
            if (!ctx || ctx.chart) return;

            ctx.chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '佣金收入',
                        data: [12000, 19000, 15000, 25000, 22000, 30000],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '佣金收入趋势'
                        }
                    }
                }
            });
        }

        // 初始化分析图表
        function initAnalyticsChart() {
            const ctx = document.getElementById('analyticsChart');
            if (!ctx || ctx.chart) return;

            ctx.chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['访问量', '注册量', '订单量', '转化率'],
                    datasets: [{
                        label: '推广效果',
                        data: [45678, 8900, 1456, 3.19],
                        backgroundColor: [
                            '#667eea',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '推广效果统计'
                        }
                    }
                }
            });
        }

        // 推广链接管理功能
        function showCreateLinkModal() {
            const modalContent = `
                <div style="padding: 20px;">
                    <h3 style="margin: 0 0 20px 0;">创建推广链接</h3>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">选择分销商</label>
                            <select class="form-input" id="link-distributor">
                                <option value="">请选择分销商</option>
                                <option value="DS001">张小明 (DS001)</option>
                                <option value="DS002">李小红 (DS002)</option>
                                <option value="DS003">王大力 (DS003)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">推广类型</label>
                            <select class="form-input" id="link-type-select">
                                <option value="product">商品推广</option>
                                <option value="category">分类推广</option>
                                <option value="store">店铺推广</option>
                                <option value="event">活动推广</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">推广内容</label>
                            <input type="text" class="form-input" id="link-content" placeholder="选择要推广的商品/分类/活动">
                        </div>
                        <div class="form-group">
                            <label class="form-label">链接有效期(天)</label>
                            <input type="number" class="form-input" id="link-validity" value="30">
                        </div>
                    </div>
                    <div style="margin-top: 20px; text-align: right;">
                        <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                        <button class="btn btn-primary" onclick="createPromotionLink()">生成链接</button>
                    </div>
                </div>
            `;
            showModal(modalContent);
        }

        function createPromotionLink() {
            const distributor = document.getElementById('link-distributor').value;
            const type = document.getElementById('link-type-select').value;
            const content = document.getElementById('link-content').value;
            const validity = document.getElementById('link-validity').value;
            
            if (!distributor || !content) {
                alert('请填写完整信息');
                return;
            }
            
            // 模拟生成推广链接
            const linkId = Math.random().toString(36).substr(2, 8);
            const generatedLink = `https://teleshop.com/${type === 'product' ? 'p' : 'c'}/${linkId}?ref=${distributor}`;
            
            alert(`推广链接已生成：${generatedLink}`);
            closeModal();
            
            // 在实际项目中这里会发送API请求创建链接
            // 然后刷新链接列表
        }

        function batchGenerateLinks() {
            alert('批量生成链接功能');
            // 实现批量为多个分销商生成推广链接
        }

        function showLinkAnalytics() {
            alert('链接分析功能');
            // 显示推广链接的详细分析数据
        }

        function copyLink(link) {
            navigator.clipboard.writeText(link).then(() => {
                showNotification('链接已复制到剪贴板', 'success');
            });
        }

        function viewLinkStats(linkId) {
            alert(`查看链接 ${linkId} 的统计数据`);
        }

        function editLink(linkId) {
            alert(`编辑链接 ${linkId}`);
        }

        function suspendLink(linkId) {
            if (confirm('确认暂停此推广链接？')) {
                alert(`链接 ${linkId} 已暂停`);
            }
        }

        function renewLink(linkId) {
            if (confirm('确认续期此推广链接？')) {
                alert(`链接 ${linkId} 已续期`);
            }
        }

        function deleteLink(linkId) {
            if (confirm('确认删除此推广链接？')) {
                alert(`链接 ${linkId} 已删除`);
            }
        }

        function searchLinks() {
            const search = document.getElementById('link-search').value;
            const status = document.getElementById('link-status').value;
            const type = document.getElementById('link-type').value;
            
            console.log('搜索推广链接:', { search, status, type });
            alert(`搜索条件: ${search || '全部'}, 状态: ${status || '全部'}, 类型: ${type || '全部'}`);
        }

        // 提现管理功能
        function approveWithdrawal(id) {
            if (confirm('确认通过此提现申请？')) {
                alert(`提现申请 ${id} 已通过`);
                // 在实际项目中发送API请求
            }
        }

        function rejectWithdrawal(id) {
            const reason = prompt('请输入拒绝理由：');
            if (reason) {
                alert(`提现申请 ${id} 已拒绝，理由：${reason}`);
            }
        }

        function completeWithdrawal(id) {
            if (confirm('确认完成此提现处理？')) {
                alert(`提现申请 ${id} 已完成`);
            }
        }

        function viewWithdrawalDetail(id) {
            alert(`查看提现申请 ${id} 的详细信息`);
        }

        function contactDistributor(id) {
            alert(`联系分销商 ${id}`);
        }

        function downloadReceipt(id) {
            alert(`下载提现凭证 ${id}`);
        }

        function viewRejectionReason(id) {
            alert(`查看拒绝理由 ${id}`);
        }

        function batchApproveWithdrawals() {
            if (confirm('确认批量通过选中的提现申请？')) {
                alert('批量通过提现申请');
            }
        }

        function exportWithdrawalReport() {
            alert('导出提现报表');
        }

        function showWithdrawalSettings() {
            alert('提现设置');
        }

        function searchWithdrawals() {
            const search = document.getElementById('withdrawal-search').value;
            const status = document.getElementById('withdrawal-status').value;
            const dateFrom = document.getElementById('withdrawal-date-from').value;
            const dateTo = document.getElementById('withdrawal-date-to').value;
            
            console.log('搜索提现申请:', { search, status, dateFrom, dateTo });
            alert(`搜索提现申请: ${search || '全部'}, 状态: ${status || '全部'}`);
        }

        // 素材库功能
        function uploadMaterial() {
            alert('上传推广素材');
        }

        function createMaterialTemplate() {
            alert('创建素材模板');
        }

        function manageMaterialCategories() {
            alert('管理素材分类');
        }

        function previewMaterial(id) {
            alert(`预览素材 ${id}`);
        }

        function downloadMaterial(id) {
            alert(`下载素材 ${id}`);
            // 在实际项目中这里会下载文件
        }

        function editMaterial(id) {
            alert(`编辑素材 ${id}`);
        }

        function approveMaterial(id) {
            if (confirm('确认审核通过此素材？')) {
                alert(`素材 ${id} 已审核通过`);
            }
        }

        function rejectMaterial(id) {
            const reason = prompt('请输入拒绝理由：');
            if (reason) {
                alert(`素材 ${id} 已拒绝，理由：${reason}`);
            }
        }

        function searchMaterials() {
            const category = document.getElementById('material-category').value;
            const size = document.getElementById('material-size').value;
            const status = document.getElementById('material-status').value;
            const search = document.getElementById('material-search').value;
            
            console.log('搜索素材:', { category, size, status, search });
            alert(`搜索素材: ${search || '全部'}, 分类: ${category || '全部'}`);
        }

        // 培训系统功能
        function createTrainingCourse() {
            alert('创建培训课程');
        }

        function uploadTrainingMaterial() {
            alert('上传培训资料');
        }

        function viewTrainingAnalytics() {
            alert('查看培训统计');
        }

        function filterTrainingByCategory(category) {
            // 更新按钮状态
            document.querySelectorAll('.btn[onclick*="filterTrainingByCategory"]').forEach(btn => {
                btn.classList.remove('btn-primary', 'active');
                btn.classList.add('btn-secondary');
            });
            
            event.target.classList.remove('btn-secondary');
            event.target.classList.add('btn-primary', 'active');
            
            console.log('筛选培训课程:', category);
            alert(`筛选培训课程: ${category}`);
        }

        function viewCourseDetail(id) {
            alert(`查看课程 ${id} 详情`);
        }

        function editCourse(id) {
            alert(`编辑课程 ${id}`);
        }

        function viewStudentProgress(id) {
            alert(`查看课程 ${id} 学员进度`);
        }

        // 通用模态框功能
        function showModal(content) {
            // 创建模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;
            
            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                border-radius: 12px;
                max-width: 600px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
            `;
            
            modalContent.innerHTML = content;
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            
            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });
            
            // 保存引用
            window.currentModal = modal;
        }

        function closeModal() {
            if (window.currentModal) {
                document.body.removeChild(window.currentModal);
                window.currentModal = null;
            }
        }

        // 通知功能
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1001;
                transform: translateX(400px);
                transition: transform 0.3s ease;
            `;
            
            // 设置不同类型的颜色
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#667eea'
            };
            
            notification.style.background = colors[type] || colors.info;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.transform = 'translateX(400px)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 默认显示佣金图表
        setTimeout(() => {
            initCommissionChart();
        }, 100);
        
        // 处理URL参数
        function getUrlParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
        
        // 佣金规则保存功能
        function initCommissionRuleHandlers() {
            const saveRuleBtn = document.getElementById('save-commission-rule');
            if (saveRuleBtn) {
                saveRuleBtn.addEventListener('click', saveCommissionRule);
            }
            
            // 等级选择变化时自动填充对应数据
            const levelSelect = document.getElementById('edit-level');
            if (levelSelect) {
                levelSelect.addEventListener('change', function() {
                    const level = this.value;
                    let rate = 0;
                    let condition = 0;
                    
                    // 根据选择的等级填充默认数据
                    switch (level) {
                        case 'bronze':
                            rate = 5;
                            condition = 0;
                            break;
                        case 'silver':
                            rate = 8;
                            condition = 10000;
                            break;
                        case 'gold':
                            rate = 12;
                            condition = 50000;
                            break;
                        case 'diamond':
                            rate = 15;
                            condition = 100000;
                            break;
                    }
                    
                    document.getElementById('edit-rate').value = rate;
                    document.getElementById('edit-condition').value = condition;
                });
                
                // 初始化触发一次
                levelSelect.dispatchEvent(new Event('change'));
            }
        }
        
        function saveCommissionRule() {
            const level = document.getElementById('edit-level').value;
            const rate = document.getElementById('edit-rate').value;
            const condition = document.getElementById('edit-condition').value;
            
            // 验证输入
            if (!rate || isNaN(rate) || rate < 0 || rate > 100) {
                alert('请输入有效的佣金比例(0-100%)');
                return;
            }
            
            if (!condition || isNaN(condition) || condition < 0) {
                alert('请输入有效的升级条件金额');
                return;
            }
            
            console.log('保存佣金规则:', {
                level: level,
                rate: rate + '%',
                condition: '¥' + parseFloat(condition).toLocaleString()
            });
            
            // 显示成功消息
            alert(`已更新${getLevelName(level)}佣金比例为 ${rate}%，月销售额条件为 ¥${parseFloat(condition).toLocaleString()}`);
            
            // 在实际项目中，这里会发送API请求保存设置
            // 然后更新UI显示
        }
        
        function getLevelName(level) {
            switch (level) {
                case 'bronze': return '铜牌分销商';
                case 'silver': return '银牌分销商';
                case 'gold': return '黄金分销商';
                case 'diamond': return '钻石分销商';
                default: return '未知等级';
            }
        }
        
        // 页面加载时检查是否有tab参数
        document.addEventListener('DOMContentLoaded', function() {
            const tabParam = getUrlParam('tab');
            if (tabParam) {
                // 找到对应的标签页按钮
                const tabButton = document.querySelector(`.tab-button[data-tab="${tabParam}"]`);
                if (tabButton) {
                    // 模拟点击该标签页
                    tabButton.click();
                }
            }
            
            // 初始化佣金规则处理函数
            initCommissionRuleHandlers();
        });
    </script>
</body>
</html> 