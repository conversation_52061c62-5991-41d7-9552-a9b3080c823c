<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户权限管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 修复样式问题的完整CSS */
        :root {
            --primary: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-500: #6b7280;
            --gray-700: #374151;
            --gray-900: #111827;
            --radius: 8px;
            --shadow: 0 1px 3px rgba(0,0,0,0.12);
        }

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gray-50);
            color: var(--gray-900);
            line-height: 1.6;
        }

        .permissions-page {
            padding: 24px;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            margin-bottom: 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .page-subtitle {
            color: var(--gray-500);
            font-size: 16px;
            margin-top: 4px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            border: none;
            border-radius: var(--radius);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-200);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .main-layout {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 24px;
            align-items: start;
        }

        .sidebar-panel {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            overflow: hidden;
            position: sticky;
            top: 24px;
        }

        .panel-header {
            padding: 20px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .merchant-count {
            background: var(--primary);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .search-box {
            margin: 16px 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius);
            font-size: 14px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="%23999"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>') no-repeat 16px center;
            background-size: 16px;
            transition: border-color 0.2s;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
            margin: 16px 20px;
            flex-wrap: wrap;
        }

        .filter-tab {
            padding: 6px 12px;
            background: var(--gray-100);
            border: 1px solid var(--gray-200);
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-tab.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .merchants-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .merchant-item {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-100);
            cursor: pointer;
            transition: background 0.2s;
            position: relative;
        }

        .merchant-item:hover {
            background: var(--gray-50);
        }

        .merchant-item.active {
            background: #eff6ff;
            border-left: 4px solid var(--primary);
        }

        .merchant-checkbox {
            position: absolute;
            top: 16px;
            right: 16px;
        }

        .merchant-info {
            padding-right: 20px;
        }

        .merchant-name {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 4px;
        }

        .merchant-details {
            font-size: 12px;
            color: var(--gray-500);
            margin-bottom: 8px;
        }

        .merchant-badges {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .merchant-status {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
        }

        .merchant-status.active {
            background: #dcfce7;
            color: #166534;
        }

        .merchant-status.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .permissions-content {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .content-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .selected-merchant {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .merchant-avatar {
            width: 48px;
            height: 48px;
            border-radius: var(--radius);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 18px;
        }

        .merchant-details h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0 0 4px 0;
        }

        .merchant-details p {
            font-size: 14px;
            color: var(--gray-500);
            margin: 0;
        }

        .merchant-stats {
            display: flex;
            gap: 24px;
            font-size: 12px;
            color: var(--gray-500);
        }

        .bulk-toolbar {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            padding: 12px 24px;
            display: none;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }

        .bulk-toolbar.show {
            display: flex;
        }

        .bulk-info {
            font-size: 14px;
            color: #1e40af;
            font-weight: 500;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
        }

        .permission-tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid var(--gray-200);
        }

        .tab-button {
            padding: 16px 24px;
            border: none;
            background: none;
            color: var(--gray-500);
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .tab-button.active {
            color: var(--primary);
            border-bottom-color: var(--primary);
        }

        .tab-content {
            padding: 24px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .permission-section {
            margin-bottom: 32px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 12px 16px;
            background: var(--gray-50);
            border-radius: var(--radius);
            border: 1px solid var(--gray-200);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .section-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .section-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--gray-500);
        }

        .toggle-switch {
            width: 48px;
            height: 24px;
            background: var(--gray-200);
            border-radius: 12px;
            position: relative;
            cursor: pointer;
            transition: background 0.2s;
        }

        .toggle-switch.active {
            background: var(--primary);
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
        }

        .toggle-switch.active::after {
            transform: translateX(24px);
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
        }

        .permission-card {
            border: 2px solid var(--gray-200);
            border-radius: var(--radius);
            padding: 20px;
            background: white;
            transition: all 0.2s;
        }

        .permission-card:hover {
            border-color: var(--primary);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .permission-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .permission-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            margin-right: 12px;
        }

        .permission-icon.products { background: var(--success); }
        .permission-icon.orders { background: var(--primary); }
        .permission-icon.customers { background: #8b5cf6; }
        .permission-icon.marketing { background: var(--warning); }
        .permission-icon.analytics { background: var(--danger); }
        .permission-icon.finance { background: #06b6d4; }
        .permission-icon.settings { background: var(--gray-500); }

        .permission-name {
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
            flex: 1;
            font-size: 16px;
        }

        .permission-items {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--gray-100);
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-label {
            font-size: 14px;
            color: var(--gray-700);
        }

        .permission-checkbox {
            width: 18px;
            height: 18px;
            accent-color: var(--primary);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: var(--gray-200);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1100;
            padding: 12px 20px;
            border-radius: var(--radius);
            color: white;
            font-size: 14px;
            min-width: 300px;
            transform: translateX(400px);
            transition: transform 0.3s;
        }

        .notification.show { transform: translateX(0); }
        .notification.success { background: var(--success); }
        .notification.warning { background: var(--warning); }
        .notification.error { background: var(--danger); }
        .notification.info { background: var(--primary); }

        .changes-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--warning);
            color: white;
            padding: 12px 16px;
            border-radius: var(--radius);
            font-size: 14px;
            display: none;
            align-items: center;
            gap: 8px;
            box-shadow: var(--shadow);
        }

        .changes-indicator.show {
            display: flex;
        }

        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
            }
            .sidebar-panel {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .permissions-page {
                padding: 12px;
            }
            .header-content {
                flex-direction: column;
                align-items: stretch;
            }
            .permissions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="permissions-page">
        <div class="page-header">
            <div class="header-content">
                <div>
                    <h1 class="page-title">商户权限管理</h1>
                    <p class="page-subtitle">统一管理商户权限，支持批量操作和审批流程</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="exportPermissions()">
                        <i class="fas fa-download"></i> 导出
                    </button>
                    <button class="btn btn-secondary" onclick="showTemplateModal()">
                        <i class="fas fa-copy"></i> 模板
                    </button>
                    <button class="btn btn-warning" onclick="showApprovalModal()">
                        <i class="fas fa-clipboard-check"></i> 审批
                    </button>
                    <button class="btn btn-primary" onclick="savePermissions()">
                        <i class="fas fa-save"></i> 保存
                    </button>
                </div>
            </div>
        </div>

        <div class="main-layout">
            <div class="sidebar-panel">
                <div class="panel-header">
                    <h2 class="panel-title">商户列表</h2>
                    <span class="merchant-count" id="merchantCount">0</span>
                </div>

                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索商户..." 
                           id="merchantSearch" oninput="searchMerchants(this.value)">
                </div>

                <div class="filter-tabs">
                    <div class="filter-tab active" data-filter="all">全部</div>
                    <div class="filter-tab" data-filter="active">正常</div>
                    <div class="filter-tab" data-filter="pending">待审</div>
                    <div class="filter-tab" data-filter="premium">高级</div>
                </div>

                <div class="merchants-list" id="merchantsList"></div>
            </div>

            <div class="permissions-content">
                <div class="content-header">
                    <div class="selected-merchant" id="selectedMerchantInfo" style="display: none;">
                        <div class="merchant-avatar" id="merchantAvatar">商</div>
                        <div class="merchant-details">
                            <h3 id="merchantName">未选择商户</h3>
                            <p id="merchantDesc">请选择商户</p>
                        </div>
                    </div>

                    <div class="merchant-stats" id="merchantStats" style="display: none;">
                        <span>总数: <strong id="totalPermissions">0</strong></span>
                        <span>启用: <strong id="enabledPermissions">0</strong></span>
                        <span>待审: <strong id="pendingPermissions">0</strong></span>
                    </div>
                </div>

                <div class="bulk-toolbar" id="bulkActions">
                    <div class="bulk-info">已选择 <span id="selectedCount">0</span> 个商户</div>
                    <div class="bulk-actions">
                        <button class="btn btn-sm btn-success" onclick="bulkEnablePermissions()">
                            <i class="fas fa-check"></i> 启用
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="bulkDisablePermissions()">
                            <i class="fas fa-times"></i> 禁用
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="bulkApplyTemplate()">
                            <i class="fas fa-copy"></i> 应用模板
                        </button>
                    </div>
                </div>

                <div class="permission-tabs">
                    <button class="tab-button active" data-tab="matrix">权限矩阵</button>
                    <button class="tab-button" data-tab="templates">权限模板</button>
                    <button class="tab-button" data-tab="logs">操作日志</button>
                </div>

                <div class="tab-content active" id="matrixTab">
                    <div id="permissionsSections">
                        <div class="empty-state">
                            <i class="fas fa-users-cog"></i>
                            <h3>请选择商户</h3>
                            <p>从左侧列表选择商户开始管理权限</p>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="templatesTab">
                    <div class="template-list"></div>
                </div>

                <div class="tab-content" id="logsTab">
                    <div class="logs-list"></div>
                </div>
            </div>
        </div>

        <div class="changes-indicator" id="changesIndicator">
            <i class="fas fa-exclamation-triangle"></i>
            <span>有未保存的更改</span>
            <button class="btn btn-sm btn-primary" onclick="savePermissions()">保存</button>
        </div>
    </div>

    <script src="permission-manager.js"></script>
    <script>
        const manager = new MerchantPermissionManager();
        
        function savePermissions() { manager.savePermissions(); }
        function exportPermissions() { manager.exportPermissions(); }
        function showTemplateModal() { manager.showTemplateModal(); }
        function showApprovalModal() { manager.showApprovalModal(); }
        function bulkEnablePermissions() { manager.bulkEnablePermissions(); }
        function bulkDisablePermissions() { manager.bulkDisablePermissions(); }
        function bulkApplyTemplate() { manager.bulkApplyTemplate(); }
        function searchMerchants(keyword) { manager.searchMerchants(keyword); }

        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter-tab')) {
                document.querySelectorAll('.filter-tab').forEach(f => f.classList.remove('active'));
                e.target.classList.add('active');
                manager.filterMerchants(e.target.dataset.filter);
            }
            
            if (e.target.classList.contains('tab-button')) {
                document.querySelectorAll('.tab-button').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                e.target.classList.add('active');
                document.getElementById(e.target.dataset.tab + 'Tab').classList.add('active');
            }
        });
    </script>
</body>
</html> 