/**
 * 商家权限管理增强功能
 * 包含权限依赖、批量操作、审批流程等高级功能
 */

class MerchantPermissionManager {
    constructor() {
        this.selectedMerchants = new Set();
        this.permissionDependencies = {
            'create_ads': ['view_marketing'],
            'manage_finance': ['view_finance'],
            'export_data': ['view_analytics'],
            'bulk_operations': ['view_products'],
            'api_access': ['advanced_features']
        };
        
        this.permissionGroups = {
            'basic': ['view_products', 'manage_inventory', 'view_orders'],
            'standard': ['view_products', 'manage_inventory', 'view_orders', 'view_customers', 'basic_marketing'],
            'premium': ['view_products', 'manage_inventory', 'view_orders', 'view_customers', 'basic_marketing', 'advanced_marketing', 'view_analytics'],
            'enterprise': ['*'] // 所有权限
        };
        
        this.pendingApprovals = new Map();
        this.changeHistory = [];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadMerchantData();
        this.setupPermissionMatrix();
        this.initializeBulkOperations();
        this.setupApprovalWorkflow();
    }
    
    setupEventListeners() {
        // 商户选择
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('merchant-selector')) {
                this.handleMerchantSelection(e.target);
            }
        });
        
        // 权限复选框变更
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('permission-checkbox')) {
                this.handlePermissionChange(e.target);
            }
        });
        
        // 批量操作
        document.addEventListener('click', (e) => {
            if (e.target.dataset.action) {
                this.handleBulkAction(e.target.dataset.action);
            }
        });
        
        // 权限模板应用
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('apply-template')) {
                this.applyPermissionTemplate(e.target.dataset.template);
            }
        });
    }
    
    loadMerchantData() {
        // 模拟加载商户数据
        const merchants = [
            {
                id: 'merchant_001',
                name: '星辰数码专营店',
                type: 'premium',
                status: 'active',
                joinDate: '2024-01-15',
                permissions: ['view_products', 'manage_inventory', 'view_orders', 'view_analytics']
            },
            {
                id: 'merchant_002', 
                name: '时尚生活馆',
                type: 'standard',
                status: 'active',
                joinDate: '2024-02-20',
                permissions: ['view_products', 'manage_inventory', 'view_orders']
            },
            {
                id: 'merchant_003',
                name: '优品食材供应',
                type: 'basic',
                status: 'pending_approval',
                joinDate: '2024-03-10',
                permissions: ['view_products']
            }
        ];
        
        this.renderMerchantList(merchants);
    }
    
    renderMerchantList(merchants) {
        const container = document.querySelector('.merchants-list');
        if (!container) return;
        
        container.innerHTML = merchants.map(merchant => `
            <div class="merchant-item" data-merchant-id="${merchant.id}">
                <div class="merchant-checkbox-wrapper">
                    <input type="checkbox" class="merchant-selector" value="${merchant.id}">
                </div>
                <div class="merchant-content">
                    <div class="merchant-name">${merchant.name}</div>
                    <div class="merchant-info">
                        商户ID: ${merchant.id} | 类型: ${merchant.type}
                    </div>
                    <div class="merchant-meta">
                        <span class="merchant-status ${merchant.status}">${this.getStatusText(merchant.status)}</span>
                        <span class="join-date">加入时间: ${merchant.joinDate}</span>
                    </div>
                </div>
                <div class="merchant-actions">
                    <button class="btn-sm" onclick="merchantPermissionManager.viewMerchantDetail('${merchant.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-sm" onclick="merchantPermissionManager.editMerchantPermissions('${merchant.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }
    
    getStatusText(status) {
        const statusMap = {
            'active': '正常营业',
            'pending_approval': '待审核',
            'suspended': '已暂停',
            'rejected': '已拒绝'
        };
        return statusMap[status] || status;
    }
    
    setupPermissionMatrix() {
        const permissions = [
            {
                category: 'products',
                name: '商品管理',
                icon: 'fas fa-box',
                color: '#10b981',
                items: [
                    { key: 'view_products', name: '查看商品', required: true },
                    { key: 'add_products', name: '添加商品', depends: ['view_products'] },
                    { key: 'edit_products', name: '编辑商品', depends: ['view_products'] },
                    { key: 'delete_products', name: '删除商品', depends: ['edit_products'] },
                    { key: 'manage_inventory', name: '库存管理', depends: ['view_products'] },
                    { key: 'bulk_operations', name: '批量操作', depends: ['edit_products'] }
                ]
            },
            {
                category: 'orders',
                name: '订单管理', 
                icon: 'fas fa-shopping-cart',
                color: '#3b82f6',
                items: [
                    { key: 'view_orders', name: '查看订单', required: true },
                    { key: 'process_orders', name: '处理订单', depends: ['view_orders'] },
                    { key: 'cancel_orders', name: '取消订单', depends: ['process_orders'] },
                    { key: 'refund_orders', name: '退款处理', depends: ['process_orders'], requiresApproval: true },
                    { key: 'export_orders', name: '导出订单', depends: ['view_orders'] }
                ]
            },
            {
                category: 'customers',
                name: '客户管理',
                icon: 'fas fa-users',
                color: '#8b5cf6',
                items: [
                    { key: 'view_customers', name: '查看客户' },
                    { key: 'edit_customers', name: '编辑客户', depends: ['view_customers'] },
                    { key: 'export_customers', name: '导出客户', depends: ['view_customers'], requiresApproval: true },
                    { key: 'customer_service', name: '客服功能', depends: ['view_customers'] }
                ]
            },
            {
                category: 'marketing',
                name: '营销推广',
                icon: 'fas fa-bullhorn',
                color: '#f59e0b',
                items: [
                    { key: 'view_marketing', name: '查看营销数据' },
                    { key: 'basic_marketing', name: '基础营销工具', depends: ['view_marketing'] },
                    { key: 'advanced_marketing', name: '高级营销工具', depends: ['basic_marketing'], requiresApproval: true },
                    { key: 'create_ads', name: '创建广告', depends: ['advanced_marketing'], requiresApproval: true },
                    { key: 'coupon_management', name: '优惠券管理', depends: ['basic_marketing'] }
                ]
            },
            {
                category: 'analytics',
                name: '数据分析',
                icon: 'fas fa-chart-bar',
                color: '#ef4444',
                items: [
                    { key: 'view_analytics', name: '查看分析报告' },
                    { key: 'advanced_analytics', name: '高级分析', depends: ['view_analytics'] },
                    { key: 'export_data', name: '导出数据', depends: ['view_analytics'], requiresApproval: true },
                    { key: 'custom_reports', name: '自定义报告', depends: ['advanced_analytics'], requiresApproval: true }
                ]
            },
            {
                category: 'finance',
                name: '财务管理',
                icon: 'fas fa-dollar-sign',
                color: '#06b6d4',
                items: [
                    { key: 'view_finance', name: '查看财务数据' },
                    { key: 'manage_finance', name: '财务管理', depends: ['view_finance'], requiresApproval: true },
                    { key: 'withdraw_funds', name: '提现申请', depends: ['view_finance'], requiresApproval: true },
                    { key: 'view_settlements', name: '查看结算记录', depends: ['view_finance'] }
                ]
            },
            {
                category: 'settings',
                name: '系统设置',
                icon: 'fas fa-cog',
                color: '#6b7280',
                items: [
                    { key: 'basic_settings', name: '基础设置' },
                    { key: 'advanced_settings', name: '高级设置', depends: ['basic_settings'], requiresApproval: true },
                    { key: 'api_access', name: 'API接口', depends: ['advanced_settings'], requiresApproval: true },
                    { key: 'webhook_management', name: 'Webhook管理', depends: ['api_access'], requiresApproval: true }
                ]
            }
        ];
        
        this.renderPermissionMatrix(permissions);
    }
    
    renderPermissionMatrix(permissions) {
        const container = document.querySelector('#permissions-content .tab-content');
        if (!container) return;
        
        container.innerHTML = permissions.map(category => `
            <div class="permission-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <div class="permission-icon ${category.category}" style="background: ${category.color}">
                            <i class="${category.icon}"></i>
                        </div>
                        ${category.name}
                    </h3>
                    <div class="section-toggle" data-category="${category.category}">
                        <div class="toggle-switch"></div>
                        <span class="toggle-label">全部启用</span>
                    </div>
                </div>
                <div class="permission-card">
                    <ul class="permission-items">
                        ${category.items.map(item => `
                            <li class="permission-item ${item.depends ? 'dependent' : ''}" data-permission="${item.key}">
                                <div class="permission-content">
                                    <label class="permission-label">${item.name}</label>
                                    ${item.depends ? `<div class="permission-dependency">依赖: ${item.depends.join(', ')}</div>` : ''}
                                    ${item.requiresApproval ? '<span class="approval-required"><i class="fas fa-shield-alt"></i> 需要审批</span>' : ''}
                                </div>
                                <div class="permission-controls">
                                    <input type="checkbox" class="permission-checkbox" 
                                           data-permission="${item.key}" 
                                           data-category="${category.category}"
                                           ${item.required ? 'checked disabled' : ''}
                                           ${item.requiresApproval ? 'data-requires-approval="true"' : ''}>
                                    ${item.requiresApproval ? '<span class="approval-status pending" style="display: none;"><i class="fas fa-clock"></i> 待审批</span>' : ''}
                                </div>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            </div>
        `).join('');
    }
    
    handleMerchantSelection(checkbox) {
        const merchantId = checkbox.value;
        
        if (checkbox.checked) {
            this.selectedMerchants.add(merchantId);
        } else {
            this.selectedMerchants.delete(merchantId);
        }
        
        this.updateBulkToolbar();
    }
    
    updateBulkToolbar() {
        const toolbar = document.querySelector('.bulk-toolbar');
        const count = this.selectedMerchants.size;
        
        if (count > 0) {
            toolbar.classList.add('show');
            toolbar.querySelector('.bulk-info').textContent = `已选择 ${count} 个商户`;
        } else {
            toolbar.classList.remove('show');
        }
    }
    
    handlePermissionChange(checkbox) {
        const permission = checkbox.dataset.permission;
        const category = checkbox.dataset.category;
        const requiresApproval = checkbox.dataset.requiresApproval === 'true';
        
        if (checkbox.checked) {
            // 检查依赖关系
            if (!this.checkDependencies(permission)) {
                checkbox.checked = false;
                this.showNotification('请先启用依赖的权限', 'warning');
                return;
            }
            
            // 如果需要审批，标记为待审批状态
            if (requiresApproval) {
                this.submitForApproval(permission, 'enable');
                this.showApprovalStatus(checkbox, 'pending');
            }
        } else {
            // 检查是否有其他权限依赖此权限
            if (this.hasBlockingDependencies(permission)) {
                checkbox.checked = true;
                this.showNotification('其他权限依赖此权限，无法禁用', 'warning');
                return;
            }
            
            if (requiresApproval) {
                this.submitForApproval(permission, 'disable');
                this.showApprovalStatus(checkbox, 'pending');
            }
        }
        
        this.updateCategoryToggle(category);
        this.showChangesIndicator();
    }
    
    checkDependencies(permission) {
        const dependencies = this.permissionDependencies[permission];
        if (!dependencies) return true;
        
        return dependencies.every(dep => {
            const depCheckbox = document.querySelector(`[data-permission="${dep}"]`);
            return depCheckbox && depCheckbox.checked;
        });
    }
    
    hasBlockingDependencies(permission) {
        return Object.entries(this.permissionDependencies).some(([key, deps]) => {
            if (deps.includes(permission)) {
                const checkbox = document.querySelector(`[data-permission="${key}"]`);
                return checkbox && checkbox.checked;
            }
            return false;
        });
    }
    
    submitForApproval(permission, action) {
        const approvalId = `approval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        this.pendingApprovals.set(approvalId, {
            permission,
            action,
            merchant: this.getCurrentMerchant(),
            submittedAt: new Date(),
            status: 'pending'
        });
        
        // 模拟审批流程
        setTimeout(() => {
            this.processApproval(approvalId, Math.random() > 0.3 ? 'approved' : 'rejected');
        }, 2000 + Math.random() * 3000);
    }
    
    processApproval(approvalId, status) {
        const approval = this.pendingApprovals.get(approvalId);
        if (!approval) return;
        
        approval.status = status;
        approval.processedAt = new Date();
        
        const checkbox = document.querySelector(`[data-permission="${approval.permission}"]`);
        if (checkbox) {
            if (status === 'approved') {
                this.showApprovalStatus(checkbox, 'approved');
                setTimeout(() => this.hideApprovalStatus(checkbox), 3000);
            } else {
                this.showApprovalStatus(checkbox, 'rejected');
                // 恢复原状态
                checkbox.checked = approval.action !== 'enable';
                setTimeout(() => this.hideApprovalStatus(checkbox), 5000);
            }
        }
        
        this.showNotification(
            `权限 "${approval.permission}" 的${approval.action === 'enable' ? '启用' : '禁用'}申请已${status === 'approved' ? '批准' : '拒绝'}`,
            status === 'approved' ? 'success' : 'error'
        );
    }
    
    showApprovalStatus(checkbox, status) {
        const statusElement = checkbox.parentElement.querySelector('.approval-status');
        if (statusElement) {
            statusElement.className = `approval-status ${status}`;
            statusElement.style.display = 'inline-flex';
            
            const statusTexts = {
                'pending': '<i class="fas fa-clock"></i> 待审批',
                'approved': '<i class="fas fa-check"></i> 已批准', 
                'rejected': '<i class="fas fa-times"></i> 已拒绝'
            };
            
            statusElement.innerHTML = statusTexts[status];
        }
    }
    
    hideApprovalStatus(checkbox) {
        const statusElement = checkbox.parentElement.querySelector('.approval-status');
        if (statusElement) {
            statusElement.style.display = 'none';
        }
    }
    
    initializeBulkOperations() {
        const toolbar = document.createElement('div');
        toolbar.className = 'bulk-toolbar';
        toolbar.innerHTML = `
            <div class="bulk-info">已选择 0 个商户</div>
            <div class="bulk-actions">
                <button class="btn btn-sm" data-action="apply-template">
                    <i class="fas fa-magic"></i> 应用模板
                </button>
                <button class="btn btn-sm" data-action="batch-enable">
                    <i class="fas fa-check-circle"></i> 批量启用
                </button>
                <button class="btn btn-sm" data-action="batch-disable">
                    <i class="fas fa-times-circle"></i> 批量禁用
                </button>
                <button class="btn btn-sm btn-warning" data-action="export-permissions">
                    <i class="fas fa-download"></i> 导出权限
                </button>
            </div>
        `;
        
        const container = document.querySelector('.sidebar-panel');
        container.insertBefore(toolbar, container.firstChild.nextSibling);
    }
    
    handleBulkAction(action) {
        if (this.selectedMerchants.size === 0) {
            this.showNotification('请先选择商户', 'warning');
            return;
        }
        
        switch (action) {
            case 'apply-template':
                this.showTemplateSelector();
                break;
            case 'batch-enable':
                this.showBatchPermissionSelector('enable');
                break;
            case 'batch-disable':
                this.showBatchPermissionSelector('disable');
                break;
            case 'export-permissions':
                this.exportPermissions();
                break;
        }
    }
    
    showTemplateSelector() {
        const modal = this.createModal('选择权限模板', `
            <div class="template-selector">
                <div class="role-templates">
                    <div class="role-template" data-template="basic">
                        <div class="role-icon basic"><i class="fas fa-star"></i></div>
                        <div class="role-name">基础商户</div>
                        <div class="role-description">基本商品和订单管理</div>
                    </div>
                    <div class="role-template" data-template="standard">
                        <div class="role-icon standard"><i class="fas fa-star-half"></i></div>
                        <div class="role-name">标准商户</div>
                        <div class="role-description">商品、订单、客户管理</div>
                    </div>
                    <div class="role-template" data-template="premium">
                        <div class="role-icon premium"><i class="fas fa-crown"></i></div>
                        <div class="role-name">高级商户</div>
                        <div class="role-description">完整功能和高级分析</div>
                    </div>
                    <div class="role-template" data-template="enterprise">
                        <div class="role-icon enterprise"><i class="fas fa-building"></i></div>
                        <div class="role-name">企业定制</div>
                        <div class="role-description">所有功能和API接口</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="merchantPermissionManager.closeModal()">取消</button>
                <button class="btn btn-primary" onclick="merchantPermissionManager.applySelectedTemplate()">应用模板</button>
            </div>
        `);
        
        // 模板选择事件
        modal.querySelectorAll('.role-template').forEach(template => {
            template.addEventListener('click', () => {
                modal.querySelectorAll('.role-template').forEach(t => t.classList.remove('selected'));
                template.classList.add('selected');
            });
        });
    }
    
    applySelectedTemplate() {
        const selectedTemplate = document.querySelector('.role-template.selected');
        if (!selectedTemplate) {
            this.showNotification('请选择一个模板', 'warning');
            return;
        }
        
        const template = selectedTemplate.dataset.template;
        const permissions = this.permissionGroups[template];
        
        if (permissions) {
            // 应用权限模板到选中的商户
            this.selectedMerchants.forEach(merchantId => {
                this.applyPermissionsToMerchant(merchantId, permissions);
            });
            
            this.showNotification(`已为 ${this.selectedMerchants.size} 个商户应用 ${selectedTemplate.querySelector('.role-name').textContent} 模板`, 'success');
            this.closeModal();
        }
    }
    
    applyPermissionsToMerchant(merchantId, permissions) {
        // 模拟应用权限到商户
        console.log(`Applying permissions to merchant ${merchantId}:`, permissions);
        
        // 更新UI中的权限状态
        if (permissions.includes('*')) {
            // 启用所有权限
            document.querySelectorAll('.permission-checkbox:not([disabled])').forEach(checkbox => {
                checkbox.checked = true;
            });
        } else {
            // 启用指定权限
            permissions.forEach(permission => {
                const checkbox = document.querySelector(`[data-permission="${permission}"]`);
                if (checkbox && !checkbox.disabled) {
                    checkbox.checked = true;
                }
            });
        }
        
        this.updateAllCategoryToggles();
    }
    
    updateAllCategoryToggles() {
        document.querySelectorAll('[data-category]').forEach(toggle => {
            const category = toggle.dataset.category;
            this.updateCategoryToggle(category);
        });
    }
    
    updateCategoryToggle(category) {
        const categoryCheckboxes = document.querySelectorAll(`[data-category="${category}"]`);
        const checkedBoxes = document.querySelectorAll(`[data-category="${category}"]:checked`);
        const toggle = document.querySelector(`[data-category="${category}"].section-toggle .toggle-switch`);
        
        if (toggle) {
            if (checkedBoxes.length === categoryCheckboxes.length) {
                toggle.classList.add('active');
            } else {
                toggle.classList.remove('active');
            }
        }
    }
    
    setupApprovalWorkflow() {
        // 创建审批工作流界面
        const approvalTab = document.createElement('button');
        approvalTab.className = 'tab-button';
        approvalTab.textContent = '审批流程';
        approvalTab.onclick = () => this.switchTab('approval');
        
        const tabHeader = document.querySelector('.permission-tabs');
        if (tabHeader) {
            tabHeader.appendChild(approvalTab);
        }
    }
    
    showChangesIndicator() {
        const indicator = document.querySelector('.changes-indicator');
        if (indicator) {
            indicator.classList.add('show');
        }
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
    
    createModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-container">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="merchantPermissionManager.closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">${content}</div>
            </div>
        `;
        
        document.body.appendChild(modal);
        return modal;
    }
    
    closeModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }
    }
    
    getCurrentMerchant() {
        const activeItem = document.querySelector('.merchant-item.active');
        return activeItem ? activeItem.dataset.merchantId : null;
    }
    
    viewMerchantDetail(merchantId) {
        // 跳转到商户详情页
        window.open(`merchant-detail.html?id=${merchantId}`, '_blank');
    }
    
    editMerchantPermissions(merchantId) {
        // 在右侧面板显示该商户的权限设置
        this.loadMerchantPermissions(merchantId);
    }
    
    loadMerchantPermissions(merchantId) {
        // 模拟加载商户权限数据
        console.log(`Loading permissions for merchant: ${merchantId}`);
        
        // 高亮当前商户
        document.querySelectorAll('.merchant-item').forEach(item => {
            item.classList.remove('active');
        });
        
        document.querySelector(`[data-merchant-id="${merchantId}"]`).classList.add('active');
    }
    
    exportPermissions() {
        const merchantIds = Array.from(this.selectedMerchants);
        const exportData = {
            exportTime: new Date().toISOString(),
            merchants: merchantIds,
            permissions: this.getSelectedPermissions()
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `merchant_permissions_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        this.showNotification('权限数据导出成功', 'success');
    }
    
    getSelectedPermissions() {
        const permissions = {};
        document.querySelectorAll('.permission-checkbox:checked').forEach(checkbox => {
            const category = checkbox.dataset.category;
            const permission = checkbox.dataset.permission;
            
            if (!permissions[category]) {
                permissions[category] = [];
            }
            permissions[category].push(permission);
        });
        
        return permissions;
    }
}

// 初始化权限管理器
let merchantPermissionManager;
document.addEventListener('DOMContentLoaded', () => {
    merchantPermissionManager = new MerchantPermissionManager();
}); 