<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户入驻申请 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .application-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            margin-bottom: 32px;
            text-align: center;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
            margin-top: 8px;
        }

        .application-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .progress-bar {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e2e8f0;
            z-index: 1;
        }

        .progress-line {
            position: absolute;
            top: 20px;
            left: 0;
            height: 2px;
            background: #3b82f6;
            transition: width 0.3s;
            z-index: 2;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 3;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s;
        }

        .step.active .step-circle {
            background: #3b82f6;
            color: white;
        }

        .step.completed .step-circle {
            background: #10b981;
            color: white;
        }

        .step-label {
            margin-top: 8px;
            font-size: 12px;
            color: #64748b;
            text-align: center;
        }

        .step.active .step-label {
            color: #3b82f6;
            font-weight: 600;
        }

        .application-form {
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e2e8f0;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row.full {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-label.required::after {
            content: '*';
            color: #ef4444;
            margin-left: 4px;
        }

        .form-input,
        .form-select,
        .form-textarea {
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            transition: all 0.2s;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .upload-area.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .upload-icon {
            font-size: 32px;
            color: #9ca3af;
            margin-bottom: 12px;
        }

        .upload-text {
            color: #6b7280;
            font-size: 14px;
        }

        .upload-hint {
            color: #9ca3af;
            font-size: 12px;
            margin-top: 4px;
        }

        .file-list {
            margin-top: 16px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f8fafc;
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .file-icon {
            color: #3b82f6;
        }

        .file-name {
            font-size: 14px;
            color: #374151;
        }

        .file-size {
            font-size: 12px;
            color: #6b7280;
        }

        .file-remove {
            color: #ef4444;
            cursor: pointer;
            padding: 4px;
        }

        .form-actions {
            display: flex;
            gap: 16px;
            justify-content: flex-end;
            margin-top: 40px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-top: 24px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-top: 2px;
        }

        .checkbox-label {
            font-size: 14px;
            color: #374151;
            line-height: 1.5;
        }

        .checkbox-label a {
            color: #3b82f6;
            text-decoration: none;
        }

        .checkbox-label a:hover {
            text-decoration: underline;
        }

        .error-message {
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
        }

        .success-message {
            background: #dcfce7;
            color: #166534;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        @media (max-width: 768px) {
            .application-page {
                padding: 16px;
            }

            .application-form {
                padding: 24px 16px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .progress-steps {
                flex-wrap: wrap;
                gap: 16px;
            }

            .step {
                flex: 1;
                min-width: 80px;
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="application-page">
        <div class="page-header">
            <h1 class="page-title">商户入驻申请</h1>
            <p class="page-subtitle">欢迎加入TeleShop平台，开启您的电商之旅</p>
        </div>

        <div class="application-container">
            <!-- 进度条 -->
            <div class="progress-bar">
                <div class="progress-steps">
                    <div class="progress-line" style="width: 0%"></div>
                    <div class="step active" data-step="1">
                        <div class="step-circle">1</div>
                        <div class="step-label">基本信息</div>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-circle">2</div>
                        <div class="step-label">资质认证</div>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-circle">3</div>
                        <div class="step-label">店铺设置</div>
                    </div>
                    <div class="step" data-step="4">
                        <div class="step-circle">4</div>
                        <div class="step-label">提交审核</div>
                    </div>
                </div>
            </div>

            <!-- 申请表单 -->
            <div class="application-form">
                <form id="merchantApplicationForm">
                    <!-- 步骤1: 基本信息 -->
                    <div class="form-step active" data-step="1">
                        <div class="form-section">
                            <h3 class="section-title">基本信息</h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label required">商户类型</label>
                                    <select class="form-select" name="merchantType" required>
                                        <option value="">请选择商户类型</option>
                                        <option value="enterprise">企业商户</option>
                                        <option value="individual">个人商户</option>
                                        <option value="flagship">品牌旗舰店</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label required">店铺名称</label>
                                    <input type="text" class="form-input" name="storeName" placeholder="请输入店铺名称" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label required">联系人姓名</label>
                                    <input type="text" class="form-input" name="contactName" placeholder="请输入联系人姓名" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label required">联系人电话</label>
                                    <input type="tel" class="form-input" name="contactPhone" placeholder="请输入联系人电话" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label required">联系邮箱</label>
                                    <input type="email" class="form-input" name="contactEmail" placeholder="请输入联系邮箱" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">QQ/微信</label>
                                    <input type="text" class="form-input" name="contactQQ" placeholder="请输入QQ或微信号">
                                </div>
                            </div>

                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label required">经营地址</label>
                                    <input type="text" class="form-input" name="businessAddress" placeholder="请输入详细经营地址" required>
                                </div>
                            </div>

                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label">经营范围</label>
                                    <textarea class="form-textarea" name="businessScope" placeholder="请描述您的主要经营范围和产品类别"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤2: 资质认证 -->
                    <div class="form-step" data-step="2">
                        <div class="form-section">
                            <h3 class="section-title">资质认证</h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label required">营业执照号</label>
                                    <input type="text" class="form-input" name="businessLicense" placeholder="请输入营业执照号" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label required">税务登记号</label>
                                    <input type="text" class="form-input" name="taxNumber" placeholder="请输入税务登记号" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label required">法人姓名</label>
                                    <input type="text" class="form-input" name="legalPerson" placeholder="请输入法人姓名" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label required">法人身份证号</label>
                                    <input type="text" class="form-input" name="legalIdCard" placeholder="请输入法人身份证号" required>
                                </div>
                            </div>

                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label required">营业执照</label>
                                    <div class="upload-area" onclick="triggerFileUpload('businessLicenseFile')">
                                        <div class="upload-icon">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                        </div>
                                        <div class="upload-text">点击上传营业执照</div>
                                        <div class="upload-hint">支持JPG、PNG格式，文件大小不超过5MB</div>
                                    </div>
                                    <input type="file" id="businessLicenseFile" accept="image/*" style="display: none;" onchange="handleFileUpload(this, 'businessLicenseList')">
                                    <div id="businessLicenseList" class="file-list"></div>
                                </div>
                            </div>

                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label required">法人身份证</label>
                                    <div class="upload-area" onclick="triggerFileUpload('idCardFile')">
                                        <div class="upload-icon">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                        </div>
                                        <div class="upload-text">点击上传法人身份证（正反面）</div>
                                        <div class="upload-hint">支持JPG、PNG格式，文件大小不超过5MB</div>
                                    </div>
                                    <input type="file" id="idCardFile" accept="image/*" multiple style="display: none;" onchange="handleFileUpload(this, 'idCardList')">
                                    <div id="idCardList" class="file-list"></div>
                                </div>
                            </div>

                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label">其他资质证明</label>
                                    <div class="upload-area" onclick="triggerFileUpload('otherCertFile')">
                                        <div class="upload-icon">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                        </div>
                                        <div class="upload-text">点击上传其他资质证明（可选）</div>
                                        <div class="upload-hint">如品牌授权书、代理证明等</div>
                                    </div>
                                    <input type="file" id="otherCertFile" accept="image/*,application/pdf" multiple style="display: none;" onchange="handleFileUpload(this, 'otherCertList')">
                                    <div id="otherCertList" class="file-list"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤3: 店铺设置 -->
                    <div class="form-step" data-step="3">
                        <div class="form-section">
                            <h3 class="section-title">店铺设置</h3>
                            
                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label required">店铺LOGO</label>
                                    <div class="upload-area" onclick="triggerFileUpload('logoFile')">
                                        <div class="upload-icon">
                                            <i class="fas fa-image"></i>
                                        </div>
                                        <div class="upload-text">点击上传店铺LOGO</div>
                                        <div class="upload-hint">建议尺寸200x200px，支持JPG、PNG格式</div>
                                    </div>
                                    <input type="file" id="logoFile" accept="image/*" style="display: none;" onchange="handleFileUpload(this, 'logoList')">
                                    <div id="logoList" class="file-list"></div>
                                </div>
                            </div>

                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label required">店铺简介</label>
                                    <textarea class="form-textarea" name="storeDescription" placeholder="请简要介绍您的店铺特色、主营产品等" required style="min-height: 120px;"></textarea>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label required">主营类目</label>
                                    <select class="form-select" name="mainCategory" required>
                                        <option value="">请选择主营类目</option>
                                        <option value="electronics">电子数码</option>
                                        <option value="clothing">服装鞋帽</option>
                                        <option value="home">家居用品</option>
                                        <option value="beauty">美妆个护</option>
                                        <option value="sports">运动户外</option>
                                        <option value="food">食品饮料</option>
                                        <option value="books">图书文具</option>
                                        <option value="toys">母婴玩具</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">期望佣金比例</label>
                                    <select class="form-select" name="commissionRate">
                                        <option value="">请选择佣金比例</option>
                                        <option value="3">3%</option>
                                        <option value="5">5%</option>
                                        <option value="8">8%</option>
                                        <option value="10">10%</option>
                                        <option value="custom">自定义</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">客服电话</label>
                                    <input type="tel" class="form-input" name="servicePhone" placeholder="请输入客服电话">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">客服邮箱</label>
                                    <input type="email" class="form-input" name="serviceEmail" placeholder="请输入客服邮箱">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤4: 提交审核 -->
                    <div class="form-step" data-step="4">
                        <div class="form-section">
                            <h3 class="section-title">提交审核</h3>
                            
                            <div style="background: #f8fafc; padding: 24px; border-radius: 8px; margin-bottom: 24px;">
                                <h4 style="margin: 0 0 16px 0; color: #1e293b;">审核说明</h4>
                                <ul style="margin: 0; padding-left: 20px; color: #64748b; line-height: 1.6;">
                                    <li>我们将在1-3个工作日内完成审核</li>
                                    <li>审核期间请保持联系方式畅通</li>
                                    <li>如有问题我们会及时与您联系</li>
                                    <li>审核通过后您将收到开店通知</li>
                                </ul>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="agreeTerms" required>
                                <label class="checkbox-label" for="agreeTerms">
                                    我已阅读并同意 <a href="#" target="_blank">《商户入驻协议》</a> 和 <a href="#" target="_blank">《平台服务条款》</a>
                                </label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="confirmInfo" required>
                                <label class="checkbox-label" for="confirmInfo">
                                    我确认以上信息真实有效，如有虚假信息愿承担相应责任
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 表单操作按钮 -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="prevBtn" onclick="previousStep()" style="display: none;">
                            <i class="fas fa-chevron-left"></i>
                            上一步
                        </button>
                        <button type="button" class="btn btn-primary" id="nextBtn" onclick="nextStep()">
                            下一步
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn" style="display: none;">
                            <i class="fas fa-paper-plane"></i>
                            提交申请
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        const totalSteps = 4;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();
            
            // 表单提交事件
            document.getElementById('merchantApplicationForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitApplication();
            });
        });

        // 下一步
        function nextStep() {
            if (validateCurrentStep()) {
                if (currentStep < totalSteps) {
                    currentStep++;
                    showStep(currentStep);
                    updateProgress();
                    updateButtons();
                }
            }
        }

        // 上一步
        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
                updateProgress();
                updateButtons();
            }
        }

        // 显示指定步骤
        function showStep(step) {
            // 隐藏所有步骤
            document.querySelectorAll('.form-step').forEach(el => {
                el.classList.remove('active');
                el.style.display = 'none';
            });
            
            // 显示当前步骤
            const currentStepEl = document.querySelector(`.form-step[data-step="${step}"]`);
            if (currentStepEl) {
                currentStepEl.classList.add('active');
                currentStepEl.style.display = 'block';
            }
        }

        // 更新进度条
        function updateProgress() {
            const progressLine = document.querySelector('.progress-line');
            const steps = document.querySelectorAll('.step');
            
            // 更新进度线
            const progressWidth = ((currentStep - 1) / (totalSteps - 1)) * 100;
            progressLine.style.width = `${progressWidth}%`;
            
            // 更新步骤状态
            steps.forEach((step, index) => {
                const stepNumber = index + 1;
                step.classList.remove('active', 'completed');
                
                if (stepNumber < currentStep) {
                    step.classList.add('completed');
                    step.querySelector('.step-circle').innerHTML = '<i class="fas fa-check"></i>';
                } else if (stepNumber === currentStep) {
                    step.classList.add('active');
                    step.querySelector('.step-circle').textContent = stepNumber;
                } else {
                    step.querySelector('.step-circle').textContent = stepNumber;
                }
            });
        }

        // 更新按钮状态
        function updateButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const submitBtn = document.getElementById('submitBtn');
            
            // 上一步按钮
            prevBtn.style.display = currentStep > 1 ? 'inline-flex' : 'none';
            
            // 下一步/提交按钮
            if (currentStep === totalSteps) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'inline-flex';
            } else {
                nextBtn.style.display = 'inline-flex';
                submitBtn.style.display = 'none';
            }
        }

        // 验证当前步骤
        function validateCurrentStep() {
            const currentStepEl = document.querySelector(`.form-step[data-step="${currentStep}"]`);
            const requiredFields = currentStepEl.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.style.borderColor = '#ef4444';
                    isValid = false;
                } else {
                    field.style.borderColor = '#d1d5db';
                }
            });
            
            if (!isValid) {
                alert('请填写所有必填项');
            }
            
            return isValid;
        }

        // 文件上传相关函数
        function triggerFileUpload(inputId) {
            document.getElementById(inputId).click();
        }

        function handleFileUpload(input, listId) {
            const files = input.files;
            const listContainer = document.getElementById(listId);
            
            // 清空现有列表（如果是单文件上传）
            if (input.id === 'logoFile' || input.id === 'businessLicenseFile') {
                listContainer.innerHTML = '';
            }
            
            Array.from(files).forEach(file => {
                const fileItem = createFileItem(file);
                listContainer.appendChild(fileItem);
            });
        }

        function createFileItem(file) {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            const fileIcon = getFileIcon(file.type);
            
            fileItem.innerHTML = `
                <div class="file-info">
                    <i class="fas ${fileIcon} file-icon"></i>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">(${fileSize}MB)</span>
                </div>
                <i class="fas fa-times file-remove" onclick="removeFile(this)"></i>
            `;
            
            return fileItem;
        }

        function getFileIcon(fileType) {
            if (fileType.startsWith('image/')) {
                return 'fa-image';
            } else if (fileType === 'application/pdf') {
                return 'fa-file-pdf';
            } else {
                return 'fa-file';
            }
        }

        function removeFile(element) {
            element.parentElement.remove();
        }

        // 提交申请
        function submitApplication() {
            // 这里可以添加表单数据收集和提交逻辑
            const formData = new FormData(document.getElementById('merchantApplicationForm'));
            
            // 模拟提交过程
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                alert('申请已提交成功！我们将在1-3个工作日内完成审核，请保持联系方式畅通。');
                
                // 可以重定向到申请状态页面
                // window.location.href = 'application-status.html';
                
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        }

        // 初始化显示第一步
        showStep(1);
        updateButtons();
    </script>
</body>
</html> 