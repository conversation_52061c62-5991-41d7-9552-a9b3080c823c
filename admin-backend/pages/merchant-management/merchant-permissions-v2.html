<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户权限管理 - TeleShop B2B2C</title>
    
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-500: #6b7280;
            --gray-700: #374151;
            --gray-900: #111827;
        }

        * { box-sizing: border-box; }

        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gray-50);
            color: var(--gray-900);
        }

        .container {
            padding: 24px;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary { background: var(--primary); color: white; }
        .btn-secondary { background: white; color: var(--gray-700); border: 1px solid var(--gray-200); }
        .btn-success { background: var(--success); color: white; }
        .btn-warning { background: var(--warning); color: white; }
        .btn-sm { padding: 4px 8px; font-size: 12px; }

        .btn:hover { transform: translateY(-1px); }

        .main-layout {
            display: grid;
            grid-template-columns: 320px 1fr;
            gap: 24px;
        }

        .sidebar {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
            position: sticky;
            top: 24px;
        }

        .sidebar-header {
            padding: 16px 20px;
            background: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }

        .count-badge {
            background: var(--primary);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .search-section {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-200);
        }

        .search-input {
            width: 100%;
            padding: 8px 12px 8px 36px;
            border: 1px solid var(--gray-200);
            border-radius: 6px;
            font-size: 14px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="%23999"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>') no-repeat 10px center;
            background-size: 16px;
        }

        .filters {
            display: flex;
            gap: 6px;
            margin-top: 12px;
            flex-wrap: wrap;
        }

        .filter {
            padding: 4px 8px;
            background: var(--gray-100);
            border: 1px solid var(--gray-200);
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .merchants-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .merchant-item {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-100);
            cursor: pointer;
            transition: background 0.2s;
            position: relative;
        }

        .merchant-item:hover { background: var(--gray-50); }
        .merchant-item.active { background: #eff6ff; border-left: 3px solid var(--primary); }

        .merchant-checkbox {
            position: absolute;
            top: 16px;
            right: 16px;
        }

        .merchant-info {
            padding-right: 20px;
        }

        .merchant-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .merchant-details {
            font-size: 12px;
            color: var(--gray-500);
            margin-bottom: 8px;
        }

        .badges {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
        }

        .badge.active { background: #dcfce7; color: #166534; }
        .badge.pending { background: #fef3c7; color: #92400e; }
        .badge.premium { background: #e0e7ff; color: #3730a3; }

        .content-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .content-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .merchant-display {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .merchant-info-panel h3 {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 4px 0;
        }

        .merchant-info-panel p {
            font-size: 14px;
            color: var(--gray-500);
            margin: 0;
        }

        .stats {
            display: flex;
            gap: 24px;
            font-size: 12px;
            color: var(--gray-500);
        }

        .bulk-bar {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            padding: 12px 24px;
            display: none;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }

        .bulk-bar.show { display: flex; }

        .bulk-info {
            font-size: 14px;
            color: #1e40af;
            font-weight: 500;
        }

        .bulk-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .permissions-content {
            padding: 24px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: var(--gray-200);
        }

        .category {
            margin-bottom: 24px;
        }

        .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
            padding: 12px 16px;
            background: var(--gray-50);
            border-radius: 6px;
            border: 1px solid var(--gray-200);
        }

        .category-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            font-weight: 600;
        }

        .category-icon {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .category-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--gray-500);
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 12px;
        }

        .permission-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: 6px;
            padding: 16px;
            transition: all 0.2s;
            position: relative;
        }

        .permission-card:hover {
            border-color: var(--primary);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .permission-card.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .permission-name {
            font-weight: 500;
        }

        .permission-description {
            font-size: 12px;
            color: var(--gray-500);
            margin-bottom: 8px;
        }

        .tags {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }

        .tag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
        }

        .tag.required { background: #fef3c7; color: #92400e; }
        .tag.approval { background: #fef2f2; color: #dc2626; }
        .tag.dependency { background: #e0e7ff; color: #3730a3; }

        .approval-status {
            position: absolute;
            top: -6px;
            right: -6px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
        }

        .approval-status.pending { background: var(--warning); color: white; }
        .approval-status.approved { background: var(--success); color: white; }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .modal.show { display: flex; }

        .modal-content {
            background: white;
            border-radius: 8px;
            max-width: 500px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 15px rgba(0,0,0,0.2);
        }

        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--gray-500);
            cursor: pointer;
            padding: 4px;
        }

        .modal-body {
            padding: 20px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1100;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            min-width: 300px;
            transform: translateX(400px);
            transition: transform 0.3s;
        }

        .notification.show { transform: translateX(0); }
        .notification.success { background: var(--success); }
        .notification.warning { background: var(--warning); }
        .notification.error { background: var(--danger); }
        .notification.info { background: var(--primary); }

        .changes-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--warning);
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 14px;
            display: none;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .changes-indicator.show { display: flex; }

        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
            }
            .sidebar {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }
            .header-content {
                flex-direction: column;
                align-items: stretch;
            }
            .permissions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div>
                    <h1 class="page-title">商户权限管理</h1>
                    <p style="color: var(--gray-500); margin-top: 4px;">统一管理商户权限，支持批量操作</p>
                </div>
                <div class="actions">
                    <button class="btn btn-secondary" onclick="exportPermissions()">
                        <i class="fas fa-download"></i> 导出
                    </button>
                    <button class="btn btn-secondary" onclick="showTemplateModal()">
                        <i class="fas fa-copy"></i> 模板
                    </button>
                    <button class="btn btn-warning" onclick="showApprovalModal()">
                        <i class="fas fa-clipboard-check"></i> 审批
                    </button>
                    <button class="btn btn-primary" onclick="savePermissions()">
                        <i class="fas fa-save"></i> 保存
                    </button>
                </div>
            </div>
        </div>

        <!-- 主布局 -->
        <div class="main-layout">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <h2 class="sidebar-title">商户列表</h2>
                    <span class="count-badge" id="merchantCount">0</span>
                </div>

                <div class="search-section">
                    <input type="text" class="search-input" placeholder="搜索商户..." 
                           id="merchantSearch" oninput="searchMerchants(this.value)">
                    
                    <div class="filters">
                        <div class="filter active" data-filter="all">全部</div>
                        <div class="filter" data-filter="active">正常</div>
                        <div class="filter" data-filter="pending">待审</div>
                        <div class="filter" data-filter="premium">高级</div>
                    </div>
                </div>

                <div class="merchants-list" id="merchantsList"></div>
            </div>

            <!-- 内容区 -->
            <div class="content-panel">
                <div class="content-header">
                    <div class="merchant-display" id="selectedMerchantInfo" style="display: none;">
                        <div class="avatar" id="merchantAvatar">商</div>
                        <div class="merchant-info-panel">
                            <h3 id="merchantName">未选择商户</h3>
                            <p id="merchantDesc">请选择商户</p>
                        </div>
                    </div>

                    <div class="stats" id="merchantStats" style="display: none;">
                        <span>总数: <strong id="totalPermissions">0</strong></span>
                        <span>启用: <strong id="enabledPermissions">0</strong></span>
                        <span>待审: <strong id="pendingPermissions">0</strong></span>
                    </div>
                </div>

                <div class="bulk-bar" id="bulkActions">
                    <div class="bulk-info">
                        已选择 <span id="selectedCount">0</span> 个商户
                    </div>
                    <div class="bulk-actions">
                        <button class="btn btn-sm btn-success" onclick="bulkEnablePermissions()">
                            <i class="fas fa-check"></i> 启用
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="bulkDisablePermissions()">
                            <i class="fas fa-times"></i> 禁用
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="bulkApplyTemplate()">
                            <i class="fas fa-copy"></i> 应用模板
                        </button>
                    </div>
                </div>

                <div class="permissions-content" id="permissionsMatrix">
                    <div class="empty-state">
                        <i class="fas fa-users-cog"></i>
                        <h3>请选择商户</h3>
                        <p>从左侧列表选择商户开始管理权限</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="changes-indicator" id="changesIndicator">
            <i class="fas fa-exclamation-triangle"></i>
            <span>有未保存的更改</span>
            <button class="btn btn-sm btn-primary" onclick="savePermissions()">保存</button>
        </div>
    </div>

    <div class="modal" id="modalOverlay">
        <div class="modal-content" id="modalContent"></div>
    </div>

    <script src="permission-manager.js"></script>
    <script>
        const manager = new MerchantPermissionManager();
        
        function savePermissions() { manager.savePermissions(); }
        function exportPermissions() { manager.exportPermissions(); }
        function showTemplateModal() { manager.showTemplateModal(); }
        function showApprovalModal() { manager.showApprovalModal(); }
        function bulkEnablePermissions() { manager.bulkEnablePermissions(); }
        function bulkDisablePermissions() { manager.bulkDisablePermissions(); }
        function bulkApplyTemplate() { manager.bulkApplyTemplate(); }
        function searchMerchants(keyword) { manager.searchMerchants(keyword); }

        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter')) {
                document.querySelectorAll('.filter').forEach(f => f.classList.remove('active'));
                e.target.classList.add('active');
                manager.filterMerchants(e.target.dataset.filter);
            }
        });
    </script>
</body>
</html> 