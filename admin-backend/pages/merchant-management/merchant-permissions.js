        // 初始化权限管理系统
        const merchantPermissionManager = new MerchantPermissionManager();
        
        // 全局函数
        function savePermissions() {
            merchantPermissionManager.savePermissions();
        }
        
        function exportPermissions() {
            merchantPermissionManager.exportPermissions();
        }
        
        function showTemplateModal() {
            merchantPermissionManager.showTemplateModal();
        }
        
        function showApprovalModal() {
            merchantPermissionManager.showApprovalModal();
        }
        
        function bulkEnablePermissions() {
            merchantPermissionManager.bulkEnablePermissions();
        }
        
        function bulkDisablePermissions() {
            merchantPermissionManager.bulkDisablePermissions();
        }
        
        function bulkApplyTemplate() {
            merchantPermissionManager.bulkApplyTemplate();
        }
        
        function searchMerchants(keyword) {
            merchantPermissionManager.searchMerchants(keyword);
        }

        function clearSelection() {
            // 清除所有选择的商户
            document.querySelectorAll('.merchant-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            
            // 隐藏批量操作栏
            const bulkActions = document.getElementById('bulkActions');
            if (bulkActions) {
                bulkActions.style.display = 'none';
            }
            
            // 显示通知
            showNotification('已清除所有选择', 'info');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="notification-icon fas ${getNotificationIcon(type)}"></i>
                    <div class="notification-message">${message}</div>
                    <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            const container = document.getElementById('notificationContainer');
            if (container) {
                container.appendChild(notification);
                setTimeout(() => notification.classList.add('show'), 100);
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }
        }

        function getNotificationIcon(type) {
            const icons = {
                'success': 'fa-check-circle',
                'warning': 'fa-exclamation-triangle',
                'error': 'fa-times-circle',
                'info': 'fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        function toggleSection(sectionId) {
            const toggle = event.target;
            const isActive = toggle.classList.contains('active');
            
            // 切换开关状态
            toggle.classList.toggle('active');
            
            // 获取该分组下的所有权限复选框
            const section = toggle.closest('.permission-section');
            const checkboxes = section.querySelectorAll('.permission-checkbox:not([disabled])');
            
            // 批量设置权限状态
            checkboxes.forEach(checkbox => {
                checkbox.checked = !isActive;
            });
            
            // 显示通知
            const message = !isActive ? '已启用该分组的所有权限' : '已禁用该分组的所有权限';
            showNotification(message, 'success');
        }

        function simulateSelectMerchant() {
            // 模拟选择商户，显示权限设置
            const emptyState = document.getElementById('emptyState');
            const permissionSections = document.getElementById('permissionSections');
            const orderPermissions = document.getElementById('orderPermissions');
            const customerPermissions = document.getElementById('customerPermissions');
            
            if (emptyState && permissionSections) {
                emptyState.style.display = 'none';
                permissionSections.style.display = 'block';
                if (orderPermissions) orderPermissions.style.display = 'block';
                if (customerPermissions) customerPermissions.style.display = 'block';
                
                // 更新商户信息显示
                const merchantInfo = document.getElementById('selectedMerchantInfo');
                const merchantAvatar = document.getElementById('merchantAvatar');
                const merchantName = document.getElementById('merchantName');
                const merchantDesc = document.getElementById('merchantDesc');
                const merchantStats = document.getElementById('merchantStats');
                
                if (merchantInfo) {
                    merchantInfo.style.display = 'flex';
                    merchantAvatar.textContent = '星';
                    merchantName.textContent = '星辰数码专营店';
                    merchantDesc.textContent = '高级商户 | 正常营业';
                    merchantStats.style.display = 'block';
                    
                    document.getElementById('totalPermissions').textContent = '18';
                    document.getElementById('enabledPermissions').textContent = '12';
                    document.getElementById('pendingPermissions').textContent = '2';
                }
            }
        }

        function previewTemplate(templateId) {
            const templates = {
                'basic': {
                    name: '基础商户模板',
                    permissions: [
                        '查看商品', '添加商品', '查看订单', '基础客服'
                    ]
                },
                'standard': {
                    name: '标准商户模板',
                    permissions: [
                        '查看商品', '添加商品', '编辑商品', '查看订单', 
                        '处理订单', '客户信息', '基础营销'
                    ]
                },
                'premium': {
                    name: '高级商户模板',
                    permissions: [
                        '全商品权限', '全订单权限', '客户管理', 
                        '高级营销', '数据分析', '财务管理'
                    ]
                },
                'enterprise': {
                    name: '企业定制模板',
                    permissions: [
                        '全部权限', '系统管理', 'API访问', 
                        '高级分析', '自定义功能', '优先支持'
                    ]
                }
            };

            const template = templates[templateId];
            if (!template) return;

            const modalContent = `
                <div class="modal-header">
                    <h3 class="modal-title">模板预览 - ${template.name}</h3>
                    <button class="modal-close" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="template-preview">
                        <h4>包含权限：</h4>
                        <div class="permission-preview-list">
                            ${template.permissions.map(permission => `
                                <div class="permission-preview-item">
                                    <i class="fas fa-check text-success"></i>
                                    <span>${permission}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="template-stats">
                            <p><strong>权限总数：</strong>${template.permissions.length} 项</p>
                            <p><strong>适用场景：</strong>${getTemplateDescription(templateId)}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">关闭</button>
                    <button class="btn btn-primary" onclick="applyTemplateToSelected('${templateId}'); closeModal();">
                        <i class="fas fa-check"></i>
                        应用此模板
                    </button>
                </div>
            `;

            showModal(modalContent);
        }

        function getTemplateDescription(templateId) {
            const descriptions = {
                'basic': '新入驻商户，需要基础功能即可',
                'standard': '运营稳定的中型商户，需要更多管理功能',
                'premium': '大型企业商户，需要高级功能和数据分析',
                'enterprise': '超大型企业，需要完整权限和定制功能'
            };
            return descriptions[templateId] || '通用模板';
        }

        function applyTemplateToSelected(templateId) {
            // 检查是否有选中的商户或正在查看的商户
            const selectedMerchant = document.getElementById('merchantName').textContent;
            if (selectedMerchant === '未选择商户') {
                showNotification('请先选择一个商户', 'warning');
                return;
            }

            // 模拟应用模板
            const templateNames = {
                'basic': '基础商户模板',
                'standard': '标准商户模板',
                'premium': '高级商户模板',
                'enterprise': '企业定制模板'
            };

            // 根据模板类型设置权限
            applyTemplatePermissions(templateId);
            
            showNotification(`已为"${selectedMerchant}"应用"${templateNames[templateId]}"`, 'success');
        }

        function applyTemplatePermissions(templateId) {
            // 先清除所有权限
            document.querySelectorAll('.permission-checkbox:not([disabled])').forEach(checkbox => {
                checkbox.checked = false;
            });

            // 根据模板设置权限
            switch (templateId) {
                case 'basic':
                    // 基础权限
                    setPermissionsBySelector([
                        'input[type="checkbox"][value*="view_products"]',
                        'input[type="checkbox"][value*="add_products"]',
                        'input[type="checkbox"][value*="view_orders"]'
                    ]);
                    break;
                case 'standard':
                    // 标准权限
                    setPermissionsBySelector([
                        'input[type="checkbox"][value*="view_products"]',
                        'input[type="checkbox"][value*="add_products"]',
                        'input[type="checkbox"][value*="edit_products"]',
                        'input[type="checkbox"][value*="view_orders"]',
                        'input[type="checkbox"][value*="process_orders"]'
                    ]);
                    break;
                case 'premium':
                    // 高级权限 (大部分权限)
                    document.querySelectorAll('.permission-checkbox:not([disabled])').forEach(checkbox => {
                        checkbox.checked = Math.random() > 0.2; // 80%的权限启用
                    });
                    break;
                case 'enterprise':
                    // 企业权限 (全部权限)
                    document.querySelectorAll('.permission-checkbox:not([disabled])').forEach(checkbox => {
                        checkbox.checked = true;
                    });
                    break;
            }

            // 更新权限统计
            updatePermissionStats();
        }

        function setPermissionsBySelector(selectors) {
            selectors.forEach(selector => {
                const checkbox = document.querySelector(selector);
                if (checkbox) checkbox.checked = true;
            });
        }

        function updatePermissionStats() {
            const total = document.querySelectorAll('.permission-checkbox').length;
            const enabled = document.querySelectorAll('.permission-checkbox:checked').length;
            const pending = document.querySelectorAll('.approval-badge.pending').length;

            document.getElementById('totalPermissions').textContent = total;
            document.getElementById('enabledPermissions').textContent = enabled;
            document.getElementById('pendingPermissions').textContent = pending;
        }

        function showModal(content) {
            const modal = document.getElementById('modalOverlay');
            const modalContent = document.getElementById('modalContent');
            if (modal && modalContent) {
                modalContent.innerHTML = content;
                modal.classList.add('show');
            }
        }

        function closeModal() {
            const modal = document.getElementById('modalOverlay');
            if (modal) {
                modal.classList.remove('show');
            }
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 显示页面加载成功通知
            setTimeout(() => {
                showNotification('商户权限管理系统已就绪', 'success');
            }, 500);
            
            // 筛选标签点击事件
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('filter-tab')) {
                    document.querySelectorAll('.filter-tab').forEach(tab => {
                        tab.classList.remove('active');
                    });
                    e.target.classList.add('active');
                    
                    const filter = e.target.dataset.filter;
                    if (merchantPermissionManager && merchantPermissionManager.filterMerchants) {
                        merchantPermissionManager.filterMerchants(filter);
                    }
                }
            });

            // 选项卡切换事件
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('tab-button')) {
                    // 移除所有活跃状态
                    document.querySelectorAll('.tab-button').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    // 添加当前活跃状态
                    e.target.classList.add('active');
                    const tabId = e.target.dataset.tab + 'Tab';
                    document.getElementById(tabId).classList.add('active');
                }
            });

            // 页面卸载前提示保存
            window.addEventListener('beforeunload', function(e) {
                if (merchantPermissionManager && merchantPermissionManager.hasChanges) {
                    e.preventDefault();
                    e.returnValue = '您有未保存的更改，确定要离开吗？';
                }
            });

            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + S 保存
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    savePermissions();
                }
                
                // Escape 关闭模态框
                if (e.key === 'Escape') {
                    const modal = document.getElementById('modalOverlay');
                    if (modal && modal.classList.contains('show')) {
                        modal.classList.remove('show');
                    }
                }
            });

            // 添加保存提示
            const saveButtons = document.querySelectorAll('[onclick*="savePermissions"]');
            saveButtons.forEach(button => {
                button.title = '保存权限设置 (Ctrl+S)';
            });
        });
