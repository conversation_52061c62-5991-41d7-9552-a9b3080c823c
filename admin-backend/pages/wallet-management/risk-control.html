<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风控管理 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="wallet-management-fixes.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .risk-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .risk-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .risk-card h3 {
            color: #666;
            font-size: 14px;
            margin: 0 0 10px 0;
        }
        
        .risk-card .value {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .risk-card .high-risk { color: #dc3545; }
        .risk-card .medium-risk { color: #ffc107; }
        .risk-card .low-risk { color: #28a745; }
        .risk-card .normal { color: #333; }
        
        .risk-tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .tab-header {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            overflow-x: auto;
        }
        
        .tab-button {
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            white-space: nowrap;
        }
        
        .tab-button.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: #f8f9fa;
        }
        
        .tab-content {
            padding: 20px;
        }
        
        .alert-list {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .alert-item {
            padding: 15px;
            border-left: 4px solid #dc3545;
            background: #fff5f5;
            margin-bottom: 10px;
            border-radius: 0 4px 4px 0;
        }
        
        .alert-item.medium {
            border-left-color: #ffc107;
            background: #fffbf0;
        }
        
        .alert-item.low {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        
        .alert-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .alert-title {
            font-weight: bold;
            color: #333;
        }
        
        .alert-time {
            color: #666;
            font-size: 12px;
        }
        
        .alert-desc {
            color: #666;
            font-size: 14px;
        }
        
        .risk-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .risk-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
        }
        
        .risk-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .risk-level {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .risk-high { background: #f8d7da; color: #721c24; }
        .risk-medium { background: #fff3cd; color: #856404; }
        .risk-low { background: #d4edda; color: #155724; }
        .risk-normal { background: #e2e3e5; color: #383d41; }
        
        .rule-config {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .rule-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        
        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .rule-switch {
            width: 40px;
            height: 20px;
            border-radius: 10px;
            background: #ccc;
            position: relative;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .rule-switch.active {
            background: #28a745;
        }
        
        .rule-switch::after {
            content: '';
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: white;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s;
        }
        
        .rule-switch.active::after {
            left: 22px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .kyc-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .kyc-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .kyc-pending { background: #fff3cd; color: #856404; }
        .kyc-approved { background: #d4edda; color: #155724; }
        .kyc-rejected { background: #f8d7da; color: #721c24; }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        
        @media (max-width: 768px) {
            .rule-config {
                grid-template-columns: 1fr;
            }
            
            .risk-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> 风控管理</h2>
            </div>
            <nav class="sidebar-nav">
                <div class="menu-section">
                    <h3>核心功能</h3>
                    <ul class="menu-list">
                        <li><a href="../dashboard/index.html"><i class="fas fa-tachometer-alt"></i> 仪表板总览</a></li>
                        <li><a href="../user-management/index.html"><i class="fas fa-users"></i> 用户管理</a></li>
                        <li><a href="../product-management/index.html"><i class="fas fa-box"></i> 商品管理</a></li>
                        <li><a href="../order-management/index.html"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>业务管理</h3>
                    <ul class="menu-list">
                        <li><a href="../content-management/chat-management.html"><i class="fas fa-comments"></i> 内容管理</a></li>
                        <li><a href="../merchant-management/index.html"><i class="fas fa-store"></i> 商户管理</a></li>
                        <li><a href="../financial-management/index.html"><i class="fas fa-chart-line"></i> 财务管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>钱包管理</h3>
                    <ul class="menu-list">
                        <li><a href="user-wallet-management.html"><i class="fas fa-wallet"></i> 用户钱包管理</a></li>
                        <li><a href="transaction-management.html"><i class="fas fa-exchange-alt"></i> 交易管理</a></li>
                        <li><a href="deposit-withdrawal.html"><i class="fas fa-money-bill-wave"></i> 充值提现管理</a></li>
                        <li class="active"><a href="risk-control.html"><i class="fas fa-shield-alt"></i> 风控管理</a></li>
                        <li><a href="digital-currency.html"><i class="fas fa-coins"></i> 数字货币管理</a></li>
                        <li><a href="financial-reconciliation.html"><i class="fas fa-calculator"></i> 财务对账</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>权限管理</h3>
                    <ul class="menu-list">
                        <li><a href="../permission-management/admin-permissions.html"><i class="fas fa-user-shield"></i> 管理员权限</a></li>
                        <li><a href="../permission-management/api-permissions.html"><i class="fas fa-key"></i> API权限管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>系统设置</h3>
                    <ul class="menu-list">
                        <li><a href="../system-settings/index.html"><i class="fas fa-cog"></i> 系统配置</a></li>
                        <li><a href="../reports-analytics/index.html"><i class="fas fa-chart-bar"></i> 数据分析</a></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="main-header">
                <h1>风控管理</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="exportRiskReport()">导出风控报告</button>
                    <button class="btn btn-success" onclick="addRiskRule()">新增风控规则</button>
                </div>
            </div>

            <!-- 风控概览 -->
            <div class="risk-overview">
                <div class="risk-card">
                    <h3>风险警报</h3>
                    <p class="value high-risk">156</p>
                </div>
                <div class="risk-card">
                    <h3>可疑交易</h3>
                    <p class="value medium-risk">89</p>
                </div>
                <div class="risk-card">
                    <h3>高风险用户</h3>
                    <p class="value high-risk">23</p>
                </div>
                <div class="risk-card">
                    <h3>待审核KYC</h3>
                    <p class="value medium-risk">67</p>
                </div>
                <div class="risk-card">
                    <h3>风控规则</h3>
                    <p class="value normal">12</p>
                </div>
                <div class="risk-card">
                    <h3>拦截率</h3>
                    <p class="value low-risk">99.2%</p>
                </div>
            </div>

            <!-- 实时警报 -->
            <div class="alert-list">
                <h4>实时风险警报</h4>
                <div class="alert-item">
                    <div class="alert-header">
                        <div class="alert-title">⚠️ 大额转账异常</div>
                        <div class="alert-time">2分钟前</div>
                    </div>
                    <div class="alert-desc">用户ID 1023 在短时间内进行多笔大额转账，总金额超过50万元</div>
                </div>
                <div class="alert-item medium">
                    <div class="alert-header">
                        <div class="alert-title">⚡ 频繁小额交易</div>
                        <div class="alert-time">5分钟前</div>
                    </div>
                    <div class="alert-desc">检测到用户ID 1045 进行频繁小额交易，疑似洗钱行为</div>
                </div>
                <div class="alert-item low">
                    <div class="alert-header">
                        <div class="alert-title">ℹ️ 新用户大额充值</div>
                        <div class="alert-time">8分钟前</div>
                    </div>
                    <div class="alert-desc">新注册用户ID 1067 首次充值金额超过1万元</div>
                </div>
            </div>

            <!-- 风控标签 -->
            <div class="risk-tabs">
                <div class="tab-header">
                    <button class="tab-button active" onclick="switchTab('suspicious')">可疑交易</button>
                    <button class="tab-button" onclick="switchTab('users')">风险用户</button>
                    <button class="tab-button" onclick="switchTab('kyc')">KYC管理</button>
                    <button class="tab-button" onclick="switchTab('rules')">风控规则</button>
                    <button class="tab-button" onclick="switchTab('reports')">风控报告</button>
                </div>

                <!-- 可疑交易 -->
                <div id="suspicious-tab" class="tab-content">
                    <div class="table-container">
                        <table class="risk-table">
                            <thead>
                                <tr>
                                    <th width="15%">交易ID</th>
                                    <th width="12%">用户信息</th>
                                    <th width="10%">交易金额</th>
                                    <th width="12%">风险类型</th>
                                    <th width="10%">风险等级</th>
                                    <th width="15%">触发时间</th>
                                    <th width="15%">风险描述</th>
                                    <th width="11%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div><strong>TXN001234567</strong></div>
                                    </td>
                                    <td>
                                        <div><strong>张三</strong></div>
                                        <div style="font-size: 12px; color: #666;">ID: 1023</div>
                                    </td>
                                    <td style="color: #dc3545; font-weight: bold;">¥50,000</td>
                                    <td>大额交易</td>
                                    <td><span class="risk-level risk-high">高风险</span></td>
                                    <td>2024-01-15 14:30</td>
                                    <td>短时间内多笔大额转账</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="reviewTransaction(1)">审核</button>
                                            <button class="btn-xs btn-danger" onclick="blockTransaction(1)">拦截</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div><strong>TXN001234568</strong></div>
                                    </td>
                                    <td>
                                        <div><strong>李四</strong></div>
                                        <div style="font-size: 12px; color: #666;">ID: 1045</div>
                                    </td>
                                    <td style="color: #ffc107; font-weight: bold;">¥299.99</td>
                                    <td>频繁交易</td>
                                    <td><span class="risk-level risk-medium">中风险</span></td>
                                    <td>2024-01-15 14:25</td>
                                    <td>1小时内进行20笔交易</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="reviewTransaction(2)">审核</button>
                                            <button class="btn-xs btn-warning" onclick="limitUser(2)">限制</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 风险用户 -->
                <div id="users-tab" class="tab-content" style="display: none;">
                    <div class="table-container">
                        <table class="risk-table">
                            <thead>
                                <tr>
                                    <th width="20%">用户信息</th>
                                    <th width="12%">风险等级</th>
                                    <th width="15%">风险评分</th>
                                    <th width="15%">风险类型</th>
                                    <th width="15%">最后活动</th>
                                    <th width="23%">风险描述</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div><strong>王五</strong></div>
                                        <div style="font-size: 12px; color: #666;">ID: 1089</div>
                                        <div style="font-size: 12px; color: #666;">139****5678</div>
                                    </td>
                                    <td><span class="risk-level risk-high">高风险</span></td>
                                    <td><strong style="color: #dc3545;">85分</strong></td>
                                    <td>洗钱嫌疑</td>
                                    <td>2024-01-15 13:45</td>
                                    <td>多次大额分散交易，疑似洗钱</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div><strong>赵六</strong></div>
                                        <div style="font-size: 12px; color: #666;">ID: 1156</div>
                                        <div style="font-size: 12px; color: #666;">150****9876</div>
                                    </td>
                                    <td><span class="risk-level risk-medium">中风险</span></td>
                                    <td><strong style="color: #ffc107;">65分</strong></td>
                                    <td>异常行为</td>
                                    <td>2024-01-15 12:30</td>
                                    <td>登录IP频繁变动，异常活跃</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- KYC管理 -->
                <div id="kyc-tab" class="tab-content" style="display: none;">
                    <div class="table-container">
                        <table class="risk-table">
                            <thead>
                                <tr>
                                    <th width="20%">用户信息</th>
                                    <th width="15%">认证类型</th>
                                    <th width="12%">认证状态</th>
                                    <th width="15%">提交时间</th>
                                    <th width="15%">审核时间</th>
                                    <th width="23%">备注/拒绝原因</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div><strong>钱七</strong></div>
                                        <div style="font-size: 12px; color: #666;">ID: 1234</div>
                                        <div style="font-size: 12px; color: #666;">188****1234</div>
                                    </td>
                                    <td>身份证认证</td>
                                    <td><span class="kyc-badge kyc-pending">待审核</span></td>
                                    <td>2024-01-15 10:20</td>
                                    <td>-</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="reviewKYC(1)">审核</button>
                                            <button class="btn-xs btn-success" onclick="approveKYC(1)">通过</button>
                                            <button class="btn-xs btn-danger" onclick="rejectKYC(1)">拒绝</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div><strong>孙八</strong></div>
                                        <div style="font-size: 12px; color: #666;">ID: 1235</div>
                                        <div style="font-size: 12px; color: #666;">177****5678</div>
                                    </td>
                                    <td>银行卡认证</td>
                                    <td><span class="kyc-badge kyc-approved">已通过</span></td>
                                    <td>2024-01-14 16:45</td>
                                    <td>2024-01-14 17:20</td>
                                    <td>认证信息完整，审核通过</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 风控规则 -->
                <div id="rules-tab" class="tab-content" style="display: none;">
                    <div class="rule-config">
                        <!-- 大额交易规则 -->
                        <div class="rule-card">
                            <div class="rule-header">
                                <h4>大额交易监控</h4>
                                <div class="rule-switch active" onclick="toggleRule(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>单笔金额阈值 (元)</label>
                                <input type="number" value="10000" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>日累计阈值 (元)</label>
                                <input type="number" value="50000" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>处理方式</label>
                                <select class="form-control">
                                    <option>自动拦截</option>
                                    <option>人工审核</option>
                                    <option>仅预警</option>
                                </select>
                            </div>
                            <button class="btn btn-primary btn-sm">保存规则</button>
                        </div>

                        <!-- 频繁交易规则 -->
                        <div class="rule-card">
                            <div class="rule-header">
                                <h4>频繁交易监控</h4>
                                <div class="rule-switch active" onclick="toggleRule(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>时间窗口 (分钟)</label>
                                <input type="number" value="60" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>交易次数阈值</label>
                                <input type="number" value="20" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>处理方式</label>
                                <select class="form-control">
                                    <option>限制交易</option>
                                    <option>人工审核</option>
                                    <option>仅预警</option>
                                </select>
                            </div>
                            <button class="btn btn-primary btn-sm">保存规则</button>
                        </div>

                        <!-- 异常登录规则 -->
                        <div class="rule-card">
                            <div class="rule-header">
                                <h4>异常登录监控</h4>
                                <div class="rule-switch" onclick="toggleRule(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>IP变动次数 (日)</label>
                                <input type="number" value="5" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>地域跨度检测</label>
                                <select class="form-control">
                                    <option>启用</option>
                                    <option>禁用</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>处理方式</label>
                                <select class="form-control">
                                    <option>要求二次验证</option>
                                    <option>临时锁定</option>
                                    <option>仅预警</option>
                                </select>
                            </div>
                            <button class="btn btn-primary btn-sm">保存规则</button>
                        </div>

                        <!-- 新用户监控规则 -->
                        <div class="rule-card">
                            <div class="rule-header">
                                <h4>新用户行为监控</h4>
                                <div class="rule-switch active" onclick="toggleRule(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>新用户期限 (天)</label>
                                <input type="number" value="7" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>首次充值阈值 (元)</label>
                                <input type="number" value="5000" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>处理方式</label>
                                <select class="form-control">
                                    <option>人工审核</option>
                                    <option>延时到账</option>
                                    <option>仅预警</option>
                                </select>
                            </div>
                            <button class="btn btn-primary btn-sm">保存规则</button>
                        </div>
                    </div>
                </div>

                <!-- 风控报告 -->
                <div id="reports-tab" class="tab-content" style="display: none;">
                    <div class="chart-container">
                        <h4>风控拦截统计</h4>
                        <canvas id="riskChart"></canvas>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 菜单交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化菜单状态
            const menuItems = document.querySelectorAll('.admin-sidebar .menu-list li');
            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && link.getAttribute('href') === '#') {
                    // 如果是父菜单项，添加展开/收起功能
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const submenu = item.querySelector('.submenu');
                        if (submenu) {
                            item.classList.toggle('open');
                            submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
                        }
                    });
                }
            });
        });

        // 切换标签
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            document.getElementById(tabName + '-tab').style.display = 'block';
            event.target.classList.add('active');
            
            if (tabName === 'reports') {
                initRiskChart();
            }
        }

        // 切换规则开关
        function toggleRule(element) {
            element.classList.toggle('active');
        }

        // 审核交易
        function reviewTransaction(id) {
            alert('进入交易审核页面 ID: ' + id);
        }

        // 拦截交易
        function blockTransaction(id) {
            if (confirm('确定要拦截这笔交易吗？')) {
                alert('交易已拦截');
                location.reload();
            }
        }

        // 限制用户
        function limitUser(id) {
            if (confirm('确定要限制该用户的交易权限吗？')) {
                alert('用户交易权限已限制');
                location.reload();
            }
        }

        // KYC审核
        function reviewKYC(id) {
            alert('进入KYC审核页面 ID: ' + id);
        }

        function approveKYC(id) {
            if (confirm('确定要通过该用户的KYC认证吗？')) {
                alert('KYC认证已通过');
                location.reload();
            }
        }

        function rejectKYC(id) {
            const reason = prompt('请输入拒绝原因：');
            if (reason) {
                alert('KYC认证已拒绝');
                location.reload();
            }
        }

        // 导出风控报告
        function exportRiskReport() {
            alert('正在导出风控报告...');
        }

        // 新增风控规则
        function addRiskRule() {
            alert('新增风控规则');
        }

        // 初始化风控图表
        function initRiskChart() {
            const ctx = document.getElementById('riskChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['大额交易', '频繁交易', '异常登录', '新用户异常', '洗钱嫌疑'],
                    datasets: [{
                        label: '拦截次数',
                        data: [45, 23, 12, 8, 3],
                        backgroundColor: [
                            '#dc3545',
                            '#ffc107', 
                            '#fd7e14',
                            '#20c997',
                            '#6f42c1'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 