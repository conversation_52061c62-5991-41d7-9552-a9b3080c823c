<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值提现管理 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="wallet-management-fixes.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #666;
            font-size: 14px;
            margin: 0 0 10px 0;
        }
        
        .stat-card .value {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .management-tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .tab-header {
            display: flex;
            border-bottom: 1px solid #e9ecef;
        }
        
        .tab-button {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: #f8f9fa;
        }
        
        .tab-content {
            padding: 20px;
        }
        
        .deposit-table, .withdrawal-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .deposit-table th, .withdrawal-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
        }
        
        .deposit-table td, .withdrawal-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .amount {
            font-weight: bold;
            color: #28a745;
        }
        
        .amount.withdrawal {
            color: #dc3545;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-success { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-processing { background: #d1ecf1; color: #0c5460; }
        
        .channel-config {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .channel-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        
        .channel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .channel-status {
            width: 40px;
            height: 20px;
            border-radius: 10px;
            background: #ccc;
            position: relative;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .channel-status.active {
            background: #28a745;
        }
        
        .channel-status::after {
            content: '';
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: white;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s;
        }
        
        .channel-status.active::after {
            left: 22px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        
        .filters-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .batch-actions {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .report-charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .chart-container h4 {
            margin-bottom: 15px;
            color: #333;
        }
        
        @media (max-width: 768px) {
            .report-charts {
                grid-template-columns: 1fr;
            }
            
            .channel-config {
                grid-template-columns: 1fr;
            }
            
            .filters-section {
                flex-direction: column;
                align-items: stretch;
            }
        }
        
        /* 增强筛选区域样式 */
        .enhanced-filters {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .filter-group label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .input-with-icon {
            position: relative;
        }
        
        .input-with-icon i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 14px;
        }
        
        .input-with-icon input {
            padding-left: 40px;
        }
        
        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .date-range span {
            color: #6b7280;
            font-size: 14px;
            white-space: nowrap;
        }
        
        .filter-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            padding-top: 16px;
            border-top: 1px solid #e5e7eb;
        }
        
        /* 增强批量操作样式 */
        .enhanced-batch-actions {
            background: white;
            padding: 16px 24px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .select-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .select-info input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .select-info label {
            font-weight: 500;
            color: #374151;
            cursor: pointer;
            margin: 0;
        }
        
        .selected-count {
            color: #6b7280;
            font-size: 14px;
        }
        
        .selected-count strong {
            color: #3b82f6;
        }
        
        .batch-buttons {
            display: flex;
            gap: 8px;
        }
        
        .batch-buttons button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 增强表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }
        
        .deposit-table th,
        .withdrawal-table th {
            background: #f8fafc;
            padding: 16px;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            font-size: 14px;
        }
        
        .deposit-table td,
        .withdrawal-table td {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 14px;
            color: #374151;
        }
        
        .deposit-table tbody tr:hover,
        .withdrawal-table tbody tr:hover {
            background: #f8fafc;
        }
        
        /* 状态徽章增强 */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }
        
        .status-pending { 
            background: #fef3c7; 
            color: #92400e; 
            border: 1px solid #f59e0b;
        }
        .status-success { 
            background: #d1fae5; 
            color: #065f46; 
            border: 1px solid #10b981;
        }
        .status-failed { 
            background: #fee2e2; 
            color: #991b1b; 
            border: 1px solid #ef4444;
        }
        .status-processing { 
            background: #dbeafe; 
            color: #1e40af; 
            border: 1px solid #3b82f6;
        }
        
        /* 操作按钮增强 */
        .action-buttons {
            display: flex;
            gap: 6px;
            align-items: center;
        }
        
        .btn-xs {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: 1px solid;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-xs:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* 响应式优化 */
        @media (max-width: 768px) {
            .filter-row {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .enhanced-batch-actions {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }
            
            .filter-actions,
            .batch-buttons {
                justify-content: center;
            }
            
            .date-range {
                flex-direction: column;
                align-items: stretch;
            }
            
            .date-range span {
                text-align: center;
                padding: 4px 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-money-bill-wave"></i> 充值提现管理</h2>
            </div>
            <nav class="sidebar-nav">
                <div class="menu-section">
                    <h3>核心功能</h3>
                    <ul class="menu-list">
                        <li><a href="../dashboard/index.html"><i class="fas fa-tachometer-alt"></i> 仪表板总览</a></li>
                        <li><a href="../user-management/index.html"><i class="fas fa-users"></i> 用户管理</a></li>
                        <li><a href="../product-management/index.html"><i class="fas fa-box"></i> 商品管理</a></li>
                        <li><a href="../order-management/index.html"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>业务管理</h3>
                    <ul class="menu-list">
                        <li><a href="../content-management/chat-management.html"><i class="fas fa-comments"></i> 内容管理</a></li>
                        <li><a href="../merchant-management/index.html"><i class="fas fa-store"></i> 商户管理</a></li>
                        <li><a href="../financial-management/index.html"><i class="fas fa-chart-line"></i> 财务管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>钱包管理</h3>
                    <ul class="menu-list">
                        <li><a href="user-wallet-management.html"><i class="fas fa-wallet"></i> 用户钱包管理</a></li>
                        <li><a href="transaction-management.html"><i class="fas fa-exchange-alt"></i> 交易管理</a></li>
                        <li class="active"><a href="deposit-withdrawal.html"><i class="fas fa-money-bill-wave"></i> 充值提现管理</a></li>
                        <li><a href="risk-control.html"><i class="fas fa-shield-alt"></i> 风控管理</a></li>
                        <li><a href="digital-currency.html"><i class="fas fa-coins"></i> 数字货币管理</a></li>
                        <li><a href="financial-reconciliation.html"><i class="fas fa-calculator"></i> 财务对账</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>权限管理</h3>
                    <ul class="menu-list">
                        <li><a href="../permission-management/admin-permissions.html"><i class="fas fa-user-shield"></i> 管理员权限</a></li>
                        <li><a href="../permission-management/api-permissions.html"><i class="fas fa-key"></i> API权限管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>系统设置</h3>
                    <ul class="menu-list">
                        <li><a href="../system-settings/index.html"><i class="fas fa-cog"></i> 系统配置</a></li>
                        <li><a href="../reports-analytics/index.html"><i class="fas fa-chart-bar"></i> 数据分析</a></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="main-header">
                <h1>充值提现管理</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="exportReports()">导出报表</button>
                    <button class="btn btn-success" onclick="addChannel()">新增渠道</button>
                </div>
            </div>

            <!-- 统计概览 -->
            <div class="overview-stats">
                <div class="stat-card">
                    <h3>今日充值</h3>
                    <p class="value">¥256,789</p>
                </div>
                <div class="stat-card">
                    <h3>今日提现</h3>
                    <p class="value">¥189,456</p>
                </div>
                <div class="stat-card">
                    <h3>净充值</h3>
                    <p class="value">¥67,333</p>
                </div>
                <div class="stat-card">
                    <h3>待审核提现</h3>
                    <p class="value" style="color: #ffc107;">156</p>
                </div>
                <div class="stat-card">
                    <h3>手续费收入</h3>
                    <p class="value">¥8,945</p>
                </div>
                <div class="stat-card">
                    <h3>活跃渠道</h3>
                    <p class="value">8</p>
                </div>
            </div>

            <!-- 管理标签 -->
            <div class="management-tabs">
                <div class="tab-header">
                    <button class="tab-button active" onclick="switchTab('deposits')">充值记录</button>
                    <button class="tab-button" onclick="switchTab('withdrawals')">提现记录</button>
                    <button class="tab-button" onclick="switchTab('channels')">渠道管理</button>
                    <button class="tab-button" onclick="switchTab('reports')">统计报表</button>
                </div>

                <!-- 充值记录 -->
                <div id="deposits-tab" class="tab-content">
                    <!-- 高级筛选区域 -->
                    <div class="enhanced-filters">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label>用户搜索</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-search"></i>
                                    <input type="text" placeholder="用户ID/手机号/订单号" class="form-control">
                                </div>
                            </div>
                            <div class="filter-group">
                                <label>充值渠道</label>
                                <select class="form-control">
                                    <option value="">全部渠道</option>
                                    <option value="alipay">支付宝</option>
                                    <option value="wechat">微信支付</option>
                                    <option value="bank">银行卡</option>
                                    <option value="crypto">数字货币</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>订单状态</label>
                                <select class="form-control">
                                    <option value="">全部状态</option>
                                    <option value="pending">待确认</option>
                                    <option value="success">成功</option>
                                    <option value="failed">失败</option>
                                    <option value="processing">处理中</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>时间范围</label>
                                <div class="date-range">
                                    <input type="date" class="form-control" id="startDate">
                                    <span>至</span>
                                    <input type="date" class="form-control" id="endDate">
                                </div>
                            </div>
                        </div>
                        <div class="filter-actions">
                            <button class="btn btn-primary" onclick="searchDeposits()">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <button class="btn btn-secondary" onclick="resetFilters()">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                            <button class="btn btn-info" onclick="exportDeposits()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>

                    <!-- 增强批量操作区域 -->
                    <div class="enhanced-batch-actions">
                        <div class="select-info">
                            <input type="checkbox" id="selectAllDeposits" onchange="toggleSelectAll('deposits')">
                            <label for="selectAllDeposits">全选</label>
                            <span class="selected-count">已选择 <strong id="selectedDepositsCount">0</strong> 项</span>
                        </div>
                        <div class="batch-buttons">
                            <button class="btn btn-success btn-sm" onclick="batchConfirmDeposits()" disabled>
                                <i class="fas fa-check"></i> 批量确认
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="batchRejectDeposits()" disabled>
                                <i class="fas fa-times"></i> 批量拒绝
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="batchPendingDeposits()" disabled>
                                <i class="fas fa-clock"></i> 批量待审
                            </button>
                        </div>
                    </div>

                    <!-- 充值列表 -->
                    <div class="table-container">
                        <table class="deposit-table">
                            <thead>
                                <tr>
                                    <th width="5%"><input type="checkbox"></th>
                                    <th width="15%">订单信息</th>
                                    <th width="15%">用户信息</th>
                                    <th width="12%">充值金额</th>
                                    <th width="12%">实际到账</th>
                                    <th width="12%">充值渠道</th>
                                    <th width="10%">状态</th>
                                    <th width="12%">创建时间</th>
                                    <th width="7%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>
                                        <div><strong>DEP001234567</strong></div>
                                        <div style="font-size: 12px; color: #666;">充值订单</div>
                                    </td>
                                    <td>
                                        <div><strong>张三</strong></div>
                                        <div style="font-size: 12px; color: #666;">ID: 1001</div>
                                    </td>
                                    <td class="amount">¥1,000.00</td>
                                    <td class="amount">¥1,000.00</td>
                                    <td>支付宝</td>
                                    <td><span class="status-badge status-success">成功</span></td>
                                    <td>2024-01-15 14:30</td>
                                    <td>
                                        <button class="btn-xs btn-info" onclick="viewDepositDetail(1)">详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>
                                        <div><strong>DEP001234568</strong></div>
                                        <div style="font-size: 12px; color: #666;">充值订单</div>
                                    </td>
                                    <td>
                                        <div><strong>李四</strong></div>
                                        <div style="font-size: 12px; color: #666;">ID: 1002</div>
                                    </td>
                                    <td class="amount">¥5,000.00</td>
                                    <td class="amount">¥4,975.00</td>
                                    <td>银行卡</td>
                                    <td><span class="status-badge status-pending">待确认</span></td>
                                    <td>2024-01-15 14:25</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-success" onclick="confirmDeposit(2)">确认</button>
                                            <button class="btn-xs btn-danger" onclick="rejectDeposit(2)">拒绝</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 提现记录 -->
                <div id="withdrawals-tab" class="tab-content" style="display: none;">
                    <!-- 筛选区域 -->
                    <div class="filters-section">
                        <input type="text" placeholder="用户ID/提现单号" class="form-control" style="width: 150px;">
                        <select class="form-control" style="width: 120px;">
                            <option value="">提现方式</option>
                            <option value="bank">银行卡</option>
                            <option value="alipay">支付宝</option>
                            <option value="wechat">微信</option>
                        </select>
                        <select class="form-control" style="width: 100px;">
                            <option value="">状态</option>
                            <option value="pending">待审核</option>
                            <option value="processing">处理中</option>
                            <option value="success">成功</option>
                            <option value="failed">失败</option>
                        </select>
                        <input type="number" placeholder="最小金额" class="form-control" style="width: 120px;">
                        <button class="btn btn-primary">搜索</button>
                    </div>

                    <!-- 批量操作 -->
                    <div class="batch-actions">
                        <input type="checkbox" id="selectAllWithdrawals">
                        <label for="selectAllWithdrawals">全选</label>
                        <button class="btn btn-success btn-sm">批量审核通过</button>
                        <button class="btn btn-danger btn-sm">批量拒绝</button>
                        <button class="btn btn-warning btn-sm">批量处理</button>
                    </div>

                    <!-- 提现列表 -->
                    <div class="table-container">
                        <table class="withdrawal-table">
                            <thead>
                                <tr>
                                    <th width="5%"><input type="checkbox"></th>
                                    <th width="15%">提现信息</th>
                                    <th width="15%">用户信息</th>
                                    <th width="12%">提现金额</th>
                                    <th width="10%">手续费</th>
                                    <th width="12%">实际到账</th>
                                    <th width="12%">提现方式</th>
                                    <th width="10%">状态</th>
                                    <th width="9%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>
                                        <div><strong>WTD001234567</strong></div>
                                        <div style="font-size: 12px; color: #666;">2024-01-15 13:30</div>
                                    </td>
                                    <td>
                                        <div><strong>王五</strong></div>
                                        <div style="font-size: 12px; color: #666;">ID: 1003</div>
                                    </td>
                                    <td class="amount withdrawal">¥2,000.00</td>
                                    <td>¥20.00</td>
                                    <td class="amount withdrawal">¥1,980.00</td>
                                    <td>银行卡</td>
                                    <td><span class="status-badge status-pending">待审核</span></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-success" onclick="approveWithdrawal(1)">通过</button>
                                            <button class="btn-xs btn-danger" onclick="rejectWithdrawal(1)">拒绝</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>
                                        <div><strong>WTD001234568</strong></div>
                                        <div style="font-size: 12px; color: #666;">2024-01-15 12:15</div>
                                    </td>
                                    <td>
                                        <div><strong>赵六</strong></div>
                                        <div style="font-size: 12px; color: #666;">ID: 1004</div>
                                    </td>
                                    <td class="amount withdrawal">¥500.00</td>
                                    <td>¥5.00</td>
                                    <td class="amount withdrawal">¥495.00</td>
                                    <td>支付宝</td>
                                    <td><span class="status-badge status-processing">处理中</span></td>
                                    <td>
                                        <button class="btn-xs btn-info" onclick="viewWithdrawalDetail(2)">详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 渠道管理 -->
                <div id="channels-tab" class="tab-content" style="display: none;">
                    <div class="channel-config">
                        <!-- 支付宝渠道 -->
                        <div class="channel-card">
                            <div class="channel-header">
                                <h4>支付宝支付</h4>
                                <div class="channel-status active" onclick="toggleChannel(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>商户号</label>
                                <input type="text" value="2088xxxxxxxxxx" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>费率 (%)</label>
                                <input type="number" value="0.6" step="0.01" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>单笔限额 (元)</label>
                                <input type="number" value="50000" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>日限额 (元)</label>
                                <input type="number" value="200000" class="form-control">
                            </div>
                            <button class="btn btn-primary btn-sm">保存配置</button>
                        </div>

                        <!-- 微信支付渠道 -->
                        <div class="channel-card">
                            <div class="channel-header">
                                <h4>微信支付</h4>
                                <div class="channel-status active" onclick="toggleChannel(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>商户号</label>
                                <input type="text" value="1230000109" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>费率 (%)</label>
                                <input type="number" value="0.6" step="0.01" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>单笔限额 (元)</label>
                                <input type="number" value="50000" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>日限额 (元)</label>
                                <input type="number" value="200000" class="form-control">
                            </div>
                            <button class="btn btn-primary btn-sm">保存配置</button>
                        </div>

                        <!-- 银行卡渠道 -->
                        <div class="channel-card">
                            <div class="channel-header">
                                <h4>银行卡支付</h4>
                                <div class="channel-status" onclick="toggleChannel(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>接口商</label>
                                <select class="form-control">
                                    <option>连连支付</option>
                                    <option>易宝支付</option>
                                    <option>宝付</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>费率 (%)</label>
                                <input type="number" value="0.8" step="0.01" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>单笔限额 (元)</label>
                                <input type="number" value="100000" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>日限额 (元)</label>
                                <input type="number" value="500000" class="form-control">
                            </div>
                            <button class="btn btn-primary btn-sm">保存配置</button>
                        </div>
                    </div>
                </div>

                <!-- 统计报表 -->
                <div id="reports-tab" class="tab-content" style="display: none;">
                    <div class="report-charts">
                        <div class="chart-container">
                            <h4>充值提现趋势</h4>
                            <canvas id="depositWithdrawalChart"></canvas>
                        </div>
                        <div class="chart-container">
                            <h4>渠道分布</h4>
                            <canvas id="channelDistributionChart"></canvas>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <h4>手续费收入统计</h4>
                        <canvas id="feeIncomeChart"></canvas>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 切换标签
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            document.getElementById(tabName + '-tab').style.display = 'block';
            event.target.classList.add('active');
            
            // 如果是报表标签，初始化图表
            if (tabName === 'reports') {
                initCharts();
            }
        }

        // 切换渠道状态
        function toggleChannel(element) {
            element.classList.toggle('active');
        }

        // 确认充值
        function confirmDeposit(id) {
            if (confirm('确定要确认这笔充值吗？')) {
                alert('充值已确认');
                location.reload();
            }
        }

        // 拒绝充值
        function rejectDeposit(id) {
            const reason = prompt('请输入拒绝原因：');
            if (reason) {
                alert('充值已拒绝');
                location.reload();
            }
        }

        // 审核通过提现
        function approveWithdrawal(id) {
            if (confirm('确定要审核通过这笔提现吗？')) {
                alert('提现审核通过');
                location.reload();
            }
        }

        // 拒绝提现
        function rejectWithdrawal(id) {
            const reason = prompt('请输入拒绝原因：');
            if (reason) {
                alert('提现已拒绝');
                location.reload();
            }
        }

        // 查看详情
        function viewDepositDetail(id) {
            alert('查看充值详情 ID: ' + id);
        }

        function viewWithdrawalDetail(id) {
            alert('查看提现详情 ID: ' + id);
        }

        // 初始化图表
        function initCharts() {
            // 充值提现趋势图
            const ctx1 = document.getElementById('depositWithdrawalChart').getContext('2d');
            new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '充值金额(万)',
                        data: [120, 150, 180, 200, 220, 250],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        fill: true
                    }, {
                        label: '提现金额(万)',
                        data: [80, 95, 110, 130, 145, 160],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 渠道分布图
            const ctx2 = document.getElementById('channelDistributionChart').getContext('2d');
            new Chart(ctx2, {
                type: 'doughnut',
                data: {
                    labels: ['支付宝', '微信支付', '银行卡', '数字货币'],
                    datasets: [{
                        data: [45, 35, 15, 5],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107', '#6f42c1']
                    }]
                },
                options: {
                    responsive: true
                }
            });

            // 手续费收入图
            const ctx3 = document.getElementById('feeIncomeChart').getContext('2d');
            new Chart(ctx3, {
                type: 'bar',
                data: {
                    labels: ['支付宝', '微信支付', '银行卡', '数字货币'],
                    datasets: [{
                        label: '手续费收入(元)',
                        data: [5200, 3800, 2100, 450],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107', '#6f42c1']
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 导出报表
        function exportReports() {
            alert('正在导出报表...');
        }

        // 新增渠道
        function addChannel() {
            alert('新增支付渠道');
        }

        // 子菜单功能
        document.addEventListener('DOMContentLoaded', function() {
            // 自动展开当前活跃的子菜单
            const activeSubmenu = document.querySelector('.menu-item.has-submenu.active');
            if (activeSubmenu) {
                const submenu = activeSubmenu.querySelector('.submenu');
                if (submenu) {
                    submenu.style.display = 'block';
                    activeSubmenu.classList.add('open');
                }
            }
            
            // 子菜单切换功能
            const submenuToggles = document.querySelectorAll('.submenu-toggle');
            submenuToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const parentItem = this.closest('.menu-item.has-submenu');
                    const submenu = parentItem.querySelector('.submenu');
                    
                    if (parentItem.classList.contains('open')) {
                        parentItem.classList.remove('open');
                        submenu.style.display = 'none';
                    } else {
                        // 关闭其他打开的子菜单
                        document.querySelectorAll('.menu-item.has-submenu.open').forEach(item => {
                            if (item !== parentItem) {
                                item.classList.remove('open');
                                item.querySelector('.submenu').style.display = 'none';
                            }
                        });
                        
                        parentItem.classList.add('open');
                        submenu.style.display = 'block';
                    }
                });
            });
        });
    </script>
</body>
</html> 