<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户钱包管理 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="wallet-management-fixes.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .wallet-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #666;
            font-size: 14px;
            margin: 0 0 10px 0;
        }
        
        .stat-card .value {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .wallet-filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .wallet-table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .wallet-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .wallet-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
        }
        
        .wallet-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .wallet-table th:nth-child(1) { width: 5%; }
        .wallet-table th:nth-child(2) { width: 20%; }
        .wallet-table th:nth-child(3) { width: 15%; }
        .wallet-table th:nth-child(4) { width: 12%; }
        .wallet-table th:nth-child(5) { width: 10%; }
        .wallet-table th:nth-child(6) { width: 10%; }
        .wallet-table th:nth-child(7) { width: 15%; }
        .wallet-table th:nth-child(8) { width: 13%; }
        
        .balance {
            font-weight: bold;
            color: #28a745;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-normal { background: #d4edda; color: #155724; }
        .status-frozen { background: #f8d7da; color: #721c24; }
        .status-restricted { background: #fff3cd; color: #856404; }
        
        .risk-level {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .risk-low { background: #d4edda; color: #155724; }
        .risk-medium { background: #fff3cd; color: #856404; }
        .risk-high { background: #f8d7da; color: #721c24; }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        
        .batch-operations {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .close {
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .info-section {
            margin-bottom: 20px;
        }
        
        .info-section h4 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }
        
        .info-item label {
            font-weight: 500;
            color: #666;
        }
        
        .transaction-history {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .transaction-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .transaction-item:last-child {
            border-bottom: none;
        }
        
        .modal-footer {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        
        @media (max-width: 768px) {
            .wallet-filters {
                flex-direction: column;
                align-items: stretch;
            }
            
            .wallet-table-container {
                overflow-x: auto;
            }
            
            .wallet-table {
                min-width: 800px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-wallet"></i> 用户钱包管理</h2>
            </div>
            <nav class="sidebar-nav">
                <div class="menu-section">
                    <h3>核心功能</h3>
                    <ul class="menu-list">
                        <li><a href="../dashboard/index.html"><i class="fas fa-tachometer-alt"></i> 仪表板总览</a></li>
                        <li><a href="../user-management/index.html"><i class="fas fa-users"></i> 用户管理</a></li>
                        <li><a href="../product-management/index.html"><i class="fas fa-box"></i> 商品管理</a></li>
                        <li><a href="../order-management/index.html"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>业务管理</h3>
                    <ul class="menu-list">
                        <li><a href="../content-management/chat-management.html"><i class="fas fa-comments"></i> 内容管理</a></li>
                        <li><a href="../merchant-management/index.html"><i class="fas fa-store"></i> 商户管理</a></li>
                        <li><a href="../financial-management/index.html"><i class="fas fa-chart-line"></i> 财务管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>钱包管理</h3>
                    <ul class="menu-list">
                        <li class="active"><a href="user-wallet-management.html"><i class="fas fa-wallet"></i> 用户钱包管理</a></li>
                        <li><a href="transaction-management.html"><i class="fas fa-exchange-alt"></i> 交易管理</a></li>
                        <li><a href="deposit-withdrawal.html"><i class="fas fa-money-bill-wave"></i> 充值提现管理</a></li>
                        <li><a href="risk-control.html"><i class="fas fa-shield-alt"></i> 风控管理</a></li>
                        <li><a href="digital-currency.html"><i class="fas fa-coins"></i> 数字货币管理</a></li>
                        <li><a href="financial-reconciliation.html"><i class="fas fa-calculator"></i> 财务对账</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>权限管理</h3>
                    <ul class="menu-list">
                        <li><a href="../permission-management/admin-permissions.html"><i class="fas fa-user-shield"></i> 管理员权限</a></li>
                        <li><a href="../permission-management/api-permissions.html"><i class="fas fa-key"></i> API权限管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>系统设置</h3>
                    <ul class="menu-list">
                        <li><a href="../system-settings/index.html"><i class="fas fa-cog"></i> 系统配置</a></li>
                        <li><a href="../reports-analytics/index.html"><i class="fas fa-chart-bar"></i> 数据分析</a></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="main-header">
                <h1>用户钱包管理</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="exportWalletData()">导出数据</button>
                    <button class="btn btn-success" onclick="openBatchOperation()">批量操作</button>
                </div>
            </div>

            <!-- 钱包统计 -->
            <div class="wallet-stats">
                <div class="stat-card">
                    <h3>总钱包用户</h3>
                    <p class="value">45,892</p>
                </div>
                <div class="stat-card">
                    <h3>活跃钱包</h3>
                    <p class="value">32,456</p>
                </div>
                <div class="stat-card">
                    <h3>总余额</h3>
                    <p class="value">¥12,458,963</p>
                </div>
                <div class="stat-card">
                    <h3>冻结资金</h3>
                    <p class="value">¥256,789</p>
                </div>
                <div class="stat-card">
                    <h3>异常钱包</h3>
                    <p class="value" style="color: #dc3545;">158</p>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="wallet-filters">
                <input type="text" placeholder="搜索用户ID/手机号/邮箱" class="form-control" style="width: 200px;">
                <select class="form-control" style="width: 120px;">
                    <option value="">钱包状态</option>
                    <option value="normal">正常</option>
                    <option value="frozen">冻结</option>
                    <option value="restricted">限制</option>
                </select>
                <select class="form-control" style="width: 120px;">
                    <option value="">实名状态</option>
                    <option value="verified">已认证</option>
                    <option value="pending">待认证</option>
                    <option value="rejected">认证失败</option>
                </select>
                <select class="form-control" style="width: 120px;">
                    <option value="">风险等级</option>
                    <option value="low">低风险</option>
                    <option value="medium">中风险</option>
                    <option value="high">高风险</option>
                </select>
                <button class="btn btn-primary">搜索</button>
                <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
            </div>

            <!-- 批量操作区域 -->
            <div class="batch-operations">
                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                <label for="selectAll">全选</label>
                <button class="btn btn-warning btn-sm" onclick="batchFreeze()">批量冻结</button>
                <button class="btn btn-success btn-sm" onclick="batchUnfreeze()">批量解冻</button>
                <button class="btn btn-info btn-sm" onclick="batchExport()">批量导出</button>
                <span class="selected-count">已选择: <span id="selectedCount">0</span> 个钱包</span>
            </div>

            <!-- 钱包列表 -->
            <div class="wallet-table-container">
                <table class="wallet-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="headerCheckbox"></th>
                            <th>用户信息</th>
                            <th>钱包余额</th>
                            <th>钱包状态</th>
                            <th>实名认证</th>
                            <th>风险等级</th>
                            <th>最后交易</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="walletTableBody">
                        <tr>
                            <td><input type="checkbox" class="wallet-checkbox" data-wallet="1001"></td>
                            <td>
                                <div><strong>张三</strong></div>
                                <div style="color: #666; font-size: 12px;">ID: 1001</div>
                                <div style="color: #666; font-size: 12px;">138****5678</div>
                            </td>
                            <td class="balance">¥15,678.90</td>
                            <td><span class="status-badge status-normal">正常</span></td>
                            <td><span class="status-badge status-normal">已认证</span></td>
                            <td><span class="risk-level risk-low">低</span></td>
                            <td>2024-01-15 14:30</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-xs btn-info" onclick="viewWalletDetail(1001)">详情</button>
                                    <button class="btn-xs btn-warning" onclick="freezeWallet(1001)">冻结</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="wallet-checkbox" data-wallet="1002"></td>
                            <td>
                                <div><strong>李四</strong></div>
                                <div style="color: #666; font-size: 12px;">ID: 1002</div>
                                <div style="color: #666; font-size: 12px;">139****9876</div>
                            </td>
                            <td class="balance">¥0.00</td>
                            <td><span class="status-badge status-frozen">冻结</span></td>
                            <td><span class="status-badge status-normal">已认证</span></td>
                            <td><span class="risk-level risk-high">高</span></td>
                            <td>2024-01-10 09:15</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-xs btn-info" onclick="viewWalletDetail(1002)">详情</button>
                                    <button class="btn-xs btn-success" onclick="unfreezeWallet(1002)">解冻</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination-container">
                <div class="pagination-info">
                    显示 1-20 共 1,532 条记录
                </div>
                <div class="pagination">
                    <button class="btn btn-sm" disabled>上一页</button>
                    <button class="btn btn-sm btn-primary">1</button>
                    <button class="btn btn-sm">2</button>
                    <button class="btn btn-sm">3</button>
                    <span>...</span>
                    <button class="btn btn-sm">77</button>
                    <button class="btn btn-sm">下一页</button>
                </div>
            </div>
        </main>
    </div>

    <!-- 钱包详情模态框 -->
    <div id="walletDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>钱包详情</h3>
                <span class="close" onclick="closeModal('walletDetailModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="wallet-detail-info">
                    <div class="info-section">
                        <h4>基本信息</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>用户ID:</label>
                                <span id="detailUserId">1001</span>
                            </div>
                            <div class="info-item">
                                <label>用户姓名:</label>
                                <span id="detailUserName">张三</span>
                            </div>
                            <div class="info-item">
                                <label>手机号:</label>
                                <span id="detailPhone">138****5678</span>
                            </div>
                            <div class="info-item">
                                <label>钱包余额:</label>
                                <span id="detailBalance" class="balance">¥15,678.90</span>
                            </div>
                            <div class="info-item">
                                <label>冻结金额:</label>
                                <span id="detailFrozen">¥0.00</span>
                            </div>
                            <div class="info-item">
                                <label>可用余额:</label>
                                <span id="detailAvailable" class="balance">¥15,678.90</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h4>状态信息</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>钱包状态:</label>
                                <span id="detailStatus" class="status-badge status-normal">正常</span>
                            </div>
                            <div class="info-item">
                                <label>实名认证:</label>
                                <span id="detailKYC" class="status-badge status-normal">已认证</span>
                            </div>
                            <div class="info-item">
                                <label>风险等级:</label>
                                <span id="detailRisk" class="risk-level risk-low">低</span>
                            </div>
                            <div class="info-item">
                                <label>开通时间:</label>
                                <span id="detailCreateTime">2023-08-15 10:30:00</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h4>交易历史</h4>
                        <div class="transaction-history" id="transactionHistory">
                            <div class="transaction-item">
                                <div>
                                    <div><strong>充值</strong></div>
                                    <div style="font-size: 12px; color: #666;">2024-01-15 14:30</div>
                                </div>
                                <div style="color: #28a745; font-weight: bold;">+¥500.00</div>
                            </div>
                            <div class="transaction-item">
                                <div>
                                    <div><strong>购买商品</strong></div>
                                    <div style="font-size: 12px; color: #666;">2024-01-14 16:20</div>
                                </div>
                                <div style="color: #dc3545; font-weight: bold;">-¥89.90</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('walletDetailModal')">关闭</button>
                <button class="btn btn-primary" onclick="editWallet()">编辑钱包</button>
            </div>
        </div>
    </div>

    <script>
        // 菜单交互功能
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.admin-sidebar .menu-list li');
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    if (this.querySelector('ul')) {
                        e.preventDefault();
                        this.classList.toggle('expanded');
                    }
                });
            });
        });

        // 钱包管理功能
        function exportWalletData() {
            alert('导出钱包数据功能');
        }

        function openBatchOperation() {
            alert('批量操作功能');
        }

        function resetFilters() {
            document.querySelectorAll('.wallet-filters input, .wallet-filters select').forEach(input => {
                input.value = '';
            });
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.wallet-table input[type="checkbox"]:not(#selectAll)');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        function batchFreeze() {
            const selected = document.querySelectorAll('.wallet-table input[type="checkbox"]:checked:not(#selectAll)');
            if (selected.length === 0) {
                alert('请选择要冻结的钱包');
                return;
            }
            alert(`批量冻结 ${selected.length} 个钱包`);
        }

        function batchUnfreeze() {
            const selected = document.querySelectorAll('.wallet-table input[type="checkbox"]:checked:not(#selectAll)');
            if (selected.length === 0) {
                alert('请选择要解冻的钱包');
                return;
            }
            alert(`批量解冻 ${selected.length} 个钱包`);
        }

        function viewWalletDetail(userId) {
            document.getElementById('walletDetailModal').style.display = 'block';
        }

        function closeModal() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.style.display = 'none';
            });
        }

        function freezeWallet(userId) {
            alert(`冻结用户 ${userId} 的钱包`);
        }

        function adjustBalance(userId) {
            alert(`调整用户 ${userId} 的余额`);
        }

        // 子菜单功能
        document.addEventListener('DOMContentLoaded', function() {
            // 自动展开当前活跃的子菜单
            const activeSubmenu = document.querySelector('.menu-item.has-submenu.active');
            if (activeSubmenu) {
                const submenu = activeSubmenu.querySelector('.submenu');
                if (submenu) {
                    submenu.style.display = 'block';
                    activeSubmenu.classList.add('open');
                }
            }
            
            // 子菜单切换功能
            const submenuToggles = document.querySelectorAll('.submenu-toggle');
            submenuToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const parentItem = this.closest('.menu-item.has-submenu');
                    const submenu = parentItem.querySelector('.submenu');
                    const arrow = parentItem.querySelector('.menu-arrow');
                    
                    if (parentItem.classList.contains('open')) {
                        parentItem.classList.remove('open');
                        submenu.style.display = 'none';
                    } else {
                        // 关闭其他打开的子菜单
                        document.querySelectorAll('.menu-item.has-submenu.open').forEach(item => {
                            if (item !== parentItem) {
                                item.classList.remove('open');
                                item.querySelector('.submenu').style.display = 'none';
                            }
                        });
                        
                        parentItem.classList.add('open');
                        submenu.style.display = 'block';
                    }
                });
            });
        });
    </script>
</body>
</html> 