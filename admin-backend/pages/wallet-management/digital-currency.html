<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字货币管理 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="wallet-management-fixes.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .crypto-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .crypto-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .crypto-card h3 {
            color: #666;
            font-size: 14px;
            margin: 0 0 10px 0;
        }
        
        .crypto-card .value {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .crypto-tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .tab-header {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            overflow-x: auto;
        }
        
        .tab-button {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            white-space: nowrap;
        }
        
        .tab-button.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: #f8f9fa;
        }
        
        .tab-content {
            padding: 20px;
        }
        
        .coin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .coin-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        
        .coin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .coin-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .coin-logo {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .coin-status {
            width: 40px;
            height: 20px;
            border-radius: 10px;
            background: #ccc;
            position: relative;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .coin-status.active {
            background: #28a745;
        }
        
        .coin-status::after {
            content: '';
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: white;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s;
        }
        
        .coin-status.active::after {
            left: 22px;
        }
        
        .network-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .network-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
        }
        
        .network-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .network-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-online { background: #d4edda; color: #155724; }
        .status-offline { background: #f8d7da; color: #721c24; }
        .status-syncing { background: #fff3cd; color: #856404; }
        
        .wallet-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .wallet-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        
        .wallet-balance {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
            margin: 10px 0;
        }
        
        .wallet-address {
            font-family: monospace;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        
        .tx-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .tx-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
        }
        
        .tx-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .tx-hash {
            font-family: monospace;
            font-size: 12px;
        }
        
        .tx-amount {
            font-weight: bold;
        }
        
        .tx-in { color: #28a745; }
        .tx-out { color: #dc3545; }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .coin-grid {
                grid-template-columns: 1fr;
            }
            
            .wallet-info {
                grid-template-columns: 1fr;
            }
            
            .crypto-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-coins"></i> 数字货币管理</h2>
            </div>
            <nav class="sidebar-nav">
                <div class="menu-section">
                    <h3>核心功能</h3>
                    <ul class="menu-list">
                        <li><a href="../dashboard/index.html"><i class="fas fa-tachometer-alt"></i> 仪表板总览</a></li>
                        <li><a href="../user-management/index.html"><i class="fas fa-users"></i> 用户管理</a></li>
                        <li><a href="../product-management/index.html"><i class="fas fa-box"></i> 商品管理</a></li>
                        <li><a href="../order-management/index.html"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>业务管理</h3>
                    <ul class="menu-list">
                        <li><a href="../content-management/chat-management.html"><i class="fas fa-comments"></i> 内容管理</a></li>
                        <li><a href="../merchant-management/index.html"><i class="fas fa-store"></i> 商户管理</a></li>
                        <li><a href="../financial-management/index.html"><i class="fas fa-chart-line"></i> 财务管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>钱包管理</h3>
                    <ul class="menu-list">
                        <li><a href="user-wallet-management.html"><i class="fas fa-wallet"></i> 用户钱包管理</a></li>
                        <li><a href="transaction-management.html"><i class="fas fa-exchange-alt"></i> 交易管理</a></li>
                        <li><a href="deposit-withdrawal.html"><i class="fas fa-money-bill-wave"></i> 充值提现管理</a></li>
                        <li><a href="risk-control.html"><i class="fas fa-shield-alt"></i> 风控管理</a></li>
                        <li class="active"><a href="digital-currency.html"><i class="fas fa-coins"></i> 数字货币管理</a></li>
                        <li><a href="financial-reconciliation.html"><i class="fas fa-calculator"></i> 财务对账</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>权限管理</h3>
                    <ul class="menu-list">
                        <li><a href="../permission-management/admin-permissions.html"><i class="fas fa-user-shield"></i> 管理员权限</a></li>
                        <li><a href="../permission-management/api-permissions.html"><i class="fas fa-key"></i> API权限管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>系统设置</h3>
                    <ul class="menu-list">
                        <li><a href="../system-settings/index.html"><i class="fas fa-cog"></i> 系统配置</a></li>
                        <li><a href="../reports-analytics/index.html"><i class="fas fa-chart-bar"></i> 数据分析</a></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="main-header">
                <h1>数字货币管理</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="addCurrency()">新增币种</button>
                    <button class="btn btn-success" onclick="exportCryptoReport()">导出报告</button>
                </div>
            </div>

            <!-- 数字货币概览 -->
            <div class="crypto-overview">
                <div class="crypto-card">
                    <h3>支持币种</h3>
                    <p class="value">8</p>
                </div>
                <div class="crypto-card">
                    <h3>总资产价值</h3>
                    <p class="value">¥1,256,789</p>
                </div>
                <div class="crypto-card">
                    <h3>热钱包余额</h3>
                    <p class="value">¥89,456</p>
                </div>
                <div class="crypto-card">
                    <h3>冷钱包余额</h3>
                    <p class="value">¥1,167,333</p>
                </div>
                <div class="crypto-card">
                    <h3>24h交易量</h3>
                    <p class="value">¥45,678</p>
                </div>
                <div class="crypto-card">
                    <h3>网络状态</h3>
                    <p class="value" style="color: #28a745;">正常</p>
                </div>
            </div>

            <!-- 管理标签 -->
            <div class="crypto-tabs">
                <div class="tab-header">
                    <button class="tab-button active" onclick="switchTab('currencies')">币种管理</button>
                    <button class="tab-button" onclick="switchTab('networks')">网络监控</button>
                    <button class="tab-button" onclick="switchTab('wallets')">钱包管理</button>
                    <button class="tab-button" onclick="switchTab('transactions')">链上交易</button>
                    <button class="tab-button" onclick="switchTab('rates')">汇率管理</button>
                </div>

                <!-- 币种管理 -->
                <div id="currencies-tab" class="tab-content">
                    <div class="coin-grid">
                        <!-- U币 -->
                        <div class="coin-card">
                            <div class="coin-header">
                                <div class="coin-info">
                                    <div class="coin-logo" style="background: #9333ea; color: white;">Ⓤ</div>
                                    <div>
                                        <h4>UCoin (UC)</h4>
                                        <div style="font-size: 12px; color: #666;">平台专用数字货币</div>
                                    </div>
                                </div>
                                <div class="coin-status active" onclick="toggleCoin(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>当前价格 (CNY)</label>
                                <input type="number" value="300.00" class="form-control" readonly>
                            </div>
                            <div class="form-group">
                                <label>最小充值金额</label>
                                <input type="number" value="10" step="1" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>最小提现金额</label>
                                <input type="number" value="50" step="1" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>提现手续费</label>
                                <input type="number" value="0.1" step="0.1" class="form-control">
                            </div>
                            <button class="btn btn-primary btn-sm">保存配置</button>
                        </div>

                        <!-- 以太坊 -->
                        <div class="coin-card">
                            <div class="coin-header">
                                <div class="coin-info">
                                    <div class="coin-logo" style="background: #627eea; color: white;">Ξ</div>
                                    <div>
                                        <h4>Ethereum (ETH)</h4>
                                        <div style="font-size: 12px; color: #666;">智能合约平台</div>
                                    </div>
                                </div>
                                <div class="coin-status active" onclick="toggleCoin(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>当前价格 (USD)</label>
                                <input type="number" value="2630.75" class="form-control" readonly>
                            </div>
                            <div class="form-group">
                                <label>最小充值金额</label>
                                <input type="number" value="0.01" step="0.01" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>最小提现金额</label>
                                <input type="number" value="0.05" step="0.01" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>提现手续费</label>
                                <input type="number" value="0.005" step="0.001" class="form-control">
                            </div>
                            <button class="btn btn-primary btn-sm">保存配置</button>
                        </div>

                        <!-- USDT -->
                        <div class="coin-card">
                            <div class="coin-header">
                                <div class="coin-info">
                                    <div class="coin-logo" style="background: #26a17b; color: white;">₮</div>
                                    <div>
                                        <h4>Tether (USDT)</h4>
                                        <div style="font-size: 12px; color: #666;">稳定币</div>
                                    </div>
                                </div>
                                <div class="coin-status active" onclick="toggleCoin(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>当前价格 (USD)</label>
                                <input type="number" value="1.00" class="form-control" readonly>
                            </div>
                            <div class="form-group">
                                <label>最小充值金额</label>
                                <input type="number" value="10" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>最小提现金额</label>
                                <input type="number" value="20" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>提现手续费</label>
                                <input type="number" value="1" class="form-control">
                            </div>
                            <button class="btn btn-primary btn-sm">保存配置</button>
                        </div>
                    </div>
                </div>

                <!-- 网络监控 -->
                <div id="networks-tab" class="tab-content" style="display: none;">
                    <div class="table-container">
                        <table class="network-table">
                            <thead>
                                <tr>
                                    <th width="15%">网络名称</th>
                                    <th width="12%">币种</th>
                                    <th width="10%">状态</th>
                                    <th width="12%">区块高度</th>
                                    <th width="12%">确认数</th>
                                    <th width="15%">同步状态</th>
                                    <th width="12%">网络费用</th>
                                    <th width="12%">最后更新</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>UCoin Mainnet</strong></td>
                                    <td>UC</td>
                                    <td><span class="network-status status-online">在线</span></td>
                                    <td>1,125,456</td>
                                    <td>3</td>
                                    <td>100%</td>
                                    <td>0.1 UC</td>
                                    <td>2024-01-15 14:30</td>
                                </tr>
                                <tr>
                                    <td><strong>Ethereum Mainnet</strong></td>
                                    <td>ETH</td>
                                    <td><span class="network-status status-online">在线</span></td>
                                    <td>18,956,789</td>
                                    <td>12</td>
                                    <td>100%</td>
                                    <td>25 Gwei</td>
                                    <td>2024-01-15 14:30</td>
                                </tr>
                                <tr>
                                    <td><strong>TRON Mainnet</strong></td>
                                    <td>TRX/USDT</td>
                                    <td><span class="network-status status-syncing">同步中</span></td>
                                    <td>56,789,123</td>
                                    <td>19</td>
                                    <td>98.5%</td>
                                    <td>1 TRX</td>
                                    <td>2024-01-15 14:28</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 钱包管理 -->
                <div id="wallets-tab" class="tab-content" style="display: none;">
                    <div class="wallet-info">
                        <!-- 热钱包 -->
                        <div class="wallet-card">
                            <h4>🔥 热钱包</h4>
                            <div class="wallet-balance">¥89,456.78</div>
                            <div style="margin-bottom: 10px;">
                                <strong>UC:</strong> 298.2 UC
                            </div>
                            <div class="wallet-address">
                                UC3qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh
                            </div>
                            <div style="margin: 15px 0;">
                                <button class="btn btn-warning btn-sm">转移到冷钱包</button>
                                <button class="btn btn-info btn-sm">查看交易</button>
                            </div>
                        </div>

                        <!-- 冷钱包 -->
                        <div class="wallet-card">
                            <h4>❄️ 冷钱包</h4>
                            <div class="wallet-balance">¥1,167,333.22</div>
                            <div style="margin-bottom: 10px;">
                                <strong>UC:</strong> 3,891.1 UC
                            </div>
                            <div class="wallet-address">
                                UC5qar0srrr7xfkvy5l643lydnw9re59gtzzwf5mdq
                            </div>
                            <div style="margin: 15px 0;">
                                <button class="btn btn-success btn-sm">转移到热钱包</button>
                                <button class="btn btn-info btn-sm">查看余额</button>
                            </div>
                        </div>
                    </div>

                    <div class="wallet-info">
                        <!-- ETH热钱包 -->
                        <div class="wallet-card">
                            <h4>🔥 ETH热钱包</h4>
                            <div class="wallet-balance">$52,615.00</div>
                            <div style="margin-bottom: 10px;">
                                <strong>ETH:</strong> 20.5 ETH
                            </div>
                            <div class="wallet-address">
                                ******************************************
                            </div>
                            <div style="margin: 15px 0;">
                                <button class="btn btn-warning btn-sm">转移到冷钱包</button>
                                <button class="btn btn-info btn-sm">查看交易</button>
                            </div>
                        </div>

                        <!-- ETH冷钱包 -->
                        <div class="wallet-card">
                            <h4>❄️ ETH冷钱包</h4>
                            <div class="wallet-balance">$263,075.00</div>
                            <div style="margin-bottom: 10px;">
                                <strong>ETH:</strong> 100.25 ETH
                            </div>
                            <div class="wallet-address">
                                0x8ba1f109551bD432803012645Hac136c23b1e0F
                            </div>
                            <div style="margin: 15px 0;">
                                <button class="btn btn-success btn-sm">转移到热钱包</button>
                                <button class="btn btn-info btn-sm">查看余额</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 链上交易 -->
                <div id="transactions-tab" class="tab-content" style="display: none;">
                    <div class="table-container">
                        <table class="tx-table">
                            <thead>
                                <tr>
                                    <th width="20%">交易哈希</th>
                                    <th width="10%">币种</th>
                                    <th width="8%">类型</th>
                                    <th width="15%">金额</th>
                                    <th width="8%">确认数</th>
                                    <th width="10%">状态</th>
                                    <th width="15%">时间</th>
                                    <th width="14%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="tx-hash">a1b2c3d4...f9e8d7c6</div>
                                    </td>
                                    <td>UC</td>
                                    <td>充值</td>
                                    <td class="tx-amount tx-in">+150.5 UC</td>
                                    <td>3/3</td>
                                    <td><span class="network-status status-online">已确认</span></td>
                                    <td>2024-01-15 14:20</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewTxDetail('a1b2c3d4')">详情</button>
                                            <button class="btn-xs btn-success" onclick="openBlockExplorer('a1b2c3d4')">浏览器</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="tx-hash">9f8e7d6c...3b2a1098</div>
                                    </td>
                                    <td>ETH</td>
                                    <td>提现</td>
                                    <td class="tx-amount tx-out">-2.5 ETH</td>
                                    <td>12/12</td>
                                    <td><span class="network-status status-online">已确认</span></td>
                                    <td>2024-01-15 13:45</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewTxDetail('9f8e7d6c')">详情</button>
                                            <button class="btn-xs btn-success" onclick="openBlockExplorer('9f8e7d6c')">浏览器</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="tx-hash">5d4c3b2a...8f7e6d5c</div>
                                    </td>
                                    <td>USDT</td>
                                    <td>内部转账</td>
                                    <td class="tx-amount">1,000 USDT</td>
                                    <td>19/19</td>
                                    <td><span class="network-status status-syncing">处理中</span></td>
                                    <td>2024-01-15 13:30</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewTxDetail('5d4c3b2a')">详情</button>
                                            <button class="btn-xs btn-warning" onclick="accelerateTx('5d4c3b2a')">加速</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 汇率管理 -->
                <div id="rates-tab" class="tab-content" style="display: none;">
                    <div class="chart-container">
                        <h4>价格走势</h4>
                        <canvas id="priceChart"></canvas>
                    </div>
                    
                    <div class="table-container">
                        <table class="network-table">
                            <thead>
                                <tr>
                                    <th width="15%">币种</th>
                                    <th width="15%">当前价格</th>
                                    <th width="12%">24h变化</th>
                                    <th width="12%">7d变化</th>
                                    <th width="15%">最后更新</th>
                                    <th width="15%">数据源</th>
                                    <th width="16%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>UC/CNY</strong></td>
                                    <td>¥300.00</td>
                                    <td style="color: #28a745;">+0.15%</td>
                                    <td style="color: #28a745;">+0.33%</td>
                                    <td>2024-01-15 14:30</td>
                                    <td>TeleShop API</td>
                                    <td>
                                        <button class="btn-xs btn-info">手动更新</button>
                                        <button class="btn-xs btn-warning">配置</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>ETH/USD</strong></td>
                                    <td>$2,630.75</td>
                                    <td style="color: #28a745;">*****%</td>
                                    <td style="color: #28a745;">*****%</td>
                                    <td>2024-01-15 14:30</td>
                                    <td>CoinMarketCap</td>
                                    <td>
                                        <button class="btn-xs btn-info">手动更新</button>
                                        <button class="btn-xs btn-warning">配置</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>USDT/USD</strong></td>
                                    <td>$1.0003</td>
                                    <td style="color: #28a745;">+0.03%</td>
                                    <td style="color: #dc3545;">-0.01%</td>
                                    <td>2024-01-15 14:30</td>
                                    <td>Binance</td>
                                    <td>
                                        <button class="btn-xs btn-info">手动更新</button>
                                        <button class="btn-xs btn-warning">配置</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 菜单交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化菜单状态
            const menuItems = document.querySelectorAll('.admin-sidebar .menu-list li');
            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && link.getAttribute('href') === '#') {
                    // 如果是父菜单项，添加展开/收起功能
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const submenu = item.querySelector('.submenu');
                        if (submenu) {
                            item.classList.toggle('open');
                            submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
                        }
                    });
                }
            });
        });

        // 切换标签
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            document.getElementById(tabName + '-tab').style.display = 'block';
            event.target.classList.add('active');
            
            if (tabName === 'rates') {
                initPriceChart();
            }
        }

        // 切换币种状态
        function toggleCoin(element) {
            element.classList.toggle('active');
        }

        // 查看交易详情
        function viewTxDetail(txHash) {
            alert('查看交易详情: ' + txHash);
        }

        // 打开区块浏览器
        function openBlockExplorer(txHash) {
            // 实际应用中应该根据币种跳转到对应的区块浏览器
            alert('在区块浏览器中查看: ' + txHash);
        }

        // 加速交易
        function accelerateTx(txHash) {
            if (confirm('确定要加速这笔交易吗？可能需要额外的网络费用。')) {
                alert('交易加速请求已提交');
            }
        }

        // 新增币种
        function addCurrency() {
            alert('新增数字货币');
        }

        // 导出报告
        function exportCryptoReport() {
            alert('正在导出数字货币报告...');
        }

        // 初始化价格图表
        function initPriceChart() {
            const ctx = document.getElementById('priceChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: 'BTC/USD',
                        data: [42800, 43200, 42950, 43250, 43100, 43250],
                        borderColor: '#f7931a',
                        backgroundColor: 'rgba(247, 147, 26, 0.1)',
                        fill: true
                    }, {
                        label: 'ETH/USD',
                        data: [2580, 2620, 2600, 2630, 2610, 2630],
                        borderColor: '#627eea',
                        backgroundColor: 'rgba(98, 126, 234, 0.1)',
                        fill: true,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 