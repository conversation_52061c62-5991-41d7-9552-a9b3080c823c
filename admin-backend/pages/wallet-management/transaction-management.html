<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易管理 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="wallet-management-fixes.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .transaction-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #666;
            font-size: 14px;
            margin: 0 0 10px 0;
        }
        
        .stat-card .value {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .filters-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .filter-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .date-range {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .transaction-table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .transaction-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .transaction-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
        }
        
        .transaction-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .transaction-table th:nth-child(1) { width: 15%; }
        .transaction-table th:nth-child(2) { width: 15%; }
        .transaction-table th:nth-child(3) { width: 10%; }
        .transaction-table th:nth-child(4) { width: 12%; }
        .transaction-table th:nth-child(5) { width: 10%; }
        .transaction-table th:nth-child(6) { width: 10%; }
        .transaction-table th:nth-child(7) { width: 15%; }
        .transaction-table th:nth-child(8) { width: 13%; }
        
        .amount {
            font-weight: bold;
        }
        
        .amount.positive {
            color: #28a745;
        }
        
        .amount.negative {
            color: #dc3545;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-success { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-refunded { background: #d1ecf1; color: #0c5460; }
        
        .transaction-type {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            background: #e9ecef;
            color: #495057;
        }
        
        .risk-flag {
            color: #dc3545;
            font-weight: bold;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        
        .bulk-actions {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .close {
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .detail-section {
            margin-bottom: 20px;
        }
        
        .detail-section h4 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .detail-item label {
            font-weight: 500;
            color: #666;
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .transaction-table-container {
                overflow-x: auto;
            }
            
            .transaction-table {
                min-width: 1000px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-exchange-alt"></i> 交易管理</h2>
            </div>
            <nav class="sidebar-nav">
                <div class="menu-section">
                    <h3>核心功能</h3>
                    <ul class="menu-list">
                        <li><a href="../dashboard/index.html"><i class="fas fa-tachometer-alt"></i> 仪表板总览</a></li>
                        <li><a href="../user-management/index.html"><i class="fas fa-users"></i> 用户管理</a></li>
                        <li><a href="../product-management/index.html"><i class="fas fa-box"></i> 商品管理</a></li>
                        <li><a href="../order-management/index.html"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>业务管理</h3>
                    <ul class="menu-list">
                        <li><a href="../content-management/chat-management.html"><i class="fas fa-comments"></i> 内容管理</a></li>
                        <li><a href="../merchant-management/index.html"><i class="fas fa-store"></i> 商户管理</a></li>
                        <li><a href="../financial-management/index.html"><i class="fas fa-chart-line"></i> 财务管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>钱包管理</h3>
                    <ul class="menu-list">
                        <li><a href="user-wallet-management.html"><i class="fas fa-wallet"></i> 用户钱包管理</a></li>
                        <li class="active"><a href="transaction-management.html"><i class="fas fa-exchange-alt"></i> 交易管理</a></li>
                        <li><a href="deposit-withdrawal.html"><i class="fas fa-money-bill-wave"></i> 充值提现管理</a></li>
                        <li><a href="risk-control.html"><i class="fas fa-shield-alt"></i> 风控管理</a></li>
                        <li><a href="digital-currency.html"><i class="fas fa-coins"></i> 数字货币管理</a></li>
                        <li><a href="financial-reconciliation.html"><i class="fas fa-calculator"></i> 财务对账</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>权限管理</h3>
                    <ul class="menu-list">
                        <li><a href="../permission-management/admin-permissions.html"><i class="fas fa-user-shield"></i> 管理员权限</a></li>
                        <li><a href="../permission-management/api-permissions.html"><i class="fas fa-key"></i> API权限管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>系统设置</h3>
                    <ul class="menu-list">
                        <li><a href="../system-settings/index.html"><i class="fas fa-cog"></i> 系统配置</a></li>
                        <li><a href="../reports-analytics/index.html"><i class="fas fa-chart-bar"></i> 数据分析</a></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="main-header">
                <h1>交易管理</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="exportTransactions()">导出交易</button>
                    <button class="btn btn-success" onclick="showChart()">统计图表</button>
                </div>
            </div>

            <!-- 交易统计 -->
            <div class="transaction-stats">
                <div class="stat-card">
                    <h3>今日交易</h3>
                    <p class="value">1,247</p>
                </div>
                <div class="stat-card">
                    <h3>交易总额</h3>
                    <p class="value">¥2,568,943</p>
                </div>
                <div class="stat-card">
                    <h3>成功率</h3>
                    <p class="value" style="color: #28a745;">98.5%</p>
                </div>
                <div class="stat-card">
                    <h3>异常交易</h3>
                    <p class="value" style="color: #dc3545;">23</p>
                </div>
                <div class="stat-card">
                    <h3>待审核</h3>
                    <p class="value" style="color: #ffc107;">45</p>
                </div>
                <div class="stat-card">
                    <h3>手续费收入</h3>
                    <p class="value">¥15,678</p>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="filters-section">
                <div class="filter-row">
                    <div class="date-range">
                        <label>时间范围：</label>
                        <input type="date" class="form-control" style="width: 150px;">
                        <span>至</span>
                        <input type="date" class="form-control" style="width: 150px;">
                    </div>
                    <input type="text" placeholder="交易ID/用户ID" class="form-control" style="width: 200px;">
                    <select class="form-control" style="width: 120px;">
                        <option value="">交易类型</option>
                        <option value="deposit">充值</option>
                        <option value="withdraw">提现</option>
                        <option value="transfer">转账</option>
                        <option value="payment">支付</option>
                        <option value="refund">退款</option>
                    </select>
                </div>
                <div class="filter-row">
                    <select class="form-control" style="width: 120px;">
                        <option value="">交易状态</option>
                        <option value="pending">待处理</option>
                        <option value="success">成功</option>
                        <option value="failed">失败</option>
                        <option value="refunded">已退款</option>
                    </select>
                    <input type="number" placeholder="最小金额" class="form-control" style="width: 120px;">
                    <input type="number" placeholder="最大金额" class="form-control" style="width: 120px;">
                    <select class="form-control" style="width: 120px;">
                        <option value="">风险等级</option>
                        <option value="normal">正常</option>
                        <option value="suspicious">可疑</option>
                        <option value="high_risk">高风险</option>
                    </select>
                    <button class="btn btn-primary">搜索</button>
                    <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
                </div>
            </div>

            <!-- 批量操作 -->
            <div class="bulk-actions">
                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                <label for="selectAll">全选</label>
                <button class="btn btn-warning btn-sm" onclick="batchApprove()">批量通过</button>
                <button class="btn btn-danger btn-sm" onclick="batchReject()">批量拒绝</button>
                <button class="btn btn-info btn-sm" onclick="batchExport()">批量导出</button>
                <span class="selected-count">已选择: <span id="selectedCount">0</span> 笔交易</span>
            </div>

            <!-- 交易列表 -->
            <div class="transaction-table-container">
                <table class="transaction-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="headerCheckbox"></th>
                            <th>交易ID</th>
                            <th>交易类型</th>
                            <th>用户信息</th>
                            <th>金额</th>
                            <th>手续费</th>
                            <th>状态</th>
                            <th>交易时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" class="transaction-checkbox" data-id="TXN001"></td>
                            <td>
                                <div><strong>TXN001234567</strong></div>
                                <div style="font-size: 12px; color: #666;">订单支付</div>
                            </td>
                            <td><span class="transaction-type">支付</span></td>
                            <td>
                                <div><strong>张三</strong></div>
                                <div style="font-size: 12px; color: #666;">ID: 1001</div>
                            </td>
                            <td class="amount negative">-¥299.00</td>
                            <td>¥2.99</td>
                            <td><span class="status-badge status-success">成功</span></td>
                            <td>2024-01-15 14:30:25</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-xs btn-info" onclick="viewTransactionDetail('TXN001')">详情</button>
                                    <button class="btn-xs btn-warning" onclick="processRefund('TXN001')">退款</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="transaction-checkbox" data-id="TXN002"></td>
                            <td>
                                <div><strong>TXN001234568</strong></div>
                                <div style="font-size: 12px; color: #666;">钱包充值</div>
                            </td>
                            <td><span class="transaction-type">充值</span></td>
                            <td>
                                <div><strong>李四</strong></div>
                                <div style="font-size: 12px; color: #666;">ID: 1002</div>
                            </td>
                            <td class="amount positive">+¥1,000.00</td>
                            <td>¥0.00</td>
                            <td><span class="status-badge status-pending">待处理</span></td>
                            <td>2024-01-15 14:25:12</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-xs btn-info" onclick="viewTransactionDetail('TXN002')">详情</button>
                                    <button class="btn-xs btn-success" onclick="approveTransaction('TXN002')">通过</button>
                                    <button class="btn-xs btn-danger" onclick="rejectTransaction('TXN002')">拒绝</button>
                                </div>
                            </td>
                        </tr>
                        <tr style="background: #fff5f5;">
                            <td><input type="checkbox" class="transaction-checkbox" data-id="TXN003"></td>
                            <td>
                                <div><strong>TXN001234569</strong></div>
                                <div style="font-size: 12px; color: #666;">大额转账</div>
                                <div class="risk-flag">⚠️ 风险交易</div>
                            </td>
                            <td><span class="transaction-type">转账</span></td>
                            <td>
                                <div><strong>王五</strong></div>
                                <div style="font-size: 12px; color: #666;">ID: 1003</div>
                            </td>
                            <td class="amount negative">-¥50,000.00</td>
                            <td>¥500.00</td>
                            <td><span class="status-badge status-pending">待审核</span></td>
                            <td>2024-01-15 14:20:33</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-xs btn-info" onclick="viewTransactionDetail('TXN003')">详情</button>
                                    <button class="btn-xs btn-warning" onclick="riskReview('TXN003')">风控审核</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination-container">
                <div class="pagination-info">
                    显示 1-20 共 2,847 条记录
                </div>
                <div class="pagination">
                    <button class="btn btn-sm" disabled>上一页</button>
                    <button class="btn btn-sm btn-primary">1</button>
                    <button class="btn btn-sm">2</button>
                    <button class="btn btn-sm">3</button>
                    <span>...</span>
                    <button class="btn btn-sm">143</button>
                    <button class="btn btn-sm">下一页</button>
                </div>
            </div>
        </main>
    </div>

    <!-- 交易详情模态框 -->
    <div id="transactionDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>交易详情</h3>
                <span class="close" onclick="closeModal('transactionDetailModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="detail-section">
                    <h4>基本信息</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>交易ID:</label>
                            <span>TXN001234567</span>
                        </div>
                        <div class="detail-item">
                            <label>交易类型:</label>
                            <span>订单支付</span>
                        </div>
                        <div class="detail-item">
                            <label>交易金额:</label>
                            <span class="amount negative">-¥299.00</span>
                        </div>
                        <div class="detail-item">
                            <label>手续费:</label>
                            <span>¥2.99</span>
                        </div>
                        <div class="detail-item">
                            <label>实际金额:</label>
                            <span>¥301.99</span>
                        </div>
                        <div class="detail-item">
                            <label>交易状态:</label>
                            <span class="status-badge status-success">成功</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>用户信息</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>用户ID:</label>
                            <span>1001</span>
                        </div>
                        <div class="detail-item">
                            <label>用户姓名:</label>
                            <span>张三</span>
                        </div>
                        <div class="detail-item">
                            <label>手机号:</label>
                            <span>138****5678</span>
                        </div>
                        <div class="detail-item">
                            <label>账户余额:</label>
                            <span>¥15,378.90</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>交易详情</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>交易时间:</label>
                            <span>2024-01-15 14:30:25</span>
                        </div>
                        <div class="detail-item">
                            <label>支付方式:</label>
                            <span>钱包余额</span>
                        </div>
                        <div class="detail-item">
                            <label>商户订单号:</label>
                            <span>ORD20240115001</span>
                        </div>
                        <div class="detail-item">
                            <label>第三方流水号:</label>
                            <span>WX2024011514302501</span>
                        </div>
                        <div class="detail-item">
                            <label>IP地址:</label>
                            <span>*************</span>
                        </div>
                        <div class="detail-item">
                            <label>设备信息:</label>
                            <span>iPhone 13 Pro</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>风控信息</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>风险等级:</label>
                            <span style="color: #28a745;">正常</span>
                        </div>
                        <div class="detail-item">
                            <label>风控规则:</label>
                            <span>金额限制检查</span>
                        </div>
                        <div class="detail-item">
                            <label>审核结果:</label>
                            <span>自动通过</span>
                        </div>
                        <div class="detail-item">
                            <label>处理时间:</label>
                            <span>2024-01-15 14:30:26</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('transactionDetailModal')">关闭</button>
                <button class="btn btn-primary" onclick="printTransaction()">打印</button>
                <button class="btn btn-warning" onclick="processRefund()">处理退款</button>
            </div>
        </div>
    </div>

    <!-- 图表模态框 -->
    <div id="chartModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>交易统计图表</h3>
                <span class="close" onclick="closeModal('chartModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="chart-container">
                    <canvas id="transactionChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 菜单交互功能
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.admin-sidebar .menu-list li');
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    if (this.querySelector('ul')) {
                        e.preventDefault();
                        this.classList.toggle('expanded');
                    }
                });
            });
        });

        // 全选功能
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.transaction-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            const selectedCheckboxes = document.querySelectorAll('.transaction-checkbox:checked');
            document.getElementById('selectedCount').textContent = selectedCheckboxes.length;
        }

        // 查看交易详情
        function viewTransactionDetail(transactionId) {
            document.getElementById('transactionDetailModal').style.display = 'block';
        }

        // 批准交易
        function approveTransaction(transactionId) {
            if (confirm('确定要批准这笔交易吗？')) {
                alert('交易已批准');
                location.reload();
            }
        }

        // 拒绝交易
        function rejectTransaction(transactionId) {
            const reason = prompt('请输入拒绝原因：');
            if (reason) {
                alert('交易已拒绝');
                location.reload();
            }
        }

        // 处理退款
        function processRefund(transactionId) {
            const amount = prompt('请输入退款金额：');
            if (amount && confirm(`确定退款 ¥${amount} 吗？`)) {
                alert('退款处理中...');
                location.reload();
            }
        }

        // 风控审核
        function riskReview(transactionId) {
            alert('跳转到风控审核页面');
        }

        // 批量批准
        function batchApprove() {
            const selected = document.querySelectorAll('.transaction-checkbox:checked');
            if (selected.length === 0) {
                alert('请选择要批准的交易');
                return;
            }
            if (confirm(`确定要批准选中的 ${selected.length} 笔交易吗？`)) {
                alert('批量批准成功');
                location.reload();
            }
        }

        // 批量拒绝
        function batchReject() {
            const selected = document.querySelectorAll('.transaction-checkbox:checked');
            if (selected.length === 0) {
                alert('请选择要拒绝的交易');
                return;
            }
            const reason = prompt('请输入拒绝原因：');
            if (reason && confirm(`确定要拒绝选中的 ${selected.length} 笔交易吗？`)) {
                alert('批量拒绝成功');
                location.reload();
            }
        }

        // 导出交易
        function exportTransactions() {
            alert('正在导出交易数据...');
        }

        // 显示图表
        function showChart() {
            document.getElementById('chartModal').style.display = 'block';
            
            const ctx = document.getElementById('transactionChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00'],
                    datasets: [{
                        label: '交易金额（万元）',
                        data: [12, 19, 15, 25, 22, 30, 28],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        fill: true
                    }, {
                        label: '交易笔数',
                        data: [65, 78, 82, 93, 85, 102, 98],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        fill: true,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        // 重置筛选
        function resetFilters() {
            document.querySelectorAll('.filters-section input, .filters-section select').forEach(element => {
                element.value = '';
            });
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 监听复选框变化
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('transaction-checkbox')) {
                updateSelectedCount();
            }
        });

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // 打印交易
        function printTransaction() {
            window.print();
        }

        // 子菜单功能
        document.addEventListener('DOMContentLoaded', function() {
            // 自动展开当前活跃的子菜单
            const activeSubmenu = document.querySelector('.menu-item.has-submenu.active');
            if (activeSubmenu) {
                const submenu = activeSubmenu.querySelector('.submenu');
                if (submenu) {
                    submenu.style.display = 'block';
                    activeSubmenu.classList.add('open');
                }
            }
            
            // 子菜单切换功能
            const submenuToggles = document.querySelectorAll('.submenu-toggle');
            submenuToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const parentItem = this.closest('.menu-item.has-submenu');
                    const submenu = parentItem.querySelector('.submenu');
                    
                    if (parentItem.classList.contains('open')) {
                        parentItem.classList.remove('open');
                        submenu.style.display = 'none';
                    } else {
                        // 关闭其他打开的子菜单
                        document.querySelectorAll('.menu-item.has-submenu.open').forEach(item => {
                            if (item !== parentItem) {
                                item.classList.remove('open');
                                item.querySelector('.submenu').style.display = 'none';
                            }
                        });
                        
                        parentItem.classList.add('open');
                        submenu.style.display = 'block';
                    }
                });
            });
        });
    </script>
</body>
</html> 