<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>U币二维码管理 - TeleShop Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* 基础变量定义 */
        :root {
            --primary-color: #007AFF;
            --primary-dark: #0051D5;
            --secondary-color: #5AC8FA;
            --success-color: #34C759;
            --warning-color: #FF9500;
            --error-color: #FF3B30;
            --info-color: #5856D6;
            
            --bg-color: #F8F9FA;
            --surface-color: #FFFFFF;
            --card-color: #FFFFFF;
            --border-color: #E5E5E7;
            --divider-color: #F2F2F7;
            
            --text-primary: #1D1D1F;
            --text-secondary: #6D6D80;
            --text-tertiary: #8E8E93;
            --text-disabled: #C7C7CC;
            
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
            
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            
            --space-xs: 4px;
            --space-sm: 8px;
            --space-md: 16px;
            --space-lg: 24px;
            --space-xl: 32px;
            --space-2xl: 48px;
            
            --transition-fast: 150ms ease;
            --transition-normal: 250ms ease;
            --transition-slow: 350ms ease;
            
            --sidebar-width: 280px;
            --header-height: 80px;
        }
        
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-color);
            overflow-x: hidden;
        }
        
        /* 管理后台容器 */
        .admin-container {
            display: flex;
            min-height: 100vh;
        }
        
        /* 侧边栏样式 */
        .admin-sidebar {
            width: var(--sidebar-width);
            background: var(--surface-color);
            border-right: 1px solid var(--border-color);
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            transition: var(--transition-normal);
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: var(--space-lg);
            border-bottom: 1px solid var(--border-color);
            background: var(--surface-color);
        }
        
        .sidebar-header h2 {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }
        
        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
            padding: var(--space-md) 0;
        }
        
        .menu-section {
            margin-bottom: var(--space-lg);
        }
        
        .menu-section h3 {
            font-size: 11px;
            font-weight: 600;
            color: var(--text-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 0 var(--space-md) var(--space-sm) var(--space-md);
        }
        
        .menu-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .menu-list li {
            margin-bottom: 2px;
        }
        
        .menu-list li.active a {
            background: #eff6ff;
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }
        
        .menu-list a {
            display: flex;
            align-items: center;
            padding: var(--space-sm) var(--space-md);
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: var(--transition-fast);
            border-right: 3px solid transparent;
            cursor: pointer;
        }
        
        .menu-list a:hover {
            background: var(--bg-color);
            color: var(--text-primary);
        }
        
        .menu-list a i {
            width: 16px;
            margin-right: var(--space-sm);
            font-size: 14px;
            flex-shrink: 0;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: var(--space-lg);
            min-height: 100vh;
            background: var(--bg-color);
        }
        
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-md);
            border-bottom: 1px solid var(--border-color);
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: var(--space-md);
        }
        
        .main-header h1 {
            color: var(--text-primary);
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }
        
        .header-actions {
            display: flex;
            gap: var(--space-sm);
        }
        
        .sidebar-toggle {
            display: none;
            padding: 8px 12px;
            border: none;
            background: #f8f9fa;
            color: #495057;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .sidebar-toggle:hover {
            background: #e9ecef;
        }
        
        /* 按钮样式 */
        .btn {
            padding: var(--space-sm) var(--space-md);
            border: none;
            border-radius: var(--radius-md);
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-fast);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--space-xs);
            line-height: 1.5;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
        }
        
        .btn-success {
            background: var(--success-color);
            color: white;
        }
        
        .btn-success:hover {
            background: #28a745;
        }
        
        .btn-danger {
            background: var(--error-color);
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc3545;
        }
        
        .btn-secondary {
            background: var(--text-secondary);
            color: white;
        }
        
        .btn-secondary:hover {
            background: var(--text-primary);
        }
        
        .btn-sm {
            padding: var(--space-xs) var(--space-sm);
            font-size: 12px;
        }
        
        /* 网格布局 */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -var(--space-sm);
        }
        
        .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 var(--space-sm);
        }
        
        /* 统计概览 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
        }
        
        .stat-card {
            background: var(--surface-color);
            padding: var(--space-md);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            text-align: center;
            border: 1px solid var(--border-color);
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--space-xs);
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 13px;
            margin-bottom: var(--space-xs);
        }
        
        .stat-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-xs);
        }
        
        .stat-change.positive {
            color: var(--success-color);
        }
        
        .stat-change.negative {
            color: var(--error-color);
        }
        
        /* 二维码管理区域 */
        .qr-management {
            background: var(--surface-color);
            padding: var(--space-lg);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }
        
        .qr-card {
            background: var(--surface-color);
            padding: var(--space-md);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            margin-bottom: var(--space-md);
            border: 1px solid var(--border-color);
        }
        
        .qr-card h3 {
            color: var(--text-primary);
            margin-bottom: var(--space-md);
            font-size: 16px;
            font-weight: 600;
        }
        
        .qr-display {
            text-align: center;
            padding: var(--space-md);
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-md);
            margin-bottom: var(--space-md);
            background: var(--bg-color);
        }
        
        .qr-code {
            width: 160px;
            height: 160px;
            margin: 0 auto var(--space-sm);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--surface-color);
        }
        
        .qr-code img {
            max-width: 100%;
            max-height: 100%;
        }
        
        .platform-address {
            background: var(--bg-color);
            padding: var(--space-sm);
            border-radius: var(--radius-md);
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            word-break: break-all;
            border: 1px solid var(--border-color);
            margin-bottom: var(--space-sm);
            font-size: 12px;
            color: var(--text-primary);
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: var(--space-md);
        }
        
        .form-group label {
            display: block;
            margin-bottom: var(--space-xs);
            color: var(--text-primary);
            font-weight: 500;
            font-size: 13px;
        }
        
        .form-control {
            width: 100%;
            padding: var(--space-sm) var(--space-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 13px;
            transition: var(--transition-fast);
            background: var(--surface-color);
            color: var(--text-primary);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }
        
        .form-actions {
            display: flex;
            gap: var(--space-sm);
            margin-top: var(--space-md);
        }
        
        /* 上传区域 */
        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--space-lg);
            text-align: center;
            background: var(--bg-color);
            cursor: pointer;
            transition: var(--transition-fast);
        }
        
        .upload-area:hover {
            border-color: var(--primary-color);
            background: #f0f8ff;
        }
        
        .upload-area.dragover {
            border-color: var(--primary-color);
            background: #f0f8ff;
        }
        
        .upload-area i {
            color: var(--primary-color);
            margin-bottom: var(--space-sm);
        }
        
        .upload-area p {
            margin-bottom: var(--space-xs);
            color: var(--text-primary);
            font-size: 13px;
        }
        
        .upload-area small {
            color: var(--text-secondary);
            font-size: 12px;
        }
        
        /* 待确认列表 */
        .pending-list {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }
        
        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-md) var(--space-lg);
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-color);
        }
        
        .list-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .pending-item {
            display: flex;
            align-items: center;
            padding: var(--space-md) var(--space-lg);
            border-bottom: 1px solid var(--divider-color);
            transition: var(--transition-fast);
        }
        
        .pending-item:last-child {
            border-bottom: none;
        }
        
        .pending-item:hover {
            background: var(--bg-color);
        }
        
        .pending-info {
            flex: 1;
        }
        
        .pending-amount {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }
        
        .pending-details {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            color: var(--text-secondary);
            font-size: 13px;
        }
        
        .pending-time {
            color: var(--text-tertiary);
            font-size: 12px;
        }
        
        .pending-status {
            margin-right: var(--space-md);
        }
        
        .status-badge {
            display: inline-block;
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            min-width: 60px;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-confirmed {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .pending-actions {
            display: flex;
            gap: var(--space-xs);
        }
        
        /* 状态徽章 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .text-success {
            color: #28a745;
        }
        
        .text-danger {
            color: #dc3545;
        }
        
        /* 加载状态 */
        .loading {
            position: relative;
            pointer-events: none;
            opacity: 0.6;
        }
        
        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        /* 改进的通知样式 */
        .notification {
            position: fixed;
            top: var(--space-lg);
            right: var(--space-lg);
            padding: var(--space-md);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            min-width: 300px;
            animation: slideIn 0.3s ease;
        }
        
        .notification.success {
            background: var(--success-color);
            color: white;
        }
        
        .notification.error {
            background: var(--error-color);
            color: white;
        }
        
        .notification.info {
            background: var(--info-color);
            color: white;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        /* 改进的表单验证 */
        .form-control.error {
            border-color: var(--error-color);
            box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1);
        }
        
        .form-control.success {
            border-color: var(--success-color);
            box-shadow: 0 0 0 3px rgba(52, 199, 89, 0.1);
        }
        
        .error-message {
            color: var(--error-color);
            font-size: 12px;
            margin-top: 5px;
        }
        
        /* 改进的二维码显示 */
        .qr-code {
            width: 200px;
            height: 200px;
            margin: 0 auto 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }
        
        /* 改进的统计卡片 */
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.12);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 8px;
            line-height: 1;
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
        }
        
        .stat-change {
            font-size: 12px;
            margin-top: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .stat-change.positive {
            color: var(--success-color);
        }
        
        .stat-change.negative {
            color: var(--error-color);
        }
        
        /* 改进的操作按钮 */
        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .action-buttons .btn {
            flex: 1;
            min-width: 120px;
            justify-content: center;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                margin-left: 0;
                padding: 15px;
            }
            
            .admin-sidebar {
                position: fixed;
                left: -280px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .admin-sidebar.show {
                left: 0;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                z-index: 999;
                display: none;
                transition: opacity 0.3s ease;
            }
            
            .sidebar-overlay.show {
                display: block;
                opacity: 1;
            }
            
            .sidebar-toggle {
                display: block;
            }
        }
        
        @media (max-width: 768px) {
            .main-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            
            .header-actions {
                width: 100%;
                justify-content: flex-start;
            }
            
            .col-md-6 {
                flex: 0 0 100%;
                max-width: 100%;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .pending-item {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--space-sm);
            }
            
            .pending-actions {
                width: 100%;
                justify-content: flex-end;
            }
            
            .action-buttons .btn {
                min-width: 100px;
            }
            
            .admin-sidebar {
                width: 100%;
                position: fixed;
                left: -100%;
                transition: left var(--transition-normal);
                z-index: 1000;
            }
            
            .admin-sidebar.active {
                left: 0;
            }
            
            .main-content {
                margin-left: 0;
                padding: var(--space-md);
            }
            
            .sidebar-toggle {
                display: block;
            }
        }
        
        @media (max-width: 480px) {
            .main-content {
                padding: 10px;
            }
            
            .qr-card {
                padding: 15px;
            }
            
            .stat-card {
                padding: 20px;
            }
            
            .stat-value {
                font-size: 24px;
            }
            
            .qr-code {
                width: 150px;
                height: 150px;
            }
        }
        
        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background: var(--bg-color);
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-tertiary);
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-qrcode"></i> U币二维码管理</h2>
            </div>
            <nav class="sidebar-nav">
                <div class="menu-section">
                    <h3>核心功能</h3>
                    <ul class="menu-list">
                        <li><a href="../dashboard/index.html"><i class="fas fa-tachometer-alt"></i> 仪表板总览</a></li>
                        <li><a href="../user-management/index.html"><i class="fas fa-users"></i> 用户管理</a></li>
                        <li><a href="../product-management/index.html"><i class="fas fa-box"></i> 商品管理</a></li>
                        <li><a href="../order-management/index.html"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>业务管理</h3>
                    <ul class="menu-list">
                        <li><a href="../content-management/chat-management.html"><i class="fas fa-comments"></i> 内容管理</a></li>
                        <li><a href="../merchant-management/index.html"><i class="fas fa-store"></i> 商户管理</a></li>
                        <li><a href="../financial-management/index.html"><i class="fas fa-chart-line"></i> 财务管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>钱包管理</h3>
                    <ul class="menu-list">
                        <li><a href="user-wallet-management.html"><i class="fas fa-wallet"></i> 用户钱包管理</a></li>
                        <li><a href="transaction-management.html"><i class="fas fa-exchange-alt"></i> 交易管理</a></li>
                        <li><a href="deposit-withdrawal.html"><i class="fas fa-money-bill-wave"></i> 充值提现管理</a></li>
                        <li><a href="risk-control.html"><i class="fas fa-shield-alt"></i> 风控管理</a></li>
                        <li><a href="digital-currency.html"><i class="fas fa-coins"></i> 数字货币管理</a></li>
                        <li class="active"><a href="ucoin-qr-management.html"><i class="fas fa-qrcode"></i> U币二维码管理</a></li>
                        <li><a href="financial-reconciliation.html"><i class="fas fa-calculator"></i> 财务对账</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>权限管理</h3>
                    <ul class="menu-list">
                        <li><a href="../permission-management/admin-permissions.html"><i class="fas fa-user-shield"></i> 管理员权限</a></li>
                        <li><a href="../permission-management/api-permissions.html"><i class="fas fa-key"></i> API权限管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>系统设置</h3>
                    <ul class="menu-list">
                        <li><a href="../system-settings/index.html"><i class="fas fa-cog"></i> 系统配置</a></li>
                        <li><a href="../reports-analytics/index.html"><i class="fas fa-chart-bar"></i> 数据分析</a></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="main-header">
                <h1>U币二维码管理</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="generateNewQR()">
                        <i class="fas fa-plus"></i> 生成新二维码
                    </button>
                    <button class="btn btn-success" onclick="exportQRData()">
                        <i class="fas fa-download"></i> 导出数据
                    </button>
                </div>
            </div>

            <!-- 统计概览 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="pendingCount">25</div>
                    <div class="stat-label">待确认充值</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+5 较昨日</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="todayAmount">¥45,680</div>
                    <div class="stat-label">今日充值总额</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+12.5%</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="todayCount">156</div>
                    <div class="stat-label">今日充值笔数</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+8.3%</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="successRate">98.5%</div>
                    <div class="stat-label">充值成功率</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+0.3%</span>
                    </div>
                </div>
            </div>

            <!-- 二维码管理区域 -->
            <div class="qr-management">
                <div class="row">
                    <div class="col-md-6">
                        <div class="qr-card">
                            <h3>当前充值二维码</h3>
                            <div class="qr-display">
                                <div class="qr-code" id="currentQRCode">
                                    <div style="background: #007bff; color: white; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                                        <div style="text-align: center;">
                                            <i class="fas fa-qrcode fa-3x" style="margin-bottom: 10px;"></i>
                                            <p style="font-size: 12px; margin: 0;">U币充值二维码</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="platform-address" id="platformAddress">
                                    UC3qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh
                                </div>
                                <div class="form-actions">
                                    <button class="btn btn-primary" onclick="copyPlatformAddress()">
                                        <i class="fas fa-copy"></i> 复制地址
                                    </button>
                                    <button class="btn btn-success" onclick="downloadQRCode()">
                                        <i class="fas fa-download"></i> 下载
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="qr-card">
                            <h3>二维码设置</h3>
                            <div class="form-group">
                                <label>平台U币地址</label>
                                <input type="text" id="platformAddressInput" class="form-control" 
                                       value="UC3qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh">
                            </div>
                            <div class="form-group">
                                <label>二维码标题</label>
                                <input type="text" id="qrTitle" class="form-control" 
                                       value="TeleShop U币充值" 
                                       placeholder="二维码标题">
                            </div>
                            <div class="form-group">
                                <label>最小充值金额</label>
                                <input type="number" id="minAmount" class="form-control" 
                                       value="10" min="1" step="1">
                            </div>
                            <div class="form-group">
                                <label>自定义二维码上传</label>
                                <div class="upload-area" onclick="document.getElementById('qrUpload').click()">
                                    <i class="fas fa-cloud-upload-alt fa-2x"></i>
                                    <p>点击上传自定义二维码</p>
                                    <small>支持PNG、JPG格式，建议尺寸200x200px</small>
                                </div>
                                <input type="file" id="qrUpload" style="display: none;" accept="image/*" onchange="handleQRUpload(event)">
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="updateQRSettings()">
                                    <i class="fas fa-save"></i> 保存设置
                                </button>
                                <button class="btn btn-secondary" onclick="resetQRSettings()">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 待确认充值列表 -->
            <div class="pending-list">
                <div class="list-header">
                    <h3>待确认充值列表</h3>
                    <div class="header-actions">
                        <button class="btn btn-sm btn-primary" onclick="refreshPendingList()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>
                <div id="pendingDepositsList">
                    <!-- 待确认充值项目 -->
                    <div class="pending-item">
                        <div class="pending-info">
                            <div class="pending-amount">150.5 UC</div>
                            <div class="pending-details">
                                <span>用户：张三 (ID: 1001)</span>
                                <div class="pending-time">2024-01-15 14:30:25</div>
                            </div>
                        </div>
                        <div class="pending-status">
                            <span class="status-badge status-pending">待确认</span>
                        </div>
                        <div class="pending-actions">
                            <button class="btn btn-sm btn-success" onclick="confirmDeposit(1, 150.5)">
                                <i class="fas fa-check"></i> 确认
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="rejectDeposit(1)">
                                <i class="fas fa-times"></i> 拒绝
                            </button>
                        </div>
                    </div>
                    
                    <div class="pending-item">
                        <div class="pending-info">
                            <div class="pending-amount">280.0 UC</div>
                            <div class="pending-details">
                                <span>用户：李四 (ID: 1002)</span>
                                <div class="pending-time">2024-01-15 15:45:12</div>
                            </div>
                        </div>
                        <div class="pending-status">
                            <span class="status-badge status-pending">待确认</span>
                        </div>
                        <div class="pending-actions">
                            <button class="btn btn-sm btn-success" onclick="confirmDeposit(2, 280.0)">
                                <i class="fas fa-check"></i> 确认
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="rejectDeposit(2)">
                                <i class="fas fa-times"></i> 拒绝
                            </button>
                        </div>
                    </div>
                    
                    <div class="pending-item">
                        <div class="pending-info">
                            <div class="pending-amount">50.0 UC</div>
                            <div class="pending-details">
                                <span>用户：王五 (ID: 1003)</span>
                                <div class="pending-time">2024-01-15 16:20:08</div>
                            </div>
                        </div>
                        <div class="pending-status">
                            <span class="status-badge status-pending">待确认</span>
                        </div>
                        <div class="pending-actions">
                            <button class="btn btn-sm btn-success" onclick="confirmDeposit(3, 50.0)">
                                <i class="fas fa-check"></i> 确认
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="rejectDeposit(3)">
                                <i class="fas fa-times"></i> 拒绝
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 二维码管理相关函数
        function generateNewQR() {
            const address = document.getElementById('platformAddressInput').value;
            const title = document.getElementById('qrTitle').value;
            
            if (!validateAddress(address)) {
                showNotification('请输入有效的平台U币地址', 'danger');
                return;
            }
            
            // 显示加载状态
            const generateBtn = event.target;
            generateBtn.classList.add('loading');
            generateBtn.disabled = true;
            
            // 模拟生成二维码
            setTimeout(() => {
                const qrCodeElement = document.getElementById('currentQRCode');
                qrCodeElement.innerHTML = `
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                        <div style="text-align: center;">
                            <i class="fas fa-qrcode fa-3x" style="margin-bottom: 10px;"></i>
                            <p style="font-size: 12px; margin: 0; font-weight: 500;">${title}</p>
                            <p style="font-size: 10px; margin: 0; opacity: 0.8;">UC充值二维码</p>
                        </div>
                    </div>
                `;
                
                generateBtn.classList.remove('loading');
                generateBtn.disabled = false;
                showNotification('二维码生成成功！', 'success');
                
                // 更新统计数据
                updateQRStats();
            }, 1500);
        }
        
        function validateAddress(address) {
            // 简单的地址验证
            return address && address.length >= 26 && address.length <= 50;
        }
        
        function updateQRStats() {
            // 模拟更新统计数据
            const stats = {
                pendingCount: Math.floor(Math.random() * 50) + 20,
                todayAmount: (Math.random() * 100000 + 20000).toFixed(0),
                todayCount: Math.floor(Math.random() * 200) + 100,
                successRate: (Math.random() * 5 + 95).toFixed(1)
            };
            
            document.getElementById('pendingCount').textContent = stats.pendingCount;
            document.getElementById('todayAmount').textContent = '¥' + stats.todayAmount;
            document.getElementById('todayCount').textContent = stats.todayCount;
            document.getElementById('successRate').textContent = stats.successRate + '%';
        }
        
        function copyPlatformAddress() {
            const address = document.getElementById('platformAddress').textContent;
            const copyBtn = event.target;
            
            // 显示加载状态
            copyBtn.classList.add('loading');
            copyBtn.disabled = true;
            
            navigator.clipboard.writeText(address).then(() => {
                copyBtn.classList.remove('loading');
                copyBtn.disabled = false;
                showNotification('平台地址已复制到剪贴板', 'success');
                
                // 视觉反馈
                copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                setTimeout(() => {
                    copyBtn.innerHTML = '<i class="fas fa-copy"></i> 复制地址';
                }, 2000);
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = address;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                copyBtn.classList.remove('loading');
                copyBtn.disabled = false;
                showNotification('平台地址已复制到剪贴板', 'success');
                
                copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                setTimeout(() => {
                    copyBtn.innerHTML = '<i class="fas fa-copy"></i> 复制地址';
                }, 2000);
            });
        }
        
        function downloadQRCode() {
            // 模拟下载二维码
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            
            // 绘制紫色背景
            ctx.fillStyle = '#9333ea';
            ctx.fillRect(0, 0, 200, 200);
            
            // 绘制二维码图案（简化版）
            ctx.fillStyle = '#ffffff';
            for (let i = 0; i < 20; i++) {
                for (let j = 0; j < 20; j++) {
                    if ((i + j) % 3 === 0) {
                        ctx.fillRect(i * 10, j * 10, 8, 8);
                    }
                }
            }
            
            // 下载
            const link = document.createElement('a');
            link.download = 'teleshop-ucoin-qr.png';
            link.href = canvas.toDataURL();
            link.click();
            
            showNotification('二维码已下载', 'success');
        }
        
        function handleQRUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const qrCodeElement = document.getElementById('currentQRCode');
                qrCodeElement.innerHTML = `<img src="${e.target.result}" alt="自定义二维码" style="width: 100%; height: 100%; object-fit: contain;">`;
                showNotification('二维码上传成功', 'success');
            };
            reader.readAsDataURL(file);
        }
        
        function updateQRSettings() {
            const address = document.getElementById('platformAddressInput').value;
            const title = document.getElementById('qrTitle').value;
            const minAmount = document.getElementById('minAmount').value;
            
            if (!address || !title || !minAmount) {
                alert('请填写所有必填字段');
                return;
            }
            
            // 更新显示的地址
            document.getElementById('platformAddress').textContent = address;
            
            // 模拟保存设置
            setTimeout(() => {
                showNotification('二维码设置已保存', 'success');
            }, 500);
        }
        
        function resetQRSettings() {
            document.getElementById('platformAddressInput').value = 'UC3qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh';
            document.getElementById('qrTitle').value = 'TeleShop U币充值';
            document.getElementById('minAmount').value = '10';
            showNotification('设置已重置', 'info');
        }
        
        function refreshPendingList() {
            showNotification('正在刷新待确认充值列表...', 'info');
            
            // 模拟刷新
            setTimeout(() => {
                showNotification('列表已刷新', 'success');
            }, 1000);
        }
        
        function confirmDeposit(depositId, amount) {
            if (!confirm(`确定要确认这笔 ${amount} UC 的充值吗？`)) {
                return;
            }
            
            showNotification('正在确认充值...', 'info');
            
            setTimeout(() => {
                // 更新状态
                const pendingItem = event.target.closest('.pending-item');
                const statusElement = pendingItem.querySelector('.status-badge');
                const actionsElement = pendingItem.querySelector('.pending-actions');
                
                statusElement.textContent = '已确认';
                statusElement.className = 'status-badge status-confirmed';
                actionsElement.innerHTML = '<span class="text-success"><i class="fas fa-check-circle"></i> 已确认</span>';
                
                showNotification(`充值确认成功！用户钱包已增加 ${amount} UC`, 'success');
                
                // 模拟发送前台通知
                sendDepositNotification(depositId, amount);
            }, 1500);
        }
        
        function rejectDeposit(depositId) {
            const reason = prompt('请输入拒绝原因：');
            if (!reason) return;
            
            showNotification('正在处理拒绝...', 'info');
            
            setTimeout(() => {
                // 更新状态
                const pendingItem = event.target.closest('.pending-item');
                const statusElement = pendingItem.querySelector('.status-badge');
                const actionsElement = pendingItem.querySelector('.pending-actions');
                
                statusElement.textContent = '已拒绝';
                statusElement.className = 'status-badge status-rejected';
                actionsElement.innerHTML = '<span class="text-danger"><i class="fas fa-times-circle"></i> 已拒绝</span>';
                
                showNotification(`充值已拒绝：${reason}`, 'warning');
            }, 1000);
        }
        
        function sendDepositNotification(depositId, amount) {
            console.log(`发送充值通知: 存款ID ${depositId}, 金额 ${amount} UC`);
            // 这里可以集成WebSocket或其他实时通信机制
        }
        
        function exportQRData() {
            const data = {
                address: document.getElementById('platformAddressInput').value,
                title: document.getElementById('qrTitle').value,
                minAmount: document.getElementById('minAmount').value,
                exportTime: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = 'ucoin-qr-settings.json';
            link.href = url;
            link.click();
            
            showNotification('二维码数据已导出', 'success');
        }
        
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            
            let icon, bgColor, textColor;
            switch (type) {
                case 'success':
                    icon = 'fas fa-check-circle';
                    bgColor = '#28a745';
                    textColor = 'white';
                    break;
                case 'warning':
                    icon = 'fas fa-exclamation-triangle';
                    bgColor = '#ffc107';
                    textColor = '#212529';
                    break;
                case 'danger':
                    icon = 'fas fa-times-circle';
                    bgColor = '#dc3545';
                    textColor = 'white';
                    break;
                default:
                    icon = 'fas fa-info-circle';
                    bgColor = '#007bff';
                    textColor = 'white';
            }
            
            notification.innerHTML = `
                <i class="${icon}"></i>
                <span>${message}</span>
            `;
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                padding: 16px 24px;
                border-radius: 8px;
                color: ${textColor};
                font-weight: 500;
                box-shadow: 0 6px 16px rgba(0,0,0,0.2);
                transform: translateX(100%);
                transition: transform 0.3s ease;
                background-color: ${bgColor};
                max-width: 350px;
                word-wrap: break-word;
                display: flex;
                align-items: center;
                gap: 8px;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动生成初始二维码
            generateNewQR();
            
            // 设置拖拽上传
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    document.getElementById('qrUpload').files = files;
                    handleQRUpload({ target: { files: files } });
                }
            });
            
        });
    </script>
</body>
</html> 