<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务对账 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="wallet-management-fixes.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .reconcile-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .overview-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .overview-card h3 {
            color: #666;
            font-size: 14px;
            margin: 0 0 10px 0;
        }
        
        .overview-card .value {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .reconcile-tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .tab-header {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            overflow-x: auto;
        }
        
        .tab-button {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            white-space: nowrap;
        }
        
        .tab-button.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: #f8f9fa;
        }
        
        .tab-content {
            padding: 20px;
        }
        
        .daily-reconcile {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .reconcile-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-success { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-danger { background: #f8d7da; color: #721c24; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        
        .reconcile-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .summary-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .summary-item.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .summary-item.warning {
            border-left-color: #ffc107;
            background: #fffbf0;
        }
        
        .summary-item h5 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .summary-item .amount {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        
        .summary-item.error .amount {
            color: #dc3545;
        }
        
        .summary-item.warning .amount {
            color: #ffc107;
        }
        
        .reconcile-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .reconcile-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
        }
        
        .reconcile-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .difference {
            font-weight: bold;
        }
        
        .difference.positive {
            color: #28a745;
        }
        
        .difference.negative {
            color: #dc3545;
        }
        
        .difference.zero {
            color: #6c757d;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        
        .error-list {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .error-item {
            padding: 15px;
            border-left: 4px solid #dc3545;
            background: #fff5f5;
            margin-bottom: 10px;
            border-radius: 0 4px 4px 0;
        }
        
        .error-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .error-title {
            font-weight: bold;
            color: #dc3545;
        }
        
        .error-time {
            color: #666;
            font-size: 12px;
        }
        
        .error-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .reconcile-controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .reconcile-summary {
                grid-template-columns: 1fr;
            }
            
            .reconcile-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .reconcile-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-calculator"></i> 财务对账</h2>
            </div>
            <nav class="sidebar-nav">
                <div class="menu-section">
                    <h3>核心功能</h3>
                    <ul class="menu-list">
                        <li><a href="../dashboard/index.html"><i class="fas fa-tachometer-alt"></i> 仪表板总览</a></li>
                        <li><a href="../user-management/index.html"><i class="fas fa-users"></i> 用户管理</a></li>
                        <li><a href="../product-management/index.html"><i class="fas fa-box"></i> 商品管理</a></li>
                        <li><a href="../order-management/index.html"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>业务管理</h3>
                    <ul class="menu-list">
                        <li><a href="../content-management/chat-management.html"><i class="fas fa-comments"></i> 内容管理</a></li>
                        <li><a href="../merchant-management/index.html"><i class="fas fa-store"></i> 商户管理</a></li>
                        <li><a href="../financial-management/index.html"><i class="fas fa-chart-line"></i> 财务管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>钱包管理</h3>
                    <ul class="menu-list">
                        <li><a href="user-wallet-management.html"><i class="fas fa-wallet"></i> 用户钱包管理</a></li>
                        <li><a href="transaction-management.html"><i class="fas fa-exchange-alt"></i> 交易管理</a></li>
                        <li><a href="deposit-withdrawal.html"><i class="fas fa-money-bill-wave"></i> 充值提现管理</a></li>
                        <li><a href="risk-control.html"><i class="fas fa-shield-alt"></i> 风控管理</a></li>
                        <li><a href="digital-currency.html"><i class="fas fa-coins"></i> 数字货币管理</a></li>
                        <li class="active"><a href="financial-reconciliation.html"><i class="fas fa-calculator"></i> 财务对账</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>权限管理</h3>
                    <ul class="menu-list">
                        <li><a href="../permission-management/admin-permissions.html"><i class="fas fa-user-shield"></i> 管理员权限</a></li>
                        <li><a href="../permission-management/api-permissions.html"><i class="fas fa-key"></i> API权限管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>系统设置</h3>
                    <ul class="menu-list">
                        <li><a href="../system-settings/index.html"><i class="fas fa-cog"></i> 系统配置</a></li>
                        <li><a href="../reports-analytics/index.html"><i class="fas fa-chart-bar"></i> 数据分析</a></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="main-header">
                <h1>财务对账</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="startReconciliation()">开始对账</button>
                    <button class="btn btn-success" onclick="exportReconcileReport()">导出报告</button>
                </div>
            </div>

            <!-- 对账概览 -->
            <div class="reconcile-overview">
                <div class="overview-card">
                    <h3>今日对账状态</h3>
                    <p class="value" style="color: #28a745;">已完成</p>
                </div>
                <div class="overview-card">
                    <h3>系统余额</h3>
                    <p class="value">¥12,458,963</p>
                </div>
                <div class="overview-card">
                    <h3>银行余额</h3>
                    <p class="value">¥12,458,963</p>
                </div>
                <div class="overview-card">
                    <h3>差额</h3>
                    <p class="value" style="color: #28a745;">¥0</p>
                </div>
                <div class="overview-card">
                    <h3>待处理差错</h3>
                    <p class="value" style="color: #dc3545;">3</p>
                </div>
                <div class="overview-card">
                    <h3>对账成功率</h3>
                    <p class="value" style="color: #28a745;">99.8%</p>
                </div>
            </div>

            <!-- 日终对账状态 -->
            <div class="daily-reconcile">
                <div class="reconcile-status">
                    <h4>2024-01-15 日终对账</h4>
                    <span class="status-badge status-success">✓ 对账完成</span>
                </div>
                
                <div class="reconcile-summary">
                    <div class="summary-item">
                        <h5>系统总收入</h5>
                        <div class="amount">¥256,789.50</div>
                    </div>
                    <div class="summary-item">
                        <h5>银行到账金额</h5>
                        <div class="amount">¥256,789.50</div>
                    </div>
                    <div class="summary-item">
                        <h5>系统总支出</h5>
                        <div class="amount">¥189,456.30</div>
                    </div>
                    <div class="summary-item">
                        <h5>银行支出金额</h5>
                        <div class="amount">¥189,456.30</div>
                    </div>
                    <div class="summary-item">
                        <h5>手续费收入</h5>
                        <div class="amount">¥8,945.20</div>
                    </div>
                    <div class="summary-item error">
                        <h5>差错金额</h5>
                        <div class="amount">¥0.00</div>
                    </div>
                </div>
            </div>

            <!-- 对账管理标签 -->
            <div class="reconcile-tabs">
                <div class="tab-header">
                    <button class="tab-button active" onclick="switchTab('daily')">日终对账</button>
                    <button class="tab-button" onclick="switchTab('banks')">银行对账</button>
                    <button class="tab-button" onclick="switchTab('payments')">第三方支付</button>
                    <button class="tab-button" onclick="switchTab('errors')">差错处理</button>
                    <button class="tab-button" onclick="switchTab('reports')">对账报表</button>
                </div>

                <!-- 日终对账 -->
                <div id="daily-tab" class="tab-content">
                    <!-- 对账控制 -->
                    <div class="reconcile-controls">
                        <div class="form-group" style="margin: 0; width: 150px;">
                            <label>对账日期</label>
                            <input type="date" value="2024-01-15" class="form-control">
                        </div>
                        <button class="btn btn-primary">重新对账</button>
                        <button class="btn btn-info">查看详情</button>
                        <button class="btn btn-warning">手动调整</button>
                    </div>

                    <div class="table-container">
                        <table class="reconcile-table">
                            <thead>
                                <tr>
                                    <th width="12%">日期</th>
                                    <th width="15%">系统金额</th>
                                    <th width="15%">银行金额</th>
                                    <th width="12%">差额</th>
                                    <th width="12%">状态</th>
                                    <th width="15%">对账时间</th>
                                    <th width="19%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-15</td>
                                    <td>¥67,333.20</td>
                                    <td>¥67,333.20</td>
                                    <td class="difference zero">¥0.00</td>
                                    <td><span class="status-badge status-success">平账</span></td>
                                    <td>2024-01-16 00:15</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewDailyDetail('2024-01-15')">详情</button>
                                            <button class="btn-xs btn-success" onclick="exportDaily('2024-01-15')">导出</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2024-01-14</td>
                                    <td>¥85,567.40</td>
                                    <td>¥85,467.40</td>
                                    <td class="difference positive">+¥100.00</td>
                                    <td><span class="status-badge status-warning">差错</span></td>
                                    <td>2024-01-15 00:18</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewDailyDetail('2024-01-14')">详情</button>
                                            <button class="btn-xs btn-warning" onclick="adjustDifference('2024-01-14')">调账</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2024-01-13</td>
                                    <td>¥92,134.60</td>
                                    <td>¥92,134.60</td>
                                    <td class="difference zero">¥0.00</td>
                                    <td><span class="status-badge status-success">平账</span></td>
                                    <td>2024-01-14 00:12</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewDailyDetail('2024-01-13')">详情</button>
                                            <button class="btn-xs btn-success" onclick="exportDaily('2024-01-13')">导出</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 银行对账 -->
                <div id="banks-tab" class="tab-content" style="display: none;">
                    <div class="table-container">
                        <table class="reconcile-table">
                            <thead>
                                <tr>
                                    <th width="15%">银行名称</th>
                                    <th width="20%">账户信息</th>
                                    <th width="15%">系统余额</th>
                                    <th width="15%">银行余额</th>
                                    <th width="10%">差额</th>
                                    <th width="10%">状态</th>
                                    <th width="15%">更新时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>中国银行</strong></td>
                                    <td>
                                        <div>账户: ****5678</div>
                                        <div style="font-size: 12px; color: #666;">主账户</div>
                                    </td>
                                    <td>¥8,456,789.50</td>
                                    <td>¥8,456,789.50</td>
                                    <td class="difference zero">¥0.00</td>
                                    <td><span class="status-badge status-success">正常</span></td>
                                    <td>2024-01-15 23:45</td>
                                </tr>
                                <tr>
                                    <td><strong>招商银行</strong></td>
                                    <td>
                                        <div>账户: ****1234</div>
                                        <div style="font-size: 12px; color: #666;">备用账户</div>
                                    </td>
                                    <td>¥2,567,890.30</td>
                                    <td>¥2,567,790.30</td>
                                    <td class="difference positive">+¥100.00</td>
                                    <td><span class="status-badge status-warning">差异</span></td>
                                    <td>2024-01-15 23:40</td>
                                </tr>
                                <tr>
                                    <td><strong>工商银行</strong></td>
                                    <td>
                                        <div>账户: ****9876</div>
                                        <div style="font-size: 12px; color: #666;">收款账户</div>
                                    </td>
                                    <td>¥1,434,283.70</td>
                                    <td>¥1,434,283.70</td>
                                    <td class="difference zero">¥0.00</td>
                                    <td><span class="status-badge status-success">正常</span></td>
                                    <td>2024-01-15 23:42</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 第三方支付对账 -->
                <div id="payments-tab" class="tab-content" style="display: none;">
                    <div class="table-container">
                        <table class="reconcile-table">
                            <thead>
                                <tr>
                                    <th width="15%">支付渠道</th>
                                    <th width="15%">商户号</th>
                                    <th width="12%">系统交易量</th>
                                    <th width="12%">渠道交易量</th>
                                    <th width="10%">差额</th>
                                    <th width="10%">状态</th>
                                    <th width="12%">手续费</th>
                                    <th width="14%">对账时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>支付宝</strong></td>
                                    <td>2088****8888</td>
                                    <td>¥156,789.50</td>
                                    <td>¥156,789.50</td>
                                    <td class="difference zero">¥0.00</td>
                                    <td><span class="status-badge status-success">平账</span></td>
                                    <td>¥940.74</td>
                                    <td>2024-01-15 02:30</td>
                                </tr>
                                <tr>
                                    <td><strong>微信支付</strong></td>
                                    <td>1230****0109</td>
                                    <td>¥89,456.30</td>
                                    <td>¥89,456.30</td>
                                    <td class="difference zero">¥0.00</td>
                                    <td><span class="status-badge status-success">平账</span></td>
                                    <td>¥536.74</td>
                                    <td>2024-01-15 02:25</td>
                                </tr>
                                <tr>
                                    <td><strong>银联支付</strong></td>
                                    <td>8821****2210</td>
                                    <td>¥23,567.80</td>
                                    <td>¥23,467.80</td>
                                    <td class="difference positive">+¥100.00</td>
                                    <td><span class="status-badge status-warning">差异</span></td>
                                    <td>¥188.54</td>
                                    <td>2024-01-15 02:35</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 差错处理 -->
                <div id="errors-tab" class="tab-content" style="display: none;">
                    <div class="error-list">
                        <h4>待处理差错</h4>
                        
                        <div class="error-item">
                            <div class="error-header">
                                <div class="error-title">银行流水差异</div>
                                <div class="error-time">2024-01-15 14:30</div>
                            </div>
                            <div class="error-desc">
                                招商银行账户存在100元差异，系统显示¥2,567,890.30，银行余额¥2,567,790.30
                            </div>
                            <div class="action-buttons">
                                <button class="btn-xs btn-info" onclick="investigateError(1)">调查</button>
                                <button class="btn-xs btn-warning" onclick="adjustError(1)">调账</button>
                                <button class="btn-xs btn-success" onclick="resolveError(1)">处理完成</button>
                            </div>
                        </div>

                        <div class="error-item">
                            <div class="error-header">
                                <div class="error-title">第三方支付差异</div>
                                <div class="error-time">2024-01-15 02:35</div>
                            </div>
                            <div class="error-desc">
                                银联支付渠道交易金额不匹配，系统记录¥23,567.80，渠道对账单¥23,467.80
                            </div>
                            <div class="action-buttons">
                                <button class="btn-xs btn-info" onclick="investigateError(2)">调查</button>
                                <button class="btn-xs btn-warning" onclick="contactChannel(2)">联系渠道</button>
                                <button class="btn-xs btn-danger" onclick="escalateError(2)">升级处理</button>
                            </div>
                        </div>

                        <div class="error-item">
                            <div class="error-header">
                                <div class="error-title">用户退款异常</div>
                                <div class="error-time">2024-01-14 16:20</div>
                            </div>
                            <div class="error-desc">
                                用户ID 1234 的退款金额在系统中已扣除，但银行流水显示退款失败
                            </div>
                            <div class="action-buttons">
                                <button class="btn-xs btn-info" onclick="investigateError(3)">调查</button>
                                <button class="btn-xs btn-warning" onclick="processRefund(3)">重新退款</button>
                                <button class="btn-xs btn-success" onclick="resolveError(3)">处理完成</button>
                            </div>
                        </div>
                    </div>

                    <div class="table-container">
                        <h4>历史差错记录</h4>
                        <table class="reconcile-table">
                            <thead>
                                <tr>
                                    <th width="12%">日期</th>
                                    <th width="15%">差错类型</th>
                                    <th width="12%">差额</th>
                                    <th width="25%">描述</th>
                                    <th width="12%">处理状态</th>
                                    <th width="12%">处理人</th>
                                    <th width="12%">处理时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-14</td>
                                    <td>银行流水</td>
                                    <td class="difference positive">+¥100.00</td>
                                    <td>招商银行差异</td>
                                    <td><span class="status-badge status-info">处理中</span></td>
                                    <td>张会计</td>
                                    <td>2024-01-15 09:30</td>
                                </tr>
                                <tr>
                                    <td>2024-01-13</td>
                                    <td>第三方支付</td>
                                    <td class="difference negative">-¥50.00</td>
                                    <td>支付宝手续费差异</td>
                                    <td><span class="status-badge status-success">已解决</span></td>
                                    <td>李会计</td>
                                    <td>2024-01-14 10:45</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 对账报表 -->
                <div id="reports-tab" class="tab-content" style="display: none;">
                    <div class="chart-container">
                        <h4>对账成功率趋势</h4>
                        <canvas id="reconcileChart"></canvas>
                    </div>
                    
                    <div class="reconcile-summary">
                        <div class="summary-item">
                            <h5>本月平账天数</h5>
                            <div class="amount">14天</div>
                        </div>
                        <div class="summary-item warning">
                            <h5>本月差错天数</h5>
                            <div class="amount">1天</div>
                        </div>
                        <div class="summary-item">
                            <h5>对账成功率</h5>
                            <div class="amount">93.3%</div>
                        </div>
                        <div class="summary-item error">
                            <h5>累计差错金额</h5>
                            <div class="amount">¥100.00</div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 菜单交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化菜单状态
            const menuItems = document.querySelectorAll('.admin-sidebar .menu-list li');
            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && link.getAttribute('href') === '#') {
                    // 如果是父菜单项，添加展开/收起功能
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const submenu = item.querySelector('.submenu');
                        if (submenu) {
                            item.classList.toggle('open');
                            submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
                        }
                    });
                }
            });
        });

        // 切换标签
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            document.getElementById(tabName + '-tab').style.display = 'block';
            event.target.classList.add('active');
            
            if (tabName === 'reports') {
                initReconcileChart();
            }
        }

        // 开始对账
        function startReconciliation() {
            if (confirm('确定要开始今日对账吗？')) {
                alert('对账程序已启动，请等待对账完成...');
            }
        }

        // 查看日终详情
        function viewDailyDetail(date) {
            alert('查看 ' + date + ' 日终对账详情');
        }

        // 调整差额
        function adjustDifference(date) {
            const amount = prompt('请输入调账金额：');
            if (amount) {
                alert('调账申请已提交，金额：¥' + amount);
            }
        }

        // 差错处理功能
        function investigateError(id) {
            alert('开始调查差错 ID: ' + id);
        }

        function adjustError(id) {
            alert('进入调账流程 ID: ' + id);
        }

        function resolveError(id) {
            if (confirm('确定要标记该差错为已解决吗？')) {
                alert('差错已标记为解决');
                location.reload();
            }
        }

        function contactChannel(id) {
            alert('已发送消息给第三方支付渠道');
        }

        function escalateError(id) {
            alert('差错已升级到上级处理');
        }

        function processRefund(id) {
            alert('重新发起退款流程');
        }

        // 导出报告
        function exportReconcileReport() {
            alert('正在导出对账报告...');
        }

        function exportDaily(date) {
            alert('正在导出 ' + date + ' 日终对账报告...');
        }

        // 初始化对账图表
        function initReconcileChart() {
            const ctx = document.getElementById('reconcileChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月1日', '1月3日', '1月5日', '1月7日', '1月9日', '1月11日', '1月13日', '1月15日'],
                    datasets: [{
                        label: '对账成功率 (%)',
                        data: [100, 100, 95, 100, 100, 90, 100, 93.3],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 