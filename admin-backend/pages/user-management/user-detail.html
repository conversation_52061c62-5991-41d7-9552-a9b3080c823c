<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户详情 - TeleShop后台管理</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.2s;
        }

        .user-header-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-avatar-large {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 600;
        }

        .user-basic-info {
            flex: 1;
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .user-meta {
            color: #64748b;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #059669; color: white; }
        .btn-warning { background: #d97706; color: white; }
        .btn-danger { background: #dc2626; color: white; }
        .btn-ghost { background: transparent; color: #64748b; border: 2px solid #e2e8f0; }

        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .detail-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            background: #f8fafc;
            padding: 16px 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .card-body {
            padding: 20px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #64748b;
            font-size: 14px;
        }

        .info-value {
            color: #1e293b;
            font-weight: 500;
            font-size: 14px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.active { background: #dcfce7; color: #166534; }
        .status-badge.inactive { background: #fee2e2; color: #991b1b; }
        .status-badge.pending { background: #fef3c7; color: #92400e; }

        .tags-list {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .tag {
            padding: 4px 8px;
            background: #f1f5f9;
            color: #64748b;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .tag.vip { background: #fef3c7; color: #92400e; }
        .tag.new { background: #dcfce7; color: #166534; }

        /* 统计数据 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 16px;
        }

        .stat-item {
            text-align: center;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
        }

        /* 图表 */
        .chart-wrapper {
            position: relative;
            height: 200px;
            margin-top: 16px;
        }

        /* 全宽卡片 */
        .full-width-card {
            grid-column: 1 / -1;
        }

        /* 表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .order-id {
            color: #3b82f6;
            text-decoration: none;
        }

        .order-id:hover {
            text-decoration: underline;
        }

        .amount {
            font-weight: 600;
            color: #059669;
        }

        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .detail-grid {
                grid-template-columns: 1fr 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .detail-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .page-header {
                flex-direction: column;
                gap: 16px;
            }

            .user-header-info {
                width: 100%;
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="user-header-info">
                <a href="index.html" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    返回用户列表
                </a>
                <div class="user-avatar-large">张</div>
                <div class="user-basic-info">
                    <h1 class="page-title">张小明</h1>
                    <div class="user-meta">ID: 10001 | 注册时间: 2024-01-10 | 最后登录: 2小时前</div>
                </div>
            </div>
            <div style="display: flex; gap: 12px;">
                <button class="btn btn-ghost">
                    <i class="fas fa-message"></i>
                    发送消息
                </button>
                <button class="btn btn-warning">
                    <i class="fas fa-edit"></i>
                    编辑用户
                </button>
                <button class="btn btn-danger">
                    <i class="fas fa-ban"></i>
                    封禁用户
                </button>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="action-buttons">
            <button class="btn btn-success">
                <i class="fas fa-unlock"></i>
                解封用户
            </button>
            <button class="btn btn-primary">
                <i class="fas fa-gift"></i>
                发放优惠券
            </button>
            <button class="btn btn-ghost">
                <i class="fas fa-history"></i>
                查看日志
            </button>
            <button class="btn btn-ghost">
                <i class="fas fa-download"></i>
                导出数据
            </button>
        </div>

        <div class="detail-grid">
            <!-- 基本信息 -->
            <div class="detail-card">
                <div class="card-header">
                    <h3 class="card-title">基本信息</h3>
                    <i class="fas fa-user"></i>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <span class="info-label">用户姓名</span>
                        <span class="info-value">张小明</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">邮箱地址</span>
                        <span class="info-value"><EMAIL></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">手机号码</span>
                        <span class="info-value">138****5678</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">性别</span>
                        <span class="info-value">男</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">生日</span>
                        <span class="info-value">1990-01-01</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">所在城市</span>
                        <span class="info-value">北京</span>
                    </div>
                </div>
            </div>

            <!-- 账户状态 -->
            <div class="detail-card">
                <div class="card-header">
                    <h3 class="card-title">账户状态</h3>
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <span class="info-label">用户状态</span>
                        <span class="status-badge active">正常</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">会员等级</span>
                        <span class="info-value">VIP黄金</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">认证状态</span>
                        <span class="status-badge active">已认证</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">风险等级</span>
                        <span class="status-badge active">低风险</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">用户标签</span>
                        <div class="tags-list">
                            <span class="tag vip">VIP</span>
                            <span class="tag">高频</span>
                            <span class="tag new">活跃</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消费统计 -->
            <div class="detail-card">
                <div class="card-header">
                    <h3 class="card-title">消费统计</h3>
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="card-body">
                    <div class="stats-grid" style="grid-template-columns: 1fr 1fr;">
                        <div class="stat-item">
                            <div class="stat-value">23</div>
                            <div class="stat-label">总订单</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥15.6K</div>
                            <div class="stat-label">消费总额</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥682</div>
                            <div class="stat-label">平均订单</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">2</div>
                            <div class="stat-label">退款次数</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行为分析 -->
            <div class="detail-card full-width-card">
                <div class="card-header">
                    <h3 class="card-title">行为分析</h3>
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="card-body">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value">156</div>
                            <div class="stat-label">登录次数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">15分钟</div>
                            <div class="stat-label">平均在线</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">34</div>
                            <div class="stat-label">收藏商品</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">12</div>
                            <div class="stat-label">分享次数</div>
                        </div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="behaviorChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 最近订单 -->
            <div class="detail-card full-width-card">
                <div class="card-header">
                    <h3 class="card-title">最近订单</h3>
                    <a href="#" class="btn btn-ghost" style="padding: 6px 12px; font-size: 12px;">查看全部</a>
                </div>
                <div class="card-body" style="padding: 0;">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>订单ID</th>
                                <th>商品</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>下单时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><a href="#" class="order-id">#ORD20240115001</a></td>
                                <td>iPhone 15 Pro Max</td>
                                <td><span class="amount">¥9,999</span></td>
                                <td><span class="status-badge active">已完成</span></td>
                                <td>2024-01-15 14:30</td>
                                <td>
                                    <button class="btn btn-ghost" style="padding: 4px 8px; font-size: 12px;">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td><a href="#" class="order-id">#ORD20240110002</a></td>
                                <td>Nike Air Max 270</td>
                                <td><span class="amount">¥899</span></td>
                                <td><span class="status-badge active">已完成</span></td>
                                <td>2024-01-10 16:20</td>
                                <td>
                                    <button class="btn btn-ghost" style="padding: 4px 8px; font-size: 12px;">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td><a href="#" class="order-id">#ORD20240105003</a></td>
                                <td>戴森吸尘器 V15</td>
                                <td><span class="amount">¥3,999</span></td>
                                <td><span class="status-badge pending">配送中</span></td>
                                <td>2024-01-05 11:45</td>
                                <td>
                                    <button class="btn btn-ghost" style="padding: 4px 8px; font-size: 12px;">详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 操作日志 -->
            <div class="detail-card full-width-card">
                <div class="card-header">
                    <h3 class="card-title">操作日志</h3>
                    <a href="#" class="btn btn-ghost" style="padding: 6px 12px; font-size: 12px;">查看全部</a>
                </div>
                <div class="card-body" style="padding: 0;">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>操作</th>
                                <th>操作人</th>
                                <th>IP地址</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-01-15 10:30</td>
                                <td>修改用户信息</td>
                                <td>管理员</td>
                                <td>*************</td>
                                <td>更新手机号码</td>
                            </tr>
                            <tr>
                                <td>2024-01-10 15:20</td>
                                <td>添加VIP标签</td>
                                <td>系统</td>
                                <td>-</td>
                                <td>自动升级为VIP</td>
                            </tr>
                            <tr>
                                <td>2024-01-10 10:30</td>
                                <td>用户注册</td>
                                <td>用户</td>
                                <td>*************</td>
                                <td>通过邮箱注册</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 用户行为分析图表
        const behaviorCtx = document.getElementById('behaviorChart').getContext('2d');
        const behaviorChart = new Chart(behaviorCtx, {
            type: 'line',
            data: {
                labels: ['1月1日', '1月3日', '1月5日', '1月7日', '1月9日', '1月11日', '1月13日', '1月15日'],
                datasets: [{
                    label: '登录次数',
                    data: [2, 3, 1, 4, 2, 5, 3, 4],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '购买行为',
                    data: [0, 1, 0, 2, 0, 1, 0, 1],
                    borderColor: '#059669',
                    backgroundColor: 'rgba(5, 150, 105, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('用户详情页面已加载');
        });
    </script>
</body>
</html> 