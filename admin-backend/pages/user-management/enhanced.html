<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理增强版 - TeleShop后台管理</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
        }

        .page-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-success {
            background: #059669;
            color: white;
        }

        .btn-ghost {
            background: transparent;
            color: #64748b;
            border: 2px solid #e2e8f0;
        }

        /* 统计概览 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-title {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .stat-change {
            font-size: 14px;
            font-weight: 500;
        }

        .stat-change.positive {
            color: #059669;
        }

        /* 用户分析图表 */
        .analytics-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }

        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
        }

        /* 用户管理主体 */
        .user-management-main {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .management-header {
            background: #f8fafc;
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .management-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        /* 高级筛选 */
        .advanced-filters {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            background: white;
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }

        .filter-tab {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .filter-tab.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .filter-groups {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
        }

        .filter-input {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        /* 用户表格 */
        .user-table {
            width: 100%;
            border-collapse: collapse;
        }

        .user-table th,
        .user-table td {
            padding: 16px 24px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .user-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        .user-table tr:hover {
            background: #f8fafc;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .user-email {
            font-size: 12px;
            color: #64748b;
        }

        .user-status {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .user-status.active {
            background: #dcfce7;
            color: #166534;
        }

        .user-status.inactive {
            background: #fee2e2;
            color: #991b1b;
        }

        .user-status.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .user-tags {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }

        .user-tag {
            padding: 2px 6px;
            background: #f1f5f9;
            color: #64748b;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
        }

        .user-tag.vip {
            background: #fef3c7;
            color: #92400e;
        }

        .user-tag.new {
            background: #dcfce7;
            color: #166534;
        }

        .user-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-btn.view {
            background: #3b82f6;
            color: white;
        }

        .action-btn.edit {
            background: #059669;
            color: white;
        }

        .action-btn.delete {
            background: #dc2626;
            color: white;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            background: white;
            border-top: 1px solid #e2e8f0;
        }

        .pagination-info {
            color: #64748b;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .pagination-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        /* 用户详情模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px;
            border-bottom: 1px solid #e2e8f0;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #64748b;
        }

        .modal-body {
            padding: 24px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .detail-section {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            color: #64748b;
            font-size: 14px;
        }

        .detail-value {
            color: #1e293b;
            font-weight: 500;
        }

        @media (max-width: 1024px) {
            .analytics-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .page-header {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }

            .detail-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">用户管理增强版</h1>
                <p class="page-subtitle">全面管理用户生命周期，深入分析用户行为</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-ghost">
                    <i class="fas fa-download"></i>
                    导出用户
                </button>
                <button class="btn btn-success">
                    <i class="fas fa-upload"></i>
                    批量导入
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-user-plus"></i>
                    添加用户
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">总用户数</span>
                    <div class="stat-icon" style="background: #3b82f6;">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value">12,847</div>
                <div class="stat-change positive">+234 本周新增</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">活跃用户</span>
                    <div class="stat-icon" style="background: #059669;">
                        <i class="fas fa-user-check"></i>
                    </div>
                </div>
                <div class="stat-value">8,234</div>
                <div class="stat-change positive">+156 较昨日</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">VIP用户</span>
                    <div class="stat-icon" style="background: #d97706;">
                        <i class="fas fa-crown"></i>
                    </div>
                </div>
                <div class="stat-value">1,567</div>
                <div class="stat-change positive">+23 本月新增</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">用户留存率</span>
                    <div class="stat-icon" style="background: #8b5cf6;">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                </div>
                <div class="stat-value">78.5%</div>
                <div class="stat-change positive">+3.2% 环比增长</div>
            </div>
        </div>

        <!-- 用户分析图表 -->
        <div class="analytics-grid">
            <div class="chart-card">
                <h3 class="chart-title">用户增长趋势</h3>
                <div class="chart-wrapper">
                    <canvas id="userGrowthChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <h3 class="chart-title">用户类型分布</h3>
                <div class="chart-wrapper">
                    <canvas id="userTypeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 用户管理主体 -->
        <div class="user-management-main">
            <div class="management-header">
                <h3 class="management-title">用户列表</h3>
                <div class="page-actions">
                    <button class="btn btn-ghost">批量操作</button>
                    <button class="btn btn-ghost">标签管理</button>
                </div>
            </div>

            <!-- 高级筛选 -->
            <div class="advanced-filters">
                <div class="filter-tabs">
                    <button class="filter-tab active">全部用户</button>
                    <button class="filter-tab">活跃用户</button>
                    <button class="filter-tab">新用户</button>
                    <button class="filter-tab">VIP用户</button>
                    <button class="filter-tab">风险用户</button>
                </div>

                <div class="filter-groups">
                    <div class="filter-group">
                        <label class="filter-label">搜索用户</label>
                        <input type="text" class="filter-input" placeholder="姓名、邮箱、手机号">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">注册时间</label>
                        <select class="filter-select">
                            <option value="">全部时间</option>
                            <option value="today">今日</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">用户状态</label>
                        <select class="filter-select">
                            <option value="">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">非活跃</option>
                            <option value="banned">已封禁</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">用户标签</label>
                        <select class="filter-select">
                            <option value="">全部标签</option>
                            <option value="vip">VIP</option>
                            <option value="new">新用户</option>
                            <option value="frequent">高频用户</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 用户表格 -->
            <table class="user-table">
                <thead>
                    <tr>
                        <th><input type="checkbox"></th>
                        <th>用户信息</th>
                        <th>注册时间</th>
                        <th>最后登录</th>
                        <th>订单数</th>
                        <th>消费金额</th>
                        <th>状态</th>
                        <th>标签</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">张</div>
                                <div class="user-details">
                                    <div class="user-name">张小明</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>2024-01-10</td>
                        <td>2小时前</td>
                        <td>23</td>
                        <td>¥15,680</td>
                        <td><span class="user-status active">活跃</span></td>
                        <td>
                            <div class="user-tags">
                                <span class="user-tag vip">VIP</span>
                                <span class="user-tag">高频</span>
                            </div>
                        </td>
                        <td>
                            <div class="user-actions">
                                <button class="action-btn view" onclick="showUserDetail('张小明')">查看</button>
                                <button class="action-btn edit">编辑</button>
                                <button class="action-btn delete">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">李</div>
                                <div class="user-details">
                                    <div class="user-name">李小红</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>2024-01-15</td>
                        <td>今天 14:30</td>
                        <td>3</td>
                        <td>¥1,299</td>
                        <td><span class="user-status active">活跃</span></td>
                        <td>
                            <div class="user-tags">
                                <span class="user-tag new">新用户</span>
                            </div>
                        </td>
                        <td>
                            <div class="user-actions">
                                <button class="action-btn view" onclick="showUserDetail('李小红')">查看</button>
                                <button class="action-btn edit">编辑</button>
                                <button class="action-btn delete">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">王</div>
                                <div class="user-details">
                                    <div class="user-name">王大力</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>2023-12-20</td>
                        <td>3天前</td>
                        <td>45</td>
                        <td>¥32,450</td>
                        <td><span class="user-status inactive">非活跃</span></td>
                        <td>
                            <div class="user-tags">
                                <span class="user-tag vip">VIP</span>
                                <span class="user-tag">流失风险</span>
                            </div>
                        </td>
                        <td>
                            <div class="user-actions">
                                <button class="action-btn view" onclick="showUserDetail('王大力')">查看</button>
                                <button class="action-btn edit">编辑</button>
                                <button class="action-btn delete">删除</button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示 1-20 条，共 12,847 条记录
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>上一页</button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户详情模态框 -->
    <div id="userDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">用户详情</h3>
                <button class="close-btn" onclick="closeUserDetail()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="detail-grid">
                    <div class="detail-section">
                        <h4 class="section-title">基本信息</h4>
                        <div class="detail-item">
                            <span class="detail-label">用户姓名</span>
                            <span class="detail-value" id="userName">张小明</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">邮箱地址</span>
                            <span class="detail-value"><EMAIL></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">手机号码</span>
                            <span class="detail-value">138****5678</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">注册时间</span>
                            <span class="detail-value">2024-01-10 10:30:25</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">最后登录</span>
                            <span class="detail-value">2小时前</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4 class="section-title">消费数据</h4>
                        <div class="detail-item">
                            <span class="detail-label">总订单数</span>
                            <span class="detail-value">23</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">消费总额</span>
                            <span class="detail-value">¥15,680</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">平均订单金额</span>
                            <span class="detail-value">¥682</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">退款次数</span>
                            <span class="detail-value">2</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">会员等级</span>
                            <span class="detail-value">VIP黄金</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section" style="margin-top: 24px;">
                    <h4 class="section-title">行为分析</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <span class="detail-label">登录频率</span>
                            <span class="detail-value">每天2-3次</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">浏览时长</span>
                            <span class="detail-value">平均15分钟</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">收藏商品</span>
                            <span class="detail-value">34个</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">分享次数</span>
                            <span class="detail-value">12次</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 用户增长趋势图表
        const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
        const userGrowthChart = new Chart(userGrowthCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
                datasets: [{
                    label: '新增用户',
                    data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '活跃用户',
                    data: [800, 1500, 2500, 4200, 1800, 2600, 3800],
                    borderColor: '#059669',
                    backgroundColor: 'rgba(5, 150, 105, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 用户类型分布图表
        const userTypeCtx = document.getElementById('userTypeChart').getContext('2d');
        const userTypeChart = new Chart(userTypeCtx, {
            type: 'doughnut',
            data: {
                labels: ['普通用户', 'VIP用户', '新用户', '流失用户'],
                datasets: [{
                    data: [65, 15, 12, 8],
                    backgroundColor: [
                        '#3b82f6',
                        '#d97706',
                        '#059669',
                        '#dc2626'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });

        // 筛选标签切换
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                console.log('切换到:', this.textContent);
                // 这里可以添加筛选逻辑
            });
        });

        // 显示用户详情
        function showUserDetail(userName) {
            document.getElementById('userName').textContent = userName;
            document.getElementById('userDetailModal').style.display = 'block';
        }

        // 关闭用户详情
        function closeUserDetail() {
            document.getElementById('userDetailModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('userDetailModal');
            if (event.target === modal) {
                closeUserDetail();
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('用户管理增强版页面已加载');
        });
    </script>
</body>
</html> 