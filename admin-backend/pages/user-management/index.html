<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>用户管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/user-management.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="../../assets/css/header-button-fix.css">
    <link rel="stylesheet" href="../../assets/css/table-enhancement.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="../../assets/js/image-error-handler.js"></script>
            overflow-x: auto;
        }
        
        .user-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1000px;
        }
        
        .user-table th,
        .user-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        
        .user-table th {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: #f8fafc;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .user-table td {
            font-size: 14px;
            color: #1e293b;
        }
        
        .user-table tr:hover {
            background: #f8fafc;
        }
        
        /* 固定各列宽度 */
        .user-table th:nth-child(1),
        .user-table td:nth-child(1) { width: 40px; }
        .user-table th:nth-child(2),
        .user-table td:nth-child(2) { width: 220px; }
        .user-table th:nth-child(3),
        .user-table td:nth-child(3) { width: 120px; }
        .user-table th:nth-child(4),
        .user-table td:nth-child(4) { width: 100px; }
        .user-table th:nth-child(5),
        .user-table td:nth-child(5) { width: 120px; }
        .user-table th:nth-child(6),
        .user-table td:nth-child(6) { width: 100px; }
        .user-table th:nth-child(7),
        .user-table td:nth-child(7) { width: 100px; }
        .user-table th:nth-child(8),
        .user-table td:nth-child(8) { width: 100px; }
        .user-table th:nth-child(9),
        .user-table td:nth-child(9) { width: 160px; }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            background: #e2e8f0;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 0;
            width: 100%;
        }
        
        .user-details {
            flex: 1;
            min-width: 0;
            overflow: hidden;
        }
        
        .user-name {
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 2px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .user-email {
            font-size: 12px;
            color: #64748b;
            margin: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            min-width: 60px;
        }
        
        .status-active {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .role-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            background: #e0e7ff;
            color: #3730a3;
        }
        
        .action-buttons {
            display: flex;
            gap: 4px;
            justify-content: center;
            flex-wrap: nowrap;
            min-width: 0;
        }
        
        .action-icon-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            background: #f1f5f9;
            color: #64748b;
            flex-shrink: 0;
        }
        
        .action-icon-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .action-icon-btn.primary {
            background: #eff6ff;
            color: #2563eb;
        }
        
        .action-icon-btn.primary:hover {
            background: #dbeafe;
        }
        
        .action-icon-btn.danger {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .action-icon-btn.danger:hover {
            background: #fee2e2;
        }
        
        .action-icon-btn.warning {
            background: #fef3c7;
            color: #d97706;
        }
        
        .action-icon-btn.warning:hover {
            background: #fcd34d;
        }
        
        .action-icon-btn.success {
            background: #f0fdf4;
            color: #16a34a;
        }
        
        .action-icon-btn.success:hover {
            background: #dcfce7;
        }
        
        .pagination {
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #e2e8f0;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .pagination-info {
            font-size: 14px;
            color: #64748b;
        }
        
        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .pagination-btn:hover:not(:disabled) {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }
        
        .pagination-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .user-management-page {
                padding: 16px;
            }
            
            .page-header {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
            
            .page-title {
                font-size: 24px;
            }
            
            .page-actions {
                justify-content: stretch;
            }
            
            .action-btn {
                flex: 1;
                justify-content: center;
            }
            
            .stats-overview {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .stat-card {
                padding: 16px;
            }
            
            .content-header {
                padding: 16px;
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-filters {
                padding: 16px;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                min-width: auto;
            }
            
            .filter-input,
            .filter-select {
                min-width: auto;
                width: 100%;
            }
            
            .table-container {
                margin: 0 -16px;
            }
            
            .user-table {
                min-width: 600px;
            }
            
            .pagination {
                padding: 16px;
                justify-content: center;
            }
            
            .pagination-info {
                order: 2;
                width: 100%;
                text-align: center;
                margin-top: 8px;
            }
        }
        
        @media (max-width: 480px) {
            .stats-overview {
                grid-template-columns: 1fr;
            }
            
            .page-actions {
                flex-direction: column;
            }
            
            .user-table {
                font-size: 12px;
            }
            
            .user-table th,
            .user-table td {
                padding: 8px 12px;
            }
            
            .table-actions {
                flex-direction: column;
                gap: 4px;
            }
        }
        
        /* 批量操作栏样式 */
        .bulk-actions {
            padding: 16px 24px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            display: none;
            animation: slideDown 0.3s ease-out;
        }
        
        .bulk-actions.show {
            display: block;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .bulk-controls {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        
        .selected-count {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-right: auto;
        }
        
        .selected-count span {
            color: #3b82f6;
            font-weight: 600;
        }
        
        /* 批量操作按钮样式 */
        .bulk-controls .action-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #374151;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
            font-weight: 500;
        }
        
        .bulk-controls .action-btn:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .bulk-controls .action-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .bulk-controls .action-btn.primary:hover {
            background: #2563eb;
            border-color: #2563eb;
        }
        
        .bulk-controls .action-btn.success {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }
        
        .bulk-controls .action-btn.success:hover {
            background: #059669;
            border-color: #059669;
        }
        
        .bulk-controls .action-btn.warning {
            background: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }
        
        .bulk-controls .action-btn.warning:hover {
            background: #d97706;
            border-color: #d97706;
        }
        
        .bulk-controls .action-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .bulk-controls .action-btn.danger:hover {
            background: #dc2626;
            border-color: #dc2626;
        }
        
        .bulk-controls .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .bulk-controls .action-btn i {
            font-size: 14px;
        }
        
        /* 确保批量操作按钮文字正确显示 */
        .bulk-controls .action-btn {
            font-size: 14px !important;
            color: inherit !important;
            text-indent: 0 !important;
            text-transform: none !important;
            letter-spacing: normal !important;
            word-spacing: normal !important;
            white-space: nowrap !important;
            overflow: visible !important;
            text-overflow: visible !important;
            opacity: 1 !important;
            visibility: visible !important;
            text-decoration: none !important;
            min-width: auto !important;
            width: auto !important;
            padding: 8px 16px !important;
            box-sizing: border-box !important;
        }
        
        .bulk-controls .action-btn.warning {
            background: #f59e0b !important;
            color: white !important;
            border-color: #f59e0b !important;
        }
        
        .bulk-controls .action-btn.warning:hover {
            background: #d97706 !important;
            border-color: #d97706 !important;
            color: white !important;
        }
        
        .bulk-controls .action-btn.danger {
            background: #ef4444 !important;
            color: white !important;
            border-color: #ef4444 !important;
        }
        
        .bulk-controls .action-btn.danger:hover {
            background: #dc2626 !important;
            border-color: #dc2626 !important;
            color: white !important;
        }
        
        /* 强制修复按钮文字显示问题 */
        .bulk-controls .action-btn {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 6px !important;
            min-width: 80px !important;
            max-width: none !important;
            width: auto !important;
            height: auto !important;
            line-height: 1.4 !important;
            text-align: center !important;
            vertical-align: middle !important;
        }
        
        .bulk-controls .action-btn span,
        .bulk-controls .action-btn:not(:has(span)) {
            display: inline !important;
            white-space: nowrap !important;
            overflow: visible !important;
            text-overflow: clip !important;
            max-width: none !important;
            width: auto !important;
        }
        
        /* 确保图标和文字都正确显示 */
        .bulk-controls .action-btn i {
            display: inline !important;
            margin-right: 4px !important;
            flex-shrink: 0 !important;
        }
        
        /* 响应式批量操作 */
        @media (max-width: 768px) {
            .bulk-actions {
                padding: 12px 16px;
            }
            
            .bulk-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
            
            .selected-count {
                text-align: center;
                margin-right: 0;
                margin-bottom: 8px;
            }
            
            .bulk-controls .action-btn {
                justify-content: center;
                padding: 10px 16px !important;
                min-width: 120px !important;
                width: auto !important;
                flex: none !important;
            }
        }
        
        @media (max-width: 480px) {
            .bulk-controls {
                gap: 8px;
            }
            
            .bulk-controls .action-btn {
                padding: 8px 12px !important;
                font-size: 13px !important;
                min-width: 100px !important;
                width: auto !important;
                white-space: nowrap !important;
                overflow: visible !important;
            }
            
            .bulk-controls .action-btn i {
                font-size: 12px !important;
                margin-right: 4px !important;
            }
        }
        
        /* 图片占位符样式 */
        .image-placeholder {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-size: 16px;
        }
        
        /* 确保所有元素都有正确的盒模型 */
        *, *::before, *::after {
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="user-management-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">用户管理</h1>
                <p style="color: #64748b; margin: 4px 0 0 0;">管理和监控 TeleShop 平台用户</p>
            </div>
            <div class="page-actions">
                <a href="../dashboard/index.html" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i> 仪表板
                </a>
                <a href="batch-operations.html" class="action-btn">
                    <i class="fas fa-tasks"></i> 批量操作
                </a>
                <a href="member-marketing.html" class="action-btn">
                    <i class="fas fa-bullhorn"></i> 会员营销
                </a>
                <button class="action-btn" onclick="exportUsers()">
                    <i class="fas fa-download"></i> 导出用户
                </button>
                <button class="action-btn" onclick="importUsers()">
                    <i class="fas fa-upload"></i> 批量导入
                </button>
                <a href="edit-user.html" class="action-btn primary">
                    <i class="fas fa-plus"></i> 添加用户
                </a>
            </div>
        </div>
        
        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">总用户数</span>
                    <div class="stat-icon" style="background: #dbeafe; color: #3b82f6;">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value">12,847</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12.5% 本月</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">活跃用户</span>
                    <div class="stat-icon" style="background: #dcfce7; color: #10b981;">
                        <i class="fas fa-user-check"></i>
                    </div>
                </div>
                <div class="stat-value">8,234</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8.2% 今日</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">商家用户</span>
                    <div class="stat-icon" style="background: #fef3c7; color: #f59e0b;">
                        <i class="fas fa-store"></i>
                    </div>
                </div>
                <div class="stat-value">1,456</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+3.8% 本周</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">新注册</span>
                    <div class="stat-icon" style="background: #f3e8ff; color: #8b5cf6;">
                        <i class="fas fa-user-plus"></i>
                    </div>
                </div>
                <div class="stat-value">234</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i>
                    <span>-2.1% 今日</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">VIP用户</span>
                    <div class="stat-icon" style="background: #fce7f3; color: #ec4899;">
                        <i class="fas fa-crown"></i>
                    </div>
                </div>
                <div class="stat-value">567</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+15.3% 本月</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">封禁用户</span>
                    <div class="stat-icon" style="background: #fee2e2; color: #ef4444;">
                        <i class="fas fa-user-slash"></i>
                    </div>
                </div>
                <div class="stat-value">89</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+5 本周</span>
                </div>
            </div>
        </div>
        
        <!-- 主要内容 -->
        <div class="main-content">
            <div class="content-header">
                <h3 class="content-title">用户列表</h3>
                <div class="page-actions">
                    <button class="action-btn" onclick="refreshUsers()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button class="action-btn" onclick="toggleBulkActions()">
                        <i class="fas fa-check-square"></i> 批量操作
                    </button>
                </div>
            </div>
            
            <!-- 搜索和过滤 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">搜索用户</label>
                        <input type="text" class="filter-input" placeholder="姓名、邮箱、ID..." 
                               onkeyup="searchUsers(this.value)">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">用户状态</label>
                        <select class="filter-select" onchange="filterByStatus(this.value)">
                            <option value="">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">非活跃</option>
                            <option value="pending">待验证</option>
                            <option value="banned">已封禁</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">用户角色</label>
                        <select class="filter-select" onchange="filterByRole(this.value)">
                            <option value="">全部角色</option>
                            <option value="user">普通用户</option>
                            <option value="seller">商家</option>
                            <option value="vip">VIP用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">注册来源</label>
                        <select class="filter-select" onchange="filterBySource(this.value)">
                            <option value="">全部来源</option>
                            <option value="telegram">Telegram</option>
                            <option value="web">网站</option>
                            <option value="referral">推荐</option>
                            <option value="api">API</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">注册时间</label>
                        <select class="filter-select" onchange="filterByRegistration(this.value)">
                            <option value="">全部时间</option>
                            <option value="today">今日</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                            <option value="quarter">本季度</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- 批量操作栏 -->
            <div class="bulk-actions" id="bulkActions">
                <div class="bulk-controls">
                    <span class="selected-count">已选择 <span id="selectedCount">0</span> 个用户</span>
                    <button class="action-btn primary" onclick="bulkExport()">
                        <i class="fas fa-download"></i> 导出选中
                    </button>
                    <button class="action-btn success" onclick="bulkActivate()">
                        <i class="fas fa-check"></i> 激活
                    </button>
                    <button class="action-btn warning" onclick="bulkDeactivate()">
                        <i class="fas fa-pause"></i> 停用
                    </button>
                    <button class="action-btn danger" onclick="bulkDelete()">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
            
            <!-- 用户表格 -->
            <table class="users-table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" onchange="toggleSelectAll(this.checked)">
                        </th>
                        <th>用户信息</th>
                        <th>状态</th>
                        <th>角色</th>
                        <th>钱包余额</th>
                        <th>注册来源</th>
                        <th>最后活跃</th>
                        <th>注册时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="1"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b332844c?w=40&h=40&fit=crop" alt="Alice" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Alice Chen</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-active">活跃</span>
                            <div class="last-active">
                                <span class="online-indicator"></span>在线
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-vip">VIP</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥12,458.50</div>
                            <div style="font-size: 12px; color: #64748b;">$1,867.23</div>
                        </td>
                        <td>
                            <span class="registration-source">Telegram</span>
                        </td>
                        <td>
                            <div class="last-active">刚刚</div>
                        </td>
                        <td>2024-01-15</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(1)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(1)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="loginAsUser(1)" title="登录为该用户">
                                    <i class="fas fa-sign-in-alt"></i>
                                </button>
                                <button class="action-icon-btn danger" onclick="banUser(1)" title="封禁">
                                    <i class="fas fa-ban"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="2"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop" alt="Bob" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Bob Wang</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-pending">待验证</span>
                            <div class="last-active">
                                <span class="offline-indicator"></span>8分钟前
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-seller">商家</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥8,234.00</div>
                            <div style="font-size: 12px; color: #64748b;">$1,234.56</div>
                        </td>
                        <td>
                            <span class="registration-source">Web</span>
                        </td>
                        <td>
                            <div class="last-active">8分钟前</div>
                        </td>
                        <td>2024-02-20</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(2)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(2)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="verifyUser(2)" title="验证用户">
                                    <i class="fas fa-check-circle"></i>
                                </button>
                                <button class="action-icon-btn danger" onclick="banUser(2)" title="封禁">
                                    <i class="fas fa-ban"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="3"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop" alt="Charlie" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Charlie Li</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-active">活跃</span>
                            <div class="last-active">
                                <span class="offline-indicator"></span>2小时前
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-user">用户</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥567.80</div>
                            <div style="font-size: 12px; color: #64748b;">$85.17</div>
                        </td>
                        <td>
                            <span class="registration-source">推荐</span>
                        </td>
                        <td>
                            <div class="last-active">2小时前</div>
                        </td>
                        <td>2024-03-10</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(3)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(3)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="upgradeToVip(3)" title="升级VIP">
                                    <i class="fas fa-crown"></i>
                                </button>
                                <button class="action-icon-btn danger" onclick="banUser(3)" title="封禁">
                                    <i class="fas fa-ban"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="4"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop" alt="David" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">David Zhang</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-banned">已封禁</span>
                            <div class="last-active">
                                <span class="offline-indicator"></span>3天前
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-user">用户</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥0.00</div>
                            <div style="font-size: 12px; color: #64748b;">$0.00</div>
                        </td>
                        <td>
                            <span class="registration-source">Telegram</span>
                        </td>
                        <td>
                            <div class="last-active">3天前</div>
                        </td>
                        <td>2024-02-28</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(4)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(4)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="unbanUser(4)" title="解封">
                                    <i class="fas fa-unlock"></i>
                                </button>
                                <button class="action-icon-btn danger" onclick="deleteUser(4)" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="5"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=40&h=40&fit=crop" alt="Eve" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Eve Johnson</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-inactive">非活跃</span>
                            <div class="last-active">
                                <span class="offline-indicator"></span>1周前
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-admin">管理员</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥25,689.00</div>
                            <div style="font-size: 12px; color: #64748b;">$3,853.35</div>
                        </td>
                        <td>
                            <span class="registration-source">API</span>
                        </td>
                        <td>
                            <div class="last-active">1周前</div>
                        </td>
                        <td>2023-12-01</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(5)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(5)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="resetPassword(5)" title="重置密码">
                                    <i class="fas fa-key"></i>
                                </button>
                                <button class="action-icon-btn" onclick="managePermissions(5)" title="权限管理">
                                    <i class="fas fa-shield-alt"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="6"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop" alt="Frank" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Frank Wilson</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-pending">待验证</span>
                            <div class="last-active">
                                <span class="offline-indicator"></span>5天前
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-user">用户</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥156.90</div>
                            <div style="font-size: 12px; color: #64748b;">$23.54</div>
                        </td>
                        <td>
                            <span class="registration-source">Web</span>
                        </td>
                        <td>
                            <div class="last-active">5天前</div>
                        </td>
                        <td>2024-03-15</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(6)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn success" onclick="verifyUser(6)" title="验证用户">
                                    <i class="fas fa-check-circle"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(6)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn danger" onclick="banUser(6)" title="封禁">
                                    <i class="fas fa-ban"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="7"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=40&h=40&fit=crop" alt="Grace" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Grace Taylor</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-active">活跃</span>
                            <div class="last-active">
                                <span class="online-indicator"></span>在线
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-seller">商家</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥45,678.20</div>
                            <div style="font-size: 12px; color: #64748b;">$6,851.73</div>
                        </td>
                        <td>
                            <span class="registration-source">API</span>
                        </td>
                        <td>
                            <div class="last-active">刚刚</div>
                        </td>
                        <td>2023-11-30</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(7)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(7)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="loginAsUser(7)" title="登录为该用户">
                                    <i class="fas fa-sign-in-alt"></i>
                                </button>
                                <button class="action-icon-btn" onclick="manageStore(7)" title="管理店铺">
                                    <i class="fas fa-store"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示 1-20 条，共 12,847 条记录
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>上一页</button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn">...</button>
                    <button class="pagination-btn">643</button>
                    <button class="pagination-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>
    
</body>
<script src="../../assets/js/user-management.js"></script>
</html> 
