<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑用户 - TeleShop后台管理</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.2s;
        }

        .back-btn:hover {
            color: #3b82f6;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
        }

        .page-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-success {
            background: #059669;
            color: white;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-ghost {
            background: transparent;
            color: #64748b;
            border: 2px solid #e2e8f0;
        }

        .edit-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }

        .form-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            background: #f8fafc;
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .card-body {
            padding: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #1e293b;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        /* 用户头像 */
        .avatar-section {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: 600;
        }

        .avatar-info {
            flex: 1;
        }

        .avatar-name {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .avatar-email {
            color: #64748b;
            font-size: 14px;
        }

        /* 标签管理 */
        .tags-section {
            margin-bottom: 24px;
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }

        .tag {
            padding: 6px 12px;
            background: #f1f5f9;
            color: #64748b;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .tag.vip {
            background: #fef3c7;
            color: #92400e;
        }

        .tag.new {
            background: #dcfce7;
            color: #166534;
        }

        .tag.active {
            background: #dbeafe;
            color: #1e40af;
        }

        .tag-remove {
            background: none;
            border: none;
            color: inherit;
            cursor: pointer;
            font-size: 10px;
            padding: 0;
        }

        .tag-input {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .tag-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }

        .add-tag-btn {
            padding: 8px 16px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
        }

        /* 状态管理 */
        .status-section {
            margin-bottom: 24px;
        }

        .status-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .status-option {
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-option.selected {
            border-color: #3b82f6;
            background: #f0f9ff;
        }

        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }

        .status-icon.active {
            background: #059669;
        }

        .status-icon.inactive {
            background: #94a3b8;
        }

        .status-icon.banned {
            background: #dc2626;
        }

        .status-info {
            flex: 1;
        }

        .status-name {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .status-desc {
            font-size: 12px;
            color: #64748b;
        }

        /* 操作历史 */
        .history-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f1f5f9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #64748b;
        }

        .history-content {
            flex: 1;
        }

        .history-action {
            font-weight: 500;
            color: #1e293b;
            font-size: 14px;
        }

        .history-time {
            font-size: 12px;
            color: #64748b;
        }

        /* 数据统计 */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-item {
            text-align: center;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .edit-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .status-options {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div style="display: flex; align-items: center; gap: 16px;">
                <a href="index.html" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    返回用户列表
                </a>
                <h1 class="page-title">编辑用户</h1>
            </div>
            <div class="page-actions">
                <button class="btn btn-ghost">
                    <i class="fas fa-history"></i>
                    操作历史
                </button>
                <button class="btn btn-danger">
                    <i class="fas fa-ban"></i>
                    封禁用户
                </button>
                <button class="btn btn-success">
                    <i class="fas fa-save"></i>
                    保存更改
                </button>
            </div>
        </div>

        <div class="edit-grid">
            <!-- 左侧：用户信息编辑 -->
            <div class="form-card">
                <div class="card-header">
                    <h3 class="card-title">用户信息</h3>
                </div>
                <div class="card-body">
                    <!-- 用户头像 -->
                    <div class="avatar-section">
                        <div class="avatar">张</div>
                        <div class="avatar-info">
                            <div class="avatar-name">张小明</div>
                            <div class="avatar-email"><EMAIL></div>
                        </div>
                        <button class="btn btn-ghost">
                            <i class="fas fa-camera"></i>
                            更换头像
                        </button>
                    </div>

                    <!-- 基本信息 -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">用户姓名</label>
                            <input type="text" class="form-input" value="张小明" placeholder="请输入用户姓名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">昵称</label>
                            <input type="text" class="form-input" value="小明同学" placeholder="请输入昵称">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">邮箱地址</label>
                            <input type="email" class="form-input" value="<EMAIL>" placeholder="请输入邮箱地址">
                        </div>
                        <div class="form-group">
                            <label class="form-label">手机号码</label>
                            <input type="tel" class="form-input" value="13800138000" placeholder="请输入手机号码">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">性别</label>
                            <select class="form-select">
                                <option value="">请选择性别</option>
                                <option value="male" selected>男</option>
                                <option value="female">女</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">生日</label>
                            <input type="date" class="form-input" value="1990-01-01">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">所在城市</label>
                            <input type="text" class="form-input" value="北京" placeholder="请输入所在城市">
                        </div>
                        <div class="form-group">
                            <label class="form-label">会员等级</label>
                            <select class="form-select">
                                <option value="normal">普通会员</option>
                                <option value="silver">银卡会员</option>
                                <option value="gold" selected>金卡会员</option>
                                <option value="platinum">白金会员</option>
                                <option value="diamond">钻石会员</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">用户备注</label>
                        <textarea class="form-input form-textarea" placeholder="添加用户备注信息">高价值客户，购买力强，需要重点维护</textarea>
                    </div>

                    <!-- 标签管理 -->
                    <div class="tags-section">
                        <label class="form-label">用户标签</label>
                        <div class="tags-container">
                            <span class="tag vip">
                                VIP
                                <button class="tag-remove">&times;</button>
                            </span>
                            <span class="tag new">
                                高频用户
                                <button class="tag-remove">&times;</button>
                            </span>
                            <span class="tag active">
                                活跃用户
                                <button class="tag-remove">&times;</button>
                            </span>
                        </div>
                        <div class="tag-input">
                            <input type="text" placeholder="输入新标签" id="newTagInput">
                            <button class="add-tag-btn" onclick="addTag()">添加标签</button>
                        </div>
                    </div>

                    <!-- 状态管理 -->
                    <div class="status-section">
                        <label class="form-label">用户状态</label>
                        <div class="status-options">
                            <div class="status-option selected" onclick="selectStatus(this, 'active')">
                                <div class="status-icon active">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="status-info">
                                    <div class="status-name">正常</div>
                                    <div class="status-desc">用户可以正常使用所有功能</div>
                                </div>
                            </div>
                            <div class="status-option" onclick="selectStatus(this, 'inactive')">
                                <div class="status-icon inactive">
                                    <i class="fas fa-pause"></i>
                                </div>
                                <div class="status-info">
                                    <div class="status-name">暂停</div>
                                    <div class="status-desc">临时暂停用户部分功能</div>
                                </div>
                            </div>
                            <div class="status-option" onclick="selectStatus(this, 'banned')">
                                <div class="status-icon banned">
                                    <i class="fas fa-ban"></i>
                                </div>
                                <div class="status-info">
                                    <div class="status-name">封禁</div>
                                    <div class="status-desc">禁止用户访问系统</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：统计信息和操作历史 -->
            <div style="display: flex; flex-direction: column; gap: 24px;">
                <!-- 数据统计 -->
                <div class="form-card">
                    <div class="card-header">
                        <h3 class="card-title">数据统计</h3>
                    </div>
                    <div class="card-body">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">23</div>
                                <div class="stat-label">总订单数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">¥15,680</div>
                                <div class="stat-label">消费总额</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">156</div>
                                <div class="stat-label">登录次数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">12</div>
                                <div class="stat-label">分享次数</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账户信息 -->
                <div class="form-card">
                    <div class="card-header">
                        <h3 class="card-title">账户信息</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">注册时间</label>
                            <input type="text" class="form-input" value="2024-01-10 10:30:25" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">最后登录</label>
                            <input type="text" class="form-input" value="2024-01-15 14:30:25" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">注册IP</label>
                            <input type="text" class="form-input" value="*************" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">最后登录IP</label>
                            <input type="text" class="form-input" value="*************" disabled>
                        </div>
                    </div>
                </div>

                <!-- 操作历史 -->
                <div class="form-card">
                    <div class="card-header">
                        <h3 class="card-title">操作历史</h3>
                    </div>
                    <div class="card-body">
                        <div class="history-item">
                            <div class="history-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="history-content">
                                <div class="history-action">修改用户信息</div>
                                <div class="history-time">2024-01-15 10:30</div>
                            </div>
                        </div>
                        <div class="history-item">
                            <div class="history-icon">
                                <i class="fas fa-tag"></i>
                            </div>
                            <div class="history-content">
                                <div class="history-action">添加VIP标签</div>
                                <div class="history-time">2024-01-10 15:20</div>
                            </div>
                        </div>
                        <div class="history-item">
                            <div class="history-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="history-content">
                                <div class="history-action">用户注册</div>
                                <div class="history-time">2024-01-10 10:30</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加标签
        function addTag() {
            const input = document.getElementById('newTagInput');
            const tagText = input.value.trim();
            
            if (tagText) {
                const tagsContainer = document.querySelector('.tags-container');
                const newTag = document.createElement('span');
                newTag.className = 'tag';
                newTag.innerHTML = `
                    ${tagText}
                    <button class="tag-remove" onclick="removeTag(this)">&times;</button>
                `;
                tagsContainer.appendChild(newTag);
                input.value = '';
            }
        }

        // 删除标签
        function removeTag(button) {
            button.parentElement.remove();
        }

        // 选择状态
        function selectStatus(element, status) {
            document.querySelectorAll('.status-option').forEach(option => {
                option.classList.remove('selected');
            });
            element.classList.add('selected');
            console.log('选择状态:', status);
        }

        // 回车添加标签
        document.getElementById('newTagInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addTag();
            }
        });

        // 保存用户信息
        function saveUser() {
            // 收集表单数据
            const formData = {
                name: document.querySelector('input[placeholder="请输入用户姓名"]').value,
                nickname: document.querySelector('input[placeholder="请输入昵称"]').value,
                email: document.querySelector('input[placeholder="请输入邮箱地址"]').value,
                phone: document.querySelector('input[placeholder="请输入手机号码"]').value,
                // ... 其他字段
            };
            
            console.log('保存用户信息:', formData);
            alert('用户信息已保存');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('用户编辑页面已加载');
        });
    </script>
</body>
</html> 