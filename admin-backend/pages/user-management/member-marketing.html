<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员营销管理 - TeleShop后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 页面基础样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: #f5f7fa;
        }
        
        .admin-container {
            min-height: 100vh;
        }
        
        /* 顶部导航栏样式 */
        .top-navbar {
            background: #2c3e50;
            color: white;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            font-weight: bold;
            padding: 15px 0;
        }
        
        .navbar-nav {
            display: flex;
            align-items: center;
            gap: 0;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.2s ease;
            border-bottom: 3px solid transparent;
        }
        
        .nav-link:hover {
            background: #34495e;
            color: white;
        }
        
        .nav-link.active {
            background: #3498db;
            color: white;
            border-bottom-color: #2980b9;
        }
        
        .nav-link i {
            width: 16px;
            text-align: center;
        }
        
        /* 主内容区样式 */
        .main-content {
            width: 100%;
            padding: 20px;
            overflow-x: auto;
        }
        
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .content-header h1 {
            margin: 0;
            color: #2c3e50;
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .member-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-card.gold {
            border-left-color: #ffc107;
        }

        .stat-card.silver {
            border-left-color: #6c757d;
        }

        .stat-card.diamond {
            border-left-color: #17a2b8;
        }

        .stat-card.platinum {
            border-left-color: #6f42c1;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .stat-change {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
        }

        .stat-change.positive {
            background: #d4edda;
            color: #155724;
        }

        .stat-change.negative {
            background: #f8d7da;
            color: #721c24;
        }

        .member-level-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .level-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 15px;
        }

        .level-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 20px;
            color: white;
        }

        .level-icon.silver { background: linear-gradient(135deg, #BDC3C7 0%, #95A5A6 100%); }
        .level-icon.gold { background: linear-gradient(135deg, #F7DC6F 0%, #F39C12 100%); }
        .level-icon.diamond { background: linear-gradient(135deg, #85C1E9 0%, #3498DB 100%); }
        .level-icon.platinum { background: linear-gradient(135deg, #D7BDE2 0%, #9B59B6 100%); }

        .privilege-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .privilege-item {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
        }

        .privilege-item i {
            color: #007bff;
            font-size: 24px;
            margin-bottom: 8px;
        }

        .tab-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tab-nav {
            display: flex;
            border-bottom: 1px solid #dee2e6;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: none;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        .tab-content {
            padding: 20px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .points-config {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .config-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .birthday-calendar {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #dee2e6;
        }

        .calendar-header {
            background: #007bff;
            color: white;
            padding: 15px;
            font-weight: bold;
            text-align: center;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #dee2e6;
        }

        .calendar-day {
            background: white;
            padding: 8px;
            text-align: center;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .calendar-day.birthday {
            background: #ffe4e6;
            color: #e74c3c;
            font-weight: bold;
        }

        .birthday-dot {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 6px;
            height: 6px;
            background: #e74c3c;
            border-radius: 50%;
        }

        .member-analytics {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .marketing-campaign {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .campaign-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .campaign-status.active {
            background: #d4edda;
            color: #155724;
        }

        .campaign-status.scheduled {
            background: #fff3cd;
            color: #856404;
        }

        .campaign-status.ended {
            background: #f8d7da;
            color: #721c24;
        }
        
        /* 响应式设计 */
        @media (max-width: 1024px) {
            .member-analytics {
                grid-template-columns: 1fr;
            }
            
            .config-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .top-navbar {
                flex-direction: column;
                padding: 10px 20px;
            }
            
            .navbar-brand {
                padding: 10px 0;
            }
            
            .navbar-nav {
                width: 100%;
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .nav-link {
                padding: 10px 15px;
                font-size: 14px;
            }
            
            .nav-link span {
                display: none;
            }
            
            .content-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: flex-start;
            }
            
            .member-stats {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }
            
            .tab-nav {
                flex-wrap: wrap;
            }
            
            .tab-button {
                flex: none;
                min-width: 120px;
            }
            
            .privilege-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 10px;
            }
        }
        
        @media (max-width: 480px) {
            .main-content {
                padding: 15px;
            }
            
            .content-header h1 {
                font-size: 24px;
            }
            
            .member-stats {
                grid-template-columns: 1fr;
            }
            
            .btn {
                padding: 8px 16px;
                font-size: 13px;
            }
            
            .tab-button {
                padding: 12px 15px;
                font-size: 13px;
            }
        }
        
        /* 修复其他样式问题 */
        .level-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .text-muted {
            color: #6c757d;
            font-size: 12px;
        }
        
        /* Bootstrap替代样式 */
        .d-flex {
            display: flex;
        }
        
        .justify-content-between {
            justify-content: space-between;
        }
        
        .align-items-start {
            align-items: flex-start;
        }
        
        .align-items-center {
            align-items: center;
        }
        
        .ml-2 {
            margin-left: 0.5rem;
        }
        
        .mt-3 {
            margin-top: 1rem;
        }
        
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -15px;
        }
        
        .col-sm-3 {
            flex: 0 0 25%;
            max-width: 25%;
            padding: 0 15px;
            margin-bottom: 10px;
        }
        
        .form-control {
            display: block;
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: 4px;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .form-control:focus {
            color: #495057;
            background-color: #fff;
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        small {
            font-size: 0.875em;
        }
        
        label {
            display: inline-block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        /* 响应式列布局 */
        @media (max-width: 768px) {
            .col-sm-3 {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }
        
        @media (max-width: 480px) {
            .col-sm-3 {
                flex: 0 0 100%;
                max-width: 100%;
            }
            
            .row {
                margin: 0;
            }
            
            .col-sm-3 {
                padding: 0;
            }
        }
        
        /* 模态框和表单增强样式 */
        .level-edit-form .form-group {
            margin-bottom: 20px;
        }
        
        .campaign-form .form-group {
            margin-bottom: 15px;
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }
        
        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 5px;
            font-weight: normal;
            margin: 0;
        }
        
        .export-options .form-group {
            margin-bottom: 20px;
        }
        
        .campaign-details {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .detail-section {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .detail-section:last-child {
            border-bottom: none;
        }
        
        .detail-section h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .timeline {
            margin-top: 10px;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 8px 0;
            border-left: 3px solid #3498db;
            padding-left: 15px;
            margin-bottom: 10px;
        }
        
        .timeline-time {
            font-size: 12px;
            color: #666;
            min-width: 120px;
        }
        
        .timeline-content {
            font-size: 14px;
            color: #333;
        }
        
        /* 按钮增强样式 */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .btn:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        /* 表单验证样式 */
        .form-control.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
        
        .form-control.is-valid {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        
        /* 加载状态样式 */
        .loading {
            position: relative;
            pointer-events: none;
        }
        
        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 模态框样式增强 */
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            margin-top: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        textarea.form-control {
            resize: vertical;
            min-height: 80px;
        }
        
        select.form-control {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px 12px;
            padding-right: 40px;
        }
        
        /* 模态框内的行列布局修复 */
        .custom-modal .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        
        .custom-modal .col-sm-6 {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 10px;
            box-sizing: border-box;
        }
        
        .custom-modal .col-sm-3 {
            flex: 0 0 25%;
            max-width: 25%;
            padding: 0 10px;
            box-sizing: border-box;
        }
        
        /* 模态框响应式布局 */
        @media (max-width: 768px) {
            .custom-modal .col-sm-6,
            .custom-modal .col-sm-3 {
                flex: 0 0 100%;
                max-width: 100%;
                margin-bottom: 15px;
            }
        }
        
        /* 模态框内表单样式优化 */
        .custom-modal .form-control {
            width: 100%;
            box-sizing: border-box;
        }
        
        .custom-modal .form-group {
            margin-bottom: 20px;
        }
        
        .custom-modal .campaign-form {
            max-width: none;
        }
        
        .custom-modal .level-edit-form {
            max-width: none;
        }
        
        /* 修复模态框内的checkbox样式 */
        .custom-modal .checkbox-group {
            padding: 10px 0;
        }
        
        .custom-modal .checkbox-group label {
            cursor: pointer;
            padding: 5px 0;
        }
        
        .custom-modal .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }
        
        /* 导出选项样式 */
        .export-options {
            min-width: 400px;
        }
        
        @media (max-width: 480px) {
            .export-options {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 顶部导航栏 -->
        <nav class="top-navbar">
            <div class="navbar-brand">
                <i class="fas fa-store"></i>
                <span>TeleShop Admin</span>
            </div>
            <div class="navbar-nav">
                <a href="../dashboard/index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </a>
                <a href="index.html" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>用户列表</span>
                </a>
                <a href="member-marketing.html" class="nav-link active">
                    <i class="fas fa-crown"></i>
                    <span>会员营销</span>
                </a>
                <a href="user-detail.html" class="nav-link">
                    <i class="fas fa-user-circle"></i>
                    <span>用户详情</span>
                </a>
            </div>
        </nav>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <header class="content-header">
                <h1><i class="fas fa-crown"></i> 会员营销管理</h1>
                <div class="header-actions">
                    <button class="btn btn-success" onclick="createCampaign()">
                        <i class="fas fa-plus"></i> 创建营销活动
                    </button>
                    <button class="btn btn-primary" onclick="exportMemberData()">
                        <i class="fas fa-download"></i> 导出数据
                    </button>
                </div>
            </header>

            <!-- 会员统计概览 -->
            <div class="member-stats">
                <div class="stat-card">
                    <div class="stat-number">15,842</div>
                    <div class="stat-label">总会员数</div>
                    <div class="stat-change positive">+12.5% 本月</div>
                </div>
                
                <div class="stat-card silver">
                    <div class="stat-number">8,950</div>
                    <div class="stat-label">白银会员</div>
                    <div class="stat-change positive">+8.3% 本月</div>
                </div>
                
                <div class="stat-card gold">
                    <div class="stat-number">4,568</div>
                    <div class="stat-label">黄金会员</div>
                    <div class="stat-change positive">+15.7% 本月</div>
                </div>
                
                <div class="stat-card diamond">
                    <div class="stat-number">1,892</div>
                    <div class="stat-label">钻石会员</div>
                    <div class="stat-change positive">+22.1% 本月</div>
                </div>
                
                <div class="stat-card platinum">
                    <div class="stat-number">432</div>
                    <div class="stat-label">白金会员</div>
                    <div class="stat-change positive">+18.4% 本月</div>
                </div>
            </div>

            <!-- 分析图表 -->
            <div class="member-analytics">
                <div class="chart-container">
                    <h3>会员增长趋势</h3>
                    <canvas id="memberGrowthChart" width="400" height="200"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3>会员等级分布</h3>
                    <canvas id="memberDistributionChart" width="300" height="200"></canvas>
                </div>
            </div>

            <!-- 功能标签页 -->
            <div class="tab-container">
                <div class="tab-nav">
                    <button class="tab-button active" onclick="showTab('levels')">
                        <i class="fas fa-layer-group"></i> 会员等级
                    </button>
                    <button class="tab-button" onclick="showTab('points')">
                        <i class="fas fa-coins"></i> 积分系统
                    </button>
                    <button class="tab-button" onclick="showTab('birthday')">
                        <i class="fas fa-birthday-cake"></i> 生日特权
                    </button>
                    <button class="tab-button" onclick="showTab('campaigns')">
                        <i class="fas fa-bullhorn"></i> 营销活动
                    </button>
                </div>

                <!-- 会员等级管理 -->
                <div id="levelsTab" class="tab-content active">
                    <h3>会员等级配置</h3>
                    
                    <div class="member-level-card">
                        <div class="level-header">
                            <div class="level-icon silver">
                                <i class="fas fa-medal"></i>
                            </div>
                            <div style="flex: 1;">
                                <h4>白银会员</h4>
                                <p>消费满 ¥1,000 自动升级</p>
                            </div>
                            <button class="btn btn-primary btn-sm" onclick="editLevel('silver')">编辑</button>
                        </div>
                        
                        <div class="privilege-grid">
                            <div class="privilege-item">
                                <i class="fas fa-percentage"></i>
                                <div>专享折扣</div>
                                <div class="text-muted">9.8折</div>
                            </div>
                            <div class="privilege-item">
                                <i class="fas fa-shipping-fast"></i>
                                <div>包邮门槛</div>
                                <div class="text-muted">¥199</div>
                            </div>
                            <div class="privilege-item">
                                <i class="fas fa-coins"></i>
                                <div>积分倍率</div>
                                <div class="text-muted">1.2倍</div>
                            </div>
                            <div class="privilege-item">
                                <i class="fas fa-gift"></i>
                                <div>生日礼品</div>
                                <div class="text-muted">¥50券包</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="member-level-card">
                        <div class="level-header">
                            <div class="level-icon gold">
                                <i class="fas fa-star"></i>
                            </div>
                            <div style="flex: 1;">
                                <h4>黄金会员</h4>
                                <p>消费满 ¥5,000 自动升级</p>
                            </div>
                            <button class="btn btn-primary btn-sm" onclick="editLevel('gold')">编辑</button>
                        </div>
                        
                        <div class="privilege-grid">
                            <div class="privilege-item">
                                <i class="fas fa-percentage"></i>
                                <div>专享折扣</div>
                                <div class="text-muted">9.5折</div>
                            </div>
                            <div class="privilege-item">
                                <i class="fas fa-shipping-fast"></i>
                                <div>包邮门槛</div>
                                <div class="text-muted">¥99</div>
                            </div>
                            <div class="privilege-item">
                                <i class="fas fa-coins"></i>
                                <div>积分倍率</div>
                                <div class="text-muted">1.5倍</div>
                            </div>
                            <div class="privilege-item">
                                <i class="fas fa-gift"></i>
                                <div>生日礼品</div>
                                <div class="text-muted">¥200券包</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="member-level-card">
                        <div class="level-header">
                            <div class="level-icon diamond">
                                <i class="fas fa-gem"></i>
                            </div>
                            <div style="flex: 1;">
                                <h4>钻石会员</h4>
                                <p>消费满 ¥15,000 自动升级</p>
                            </div>
                            <button class="btn btn-primary btn-sm" onclick="editLevel('diamond')">编辑</button>
                        </div>
                        
                        <div class="privilege-grid">
                            <div class="privilege-item">
                                <i class="fas fa-percentage"></i>
                                <div>专享折扣</div>
                                <div class="text-muted">9.0折</div>
                            </div>
                            <div class="privilege-item">
                                <i class="fas fa-shipping-fast"></i>
                                <div>包邮门槛</div>
                                <div class="text-muted">¥59</div>
                            </div>
                            <div class="privilege-item">
                                <i class="fas fa-coins"></i>
                                <div>积分倍率</div>
                                <div class="text-muted">2倍</div>
                            </div>
                            <div class="privilege-item">
                                <i class="fas fa-gift"></i>
                                <div>生日礼品</div>
                                <div class="text-muted">¥500券包</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 积分系统 -->
                <div id="pointsTab" class="tab-content">
                    <h3>积分系统配置</h3>
                    
                    <div class="points-config">
                        <h4>积分获取规则</h4>
                        <div class="config-grid">
                            <div class="config-item">
                                <label>购物积分比例</label>
                                <input type="number" class="form-control" value="1" min="0" step="0.1">
                                <small class="text-muted">每消费1元获得积分数</small>
                            </div>
                            <div class="config-item">
                                <label>每日签到积分</label>
                                <input type="number" class="form-control" value="10" min="1">
                                <small class="text-muted">每日签到获得固定积分</small>
                            </div>
                            <div class="config-item">
                                <label>邀请好友积分</label>
                                <input type="number" class="form-control" value="200" min="1">
                                <small class="text-muted">成功邀请一个好友获得积分</small>
                            </div>
                            <div class="config-item">
                                <label>评价商品积分</label>
                                <input type="number" class="form-control" value="20" min="1">
                                <small class="text-muted">每次评价商品获得积分</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="points-config">
                        <h4>积分消耗规则</h4>
                        <div class="config-grid">
                            <div class="config-item">
                                <label>积分抵现比例</label>
                                <input type="number" class="form-control" value="100" min="1">
                                <small class="text-muted">多少积分抵扣1元现金</small>
                            </div>
                            <div class="config-item">
                                <label>积分有效期</label>
                                <select class="form-control">
                                    <option value="365">1年</option>
                                    <option value="730">2年</option>
                                    <option value="1095">3年</option>
                                    <option value="0">永久有效</option>
                                </select>
                            </div>
                            <div class="config-item">
                                <label>单次使用上限</label>
                                <input type="number" class="form-control" value="5000" min="1">
                                <small class="text-muted">单次订单最多使用积分数</small>
                            </div>
                            <div class="config-item">
                                <label>积分使用门槛</label>
                                <input type="number" class="form-control" value="100" min="1">
                                <small class="text-muted">订单满多少元才能使用积分</small>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-success" onclick="savePointsConfig()">保存配置</button>
                            <button class="btn btn-secondary" onclick="resetPointsConfig()">重置默认</button>
                        </div>
                    </div>
                </div>

                <!-- 生日特权 -->
                <div id="birthdayTab" class="tab-content">
                    <h3>生日特权管理</h3>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="birthday-calendar">
                                <div class="calendar-header">
                                    2024年1月 - 会员生日分布
                                </div>
                                <!-- 这里可以放置生日日历组件 -->
                                <div style="padding: 20px; text-align: center; color: #666;">
                                    <i class="fas fa-calendar-alt" style="font-size: 48px; margin-bottom: 10px;"></i>
                                    <p>生日日历功能开发中...</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="birthday-stats">
                                <div class="stat-card">
                                    <div class="stat-number">156</div>
                                    <div class="stat-label">本月生日会员</div>
                                </div>
                                
                                <div class="stat-card" style="margin-top: 15px;">
                                    <div class="stat-number">89%</div>
                                    <div class="stat-label">生日券包领取率</div>
                                </div>
                                
                                <div class="stat-card" style="margin-top: 15px;">
                                    <div class="stat-number">¥45,680</div>
                                    <div class="stat-label">生日月消费总额</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="points-config">
                        <h4>生日特权配置</h4>
                        <div class="config-grid">
                            <div class="config-item">
                                <label>白银会员生日券包</label>
                                <input type="number" class="form-control" value="50" min="0">
                                <small class="text-muted">生日券包价值（元）</small>
                            </div>
                            <div class="config-item">
                                <label>黄金会员生日券包</label>
                                <input type="number" class="form-control" value="200" min="0">
                                <small class="text-muted">生日券包价值（元）</small>
                            </div>
                            <div class="config-item">
                                <label>钻石会员生日券包</label>
                                <input type="number" class="form-control" value="500" min="0">
                                <small class="text-muted">生日券包价值（元）</small>
                            </div>
                            <div class="config-item">
                                <label>白金会员生日券包</label>
                                <input type="number" class="form-control" value="1000" min="0">
                                <small class="text-muted">生日券包价值（元）</small>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-success" onclick="saveBirthdayConfig()">保存配置</button>
                        </div>
                    </div>
                </div>

                <!-- 营销活动 -->
                <div id="campaignsTab" class="tab-content">
                    <h3>营销活动管理</h3>
                    
                    <div class="marketing-campaign">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h4>新年会员升级大促</h4>
                                <p class="text-muted">消费满额送积分，邀请好友获得双倍奖励</p>
                                <div>
                                    <span class="campaign-status active">进行中</span>
                                    <span class="ml-2 text-muted">2024-01-01 至 2024-01-31</span>
                                </div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-primary" onclick="editCampaign(1)">编辑</button>
                                <button class="btn btn-sm btn-secondary" onclick="viewCampaign(1)">查看</button>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-sm-3">
                                    <strong>参与人数:</strong> 2,458
                                </div>
                                <div class="col-sm-3">
                                    <strong>转化率:</strong> 18.5%
                                </div>
                                <div class="col-sm-3">
                                    <strong>消费增长:</strong> +32.1%
                                </div>
                                <div class="col-sm-3">
                                    <strong>投入产出比:</strong> 1:4.2
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="marketing-campaign">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h4>会员生日专享月</h4>
                                <p class="text-muted">生日月会员专享折扣和礼品</p>
                                <div>
                                    <span class="campaign-status scheduled">即将开始</span>
                                    <span class="ml-2 text-muted">2024-02-01 至 2024-02-29</span>
                                </div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-primary" onclick="editCampaign(2)">编辑</button>
                                <button class="btn btn-sm btn-secondary" onclick="viewCampaign(2)">查看</button>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-sm-3">
                                    <strong>目标人群:</strong> 1,200+
                                </div>
                                <div class="col-sm-3">
                                    <strong>预算:</strong> ¥50,000
                                </div>
                                <div class="col-sm-3">
                                    <strong>预期转化:</strong> 15%
                                </div>
                                <div class="col-sm-3">
                                    <strong>预期收益:</strong> ¥200,000
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="marketing-campaign">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h4>积分翻倍周末</h4>
                                <p class="text-muted">周末购物积分翻倍活动</p>
                                <div>
                                    <span class="campaign-status ended">已结束</span>
                                    <span class="ml-2 text-muted">2023-12-01 至 2023-12-31</span>
                                </div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-secondary" onclick="viewCampaign(3)">查看报告</button>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-sm-3">
                                    <strong>参与人数:</strong> 8,945
                                </div>
                                <div class="col-sm-3">
                                    <strong>转化率:</strong> 24.7%
                                </div>
                                <div class="col-sm-3">
                                    <strong>消费增长:</strong> +45.2%
                                </div>
                                <div class="col-sm-3">
                                    <strong>投入产出比:</strong> 1:5.8
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../../assets/js/admin-main.js"></script>
    <script>
        // 全局变量
        let memberData = {
            levels: {
                silver: { name: '白银会员', threshold: 1000, discount: 0.98, shipping: 199, points: 1.2, birthday: 50 },
                gold: { name: '黄金会员', threshold: 5000, discount: 0.95, shipping: 99, points: 1.5, birthday: 200 },
                diamond: { name: '钻石会员', threshold: 20000, discount: 0.92, shipping: 0, points: 2.0, birthday: 500 },
                platinum: { name: '白金会员', threshold: 50000, discount: 0.90, shipping: 0, points: 2.5, birthday: 1000 }
            },
            campaigns: [
                { id: 1, name: '新年会员升级大促', status: 'active', startDate: '2024-01-01', endDate: '2024-01-31', participants: 2458, conversion: 18.5, growth: 32.1, roi: 4.2 },
                { id: 2, name: '会员生日专享月', status: 'scheduled', startDate: '2024-02-01', endDate: '2024-02-29', target: 1200, budget: 50000, expectedConversion: 15, expectedRevenue: 200000 },
                { id: 3, name: '积分翻倍周末', status: 'ended', startDate: '2023-12-01', endDate: '2023-12-31', participants: 8945, conversion: 24.7, growth: 45.2, roi: 5.8 }
            ]
        };
        
        // 标签页切换
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            document.getElementById(tabName + 'Tab').classList.add('active');
            event.target.classList.add('active');
        }
        
        // 会员等级管理
        function editLevel(level) {
            const levelData = memberData.levels[level];
            if (!levelData) return;
            
            const modal = createModal(`编辑${levelData.name}`, `
                <form id="editLevelForm" class="level-edit-form">
                    <div class="form-group">
                        <label>升级门槛（消费金额）：</label>
                        <input type="number" id="threshold" class="form-control" value="${levelData.threshold}" min="0" step="100">
                        <small class="text-muted">用户累计消费达到此金额自动升级</small>
                    </div>
                    <div class="form-group">
                        <label>专享折扣：</label>
                        <input type="number" id="discount" class="form-control" value="${(levelData.discount * 100).toFixed(1)}" min="50" max="100" step="0.1">
                        <small class="text-muted">百分比，如95表示9.5折</small>
                    </div>
                    <div class="form-group">
                        <label>包邮门槛：</label>
                        <input type="number" id="shipping" class="form-control" value="${levelData.shipping}" min="0" step="10">
                        <small class="text-muted">满多少元包邮，0表示全部包邮</small>
                    </div>
                    <div class="form-group">
                        <label>积分倍率：</label>
                        <input type="number" id="points" class="form-control" value="${levelData.points}" min="1" max="5" step="0.1">
                        <small class="text-muted">消费获得积分的倍数</small>
                    </div>
                    <div class="form-group">
                        <label>生日礼品价值：</label>
                        <input type="number" id="birthday" class="form-control" value="${levelData.birthday}" min="0" step="10">
                        <small class="text-muted">生日月赠送优惠券价值（元）</small>
                    </div>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveLevelConfig('${level}')">保存</button>
                </div>
            `);
        }
        
        function saveLevelConfig(level) {
            memberData.levels[level] = {
                ...memberData.levels[level],
                threshold: parseInt(document.getElementById('threshold').value),
                discount: parseFloat(document.getElementById('discount').value) / 100,
                shipping: parseInt(document.getElementById('shipping').value),
                points: parseFloat(document.getElementById('points').value),
                birthday: parseInt(document.getElementById('birthday').value)
            };
            
            closeModal();
            showNotification(`${memberData.levels[level].name}配置保存成功！`, 'success');
            setTimeout(() => location.reload(), 1000);
        }
        
        // 积分系统管理
        function savePointsConfig() {
            const configs = document.querySelectorAll('#pointsTab .form-control');
            let isValid = true;
            
            configs.forEach(input => {
                if (input.value === '' || parseFloat(input.value) < 0) {
                    input.style.borderColor = '#dc3545';
                    isValid = false;
                } else {
                    input.style.borderColor = '#ced4da';
                }
            });
            
            if (!isValid) {
                showNotification('请填写正确的配置值', 'error');
                return;
            }
            
            showNotification('积分配置保存成功！', 'success');
            
            const saveBtn = event.target;
            const originalText = saveBtn.textContent;
            saveBtn.textContent = '保存中...';
            saveBtn.disabled = true;
            
            setTimeout(() => {
                saveBtn.textContent = originalText;
                saveBtn.disabled = false;
            }, 1500);
        }
        
        function resetPointsConfig() {
            if (confirm('确定要重置为默认配置吗？此操作不可撤销。')) {
                const defaultValues = [100, 10, 20, 50, 1, 100, 0.01];
                const inputs = document.querySelectorAll('#pointsTab .form-control');
                
                inputs.forEach((input, index) => {
                    if (defaultValues[index] !== undefined) {
                        input.value = defaultValues[index];
                    }
                });
                
                showNotification('配置已重置为默认值', 'success');
            }
        }
        
        // 生日特权管理
        function saveBirthdayConfig() {
            showNotification('生日特权配置保存成功！', 'success');
        }
        
        // 营销活动管理
        function createCampaign() {
            const modal = createModal('创建营销活动', `
                <form id="createCampaignForm" class="campaign-form">
                    <div class="form-group">
                        <label>活动名称：</label>
                        <input type="text" id="campaignName" class="form-control" placeholder="请输入活动名称" required>
                    </div>
                    <div class="form-group">
                        <label>活动描述：</label>
                        <textarea id="campaignDesc" class="form-control" rows="3" placeholder="请输入活动描述"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>开始时间：</label>
                                <input type="date" id="startDate" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>结束时间：</label>
                                <input type="date" id="endDate" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>目标人群：</label>
                                <select id="targetGroup" class="form-control">
                                    <option value="all">全部会员</option>
                                    <option value="silver">白银会员</option>
                                    <option value="gold">黄金会员</option>
                                    <option value="diamond">钻石会员</option>
                                    <option value="platinum">白金会员</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>活动预算：</label>
                                <input type="number" id="budget" class="form-control" min="0" step="100" placeholder="预算金额">
                            </div>
                        </div>
                    </div>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitCampaign()">创建活动</button>
                </div>
            `);
            
            const today = new Date();
            const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
            document.getElementById('startDate').value = today.toISOString().split('T')[0];
            document.getElementById('endDate').value = nextWeek.toISOString().split('T')[0];
        }
        
        function submitCampaign() {
            const name = document.getElementById('campaignName').value.trim();
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!name) {
                showNotification('请输入活动名称', 'error');
                return;
            }
            
            if (new Date(startDate) >= new Date(endDate)) {
                showNotification('结束时间必须晚于开始时间', 'error');
                return;
            }
            
            closeModal();
            showNotification('营销活动创建成功！', 'success');
        }
        
        function editCampaign(id) {
            const campaign = memberData.campaigns.find(c => c.id === id);
            if (!campaign) return;
            
            const modal = createModal(`编辑营销活动 - ${campaign.name}`, `
                <form id="editCampaignForm" class="campaign-form">
                    <div class="form-group">
                        <label>活动名称：</label>
                        <input type="text" id="editCampaignName" class="form-control" value="${campaign.name}" required>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>开始时间：</label>
                                <input type="date" id="editStartDate" class="form-control" value="${campaign.startDate}" required>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>结束时间：</label>
                                <input type="date" id="editEndDate" class="form-control" value="${campaign.endDate}" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>活动状态：</label>
                        <select id="editStatus" class="form-control">
                            <option value="scheduled" ${campaign.status === 'scheduled' ? 'selected' : ''}>即将开始</option>
                            <option value="active" ${campaign.status === 'active' ? 'selected' : ''}>进行中</option>
                            <option value="paused" ${campaign.status === 'paused' ? 'selected' : ''}>已暂停</option>
                            <option value="ended" ${campaign.status === 'ended' ? 'selected' : ''}>已结束</option>
                        </select>
                    </div>
                </form>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" onclick="deleteCampaign(${id})">删除活动</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateCampaign(${id})">保存修改</button>
                </div>
            `);
        }
        
        function updateCampaign(id) {
            closeModal();
            showNotification('活动信息更新成功！', 'success');
        }
        
        function deleteCampaign(id) {
            if (confirm('确定要删除这个营销活动吗？此操作不可撤销。')) {
                closeModal();
                showNotification('活动已删除', 'success');
            }
        }
        
        function viewCampaign(id) {
            const campaign = memberData.campaigns.find(c => c.id === id);
            if (!campaign) return;
            
            const modal = createModal(`活动详情 - ${campaign.name}`, `
                <div class="campaign-details">
                    <div class="detail-section">
                        <h4>基本信息</h4>
                        <div class="row">
                            <div class="col-sm-6">
                                <strong>活动名称：</strong> ${campaign.name}
                            </div>
                            <div class="col-sm-6">
                                <strong>活动状态：</strong> 
                                <span class="campaign-status ${campaign.status}">
                                    ${campaign.status === 'active' ? '进行中' : 
                                      campaign.status === 'scheduled' ? '即将开始' : 
                                      campaign.status === 'ended' ? '已结束' : '已暂停'}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="exportCampaignReport(${id})">导出报告</button>
                </div>
            `);
        }
        
        function exportCampaignReport(id) {
            showNotification('正在生成活动报告...', 'info');
            setTimeout(() => {
                showNotification('活动报告导出成功！', 'success');
            }, 2000);
        }
        
        // 数据导出
        function exportMemberData() {
            const modal = createModal('导出会员数据', `
                <div class="export-options">
                    <div class="form-group">
                        <label>导出格式：</label>
                        <select id="exportFormat" class="form-control">
                            <option value="excel">Excel (.xlsx)</option>
                            <option value="csv">CSV (.csv)</option>
                            <option value="pdf">PDF报告</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>导出内容：</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" checked> 会员基本信息</label>
                            <label><input type="checkbox" checked> 会员等级分布</label>
                            <label><input type="checkbox" checked> 消费记录</label>
                            <label><input type="checkbox"> 积分明细</label>
                            <label><input type="checkbox"> 营销活动参与记录</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="executeExport()">开始导出</button>
                </div>
            `);
        }
        
        function executeExport() {
            const selectedOptions = Array.from(document.querySelectorAll('.checkbox-group input:checked')).length;
            
            if (selectedOptions === 0) {
                showNotification('请至少选择一项导出内容', 'warning');
                return;
            }
            
            closeModal();
            showNotification('正在导出数据，请稍候...', 'info');
            
            setTimeout(() => {
                showNotification('数据导出完成！', 'success');
            }, 2000);
        }
        
        // 实现图表功能
        function initCharts() {
            initMemberGrowthChart();
            initMemberDistributionChart();
        }
        
        function initMemberGrowthChart() {
            const canvas = document.getElementById('memberGrowthChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            
            ctx.clearRect(0, 0, width, height);
            
            const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
            const data = [8500, 9200, 10800, 12300, 14100, 15842];
            
            drawLineChart(ctx, width, height, months, data);
        }
        
        function initMemberDistributionChart() {
            const canvas = document.getElementById('memberDistributionChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            
            ctx.clearRect(0, 0, width, height);
            
            const levels = ['白银', '黄金', '钻石', '白金'];
            const data = [8950, 4568, 1892, 432];
            const colors = ['#BDC3C7', '#F39C12', '#3498DB', '#9B59B6'];
            
            drawPieChart(ctx, width, height, levels, data, colors);
        }
        
        // 绘制折线图
        function drawLineChart(ctx, width, height, labels, data) {
            const padding = 50;
            const chartWidth = width - padding * 2;
            const chartHeight = height - padding * 2;
            
            const maxValue = Math.max(...data);
            const minValue = Math.min(...data);
            const range = maxValue - minValue;
            
            // 绘制坐标轴
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(padding, padding);
            ctx.lineTo(padding, height - padding);
            ctx.lineTo(width - padding, height - padding);
            ctx.stroke();
            
            // 绘制数据点和连线
            ctx.strokeStyle = '#3498db';
            ctx.fillStyle = '#3498db';
            ctx.lineWidth = 2;
            
            const stepX = chartWidth / (labels.length - 1);
            
            ctx.beginPath();
            for (let i = 0; i < data.length; i++) {
                const x = padding + i * stepX;
                const y = height - padding - ((data[i] - minValue) / range) * chartHeight;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
                
                ctx.save();
                ctx.beginPath();
                ctx.arc(x, y, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                
                ctx.save();
                ctx.fillStyle = '#666';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(labels[i], x, height - padding + 20);
                ctx.fillText(data[i].toLocaleString(), x, y - 10);
                ctx.restore();
            }
            ctx.stroke();
        }
        
        // 绘制饼图
        function drawPieChart(ctx, width, height, labels, data, colors) {
            const centerX = width / 2;
            const centerY = height / 2 + 10;
            const radius = Math.min(width, height) / 3;
            
            const total = data.reduce((sum, value) => sum + value, 0);
            let currentAngle = -Math.PI / 2;
            
            for (let i = 0; i < data.length; i++) {
                const sliceAngle = (data[i] / total) * 2 * Math.PI;
                
                ctx.fillStyle = colors[i];
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                ctx.closePath();
                ctx.fill();
                
                const labelAngle = currentAngle + sliceAngle / 2;
                const labelX = centerX + Math.cos(labelAngle) * (radius + 30);
                const labelY = centerY + Math.sin(labelAngle) * (radius + 30);
                
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`${labels[i]}`, labelX, labelY);
                ctx.fillText(`${data[i].toLocaleString()}`, labelX, labelY + 15);
                ctx.fillText(`${((data[i] / total) * 100).toFixed(1)}%`, labelX, labelY + 30);
                
                currentAngle += sliceAngle;
            }
        }
        
        // 通用模态框函数
        function createModal(title, content) {
            const modal = document.createElement('div');
            modal.className = 'custom-modal';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.5); z-index: 1000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px; box-sizing: border-box;
            `;
            
            modal.innerHTML = `
                <div class="modal-content" style="
                    background: white; border-radius: 8px; 
                    max-width: 600px; width: 100%; max-height: 90vh; 
                    overflow-y: auto; box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                ">
                    <div class="modal-header" style="
                        padding: 20px; border-bottom: 1px solid #eee;
                        display: flex; justify-content: space-between; align-items: center;
                    ">
                        <h3 style="margin: 0; color: #333;">${title}</h3>
                        <button onclick="closeModal()" style="
                            background: none; border: none; font-size: 24px;
                            cursor: pointer; color: #999; padding: 0; width: 30px; height: 30px;
                        ">&times;</button>
                    </div>
                    <div class="modal-body" style="padding: 20px;">
                        ${content}
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            return modal;
        }
        
        function closeModal() {
            const modal = document.querySelector('.custom-modal');
            if (modal) modal.remove();
        }
        
        // 通知函数
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                padding: 15px 20px; border-radius: 6px; color: white;
                font-size: 14px; max-width: 300px; word-wrap: break-word;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transform: translateX(100%); transition: transform 0.3s ease;
                ${type === 'success' ? 'background: #27ae60;' : 
                  type === 'error' ? 'background: #e74c3c;' : 
                  type === 'warning' ? 'background: #f39c12;' : 'background: #3498db;'}
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            
            document.querySelectorAll('input[type="number"]').forEach(input => {
                input.addEventListener('input', function() {
                    if (this.value < 0) {
                        this.style.borderColor = '#dc3545';
                    } else {
                        this.style.borderColor = '#ced4da';
                    }
                });
            });
            
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeModal();
                }
            });
        });
    </script>
</body>
</html> 