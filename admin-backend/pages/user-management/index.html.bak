<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>用户管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/user-management.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="../../assets/css/header-button-fix.css">
    <link rel="stylesheet" href="../../assets/css/table-enhancement.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="../../assets/js/image-error-handler.js"></script>
    <style>
        /* 用户管理页面专用样式 */
        * {
            box-sizing: border-box;
        }
        
        .user-management-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
            width: 100%;
            overflow-x: hidden;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }
        
        .page-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }
        
        .action-btn:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
            text-decoration: none;
        }
        
        .action-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .action-btn.primary:hover {
            background: #2563eb;
        }
        
        .action-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .action-btn.danger:hover {
            background: #dc2626;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .stat-title {
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
            word-break: break-all;
        }
        
        .stat-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .stat-change.positive {
            color: #10b981;
        }
        
        .stat-change.negative {
            color: #ef4444;
        }
        
        .main-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .content-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }
        
        .search-filters {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            background: #f8fafc;
        }
        
        .filter-row {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
            min-width: 150px;
        }
        
        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
        }
        
        .filter-input {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            min-width: 150px;
        }
        
        .filter-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            min-width: 150px;
            background: white;
        }
        
        .filter-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .user-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1000px;
        }
        
        .user-table th,
        .user-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        
        .user-table th {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: #f8fafc;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .user-table td {
            font-size: 14px;
            color: #1e293b;
        }
        
        .user-table tr:hover {
            background: #f8fafc;
        }
        
        /* 固定各列宽度 */
        .user-table th:nth-child(1),
        .user-table td:nth-child(1) { width: 40px; }
        .user-table th:nth-child(2),
        .user-table td:nth-child(2) { width: 220px; }
        .user-table th:nth-child(3),
        .user-table td:nth-child(3) { width: 120px; }
        .user-table th:nth-child(4),
        .user-table td:nth-child(4) { width: 100px; }
        .user-table th:nth-child(5),
        .user-table td:nth-child(5) { width: 120px; }
        .user-table th:nth-child(6),
        .user-table td:nth-child(6) { width: 100px; }
        .user-table th:nth-child(7),
        .user-table td:nth-child(7) { width: 100px; }
        .user-table th:nth-child(8),
        .user-table td:nth-child(8) { width: 100px; }
        .user-table th:nth-child(9),
        .user-table td:nth-child(9) { width: 160px; }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            background: #e2e8f0;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 0;
            width: 100%;
        }
        
        .user-details {
            flex: 1;
            min-width: 0;
            overflow: hidden;
        }
        
        .user-name {
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 2px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .user-email {
            font-size: 12px;
            color: #64748b;
            margin: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            min-width: 60px;
        }
        
        .status-active {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .role-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            background: #e0e7ff;
            color: #3730a3;
        }
        
        .action-buttons {
            display: flex;
            gap: 4px;
            justify-content: center;
            flex-wrap: nowrap;
            min-width: 0;
        }
        
        .action-icon-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            background: #f1f5f9;
            color: #64748b;
            flex-shrink: 0;
        }
        
        .action-icon-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .action-icon-btn.primary {
            background: #eff6ff;
            color: #2563eb;
        }
        
        .action-icon-btn.primary:hover {
            background: #dbeafe;
        }
        
        .action-icon-btn.danger {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .action-icon-btn.danger:hover {
            background: #fee2e2;
        }
        
        .action-icon-btn.warning {
            background: #fef3c7;
            color: #d97706;
        }
        
        .action-icon-btn.warning:hover {
            background: #fcd34d;
        }
        
        .action-icon-btn.success {
            background: #f0fdf4;
            color: #16a34a;
        }
        
        .action-icon-btn.success:hover {
            background: #dcfce7;
        }
        
        .pagination {
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #e2e8f0;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .pagination-info {
            font-size: 14px;
            color: #64748b;
        }
        
        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .pagination-btn:hover:not(:disabled) {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }
        
        .pagination-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .user-management-page {
                padding: 16px;
            }
            
            .page-header {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
            
            .page-title {
                font-size: 24px;
            }
            
            .page-actions {
                justify-content: stretch;
            }
            
            .action-btn {
                flex: 1;
                justify-content: center;
            }
            
            .stats-overview {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .stat-card {
                padding: 16px;
            }
            
            .content-header {
                padding: 16px;
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-filters {
                padding: 16px;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                min-width: auto;
            }
            
            .filter-input,
            .filter-select {
                min-width: auto;
                width: 100%;
            }
            
            .table-container {
                margin: 0 -16px;
            }
            
            .user-table {
                min-width: 600px;
            }
            
            .pagination {
                padding: 16px;
                justify-content: center;
            }
            
            .pagination-info {
                order: 2;
                width: 100%;
                text-align: center;
                margin-top: 8px;
            }
        }
        
        @media (max-width: 480px) {
            .stats-overview {
                grid-template-columns: 1fr;
            }
            
            .page-actions {
                flex-direction: column;
            }
            
            .user-table {
                font-size: 12px;
            }
            
            .user-table th,
            .user-table td {
                padding: 8px 12px;
            }
            
            .table-actions {
                flex-direction: column;
                gap: 4px;
            }
        }
        
        /* 批量操作栏样式 */
        .bulk-actions {
            padding: 16px 24px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            display: none;
            animation: slideDown 0.3s ease-out;
        }
        
        .bulk-actions.show {
            display: block;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .bulk-controls {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        
        .selected-count {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-right: auto;
        }
        
        .selected-count span {
            color: #3b82f6;
            font-weight: 600;
        }
        
        /* 批量操作按钮样式 */
        .bulk-controls .action-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #374151;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
            font-weight: 500;
        }
        
        .bulk-controls .action-btn:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .bulk-controls .action-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .bulk-controls .action-btn.primary:hover {
            background: #2563eb;
            border-color: #2563eb;
        }
        
        .bulk-controls .action-btn.success {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }
        
        .bulk-controls .action-btn.success:hover {
            background: #059669;
            border-color: #059669;
        }
        
        .bulk-controls .action-btn.warning {
            background: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }
        
        .bulk-controls .action-btn.warning:hover {
            background: #d97706;
            border-color: #d97706;
        }
        
        .bulk-controls .action-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .bulk-controls .action-btn.danger:hover {
            background: #dc2626;
            border-color: #dc2626;
        }
        
        .bulk-controls .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .bulk-controls .action-btn i {
            font-size: 14px;
        }
        
        /* 确保批量操作按钮文字正确显示 */
        .bulk-controls .action-btn {
            font-size: 14px !important;
            color: inherit !important;
            text-indent: 0 !important;
            text-transform: none !important;
            letter-spacing: normal !important;
            word-spacing: normal !important;
            white-space: nowrap !important;
            overflow: visible !important;
            text-overflow: visible !important;
            opacity: 1 !important;
            visibility: visible !important;
            text-decoration: none !important;
            min-width: auto !important;
            width: auto !important;
            padding: 8px 16px !important;
            box-sizing: border-box !important;
        }
        
        .bulk-controls .action-btn.warning {
            background: #f59e0b !important;
            color: white !important;
            border-color: #f59e0b !important;
        }
        
        .bulk-controls .action-btn.warning:hover {
            background: #d97706 !important;
            border-color: #d97706 !important;
            color: white !important;
        }
        
        .bulk-controls .action-btn.danger {
            background: #ef4444 !important;
            color: white !important;
            border-color: #ef4444 !important;
        }
        
        .bulk-controls .action-btn.danger:hover {
            background: #dc2626 !important;
            border-color: #dc2626 !important;
            color: white !important;
        }
        
        /* 强制修复按钮文字显示问题 */
        .bulk-controls .action-btn {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 6px !important;
            min-width: 80px !important;
            max-width: none !important;
            width: auto !important;
            height: auto !important;
            line-height: 1.4 !important;
            text-align: center !important;
            vertical-align: middle !important;
        }
        
        .bulk-controls .action-btn span,
        .bulk-controls .action-btn:not(:has(span)) {
            display: inline !important;
            white-space: nowrap !important;
            overflow: visible !important;
            text-overflow: clip !important;
            max-width: none !important;
            width: auto !important;
        }
        
        /* 确保图标和文字都正确显示 */
        .bulk-controls .action-btn i {
            display: inline !important;
            margin-right: 4px !important;
            flex-shrink: 0 !important;
        }
        
        /* 响应式批量操作 */
        @media (max-width: 768px) {
            .bulk-actions {
                padding: 12px 16px;
            }
            
            .bulk-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
            
            .selected-count {
                text-align: center;
                margin-right: 0;
                margin-bottom: 8px;
            }
            
            .bulk-controls .action-btn {
                justify-content: center;
                padding: 10px 16px !important;
                min-width: 120px !important;
                width: auto !important;
                flex: none !important;
            }
        }
        
        @media (max-width: 480px) {
            .bulk-controls {
                gap: 8px;
            }
            
            .bulk-controls .action-btn {
                padding: 8px 12px !important;
                font-size: 13px !important;
                min-width: 100px !important;
                width: auto !important;
                white-space: nowrap !important;
                overflow: visible !important;
            }
            
            .bulk-controls .action-btn i {
                font-size: 12px !important;
                margin-right: 4px !important;
            }
        }
        
        /* 图片占位符样式 */
        .image-placeholder {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-size: 16px;
        }
        
        /* 确保所有元素都有正确的盒模型 */
        *, *::before, *::after {
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="user-management-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">用户管理</h1>
                <p style="color: #64748b; margin: 4px 0 0 0;">管理和监控 TeleShop 平台用户</p>
            </div>
            <div class="page-actions">
                <a href="../dashboard/index.html" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i> 仪表板
                </a>
                <a href="batch-operations.html" class="action-btn">
                    <i class="fas fa-tasks"></i> 批量操作
                </a>
                <a href="member-marketing.html" class="action-btn">
                    <i class="fas fa-bullhorn"></i> 会员营销
                </a>
                <button class="action-btn" onclick="exportUsers()">
                    <i class="fas fa-download"></i> 导出用户
                </button>
                <button class="action-btn" onclick="importUsers()">
                    <i class="fas fa-upload"></i> 批量导入
                </button>
                <a href="edit-user.html" class="action-btn primary">
                    <i class="fas fa-plus"></i> 添加用户
                </a>
            </div>
        </div>
        
        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">总用户数</span>
                    <div class="stat-icon" style="background: #dbeafe; color: #3b82f6;">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value">12,847</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12.5% 本月</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">活跃用户</span>
                    <div class="stat-icon" style="background: #dcfce7; color: #10b981;">
                        <i class="fas fa-user-check"></i>
                    </div>
                </div>
                <div class="stat-value">8,234</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8.2% 今日</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">商家用户</span>
                    <div class="stat-icon" style="background: #fef3c7; color: #f59e0b;">
                        <i class="fas fa-store"></i>
                    </div>
                </div>
                <div class="stat-value">1,456</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+3.8% 本周</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">新注册</span>
                    <div class="stat-icon" style="background: #f3e8ff; color: #8b5cf6;">
                        <i class="fas fa-user-plus"></i>
                    </div>
                </div>
                <div class="stat-value">234</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i>
                    <span>-2.1% 今日</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">VIP用户</span>
                    <div class="stat-icon" style="background: #fce7f3; color: #ec4899;">
                        <i class="fas fa-crown"></i>
                    </div>
                </div>
                <div class="stat-value">567</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+15.3% 本月</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">封禁用户</span>
                    <div class="stat-icon" style="background: #fee2e2; color: #ef4444;">
                        <i class="fas fa-user-slash"></i>
                    </div>
                </div>
                <div class="stat-value">89</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+5 本周</span>
                </div>
            </div>
        </div>
        
        <!-- 主要内容 -->
        <div class="main-content">
            <div class="content-header">
                <h3 class="content-title">用户列表</h3>
                <div class="page-actions">
                    <button class="action-btn" onclick="refreshUsers()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button class="action-btn" onclick="toggleBulkActions()">
                        <i class="fas fa-check-square"></i> 批量操作
                    </button>
                </div>
            </div>
            
            <!-- 搜索和过滤 -->
            <div class="search-filters">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">搜索用户</label>
                        <input type="text" class="filter-input" placeholder="姓名、邮箱、ID..." 
                               onkeyup="searchUsers(this.value)">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">用户状态</label>
                        <select class="filter-select" onchange="filterByStatus(this.value)">
                            <option value="">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">非活跃</option>
                            <option value="pending">待验证</option>
                            <option value="banned">已封禁</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">用户角色</label>
                        <select class="filter-select" onchange="filterByRole(this.value)">
                            <option value="">全部角色</option>
                            <option value="user">普通用户</option>
                            <option value="seller">商家</option>
                            <option value="vip">VIP用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">注册来源</label>
                        <select class="filter-select" onchange="filterBySource(this.value)">
                            <option value="">全部来源</option>
                            <option value="telegram">Telegram</option>
                            <option value="web">网站</option>
                            <option value="referral">推荐</option>
                            <option value="api">API</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">注册时间</label>
                        <select class="filter-select" onchange="filterByRegistration(this.value)">
                            <option value="">全部时间</option>
                            <option value="today">今日</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                            <option value="quarter">本季度</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- 批量操作栏 -->
            <div class="bulk-actions" id="bulkActions">
                <div class="bulk-controls">
                    <span class="selected-count">已选择 <span id="selectedCount">0</span> 个用户</span>
                    <button class="action-btn primary" onclick="bulkExport()">
                        <i class="fas fa-download"></i> 导出选中
                    </button>
                    <button class="action-btn success" onclick="bulkActivate()">
                        <i class="fas fa-check"></i> 激活
                    </button>
                    <button class="action-btn warning" onclick="bulkDeactivate()">
                        <i class="fas fa-pause"></i> 停用
                    </button>
                    <button class="action-btn danger" onclick="bulkDelete()">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
            
            <!-- 用户表格 -->
            <table class="users-table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" onchange="toggleSelectAll(this.checked)">
                        </th>
                        <th>用户信息</th>
                        <th>状态</th>
                        <th>角色</th>
                        <th>钱包余额</th>
                        <th>注册来源</th>
                        <th>最后活跃</th>
                        <th>注册时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="1"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b332844c?w=40&h=40&fit=crop" alt="Alice" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Alice Chen</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-active">活跃</span>
                            <div class="last-active">
                                <span class="online-indicator"></span>在线
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-vip">VIP</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥12,458.50</div>
                            <div style="font-size: 12px; color: #64748b;">$1,867.23</div>
                        </td>
                        <td>
                            <span class="registration-source">Telegram</span>
                        </td>
                        <td>
                            <div class="last-active">刚刚</div>
                        </td>
                        <td>2024-01-15</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(1)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(1)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="loginAsUser(1)" title="登录为该用户">
                                    <i class="fas fa-sign-in-alt"></i>
                                </button>
                                <button class="action-icon-btn danger" onclick="banUser(1)" title="封禁">
                                    <i class="fas fa-ban"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="2"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop" alt="Bob" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Bob Wang</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-pending">待验证</span>
                            <div class="last-active">
                                <span class="offline-indicator"></span>8分钟前
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-seller">商家</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥8,234.00</div>
                            <div style="font-size: 12px; color: #64748b;">$1,234.56</div>
                        </td>
                        <td>
                            <span class="registration-source">Web</span>
                        </td>
                        <td>
                            <div class="last-active">8分钟前</div>
                        </td>
                        <td>2024-02-20</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(2)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(2)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="verifyUser(2)" title="验证用户">
                                    <i class="fas fa-check-circle"></i>
                                </button>
                                <button class="action-icon-btn danger" onclick="banUser(2)" title="封禁">
                                    <i class="fas fa-ban"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="3"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop" alt="Charlie" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Charlie Li</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-active">活跃</span>
                            <div class="last-active">
                                <span class="offline-indicator"></span>2小时前
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-user">用户</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥567.80</div>
                            <div style="font-size: 12px; color: #64748b;">$85.17</div>
                        </td>
                        <td>
                            <span class="registration-source">推荐</span>
                        </td>
                        <td>
                            <div class="last-active">2小时前</div>
                        </td>
                        <td>2024-03-10</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(3)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(3)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="upgradeToVip(3)" title="升级VIP">
                                    <i class="fas fa-crown"></i>
                                </button>
                                <button class="action-icon-btn danger" onclick="banUser(3)" title="封禁">
                                    <i class="fas fa-ban"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="4"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop" alt="David" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">David Zhang</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-banned">已封禁</span>
                            <div class="last-active">
                                <span class="offline-indicator"></span>3天前
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-user">用户</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥0.00</div>
                            <div style="font-size: 12px; color: #64748b;">$0.00</div>
                        </td>
                        <td>
                            <span class="registration-source">Telegram</span>
                        </td>
                        <td>
                            <div class="last-active">3天前</div>
                        </td>
                        <td>2024-02-28</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(4)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(4)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="unbanUser(4)" title="解封">
                                    <i class="fas fa-unlock"></i>
                                </button>
                                <button class="action-icon-btn danger" onclick="deleteUser(4)" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="5"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=40&h=40&fit=crop" alt="Eve" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Eve Johnson</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-inactive">非活跃</span>
                            <div class="last-active">
                                <span class="offline-indicator"></span>1周前
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-admin">管理员</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥25,689.00</div>
                            <div style="font-size: 12px; color: #64748b;">$3,853.35</div>
                        </td>
                        <td>
                            <span class="registration-source">API</span>
                        </td>
                        <td>
                            <div class="last-active">1周前</div>
                        </td>
                        <td>2023-12-01</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(5)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(5)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="resetPassword(5)" title="重置密码">
                                    <i class="fas fa-key"></i>
                                </button>
                                <button class="action-icon-btn" onclick="managePermissions(5)" title="权限管理">
                                    <i class="fas fa-shield-alt"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="6"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop" alt="Frank" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Frank Wilson</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-pending">待验证</span>
                            <div class="last-active">
                                <span class="offline-indicator"></span>5天前
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-user">用户</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥156.90</div>
                            <div style="font-size: 12px; color: #64748b;">$23.54</div>
                        </td>
                        <td>
                            <span class="registration-source">Web</span>
                        </td>
                        <td>
                            <div class="last-active">5天前</div>
                        </td>
                        <td>2024-03-15</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(6)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn success" onclick="verifyUser(6)" title="验证用户">
                                    <i class="fas fa-check-circle"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(6)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn danger" onclick="banUser(6)" title="封禁">
                                    <i class="fas fa-ban"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" data-user-id="7"></td>
                        <td>
                            <div class="user-info">
                                <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=40&h=40&fit=crop" alt="Grace" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">Grace Taylor</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-active">活跃</span>
                            <div class="last-active">
                                <span class="online-indicator"></span>在线
                            </div>
                        </td>
                        <td>
                            <span class="role-badge role-seller">商家</span>
                        </td>
                        <td>
                            <div class="wallet-amount">¥45,678.20</div>
                            <div style="font-size: 12px; color: #64748b;">$6,851.73</div>
                        </td>
                        <td>
                            <span class="registration-source">API</span>
                        </td>
                        <td>
                            <div class="last-active">刚刚</div>
                        </td>
                        <td>2023-11-30</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-icon-btn primary" onclick="viewUser(7)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-icon-btn" onclick="editUser(7)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-icon-btn" onclick="loginAsUser(7)" title="登录为该用户">
                                    <i class="fas fa-sign-in-alt"></i>
                                </button>
                                <button class="action-icon-btn" onclick="manageStore(7)" title="管理店铺">
                                    <i class="fas fa-store"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示 1-20 条，共 12,847 条记录
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>上一页</button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn">...</button>
                    <button class="pagination-btn">643</button>
                    <button class="pagination-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let selectedUsers = new Set();
        let bulkActionsVisible = false;
        
        // 搜索用户
        function searchUsers(query) {
            if (query.trim()) {
                showNotification(`搜索用户: ${query}`, 'info');
            }
        }
        
        // 按状态过滤
        function filterByStatus(status) {
            showNotification(`已过滤到 ${status || '全部'} 状态用户`, 'info');
        }
        
        // 按角色过滤
        function filterByRole(role) {
            showNotification(`已过滤到 ${role || '全部'} 角色用户`, 'info');
        }
        
        // 按来源过滤
        function filterBySource(source) {
            showNotification(`已过滤到 ${source || '全部'} 来源用户`, 'info');
        }
        
        // 按注册时间过滤
        function filterByRegistration(time) {
            showNotification(`已过滤到 ${time || '全部'} 时间用户`, 'info');
        }
        
        // 刷新用户列表
        function refreshUsers() {
            showNotification('正在刷新用户列表...', 'info');
            setTimeout(() => {
                showNotification('用户列表已更新！', 'success');
            }, 1500);
        }
        
        // 切换批量操作
        function toggleBulkActions() {
            bulkActionsVisible = !bulkActionsVisible;
            const bulkActions = document.getElementById('bulkActions');
            if (bulkActionsVisible) {
                bulkActions.classList.add('show');
            } else {
                bulkActions.classList.remove('show');
                // 取消所有选择
                document.querySelectorAll('.user-checkbox').forEach(cb => cb.checked = false);
                selectedUsers.clear();
                updateSelectedCount();
            }
        }
        
        // 全选/取消全选
        function toggleSelectAll(checked) {
            document.querySelectorAll('.user-checkbox').forEach(cb => {
                cb.checked = checked;
                const userId = cb.getAttribute('data-user-id');
                if (checked) {
                    selectedUsers.add(userId);
                } else {
                    selectedUsers.delete(userId);
                }
            });
            updateSelectedCount();
        }
        
        // 更新选中数量
        function updateSelectedCount() {
            document.getElementById('selectedCount').textContent = selectedUsers.size;
            
            // 自动显示/隐藏批量操作栏
            const bulkActions = document.getElementById('bulkActions');
            if (selectedUsers.size > 0) {
                bulkActions.classList.add('show');
                bulkActionsVisible = true;
            } else {
                bulkActions.classList.remove('show');
                bulkActionsVisible = false;
            }
        }
        
        // 单个用户选择
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('user-checkbox')) {
                const userId = e.target.getAttribute('data-user-id');
                if (e.target.checked) {
                    selectedUsers.add(userId);
                } else {
                    selectedUsers.delete(userId);
                }
                updateSelectedCount();
            }
        });
        
        // 查看用户详情
        function viewUser(userId) {
            showNotification(`正在打开用户 ${userId} 详情页面...`, 'info');
            setTimeout(() => {
                window.open(`user-detail.html?id=${userId}`, '_blank');
            }, 500);
        }
        
        // 编辑用户
        function editUser(userId) {
            showNotification(`正在打开用户 ${userId} 编辑页面...`, 'info');
            setTimeout(() => {
                window.open(`edit-user.html?id=${userId}`, '_blank');
            }, 500);
        }
        
        // 登录为用户
        function loginAsUser(userId) {
            if (confirm('确定要以该用户身份登录吗？')) {
                showNotification(`正在切换到用户 ${userId}...`, 'info');
            }
        }
        
        // 封禁用户
        function banUser(userId) {
            if (confirm('确定要封禁这个用户吗？')) {
                showNotification(`用户 ${userId} 已被封禁`, 'success');
            }
        }
        
        // 解封用户
        function unbanUser(userId) {
            if (confirm('确定要解封这个用户吗？')) {
                showNotification(`用户 ${userId} 已解封`, 'success');
            }
        }
        
        // 删除用户
        function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？此操作不可恢复！')) {
                showNotification(`用户 ${userId} 已删除`, 'success');
            }
        }
        
        // 验证用户
        function verifyUser(userId) {
            showNotification(`用户 ${userId} 已验证`, 'success');
        }
        
        // 升级为VIP
        function upgradeToVip(userId) {
            if (confirm('确定要将该用户升级为VIP吗？')) {
                showNotification(`用户 ${userId} 已升级为VIP`, 'success');
            }
        }
        
        // 重置密码
        function resetPassword(userId) {
            if (confirm('确定要重置该用户密码吗？')) {
                showNotification(`用户 ${userId} 密码重置邮件已发送`, 'success');
            }
        }
        
        // 权限管理
        function managePermissions(userId) {
            showNotification(`打开用户 ${userId} 权限管理`, 'info');
        }
        
        // 添加用户
        function addUser() {
            showNotification('打开添加用户对话框', 'info');
        }
        
        // 导出用户
        function exportUsers() {
            showExportModal();
        }
        
        // 导入用户
        function importUsers() {
            showImportModal();
        }
        
        // 批量操作
        function bulkExport() {
            if (selectedUsers.size === 0) {
                showNotification('请先选择要导出的用户', 'warning');
                return;
            }
            showNotification(`正在导出 ${selectedUsers.size} 个用户数据...`, 'info');
            
            // 模拟导出过程
            setTimeout(() => {
                // 创建并下载CSV文件
                const csvData = generateUserCSV(Array.from(selectedUsers));
                downloadCSV(csvData, `users_export_${new Date().toISOString().split('T')[0]}.csv`);
                showNotification(`成功导出 ${selectedUsers.size} 个用户数据`, 'success');
            }, 2000);
        }
        
        // 生成CSV数据
        function generateUserCSV(userIds) {
            const headers = ['用户ID', '用户名', '邮箱', '状态', '角色', '注册时间'];
            const csvContent = [
                headers.join(','),
                ...userIds.map(id => `${id},用户${id},user${id}@example.com,活跃,普通用户,2024-01-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`)
            ].join('\n');
            return csvContent;
        }
        
        // 下载CSV文件
        function downloadCSV(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        function bulkActivate() {
            if (selectedUsers.size === 0) {
                showNotification('请先选择要激活的用户', 'warning');
                return;
            }
            showNotification(`已激活 ${selectedUsers.size} 个用户`, 'success');
        }
        
        function bulkDeactivate() {
            if (selectedUsers.size === 0) {
                showNotification('请先选择要停用的用户', 'warning');
                return;
            }
            showNotification(`已停用 ${selectedUsers.size} 个用户`, 'success');
        }
        
        function bulkDelete() {
            if (selectedUsers.size === 0) {
                showNotification('请先选择要删除的用户', 'warning');
                return;
            }
            if (confirm(`确定要删除选中的 ${selectedUsers.size} 个用户吗？此操作不可恢复！`)) {
                showNotification(`已删除 ${selectedUsers.size} 个用户`, 'success');
                selectedUsers.clear();
                updateSelectedCount();
            }
        }
        
        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg text-white text-sm font-medium transition-all duration-300 ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 
                type === 'warning' ? 'bg-orange-500' : 'bg-blue-500'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // ==================== 用户导出功能 ====================
        
        function showExportModal() {
            const modal = createModal('导出用户数据', `
                <div class="export-options">
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #374151;">导出格式：</label>
                        <select id="exportFormat" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                            <option value="xlsx">Excel 文件 (.xlsx)</option>
                            <option value="csv">CSV 文件 (.csv)</option>
                            <option value="json">JSON 文件 (.json)</option>
                            <option value="pdf">PDF 报告 (.pdf)</option>
                        </select>
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #374151;">导出范围：</label>
                        <div class="radio-group" style="display: flex; flex-direction: column; gap: 8px;">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="radio" name="exportRange" value="all" checked style="margin: 0;">
                                <span>所有用户 (12,847个)</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="radio" name="exportRange" value="filtered" style="margin: 0;">
                                <span>当前筛选结果</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="radio" name="exportRange" value="active" style="margin: 0;">
                                <span>仅活跃用户 (8,234个)</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="radio" name="exportRange" value="vip" style="margin: 0;">
                                <span>仅VIP用户 (567个)</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #374151;">包含字段：</label>
                        <div class="checkbox-group" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" checked style="margin: 0;">
                                <span>用户ID</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" checked style="margin: 0;">
                                <span>用户名</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" checked style="margin: 0;">
                                <span>邮箱地址</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" checked style="margin: 0;">
                                <span>手机号码</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" checked style="margin: 0;">
                                <span>用户状态</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" checked style="margin: 0;">
                                <span>用户角色</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" style="margin: 0;">
                                <span>钱包余额</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" style="margin: 0;">
                                <span>注册来源</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" checked style="margin: 0;">
                                <span>注册时间</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" style="margin: 0;">
                                <span>最后活跃时间</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #374151;">时间范围：</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                            <div>
                                <label style="display: block; margin-bottom: 4px; font-size: 12px; color: #6b7280;">开始日期</label>
                                <input type="date" id="exportStartDate" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 4px; font-size: 12px; color: #6b7280;">结束日期</label>
                                <input type="date" id="exportEndDate" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <button onclick="closeModal()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; font-size: 14px; cursor: pointer;">取消</button>
                    <button onclick="executeExport()" style="padding: 8px 16px; border: 1px solid #3b82f6; border-radius: 6px; background: #3b82f6; color: white; font-size: 14px; cursor: pointer;">开始导出</button>
                </div>
            `);
            
            // 设置默认日期
            const today = new Date();
            const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            document.getElementById('exportStartDate').value = lastMonth.toISOString().split('T')[0];
            document.getElementById('exportEndDate').value = today.toISOString().split('T')[0];
        }
        
        function executeExport() {
            const format = document.getElementById('exportFormat').value;
            const range = document.querySelector('input[name="exportRange"]:checked').value;
            const fields = Array.from(document.querySelectorAll('.checkbox-group input:checked')).map(cb => cb.parentElement.textContent.trim());
            const startDate = document.getElementById('exportStartDate').value;
            const endDate = document.getElementById('exportEndDate').value;
            
            closeModal();
            
            // 显示导出进度
            showExportProgress(format, range, fields, startDate, endDate);
        }
        
        function showExportProgress(format, range, fields, startDate, endDate) {
            const progressModal = createModal('导出进度', `
                <div class="export-progress">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <div class="progress-icon" style="width: 60px; height: 60px; margin: 0 auto 16px; border: 4px solid #e5e7eb; border-top: 4px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                        <h3 style="margin: 0 0 8px 0; color: #1f2937;">正在导出用户数据...</h3>
                        <p style="margin: 0; color: #6b7280; font-size: 14px;">请稍候，这可能需要几分钟时间</p>
                    </div>
                    
                    <div class="progress-bar" style="width: 100%; height: 8px; background: #f3f4f6; border-radius: 4px; overflow: hidden; margin-bottom: 16px;">
                        <div id="progressFill" style="width: 0%; height: 100%; background: #3b82f6; transition: width 0.3s ease;"></div>
                    </div>
                    
                    <div id="progressText" style="text-align: center; font-size: 14px; color: #6b7280;">准备导出数据...</div>
                    
                    <div style="margin-top: 20px; padding: 16px; background: #f9fafb; border-radius: 6px; border: 1px solid #e5e7eb;">
                        <h4 style="margin: 0 0 8px 0; font-size: 14px; color: #374151;">导出设置</h4>
                        <div style="font-size: 12px; color: #6b7280; line-height: 1.5;">
                            <div><strong>格式：</strong> ${format.toUpperCase()}</div>
                            <div><strong>范围：</strong> ${getRangeText(range)}</div>
                            <div><strong>字段：</strong> ${fields.length} 个字段</div>
                            <div><strong>时间：</strong> ${startDate} 至 ${endDate}</div>
                        </div>
                    </div>
                </div>
                
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `);
            
            // 模拟导出进度
            let progress = 0;
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            const steps = [
                { progress: 10, text: '正在查询用户数据...' },
                { progress: 30, text: '正在处理用户信息...' },
                { progress: 50, text: '正在生成导出文件...' },
                { progress: 70, text: '正在压缩数据...' },
                { progress: 90, text: '正在准备下载...' },
                { progress: 100, text: '导出完成！' }
            ];
            
            let stepIndex = 0;
            const updateProgress = () => {
                if (stepIndex < steps.length) {
                    const step = steps[stepIndex];
                    progressFill.style.width = step.progress + '%';
                    progressText.textContent = step.text;
                    stepIndex++;
                    
                    if (stepIndex < steps.length) {
                        setTimeout(updateProgress, 1000 + Math.random() * 1000);
                    } else {
                        // 导出完成
                        setTimeout(() => {
                            closeModal();
                            downloadFile(format, range, fields);
                            showNotification(`${format.toUpperCase()} 文件导出成功！`, 'success');
                        }, 1000);
                    }
                }
            };
            
            setTimeout(updateProgress, 500);
        }
        
        function getRangeText(range) {
            const rangeMap = {
                'all': '所有用户',
                'filtered': '当前筛选结果', 
                'active': '仅活跃用户',
                'vip': '仅VIP用户'
            };
            return rangeMap[range] || range;
        }
        
        function downloadFile(format, range, fields) {
            // 模拟文件下载
            const filename = `用户数据_${range}_${new Date().toISOString().split('T')[0]}.${format}`;
            
            if (format === 'csv') {
                const csvData = generateAdvancedUserCSV(range, fields);
                downloadCSV(csvData, filename);
            } else if (format === 'json') {
                const jsonData = generateUserJSON(range, fields);
                downloadJSON(jsonData, filename);
            } else {
                // Excel 和 PDF 格式的模拟下载
                console.log(`下载 ${format} 文件: ${filename}`);
            }
        }
        
        function generateAdvancedUserCSV(range, fields) {
            const headers = fields;
            const sampleData = [
                ['1', 'Alice Chen', '<EMAIL>', '13812345678', '活跃', 'VIP', '¥12,458.50', 'Telegram', '2024-01-15', '刚刚'],
                ['2', 'Bob Wang', '<EMAIL>', '13823456789', '待验证', '商家', '¥8,234.00', 'Web', '2024-02-20', '8分钟前'],
                ['3', 'Charlie Li', '<EMAIL>', '13834567890', '活跃', '用户', '¥567.80', '推荐', '2024-03-10', '2小时前']
            ];
            
            return [headers.join(','), ...sampleData.map(row => row.join(','))].join('\n');
        }
        
        function generateUserJSON(range, fields) {
            const userData = [
                {
                    id: 1,
                    name: 'Alice Chen',
                    email: '<EMAIL>',
                    phone: '13812345678',
                    status: 'active',
                    role: 'vip',
                    balance: 12458.50,
                    source: 'telegram',
                    registerTime: '2024-01-15',
                    lastActive: '刚刚'
                },
                {
                    id: 2,
                    name: 'Bob Wang', 
                    email: '<EMAIL>',
                    phone: '13823456789',
                    status: 'pending',
                    role: 'seller',
                    balance: 8234.00,
                    source: 'web',
                    registerTime: '2024-02-20',
                    lastActive: '8分钟前'
                }
            ];
            
            return JSON.stringify({
                exportTime: new Date().toISOString(),
                range: range,
                fields: fields,
                totalCount: userData.length,
                data: userData
            }, null, 2);
        }
        
        function downloadJSON(jsonContent, filename) {
            const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // ==================== 用户导入功能 ====================
        
        function showImportModal() {
            const modal = createModal('批量导入用户', `
                <div class="import-options">
                    <div class="import-step" id="step1">
                        <h4 style="margin: 0 0 16px 0; color: #1f2937;">第一步：选择导入文件</h4>
                        
                        <div class="file-upload-area" style="border: 2px dashed #d1d5db; border-radius: 8px; padding: 40px 20px; text-align: center; background: #f9fafb; cursor: pointer;" onclick="document.getElementById('importFile').click()">
                            <div style="font-size: 48px; color: #9ca3af; margin-bottom: 16px;">📁</div>
                            <h3 style="margin: 0 0 8px 0; color: #374151;">点击选择文件或拖拽文件到此处</h3>
                            <p style="margin: 0; color: #6b7280; font-size: 14px;">支持 Excel (.xlsx), CSV (.csv) 格式</p>
                            <input type="file" id="importFile" accept=".xlsx,.csv" style="display: none;" onchange="handleFileSelect(this)">
                        </div>
                        
                        <div id="selectedFile" style="display: none; margin-top: 16px; padding: 12px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span style="font-size: 20px;">📄</span>
                                <div>
                                    <div id="fileName" style="font-weight: 500; color: #0f172a;"></div>
                                    <div id="fileSize" style="font-size: 12px; color: #64748b;"></div>
                                </div>
                                <button onclick="clearFile()" style="margin-left: auto; padding: 4px 8px; border: none; background: #ef4444; color: white; border-radius: 4px; cursor: pointer;">删除</button>
                            </div>
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <h4 style="margin: 0 0 12px 0; color: #1f2937;">导入设置</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                <div>
                                    <label style="display: block; margin-bottom: 4px; font-size: 14px; color: #374151;">重复处理方式</label>
                                    <select id="duplicateHandling" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                        <option value="skip">跳过重复用户</option>
                                        <option value="update">更新已有用户</option>
                                        <option value="error">报错停止</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 4px; font-size: 14px; color: #374151;">默认用户角色</label>
                                    <select id="defaultRole" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                        <option value="user">普通用户</option>
                                        <option value="vip">VIP用户</option>
                                        <option value="seller">商家</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="sendWelcomeEmail" checked style="margin: 0;">
                                <span style="font-size: 14px; color: #374151;">发送欢迎邮件给新用户</span>
                            </label>
                        </div>
                        
                        <div style="margin-top: 20px; padding: 16px; background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px;">
                            <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 14px;">📋 导入模板说明</h4>
                            <p style="margin: 0; font-size: 12px; color: #92400e; line-height: 1.5;">
                                Excel/CSV 文件应包含以下列：<br>
                                <strong>必需：</strong>用户名, 邮箱地址<br>
                                <strong>可选：</strong>手机号码, 用户角色, 注册来源, 初始余额
                            </p>
                            <button onclick="downloadTemplate()" style="margin-top: 8px; padding: 4px 8px; border: 1px solid #f59e0b; background: white; color: #f59e0b; border-radius: 4px; font-size: 12px; cursor: pointer;">下载模板文件</button>
                        </div>
                    </div>
                    
                    <div class="import-step" id="step2" style="display: none;">
                        <h4 style="margin: 0 0 16px 0; color: #1f2937;">第二步：数据预览与验证</h4>
                        
                        <div id="dataPreview" style="border: 1px solid #e5e7eb; border-radius: 6px; overflow: hidden;">
                            <!-- 数据预览表格将在这里显示 -->
                        </div>
                        
                        <div id="validationResults" style="margin-top: 16px;">
                            <!-- 验证结果将在这里显示 -->
                        </div>
                    </div>
                    
                    <div class="import-step" id="step3" style="display: none;">
                        <h4 style="margin: 0 0 16px 0; color: #1f2937;">第三步：导入进度</h4>
                        
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div class="progress-icon" style="width: 60px; height: 60px; margin: 0 auto 16px; border: 4px solid #e5e7eb; border-top: 4px solid #10b981; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                            <h3 style="margin: 0 0 8px 0; color: #1f2937;">正在导入用户数据...</h3>
                            <p style="margin: 0; color: #6b7280; font-size: 14px;">请稍候，正在处理您的数据</p>
                        </div>
                        
                        <div class="progress-bar" style="width: 100%; height: 8px; background: #f3f4f6; border-radius: 4px; overflow: hidden; margin-bottom: 16px;">
                            <div id="importProgressFill" style="width: 0%; height: 100%; background: #10b981; transition: width 0.3s ease;"></div>
                        </div>
                        
                        <div id="importProgressText" style="text-align: center; font-size: 14px; color: #6b7280;">准备导入数据...</div>
                        
                        <div id="importResults" style="margin-top: 20px; display: none;">
                            <!-- 导入结果将在这里显示 -->
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; justify-content: space-between; margin-top: 24px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <button id="prevBtn" onclick="previousStep()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; font-size: 14px; cursor: pointer; display: none;">上一步</button>
                    <div style="margin-left: auto; display: flex; gap: 12px;">
                        <button onclick="closeModal()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; font-size: 14px; cursor: pointer;">取消</button>
                        <button id="nextBtn" onclick="nextStep()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: #f3f4f6; color: #9ca3af; font-size: 14px; cursor: not-allowed;" disabled>下一步</button>
                    </div>
                </div>
            `);
        }
        
        let currentStep = 1;
        let importData = null;
        
        function handleFileSelect(input) {
            const file = input.files[0];
            if (file) {
                document.getElementById('selectedFile').style.display = 'block';
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = `${(file.size / 1024).toFixed(1)} KB`;
                document.getElementById('nextBtn').disabled = false;
                document.getElementById('nextBtn').style.background = '#3b82f6';
                document.getElementById('nextBtn').style.color = 'white';
                document.getElementById('nextBtn').style.cursor = 'pointer';
                
                // 模拟文件解析
                parseImportFile(file);
            }
        }
        
        function clearFile() {
            document.getElementById('importFile').value = '';
            document.getElementById('selectedFile').style.display = 'none';
            document.getElementById('nextBtn').disabled = true;
            document.getElementById('nextBtn').style.background = '#f3f4f6';
            document.getElementById('nextBtn').style.color = '#9ca3af';
            document.getElementById('nextBtn').style.cursor = 'not-allowed';
            importData = null;
        }
        
        function parseImportFile(file) {
            // 模拟文件解析
            importData = {
                headers: ['用户名', '邮箱地址', '手机号码', '用户角色'],
                rows: [
                    ['张三', '<EMAIL>', '13812345678', '用户'],
                    ['李四', '<EMAIL>', '13823456789', 'VIP'],
                    ['王五', '<EMAIL>', '13834567890', '商家'],
                    ['赵六', '<EMAIL>', '13845678901', '用户']
                ],
                validRows: 4,
                invalidRows: 0,
                duplicates: 0
            };
        }
        
        function nextStep() {
            if (currentStep === 1 && importData) {
                showStep(2);
                showDataPreview();
            } else if (currentStep === 2) {
                showStep(3);
                executeImport();
            }
        }
        
        function previousStep() {
            if (currentStep > 1) {
                showStep(currentStep - 1);
            }
        }
        
        function showStep(step) {
            // 隐藏所有步骤
            for (let i = 1; i <= 3; i++) {
                document.getElementById(`step${i}`).style.display = 'none';
            }
            
            // 显示当前步骤
            document.getElementById(`step${step}`).style.display = 'block';
            currentStep = step;
            
            // 更新按钮状态
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            prevBtn.style.display = step > 1 ? 'block' : 'none';
            
            if (step === 3) {
                nextBtn.style.display = 'none';
            } else {
                nextBtn.style.display = 'block';
                nextBtn.textContent = step === 2 ? '开始导入' : '下一步';
            }
        }
        
        function showDataPreview() {
            const preview = document.getElementById('dataPreview');
            const validation = document.getElementById('validationResults');
            
            // 显示数据预览表格
            preview.innerHTML = `
                <table style="width: 100%; border-collapse: collapse;">
                    <thead style="background: #f9fafb;">
                        <tr>
                            ${importData.headers.map(header => `<th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 500; color: #374151;">${header}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${importData.rows.slice(0, 5).map(row => `
                            <tr>
                                ${row.map(cell => `<td style="padding: 12px; border-bottom: 1px solid #f3f4f6; color: #1f2937;">${cell}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                ${importData.rows.length > 5 ? `<div style="padding: 12px; text-align: center; color: #6b7280; font-size: 14px; border-top: 1px solid #e5e7eb;">... 还有 ${importData.rows.length - 5} 行数据</div>` : ''}
            `;
            
            // 显示验证结果
            validation.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
                    <div style="padding: 16px; background: #f0fdf4; border: 1px solid #22c55e; border-radius: 6px; text-align: center;">
                        <div style="font-size: 24px; font-weight: 700; color: #16a34a;">${importData.validRows}</div>
                        <div style="font-size: 14px; color: #15803d;">有效记录</div>
                    </div>
                    <div style="padding: 16px; background: #fef2f2; border: 1px solid #ef4444; border-radius: 6px; text-align: center;">
                        <div style="font-size: 24px; font-weight: 700; color: #dc2626;">${importData.invalidRows}</div>
                        <div style="font-size: 14px; color: #dc2626;">无效记录</div>
                    </div>
                    <div style="padding: 16px; background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; text-align: center;">
                        <div style="font-size: 24px; font-weight: 700; color: #d97706;">${importData.duplicates}</div>
                        <div style="font-size: 14px; color: #d97706;">重复记录</div>
                    </div>
                </div>
                
                <div style="margin-top: 16px; padding: 12px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px;">
                    <div style="font-size: 14px; color: #0f172a;">✅ 数据验证通过，可以开始导入</div>
                </div>
            `;
        }
        
        function executeImport() {
            const progressFill = document.getElementById('importProgressFill');
            const progressText = document.getElementById('importProgressText');
            const results = document.getElementById('importResults');
            
            const steps = [
                { progress: 20, text: '正在验证用户数据...' },
                { progress: 40, text: '正在检查重复用户...' },
                { progress: 60, text: '正在创建用户账号...' },
                { progress: 80, text: '正在发送欢迎邮件...' },
                { progress: 100, text: '导入完成！' }
            ];
            
            let stepIndex = 0;
            const updateProgress = () => {
                if (stepIndex < steps.length) {
                    const step = steps[stepIndex];
                    progressFill.style.width = step.progress + '%';
                    progressText.textContent = step.text;
                    stepIndex++;
                    
                    if (stepIndex < steps.length) {
                        setTimeout(updateProgress, 800 + Math.random() * 400);
                    } else {
                        // 导入完成
                        setTimeout(() => {
                            results.style.display = 'block';
                            results.innerHTML = `
                                <div style="text-align: center; padding: 20px;">
                                    <div style="font-size: 48px; margin-bottom: 16px;">🎉</div>
                                    <h3 style="margin: 0 0 8px 0; color: #16a34a;">导入成功完成！</h3>
                                    <p style="margin: 0; color: #6b7280; font-size: 14px;">共导入 ${importData.validRows} 个用户</p>
                                </div>
                                
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; margin-top: 20px;">
                                    <div style="padding: 16px; background: #f0fdf4; border-radius: 6px; text-align: center;">
                                        <div style="font-size: 20px; font-weight: 600; color: #16a34a;">${importData.validRows}</div>
                                        <div style="font-size: 12px; color: #15803d;">成功导入</div>
                                    </div>
                                    <div style="padding: 16px; background: #fef3c7; border-radius: 6px; text-align: center;">
                                        <div style="font-size: 20px; font-weight: 600; color: #d97706;">${importData.duplicates}</div>
                                        <div style="font-size: 12px; color: #d97706;">跳过重复</div>
                                    </div>
                                </div>
                                
                                <div style="margin-top: 20px; text-align: center;">
                                    <button onclick="closeModal(); refreshUsers();" style="padding: 8px 24px; border: 1px solid #16a34a; border-radius: 6px; background: #16a34a; color: white; font-size: 14px; cursor: pointer;">完成</button>
                                </div>
                            `;
                            showNotification('用户批量导入成功！', 'success');
                        }, 1000);
                    }
                }
            };
            
            setTimeout(updateProgress, 500);
        }
        
        function downloadTemplate() {
            const templateData = [
                ['用户名', '邮箱地址', '手机号码', '用户角色', '注册来源', '初始余额'],
                ['张三', '<EMAIL>', '13812345678', '用户', 'Web', '0'],
                ['李四', '<EMAIL>', '13823456789', 'VIP', 'Telegram', '100'],
                ['王五', '<EMAIL>', '13834567890', '商家', '推荐', '500']
            ];
            
            const csvContent = templateData.map(row => row.join(',')).join('\n');
            downloadCSV(csvContent, '用户导入模板.csv');
            showNotification('模板文件下载成功！', 'success');
        }

        // ==================== 通用模态框功能 ====================
        
        function createModal(title, content) {
            const modal = document.createElement('div');
            modal.className = 'custom-modal';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.5); z-index: 1000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px;
            `;
            
            modal.innerHTML = `
                <div class="modal-content" style="background: white; border-radius: 12px; max-width: 800px; max-height: 90vh; overflow-y: auto; width: 100%; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);">
                    <div class="modal-header" style="padding: 24px 24px 0 24px; border-bottom: 1px solid #e5e7eb; margin-bottom: 24px;">
                        <h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #1f2937; padding-bottom: 16px;">${title}</h3>
                    </div>
                    <div class="modal-body" style="padding: 0 24px 24px 24px;">
                        ${content}
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            return modal;
        }
        
        function closeModal() {
            const modal = document.querySelector('.custom-modal');
            if (modal) {
                modal.remove();
                currentStep = 1;
                importData = null;
            }
        }
    </script>
</body>
</html> 
