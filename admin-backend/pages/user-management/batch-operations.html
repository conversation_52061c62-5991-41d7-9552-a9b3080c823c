<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量操作 - TeleShop后台管理</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.2s;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
        }

        .selection-info {
            background: #dbeafe;
            color: #1e40af;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #059669; color: white; }
        .btn-warning { background: #d97706; color: white; }
        .btn-danger { background: #dc2626; color: white; }
        .btn-ghost { background: transparent; color: #64748b; border: 2px solid #e2e8f0; }

        .operations-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .operation-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            background: #f8fafc;
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-body {
            padding: 24px;
        }

        .operation-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 12px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .operation-item:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .operation-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .operation-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .operation-details {
            flex: 1;
        }

        .operation-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .operation-desc {
            font-size: 12px;
            color: #64748b;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #1e293b;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .tag-input-group {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .tag {
            padding: 4px 8px;
            background: #dbeafe;
            color: #1e40af;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .tag-remove {
            background: none;
            border: none;
            color: inherit;
            cursor: pointer;
            font-size: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f1f5f9;
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }

        .progress-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            font-size: 14px;
            color: #64748b;
            text-align: center;
        }

        .export-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .export-option {
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
        }

        .export-option.selected {
            border-color: #3b82f6;
            background: #f0f9ff;
        }

        .export-icon {
            font-size: 24px;
            margin-bottom: 8px;
            color: #64748b;
        }

        .export-option.selected .export-icon {
            color: #3b82f6;
        }

        .export-name {
            font-weight: 500;
            color: #1e293b;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .selected-users-preview {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 12px;
            background: #f8fafc;
        }

        .user-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 0;
        }

        .user-avatar-small {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            max-width: 500px;
            width: 90%;
            padding: 24px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #64748b;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .operations-grid {
                grid-template-columns: 1fr;
            }

            .export-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div style="display: flex; align-items: center; gap: 16px;">
                <a href="index.html" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    返回用户列表
                </a>
                <div>
                    <h1 class="page-title">批量操作</h1>
                    <div class="selection-info" id="selectionInfo">
                        <i class="fas fa-users"></i>
                        已选择 5 个用户
                    </div>
                </div>
            </div>
        </div>

        <!-- 选中用户预览 -->
        <div class="operation-card full-width" style="margin-bottom: 24px;">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-check-square"></i>
                    已选择的用户
                </h3>
            </div>
            <div class="card-body">
                <div class="selected-users-preview">
                    <div class="user-item">
                        <div class="user-avatar-small">张</div>
                        <span>张小明 (<EMAIL>)</span>
                    </div>
                    <div class="user-item">
                        <div class="user-avatar-small">李</div>
                        <span>李小红 (<EMAIL>)</span>
                    </div>
                    <div class="user-item">
                        <div class="user-avatar-small">王</div>
                        <span>王大力 (<EMAIL>)</span>
                    </div>
                    <div class="user-item">
                        <div class="user-avatar-small">赵</div>
                        <span>赵美丽 (<EMAIL>)</span>
                    </div>
                    <div class="user-item">
                        <div class="user-avatar-small">刘</div>
                        <span>刘强 (<EMAIL>)</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="operations-grid">
            <!-- 批量标签管理 -->
            <div class="operation-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tags"></i>
                        标签管理
                    </h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="form-label">操作类型</label>
                        <select class="form-select" id="tagOperation">
                            <option value="add">添加标签</option>
                            <option value="remove">移除标签</option>
                            <option value="replace">替换标签</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">标签</label>
                        <div class="tag-input-group">
                            <input type="text" class="form-input" id="tagInput" placeholder="输入标签名称">
                            <button class="btn btn-primary" onclick="addTag()">添加</button>
                        </div>
                        <div class="selected-tags" id="selectedTags">
                            <span class="tag">
                                VIP
                                <button class="tag-remove" onclick="removeTag(this)">&times;</button>
                            </span>
                            <span class="tag">
                                高频用户
                                <button class="tag-remove" onclick="removeTag(this)">&times;</button>
                            </span>
                        </div>
                    </div>

                    <button class="btn btn-success" onclick="executeBatchTagOperation()">
                        <i class="fas fa-play"></i>
                        执行标签操作
                    </button>
                </div>
            </div>

            <!-- 批量状态管理 -->
            <div class="operation-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-toggle-on"></i>
                        状态管理
                    </h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="form-label">更改状态</label>
                        <select class="form-select" id="statusSelect">
                            <option value="active">激活</option>
                            <option value="inactive">暂停</option>
                            <option value="banned">封禁</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">操作原因</label>
                        <textarea class="form-input form-textarea" id="statusReason" placeholder="请输入状态更改的原因"></textarea>
                    </div>

                    <button class="btn btn-warning" onclick="executeBatchStatusOperation()">
                        <i class="fas fa-edit"></i>
                        更新状态
                    </button>
                </div>
            </div>

            <!-- 批量导出 -->
            <div class="operation-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-download"></i>
                        批量导出
                    </h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="form-label">导出格式</label>
                        <div class="export-options">
                            <div class="export-option selected" onclick="selectExportFormat(this, 'excel')">
                                <div class="export-icon">
                                    <i class="fas fa-file-excel"></i>
                                </div>
                                <div class="export-name">Excel</div>
                            </div>
                            <div class="export-option" onclick="selectExportFormat(this, 'csv')">
                                <div class="export-icon">
                                    <i class="fas fa-file-csv"></i>
                                </div>
                                <div class="export-name">CSV</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">导出字段</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 14px;">
                            <label><input type="checkbox" checked> 基本信息</label>
                            <label><input type="checkbox" checked> 联系方式</label>
                            <label><input type="checkbox"> 消费数据</label>
                            <label><input type="checkbox"> 行为数据</label>
                            <label><input type="checkbox"> 标签信息</label>
                            <label><input type="checkbox"> 状态信息</label>
                        </div>
                    </div>

                    <button class="btn btn-primary" onclick="exportUsers()">
                        <i class="fas fa-download"></i>
                        开始导出
                    </button>
                </div>
            </div>

            <!-- 批量删除 -->
            <div class="operation-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-trash"></i>
                        批量删除
                    </h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label class="form-label">删除确认</label>
                        <p style="color: #dc2626; font-size: 14px; margin-bottom: 12px;">
                            <i class="fas fa-exclamation-triangle"></i>
                            警告：此操作不可撤销，将永久删除所选用户的所有数据
                        </p>
                        <input type="text" class="form-input" placeholder="输入 'DELETE' 确认删除" id="deleteConfirm">
                    </div>

                    <div class="form-group">
                        <label class="form-label">删除原因</label>
                        <textarea class="form-input form-textarea" placeholder="请说明删除原因"></textarea>
                    </div>

                    <button class="btn btn-danger" onclick="confirmBatchDelete()">
                        <i class="fas fa-trash"></i>
                        永久删除用户
                    </button>
                </div>
            </div>
        </div>

        <!-- 进度显示 -->
        <div id="progressSection" style="display: none; margin-top: 24px;">
            <div class="operation-card full-width">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cog fa-spin"></i>
                        操作进行中
                    </h3>
                </div>
                <div class="card-body">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认对话框 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">确认操作</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div id="modalBody">
                <p>您确定要执行此批量操作吗？</p>
            </div>
            <div class="modal-actions">
                <button class="btn btn-ghost" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" id="confirmBtn" onclick="confirmOperation()">确认</button>
            </div>
        </div>
    </div>

    <script>
        let currentOperation = null;
        let selectedUsers = [
            { id: 1, name: '张小明', email: '<EMAIL>' },
            { id: 2, name: '李小红', email: '<EMAIL>' },
            { id: 3, name: '王大力', email: '<EMAIL>' },
            { id: 4, name: '赵美丽', email: '<EMAIL>' },
            { id: 5, name: '刘强', email: '<EMAIL>' }
        ];

        // 添加标签
        function addTag() {
            const input = document.getElementById('tagInput');
            const tagText = input.value.trim();
            
            if (tagText) {
                const tagsContainer = document.getElementById('selectedTags');
                const newTag = document.createElement('span');
                newTag.className = 'tag';
                newTag.innerHTML = `
                    ${tagText}
                    <button class="tag-remove" onclick="removeTag(this)">&times;</button>
                `;
                tagsContainer.appendChild(newTag);
                input.value = '';
            }
        }

        // 删除标签
        function removeTag(button) {
            button.parentElement.remove();
        }

        // 选择导出格式
        function selectExportFormat(element, format) {
            document.querySelectorAll('.export-option').forEach(option => {
                option.classList.remove('selected');
            });
            element.classList.add('selected');
        }

        // 执行标签操作
        function executeBatchTagOperation() {
            currentOperation = 'tag';
            const operation = document.getElementById('tagOperation').value;
            const tags = Array.from(document.querySelectorAll('#selectedTags .tag')).map(tag => tag.textContent.trim());
            
            if (tags.length === 0) {
                alert('请至少添加一个标签');
                return;
            }

            document.getElementById('modalBody').innerHTML = `
                <p>您将对 ${selectedUsers.length} 个用户执行标签${operation === 'add' ? '添加' : operation === 'remove' ? '移除' : '替换'}操作</p>
                <p><strong>标签：</strong>${tags.join(', ')}</p>
            `;
            document.getElementById('confirmModal').style.display = 'block';
        }

        // 执行状态操作
        function executeBatchStatusOperation() {
            currentOperation = 'status';
            const status = document.getElementById('statusSelect').value;
            const reason = document.getElementById('statusReason').value;
            
            if (!reason.trim()) {
                alert('请输入状态更改原因');
                return;
            }

            const statusText = status === 'active' ? '激活' : status === 'inactive' ? '暂停' : '封禁';
            document.getElementById('modalBody').innerHTML = `
                <p>您将把 ${selectedUsers.length} 个用户的状态更改为：<strong>${statusText}</strong></p>
                <p><strong>原因：</strong>${reason}</p>
            `;
            document.getElementById('confirmModal').style.display = 'block';
        }

        // 导出用户
        function exportUsers() {
            currentOperation = 'export';
            document.getElementById('modalBody').innerHTML = `
                <p>您将导出 ${selectedUsers.length} 个用户的数据</p>
                <p>导出完成后将自动下载文件</p>
            `;
            document.getElementById('confirmModal').style.display = 'block';
        }

        // 确认批量删除
        function confirmBatchDelete() {
            const confirmText = document.getElementById('deleteConfirm').value;
            if (confirmText !== 'DELETE') {
                alert('请输入 DELETE 确认删除操作');
                return;
            }

            currentOperation = 'delete';
            document.getElementById('modalBody').innerHTML = `
                <div style="color: #dc2626;">
                    <p><strong>⚠️ 危险操作</strong></p>
                    <p>您将永久删除 ${selectedUsers.length} 个用户的所有数据，此操作不可撤销！</p>
                </div>
            `;
            document.getElementById('confirmBtn').className = 'btn btn-danger';
            document.getElementById('confirmModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('confirmModal').style.display = 'none';
            document.getElementById('confirmBtn').className = 'btn btn-primary';
            currentOperation = null;
        }

        // 确认操作
        function confirmOperation() {
            if (!currentOperation) return;

            closeModal();
            showProgress();

            // 模拟操作进度
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(progressInterval);
                    
                    setTimeout(() => {
                        hideProgress();
                        showSuccessMessage();
                    }, 500);
                }

                document.getElementById('progressFill').style.width = progress + '%';
                document.getElementById('progressText').textContent = 
                    `正在处理... ${Math.round(progress)}%`;
            }, 300);
        }

        // 显示进度
        function showProgress() {
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = '准备中...';
        }

        // 隐藏进度
        function hideProgress() {
            document.getElementById('progressSection').style.display = 'none';
        }

        // 显示成功消息
        function showSuccessMessage() {
            const operationNames = {
                'tag': '标签操作',
                'status': '状态更新',
                'export': '数据导出',
                'delete': '用户删除'
            };

            alert(`${operationNames[currentOperation]}已成功完成！`);
            
            if (currentOperation === 'export') {
                // 模拟文件下载
                const link = document.createElement('a');
                link.href = '#';
                link.download = 'users_export.xlsx';
                link.click();
            }
            
            currentOperation = null;
        }

        // 回车添加标签
        document.getElementById('tagInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addTag();
            }
        });

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('confirmModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('批量操作页面已加载');
            document.getElementById('selectionInfo').innerHTML = `
                <i class="fas fa-users"></i>
                已选择 ${selectedUsers.length} 个用户
            `;
        });
    </script>
</body>
</html> 