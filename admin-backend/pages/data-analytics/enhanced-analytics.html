<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析升级 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .analytics-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
            margin-top: 4px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .analytics-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            margin-bottom: 24px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .tab-btn {
            flex: 1;
            padding: 16px 24px;
            border: none;
            background: white;
            color: #64748b;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .tab-btn.active {
            color: #3b82f6;
            background: #eff6ff;
            border-bottom: 3px solid #3b82f6;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .stat-icon.revenue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.orders { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.users { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-icon.conversion { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .stat-change.positive {
            color: #10b981;
        }

        .stat-change.negative {
            color: #ef4444;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .merchant-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .merchant-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .merchant-item:hover {
            background: #f1f5f9;
        }

        .merchant-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .merchant-info {
            flex: 1;
        }

        .merchant-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .merchant-stats {
            font-size: 12px;
            color: #64748b;
        }

        .merchant-revenue {
            font-weight: 600;
            color: #10b981;
        }

        .filter-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .filter-select {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .analysis-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .analysis-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .analysis-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .analysis-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .analysis-icon.trend { background: #3b82f6; }
        .analysis-icon.prediction { background: #8b5cf6; }
        .analysis-icon.segment { background: #10b981; }
        .analysis-icon.alert { background: #f59e0b; }

        .analysis-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .metric-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: #f8fafc;
            border-radius: 6px;
        }

        .metric-name {
            font-size: 14px;
            color: #64748b;
        }

        .metric-value {
            font-weight: 600;
            color: #1e293b;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: white;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .alert-panel {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 1px solid #fca5a5;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .alert-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .alert-icon {
            color: #dc2626;
            font-size: 16px;
        }

        .alert-title {
            font-weight: 600;
            color: #dc2626;
        }

        .alert-message {
            color: #991b1b;
            font-size: 14px;
            line-height: 1.5;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .analytics-page {
                padding: 16px;
            }

            .header-content {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }

            .analytics-tabs {
                flex-direction: column;
            }

            .filter-grid {
                grid-template-columns: 1fr;
            }

            .analysis-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="analytics-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title">数据分析升级</h1>
                    <p class="page-subtitle">多维度分析、商家数据隔离、实时监控与预测分析</p>
                </div>
                
                <div class="header-actions">
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        导出报告
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-chart-bar"></i>
                        自定义分析
                    </button>
                </div>
            </div>
        </div>

        <!-- 实时预警面板 -->
        <div class="alert-panel">
            <div class="alert-header">
                <i class="fas fa-exclamation-triangle alert-icon"></i>
                <span class="alert-title">实时预警</span>
            </div>
            <div class="alert-message">
                检测到"华为旗舰专卖店"销售额异常下降32%，建议立即关注。同时发现3个商家库存预警，需要及时补货。
            </div>
        </div>

        <!-- 核心指标概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon revenue">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="stat-value">¥18.5M</div>
                <div class="stat-label">本月总收入</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +15.3% 环比上月
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon orders">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="stat-value">45,678</div>
                <div class="stat-label">订单总数</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +23.1% 环比上月
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value">12,456</div>
                <div class="stat-label">活跃用户</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +8.7% 环比上月
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon conversion">
                        <i class="fas fa-percentage"></i>
                    </div>
                </div>
                <div class="stat-value">3.2%</div>
                <div class="stat-label">平均转化率</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i>
                    -0.5% 环比上月
                </div>
            </div>
        </div>

        <!-- 功能标签页 -->
        <div class="analytics-tabs">
            <button class="tab-btn active" onclick="switchTab('overview')">
                <i class="fas fa-chart-line"></i>
                数据概览
            </button>
            <button class="tab-btn" onclick="switchTab('merchant')">
                <i class="fas fa-store"></i>
                商家分析
            </button>
            <button class="tab-btn" onclick="switchTab('prediction')">
                <i class="fas fa-crystal-ball"></i>
                预测分析
            </button>
            <button class="tab-btn" onclick="switchTab('realtime')">
                <i class="fas fa-broadcast-tower"></i>
                实时监控
            </button>
        </div>

        <!-- 数据概览内容 -->
        <div class="tab-content active" id="overview-content">
            <div class="content-grid">
                <div class="chart-card">
                    <div class="card-header">
                        <h3 class="card-title">销售趋势分析</h3>
                        <div class="card-actions">
                            <button class="btn btn-small btn-secondary">7天</button>
                            <button class="btn btn-small btn-secondary">30天</button>
                            <button class="btn btn-small btn-primary">90天</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <div class="card-header">
                        <h3 class="card-title">热门商家排行</h3>
                        <a href="#" class="btn btn-small btn-secondary">查看全部</a>
                    </div>
                    <div class="merchant-list">
                        <div class="merchant-item">
                            <div class="merchant-avatar">华</div>
                            <div class="merchant-info">
                                <div class="merchant-name">华为旗舰专卖店</div>
                                <div class="merchant-stats">订单: 2,456 | 转化率: 4.2%</div>
                            </div>
                            <div class="merchant-revenue">¥2.5M</div>
                        </div>
                        
                        <div class="merchant-item">
                            <div class="merchant-avatar">小</div>
                            <div class="merchant-info">
                                <div class="merchant-name">小米生活馆</div>
                                <div class="merchant-stats">订单: 1,876 | 转化率: 3.8%</div>
                            </div>
                            <div class="merchant-revenue">¥1.9M</div>
                        </div>
                        
                        <div class="merchant-item">
                            <div class="merchant-avatar">时</div>
                            <div class="merchant-info">
                                <div class="merchant-name">时尚潮牌屋</div>
                                <div class="merchant-stats">订单: 1,234 | 转化率: 3.2%</div>
                            </div>
                            <div class="merchant-revenue">¥1.2M</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="analysis-cards">
                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon trend">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="analysis-title">增长趋势分析</div>
                    </div>
                    <div class="metric-list">
                        <div class="metric-item">
                            <span class="metric-name">月增长率</span>
                            <span class="metric-value">+15.3%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">用户增长</span>
                            <span class="metric-value">+8.7%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">商家增长</span>
                            <span class="metric-value">+12.4%</span>
                        </div>
                    </div>
                </div>
                
                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon segment">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="analysis-title">用户群体分析</div>
                    </div>
                    <div class="metric-list">
                        <div class="metric-item">
                            <span class="metric-name">新用户占比</span>
                            <span class="metric-value">32.5%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">回购用户占比</span>
                            <span class="metric-value">67.5%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">平均客单价</span>
                            <span class="metric-value">¥456</span>
                        </div>
                    </div>
                </div>
                
                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon alert">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="analysis-title">异常监控</div>
                    </div>
                    <div class="metric-list">
                        <div class="metric-item">
                            <span class="metric-name">销售异常商家</span>
                            <span class="metric-value">3个</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">库存预警</span>
                            <span class="metric-value">15个</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">投诉率异常</span>
                            <span class="metric-value">2个</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商家分析内容 -->
        <div class="tab-content" id="merchant-content">
            <div class="filter-section">
                <div class="filter-grid">
                    <div class="filter-group">
                        <label class="filter-label">商家类型</label>
                        <select class="filter-select">
                            <option>全部商家</option>
                            <option>旗舰店</option>
                            <option>专营店</option>
                            <option>普通店铺</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">商品类目</label>
                        <select class="filter-select">
                            <option>全部类目</option>
                            <option>数码电器</option>
                            <option>服装鞋包</option>
                            <option>家居生活</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">时间范围</label>
                        <select class="filter-select">
                            <option>最近30天</option>
                            <option>最近7天</option>
                            <option>最近90天</option>
                            <option>自定义</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            查询分析
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="chart-card">
                    <div class="card-header">
                        <h3 class="card-title">商家销售对比</h3>
                        <div class="card-actions">
                            <button class="btn btn-small btn-secondary">销售额</button>
                            <button class="btn btn-small btn-primary">订单量</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="merchantChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <div class="card-header">
                        <h3 class="card-title">商家绩效评分</h3>
                        <a href="#" class="btn btn-small btn-secondary">详细报告</a>
                    </div>
                    <div class="metric-list">
                        <div class="metric-item">
                            <span class="metric-name">华为旗舰专卖店</span>
                            <span class="metric-value">98分</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">小米生活馆</span>
                            <span class="metric-value">95分</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">时尚潮牌屋</span>
                            <span class="metric-value">92分</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">美妆护肤专营</span>
                            <span class="metric-value">89分</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">运动户外旗舰</span>
                            <span class="metric-value">87分</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预测分析内容 -->
        <div class="tab-content" id="prediction-content">
            <div class="analysis-cards">
                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon prediction">
                            <i class="fas fa-crystal-ball"></i>
                        </div>
                        <div class="analysis-title">销售预测</div>
                    </div>
                    <div class="metric-list">
                        <div class="metric-item">
                            <span class="metric-name">下月预计销售额</span>
                            <span class="metric-value">¥21.2M</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">预测增长率</span>
                            <span class="metric-value">+14.6%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">置信度</span>
                            <span class="metric-value">87%</span>
                        </div>
                    </div>
                </div>
                
                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon trend">
                            <i class="fas fa-chart-area"></i>
                        </div>
                        <div class="analysis-title">用户行为预测</div>
                    </div>
                    <div class="metric-list">
                        <div class="metric-item">
                            <span class="metric-name">新用户增长预测</span>
                            <span class="metric-value">+2,345</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">流失风险用户</span>
                            <span class="metric-value">1,234</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">高价值用户增长</span>
                            <span class="metric-value">+456</span>
                        </div>
                    </div>
                </div>
                
                <div class="analysis-card">
                    <div class="analysis-header">
                        <div class="analysis-icon segment">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="analysis-title">库存预测</div>
                    </div>
                    <div class="metric-list">
                        <div class="metric-item">
                            <span class="metric-name">热销商品缺货风险</span>
                            <span class="metric-value">23个</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">滞销商品预警</span>
                            <span class="metric-value">156个</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">补货建议</span>
                            <span class="metric-value">89个</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="chart-card full-width">
                <div class="card-header">
                    <h3 class="card-title">预测模型准确性</h3>
                    <div class="card-actions">
                        <button class="btn btn-small btn-secondary">销售预测</button>
                        <button class="btn btn-small btn-secondary">用户预测</button>
                        <button class="btn btn-small btn-primary">库存预测</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="predictionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 实时监控内容 -->
        <div class="tab-content" id="realtime-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon revenue">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                    <div class="stat-value">2,456</div>
                    <div class="stat-label">实时访问用户</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        较1小时前 +12%
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon orders">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                    </div>
                    <div class="stat-value">89</div>
                    <div class="stat-label">实时订单数</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        较1小时前 +8%
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon users">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="stat-value">¥45,678</div>
                    <div class="stat-label">实时销售额</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        较1小时前 +15%
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon conversion">
                            <i class="fas fa-percentage"></i>
                        </div>
                    </div>
                    <div class="stat-value">3.2%</div>
                    <div class="stat-label">实时转化率</div>
                    <div class="stat-change negative">
                        <i class="fas fa-arrow-down"></i>
                        较1小时前 -0.3%
                    </div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="chart-card">
                    <div class="card-header">
                        <h3 class="card-title">实时流量监控</h3>
                        <div class="card-actions">
                            <button class="btn btn-small btn-primary">自动刷新</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="realtimeChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <div class="card-header">
                        <h3 class="card-title">系统性能监控</h3>
                        <a href="#" class="btn btn-small btn-secondary">详细监控</a>
                    </div>
                    <div class="metric-list">
                        <div class="metric-item">
                            <span class="metric-name">CPU使用率</span>
                            <span class="metric-value">45%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">内存使用率</span>
                            <span class="metric-value">67%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">数据库连接</span>
                            <span class="metric-value">234/500</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">API响应时间</span>
                            <span class="metric-value">156ms</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-name">错误率</span>
                            <span class="metric-value">0.02%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有活动状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的内容
            document.getElementById(tabName + '-content').classList.add('active');
            
            // 添加活动状态
            event.target.classList.add('active');
        }

        // 初始化图表
        function initCharts() {
            // 销售趋势图
            const salesCtx = document.getElementById('salesChart');
            if (salesCtx) {
                new Chart(salesCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            label: '销售额 (万元)',
                            data: [1200, 1450, 1680, 1890, 1750, 1850],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // 商家对比图
            const merchantCtx = document.getElementById('merchantChart');
            if (merchantCtx) {
                new Chart(merchantCtx, {
                    type: 'bar',
                    data: {
                        labels: ['华为专卖', '小米生活', '时尚潮牌', '美妆护肤', '运动户外'],
                        datasets: [{
                            label: '销售额 (万元)',
                            data: [250, 190, 120, 85, 75],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(139, 92, 246, 0.8)',
                                'rgba(239, 68, 68, 0.8)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            // 预测分析图
            const predictionCtx = document.getElementById('predictionChart');
            if (predictionCtx) {
                new Chart(predictionCtx, {
                    type: 'line',
                    data: {
                        labels: ['1周前', '6天前', '5天前', '4天前', '3天前', '2天前', '昨天', '今天'],
                        datasets: [{
                            label: '实际值',
                            data: [85, 87, 84, 89, 92, 88, 91, 93],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4
                        }, {
                            label: '预测值',
                            data: [83, 86, 85, 88, 90, 89, 92, 94],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            borderDash: [5, 5]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false,
                                min: 80,
                                max: 100
                            }
                        }
                    }
                });
            }

            // 实时监控图
            const realtimeCtx = document.getElementById('realtimeChart');
            if (realtimeCtx) {
                const realtimeChart = new Chart(realtimeCtx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '访问量',
                            data: [],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                display: false
                            },
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // 模拟实时数据更新
                setInterval(() => {
                    const now = new Date();
                    const timeLabel = now.getHours() + ':' + now.getMinutes().toString().padStart(2, '0');
                    const randomValue = Math.floor(Math.random() * 100) + 50;

                    realtimeChart.data.labels.push(timeLabel);
                    realtimeChart.data.datasets[0].data.push(randomValue);

                    // 保持最近20个数据点
                    if (realtimeChart.data.labels.length > 20) {
                        realtimeChart.data.labels.shift();
                        realtimeChart.data.datasets[0].data.shift();
                    }

                    realtimeChart.update('none');
                }, 5000);
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据分析升级页面已加载');
            initCharts();
        });
    </script>
</body>
</html>