<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息数据统计 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .message-stats-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .message-charts {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .chart-container {
            position: relative;
            height: 350px;
        }
        
        .type-distribution {
            max-height: 350px;
            overflow-y: auto;
        }
        
        .type-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .type-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .type-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .type-stats {
            text-align: right;
        }
        
        .type-count {
            font-weight: 600;
            color: #1e293b;
        }
        
        .type-percentage {
            font-size: 12px;
            color: #64748b;
        }
        
        .peak-hours {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            padding: 24px;
            margin-bottom: 32px;
        }
        
        .hours-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 8px;
            margin-top: 16px;
        }
        
        .hour-block {
            aspect-ratio: 1;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .hour-low {
            background: #f1f5f9;
            color: #64748b;
        }
        
        .hour-medium {
            background: #fbbf24;
            color: white;
        }
        
        .hour-high {
            background: #ef4444;
            color: white;
        }
        
        .hour-peak {
            background: #7c3aed;
            color: white;
        }
        
        .group-rankings {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .ranking-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .ranking-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #f1f5f9;
            color: #64748b;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        .ranking-number.top3 {
            background: #fbbf24;
            color: white;
        }
        
        .group-info {
            flex: 1;
            min-width: 0;
        }
        
        .group-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .group-type {
            font-size: 12px;
            color: #64748b;
        }
        
        .message-count {
            text-align: right;
            font-weight: 600;
            color: #374151;
        }
        
        .success-rate {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            padding: 24px;
            margin-bottom: 32px;
        }
        
        .rate-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .rate-metric {
            text-align: center;
            padding: 16px;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .rate-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .rate-label {
            font-size: 12px;
            color: #64748b;
        }
        
        .rate-success {
            color: #10b981;
        }
        
        .rate-warning {
            color: #f59e0b;
        }
        
        .rate-danger {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="message-stats-page">
        <div class="page-header">
            <h1 class="page-title">消息数据统计</h1>
            <div class="page-actions">
                <button class="action-btn" onclick="exportStats()">
                    <i class="fas fa-download"></i> 导出统计
                </button>
                <button class="action-btn primary" onclick="refreshStats()">
                    <i class="fas fa-sync-alt"></i> 刷新数据
                </button>
            </div>
        </div>
        
        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">今日消息总数</span>
                    <div class="stat-icon" style="background: #dbeafe; color: #2563eb;">
                        <i class="fas fa-comments"></i>
                    </div>
                </div>
                <div class="stat-value">142,358</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +18.2% 昨日
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">群组消息</span>
                    <div class="stat-icon" style="background: #dcfce7; color: #16a34a;">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value">98,247</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +15.7% 昨日
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">私聊消息</span>
                    <div class="stat-icon" style="background: #fef3c7; color: #d97706;">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <div class="stat-value">44,111</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +22.1% 昨日
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">消息成功率</span>
                    <div class="stat-icon" style="background: #fce7f3; color: #be185d;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-value">99.8%</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +0.1% 昨日
                </div>
            </div>
        </div>
        
        <!-- 消息趋势和类型分布 -->
        <div class="message-charts">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">消息发送趋势</h3>
                    <select onchange="updateTrendChart(this.value)" style="padding: 4px 8px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        <option value="hourly">按小时</option>
                        <option value="daily">按天</option>
                        <option value="weekly">按周</option>
                    </select>
                </div>
                <div class="chart-container">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">消息类型分布</h3>
                </div>
                <div class="type-distribution">
                    <div class="type-item">
                        <div class="type-info">
                            <div class="type-icon" style="background: #dbeafe; color: #2563eb;">
                                <i class="fas fa-font"></i>
                            </div>
                            <span>文本消息</span>
                        </div>
                        <div class="type-stats">
                            <div class="type-count">89,247</div>
                            <div class="type-percentage">62.7%</div>
                        </div>
                    </div>
                    
                    <div class="type-item">
                        <div class="type-info">
                            <div class="type-icon" style="background: #dcfce7; color: #16a34a;">
                                <i class="fas fa-image"></i>
                            </div>
                            <span>图片消息</span>
                        </div>
                        <div class="type-stats">
                            <div class="type-count">28,934</div>
                            <div class="type-percentage">20.3%</div>
                        </div>
                    </div>
                    
                    <div class="type-item">
                        <div class="type-info">
                            <div class="type-icon" style="background: #fef3c7; color: #d97706;">
                                <i class="fas fa-microphone"></i>
                            </div>
                            <span>语音消息</span>
                        </div>
                        <div class="type-stats">
                            <div class="type-count">15,672</div>
                            <div class="type-percentage">11.0%</div>
                        </div>
                    </div>
                    
                    <div class="type-item">
                        <div class="type-info">
                            <div class="type-icon" style="background: #fce7f3; color: #be185d;">
                                <i class="fas fa-video"></i>
                            </div>
                            <span>视频消息</span>
                        </div>
                        <div class="type-stats">
                            <div class="type-count">5,834</div>
                            <div class="type-percentage">4.1%</div>
                        </div>
                    </div>
                    
                    <div class="type-item">
                        <div class="type-info">
                            <div class="type-icon" style="background: #e0e7ff; color: #4338ca;">
                                <i class="fas fa-file"></i>
                            </div>
                            <span>文件消息</span>
                        </div>
                        <div class="type-stats">
                            <div class="type-count">2,671</div>
                            <div class="type-percentage">1.9%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 消息高峰时段分析 -->
        <div class="peak-hours">
            <div class="chart-header">
                <h3 class="chart-title">消息高峰时段分析</h3>
                <p style="color: #64748b; font-size: 14px; margin: 4px 0 0 0;">颜色深度表示消息活跃度</p>
            </div>
            <div class="hours-grid">
                <div class="hour-block hour-low">00</div>
                <div class="hour-block hour-low">01</div>
                <div class="hour-block hour-low">02</div>
                <div class="hour-block hour-low">03</div>
                <div class="hour-block hour-low">04</div>
                <div class="hour-block hour-low">05</div>
                <div class="hour-block hour-medium">06</div>
                <div class="hour-block hour-medium">07</div>
                <div class="hour-block hour-high">08</div>
                <div class="hour-block hour-peak">09</div>
                <div class="hour-block hour-peak">10</div>
                <div class="hour-block hour-high">11</div>
                
                <div class="hour-block hour-high">12</div>
                <div class="hour-block hour-medium">13</div>
                <div class="hour-block hour-peak">14</div>
                <div class="hour-block hour-peak">15</div>
                <div class="hour-block hour-high">16</div>
                <div class="hour-block hour-medium">17</div>
                <div class="hour-block hour-high">18</div>
                <div class="hour-block hour-peak">19</div>
                <div class="hour-block hour-peak">20</div>
                <div class="hour-block hour-high">21</div>
                <div class="hour-block hour-medium">22</div>
                <div class="hour-block hour-medium">23</div>
            </div>
        </div>
        
        <!-- 群组活跃度排名 -->
        <div class="group-rankings">
            <div class="chart-header" style="padding: 20px 24px; border-bottom: 1px solid #e2e8f0;">
                <h3 class="chart-title">群组消息活跃度排名</h3>
            </div>
            
            <div class="ranking-item">
                <div class="ranking-number top3">1</div>
                <div class="group-info">
                    <div class="group-name">TeleShop官方交流群</div>
                    <div class="group-type">超级群组 • 2,547人</div>
                </div>
                <div class="message-count">8,934</div>
            </div>
            
            <div class="ranking-item">
                <div class="ranking-number top3">2</div>
                <div class="group-info">
                    <div class="group-name">商品推广频道</div>
                    <div class="group-type">频道 • 8,923人</div>
                </div>
                <div class="message-count">5,672</div>
            </div>
            
            <div class="ranking-item">
                <div class="ranking-number top3">3</div>
                <div class="group-info">
                    <div class="group-name">客服支持群</div>
                    <div class="group-type">群组 • 1,234人</div>
                </div>
                <div class="message-count">3,456</div>
            </div>
            
            <div class="ranking-item">
                <div class="ranking-number">4</div>
                <div class="group-info">
                    <div class="group-name">技术讨论群</div>
                    <div class="group-type">群组 • 892人</div>
                </div>
                <div class="message-count">2,134</div>
            </div>
            
            <div class="ranking-item">
                <div class="ranking-number">5</div>
                <div class="group-info">
                    <div class="group-name">新用户指导群</div>
                    <div class="group-type">群组 • 567人</div>
                </div>
                <div class="message-count">1,789</div>
            </div>
        </div>
        
        <!-- 消息传输成功率 -->
        <div style="margin-top: 32px;">
            <div class="success-rate">
                <div class="chart-header">
                    <h3 class="chart-title">消息传输成功率监控</h3>
                </div>
                <div class="rate-metrics">
                    <div class="rate-metric">
                        <div class="rate-value rate-success">99.8%</div>
                        <div class="rate-label">总体成功率</div>
                    </div>
                    <div class="rate-metric">
                        <div class="rate-value rate-success">99.9%</div>
                        <div class="rate-label">文本消息</div>
                    </div>
                    <div class="rate-metric">
                        <div class="rate-value rate-success">99.5%</div>
                        <div class="rate-label">图片消息</div>
                    </div>
                    <div class="rate-metric">
                        <div class="rate-value rate-warning">98.2%</div>
                        <div class="rate-label">语音消息</div>
                    </div>
                    <div class="rate-metric">
                        <div class="rate-value rate-warning">97.8%</div>
                        <div class="rate-label">视频消息</div>
                    </div>
                    <div class="rate-metric">
                        <div class="rate-value rate-success">99.7%</div>
                        <div class="rate-label">文件消息</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 初始化图表
        let trendChart;
        
        document.addEventListener('DOMContentLoaded', function() {
            initializeTrendChart();
        });
        
        function initializeTrendChart() {
            const ctx = document.getElementById('trendChart').getContext('2d');
            trendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    datasets: [{
                        label: '群组消息',
                        data: [1200, 800, 3500, 2800, 4200, 5500, 2800],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '私聊消息',
                        data: [800, 500, 1800, 2200, 2800, 3200, 1500],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });
        }
        
        function updateTrendChart(period) {
            showNotification(`图表已切换到${period}视图`, 'info');
        }
        
        function exportStats() {
            showNotification('正在导出消息统计数据...', 'info');
            setTimeout(() => {
                showNotification('统计数据已导出!', 'success');
            }, 2000);
        }
        
        function refreshStats() {
            showNotification('正在刷新统计数据...', 'info');
            setTimeout(() => {
                showNotification('数据已更新!', 'success');
            }, 1500);
        }
        
        function showNotification(message, type) {
            console.log(`${type}: ${message}`);
        }
    </script>
</body>
</html> 