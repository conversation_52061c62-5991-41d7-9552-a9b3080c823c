<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>数据分析 - TeleShop后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title-section {
            flex: 1;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
        }

        .date-selector {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .date-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .date-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .date-btn:hover {
            background: #f8fafc;
        }

        .date-btn.active:hover {
            background: #2563eb;
        }

        /* 子菜单导航样式 */
        .sub-navigation {
            background: white;
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .nav-tabs {
            display: flex;
            gap: 4px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .nav-tab {
            padding: 12px 20px;
            border: none;
            background: transparent;
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
            min-width: max-content;
        }

        .nav-tab:hover {
            background: #f1f5f9;
            color: #475569;
        }

        .nav-tab.active {
            background: #3b82f6;
            color: white;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        .nav-tab.active:hover {
            background: #2563eb;
        }

        /* 分析内容区域 */
        .analysis-content {
            display: none;
        }

        .analysis-content.active {
            display: block;
        }

        /* 内容标题样式 */
        .content-header {
            margin-bottom: 24px;
        }

        .content-header h2 {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .content-header p {
            color: #64748b;
            font-size: 16px;
        }

        /* 关键指标 */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .kpi-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
            position: relative;
            overflow: hidden;
        }

        .kpi-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .kpi-card.revenue::before { background: linear-gradient(90deg, #059669, #10b981); }
        .kpi-card.users::before { background: linear-gradient(90deg, #3b82f6, #60a5fa); }
        .kpi-card.orders::before { background: linear-gradient(90deg, #d97706, #f59e0b); }
        .kpi-card.conversion::before { background: linear-gradient(90deg, #8b5cf6, #a78bfa); }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .kpi-title {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }

        .kpi-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
        }

        .kpi-trend.up {
            background: #dcfce7;
            color: #166534;
        }

        .kpi-trend.down {
            background: #fee2e2;
            color: #991b1b;
        }

        .kpi-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .kpi-compare {
            font-size: 14px;
            color: #64748b;
        }

        .kpi-compare .change {
            font-weight: 500;
        }

        .kpi-compare .change.positive {
            color: #059669;
        }

        .kpi-compare .change.negative {
            color: #dc2626;
        }

        /* 图表网格 */
        .chart-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }

        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .chart-options {
            display: flex;
            gap: 8px;
        }

        .chart-option {
            padding: 4px 8px;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .chart-option.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
        }

        /* 用户行为分析 */
        .behavior-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }

        .behavior-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .behavior-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
        }

        .behavior-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .behavior-item:last-child {
            border-bottom: none;
        }

        .behavior-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .behavior-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
        }

        .behavior-name {
            font-weight: 500;
            color: #1e293b;
        }

        .behavior-value {
            font-weight: 600;
            color: #1e293b;
        }

        .behavior-bar {
            width: 80px;
            height: 6px;
            background: #f1f5f9;
            border-radius: 3px;
            overflow: hidden;
            margin-left: 12px;
        }

        .behavior-progress {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        /* 热门内容分析 */
        .content-analysis {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .content-header {
            background: #f8fafc;
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .content-filters {
            display: flex;
            gap: 12px;
        }

        .filter-tab {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .filter-tab.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .content-table {
            width: 100%;
            border-collapse: collapse;
        }

        .content-table th,
        .content-table td {
            padding: 16px 24px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .content-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        .content-table tr:hover {
            background: #f8fafc;
        }

        .content-item {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .content-image {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            object-fit: cover;
        }

        .content-info {
            flex: 1;
        }

        .content-name {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .content-category {
            font-size: 12px;
            color: #64748b;
        }

        .metric-value {
            font-weight: 600;
            color: #1e293b;
        }

        .metric-change {
            font-size: 12px;
            margin-left: 8px;
        }

        .metric-change.positive {
            color: #059669;
        }

        .metric-change.negative {
            color: #dc2626;
        }

        /* 地理分析 */
        .geo-analysis {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }

        .geo-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .geo-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
        }

        .geo-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .geo-item:last-child {
            border-bottom: none;
        }

        .geo-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .geo-rank {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #f1f5f9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
        }

        .geo-rank.top3 {
            background: #3b82f6;
            color: white;
        }

        .geo-name {
            font-weight: 500;
            color: #1e293b;
        }

        .geo-metrics {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .geo-value {
            font-weight: 600;
            color: #1e293b;
        }

        .geo-percentage {
            font-size: 12px;
            color: #64748b;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .chart-grid {
                grid-template-columns: 1fr;
            }

            .behavior-grid,
            .geo-analysis {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .page-header {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }

            .kpi-grid {
                grid-template-columns: 1fr;
            }

            .date-selector {
                justify-content: center;
            }

            .content-table {
                font-size: 14px;
            }

            .content-table th,
            .content-table td {
                padding: 12px 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title-section">
                <h1 class="page-title">数据分析</h1>
                <p class="page-subtitle">深入分析业务数据，洞察用户行为和市场趋势</p>
            </div>
            <div class="date-selector">
                <button class="date-btn">今日</button>
                <button class="date-btn active">7天</button>
                <button class="date-btn">30天</button>
                <button class="date-btn">90天</button>
                <button class="date-btn">自定义</button>
                <button onclick="testAllSubMenus()" style="background: #ef4444; color: white; margin-left: 10px;">🔧 测试子菜单</button>
            </div>
        </div>

        <!-- 子菜单导航 -->
        <div class="sub-navigation">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="switchSection('overview', this)">
                    <i class="fas fa-chart-line"></i>
                    概览
                </button>
                <button class="nav-tab" onclick="switchSection('sales', this)">
                    <i class="fas fa-chart-bar"></i>
                    销售分析
                </button>
                <button class="nav-tab" onclick="switchSection('users', this)">
                    <i class="fas fa-users"></i>
                    用户分析
                </button>
                <button class="nav-tab" onclick="switchSection('products', this)">
                    <i class="fas fa-box"></i>
                    商品分析
                </button>
                <button class="nav-tab" onclick="switchSection('finance', this)">
                    <i class="fas fa-dollar-sign"></i>
                    财务报告
                </button>
            </div>
        </div>

        <!-- 调试信息面板（调试模式） -->
        <div id="debugPanel" style="position: fixed; top: 10px; right: 10px; background: #1e293b; color: white; padding: 10px; border-radius: 6px; font-family: monospace; font-size: 12px; max-width: 300px; z-index: 9999; display: none;">
            <div style="font-weight: bold; margin-bottom: 5px;">调试信息</div>
            <div id="debugLog"></div>
            <button onclick="toggleDebug()" style="background: #3b82f6; color: white; border: none; padding: 5px 10px; border-radius: 3px; font-size: 11px; margin-top: 5px;">关闭</button>
        </div>

        <!-- 概览内容区域 -->
        <div id="overview-content" class="analysis-content active">

        <!-- 关键指标 -->
        <div class="kpi-grid">
            <div class="kpi-card revenue">
                <div class="kpi-header">
                    <span class="kpi-title">总收入</span>
                    <div class="kpi-trend up">
                        <i class="fas fa-arrow-up"></i>
                        +15.3%
                    </div>
                </div>
                <div class="kpi-value">¥2,458,690</div>
                <div class="kpi-compare">
                    比上周 <span class="change positive">+¥324,570</span>
                </div>
            </div>

            <div class="kpi-card users">
                <div class="kpi-header">
                    <span class="kpi-title">活跃用户</span>
                    <div class="kpi-trend up">
                        <i class="fas fa-arrow-up"></i>
                        +8.7%
                    </div>
                </div>
                <div class="kpi-value">12,847</div>
                <div class="kpi-compare">
                    比上周 <span class="change positive">+1,034</span>
                </div>
            </div>

            <div class="kpi-card orders">
                <div class="kpi-header">
                    <span class="kpi-title">订单数量</span>
                    <div class="kpi-trend up">
                        <i class="fas fa-arrow-up"></i>
                        +12.4%
                    </div>
                </div>
                <div class="kpi-value">3,892</div>
                <div class="kpi-compare">
                    比上周 <span class="change positive">+431</span>
                </div>
            </div>

            <div class="kpi-card conversion">
                <div class="kpi-header">
                    <span class="kpi-title">转化率</span>
                    <div class="kpi-trend down">
                        <i class="fas fa-arrow-down"></i>
                        -2.1%
                    </div>
                </div>
                <div class="kpi-value">3.47%</div>
                <div class="kpi-compare">
                    比上周 <span class="change negative">-0.07%</span>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="chart-grid">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">用户流量趋势</h3>
                    <div class="chart-options">
                        <button class="chart-option active">访问量</button>
                        <button class="chart-option">新用户</button>
                        <button class="chart-option">活跃用户</button>
                    </div>
                </div>
                <div class="chart-wrapper">
                    <canvas id="trafficChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">用户来源</h3>
                </div>
                <div class="chart-wrapper">
                    <canvas id="sourceChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 用户行为分析 -->
        <div class="behavior-grid">
            <div class="behavior-card">
                <h3 class="behavior-title">页面浏览分析</h3>
                
                <div class="behavior-item">
                    <div class="behavior-info">
                        <div class="behavior-icon" style="background: #3b82f6;">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="behavior-name">首页</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <span class="behavior-value">45,678</span>
                        <div class="behavior-bar">
                            <div class="behavior-progress" style="width: 90%; background: #3b82f6;"></div>
                        </div>
                    </div>
                </div>

                <div class="behavior-item">
                    <div class="behavior-info">
                        <div class="behavior-icon" style="background: #059669;">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <span class="behavior-name">商品页</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <span class="behavior-value">32,456</span>
                        <div class="behavior-bar">
                            <div class="behavior-progress" style="width: 65%; background: #059669;"></div>
                        </div>
                    </div>
                </div>

                <div class="behavior-item">
                    <div class="behavior-info">
                        <div class="behavior-icon" style="background: #d97706;">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <span class="behavior-name">购物车</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <span class="behavior-value">18,234</span>
                        <div class="behavior-bar">
                            <div class="behavior-progress" style="width: 36%; background: #d97706;"></div>
                        </div>
                    </div>
                </div>

                <div class="behavior-item">
                    <div class="behavior-info">
                        <div class="behavior-icon" style="background: #8b5cf6;">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <span class="behavior-name">结算页</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <span class="behavior-value">8,901</span>
                        <div class="behavior-bar">
                            <div class="behavior-progress" style="width: 18%; background: #8b5cf6;"></div>
                        </div>
                    </div>
                </div>

                <div class="behavior-item">
                    <div class="behavior-info">
                        <div class="behavior-icon" style="background: #dc2626;">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="behavior-name">个人中心</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <span class="behavior-value">15,678</span>
                        <div class="behavior-bar">
                            <div class="behavior-progress" style="width: 31%; background: #dc2626;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="behavior-card">
                <h3 class="behavior-title">用户互动行为</h3>
                
                <div class="behavior-item">
                    <div class="behavior-info">
                        <div class="behavior-icon" style="background: #059669;">
                            <i class="fas fa-comments"></i>
                        </div>
                        <span class="behavior-name">聊天消息</span>
                    </div>
                    <span class="behavior-value">125,678 条</span>
                </div>

                <div class="behavior-item">
                    <div class="behavior-info">
                        <div class="behavior-icon" style="background: #3b82f6;">
                            <i class="fas fa-search"></i>
                        </div>
                        <span class="behavior-name">搜索查询</span>
                    </div>
                    <span class="behavior-value">23,456 次</span>
                </div>

                <div class="behavior-item">
                    <div class="behavior-info">
                        <div class="behavior-icon" style="background: #d97706;">
                            <i class="fas fa-heart"></i>
                        </div>
                        <span class="behavior-name">商品收藏</span>
                    </div>
                    <span class="behavior-value">8,902 个</span>
                </div>

                <div class="behavior-item">
                    <div class="behavior-info">
                        <div class="behavior-icon" style="background: #8b5cf6;">
                            <i class="fas fa-share"></i>
                        </div>
                        <span class="behavior-name">分享行为</span>
                    </div>
                    <span class="behavior-value">2,345 次</span>
                </div>

                <div class="behavior-item">
                    <div class="behavior-info">
                        <div class="behavior-icon" style="background: #dc2626;">
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="behavior-name">商品评价</span>
                    </div>
                    <span class="behavior-value">1,567 条</span>
                </div>
            </div>
        </div>

        <!-- 热门内容分析 -->
        <div class="content-analysis">
            <div class="content-header">
                <h3 class="content-title">热门内容分析</h3>
                <div class="content-filters">
                    <button class="filter-tab active">商品</button>
                    <button class="filter-tab">聊天话题</button>
                    <button class="filter-tab">搜索关键词</button>
                </div>
            </div>

            <table class="content-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>内容</th>
                        <th>浏览量</th>
                        <th>转化率</th>
                        <th>收入</th>
                        <th>趋势</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>
                            <div class="content-item">
                                <img src="https://via.placeholder.com/40" alt="商品图片" class="content-image">
                                <div class="content-info">
                                    <div class="content-name">iPhone 15 Pro Max</div>
                                    <div class="content-category">电子产品</div>
                                </div>
                            </div>
                        </td>
                        <td class="metric-value">45,678</td>
                        <td class="metric-value">
                            5.67%
                            <span class="metric-change positive">+0.34%</span>
                        </td>
                        <td class="metric-value">¥1,234,567</td>
                        <td>
                            <i class="fas fa-arrow-up" style="color: #059669;"></i>
                        </td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>
                            <div class="content-item">
                                <img src="https://via.placeholder.com/40" alt="商品图片" class="content-image">
                                <div class="content-info">
                                    <div class="content-name">Nike Air Max 270</div>
                                    <div class="content-category">运动户外</div>
                                </div>
                            </div>
                        </td>
                        <td class="metric-value">32,456</td>
                        <td class="metric-value">
                            3.89%
                            <span class="metric-change negative">-0.12%</span>
                        </td>
                        <td class="metric-value">¥567,890</td>
                        <td>
                            <i class="fas fa-arrow-down" style="color: #dc2626;"></i>
                        </td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>
                            <div class="content-item">
                                <img src="https://via.placeholder.com/40" alt="商品图片" class="content-image">
                                <div class="content-info">
                                    <div class="content-name">戴森吸尘器 V15</div>
                                    <div class="content-category">家居用品</div>
                                </div>
                            </div>
                        </td>
                        <td class="metric-value">28,901</td>
                        <td class="metric-value">
                            4.23%
                            <span class="metric-change positive">+0.78%</span>
                        </td>
                        <td class="metric-value">¥389,456</td>
                        <td>
                            <i class="fas fa-arrow-up" style="color: #059669;"></i>
                        </td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>
                            <div class="content-item">
                                <img src="https://via.placeholder.com/40" alt="商品图片" class="content-image">
                                <div class="content-info">
                                    <div class="content-name">Uniqlo 基础T恤</div>
                                    <div class="content-category">服装服饰</div>
                                </div>
                            </div>
                        </td>
                        <td class="metric-value">25,678</td>
                        <td class="metric-value">
                            8.12%
                            <span class="metric-change positive">+1.23%</span>
                        </td>
                        <td class="metric-value">¥156,789</td>
                        <td>
                            <i class="fas fa-arrow-up" style="color: #059669;"></i>
                        </td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>
                            <div class="content-item">
                                <img src="https://via.placeholder.com/40" alt="商品图片" class="content-image">
                                <div class="content-info">
                                    <div class="content-name">小米智能音箱</div>
                                    <div class="content-category">电子产品</div>
                                </div>
                            </div>
                        </td>
                        <td class="metric-value">18,234</td>
                        <td class="metric-value">
                            2.45%
                            <span class="metric-change negative">-0.56%</span>
                        </td>
                        <td class="metric-value">¥89,234</td>
                        <td>
                            <i class="fas fa-arrow-down" style="color: #dc2626;"></i>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 地理分析 -->
        <div class="geo-analysis">
            <div class="geo-card">
                <h3 class="geo-title">用户地域分布</h3>
                
                <div class="geo-item">
                    <div class="geo-info">
                        <span class="geo-rank top3">1</span>
                        <span class="geo-name">广东</span>
                    </div>
                    <div class="geo-metrics">
                        <span class="geo-value">3,456</span>
                        <span class="geo-percentage">26.9%</span>
                    </div>
                </div>

                <div class="geo-item">
                    <div class="geo-info">
                        <span class="geo-rank top3">2</span>
                        <span class="geo-name">北京</span>
                    </div>
                    <div class="geo-metrics">
                        <span class="geo-value">2,890</span>
                        <span class="geo-percentage">22.5%</span>
                    </div>
                </div>

                <div class="geo-item">
                    <div class="geo-info">
                        <span class="geo-rank top3">3</span>
                        <span class="geo-name">上海</span>
                    </div>
                    <div class="geo-metrics">
                        <span class="geo-value">2,234</span>
                        <span class="geo-percentage">17.4%</span>
                    </div>
                </div>

                <div class="geo-item">
                    <div class="geo-info">
                        <span class="geo-rank">4</span>
                        <span class="geo-name">江苏</span>
                    </div>
                    <div class="geo-metrics">
                        <span class="geo-value">1,567</span>
                        <span class="geo-percentage">12.2%</span>
                    </div>
                </div>

                <div class="geo-item">
                    <div class="geo-info">
                        <span class="geo-rank">5</span>
                        <span class="geo-name">浙江</span>
                    </div>
                    <div class="geo-metrics">
                        <span class="geo-value">1,234</span>
                        <span class="geo-percentage">9.6%</span>
                    </div>
                </div>
            </div>

            <div class="geo-card">
                <h3 class="geo-title">销售额地域分布</h3>
                
                <div class="geo-item">
                    <div class="geo-info">
                        <span class="geo-rank top3">1</span>
                        <span class="geo-name">北京</span>
                    </div>
                    <div class="geo-metrics">
                        <span class="geo-value">¥845K</span>
                        <span class="geo-percentage">32.4%</span>
                    </div>
                </div>

                <div class="geo-item">
                    <div class="geo-info">
                        <span class="geo-rank top3">2</span>
                        <span class="geo-name">上海</span>
                    </div>
                    <div class="geo-metrics">
                        <span class="geo-value">¥567K</span>
                        <span class="geo-percentage">21.7%</span>
                    </div>
                </div>

                <div class="geo-item">
                    <div class="geo-info">
                        <span class="geo-rank top3">3</span>
                        <span class="geo-name">广东</span>
                    </div>
                    <div class="geo-metrics">
                        <span class="geo-value">¥456K</span>
                        <span class="geo-percentage">17.5%</span>
                    </div>
                </div>

                <div class="geo-item">
                    <div class="geo-info">
                        <span class="geo-rank">4</span>
                        <span class="geo-name">江苏</span>
                    </div>
                    <div class="geo-metrics">
                        <span class="geo-value">¥234K</span>
                        <span class="geo-percentage">9.0%</span>
                    </div>
                </div>

                <div class="geo-item">
                    <div class="geo-info">
                        <span class="geo-rank">5</span>
                        <span class="geo-name">浙江</span>
                    </div>
                    <div class="geo-metrics">
                        <span class="geo-value">¥189K</span>
                        <span class="geo-percentage">7.3%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div> <!-- 结束概览内容区域 -->

    <script src="../../assets/js/image-error-handler.js"></script>
    <script>
        // 用户流量趋势图表
        const trafficCtx = document.getElementById('trafficChart').getContext('2d');
        const trafficChart = new Chart(trafficCtx, {
            type: 'line',
            data: {
                labels: ['1月9日', '1月10日', '1月11日', '1月12日', '1月13日', '1月14日', '1月15日'],
                datasets: [{
                    label: '访问量',
                    data: [45600, 52300, 48900, 59800, 67200, 61500, 68900],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '新用户',
                    data: [8900, 12400, 9800, 15600, 18200, 16800, 19300],
                    borderColor: '#059669',
                    backgroundColor: 'rgba(5, 150, 105, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '活跃用户',
                    data: [23400, 28900, 25600, 32100, 36800, 34200, 38900],
                    borderColor: '#d97706',
                    backgroundColor: 'rgba(217, 119, 6, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return (value / 1000) + 'K';
                            }
                        }
                    }
                }
            }
        });

        // 用户来源图表
        const sourceCtx = document.getElementById('sourceChart').getContext('2d');
        const sourceChart = new Chart(sourceCtx, {
            type: 'doughnut',
            data: {
                labels: ['Telegram', '微信', '直接访问', '搜索引擎', '其他'],
                datasets: [{
                    data: [45, 25, 15, 10, 5],
                    backgroundColor: [
                        '#3b82f6',
                        '#059669',
                        '#d97706',
                        '#8b5cf6',
                        '#dc2626'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });

        // 日期选择器
        document.querySelectorAll('.date-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.date-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                console.log('切换时间范围:', this.textContent);
                // 这里可以添加重新加载数据的逻辑
            });
        });

        // 图表选项切换
        document.querySelectorAll('.chart-option').forEach(option => {
            option.addEventListener('click', function() {
                const parent = this.closest('.chart-card');
                parent.querySelectorAll('.chart-option').forEach(o => o.classList.remove('active'));
                this.classList.add('active');
                
                console.log('切换图表指标:', this.textContent);
                // 这里可以添加切换图表数据的逻辑
            });
        });

        // 内容筛选标签
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                console.log('切换内容类型:', this.textContent);
                // 这里可以添加切换表格数据的逻辑
            });
        });

        // 实时数据更新
        function updateAnalyticsData() {
            console.log('更新分析数据...');
            // 这里可以添加实际的数据更新逻辑
        }

        // 每60秒更新一次数据
        setInterval(updateAnalyticsData, 60000);

        // 子菜单切换功能
        function switchSection(sectionId, tabElement) {
            debugLog(`🔄 开始切换到: ${sectionId}`);
            
            try {
                // 隐藏所有内容区域
                const allContent = document.querySelectorAll('.analysis-content');
                debugLog(`找到 ${allContent.length} 个内容区域`);
                allContent.forEach(content => {
                    content.classList.remove('active');
                });
                
                // 移除所有标签的active状态
                const allTabs = document.querySelectorAll('.nav-tab');
                debugLog(`找到 ${allTabs.length} 个导航标签`);
                allTabs.forEach(tab => {
                    tab.classList.remove('active');
                });
                
                // 显示对应的内容区域
                const targetContent = document.getElementById(sectionId + '-content');
                
                if (targetContent) {
                    targetContent.classList.add('active');
                    debugLog(`✓ 成功激活: ${sectionId}`);
                } else {
                    debugLog(`⚠ 内容区域不存在，创建: ${sectionId}`);
                    // 如果内容区域不存在，创建一个默认的内容
                    createDefaultContent(sectionId);
                }
                
                // 激活点击的标签
                if (tabElement) {
                    tabElement.classList.add('active');
                    debugLog(`✓ 激活标签按钮`);
                } else {
                    debugLog(`⚠ 标签按钮为空`);
                }
                
                debugLog(`✅ 切换完成: ${sectionId}`);
                
            } catch (error) {
                debugLog(`❌ 切换错误: ${error.message}`);
                console.error('switchSection 错误:', error);
            }
        }

        // 创建默认内容区域
        function createDefaultContent(sectionId) {
            const container = document.querySelector('.container');
            const existingContent = document.getElementById(sectionId + '-content');
            
            if (existingContent) return;
            
            const contentDiv = document.createElement('div');
            contentDiv.id = sectionId + '-content';
            contentDiv.className = 'analysis-content active';
            
            let contentHTML = '';
            
            switch(sectionId) {
                case 'sales':
                    contentHTML = `
                        <div class="content-header">
                            <h2>销售分析</h2>
                            <p>深入分析销售数据，掌握业务增长趋势</p>
                        </div>
                        <div class="kpi-grid">
                            <div class="kpi-card revenue">
                                <div class="kpi-header">
                                    <span class="kpi-title">总销售额</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 12.5%</span>
                                </div>
                                <div class="kpi-value">¥2,845,678</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+¥245,678</span></div>
                            </div>
                            <div class="kpi-card orders">
                                <div class="kpi-header">
                                    <span class="kpi-title">订单数量</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 8.3%</span>
                                </div>
                                <div class="kpi-value">4,256</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+327</span></div>
                            </div>
                            <div class="kpi-card conversion">
                                <div class="kpi-header">
                                    <span class="kpi-title">转化率</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 2.1%</span>
                                </div>
                                <div class="kpi-value">5.67%</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+0.34%</span></div>
                            </div>
                            <div class="kpi-card users">
                                <div class="kpi-header">
                                    <span class="kpi-title">客单价</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 4.2%</span>
                                </div>
                                <div class="kpi-value">¥668</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+¥27</span></div>
                            </div>
                        </div>
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">销售趋势分析</h3>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="salesChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    `;
                    break;
                case 'users':
                    contentHTML = `
                        <div class="content-header">
                            <h2>用户分析</h2>
                            <p>了解用户行为模式，优化用户体验</p>
                        </div>
                        <div class="kpi-grid">
                            <div class="kpi-card users">
                                <div class="kpi-header">
                                    <span class="kpi-title">总用户数</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 15.2%</span>
                                </div>
                                <div class="kpi-value">145,678</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+19,234</span></div>
                            </div>
                            <div class="kpi-card users">
                                <div class="kpi-header">
                                    <span class="kpi-title">活跃用户</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 8.9%</span>
                                </div>
                                <div class="kpi-value">89,456</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+7,321</span></div>
                            </div>
                            <div class="kpi-card users">
                                <div class="kpi-header">
                                    <span class="kpi-title">新增用户</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 22.3%</span>
                                </div>
                                <div class="kpi-value">12,345</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+2,234</span></div>
                            </div>
                            <div class="kpi-card users">
                                <div class="kpi-header">
                                    <span class="kpi-title">留存率</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 3.1%</span>
                                </div>
                                <div class="kpi-value">73.4%</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+2.1%</span></div>
                            </div>
                        </div>
                        <div class="behavior-grid">
                            <div class="behavior-card">
                                <h3 class="behavior-title">用户行为统计</h3>
                                <div class="behavior-item">
                                    <div class="behavior-info">
                                        <div class="behavior-icon" style="background: #3b82f6;">
                                            <i class="fas fa-eye"></i>
                                        </div>
                                        <span class="behavior-name">页面浏览</span>
                                    </div>
                                    <div style="display: flex; align-items: center;">
                                        <span class="behavior-value">456,789</span>
                                        <div class="behavior-bar">
                                            <div class="behavior-progress" style="width: 85%; background: #3b82f6;"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="behavior-item">
                                    <div class="behavior-info">
                                        <div class="behavior-icon" style="background: #059669;">
                                            <i class="fas fa-shopping-cart"></i>
                                        </div>
                                        <span class="behavior-name">加入购物车</span>
                                    </div>
                                    <div style="display: flex; align-items: center;">
                                        <span class="behavior-value">23,456</span>
                                        <div class="behavior-bar">
                                            <div class="behavior-progress" style="width: 65%; background: #059669;"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="behavior-item">
                                    <div class="behavior-info">
                                        <div class="behavior-icon" style="background: #d97706;">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                        <span class="behavior-name">完成支付</span>
                                    </div>
                                    <div style="display: flex; align-items: center;">
                                        <span class="behavior-value">12,789</span>
                                        <div class="behavior-bar">
                                            <div class="behavior-progress" style="width: 45%; background: #d97706;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="behavior-card">
                                <h3 class="behavior-title">设备来源分析</h3>
                                <div class="behavior-item">
                                    <div class="behavior-info">
                                        <div class="behavior-icon" style="background: #8b5cf6;">
                                            <i class="fas fa-mobile-alt"></i>
                                        </div>
                                        <span class="behavior-name">移动端</span>
                                    </div>
                                    <div style="display: flex; align-items: center;">
                                        <span class="behavior-value">67.2%</span>
                                        <div class="behavior-bar">
                                            <div class="behavior-progress" style="width: 67%; background: #8b5cf6;"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="behavior-item">
                                    <div class="behavior-info">
                                        <div class="behavior-icon" style="background: #06b6d4;">
                                            <i class="fas fa-desktop"></i>
                                        </div>
                                        <span class="behavior-name">桌面端</span>
                                    </div>
                                    <div style="display: flex; align-items: center;">
                                        <span class="behavior-value">28.5%</span>
                                        <div class="behavior-bar">
                                            <div class="behavior-progress" style="width: 28%; background: #06b6d4;"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="behavior-item">
                                    <div class="behavior-info">
                                        <div class="behavior-icon" style="background: #dc2626;">
                                            <i class="fas fa-tablet-alt"></i>
                                        </div>
                                        <span class="behavior-name">平板端</span>
                                    </div>
                                    <div style="display: flex; align-items: center;">
                                        <span class="behavior-value">4.3%</span>
                                        <div class="behavior-bar">
                                            <div class="behavior-progress" style="width: 4%; background: #dc2626;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                case 'products':
                    contentHTML = `
                        <div class="content-header">
                            <h2>商品分析</h2>
                            <p>分析商品表现，优化商品策略</p>
                        </div>
                        <div class="kpi-grid">
                            <div class="kpi-card orders">
                                <div class="kpi-header">
                                    <span class="kpi-title">商品总数</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 5.2%</span>
                                </div>
                                <div class="kpi-value">12,456</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+612</span></div>
                            </div>
                            <div class="kpi-card revenue">
                                <div class="kpi-header">
                                    <span class="kpi-title">畅销商品</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 8.7%</span>
                                </div>
                                <div class="kpi-value">2,345</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+187</span></div>
                            </div>
                            <div class="kpi-card conversion">
                                <div class="kpi-header">
                                    <span class="kpi-title">库存周转率</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 12.3%</span>
                                </div>
                                <div class="kpi-value">4.8</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+0.5</span></div>
                            </div>
                            <div class="kpi-card users">
                                <div class="kpi-header">
                                    <span class="kpi-title">平均评分</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 2.1%</span>
                                </div>
                                <div class="kpi-value">4.6</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+0.1</span></div>
                            </div>
                        </div>
                        <div class="content-analysis">
                            <div class="content-header">
                                <h3 class="content-title">热门商品排行</h3>
                                <div class="content-filters">
                                    <button class="filter-tab active">销量</button>
                                    <button class="filter-tab">收入</button>
                                    <button class="filter-tab">评分</button>
                                </div>
                            </div>
                            <table class="content-table">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>商品</th>
                                        <th>销量</th>
                                        <th>收入</th>
                                        <th>评分</th>
                                        <th>趋势</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>
                                            <div class="content-item">
                                                <div class="image-placeholder type-product size-md">
                                                    <i class="fas fa-mobile-alt"></i>
                                                </div>
                                                <div class="content-info">
                                                    <div class="content-name">iPhone 15 Pro Max</div>
                                                    <div class="content-category">电子产品</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="metric-value">1,234</td>
                                        <td class="metric-value">¥1,234,567</td>
                                        <td class="metric-value">4.8 ⭐</td>
                                        <td><i class="fas fa-arrow-up" style="color: #059669;"></i></td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>
                                            <div class="content-item">
                                                <div class="image-placeholder type-product size-md">
                                                    <i class="fas fa-shoe-prints"></i>
                                                </div>
                                                <div class="content-info">
                                                    <div class="content-name">Nike Air Max 270</div>
                                                    <div class="content-category">运动户外</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="metric-value">987</td>
                                        <td class="metric-value">¥567,890</td>
                                        <td class="metric-value">4.6 ⭐</td>
                                        <td><i class="fas fa-arrow-up" style="color: #059669;"></i></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    `;
                    break;
                case 'finance':
                    contentHTML = `
                        <div class="content-header">
                            <h2>财务报告</h2>
                            <p>全面的财务数据分析和报告</p>
                        </div>
                        <div class="kpi-grid">
                            <div class="kpi-card revenue">
                                <div class="kpi-header">
                                    <span class="kpi-title">总收入</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 15.8%</span>
                                </div>
                                <div class="kpi-value">¥5,678,901</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+¥756,234</span></div>
                            </div>
                            <div class="kpi-card orders">
                                <div class="kpi-header">
                                    <span class="kpi-title">净利润</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 12.3%</span>
                                </div>
                                <div class="kpi-value">¥1,234,567</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+¥135,234</span></div>
                            </div>
                            <div class="kpi-card conversion">
                                <div class="kpi-header">
                                    <span class="kpi-title">毛利率</span>
                                    <span class="kpi-trend up"><i class="fas fa-arrow-up"></i> 2.1%</span>
                                </div>
                                <div class="kpi-value">21.7%</div>
                                <div class="kpi-compare">较上月 <span class="change positive">+0.5%</span></div>
                            </div>
                            <div class="kpi-card users">
                                <div class="kpi-header">
                                    <span class="kpi-title">退款率</span>
                                    <span class="kpi-trend down"><i class="fas fa-arrow-down"></i> 1.2%</span>
                                </div>
                                <div class="kpi-value">2.3%</div>
                                <div class="kpi-compare">较上月 <span class="change negative">-0.3%</span></div>
                            </div>
                        </div>
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">财务收入趋势</h3>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="financeChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    `;
                    break;
            }
            
            contentDiv.innerHTML = contentHTML;
            
            // 将新内容插入到容器的最后
            container.appendChild(contentDiv);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据分析页面已加载');
            updateAnalyticsData();
            
            // 确保子菜单功能正常工作
            const navTabs = document.querySelectorAll('.nav-tab');
            console.log('找到导航标签数量:', navTabs.length);
            
            // 为每个导航标签添加事件监听器（双重保险）
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const onclick = this.getAttribute('onclick');
                    if (onclick) {
                        // 解析onclick属性中的参数
                        const match = onclick.match(/switchSection\('([^']+)',\s*this\)/);
                        if (match) {
                            const sectionId = match[1];
                            console.log('通过事件监听器切换到:', sectionId);
                            switchSection(sectionId, this);
                        }
                    }
                });
            });
            
            // 检查概览内容区域是否存在
            const overviewContent = document.getElementById('overview-content');
            if (overviewContent) {
                console.log('概览内容区域已存在');
                debugLog('概览内容区域: ✓ 已存在');
            } else {
                console.log('概览内容区域不存在，需要检查HTML结构');
                debugLog('概览内容区域: ✗ 不存在');
            }
            
            // 显示调试面板
            setTimeout(() => {
                document.getElementById('debugPanel').style.display = 'block';
                debugLog('页面加载完成，调试模式已启用');
            }, 1000);
        });

        // 调试功能
        function debugLog(message) {
            const debugLog = document.getElementById('debugLog');
            if (debugLog) {
                const time = new Date().toLocaleTimeString();
                debugLog.innerHTML += `<div>[${time}] ${message}</div>`;
                debugLog.scrollTop = debugLog.scrollHeight;
            }
            console.log(message);
        }

        function toggleDebug() {
            const panel = document.getElementById('debugPanel');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
            } else {
                panel.style.display = 'none';
            }
        }

        // 测试所有子菜单功能
        function testAllSubMenus() {
            const sections = ['overview', 'sales', 'users', 'products', 'finance'];
            let index = 0;
            
            function testNext() {
                if (index < sections.length) {
                    const sectionId = sections[index];
                    const tabElement = document.querySelector(`[onclick*="${sectionId}"]`);
                    
                    debugLog(`测试子菜单: ${sectionId}`);
                    
                    if (tabElement) {
                        switchSection(sectionId, tabElement);
                        index++;
                        setTimeout(testNext, 2000);
                    } else {
                        debugLog(`✗ 找不到子菜单按钮: ${sectionId}`);
                        index++;
                        setTimeout(testNext, 1000);
                    }
                } else {
                    debugLog('所有子菜单测试完成');
                }
            }
            
            testNext();
        }
    </script>
</body>
</html> 