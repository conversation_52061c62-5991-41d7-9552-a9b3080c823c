<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户数据分析 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .analytics-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .time-selector {
            background: white;
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .time-btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .time-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .metric-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .metric-title {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }
        
        .metric-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .metric-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .change-positive {
            color: #10b981;
        }
        
        .change-negative {
            color: #ef4444;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .region-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .region-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .region-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .region-flag {
            width: 24px;
            height: 16px;
            border-radius: 2px;
            background: #e2e8f0;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .region-stats {
            text-align: right;
        }
        
        .region-users {
            font-weight: 600;
            color: #1e293b;
        }
        
        .region-percentage {
            font-size: 12px;
            color: #64748b;
        }
        
        .retention-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .table-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .retention-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 1px;
            background: #f1f5f9;
            padding: 1px;
        }
        
        .retention-cell {
            background: white;
            padding: 12px 8px;
            text-align: center;
            font-size: 12px;
        }
        
        .retention-header {
            background: #f8fafc;
            font-weight: 600;
            color: #64748b;
        }
        
        .retention-high {
            background: #10b981;
            color: white;
        }
        
        .retention-medium {
            background: #fbbf24;
            color: white;
        }
        
        .retention-low {
            background: #ef4444;
            color: white;
        }
    </style>
</head>
<body>
    <div class="analytics-page">
        <div class="page-header">
            <h1 class="page-title">用户数据分析</h1>
            <div class="page-actions">
                <a href="../dashboard/index.html" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i> 仪表板
                </a>
                <a href="index.html" class="action-btn">
                    <i class="fas fa-analytics"></i> 分析首页
                </a>
                <a href="message-data-statistics.html" class="action-btn">
                    <i class="fas fa-envelope"></i> 消息统计
                </a>
                <a href="enhanced-analytics.html" class="action-btn">
                    <i class="fas fa-chart-line"></i> 高级分析
                </a>
                <button class="action-btn" onclick="exportReport()">
                    <i class="fas fa-download"></i> 导出报告
                </button>
                <button class="action-btn primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> 刷新数据
                </button>
            </div>
        </div>
        
        <div class="time-selector">
            <span style="font-weight: 500; color: #374151;">时间范围：</span>
            <button class="time-btn" onclick="selectTimeRange('7d')">最近7天</button>
            <button class="time-btn active" onclick="selectTimeRange('30d')">最近30天</button>
            <button class="time-btn" onclick="selectTimeRange('90d')">最近90天</button>
            <button class="time-btn" onclick="selectTimeRange('1y')">最近1年</button>
            <button class="time-btn" onclick="selectTimeRange('custom')">自定义</button>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">总用户数</span>
                    <div class="metric-icon" style="background: #dbeafe; color: #2563eb;">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="metric-value">12,847</div>
                <div class="metric-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    +15.3% 本月
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">日活跃用户(DAU)</span>
                    <div class="metric-icon" style="background: #dcfce7; color: #16a34a;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="metric-value">3,428</div>
                <div class="metric-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    +8.7% 昨日
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">月活跃用户(MAU)</span>
                    <div class="metric-icon" style="background: #fef3c7; color: #d97706;">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
                <div class="metric-value">8,954</div>
                <div class="metric-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    +12.1% 本月
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">新用户注册</span>
                    <div class="metric-icon" style="background: #fce7f3; color: #be185d;">
                        <i class="fas fa-user-plus"></i>
                    </div>
                </div>
                <div class="metric-value">247</div>
                <div class="metric-change change-negative">
                    <i class="fas fa-arrow-down"></i>
                    -3.2% 昨日
                </div>
            </div>
        </div>
        
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">用户增长趋势</h3>
                    <div class="chart-controls">
                        <select onchange="updateGrowthChart(this.value)" style="padding: 4px 8px; border: 1px solid #e2e8f0; border-radius: 4px;">
                            <option value="daily">按天</option>
                            <option value="weekly">按周</option>
                            <option value="monthly">按月</option>
                        </select>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="growthChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">地域分布</h3>
                </div>
                <div class="region-list">
                    <div class="region-item">
                        <div class="region-info">
                            <div class="region-flag">🇨🇳</div>
                            <span>中国</span>
                        </div>
                        <div class="region-stats">
                            <div class="region-users">8,456</div>
                            <div class="region-percentage">65.8%</div>
                        </div>
                    </div>
                    
                    <div class="region-item">
                        <div class="region-info">
                            <div class="region-flag">🇺🇸</div>
                            <span>美国</span>
                        </div>
                        <div class="region-stats">
                            <div class="region-users">1,923</div>
                            <div class="region-percentage">15.0%</div>
                        </div>
                    </div>
                    
                    <div class="region-item">
                        <div class="region-info">
                            <div class="region-flag">🇯🇵</div>
                            <span>日本</span>
                        </div>
                        <div class="region-stats">
                            <div class="region-users">847</div>
                            <div class="region-percentage">6.6%</div>
                        </div>
                    </div>
                    
                    <div class="region-item">
                        <div class="region-info">
                            <div class="region-flag">🇰🇷</div>
                            <span>韩国</span>
                        </div>
                        <div class="region-stats">
                            <div class="region-users">623</div>
                            <div class="region-percentage">4.8%</div>
                        </div>
                    </div>
                    
                    <div class="region-item">
                        <div class="region-info">
                            <div class="region-flag">🌐</div>
                            <span>其他</span>
                        </div>
                        <div class="region-stats">
                            <div class="region-users">998</div>
                            <div class="region-percentage">7.8%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="retention-table">
            <div class="table-header">
                <h3 class="chart-title">用户留存率分析</h3>
                <p style="color: #64748b; font-size: 14px; margin: 8px 0 0 0;">按注册日期分组的用户留存情况</p>
            </div>
            <div class="retention-grid">
                <div class="retention-cell retention-header">注册日期</div>
                <div class="retention-cell retention-header">第1天</div>
                <div class="retention-cell retention-header">第3天</div>
                <div class="retention-cell retention-header">第7天</div>
                <div class="retention-cell retention-header">第14天</div>
                <div class="retention-cell retention-header">第30天</div>
                <div class="retention-cell retention-header">第60天</div>
                <div class="retention-cell retention-header">第90天</div>
                
                <div class="retention-cell">2024-03-01</div>
                <div class="retention-cell retention-high">85%</div>
                <div class="retention-cell retention-high">72%</div>
                <div class="retention-cell retention-medium">58%</div>
                <div class="retention-cell retention-medium">45%</div>
                <div class="retention-cell retention-low">32%</div>
                <div class="retention-cell retention-low">28%</div>
                <div class="retention-cell retention-low">25%</div>
                
                <div class="retention-cell">2024-02-29</div>
                <div class="retention-cell retention-high">82%</div>
                <div class="retention-cell retention-high">69%</div>
                <div class="retention-cell retention-medium">55%</div>
                <div class="retention-cell retention-medium">42%</div>
                <div class="retention-cell retention-low">30%</div>
                <div class="retention-cell retention-low">26%</div>
                <div class="retention-cell retention-low">23%</div>
                
                <div class="retention-cell">2024-02-28</div>
                <div class="retention-cell retention-high">88%</div>
                <div class="retention-cell retention-high">75%</div>
                <div class="retention-cell retention-medium">62%</div>
                <div class="retention-cell retention-medium">48%</div>
                <div class="retention-cell retention-low">35%</div>
                <div class="retention-cell retention-low">31%</div>
                <div class="retention-cell retention-low">28%</div>
            </div>
        </div>
    </div>
    
    <script>
        // 初始化图表
        let growthChart;
        
        document.addEventListener('DOMContentLoaded', function() {
            initializeGrowthChart();
        });
        
        function initializeGrowthChart() {
            const ctx = document.getElementById('growthChart').getContext('2d');
            growthChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['3/1', '3/2', '3/3', '3/4', '3/5', '3/6', '3/7', '3/8', '3/9', '3/10'],
                    datasets: [{
                        label: '新增用户',
                        data: [120, 190, 300, 500, 200, 300, 450, 280, 350, 420],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '活跃用户',
                        data: [2800, 3200, 3500, 3800, 3600, 3900, 4200, 3950, 4100, 4300],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });
        }
        
        function selectTimeRange(range) {
            document.querySelectorAll('.time-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            showNotification(`已切换到时间范围: ${range}`, 'info');
            // 这里可以重新加载数据
        }
        
        function updateGrowthChart(period) {
            showNotification(`图表已切换到${period}视图`, 'info');
            // 这里可以更新图表数据
        }
        
        function exportReport() {
            showNotification('正在生成用户分析报告...', 'info');
            setTimeout(() => {
                showNotification('报告已导出!', 'success');
            }, 2000);
        }
        
        function refreshData() {
            showNotification('正在刷新数据...', 'info');
            setTimeout(() => {
                showNotification('数据已更新!', 'success');
            }, 1500);
        }
        
        function showNotification(message, type) {
            console.log(`${type}: ${message}`);
        }
    </script>
</body>
</html> 