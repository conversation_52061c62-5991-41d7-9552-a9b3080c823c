<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>系统设置 - TeleShop后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { margin: 0; padding: 0; font-family: 'Inter', sans-serif; background: #f8fafc; }
        .settings-page { padding: 24px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 32px; }
        .page-title { font-size: 28px; font-weight: 700; color: #1e293b; margin: 0; }
        .settings-container { display: grid; grid-template-columns: 280px 1fr; gap: 24px; }
        
        /* 侧边栏导航 */
        .settings-nav { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; padding: 20px 0; }
        .nav-section { margin-bottom: 24px; }
        .nav-section:last-child { margin-bottom: 0; }
        .nav-title { font-size: 12px; font-weight: 600; color: #64748b; text-transform: uppercase; letter-spacing: 0.5px; margin: 0 20px 12px 20px; }
        .nav-list { list-style: none; margin: 0; padding: 0; }
        .nav-item { margin-bottom: 2px; }
        .nav-link { display: flex; align-items: center; padding: 12px 20px; color: #64748b; text-decoration: none; font-size: 14px; font-weight: 500; transition: all 0.2s; border-right: 3px solid transparent; }
        .nav-link:hover { background: #f8fafc; color: #1e293b; }
        .nav-link.active { background: #eff6ff; color: #3b82f6; border-right-color: #3b82f6; }
        .nav-link i { width: 20px; margin-right: 12px; font-size: 16px; }
        
        /* 设置内容区 */
        .settings-content { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; min-height: 600px; }
        .content-section { display: none; }
        .content-section.active { display: block; }
        .section-header { padding: 24px 24px 0 24px; margin-bottom: 24px; }
        .section-title { font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 8px; }
        .section-desc { color: #64748b; font-size: 14px; }
        .section-body { padding: 0 24px 24px 24px; }
        
        /* 表单样式 */
        .form-group { margin-bottom: 24px; }
        .form-label { display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px; }
        .form-input { width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; transition: border-color 0.2s; }
        .form-input:focus { outline: none; border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
        .form-select { width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; background: white; }
        .form-textarea { width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical; min-height: 80px; }
        .form-switch { display: flex; align-items: center; gap: 12px; }
        .switch { position: relative; width: 44px; height: 24px; }
        .switch input { opacity: 0; width: 0; height: 0; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #cbd5e1; transition: .2s; border-radius: 24px; }
        .slider:before { position: absolute; content: ""; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .2s; border-radius: 50%; }
        input:checked + .slider { background-color: #3b82f6; }
        input:checked + .slider:before { transform: translateX(20px); }
        
        /* 按钮样式 */
        .btn { padding: 10px 20px; border: 1px solid transparent; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s; text-decoration: none; display: inline-flex; align-items: center; gap: 8px; }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-primary:hover { background: #2563eb; }
        .btn-secondary { background: #f8fafc; color: #64748b; border-color: #e2e8f0; }
        .btn-secondary:hover { background: #f1f5f9; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-danger:hover { background: #dc2626; }
        
        /* 卡片布局 */
        .settings-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .card-title { font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 8px; }
        .card-desc { color: #64748b; font-size: 14px; margin-bottom: 16px; }
        
        /* 状态指示器 */
        .status-indicator { display: inline-flex; align-items: center; gap: 6px; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-online { background: #dcfce7; color: #166534; }
        .status-offline { background: #fef2f2; color: #dc2626; }
        .status-warning { background: #fef3c7; color: #92400e; }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .settings-container { grid-template-columns: 1fr; }
            .settings-nav { display: none; }
        }
    </style>
</head>
<body>
    <div class="settings-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">系统设置</h1>
            <div style="display: flex; gap: 12px;">
                <button class="btn btn-secondary" onclick="resetSettings()">
                    <i class="fas fa-undo"></i>
                    重置设置
                </button>
                <button class="btn btn-primary" onclick="saveSettings()">
                    <i class="fas fa-save"></i>
                    保存设置
                </button>
            </div>
        </div>

        <div class="settings-container">
            <!-- 设置导航 -->
            <div class="settings-nav">
                <div class="nav-section">
                    <h3 class="nav-title">基础设置</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link active" onclick="showSection('general', this)">
                                <i class="fas fa-cog"></i>
                                通用设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showSection('store', this)">
                                <i class="fas fa-store"></i>
                                商店信息
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showSection('payment', this)">
                                <i class="fas fa-credit-card"></i>
                                支付设置
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <h3 class="nav-title">功能设置</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showSection('shipping', this)">
                                <i class="fas fa-shipping-fast"></i>
                                物流配送
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showSection('notification', this)">
                                <i class="fas fa-bell"></i>
                                通知设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showSection('sms', this)">
                                <i class="fas fa-sms"></i>
                                短信配置
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <h3 class="nav-title">安全设置</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showSection('security', this)">
                                <i class="fas fa-shield-alt"></i>
                                安全策略
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showSection('backup', this)">
                                <i class="fas fa-database"></i>
                                数据备份
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showSection('logs', this)">
                                <i class="fas fa-file-alt"></i>
                                系统日志
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 设置内容 -->
            <div class="settings-content">
                <!-- 通用设置 -->
                <div id="general" class="content-section active">
                    <div class="section-header">
                        <h2 class="section-title">通用设置</h2>
                        <p class="section-desc">配置系统的基本信息和默认行为</p>
                    </div>
                    <div class="section-body">
                        <div class="settings-card">
                            <h3 class="card-title">基础信息</h3>
                            <p class="card-desc">设置系统的基本信息</p>
                            
                            <div class="form-group">
                                <label class="form-label">系统名称</label>
                                <input type="text" class="form-input" value="TeleShop 电商平台" placeholder="请输入系统名称">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">系统版本</label>
                                <input type="text" class="form-input" value="v2.1.0" readonly>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">系统描述</label>
                                <textarea class="form-textarea" placeholder="请输入系统描述">基于Telegram的创新电商平台，为用户提供便捷的购物体验</textarea>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">时区设置</label>
                                <select class="form-select">
                                    <option value="Asia/Shanghai" selected>Asia/Shanghai (+08:00)</option>
                                    <option value="Asia/Tokyo">Asia/Tokyo (+09:00)</option>
                                    <option value="UTC">UTC (+00:00)</option>
                                    <option value="America/New_York">America/New_York (-05:00)</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="settings-card">
                            <h3 class="card-title">系统功能</h3>
                            <p class="card-desc">启用或禁用系统功能</p>
                            
                            <div class="form-group">
                                <div class="form-switch">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                    <div>
                                        <div style="font-weight: 500;">用户注册</div>
                                        <div style="font-size: 12px; color: #64748b;">允许新用户注册账户</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-switch">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                    <div>
                                        <div style="font-weight: 500;">维护模式</div>
                                        <div style="font-size: 12px; color: #64748b;">开启后系统将显示维护页面</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-switch">
                                    <label class="switch">
                                        <input type="checkbox">
                                        <span class="slider"></span>
                                    </label>
                                    <div>
                                        <div style="font-weight: 500;">调试模式</div>
                                        <div style="font-size: 12px; color: #64748b;">显示详细的错误信息（仅开发环境）</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商店信息 -->
                <div id="store" class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">商店信息</h2>
                        <p class="section-desc">设置您的商店基本信息</p>
                    </div>
                    <div class="section-body">
                        <div class="settings-card">
                            <h3 class="card-title">商店基本信息</h3>
                            <div class="form-group">
                                <label class="form-label">商店名称</label>
                                <input type="text" class="form-input" value="TeleShop 精品商城" placeholder="请输入商店名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">商店地址</label>
                                <input type="text" class="form-input" value="北京市朝阳区xxx路xxx号" placeholder="请输入商店地址">
                            </div>
                            <div class="form-group">
                                <label class="form-label">联系电话</label>
                                <input type="text" class="form-input" value="************" placeholder="请输入联系电话">
                            </div>
                            <div class="form-group">
                                <label class="form-label">客服邮箱</label>
                                <input type="email" class="form-input" value="<EMAIL>" placeholder="请输入客服邮箱">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 支付设置 -->
                <div id="payment" class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">支付设置</h2>
                        <p class="section-desc">配置支付方式和相关参数</p>
                    </div>
                    <div class="section-body">
                        <div class="settings-card">
                            <h3 class="card-title">支付方式</h3>
                            <div class="form-group">
                                <div class="form-switch">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                    <div>
                                        <div style="font-weight: 500;">微信支付</div>
                                        <div style="font-size: 12px; color: #64748b;">启用微信支付功能</div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-switch">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                    <div>
                                        <div style="font-weight: 500;">支付宝</div>
                                        <div style="font-size: 12px; color: #64748b;">启用支付宝支付功能</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 物流配送 -->
                <div id="shipping" class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">物流配送</h2>
                        <p class="section-desc">配置物流和配送相关设置</p>
                    </div>
                    <div class="section-body">
                        <div class="settings-card">
                            <h3 class="card-title">配送设置</h3>
                            <div class="form-group">
                                <label class="form-label">免运费金额</label>
                                <input type="number" class="form-input" value="99" placeholder="满多少元免运费">
                            </div>
                            <div class="form-group">
                                <label class="form-label">默认运费</label>
                                <input type="number" class="form-input" value="10" placeholder="默认运费">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 通知设置 -->
                <div id="notification" class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">通知设置</h2>
                        <p class="section-desc">配置系统通知和消息推送</p>
                    </div>
                    <div class="section-body">
                        <div class="settings-card">
                            <h3 class="card-title">系统通知</h3>
                            <div class="form-group">
                                <div class="form-switch">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                    <div>
                                        <div style="font-weight: 500;">邮件通知</div>
                                        <div style="font-size: 12px; color: #64748b;">重要事件邮件通知</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 短信配置 -->
                <div id="sms" class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">短信配置</h2>
                        <p class="section-desc">配置短信服务和模板</p>
                    </div>
                    <div class="section-body">
                        <div class="settings-card">
                            <h3 class="card-title">短信服务商</h3>
                            <div class="form-group">
                                <label class="form-label">服务商</label>
                                <select class="form-select">
                                    <option value="aliyun">阿里云</option>
                                    <option value="tencent">腾讯云</option>
                                    <option value="huawei">华为云</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全策略 -->
                <div id="security" class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">安全策略</h2>
                        <p class="section-desc">配置系统安全相关设置</p>
                    </div>
                    <div class="section-body">
                        <div class="settings-card">
                            <h3 class="card-title">登录安全</h3>
                            <div class="form-group">
                                <label class="form-label">密码最小长度</label>
                                <input type="number" class="form-input" value="8" min="6" max="32">
                            </div>
                            <div class="form-group">
                                <label class="form-label">登录失败次数限制</label>
                                <input type="number" class="form-input" value="5" min="3" max="10">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据备份 -->
                <div id="backup" class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">数据备份</h2>
                        <p class="section-desc">管理系统数据备份</p>
                    </div>
                    <div class="section-body">
                        <div class="settings-card">
                            <h3 class="card-title">自动备份</h3>
                            <div class="form-group">
                                <div class="form-switch">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                    <div>
                                        <div style="font-weight: 500;">启用自动备份</div>
                                        <div style="font-size: 12px; color: #64748b;">每日自动备份系统数据</div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <button class="btn btn-primary">
                                    <i class="fas fa-download"></i>
                                    立即备份
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统日志 -->
                <div id="logs" class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">系统日志</h2>
                        <p class="section-desc">查看和管理系统日志</p>
                    </div>
                    <div class="section-body">
                        <div class="settings-card">
                            <h3 class="card-title">日志设置</h3>
                            <div class="form-group">
                                <label class="form-label">日志级别</label>
                                <select class="form-select">
                                    <option value="debug">Debug</option>
                                    <option value="info" selected>Info</option>
                                    <option value="warning">Warning</option>
                                    <option value="error">Error</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button class="btn btn-secondary">
                                    <i class="fas fa-eye"></i>
                                    查看日志
                                </button>
                                <button class="btn btn-danger" style="margin-left: 8px;">
                                    <i class="fas fa-trash"></i>
                                    清空日志
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId, link) {
            // 隐藏所有内容区域
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // 显示选中的内容区域
            document.getElementById(sectionId).classList.add('active');
            
            // 更新导航样式
            document.querySelectorAll('.nav-link').forEach(navLink => {
                navLink.classList.remove('active');
            });
            link.classList.add('active');
        }

        function saveSettings() {
            // 模拟保存设置
            alert('设置已保存！');
        }

        function resetSettings() {
            if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
                alert('设置已重置！');
            }
        }
    </script>
</body>
</html> 