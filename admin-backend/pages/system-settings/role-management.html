<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色权限管理 - TeleShop后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .role-management-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
        }

        .role-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 24px;
        }

        .role-list {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 20px;
        }

        .role-item {
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .role-item:hover {
            background: #f8fafc;
        }

        .role-item.active {
            background: #eff6ff;
            border: 1px solid #3b82f6;
        }

        .role-info h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .role-info p {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
        }

        .role-details {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 20px;
        }

        .permission-section {
            margin-bottom: 24px;
        }

        .permission-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
        }

        .permission-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 0 8px;
        }

        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 12px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .permission-item:hover {
            background: #f9fafb;
        }

        .permission-item input[type="checkbox"] {
            margin-right: 8px;
        }

        .permission-item label {
            flex: 1;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
        }

        .btn-group {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        @media (max-width: 1024px) {
            .role-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="role-management-container">
        <div class="page-header">
            <h1 class="page-title">角色权限管理</h1>
            <div>
                <button class="btn-secondary" onclick="roleManager.addRole()">
                    <i class="fas fa-plus"></i>
                    添加角色
                </button>
            </div>
        </div>

        <div class="role-content">
            <!-- 角色列表 -->
            <div class="role-list">
                <h3>角色列表</h3>
                <div id="roleListContainer">
                    <!-- 角色列表将通过JavaScript生成 -->
                </div>
            </div>

            <!-- 权限详情 -->
            <div class="role-details">
                <div id="roleDetailsContainer">
                    <p style="text-align: center; color: #6b7280; margin-top: 100px;">
                        请选择一个角色来查看详情
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 角色权限管理器
        class RolePermissionManager {
            constructor() {
                this.roles = [];
                this.currentRole = null;
                this.permissions = {};
                
                this.init();
            }

            init() {
                this.loadRoles();
                this.loadPermissions();
                this.renderRoleList();
            }

            loadRoles() {
                // 模拟角色数据
                this.roles = [
                    {
                        id: 1,
                        name: '超级管理员',
                        description: '拥有所有权限的最高级别管理员',
                        userCount: 2,
                        permissions: ['*'] // 所有权限
                    },
                    {
                        id: 2,
                        name: '运营管理员',
                        description: '负责日常运营管理的管理员',
                        userCount: 5,
                        permissions: ['dashboard.view', 'orders.view', 'orders.update', 'users.view', 'products.view']
                    },
                    {
                        id: 3,
                        name: '商品管理员',
                        description: '专门负责商品管理的管理员',
                        userCount: 3,
                        permissions: ['dashboard.view', 'products.view', 'products.create', 'products.update', 'products.delete']
                    },
                    {
                        id: 4,
                        name: '客服专员',
                        description: '处理客户服务和订单问题',
                        userCount: 8,
                        permissions: ['dashboard.view', 'orders.view', 'orders.update', 'users.view', 'chat.manage']
                    },
                    {
                        id: 5,
                        name: '数据分析师',
                        description: '负责查看和分析各类数据报表',
                        userCount: 2,
                        permissions: ['dashboard.view', 'analytics.view', 'reports.view', 'reports.export']
                    }
                ];
            }

            loadPermissions() {
                // 权限定义
                this.permissions = {
                    '仪表盘': {
                        icon: 'fas fa-tachometer-alt',
                        permissions: [
                            { key: 'dashboard.view', name: '查看仪表盘', description: '查看系统概览和统计数据' }
                        ]
                    },
                    '用户管理': {
                        icon: 'fas fa-users',
                        permissions: [
                            { key: 'users.view', name: '查看用户', description: '查看用户列表和详情' },
                            { key: 'users.create', name: '创建用户', description: '添加新用户' },
                            { key: 'users.update', name: '编辑用户', description: '修改用户信息' },
                            { key: 'users.delete', name: '删除用户', description: '删除用户账户' },
                            { key: 'users.ban', name: '封禁用户', description: '禁用用户账户' }
                        ]
                    },
                    '商品管理': {
                        icon: 'fas fa-box',
                        permissions: [
                            { key: 'products.view', name: '查看商品', description: '查看商品列表和详情' },
                            { key: 'products.create', name: '创建商品', description: '添加新商品' },
                            { key: 'products.update', name: '编辑商品', description: '修改商品信息' },
                            { key: 'products.delete', name: '删除商品', description: '删除商品' },
                            { key: 'products.inventory', name: '库存管理', description: '管理商品库存' }
                        ]
                    },
                    '订单管理': {
                        icon: 'fas fa-shopping-cart',
                        permissions: [
                            { key: 'orders.view', name: '查看订单', description: '查看订单列表和详情' },
                            { key: 'orders.update', name: '处理订单', description: '更新订单状态' },
                            { key: 'orders.refund', name: '订单退款', description: '处理退款申请' },
                            { key: 'orders.cancel', name: '取消订单', description: '取消订单' }
                        ]
                    },
                    '财务管理': {
                        icon: 'fas fa-chart-line',
                        permissions: [
                            { key: 'finance.view', name: '查看财务', description: '查看财务数据' },
                            { key: 'finance.withdraw', name: '提现管理', description: '处理提现申请' },
                            { key: 'finance.settlement', name: '结算管理', description: '管理结算流程' }
                        ]
                    },
                    '数据分析': {
                        icon: 'fas fa-chart-bar',
                        permissions: [
                            { key: 'analytics.view', name: '查看分析', description: '查看数据分析' },
                            { key: 'reports.view', name: '查看报表', description: '查看各类报表' },
                            { key: 'reports.export', name: '导出报表', description: '导出报表数据' },
                            { key: 'reports.create', name: '创建报表', description: '创建自定义报表' }
                        ]
                    },
                    '内容管理': {
                        icon: 'fas fa-file-alt',
                        permissions: [
                            { key: 'content.view', name: '查看内容', description: '查看内容管理' },
                            { key: 'content.create', name: '创建内容', description: '创建新内容' },
                            { key: 'content.update', name: '编辑内容', description: '修改内容' },
                            { key: 'content.delete', name: '删除内容', description: '删除内容' },
                            { key: 'chat.manage', name: '聊天管理', description: '管理客服聊天' }
                        ]
                    },
                    '系统设置': {
                        icon: 'fas fa-cog',
                        permissions: [
                            { key: 'settings.view', name: '查看设置', description: '查看系统设置' },
                            { key: 'settings.update', name: '修改设置', description: '修改系统配置' },
                            { key: 'roles.manage', name: '角色管理', description: '管理用户角色' },
                            { key: 'logs.view', name: '查看日志', description: '查看系统日志' },
                            { key: 'backup.manage', name: '备份管理', description: '管理数据备份' }
                        ]
                    }
                };
            }

            renderRoleList() {
                const container = document.getElementById('roleListContainer');
                let html = '';

                this.roles.forEach(role => {
                    html += `
                        <div class="role-item" data-role-id="${role.id}" onclick="roleManager.selectRole(${role.id})">
                            <div class="role-info">
                                <h4>${role.name}</h4>
                                <p>${role.userCount} 个用户</p>
                            </div>
                            <div class="role-actions">
                                <button class="btn-icon" onclick="event.stopPropagation(); roleManager.editRole(${role.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon" onclick="event.stopPropagation(); roleManager.deleteRole(${role.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = html;
            }

            selectRole(roleId) {
                // 更新选中状态
                document.querySelectorAll('.role-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-role-id="${roleId}"]`).classList.add('active');

                // 设置当前角色
                this.currentRole = this.roles.find(role => role.id === roleId);
                
                // 渲染权限详情
                this.renderRoleDetails();
            }

            renderRoleDetails() {
                if (!this.currentRole) return;

                const container = document.getElementById('roleDetailsContainer');
                let html = `
                    <div class="role-header">
                        <h2>${this.currentRole.name}</h2>
                        <p>${this.currentRole.description}</p>
                    </div>
                `;

                // 渲染权限分组
                Object.entries(this.permissions).forEach(([groupName, group]) => {
                    html += `
                        <div class="permission-section">
                            <div class="permission-header">
                                <i class="${group.icon}"></i>
                                <h3 class="permission-title">${groupName}</h3>
                            </div>
                            <div class="permission-grid">
                    `;

                    group.permissions.forEach(permission => {
                        const isChecked = this.hasPermission(permission.key);
                        const isDisabled = this.currentRole.id === 1; // 超级管理员不可编辑
                        
                        html += `
                            <div class="permission-item">
                                <input type="checkbox" 
                                       id="perm_${permission.key}" 
                                       ${isChecked ? 'checked' : ''} 
                                       ${isDisabled ? 'disabled' : ''}
                                       onchange="roleManager.togglePermission('${permission.key}', this.checked)">
                                <label for="perm_${permission.key}">
                                    ${permission.name}<br>
                                    <small style="color: #6b7280;">${permission.description}</small>
                                </label>
                            </div>
                        `;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                });

                // 添加操作按钮
                if (this.currentRole.id !== 1) { // 超级管理员角色不可删除
                    html += `
                        <div class="btn-group">
                            <button class="btn-primary" onclick="roleManager.saveRole()">
                                <i class="fas fa-save"></i>
                                保存权限
                            </button>
                            <button class="btn-secondary" onclick="roleManager.resetPermissions()">
                                <i class="fas fa-undo"></i>
                                重置
                            </button>
                        </div>
                    `;
                }

                container.innerHTML = html;
            }

            hasPermission(permissionKey) {
                if (!this.currentRole) return false;
                
                // 超级管理员拥有所有权限
                if (this.currentRole.permissions.includes('*')) {
                    return true;
                }
                
                return this.currentRole.permissions.includes(permissionKey);
            }

            togglePermission(permissionKey, isChecked) {
                if (!this.currentRole || this.currentRole.id === 1) return;

                if (isChecked) {
                    if (!this.currentRole.permissions.includes(permissionKey)) {
                        this.currentRole.permissions.push(permissionKey);
                    }
                } else {
                    this.currentRole.permissions = this.currentRole.permissions.filter(p => p !== permissionKey);
                }
            }

            saveRole() {
                if (!this.currentRole) return;

                // 模拟保存
                console.log('保存角色权限:', this.currentRole);
                
                this.showNotification('角色权限已保存', 'success');
            }

            resetPermissions() {
                if (!this.currentRole) return;

                // 重新加载原始数据
                this.loadRoles();
                this.currentRole = this.roles.find(role => role.id === this.currentRole.id);
                this.renderRoleDetails();
                
                this.showNotification('权限已重置', 'info');
            }

            addRole() {
                const name = prompt('请输入角色名称:');
                if (!name) return;

                const description = prompt('请输入角色描述:');
                if (!description) return;

                const newRole = {
                    id: Date.now(), // 简单的ID生成
                    name: name,
                    description: description,
                    userCount: 0,
                    permissions: ['dashboard.view'] // 默认权限
                };

                this.roles.push(newRole);
                this.renderRoleList();
                this.selectRole(newRole.id);
                
                this.showNotification('角色已添加', 'success');
            }

            editRole(roleId) {
                const role = this.roles.find(r => r.id === roleId);
                if (!role) return;

                const newName = prompt('请输入新的角色名称:', role.name);
                if (!newName) return;

                const newDescription = prompt('请输入新的角色描述:', role.description);
                if (!newDescription) return;

                role.name = newName;
                role.description = newDescription;

                this.renderRoleList();
                if (this.currentRole && this.currentRole.id === roleId) {
                    this.renderRoleDetails();
                }
                
                this.showNotification('角色已更新', 'success');
            }

            deleteRole(roleId) {
                if (roleId === 1) {
                    this.showNotification('超级管理员角色不能删除', 'error');
                    return;
                }

                const role = this.roles.find(r => r.id === roleId);
                if (!role) return;

                if (role.userCount > 0) {
                    this.showNotification(`该角色还有 ${role.userCount} 个用户，请先转移用户后再删除`, 'warning');
                    return;
                }

                if (confirm(`确定要删除角色"${role.name}"吗？`)) {
                    this.roles = this.roles.filter(r => r.id !== roleId);
                    this.renderRoleList();
                    
                    if (this.currentRole && this.currentRole.id === roleId) {
                        this.currentRole = null;
                        document.getElementById('roleDetailsContainer').innerHTML = `
                            <p style="text-align: center; color: #6b7280; margin-top: 100px;">
                                请选择一个角色来查看详情
                            </p>
                        `;
                    }
                    
                    this.showNotification('角色已删除', 'success');
                }
            }

            showNotification(message, type = 'info') {
                // 简单的通知实现
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 8px;
                    color: white;
                    font-size: 14px;
                    font-weight: 500;
                    z-index: 10001;
                    transform: translateX(100%);
                    transition: transform 0.3s;
                `;
                
                const colors = {
                    success: '#10b981',
                    error: '#ef4444',
                    warning: '#f59e0b',
                    info: '#3b82f6'
                };
                
                notification.style.background = colors[type] || colors.info;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 10);
                
                setTimeout(() => {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }
        }

        // 初始化角色权限管理器
        const roleManager = new RolePermissionManager();
    </script>
</body>
</html> 