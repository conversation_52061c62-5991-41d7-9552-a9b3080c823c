<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置扩展 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .settings-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
            margin-top: 4px;
        }

        .settings-layout {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 24px;
        }

        .settings-nav {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .nav-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }

        .nav-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .nav-item {
            padding: 12px 16px;
            border-radius: 8px;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .nav-item:hover {
            background: #f1f5f9;
            color: #1e293b;
        }

        .nav-item.active {
            background: #eff6ff;
            color: #3b82f6;
        }

        .settings-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .section-description {
            color: #64748b;
            font-size: 16px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .form-textarea {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-checkbox input {
            width: 16px;
            height: 16px;
        }

        .settings-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 24px;
        }

        .settings-table th {
            background: #f8fafc;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .settings-table td {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .settings-table tr:hover {
            background: #f9fafb;
        }

        .config-card {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }

        .config-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 16px;
        }

        .config-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .config-description {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: white;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
        }

        .alert {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
        }

        .alert-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }

        .alert-warning {
            background: #fefce8;
            border: 1px solid #fde047;
            color: #a16207;
        }

        .alert-success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 48px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #3b82f6;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(24px);
        }

        @media (max-width: 1024px) {
            .settings-layout {
                grid-template-columns: 1fr;
            }

            .settings-nav {
                order: 2;
            }

            .settings-content {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .settings-page {
                padding: 16px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .header-content {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="settings-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title">系统设置扩展</h1>
                    <p class="page-subtitle">平台规则、佣金费率、多租户配置与系统参数管理</p>
                </div>
                
                <div class="header-actions">
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        导出配置
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                </div>
            </div>
        </div>

        <!-- 设置布局 -->
        <div class="settings-layout">
            <!-- 导航菜单 -->
            <div class="settings-nav">
                <div class="nav-title">设置分类</div>
                <div class="nav-list">
                    <div class="nav-item active" onclick="switchSection('platform')">
                        <i class="fas fa-cogs"></i>
                        平台规则
                    </div>
                    <div class="nav-item" onclick="switchSection('commission')">
                        <i class="fas fa-percentage"></i>
                        佣金费率
                    </div>
                    <div class="nav-item" onclick="switchSection('tenant')">
                        <i class="fas fa-building"></i>
                        多租户配置
                    </div>
                    <div class="nav-item" onclick="switchSection('system')">
                        <i class="fas fa-server"></i>
                        系统参数
                    </div>
                    <div class="nav-item" onclick="switchSection('notification')">
                        <i class="fas fa-bell"></i>
                        通知设置
                    </div>
                    <div class="nav-item" onclick="switchSection('security')">
                        <i class="fas fa-shield-alt"></i>
                        安全设置
                    </div>
                </div>
            </div>

            <!-- 设置内容 -->
            <div class="settings-content">
                <!-- 平台规则设置 -->
                <div class="content-section active" id="platform-section">
                    <div class="section-header">
                        <h2 class="section-title">平台规则设置</h2>
                        <p class="section-description">配置平台运营规则、商家准入标准和业务流程规范</p>
                    </div>

                    <div class="config-card">
                        <div class="config-title">商家准入规则</div>
                        <div class="config-description">设置商家入驻的基本要求和审核标准</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">最低保证金 (元)</label>
                                <input type="number" class="form-input" value="10000" placeholder="请输入最低保证金">
                            </div>
                            <div class="form-group">
                                <label class="form-label">营业执照要求</label>
                                <label class="form-checkbox">
                                    <input type="checkbox" checked>
                                    <span>必须提供营业执照</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">经营年限要求 (年)</label>
                                <input type="number" class="form-input" value="1" placeholder="最低经营年限">
                            </div>
                            <div class="form-group">
                                <label class="form-label">自动审核</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-title">商品管理规则</div>
                        <div class="config-description">设置商品发布、审核和管理的相关规则</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">商品审核模式</label>
                                <select class="form-select">
                                    <option value="auto">自动审核</option>
                                    <option value="manual">人工审核</option>
                                    <option value="mixed">混合审核</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">单个商家商品上限</label>
                                <input type="number" class="form-input" value="1000" placeholder="商品数量上限">
                            </div>
                            <div class="form-group">
                                <label class="form-label">商品图片数量</label>
                                <input type="number" class="form-input" value="9" placeholder="最多图片数量">
                            </div>
                            <div class="form-group">
                                <label class="form-label">禁售商品检测</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 佣金费率设置 -->
                <div class="content-section" id="commission-section">
                    <div class="section-header">
                        <h2 class="section-title">佣金费率设置</h2>
                        <p class="section-description">配置不同类目和商家等级的佣金费率</p>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        佣金费率的调整将影响所有相关商家的收益，请谨慎操作。
                    </div>

                    <div class="config-card">
                        <div class="config-title">分类佣金费率</div>
                        <div class="config-description">按商品类目设置不同的佣金费率</div>
                        
                        <table class="settings-table">
                            <thead>
                                <tr>
                                    <th>商品类目</th>
                                    <th>佣金费率</th>
                                    <th>最低佣金</th>
                                    <th>最高佣金</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>数码电器</td>
                                    <td>3.0%</td>
                                    <td>¥5</td>
                                    <td>¥500</td>
                                    <td><button class="btn btn-small btn-secondary">编辑</button></td>
                                </tr>
                                <tr>
                                    <td>服装鞋包</td>
                                    <td>8.0%</td>
                                    <td>¥2</td>
                                    <td>¥200</td>
                                    <td><button class="btn btn-small btn-secondary">编辑</button></td>
                                </tr>
                                <tr>
                                    <td>家居生活</td>
                                    <td>5.0%</td>
                                    <td>¥3</td>
                                    <td>¥300</td>
                                    <td><button class="btn btn-small btn-secondary">编辑</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="config-card">
                        <div class="config-title">商家等级佣金</div>
                        <div class="config-description">根据商家等级设置不同的佣金优惠</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">普通商家佣金</label>
                                <input type="number" class="form-input" value="0" placeholder="佣金调整百分比">
                            </div>
                            <div class="form-group">
                                <label class="form-label">银牌商家佣金</label>
                                <input type="number" class="form-input" value="-5" placeholder="佣金调整百分比">
                            </div>
                            <div class="form-group">
                                <label class="form-label">金牌商家佣金</label>
                                <input type="number" class="form-input" value="-10" placeholder="佣金调整百分比">
                            </div>
                            <div class="form-group">
                                <label class="form-label">钻石商家佣金</label>
                                <input type="number" class="form-input" value="-15" placeholder="佣金调整百分比">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 多租户配置 -->
                <div class="content-section" id="tenant-section">
                    <div class="section-header">
                        <h2 class="section-title">多租户配置</h2>
                        <p class="section-description">管理多租户环境下的数据隔离和权限配置</p>
                    </div>

                    <div class="config-card">
                        <div class="config-title">租户管理</div>
                        <div class="config-description">配置租户的基本信息和权限</div>
                        
                        <table class="settings-table">
                            <thead>
                                <tr>
                                    <th>租户名称</th>
                                    <th>域名</th>
                                    <th>状态</th>
                                    <th>商家数量</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>TeleShop主站</td>
                                    <td>www.teleshop.com</td>
                                    <td><span style="color: #10b981;">活跃</span></td>
                                    <td>156</td>
                                    <td><button class="btn btn-small btn-secondary">管理</button></td>
                                </tr>
                                <tr>
                                    <td>区域分站</td>
                                    <td>region.teleshop.com</td>
                                    <td><span style="color: #10b981;">活跃</span></td>
                                    <td>89</td>
                                    <td><button class="btn btn-small btn-secondary">管理</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="config-card">
                        <div class="config-title">数据隔离设置</div>
                        <div class="config-description">配置租户间的数据隔离级别</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">商家数据隔离</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">订单数据隔离</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">用户数据隔离</label>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">财务数据隔离</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统参数设置 -->
                <div class="content-section" id="system-section">
                    <div class="section-header">
                        <h2 class="section-title">系统参数设置</h2>
                        <p class="section-description">配置系统运行的核心参数和性能设置</p>
                    </div>

                    <div class="config-card">
                        <div class="config-title">性能参数</div>
                        <div class="config-description">配置系统性能相关参数</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">API请求频率限制 (次/分钟)</label>
                                <input type="number" class="form-input" value="1000" placeholder="请求频率限制">
                            </div>
                            <div class="form-group">
                                <label class="form-label">数据库连接池大小</label>
                                <input type="number" class="form-input" value="50" placeholder="连接池大小">
                            </div>
                            <div class="form-group">
                                <label class="form-label">缓存过期时间 (秒)</label>
                                <input type="number" class="form-input" value="3600" placeholder="缓存时间">
                            </div>
                            <div class="form-group">
                                <label class="form-label">文件上传大小限制 (MB)</label>
                                <input type="number" class="form-input" value="10" placeholder="文件大小限制">
                            </div>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-title">业务参数</div>
                        <div class="config-description">配置业务流程相关参数</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">订单自动确认时间 (天)</label>
                                <input type="number" class="form-input" value="7" placeholder="自动确认天数">
                            </div>
                            <div class="form-group">
                                <label class="form-label">退款处理时间 (天)</label>
                                <input type="number" class="form-input" value="15" placeholder="退款处理天数">
                            </div>
                            <div class="form-group">
                                <label class="form-label">库存预警阈值</label>
                                <input type="number" class="form-input" value="10" placeholder="库存预警数量">
                            </div>
                            <div class="form-group">
                                <label class="form-label">价格变动通知阈值 (%)</label>
                                <input type="number" class="form-input" value="20" placeholder="价格变动百分比">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 通知设置 -->
                <div class="content-section" id="notification-section">
                    <div class="section-header">
                        <h2 class="section-title">通知设置</h2>
                        <p class="section-description">配置系统通知和消息推送设置</p>
                    </div>

                    <div class="config-card">
                        <div class="config-title">邮件通知</div>
                        <div class="config-description">配置邮件通知的发送规则</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">SMTP服务器</label>
                                <input type="text" class="form-input" value="smtp.example.com" placeholder="SMTP服务器地址">
                            </div>
                            <div class="form-group">
                                <label class="form-label">端口</label>
                                <input type="number" class="form-input" value="587" placeholder="SMTP端口">
                            </div>
                            <div class="form-group">
                                <label class="form-label">发件人邮箱</label>
                                <input type="email" class="form-input" value="<EMAIL>" placeholder="发件人邮箱">
                            </div>
                            <div class="form-group">
                                <label class="form-label">启用SSL</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-title">短信通知</div>
                        <div class="config-description">配置短信通知服务</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">短信服务商</label>
                                <select class="form-select">
                                    <option value="aliyun">阿里云</option>
                                    <option value="tencent">腾讯云</option>
                                    <option value="huawei">华为云</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">AccessKey ID</label>
                                <input type="text" class="form-input" placeholder="AccessKey ID">
                            </div>
                            <div class="form-group">
                                <label class="form-label">AccessKey Secret</label>
                                <input type="password" class="form-input" placeholder="AccessKey Secret">
                            </div>
                            <div class="form-group">
                                <label class="form-label">签名名称</label>
                                <input type="text" class="form-input" value="TeleShop" placeholder="短信签名">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全设置 -->
                <div class="content-section" id="security-section">
                    <div class="section-header">
                        <h2 class="section-title">安全设置</h2>
                        <p class="section-description">配置系统安全策略和访问控制</p>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        安全设置的修改可能影响系统访问，请谨慎操作并做好备份。
                    </div>

                    <div class="config-card">
                        <div class="config-title">访问控制</div>
                        <div class="config-description">配置系统访问控制策略</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">登录失败锁定次数</label>
                                <input type="number" class="form-input" value="5" placeholder="失败次数">
                            </div>
                            <div class="form-group">
                                <label class="form-label">锁定时间 (分钟)</label>
                                <input type="number" class="form-input" value="30" placeholder="锁定时间">
                            </div>
                            <div class="form-group">
                                <label class="form-label">密码最小长度</label>
                                <input type="number" class="form-input" value="8" placeholder="密码长度">
                            </div>
                            <div class="form-group">
                                <label class="form-label">强制双因子认证</label>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-title">数据加密</div>
                        <div class="config-description">配置数据加密和传输安全</div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">数据库加密</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">强制HTTPS</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">API签名验证</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">日志加密存储</label>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                    <button class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        恢复默认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换设置分类
        function switchSection(sectionName) {
            // 隐藏所有内容
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // 移除所有活动状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中的内容
            document.getElementById(sectionName + '-section').classList.add('active');
            
            // 添加活动状态
            event.target.classList.add('active');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('系统设置扩展页面已加载');
        });
    </script>
</body>
</html> 