<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全管控 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .security-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .security-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .security-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .security-card.critical {
            border-left: 4px solid #ef4444;
        }
        
        .security-card.warning {
            border-left: 4px solid #f59e0b;
        }
        
        .security-card.safe {
            border-left: 4px solid #10b981;
        }
        
        .threat-list {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 24px;
        }
        
        .threat-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .threat-info h4 {
            color: #1e293b;
            margin: 0 0 4px 0;
            font-size: 16px;
        }
        
        .threat-info p {
            color: #64748b;
            margin: 0;
            font-size: 14px;
        }
        
        .threat-level {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .threat-level.high {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .threat-level.medium {
            background: #fef3c7;
            color: #92400e;
        }
        
        .threat-level.low {
            background: #dcfce7;
            color: #166534;
        }
        
        .security-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .action-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .action-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            background: #f1f5f9;
            color: #64748b;
        }
        
        .action-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .action-controls {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .control-btn:hover {
            background: #f1f5f9;
        }
        
        .control-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .control-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        @media (max-width: 768px) {
            .security-page {
                padding: 16px;
            }
            
            .security-overview {
                grid-template-columns: 1fr;
            }
            
            .security-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="security-page">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-shield-alt"></i>
                安全管控中心
            </h1>
            <div class="page-actions">
                <button class="action-btn" onclick="refreshSecurityData()">
                    <i class="fas fa-sync-alt"></i>
                    刷新数据
                </button>
                <button class="action-btn primary" onclick="exportSecurityReport()">
                    <i class="fas fa-download"></i>
                    导出报告
                </button>
            </div>
        </div>

        <!-- 安全状态概览 -->
        <div class="security-overview">
            <div class="security-card safe">
                <div class="stat-header">
                    <div class="stat-title">整体安全评分</div>
                    <div class="stat-icon" style="background: #dcfce7; color: #166534;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-value" style="color: #10b981;">92/100</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    较上周提升 3 分
                </div>
            </div>

            <div class="security-card warning">
                <div class="stat-header">
                    <div class="stat-title">安全威胁</div>
                    <div class="stat-icon" style="background: #fef3c7; color: #92400e;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stat-value" style="color: #f59e0b;">5个</div>
                <div class="stat-change">
                    <i class="fas fa-clock"></i>
                    需要处理
                </div>
            </div>

            <div class="security-card safe">
                <div class="stat-header">
                    <div class="stat-title">访问控制</div>
                    <div class="stat-icon" style="background: #dcfce7; color: #166534;">
                        <i class="fas fa-user-shield"></i>
                    </div>
                </div>
                <div class="stat-value">正常</div>
                <div class="stat-change">
                    <i class="fas fa-users"></i>
                    156个活跃会话
                </div>
            </div>

            <div class="security-card safe">
                <div class="stat-header">
                    <div class="stat-title">数据加密</div>
                    <div class="stat-icon" style="background: #dcfce7; color: #166534;">
                        <i class="fas fa-lock"></i>
                    </div>
                </div>
                <div class="stat-value">已启用</div>
                <div class="stat-change">
                    <i class="fas fa-certificate"></i>
                    SSL证书有效
                </div>
            </div>
        </div>

        <!-- 安全威胁列表 -->
        <div class="threat-list">
            <div class="content-header">
                <h2 class="content-title">近期安全威胁</h2>
            </div>
            <div class="threat-item">
                <div class="threat-info">
                    <h4>异常登录尝试</h4>
                    <p>IP: ************ 尝试暴力破解管理员账户</p>
                </div>
                <div class="threat-level high">高风险</div>
            </div>
            <div class="threat-item">
                <div class="threat-info">
                    <h4>可疑文件上传</h4>
                    <p>检测到恶意文件上传尝试，已自动拦截</p>
                </div>
                <div class="threat-level medium">中风险</div>
            </div>
            <div class="threat-item">
                <div class="threat-info">
                    <h4>API访问异常</h4>
                    <p>来自未知来源的高频率API调用</p>
                </div>
                <div class="threat-level medium">中风险</div>
            </div>
            <div class="threat-item">
                <div class="threat-info">
                    <h4>权限提升尝试</h4>
                    <p>普通用户尝试访问管理员功能</p>
                </div>
                <div class="threat-level low">低风险</div>
            </div>
        </div>

        <!-- 安全操作面板 -->
        <div class="security-actions">
            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="action-title">IP封禁管理</div>
                </div>
                <p style="color: #64748b; font-size: 14px; margin-bottom: 16px;">
                    管理被封禁的IP地址和访问限制
                </p>
                <div class="action-controls">
                    <button class="control-btn">查看封禁列表</button>
                    <button class="control-btn danger">添加封禁</button>
                    <button class="control-btn">解除封禁</button>
                </div>
            </div>

            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="action-title">API密钥管理</div>
                </div>
                <p style="color: #64748b; font-size: 14px; margin-bottom: 16px;">
                    管理系统API访问密钥和权限
                </p>
                <div class="action-controls">
                    <button class="control-btn">查看密钥</button>
                    <button class="control-btn primary">生成新密钥</button>
                    <button class="control-btn danger">撤销密钥</button>
                </div>
            </div>

            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="action-title">操作审计</div>
                </div>
                <p style="color: #64748b; font-size: 14px; margin-bottom: 16px;">
                    查看系统操作日志和审核记录
                </p>
                <div class="action-controls">
                    <button class="control-btn">查看日志</button>
                    <button class="control-btn">导出记录</button>
                    <button class="control-btn">设置告警</button>
                </div>
            </div>

            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="action-title">安全策略</div>
                </div>
                <p style="color: #64748b; font-size: 14px; margin-bottom: 16px;">
                    配置系统安全策略和规则
                </p>
                <div class="action-controls">
                    <button class="control-btn">密码策略</button>
                    <button class="control-btn">访问控制</button>
                    <button class="control-btn primary">更新配置</button>
                </div>
            </div>

            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">
                        <i class="fas fa-virus"></i>
                    </div>
                    <div class="action-title">恶意软件扫描</div>
                </div>
                <p style="color: #64748b; font-size: 14px; margin-bottom: 16px;">
                    定期扫描系统文件和上传内容
                </p>
                <div class="action-controls">
                    <button class="control-btn primary">开始扫描</button>
                    <button class="control-btn">查看结果</button>
                    <button class="control-btn">隔离威胁</button>
                </div>
            </div>

            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="action-title">防火墙管理</div>
                </div>
                <p style="color: #64748b; font-size: 14px; margin-bottom: 16px;">
                    配置网络防火墙规则和端口
                </p>
                <div class="action-controls">
                    <button class="control-btn">查看规则</button>
                    <button class="control-btn primary">添加规则</button>
                    <button class="control-btn">测试连接</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function refreshSecurityData() {
            console.log('刷新安全数据...');
            // 实际项目中这里会调用API获取最新安全数据
        }

        function exportSecurityReport() {
            console.log('导出安全报告...');
            // 实际项目中这里会生成安全报告文件
        }
    </script>
</body>
</html> 