<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .category-management-page { padding: 24px; background: #f8fafc; min-height: 100vh; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 28px; font-weight: 700; color: #1e293b; margin: 0; }
        .action-btn { padding: 10px 20px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 8px; text-decoration: none; }
        .action-btn:hover { background: #f1f5f9; border-color: #cbd5e1; }
        .action-btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .main-content { display: grid; grid-template-columns: 1fr 400px; gap: 24px; }
        .category-tree-section { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .section-header { padding: 20px 24px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center; }
        .section-title { font-size: 18px; font-weight: 600; color: #1e293b; margin: 0; }
        .tree-container { padding: 24px; }
        .category-tree { list-style: none; padding: 0; margin: 0; }
        .tree-item { margin-bottom: 8px; }
        .tree-node { display: flex; align-items: center; padding: 12px; border-radius: 8px; cursor: pointer; transition: all 0.2s; position: relative; user-select: none; }
        .tree-node:hover { background: #f8fafc; }
        .tree-node.selected { background: #eff6ff; border: 1px solid #3b82f6; }
        .tree-node.dragging { opacity: 0.5; }
        .tree-toggle { width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; cursor: pointer; margin-right: 8px; color: #64748b; }
        .tree-toggle.expanded { transform: rotate(90deg); }
        .tree-icon { width: 32px; height: 32px; background: #f1f5f9; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 12px; font-size: 14px; color: #64748b; }
        .tree-content { flex: 1; }
        .tree-name { font-size: 14px; font-weight: 500; color: #1e293b; margin-bottom: 2px; }
        .tree-info { font-size: 12px; color: #64748b; }
        .tree-actions { display: flex; gap: 8px; opacity: 0; transition: opacity 0.2s; }
        .tree-node:hover .tree-actions { opacity: 1; }
        .tree-action { width: 28px; height: 28px; border: 1px solid #e2e8f0; border-radius: 4px; background: white; color: #64748b; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 12px; }
        .tree-action:hover { background: #f1f5f9; }
        .tree-action.danger:hover { background: #fef2f2; color: #dc2626; border-color: #fecaca; }
        .tree-children { list-style: none; padding: 0; margin: 0; padding-left: 40px; }
        .tree-children.collapsed { display: none; }
        .category-form { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; position: sticky; top: 24px; }
        .form-content { padding: 24px; }
        .form-group { margin-bottom: 20px; }
        .form-label { display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px; }
        .form-label.required::after { content: '*'; color: #ef4444; margin-left: 4px; }
        .form-input { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; }
        .form-input:focus { outline: none; border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
        .form-select { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; background: white; }
        .form-textarea { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; resize: vertical; min-height: 80px; }
        .icon-selector { display: grid; grid-template-columns: repeat(6, 1fr); gap: 8px; margin-top: 8px; }
        .icon-option { width: 40px; height: 40px; border: 1px solid #e2e8f0; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s; }
        .icon-option:hover { background: #f1f5f9; }
        .icon-option.selected { background: #eff6ff; border-color: #3b82f6; color: #3b82f6; }
        .form-actions { display: flex; gap: 12px; padding-top: 20px; border-top: 1px solid #e2e8f0; }
        .btn { padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.2s; flex: 1; }
        .btn.primary { background: #3b82f6; color: white; border: 1px solid #3b82f6; }
        .btn.secondary { background: white; color: #64748b; border: 1px solid #e2e8f0; }
        .btn:hover { opacity: 0.9; }
        .drag-indicator { position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: #3b82f6; opacity: 0; transition: opacity 0.2s; }
        .tree-node.drag-over .drag-indicator { opacity: 1; }
        .empty-state { text-align: center; padding: 60px 24px; color: #64748b; }
        .empty-state i { font-size: 48px; margin-bottom: 16px; opacity: 0.5; }
        .empty-state h3 { font-size: 18px; font-weight: 600; margin-bottom: 8px; color: #1e293b; }
        .empty-state p { margin: 0; }
    </style>
</head>
<body>
    <div class="category-management-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">分类管理</h1>
            <div style="display: flex; gap: 12px;">
                <a href="index.html" class="action-btn">
                    <i class="fas fa-arrow-left"></i>
                    返回商品列表
                </a>
                <button class="action-btn primary" onclick="addCategory()">
                    <i class="fas fa-plus"></i>
                    新增分类
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- 分类树 -->
            <div class="category-tree-section">
                <div class="section-header">
                    <h3 class="section-title">分类结构</h3>
                    <div style="display: flex; gap: 8px;">
                        <button class="action-btn" onclick="expandAll()">
                            <i class="fas fa-expand-alt"></i>
                            全部展开
                        </button>
                        <button class="action-btn" onclick="collapseAll()">
                            <i class="fas fa-compress-alt"></i>
                            全部收起
                        </button>
                    </div>
                </div>
                <div class="tree-container">
                    <ul class="category-tree" id="categoryTree">
                        <!-- 一级分类：电子产品 -->
                        <li class="tree-item" data-id="1" draggable="true">
                            <div class="tree-node" onclick="selectCategory(1)">
                                <div class="drag-indicator"></div>
                                <span class="tree-toggle expanded" onclick="toggleNode(event, this)">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                                <div class="tree-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="tree-content">
                                    <div class="tree-name">电子产品</div>
                                    <div class="tree-info">3个子分类 | 1,234个商品</div>
                                </div>
                                <div class="tree-actions">
                                    <button class="tree-action" onclick="editCategory(event, 1)" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="tree-action" onclick="addSubCategory(event, 1)" title="添加子分类">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <button class="tree-action danger" onclick="deleteCategory(event, 1)" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <ul class="tree-children">
                                <!-- 二级分类：手机通讯 -->
                                <li class="tree-item" data-id="2" draggable="true">
                                    <div class="tree-node" onclick="selectCategory(2)">
                                        <div class="drag-indicator"></div>
                                        <span class="tree-toggle expanded" onclick="toggleNode(event, this)">
                                            <i class="fas fa-chevron-right"></i>
                                        </span>
                                        <div class="tree-icon">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <div class="tree-content">
                                            <div class="tree-name">手机通讯</div>
                                            <div class="tree-info">3个子分类 | 856个商品</div>
                                        </div>
                                        <div class="tree-actions">
                                            <button class="tree-action" onclick="editCategory(event, 2)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="tree-action" onclick="addSubCategory(event, 2)">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button class="tree-action danger" onclick="deleteCategory(event, 2)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <ul class="tree-children">
                                        <!-- 三级分类 -->
                                        <li class="tree-item" data-id="3" draggable="true">
                                            <div class="tree-node selected" onclick="selectCategory(3)">
                                                <div class="drag-indicator"></div>
                                                <span class="tree-toggle" style="width: 20px;"></span>
                                                <div class="tree-icon">
                                                    <i class="fas fa-mobile"></i>
                                                </div>
                                                <div class="tree-content">
                                                    <div class="tree-name">智能手机</div>
                                                    <div class="tree-info">无子分类 | 456个商品</div>
                                                </div>
                                                <div class="tree-actions">
                                                    <button class="tree-action" onclick="editCategory(event, 3)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="tree-action danger" onclick="deleteCategory(event, 3)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </li>
                                        <li class="tree-item" data-id="4" draggable="true">
                                            <div class="tree-node" onclick="selectCategory(4)">
                                                <div class="drag-indicator"></div>
                                                <span class="tree-toggle" style="width: 20px;"></span>
                                                <div class="tree-icon">
                                                    <i class="fas fa-headphones"></i>
                                                </div>
                                                <div class="tree-content">
                                                    <div class="tree-name">手机配件</div>
                                                    <div class="tree-info">无子分类 | 234个商品</div>
                                                </div>
                                                <div class="tree-actions">
                                                    <button class="tree-action" onclick="editCategory(event, 4)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="tree-action danger" onclick="deleteCategory(event, 4)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </li>
                                <!-- 二级分类：电脑办公 -->
                                <li class="tree-item" data-id="5" draggable="true">
                                    <div class="tree-node" onclick="selectCategory(5)">
                                        <div class="drag-indicator"></div>
                                        <span class="tree-toggle" onclick="toggleNode(event, this)">
                                            <i class="fas fa-chevron-right"></i>
                                        </span>
                                        <div class="tree-icon">
                                            <i class="fas fa-laptop"></i>
                                        </div>
                                        <div class="tree-content">
                                            <div class="tree-name">电脑办公</div>
                                            <div class="tree-info">2个子分类 | 278个商品</div>
                                        </div>
                                        <div class="tree-actions">
                                            <button class="tree-action" onclick="editCategory(event, 5)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="tree-action" onclick="addSubCategory(event, 5)">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button class="tree-action danger" onclick="deleteCategory(event, 5)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <ul class="tree-children collapsed">
                                        <li class="tree-item" data-id="6" draggable="true">
                                            <div class="tree-node" onclick="selectCategory(6)">
                                                <div class="drag-indicator"></div>
                                                <span class="tree-toggle" style="width: 20px;"></span>
                                                <div class="tree-icon">
                                                    <i class="fas fa-desktop"></i>
                                                </div>
                                                <div class="tree-content">
                                                    <div class="tree-name">台式电脑</div>
                                                    <div class="tree-info">无子分类 | 145个商品</div>
                                                </div>
                                                <div class="tree-actions">
                                                    <button class="tree-action" onclick="editCategory(event, 6)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="tree-action danger" onclick="deleteCategory(event, 6)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </li>

                        <!-- 一级分类：服装配饰 -->
                        <li class="tree-item" data-id="7" draggable="true">
                            <div class="tree-node" onclick="selectCategory(7)">
                                <div class="drag-indicator"></div>
                                <span class="tree-toggle" onclick="toggleNode(event, this)">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                                <div class="tree-icon">
                                    <i class="fas fa-tshirt"></i>
                                </div>
                                <div class="tree-content">
                                    <div class="tree-name">服装配饰</div>
                                    <div class="tree-info">2个子分类 | 892个商品</div>
                                </div>
                                <div class="tree-actions">
                                    <button class="tree-action" onclick="editCategory(event, 7)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="tree-action" onclick="addSubCategory(event, 7)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <button class="tree-action danger" onclick="deleteCategory(event, 7)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <ul class="tree-children collapsed">
                                <li class="tree-item" data-id="8" draggable="true">
                                    <div class="tree-node" onclick="selectCategory(8)">
                                        <div class="drag-indicator"></div>
                                        <span class="tree-toggle" style="width: 20px;"></span>
                                        <div class="tree-icon">
                                            <i class="fas fa-male"></i>
                                        </div>
                                        <div class="tree-content">
                                            <div class="tree-name">男装</div>
                                            <div class="tree-info">无子分类 | 445个商品</div>
                                        </div>
                                        <div class="tree-actions">
                                            <button class="tree-action" onclick="editCategory(event, 8)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="tree-action danger" onclick="deleteCategory(event, 8)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 分类表单 -->
            <div class="category-form" id="categoryForm">
                <div class="section-header">
                    <h3 class="section-title" id="formTitle">编辑分类</h3>
                </div>
                <div class="form-content">
                    <div class="form-group">
                        <label class="form-label required">分类名称</label>
                        <input type="text" class="form-input" id="categoryName" placeholder="请输入分类名称" value="智能手机">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">上级分类</label>
                        <select class="form-select" id="parentCategory">
                            <option value="">顶级分类</option>
                            <option value="1">电子产品</option>
                            <option value="2" selected>手机通讯</option>
                            <option value="7">服装配饰</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">分类图标</label>
                        <div class="icon-selector">
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="icon-option selected" onclick="selectIcon(this)">
                                <i class="fas fa-mobile"></i>
                            </div>
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-laptop"></i>
                            </div>
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-headphones"></i>
                            </div>
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-male"></i>
                            </div>
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-female"></i>
                            </div>
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-home"></i>
                            </div>
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-gamepad"></i>
                            </div>
                            <div class="icon-option" onclick="selectIcon(this)">
                                <i class="fas fa-book"></i>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">排序权重</label>
                        <input type="number" class="form-input" id="categorySort" placeholder="数值越大越靠前" value="100">
                    </div>

                    <div class="form-group">
                        <label class="form-label">分类描述</label>
                        <textarea class="form-textarea" id="categoryDesc" placeholder="请输入分类描述">智能手机类目包含各品牌最新款智能手机产品</textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <select class="form-select" id="categoryStatus">
                            <option value="1" selected>启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <button class="btn secondary" onclick="cancelEdit()">取消</button>
                        <button class="btn primary" onclick="saveCategory()">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedCategoryId = 3;

        // 选择分类
        function selectCategory(id) {
            // 移除之前的选中状态
            document.querySelectorAll('.tree-node').forEach(node => {
                node.classList.remove('selected');
            });
            
            // 添加当前选中状态
            event.currentTarget.classList.add('selected');
            selectedCategoryId = id;
            
            // 加载分类信息到表单
            loadCategoryToForm(id);
        }

        // 切换节点展开/收起
        function toggleNode(event, toggle) {
            event.stopPropagation();
            const children = toggle.closest('.tree-item').querySelector('.tree-children');
            if (children) {
                children.classList.toggle('collapsed');
                toggle.classList.toggle('expanded');
            }
        }

        // 全部展开
        function expandAll() {
            document.querySelectorAll('.tree-children').forEach(children => {
                children.classList.remove('collapsed');
            });
            document.querySelectorAll('.tree-toggle').forEach(toggle => {
                toggle.classList.add('expanded');
            });
        }

        // 全部收起
        function collapseAll() {
            document.querySelectorAll('.tree-children').forEach(children => {
                children.classList.add('collapsed');
            });
            document.querySelectorAll('.tree-toggle').forEach(toggle => {
                toggle.classList.remove('expanded');
            });
        }

        // 编辑分类
        function editCategory(event, id) {
            event.stopPropagation();
            selectCategory(id);
            document.getElementById('formTitle').textContent = '编辑分类';
        }

        // 添加分类
        function addCategory() {
            document.getElementById('formTitle').textContent = '新增分类';
            clearForm();
        }

        // 添加子分类
        function addSubCategory(event, parentId) {
            event.stopPropagation();
            document.getElementById('formTitle').textContent = '新增子分类';
            clearForm();
            document.getElementById('parentCategory').value = parentId;
        }

        // 删除分类
        function deleteCategory(event, id) {
            event.stopPropagation();
            if (confirm('确定要删除该分类吗？删除后无法恢复！')) {
                alert(`删除分类 ${id}`);
                // 这里添加删除逻辑
            }
        }

        // 选择图标
        function selectIcon(icon) {
            document.querySelectorAll('.icon-option').forEach(option => {
                option.classList.remove('selected');
            });
            icon.classList.add('selected');
        }

        // 保存分类
        function saveCategory() {
            const name = document.getElementById('categoryName').value;
            if (!name.trim()) {
                alert('请输入分类名称');
                return;
            }
            
            alert('保存成功');
            // 这里添加保存逻辑
        }

        // 取消编辑
        function cancelEdit() {
            if (selectedCategoryId) {
                loadCategoryToForm(selectedCategoryId);
            }
        }

        // 清空表单
        function clearForm() {
            document.getElementById('categoryName').value = '';
            document.getElementById('parentCategory').value = '';
            document.getElementById('categorySort').value = '100';
            document.getElementById('categoryDesc').value = '';
            document.getElementById('categoryStatus').value = '1';
            
            // 清除图标选择
            document.querySelectorAll('.icon-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector('.icon-option').classList.add('selected');
        }

        // 加载分类到表单
        function loadCategoryToForm(id) {
            // 这里应该根据ID加载实际的分类数据
            // 目前使用示例数据
            switch(id) {
                case 3:
                    document.getElementById('categoryName').value = '智能手机';
                    document.getElementById('parentCategory').value = '2';
                    document.getElementById('categorySort').value = '100';
                    document.getElementById('categoryDesc').value = '智能手机类目包含各品牌最新款智能手机产品';
                    break;
                default:
                    break;
            }
        }

        // 拖拽功能
        document.addEventListener('DOMContentLoaded', function() {
            const tree = document.getElementById('categoryTree');
            let draggedElement = null;

            tree.addEventListener('dragstart', function(e) {
                draggedElement = e.target.closest('.tree-item');
                draggedElement.classList.add('dragging');
            });

            tree.addEventListener('dragend', function(e) {
                if (draggedElement) {
                    draggedElement.classList.remove('dragging');
                    draggedElement = null;
                }
                // 清除所有拖拽状态
                document.querySelectorAll('.tree-node').forEach(node => {
                    node.classList.remove('drag-over');
                });
            });

            tree.addEventListener('dragover', function(e) {
                e.preventDefault();
                const target = e.target.closest('.tree-node');
                if (target && draggedElement && target.closest('.tree-item') !== draggedElement) {
                    // 清除其他拖拽状态
                    document.querySelectorAll('.tree-node').forEach(node => {
                        node.classList.remove('drag-over');
                    });
                    target.classList.add('drag-over');
                }
            });

            tree.addEventListener('drop', function(e) {
                e.preventDefault();
                const target = e.target.closest('.tree-node');
                if (target && draggedElement && target.closest('.tree-item') !== draggedElement) {
                    // 这里处理拖拽排序逻辑
                    console.log('拖拽排序:', draggedElement.dataset.id, '->', target.closest('.tree-item').dataset.id);
                    alert('拖拽排序功能');
                }
            });
        });
    </script>
</body>
</html> 