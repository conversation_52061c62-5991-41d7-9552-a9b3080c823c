<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多商家商品管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .product-management-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 10px 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .action-btn.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .action-btn.success { background: #10b981; color: white; border-color: #10b981; }
        .action-btn.danger { background: #ef4444; color: white; border-color: #ef4444; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .stat-icon.products { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.pending { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.merchants { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-icon.categories { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        
        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .stat-label {
            color: #64748b;
            font-size: 14px;
        }
        
        .main-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .content-tabs {
            display: flex;
            border-bottom: 1px solid #e2e8f0;
            overflow-x: auto;
        }
        
        .tab-button {
            padding: 16px 24px;
            border: none;
            background: none;
            color: #64748b;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            white-space: nowrap;
            font-size: 14px;
            font-weight: 500;
        }
        
        .tab-button.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }
        
        .tab-content {
            padding: 24px;
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .filter-section {
            margin-bottom: 24px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
        }
        
        .filter-input, .filter-select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .product-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .product-table th,
        .product-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }
        
        .product-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            font-size: 12px;
            position: sticky;
            top: 0;
        }
        
        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
        }
        
        .product-info {
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 200px;
        }
        
        .product-details h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            color: #1e293b;
        }
        
        .product-details p {
            margin: 0;
            font-size: 12px;
            color: #64748b;
        }
        
        .merchant-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .merchant-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .merchant-name {
            font-size: 14px;
            color: #1e293b;
            font-weight: 500;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active { background: #dcfce7; color: #166534; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-rejected { background: #fecaca; color: #dc2626; }
        .status-offline { background: #f1f5f9; color: #64748b; }
        
        .price-info {
            text-align: right;
        }
        
        .current-price {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .original-price {
            font-size: 12px;
            color: #64748b;
            text-decoration: line-through;
        }
        
        .stock-info {
            text-align: center;
        }
        
        .stock-value {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .stock-status {
            font-size: 12px;
            color: #64748b;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-sm.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .btn-sm.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .btn-sm.danger { background: #ef4444; color: white; border-color: #ef4444; }
        .btn-sm.success { background: #10b981; color: white; border-color: #10b981; }
        
        .audit-panel {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }
        
        .audit-title {
            font-size: 16px;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 8px;
        }
        
        .audit-desc {
            color: #92400e;
            font-size: 14px;
            margin-bottom: 12px;
        }
        
        .bulk-actions {
            padding: 16px 24px;
            background: #eff6ff;
            border-bottom: 1px solid #e2e8f0;
            display: none;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .bulk-actions.show {
            display: flex;
        }
        
        .bulk-info {
            font-size: 14px;
            color: #1e40af;
        }
        
        .bulk-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .product-management-page { padding: 16px; }
            .page-header { flex-direction: column; align-items: stretch; }
            .stats-grid { grid-template-columns: 1fr 1fr; gap: 16px; }
            .filter-grid { grid-template-columns: 1fr; gap: 12px; }
        }
    </style>
</head>
<body>
    <div class="product-management-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">多商家商品管理</h1>
            <div class="header-actions">
                <button class="action-btn" onclick="exportProducts()">
                    <i class="fas fa-download"></i>
                    导出商品
                </button>
                <button class="action-btn warning" onclick="batchAudit()">
                    <i class="fas fa-check-circle"></i>
                    批量审核
                </button>
                <button class="action-btn success" onclick="categoryManagement()">
                    <i class="fas fa-tags"></i>
                    分类管理
                </button>
                <button class="action-btn primary" onclick="refreshData()">
                    <i class="fas fa-sync"></i>
                    刷新数据
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon products">
                        <i class="fas fa-box"></i>
                    </div>
                </div>
                <div class="stat-value">12,456</div>
                <div class="stat-label">商品总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value">89</div>
                <div class="stat-label">待审核商品</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon merchants">
                        <i class="fas fa-store"></i>
                    </div>
                </div>
                <div class="stat-value">156</div>
                <div class="stat-label">活跃商家</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon categories">
                        <i class="fas fa-sitemap"></i>
                    </div>
                </div>
                <div class="stat-value">23</div>
                <div class="stat-label">商品分类</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 标签页导航 -->
            <div class="content-tabs">
                <button class="tab-button active" onclick="switchTab('all-products', this)">全部商品</button>
                <button class="tab-button" onclick="switchTab('audit-products', this)">商品审核 <span style="background: #ef4444; color: white; padding: 2px 6px; border-radius: 10px; font-size: 10px; margin-left: 4px;">89</span></button>
                <button class="tab-button" onclick="switchTab('inventory', this)">库存管理</button>
                <button class="tab-button" onclick="switchTab('price-control', this)">价格管控</button>
                <button class="tab-button" onclick="switchTab('categories', this)">分类管理</button>
            </div>

            <!-- 全部商品标签页 -->
            <div id="all-products" class="tab-content active">
                <div class="filter-section">
                    <div class="filter-grid">
                        <div class="filter-group">
                            <label class="filter-label">商品名称</label>
                            <input type="text" class="filter-input" placeholder="输入商品名称">
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">商家</label>
                            <select class="filter-select">
                                <option>全部商家</option>
                                <option>星辰数码专营店</option>
                                <option>时尚服饰旗舰店</option>
                                <option>美妆护肤专卖</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">商品状态</label>
                            <select class="filter-select">
                                <option>全部状态</option>
                                <option>在售</option>
                                <option>待审核</option>
                                <option>已下架</option>
                                <option>已拒绝</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">分类</label>
                            <select class="filter-select">
                                <option>全部分类</option>
                                <option>数码电器</option>
                                <option>服装配饰</option>
                                <option>美妆护肤</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">&nbsp;</label>
                            <button class="action-btn primary">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 批量操作栏 -->
                <div class="bulk-actions" id="bulkActions">
                    <div class="bulk-info">
                        已选择 <span id="selectedCount">0</span> 个商品
                    </div>
                    <div class="bulk-buttons">
                        <button class="btn-sm success" onclick="bulkApprove()">批量上架</button>
                        <button class="btn-sm warning" onclick="bulkOffline()">批量下架</button>
                        <button class="btn-sm danger" onclick="bulkDelete()">批量删除</button>
                    </div>
                </div>

                <!-- 商品表格 -->
                <table class="product-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                            <th>商品信息</th>
                            <th>商家</th>
                            <th>分类</th>
                            <th>价格</th>
                            <th>库存</th>
                            <th>销量</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" class="product-checkbox" onchange="updateSelection()"></td>
                            <td>
                                <div class="product-info">
                                    <img src="../../assets/images/product-default.jpg" alt="商品" class="product-image">
                                    <div class="product-details">
                                        <h4>iPhone 15 Pro Max 256GB</h4>
                                        <p>天然钛色，支持5G网络</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="merchant-info">
                                    <img src="../../assets/images/avatar-default.png" alt="商家" class="merchant-avatar">
                                    <span class="merchant-name">星辰数码专营店</span>
                                </div>
                            </td>
                            <td>数码电器</td>
                            <td>
                                <div class="price-info">
                                    <div class="current-price">¥8,999</div>
                                    <div class="original-price">¥9,999</div>
                                </div>
                            </td>
                            <td>
                                <div class="stock-info">
                                    <div class="stock-value">156</div>
                                    <div class="stock-status">充足</div>
                                </div>
                            </td>
                            <td>89</td>
                            <td><span class="status-badge status-active">在售</span></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-sm">查看</button>
                                    <button class="btn-sm primary">编辑</button>
                                    <button class="btn-sm warning">下架</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="product-checkbox" onchange="updateSelection()"></td>
                            <td>
                                <div class="product-info">
                                    <img src="../../assets/images/product-default.jpg" alt="商品" class="product-image">
                                    <div class="product-details">
                                        <h4>时尚休闲运动套装</h4>
                                        <p>舒适透气，多色可选</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="merchant-info">
                                    <img src="../../assets/images/avatar-default.png" alt="商家" class="merchant-avatar">
                                    <span class="merchant-name">时尚服饰旗舰店</span>
                                </div>
                            </td>
                            <td>服装配饰</td>
                            <td>
                                <div class="price-info">
                                    <div class="current-price">¥299</div>
                                </div>
                            </td>
                            <td>
                                <div class="stock-info">
                                    <div class="stock-value">23</div>
                                    <div class="stock-status">偏低</div>
                                </div>
                            </td>
                            <td>156</td>
                            <td><span class="status-badge status-pending">待审核</span></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-sm">查看</button>
                                    <button class="btn-sm success">审核</button>
                                    <button class="btn-sm danger">拒绝</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 商品审核标签页 -->
            <div id="audit-products" class="tab-content">
                <div class="audit-panel">
                    <div class="audit-title">商品审核说明</div>
                    <div class="audit-desc">
                        商家提交的新商品需要平台审核后才能上架销售。请仔细检查商品信息、图片、价格等是否符合平台规范。
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn success" onclick="quickApprove()">
                            <i class="fas fa-check"></i>
                            快速通过
                        </button>
                        <button class="action-btn danger" onclick="quickReject()">
                            <i class="fas fa-times"></i>
                            批量拒绝
                        </button>
                    </div>
                </div>

                <!-- 待审核商品列表 -->
                <table class="product-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox"></th>
                            <th>商品信息</th>
                            <th>商家</th>
                            <th>提交时间</th>
                            <th>价格</th>
                            <th>审核状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox"></td>
                            <td>
                                <div class="product-info">
                                    <img src="../../assets/images/product-default.jpg" alt="商品" class="product-image">
                                    <div class="product-details">
                                        <h4>兰蔻小黑瓶精华液</h4>
                                        <p>30ml，抗衰老精华</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="merchant-info">
                                    <img src="../../assets/images/avatar-default.png" alt="商家" class="merchant-avatar">
                                    <span class="merchant-name">美妆护肤专卖</span>
                                </div>
                            </td>
                            <td>2023-12-15 14:30</td>
                            <td>¥799</td>
                            <td><span class="status-badge status-pending">待审核</span></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-sm">详情</button>
                                    <button class="btn-sm success">通过</button>
                                    <button class="btn-sm danger">拒绝</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 库存管理标签页 -->
            <div id="inventory" class="tab-content">
                <div style="background: #f0f9ff; padding: 16px; border-radius: 8px; margin-bottom: 24px;">
                    <h3 style="color: #0c4a6e; margin: 0 0 8px 0;">库存预警</h3>
                    <p style="color: #0c4a6e; margin: 0;">以下商品库存不足，请提醒商家及时补货</p>
                </div>

                <table class="product-table">
                    <thead>
                        <tr>
                            <th>商品信息</th>
                            <th>商家</th>
                            <th>当前库存</th>
                            <th>预警阈值</th>
                            <th>销售速度</th>
                            <th>预计缺货时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="product-info">
                                    <img src="../../assets/images/product-default.jpg" alt="商品" class="product-image">
                                    <div class="product-details">
                                        <h4>时尚休闲运动套装</h4>
                                        <p>黑色 L码</p>
                                    </div>
                                </div>
                            </td>
                            <td>时尚服饰旗舰店</td>
                            <td style="color: #ef4444; font-weight: 600;">23</td>
                            <td>50</td>
                            <td>5件/天</td>
                            <td style="color: #ef4444;">4天后</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-sm warning">提醒补货</button>
                                    <button class="btn-sm">查看详情</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 价格管控标签页 -->
            <div id="price-control" class="tab-content">
                <div style="background: #fef3c7; padding: 16px; border-radius: 8px; margin-bottom: 24px;">
                    <h3 style="color: #92400e; margin: 0 0 8px 0;">价格异常监控</h3>
                    <p style="color: #92400e; margin: 0;">系统检测到以下商品价格异常，请核实处理</p>
                </div>

                <table class="product-table">
                    <thead>
                        <tr>
                            <th>商品信息</th>
                            <th>商家</th>
                            <th>当前价格</th>
                            <th>市场均价</th>
                            <th>价格变动</th>
                            <th>异常类型</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="product-info">
                                    <img src="../../assets/images/product-default.jpg" alt="商品" class="product-image">
                                    <div class="product-details">
                                        <h4>iPhone 15 Pro Max</h4>
                                        <p>256GB 天然钛色</p>
                                    </div>
                                </div>
                            </td>
                            <td>星辰数码专营店</td>
                            <td style="color: #ef4444; font-weight: 600;">¥7,999</td>
                            <td>¥8,999</td>
                            <td style="color: #ef4444;">-¥1,000</td>
                            <td><span class="status-badge status-rejected">低价倾销</span></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-sm warning">价格预警</button>
                                    <button class="btn-sm">联系商家</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分类管理标签页 -->
            <div id="categories" class="tab-content">
                <div class="action-buttons" style="margin-bottom: 24px;">
                    <button class="action-btn primary" onclick="addCategory()">
                        <i class="fas fa-plus"></i>
                        添加分类
                    </button>
                    <button class="action-btn" onclick="sortCategories()">
                        <i class="fas fa-sort"></i>
                        排序管理
                    </button>
                </div>

                <table class="product-table">
                    <thead>
                        <tr>
                            <th>分类名称</th>
                            <th>父级分类</th>
                            <th>商品数量</th>
                            <th>佣金率</th>
                            <th>状态</th>
                            <th>排序</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>数码电器</td>
                            <td>-</td>
                            <td>1,234</td>
                            <td>3%</td>
                            <td><span class="status-badge status-active">启用</span></td>
                            <td>1</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-sm">编辑</button>
                                    <button class="btn-sm primary">子分类</button>
                                    <button class="btn-sm warning">禁用</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>手机通讯</td>
                            <td>数码电器</td>
                            <td>456</td>
                            <td>3%</td>
                            <td><span class="status-badge status-active">启用</span></td>
                            <td>1</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-sm">编辑</button>
                                    <button class="btn-sm warning">禁用</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabId, buttonElement) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有按钮的激活状态
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabId).classList.add('active');
            
            // 激活选中的按钮
            buttonElement.classList.add('active');
        }
        
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.product-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            
            updateSelection();
        }
        
        function updateSelection() {
            const checkboxes = document.querySelectorAll('.product-checkbox');
            const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            
            if (checkedBoxes.length > 0) {
                bulkActions.classList.add('show');
                selectedCount.textContent = checkedBoxes.length;
            } else {
                bulkActions.classList.remove('show');
            }
            
            // 更新全选状态
            const selectAll = document.getElementById('selectAll');
            selectAll.checked = checkedBoxes.length === checkboxes.length;
            selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
        }
        
        function exportProducts() {
            alert('导出商品数据');
        }
        
        function batchAudit() {
            alert('批量审核商品');
        }
        
        function categoryManagement() {
            alert('分类管理');
        }
        
        function refreshData() {
            alert('刷新数据');
        }
        
        function bulkApprove() {
            alert('批量上架商品');
        }
        
        function bulkOffline() {
            alert('批量下架商品');
        }
        
        function bulkDelete() {
            if (confirm('确定要删除选中的商品吗？')) {
                alert('商品已删除');
            }
        }
        
        function quickApprove() {
            alert('快速通过审核');
        }
        
        function quickReject() {
            alert('批量拒绝商品');
        }
        
        function addCategory() {
            alert('添加新分类');
        }
        
        function sortCategories() {
            alert('分类排序管理');
        }
    </script>
</body>
</html> 