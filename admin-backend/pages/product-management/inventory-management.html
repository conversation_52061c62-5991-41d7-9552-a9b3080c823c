<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .inventory-page { padding: 24px; background: #f8fafc; min-height: 100vh; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 28px; font-weight: 700; color: #1e293b; margin: 0; }
        .action-btn { padding: 10px 20px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 8px; text-decoration: none; }
        .action-btn:hover { background: #f1f5f9; border-color: #cbd5e1; }
        .action-btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .action-btn.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .action-btn.danger { background: #ef4444; color: white; border-color: #ef4444; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 24px; }
        .stat-card { background: white; padding: 20px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .stat-title { font-size: 14px; font-weight: 500; color: #64748b; margin-bottom: 8px; }
        .stat-value { font-size: 24px; font-weight: 700; color: #1e293b; margin-bottom: 4px; }
        .stat-desc { font-size: 12px; color: #64748b; }
        .main-content { display: grid; grid-template-columns: 1fr 400px; gap: 24px; }
        .content-section { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .section-header { padding: 20px 24px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center; }
        .section-title { font-size: 18px; font-weight: 600; color: #1e293b; margin: 0; }
        .filter-section { padding: 20px 24px; border-bottom: 1px solid #e2e8f0; background: #f8fafc; }
        .filter-row { display: flex; gap: 16px; align-items: center; flex-wrap: wrap; }
        .filter-group { display: flex; flex-direction: column; gap: 4px; }
        .filter-label { font-size: 12px; font-weight: 500; color: #64748b; }
        .filter-input { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; min-width: 150px; }
        .filter-select { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; background: white; min-width: 120px; }
        .inventory-table { width: 100%; border-collapse: collapse; }
        .inventory-table th, .inventory-table td { padding: 12px; border-bottom: 1px solid #f1f5f9; text-align: left; }
        .inventory-table th { background: #f8fafc; font-weight: 600; color: #374151; position: sticky; top: 0; }
        .product-info { display: flex; align-items: center; gap: 12px; }
        .product-image { width: 50px; height: 50px; border-radius: 6px; object-fit: cover; background: #f1f5f9; }
        .product-details h4 { margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #1e293b; }
        .product-details p { margin: 0; font-size: 12px; color: #64748b; }
        .stock-status { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-normal { background: #dcfce7; color: #166534; }
        .status-low { background: #fef3c7; color: #92400e; }
        .status-out { background: #fef2f2; color: #dc2626; }
        .warehouse-stocks { display: flex; gap: 8px; }
        .warehouse-stock { padding: 4px 8px; background: #f1f5f9; border-radius: 4px; font-size: 12px; }
        .stock-actions { display: flex; gap: 8px; }
        .btn-sm { padding: 6px 12px; border: 1px solid #e2e8f0; border-radius: 4px; background: white; color: #64748b; font-size: 12px; cursor: pointer; transition: all 0.2s; }
        .btn-sm:hover { background: #f1f5f9; }
        .btn-sm.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .btn-sm.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .sidebar-section { margin-bottom: 24px; }
        .log-item { padding: 12px 16px; border-bottom: 1px solid #f1f5f9; }
        .log-item:last-child { border-bottom: none; }
        .log-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
        .log-type { padding: 2px 6px; border-radius: 10px; font-size: 10px; font-weight: 500; }
        .type-in { background: #dcfce7; color: #166534; }
        .type-out { background: #fef2f2; color: #dc2626; }
        .type-adjust { background: #dbeafe; color: #1d4ed8; }
        .log-time { font-size: 12px; color: #64748b; }
        .log-content { font-size: 14px; color: #1e293b; margin-bottom: 4px; }
        .log-note { font-size: 12px; color: #64748b; }
        .quick-actions { padding: 20px; background: #f8fafc; border-bottom: 1px solid #e2e8f0; }
        .quick-actions h4 { margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: #1e293b; }
        .action-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 8px; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal.show { display: flex; align-items: center; justify-content: center; }
        .modal-content { background: white; border-radius: 12px; padding: 24px; width: 500px; max-width: 90vw; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .modal-title { font-size: 18px; font-weight: 600; color: #1e293b; margin: 0; }
        .modal-close { background: none; border: none; font-size: 24px; color: #64748b; cursor: pointer; }
        .form-group { margin-bottom: 16px; }
        .form-label { display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px; }
        .form-input { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; }
        .form-select { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; background: white; }
        .form-textarea { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; resize: vertical; min-height: 80px; }
        .modal-actions { display: flex; gap: 12px; justify-content: flex-end; margin-top: 20px; }
        .btn { padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.2s; }
        .btn.primary { background: #3b82f6; color: white; border: 1px solid #3b82f6; }
        .btn.secondary { background: white; color: #64748b; border: 1px solid #e2e8f0; }
    </style>
</head>
<body>
    <div class="inventory-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">库存管理</h1>
            <div style="display: flex; gap: 12px;">
                <a href="index.html" class="action-btn">
                    <i class="fas fa-arrow-left"></i>
                    返回商品列表
                </a>
                <button class="action-btn warning" onclick="showBatchAdjust()">
                    <i class="fas fa-edit"></i>
                    批量调整
                </button>
                <button class="action-btn primary" onclick="showStockIn()">
                    <i class="fas fa-plus"></i>
                    采购入库
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-title">总库存值</div>
                <div class="stat-value">¥2,847,392</div>
                <div class="stat-desc">涵盖所有仓库库存价值</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">预警商品</div>
                <div class="stat-value" style="color: #ef4444;">23</div>
                <div class="stat-desc">低于安全库存的商品</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">缺货商品</div>
                <div class="stat-value" style="color: #f59e0b;">8</div>
                <div class="stat-desc">库存为0的商品</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">今日变动</div>
                <div class="stat-value" style="color: #10b981;">+186</div>
                <div class="stat-desc">入库 +234 | 出库 -48</div>
            </div>
        </div>

        <div class="main-content">
            <!-- 库存列表 -->
            <div class="content-section">
                <div class="section-header">
                    <h3 class="section-title">库存列表</h3>
                    <div style="display: flex; gap: 8px;">
                        <button class="action-btn" onclick="exportInventory()">
                            <i class="fas fa-download"></i>
                            导出
                        </button>
                        <button class="action-btn" onclick="refreshInventory()">
                            <i class="fas fa-sync"></i>
                            刷新
                        </button>
                    </div>
                </div>

                <!-- 筛选区域 -->
                <div class="filter-section">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label class="filter-label">商品名称</label>
                            <input type="text" class="filter-input" placeholder="搜索商品">
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">仓库</label>
                            <select class="filter-select">
                                <option value="">全部仓库</option>
                                <option value="main">主仓库</option>
                                <option value="backup">备用仓库</option>
                                <option value="third">第三方仓库</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">库存状态</label>
                            <select class="filter-select">
                                <option value="">全部状态</option>
                                <option value="normal">正常</option>
                                <option value="low">库存预警</option>
                                <option value="out">缺货</option>
                            </select>
                        </div>
                        <button class="action-btn primary" style="align-self: flex-end;">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                    </div>
                </div>

                <!-- 库存表格 -->
                <div style="max-height: 600px; overflow-y: auto;">
                    <table class="inventory-table">
                        <thead>
                            <tr>
                                <th>商品信息</th>
                                <th>SKU</th>
                                <th>安全库存</th>
                                <th>总库存</th>
                                <th>仓库分布</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="product-info">
                                        <img src="../../assets/images/iphone-1.jpg" alt="商品" class="product-image">
                                        <div class="product-details">
                                            <h4>iPhone 14 Pro Max 深空黑 128GB</h4>
                                            <p>分类: 智能手机 | 品牌: Apple</p>
                                        </div>
                                    </div>
                                </td>
                                <td>IP14PM128BK</td>
                                <td>10</td>
                                <td><strong>45</strong></td>
                                <td>
                                    <div class="warehouse-stocks">
                                        <span class="warehouse-stock">主库: 25</span>
                                        <span class="warehouse-stock">备库: 15</span>
                                        <span class="warehouse-stock">三方: 5</span>
                                    </div>
                                </td>
                                <td><span class="stock-status status-normal">正常</span></td>
                                <td>
                                    <div class="stock-actions">
                                        <button class="btn-sm primary" onclick="adjustStock('IP14PM128BK')">调整</button>
                                        <button class="btn-sm" onclick="viewStockLog('IP14PM128BK')">日志</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="product-info">
                                        <img src="../../assets/images/nike-shoes.jpg" alt="商品" class="product-image">
                                        <div class="product-details">
                                            <h4>Nike Air Max 270 黑色 42码</h4>
                                            <p>分类: 运动鞋 | 品牌: Nike</p>
                                        </div>
                                    </div>
                                </td>
                                <td>NAM270BK42</td>
                                <td>5</td>
                                <td><strong style="color: #f59e0b;">3</strong></td>
                                <td>
                                    <div class="warehouse-stocks">
                                        <span class="warehouse-stock">主库: 2</span>
                                        <span class="warehouse-stock">备库: 1</span>
                                        <span class="warehouse-stock">三方: 0</span>
                                    </div>
                                </td>
                                <td><span class="stock-status status-low">库存预警</span></td>
                                <td>
                                    <div class="stock-actions">
                                        <button class="btn-sm warning" onclick="restockProduct('NAM270BK42')">补货</button>
                                        <button class="btn-sm primary" onclick="adjustStock('NAM270BK42')">调整</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="product-info">
                                        <img src="../../assets/images/muji-pillow.jpg" alt="商品" class="product-image">
                                        <div class="product-details">
                                            <h4>无印良品 羽绒枕头</h4>
                                            <p>分类: 家居用品 | 品牌: MUJI</p>
                                        </div>
                                    </div>
                                </td>
                                <td>MUJI-PILLOW</td>
                                <td>20</td>
                                <td><strong style="color: #ef4444;">0</strong></td>
                                <td>
                                    <div class="warehouse-stocks">
                                        <span class="warehouse-stock">主库: 0</span>
                                        <span class="warehouse-stock">备库: 0</span>
                                        <span class="warehouse-stock">三方: 0</span>
                                    </div>
                                </td>
                                <td><span class="stock-status status-out">缺货</span></td>
                                <td>
                                    <div class="stock-actions">
                                        <button class="btn-sm warning" onclick="urgentRestock('MUJI-PILLOW')">紧急补货</button>
                                        <button class="btn-sm" onclick="viewStockLog('MUJI-PILLOW')">日志</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 快速操作 -->
                <div class="sidebar-section">
                    <div class="content-section quick-actions">
                        <h4>快速操作</h4>
                        <div class="action-grid">
                            <button class="action-btn primary" onclick="showStockIn()">
                                <i class="fas fa-plus"></i>
                                采购入库
                            </button>
                            <button class="action-btn warning" onclick="showStockOut()">
                                <i class="fas fa-minus"></i>
                                库存出库
                            </button>
                            <button class="action-btn" onclick="showStockTransfer()">
                                <i class="fas fa-exchange-alt"></i>
                                仓库调拨
                            </button>
                            <button class="action-btn" onclick="showStockCheck()">
                                <i class="fas fa-clipboard-check"></i>
                                库存盘点
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 库存变更日志 -->
                <div class="sidebar-section">
                    <div class="content-section">
                        <div class="section-header">
                            <h3 class="section-title">变更日志</h3>
                            <button class="action-btn" onclick="viewAllLogs()">
                                <i class="fas fa-history"></i>
                                查看全部
                            </button>
                        </div>
                        
                        <div style="max-height: 400px; overflow-y: auto;">
                            <div class="log-item">
                                <div class="log-header">
                                    <span class="log-type type-in">入库</span>
                                    <span class="log-time">2小时前</span>
                                </div>
                                <div class="log-content">iPhone 14 Pro Max 入库 +50</div>
                                <div class="log-note">采购订单: PO202312150001</div>
                            </div>
                            
                            <div class="log-item">
                                <div class="log-header">
                                    <span class="log-type type-out">出库</span>
                                    <span class="log-time">4小时前</span>
                                </div>
                                <div class="log-content">Nike Air Max 270 出库 -2</div>
                                <div class="log-note">订单出库: OD202312150123</div>
                            </div>
                            
                            <div class="log-item">
                                <div class="log-header">
                                    <span class="log-type type-adjust">调整</span>
                                    <span class="log-time">6小时前</span>
                                </div>
                                <div class="log-content">无印良品枕头 库存调整 -5</div>
                                <div class="log-note">库存盘点差异调整</div>
                            </div>
                            
                            <div class="log-item">
                                <div class="log-header">
                                    <span class="log-type type-in">入库</span>
                                    <span class="log-time">昨天</span>
                                </div>
                                <div class="log-content">Apple Watch 入库 +30</div>
                                <div class="log-note">采购订单: PO202312140002</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 库存调整弹窗 -->
    <div id="stockAdjustModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">库存调整</h3>
                <button class="modal-close" onclick="closeModal('stockAdjustModal')">&times;</button>
            </div>
            
            <div class="form-group">
                <label class="form-label">商品SKU</label>
                <input type="text" class="form-input" id="adjustSku" readonly>
            </div>
            
            <div class="form-group">
                <label class="form-label">仓库</label>
                <select class="form-select" id="adjustWarehouse">
                    <option value="main">主仓库</option>
                    <option value="backup">备用仓库</option>
                    <option value="third">第三方仓库</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">当前库存</label>
                <input type="number" class="form-input" id="currentStock" readonly>
            </div>
            
            <div class="form-group">
                <label class="form-label">调整数量</label>
                <input type="number" class="form-input" id="adjustQuantity" placeholder="正数为增加，负数为减少">
            </div>
            
            <div class="form-group">
                <label class="form-label">调整后库存</label>
                <input type="number" class="form-input" id="newStock" readonly>
            </div>
            
            <div class="form-group">
                <label class="form-label">调整原因</label>
                <select class="form-select" id="adjustReason">
                    <option value="">请选择调整原因</option>
                    <option value="damage">商品损坏</option>
                    <option value="lost">商品丢失</option>
                    <option value="return">退货入库</option>
                    <option value="check">盘点调整</option>
                    <option value="other">其他原因</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">备注说明</label>
                <textarea class="form-textarea" id="adjustNote" placeholder="请输入详细说明"></textarea>
            </div>
            
            <div class="modal-actions">
                <button class="btn secondary" onclick="closeModal('stockAdjustModal')">取消</button>
                <button class="btn primary" onclick="confirmAdjust()">确认调整</button>
            </div>
        </div>
    </div>

    <!-- 采购入库弹窗 -->
    <div id="stockInModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">采购入库</h3>
                <button class="modal-close" onclick="closeModal('stockInModal')">&times;</button>
            </div>
            
            <div class="form-group">
                <label class="form-label">采购订单号</label>
                <input type="text" class="form-input" placeholder="请输入采购订单号">
            </div>
            
            <div class="form-group">
                <label class="form-label">供应商</label>
                <select class="form-select">
                    <option value="">请选择供应商</option>
                    <option value="supplier1">苹果官方供应商</option>
                    <option value="supplier2">Nike官方供应商</option>
                    <option value="supplier3">无印良品供应商</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">入库仓库</label>
                <select class="form-select">
                    <option value="main">主仓库</option>
                    <option value="backup">备用仓库</option>
                    <option value="third">第三方仓库</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">入库说明</label>
                <textarea class="form-textarea" placeholder="请输入入库说明"></textarea>
            </div>
            
            <div class="modal-actions">
                <button class="btn secondary" onclick="closeModal('stockInModal')">取消</button>
                <button class="btn primary" onclick="confirmStockIn()">确认入库</button>
            </div>
        </div>
    </div>

    <script>
        // 显示库存调整弹窗
        function adjustStock(sku) {
            document.getElementById('adjustSku').value = sku;
            document.getElementById('currentStock').value = '45'; // 示例数据
            document.getElementById('stockAdjustModal').classList.add('show');
        }

        // 显示采购入库弹窗
        function showStockIn() {
            document.getElementById('stockInModal').classList.add('show');
        }

        // 关闭弹窗
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // 计算调整后库存
        document.getElementById('adjustQuantity').addEventListener('input', function() {
            const current = parseInt(document.getElementById('currentStock').value) || 0;
            const adjust = parseInt(this.value) || 0;
            document.getElementById('newStock').value = current + adjust;
        });

        // 确认库存调整
        function confirmAdjust() {
            const sku = document.getElementById('adjustSku').value;
            const quantity = document.getElementById('adjustQuantity').value;
            const reason = document.getElementById('adjustReason').value;
            
            if (!quantity || !reason) {
                alert('请填写完整信息');
                return;
            }
            
            alert(`库存调整成功: ${sku} ${quantity > 0 ? '+' : ''}${quantity}`);
            closeModal('stockAdjustModal');
        }

        // 确认采购入库
        function confirmStockIn() {
            alert('采购入库成功');
            closeModal('stockInModal');
        }

        // 补货功能
        function restockProduct(sku) {
            if (confirm(`确定要为商品 ${sku} 创建补货申请吗？`)) {
                alert('补货申请已提交，将跳转到采购模块');
            }
        }

        // 紧急补货
        function urgentRestock(sku) {
            if (confirm(`确定要为商品 ${sku} 创建紧急补货申请吗？`)) {
                alert('紧急补货申请已提交，将优先处理');
            }
        }

        // 查看库存日志
        function viewStockLog(sku) {
            alert(`查看商品 ${sku} 的库存变更日志`);
        }

        // 导出库存
        function exportInventory() {
            alert('导出库存数据');
        }

        // 刷新库存
        function refreshInventory() {
            alert('刷新库存数据');
        }

        // 批量调整
        function showBatchAdjust() {
            alert('批量调整功能');
        }

        // 其他快速操作
        function showStockOut() {
            alert('库存出库功能');
        }

        function showStockTransfer() {
            alert('仓库调拨功能');
        }

        function showStockCheck() {
            alert('库存盘点功能');
        }

        function viewAllLogs() {
            alert('查看全部变更日志');
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html> 