<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>商品管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="../../assets/css/header-button-fix.css">
    <link rel="stylesheet" href="../../assets/css/table-enhancement.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="../../assets/js/image-error-handler.js"></script>
    <style>
        /* 商品管理页面专用样式 */
        * {
            box-sizing: border-box;
        }
        
        .product-management-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
            width: 100%;
            overflow-x: hidden;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }
        
        .page-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 10px 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }
        
        .action-btn:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
            text-decoration: none;
        }
        
        .action-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .action-btn.primary:hover {
            background: #2563eb;
        }
        
        .action-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .action-btn.danger:hover {
            background: #dc2626;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .stat-title {
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 8px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
            word-break: break-all;
        }
        
        .stat-desc {
            font-size: 12px;
            color: #64748b;
        }
        
        .main-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .content-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }
        
        .view-controls {
            display: flex;
            gap: 8px;
        }
        
        .view-btn {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .view-btn:hover {
            background: #f1f5f9;
        }
        
        .view-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .filter-section {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            background: #f8fafc;
        }
        
        .filter-row {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 16px;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
            min-width: 150px;
        }
        
        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
        }
        
        .filter-input {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            min-width: 150px;
        }
        
        .filter-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            min-width: 120px;
        }
        
        .filter-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .product-list {
            padding: 0;
            overflow-x: auto;
        }
        
        .product-item {
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            align-items: center;
            gap: 16px;
            min-width: 600px;
        }
        
        .product-item:hover {
            background: #f8fafc;
        }
        
        .product-checkbox {
            width: 18px;
            height: 18px;
            flex-shrink: 0;
        }
        
        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
            background: #f1f5f9;
            flex-shrink: 0;
        }
        
        .image-placeholder {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-size: 20px;
            flex-shrink: 0;
        }
        
        .product-info {
            flex: 1;
            min-width: 0;
        }
        
        .product-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .product-meta {
            font-size: 12px;
            color: #64748b;
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .product-category {
            background: #f1f5f9;
            color: #64748b;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            white-space: nowrap;
        }
        
        .product-price {
            font-size: 16px;
            font-weight: 600;
            color: #ef4444;
            margin: 0 16px;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .product-stock {
            font-size: 14px;
            color: #64748b;
            margin: 0 16px;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .stock-warning {
            color: #ef4444;
            font-weight: 600;
        }
        
        .product-status {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .status-online {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-offline {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .status-draft {
            background: #f3f4f6;
            color: #374151;
        }
        
        .product-actions {
            display: flex;
            gap: 8px;
            flex-shrink: 0;
        }
        
        .btn-sm {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }
        
        .btn-sm:hover {
            background: #f1f5f9;
        }
        
        .btn-sm.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .btn-sm.primary:hover {
            background: #2563eb;
        }
        
        .btn-sm.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .btn-sm.danger:hover {
            background: #dc2626;
        }
        
        .pagination {
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .pagination-info {
            font-size: 14px;
            color: #64748b;
        }
        
        .pagination-controls {
            display: flex;
            gap: 8px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background: white;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .page-btn:hover {
            background: #f1f5f9;
        }
        
        .page-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .bulk-actions {
            padding: 16px 24px;
            background: #eff6ff;
            border-bottom: 1px solid #e2e8f0;
            display: none;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .bulk-actions.show {
            display: flex;
        }
        
        .bulk-actions-info {
            font-size: 14px;
            color: #1e40af;
            margin-right: auto;
        }
        
        .price-range-inputs {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .price-range-inputs input {
            width: 80px;
        }
        
        /* 网格视图样式 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            padding: 24px;
        }
        
        .product-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .product-card-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f1f5f9;
        }
        
        .product-card-content {
            padding: 16px;
        }
        
        .product-card-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .product-card-price {
            font-size: 18px;
            font-weight: 700;
            color: #ef4444;
            margin-bottom: 8px;
        }
        
        .product-card-meta {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 12px;
        }
        
        .product-card-actions {
            display: flex;
            gap: 8px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .product-management-page {
                padding: 16px;
            }
            
            .page-header {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
            
            .page-title {
                font-size: 24px;
            }
            
            .page-actions {
                justify-content: stretch;
            }
            
            .action-btn {
                flex: 1;
                justify-content: center;
            }
            
            .stats-overview {
                grid-template-columns: 1fr 1fr;
                gap: 16px;
            }
            
            .stat-card {
                padding: 16px;
            }
            
            .content-header {
                padding: 16px;
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-section {
                padding: 16px;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                min-width: auto;
            }
            
            .filter-input,
            .filter-select {
                min-width: auto;
                width: 100%;
            }
            
            .price-range-inputs {
                width: 100%;
            }
            
            .price-range-inputs input {
                flex: 1;
                width: auto;
            }
            
            .product-list {
                margin: 0 -16px;
            }
            
            .product-item {
                flex-wrap: wrap;
                min-width: auto;
                padding: 12px 16px;
            }
            
            .product-info {
                order: 1;
                width: 100%;
                margin-top: 8px;
            }
            
            .product-actions {
                order: 2;
                width: 100%;
                justify-content: center;
                margin-top: 8px;
            }
            
            .pagination {
                padding: 16px;
                justify-content: center;
            }
            
            .pagination-info {
                order: 2;
                width: 100%;
                text-align: center;
                margin-top: 8px;
            }
            
            .product-grid {
                grid-template-columns: 1fr;
                padding: 16px;
                gap: 16px;
            }
        }
        
        @media (max-width: 480px) {
            .stats-overview {
                grid-template-columns: 1fr;
            }
            
            .page-actions {
                flex-direction: column;
            }
            
            .product-actions {
                flex-direction: column;
                gap: 4px;
            }
            
            .btn-sm {
                text-align: center;
            }
        }
        
        /* 确保所有元素都有正确的盒模型 */
        *, *::before, *::after {
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="product-management-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">商品管理</h1>
            <div class="page-actions">
                <a href="category-management.html" class="action-btn">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="inventory-management.html" class="action-btn">
                    <i class="fas fa-warehouse"></i>
                    库存管理
                </a>
                <a href="product-edit.html" class="action-btn primary">
                    <i class="fas fa-plus"></i>
                    新增商品
                </a>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-title">商品总数</div>
                <div class="stat-value">2,847</div>
                <div class="stat-desc">在售商品 1,234 | 下架商品 892</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">库存预警</div>
                <div class="stat-value" style="color: #ef4444;">23</div>
                <div class="stat-desc">低库存商品需及时补货</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">待审核</div>
                <div class="stat-value" style="color: #f59e0b;">12</div>
                <div class="stat-desc">新增商品等待审核</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">今日销量</div>
                <div class="stat-value" style="color: #10b981;">186</div>
                <div class="stat-desc">相比昨日 +15.2%</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 内容头部 -->
            <div class="content-header">
                <h2 class="content-title">商品列表</h2>
                <div class="view-controls">
                    <button class="view-btn active" onclick="switchView('list')">
                        <i class="fas fa-list"></i>
                    </button>
                    <button class="view-btn" onclick="switchView('grid')">
                        <i class="fas fa-th-large"></i>
                    </button>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">商品名称</label>
                        <input type="text" class="filter-input" placeholder="请输入商品名称">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">商品分类</label>
                        <select class="filter-select">
                            <option value="">全部分类</option>
                            <option value="electronics">电子产品</option>
                            <option value="clothing">服装配饰</option>
                            <option value="home">家居用品</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">商品状态</label>
                        <select class="filter-select">
                            <option value="">全部状态</option>
                            <option value="online">在售</option>
                            <option value="offline">下架</option>
                            <option value="draft">草稿</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">价格范围</label>
                        <div style="display: flex; gap: 8px;">
                            <input type="number" class="filter-input" placeholder="最低价" style="width: 80px;">
                            <span style="align-self: center;">-</span>
                            <input type="number" class="filter-input" placeholder="最高价" style="width: 80px;">
                        </div>
                    </div>
                    <button class="action-btn primary" style="align-self: flex-end;">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                </div>
            </div>

            <!-- 批量操作栏 -->
            <div id="bulkActions" class="bulk-actions">
                <span class="bulk-actions-info">已选择 <span id="selectedCount">0</span> 个商品</span>
                <div class="page-actions">
                    <button class="action-btn" onclick="bulkAction('online')">
                        <i class="fas fa-eye"></i>
                        批量上架
                    </button>
                    <button class="action-btn" onclick="bulkAction('offline')">
                        <i class="fas fa-eye-slash"></i>
                        批量下架
                    </button>
                    <button class="action-btn danger" onclick="bulkAction('delete')">
                        <i class="fas fa-trash"></i>
                        批量删除
                    </button>
                </div>
            </div>

            <!-- 商品列表表格 -->
            <div style="overflow-x: auto;">
                <table class="product-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" onchange="toggleSelectAll(this.checked)"></th>
                            <th>商品信息</th>
                            <th>分类</th>
                            <th>价格</th>
                            <th>库存</th>
                            <th>状态</th>
                            <th>销量</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" class="product-checkbox" onchange="updateBulkActions()"></td>
                            <td>
                                <div class="product-info">
                                    <img src="../../assets/images/product-1.jpg" alt="商品图片" class="product-image">
                                    <div class="product-details">
                                        <div class="product-name">iPhone 14 Pro Max 128GB 深空黑色</div>
                                        <div class="product-sku">SKU: IP14PM128BK</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="type-badge individual">电子产品</span></td>
                            <td><div class="price-display">¥8,999</div></td>
                            <td>
                                <div class="data-stats">
                                    <div class="main-stat">45</div>
                                    <div class="sub-stat">正常库存</div>
                                </div>
                            </td>
                            <td><span class="status-badge active">在售</span></td>
                            <td>
                                <div class="data-stats">
                                    <div class="main-stat">128</div>
                                    <div class="sub-stat">月销量</div>
                                </div>
                            </td>
                            <td>2024-01-15</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-icon-btn primary" onclick="editProduct(1)" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-icon-btn" onclick="viewProduct(1)" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-icon-btn danger" onclick="deleteProduct(1)" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="product-checkbox" onchange="updateBulkActions()"></td>
                            <td>
                                <div class="product-info">
                                    <img src="../../assets/images/product-2.jpg" alt="商品图片" class="product-image">
                                    <div class="product-details">
                                        <div class="product-name">Nike Air Max 270 男士运动鞋</div>
                                        <div class="product-sku">SKU: NAM270MEN</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="type-badge enterprise">服装配饰</span></td>
                            <td><div class="price-display">¥1,299</div></td>
                            <td>
                                <div class="data-stats">
                                    <div class="main-stat" style="color: #ef4444;">3</div>
                                    <div class="sub-stat">库存不足</div>
                                </div>
                            </td>
                            <td><span class="status-badge active">在售</span></td>
                            <td>
                                <div class="data-stats">
                                    <div class="main-stat">89</div>
                                    <div class="sub-stat">月销量</div>
                                </div>
                            </td>
                            <td>2024-02-10</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-icon-btn primary" onclick="editProduct(2)" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-icon-btn warning" onclick="restockProduct(2)" title="补货">
                                        <i class="fas fa-boxes"></i>
                                    </button>
                                    <button class="action-icon-btn danger" onclick="deleteProduct(2)" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="product-checkbox" onchange="updateBulkActions()"></td>
                            <td>
                                <div class="product-info">
                                    <img src="../../assets/images/product-3.jpg" alt="商品图片" class="product-image">
                                    <div class="product-details">
                                        <div class="product-name">MUJI 无印良品 羽绒被 双人</div>
                                        <div class="product-sku">SKU: MUJI-DOWN-D</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="type-badge flagship">家居用品</span></td>
                            <td><div class="price-display">¥899</div></td>
                            <td>
                                <div class="data-stats">
                                    <div class="main-stat">128</div>
                                    <div class="sub-stat">充足库存</div>
                                </div>
                            </td>
                            <td><span class="status-badge pending">草稿</span></td>
                            <td>
                                <div class="data-stats">
                                    <div class="main-stat">0</div>
                                    <div class="sub-stat">未发布</div>
                                </div>
                            </td>
                            <td>2024-03-05</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-icon-btn primary" onclick="editProduct(3)" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-icon-btn success" onclick="publishProduct(3)" title="发布">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="action-icon-btn danger" onclick="deleteProduct(3)" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 1-20 条，共 2,847 条记录
                </div>
                <div class="pagination-controls">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 视图切换
        function switchView(view) {
            const buttons = document.querySelectorAll('.view-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.closest('.view-btn').classList.add('active');
            
            // 这里可以添加切换视图的逻辑
            if (view === 'grid') {
                // 切换到网格视图
                console.log('切换到网格视图');
            } else {
                // 切换到列表视图
                console.log('切换到列表视图');
            }
        }

        // 全选切换
        function toggleSelectAll(checked) {
            const checkboxes = document.querySelectorAll('.product-checkbox');
            checkboxes.forEach(cb => cb.checked = checked);
            updateBulkActions();
        }

        // 更新批量操作栏
        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.product-checkbox');
            const checked = Array.from(checkboxes).filter(cb => cb.checked);
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            
            if (checked.length > 0) {
                bulkActions.classList.add('show');
                selectedCount.textContent = checked.length;
            } else {
                bulkActions.classList.remove('show');
            }
        }

        // 批量操作
        function bulkAction(action) {
            const checkboxes = document.querySelectorAll('.product-checkbox:checked');
            const productIds = Array.from(checkboxes).map((cb, index) => index + 1);
            
            switch(action) {
                case 'online':
                    alert(`批量上架商品: ${productIds.join(', ')}`);
                    break;
                case 'offline':
                    alert(`批量下架商品: ${productIds.join(', ')}`);
                    break;
                case 'delete':
                    if (confirm('确定要删除选中的商品吗？')) {
                        alert(`批量删除商品: ${productIds.join(', ')}`);
                    }
                    break;
            }
        }

        // 商品操作
        function editProduct(id) {
            window.location.href = `product-edit.html?id=${id}`;
        }

        function viewProduct(id) {
            window.location.href = `product-detail.html?id=${id}`;
        }

        function deleteProduct(id) {
            if (confirm('确定要删除该商品吗？')) {
                alert(`删除商品 ${id}`);
            }
        }

        function restockProduct(id) {
            window.location.href = `../inventory-management/index.html?product=${id}`;
        }

        function publishProduct(id) {
            if (confirm('确定要发布该商品吗？')) {
                alert(`发布商品 ${id}`);
            }
        }
    </script>
</body>
</html> 