<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品编辑 - TeleShop后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { margin: 0; padding: 0; font-family: 'Inter', sans-serif; background: #f8fafc; }
        .product-edit-page { padding: 24px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 32px; }
        .page-title { font-size: 28px; font-weight: 700; color: #1e293b; margin: 0; }
        .header-actions { display: flex; gap: 12px; }
        .btn { padding: 10px 20px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px; cursor: pointer; transition: all 0.2s; display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }
        .btn:hover { background: #f1f5f9; border-color: #cbd5e1; }
        .btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .btn.secondary { background: #64748b; color: white; border-color: #64748b; }
        
        .form-container { display: grid; grid-template-columns: 1fr 350px; gap: 24px; }
        .form-main { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .form-sidebar { display: flex; flex-direction: column; gap: 20px; }
        .sidebar-card { background: white; padding: 20px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        
        .form-section { padding: 24px; border-bottom: 1px solid #e2e8f0; }
        .form-section:last-child { border-bottom: none; }
        .section-title { font-size: 18px; font-weight: 600; color: #1e293b; margin-bottom: 20px; }
        
        .form-group { margin-bottom: 20px; }
        .form-label { display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px; }
        .form-input, .form-select, .form-textarea { width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; transition: border-color 0.2s; }
        .form-input:focus, .form-select:focus, .form-textarea:focus { outline: none; border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
        .form-textarea { resize: vertical; min-height: 100px; }
        
        .image-upload { border: 2px dashed #d1d5db; border-radius: 8px; padding: 40px; text-align: center; cursor: pointer; transition: all 0.2s; }
        .image-upload:hover { border-color: #3b82f6; background: #f8fafc; }
        .image-upload.dragover { border-color: #3b82f6; background: #eff6ff; }
        
        .image-preview { display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 12px; margin-top: 16px; }
        .preview-item { position: relative; border-radius: 8px; overflow: hidden; }
        .preview-image { width: 100%; height: 100px; object-fit: cover; }
        .preview-remove { position: absolute; top: 4px; right: 4px; width: 24px; height: 24px; background: rgba(239, 68, 68, 0.9); color: white; border: none; border-radius: 50%; cursor: pointer; font-size: 12px; }
        
        .price-group { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; }
        .stock-group { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px; }
        
        .status-toggle { display: flex; align-items: center; gap: 12px; }
        .switch { position: relative; width: 44px; height: 24px; }
        .switch input { opacity: 0; width: 0; height: 0; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #cbd5e1; transition: .2s; border-radius: 24px; }
        .slider:before { position: absolute; content: ""; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .2s; border-radius: 50%; }
        input:checked + .slider { background-color: #3b82f6; }
        input:checked + .slider:before { transform: translateX(20px); }
        
        .variant-section { border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin-bottom: 16px; }
        .variant-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; }
        .variant-title { font-weight: 500; color: #1e293b; }
        .variant-remove { background: #ef4444; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px; }
        
        .add-variant { background: #f8fafc; border: 2px dashed #d1d5db; border-radius: 8px; padding: 20px; text-align: center; cursor: pointer; margin-bottom: 16px; }
        .add-variant:hover { border-color: #3b82f6; background: #eff6ff; }
        
        .seo-preview { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin-top: 16px; }
        .seo-title { color: #1a0dab; font-size: 18px; text-decoration: underline; margin-bottom: 4px; cursor: pointer; }
        .seo-url { color: #006621; font-size: 14px; margin-bottom: 8px; }
        .seo-desc { color: #545454; font-size: 14px; line-height: 1.4; }
        
        @media (max-width: 1024px) {
            .form-container { grid-template-columns: 1fr; }
            .price-group { grid-template-columns: 1fr; }
            .stock-group { grid-template-columns: 1fr 1fr; }
        }
        
        @media (max-width: 768px) {
            .product-edit-page { padding: 16px; }
            .page-header { flex-direction: column; align-items: stretch; gap: 16px; }
            .stock-group { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="product-edit-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">编辑商品</h1>
            <div class="header-actions">
                <a href="index.html" class="btn">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
                <button class="btn secondary" onclick="previewProduct()">
                    <i class="fas fa-eye"></i>
                    预览
                </button>
                <button class="btn" onclick="saveAsDraft()">
                    <i class="fas fa-save"></i>
                    保存草稿
                </button>
                <button class="btn primary" onclick="publishProduct()">
                    <i class="fas fa-check"></i>
                    发布商品
                </button>
            </div>
        </div>

        <div class="form-container">
            <!-- 主要表单区域 -->
            <div class="form-main">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h2 class="section-title">基本信息</h2>
                    
                    <div class="form-group">
                        <label class="form-label" for="productName">商品名称 *</label>
                        <input type="text" id="productName" class="form-input" value="iPhone 14 Pro Max 128GB 深空黑色" placeholder="请输入商品名称">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="productDesc">商品描述</label>
                        <textarea id="productDesc" class="form-textarea" placeholder="请输入商品描述">iPhone 14 Pro Max，采用A16仿生芯片，配备专业级摄像头系统，支持灵动岛设计，为您带来前所未有的智能手机体验。</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="productCategory">商品分类 *</label>
                        <select id="productCategory" class="form-select">
                            <option value="">请选择分类</option>
                            <option value="electronics" selected>电子产品</option>
                            <option value="clothing">服装配饰</option>
                            <option value="home">家居用品</option>
                            <option value="beauty">美妆护肤</option>
                            <option value="sports">运动户外</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="productBrand">品牌</label>
                        <input type="text" id="productBrand" class="form-input" value="Apple" placeholder="请输入品牌名称">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="productSku">SKU *</label>
                        <input type="text" id="productSku" class="form-input" value="IP14PM128BK" placeholder="请输入SKU">
                    </div>
                </div>

                <!-- 商品图片 -->
                <div class="form-section">
                    <h2 class="section-title">商品图片</h2>
                    
                    <div class="form-group">
                        <label class="form-label">商品图片</label>
                        <div class="image-upload" onclick="triggerImageUpload()">
                            <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #94a3b8; margin-bottom: 16px;"></i>
                            <p style="margin: 0; color: #64748b;">点击上传图片或拖拽图片到此处</p>
                            <p style="margin: 8px 0 0 0; color: #94a3b8; font-size: 12px;">支持 JPG、PNG 格式，建议尺寸 800x800px</p>
                        </div>
                        <input type="file" id="imageUpload" multiple accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
                        
                        <div class="image-preview" id="imagePreview">
                            <div class="preview-item">
                                <img src="../../assets/images/product-default.jpg" class="preview-image" alt="商品图片">
                                <button class="preview-remove" onclick="removeImage(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 价格与库存 -->
                <div class="form-section">
                    <h2 class="section-title">价格与库存</h2>
                    
                    <div class="price-group">
                        <div class="form-group">
                            <label class="form-label" for="salePrice">销售价格 *</label>
                            <input type="number" id="salePrice" class="form-input" value="8999" placeholder="0.00" step="0.01">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="originalPrice">原价</label>
                            <input type="number" id="originalPrice" class="form-input" value="9999" placeholder="0.00" step="0.01">
                        </div>
                    </div>
                    
                    <div class="stock-group">
                        <div class="form-group">
                            <label class="form-label" for="stockQuantity">库存数量 *</label>
                            <input type="number" id="stockQuantity" class="form-input" value="234" placeholder="0" min="0">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="lowStockAlert">低库存预警</label>
                            <input type="number" id="lowStockAlert" class="form-input" value="10" placeholder="0" min="0">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="maxOrder">最大订购量</label>
                            <input type="number" id="maxOrder" class="form-input" value="5" placeholder="不限制" min="1">
                        </div>
                    </div>
                </div>

                <!-- 商品规格 -->
                <div class="form-section">
                    <h2 class="section-title">商品规格</h2>
                    
                    <div class="add-variant" onclick="addVariant()">
                        <i class="fas fa-plus" style="color: #3b82f6; margin-right: 8px;"></i>
                        添加规格
                    </div>
                    
                    <div id="variantContainer">
                        <div class="variant-section">
                            <div class="variant-header">
                                <span class="variant-title">颜色</span>
                                <button class="variant-remove" onclick="removeVariant(this)">删除</button>
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-input" value="深空黑色,银色,金色,暗紫色" placeholder="请输入规格值，用逗号分隔">
                            </div>
                        </div>
                        
                        <div class="variant-section">
                            <div class="variant-header">
                                <span class="variant-title">存储容量</span>
                                <button class="variant-remove" onclick="removeVariant(this)">删除</button>
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-input" value="128GB,256GB,512GB,1TB" placeholder="请输入规格值，用逗号分隔">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO设置 -->
                <div class="form-section">
                    <h2 class="section-title">SEO设置</h2>
                    
                    <div class="form-group">
                        <label class="form-label" for="seoTitle">SEO标题</label>
                        <input type="text" id="seoTitle" class="form-input" value="iPhone 14 Pro Max 128GB 深空黑色 - TeleShop" placeholder="请输入SEO标题" onkeyup="updateSeoPreview()">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="seoDesc">SEO描述</label>
                        <textarea id="seoDesc" class="form-textarea" placeholder="请输入SEO描述" onkeyup="updateSeoPreview()">iPhone 14 Pro Max，A16仿生芯片，专业级摄像头系统，灵动岛设计，现货热销中！</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="seoKeywords">关键词</label>
                        <input type="text" id="seoKeywords" class="form-input" value="iPhone,苹果手机,智能手机,Pro Max" placeholder="请输入关键词，用逗号分隔">
                    </div>
                    
                    <div class="seo-preview">
                        <div class="seo-title">iPhone 14 Pro Max 128GB 深空黑色 - TeleShop</div>
                        <div class="seo-url">https://teleshop.com/product/iphone-14-pro-max-128gb</div>
                        <div class="seo-desc">iPhone 14 Pro Max，A16仿生芯片，专业级摄像头系统，灵动岛设计，现货热销中！</div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="form-sidebar">
                <!-- 发布状态 -->
                <div class="sidebar-card">
                    <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 16px;">发布状态</h3>
                    
                    <div class="form-group">
                        <label class="form-label">商品状态</label>
                        <div class="status-toggle">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <span style="font-size: 14px; color: #1e293b;">上架销售</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">发布时间</label>
                        <input type="datetime-local" class="form-input" value="2024-01-15T10:00">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">商品排序</label>
                        <input type="number" class="form-input" value="100" placeholder="数值越大排序越靠前">
                    </div>
                </div>

                <!-- 商品标签 -->
                <div class="sidebar-card">
                    <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 16px;">商品标签</h3>
                    
                    <div class="form-group">
                        <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 12px;">
                            <span style="background: #eff6ff; color: #1e40af; padding: 4px 8px; border-radius: 12px; font-size: 12px;">热销</span>
                            <span style="background: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 12px; font-size: 12px;">新品</span>
                            <span style="background: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 12px; font-size: 12px;">推荐</span>
                        </div>
                        <input type="text" class="form-input" placeholder="输入标签后按回车添加" onkeypress="addTag(event)">
                    </div>
                </div>

                <!-- 运费设置 -->
                <div class="sidebar-card">
                    <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 16px;">运费设置</h3>
                    
                    <div class="form-group">
                        <label class="form-label">运费模板</label>
                        <select class="form-select">
                            <option value="free">包邮</option>
                            <option value="template1" selected>标准运费模板</option>
                            <option value="template2">重货运费模板</option>
                            <option value="custom">自定义运费</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">商品重量 (kg)</label>
                        <input type="number" class="form-input" value="0.3" step="0.1" min="0">
                    </div>
                </div>

                <!-- 库存预警 -->
                <div class="sidebar-card">
                    <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 16px;">库存状态</h3>
                    
                    <div style="padding: 12px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px; margin-bottom: 12px;">
                        <div style="display: flex; align-items: center; gap: 8px; color: #0369a1; font-size: 14px;">
                            <i class="fas fa-info-circle"></i>
                            当前库存充足
                        </div>
                    </div>
                    
                    <div style="font-size: 14px; color: #64748b; line-height: 1.5;">
                        <div>当前库存：<strong>234</strong> 件</div>
                        <div>已售数量：<strong>1,856</strong> 件</div>
                        <div>预警阈值：<strong>10</strong> 件</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 图片上传相关
        function triggerImageUpload() {
            document.getElementById('imageUpload').click();
        }

        function handleImageUpload(event) {
            const files = event.target.files;
            const preview = document.getElementById('imagePreview');
            
            for (let file of files) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'preview-item';
                        previewItem.innerHTML = `
                            <img src="${e.target.result}" class="preview-image" alt="商品图片">
                            <button class="preview-remove" onclick="removeImage(this)">
                                <i class="fas fa-times"></i>
                            </button>
                        `;
                        preview.appendChild(previewItem);
                    };
                    reader.readAsDataURL(file);
                }
            }
        }

        function removeImage(button) {
            button.parentElement.remove();
        }

        // 规格管理
        function addVariant() {
            const container = document.getElementById('variantContainer');
            const variantSection = document.createElement('div');
            variantSection.className = 'variant-section';
            variantSection.innerHTML = `
                <div class="variant-header">
                    <input type="text" class="variant-title" style="border: none; background: none; font-weight: 500; color: #1e293b;" placeholder="规格名称" value="新规格">
                    <button class="variant-remove" onclick="removeVariant(this)">删除</button>
                </div>
                <div class="form-group">
                    <input type="text" class="form-input" placeholder="请输入规格值，用逗号分隔">
                </div>
            `;
            container.appendChild(variantSection);
        }

        function removeVariant(button) {
            button.closest('.variant-section').remove();
        }

        // 标签管理
        function addTag(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const input = event.target;
                const tagText = input.value.trim();
                if (tagText) {
                    const tagContainer = input.previousElementSibling;
                    const tag = document.createElement('span');
                    tag.style.cssText = 'background: #eff6ff; color: #1e40af; padding: 4px 8px; border-radius: 12px; font-size: 12px; cursor: pointer;';
                    tag.innerHTML = `${tagText} <i class="fas fa-times" style="margin-left: 4px;" onclick="this.parentElement.remove()"></i>`;
                    tagContainer.appendChild(tag);
                    input.value = '';
                }
            }
        }

        // SEO预览更新
        function updateSeoPreview() {
            const title = document.getElementById('seoTitle').value;
            const desc = document.getElementById('seoDesc').value;
            
            document.querySelector('.seo-title').textContent = title || '商品标题';
            document.querySelector('.seo-desc').textContent = desc || '商品描述';
        }

        // 功能按钮
        function previewProduct() {
            alert('商品预览功能开发中...');
        }

        function saveAsDraft() {
            alert('商品已保存为草稿！');
        }

        function publishProduct() {
            if (confirm('确定要发布此商品吗？')) {
                alert('商品已成功发布！');
            }
        }

        // 拖拽上传
        const imageUpload = document.querySelector('.image-upload');
        
        imageUpload.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        imageUpload.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        imageUpload.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            const files = e.dataTransfer.files;
            handleImageUpload({target: {files}});
        });
    </script>
</body>
</html> 