<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>朋友圈管理 - TeleShop Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <style>
        .moments-admin {
            padding: 20px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #1a202c;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
        }

        .stat-icon.posts {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-icon.reviews {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stat-icon.users {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stat-icon.reports {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #1a202c;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
        }

        .management-tabs {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .tabs-header {
            display: flex;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            color: #64748b;
            font-weight: 500;
        }

        .tab-btn.active {
            color: #667eea;
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: #667eea;
        }

        .tab-content {
            padding: 20px;
        }

        .filters-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-select {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 8px 12px;
            background: white;
            font-size: 14px;
        }

        .search-input {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            width: 250px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .moment-list {
            display: grid;
            gap: 15px;
        }

        .moment-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .moment-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .moment-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-info {
            flex: 1;
        }

        .username {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .post-time {
            font-size: 12px;
            color: #64748b;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-approved {
            background: #d1fae5;
            color: #065f46;
        }

        .status-rejected {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-reported {
            background: #fecaca;
            color: #b91c1c;
        }

        .moment-content {
            margin-bottom: 15px;
        }

        .moment-text {
            margin-bottom: 10px;
            line-height: 1.5;
        }

        .moment-media {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }

        .media-thumbnail {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            object-fit: cover;
            cursor: pointer;
        }

        .moment-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #64748b;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .moment-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .sensitive-words {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .sensitive-title {
            font-weight: 600;
            color: #dc2626;
            margin-bottom: 10px;
        }

        .words-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .word-tag {
            background: #dc2626;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .analytics-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart-container {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
            border-radius: 8px;
            color: #64748b;
        }

        .settings-grid {
            display: grid;
            gap: 20px;
        }

        .setting-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .setting-title {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .setting-description {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 10px 12px;
            font-size: 14px;
        }

        .form-textarea {
            width: 100%;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 10px 12px;
            font-size: 14px;
            min-height: 80px;
            resize: vertical;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 25px;
        }

        .toggle-input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #cbd5e0;
            transition: .4s;
            border-radius: 25px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 19px;
            width: 19px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .toggle-input:checked + .toggle-slider {
            background-color: #667eea;
        }

        .toggle-input:checked + .toggle-slider:before {
            transform: translateX(25px);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #64748b;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .moments-admin {
                padding: 15px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .filters-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                width: 100%;
            }

            .tabs-header {
                flex-wrap: wrap;
            }

            .tab-btn {
                flex: 1;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="moments-admin">
        <div class="page-header">
            <h1 class="page-title">朋友圈管理</h1>
            <p class="page-subtitle">管理用户动态内容、审核发布、监控用户行为</p>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon posts">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="stat-number">12,547</div>
                <div class="stat-label">总动态数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon reviews">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number">156</div>
                <div class="stat-label">待审核</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon users">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number">8,923</div>
                <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon reports">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number">23</div>
                <div class="stat-label">举报处理</div>
            </div>
        </div>

        <!-- 管理标签页 -->
        <div class="management-tabs">
            <div class="tabs-header">
                <button class="tab-btn active" onclick="switchTab('content')">
                    <i class="fas fa-list"></i> 内容管理
                </button>
                <button class="tab-btn" onclick="switchTab('review')">
                    <i class="fas fa-search"></i> 内容审核
                </button>
                <button class="tab-btn" onclick="switchTab('analytics')">
                    <i class="fas fa-chart-bar"></i> 数据分析
                </button>
                <button class="tab-btn" onclick="switchTab('settings')">
                    <i class="fas fa-cog"></i> 系统设置
                </button>
            </div>

            <!-- 内容管理 -->
            <div class="tab-content" id="contentTab">
                <div class="filters-bar">
                    <div class="filter-group">
                        <label>状态:</label>
                        <select class="filter-select" id="statusFilter">
                            <option value="">全部</option>
                            <option value="approved">已通过</option>
                            <option value="pending">待审核</option>
                            <option value="rejected">已拒绝</option>
                            <option value="reported">被举报</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>时间:</label>
                        <select class="filter-select" id="timeFilter">
                            <option value="">全部</option>
                            <option value="today">今天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>类型:</label>
                        <select class="filter-select" id="typeFilter">
                            <option value="">全部</option>
                            <option value="text">文字</option>
                            <option value="image">图片</option>
                            <option value="video">视频</option>
                            <option value="product">商品分享</option>
                        </select>
                    </div>
                    <input type="text" class="search-input" placeholder="搜索用户名或内容..." id="searchInput">
                    <button class="btn btn-primary" onclick="searchMoments()">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>

                <div class="moment-list" id="momentList">
                    <!-- 动态项目1 -->
                    <div class="moment-card">
                        <div class="moment-header">
                            <img src="https://via.placeholder.com/40x40/667eea/ffffff?text=A" alt="头像" class="user-avatar">
                            <div class="user-info">
                                <div class="username">Alice Chen</div>
                                <div class="post-time">2024-01-15 14:30</div>
                            </div>
                            <span class="status-badge status-approved">已通过</span>
                        </div>
                        
                        <div class="moment-content">
                            <div class="moment-text">
                                今天收到了心心念念的新手机！📱 性能真的太棒了，拍照效果也超级赞！推荐给大家~
                                <span style="color: #667eea;">#数码分享 #新手机 #好物推荐</span>
                            </div>
                            <div class="moment-media">
                                <img src="https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=60&h=60&fit=crop" alt="" class="media-thumbnail" onclick="viewMedia(this)">
                                <img src="https://images.unsplash.com/photo-1574944985070-8f3ebc6b79d2?w=60&h=60&fit=crop" alt="" class="media-thumbnail" onclick="viewMedia(this)">
                            </div>
                        </div>
                        
                        <div class="moment-stats">
                            <div class="stat-item">
                                <i class="fas fa-heart"></i>
                                <span>25 点赞</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-comment"></i>
                                <span>15 评论</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-share"></i>
                                <span>8 分享</span>
                            </div>
                        </div>
                        
                        <div class="moment-actions">
                            <button class="btn btn-primary" onclick="viewMomentDetail(1)">
                                <i class="fas fa-eye"></i> 查看详情
                            </button>
                            <button class="btn btn-secondary" onclick="editMoment(1)">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger" onclick="deleteMoment(1)">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>

                    <!-- 动态项目2 -->
                    <div class="moment-card">
                        <div class="moment-header">
                            <img src="https://via.placeholder.com/40x40/ff6b6b/ffffff?text=B" alt="头像" class="user-avatar">
                            <div class="user-info">
                                <div class="username">Bob Wang</div>
                                <div class="post-time">2024-01-15 12:15</div>
                            </div>
                            <span class="status-pending">待审核</span>
                        </div>
                        
                        <div class="sensitive-words">
                            <div class="sensitive-title">检测到敏感词汇:</div>
                            <div class="words-list">
                                <span class="word-tag">违规词汇</span>
                                <span class="word-tag">不当内容</span>
                            </div>
                        </div>
                        
                        <div class="moment-content">
                            <div class="moment-text">
                                这是一条包含敏感词汇的动态内容，需要人工审核...
                            </div>
                        </div>
                        
                        <div class="moment-actions">
                            <button class="btn btn-success" onclick="approveMoment(2)">
                                <i class="fas fa-check"></i> 通过
                            </button>
                            <button class="btn btn-danger" onclick="rejectMoment(2)">
                                <i class="fas fa-times"></i> 拒绝
                            </button>
                            <button class="btn btn-warning" onclick="editAndApprove(2)">
                                <i class="fas fa-edit"></i> 编辑后通过
                            </button>
                        </div>
                    </div>

                    <!-- 动态项目3 -->
                    <div class="moment-card">
                        <div class="moment-header">
                            <img src="https://via.placeholder.com/40x40/4ecdc4/ffffff?text=C" alt="头像" class="user-avatar">
                            <div class="user-info">
                                <div class="username">Carol Li</div>
                                <div class="post-time">2024-01-15 10:45</div>
                            </div>
                            <span class="status-reported">被举报</span>
                        </div>
                        
                        <div class="moment-content">
                            <div class="moment-text">
                                这是一条被用户举报的动态内容，需要管理员处理...
                            </div>
                            <div class="moment-media">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop" alt="" class="media-thumbnail" onclick="viewMedia(this)">
                            </div>
                        </div>
                        
                        <div class="moment-stats">
                            <div class="stat-item">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>3 次举报</span>
                            </div>
                        </div>
                        
                        <div class="moment-actions">
                            <button class="btn btn-primary" onclick="viewReports(3)">
                                <i class="fas fa-list"></i> 查看举报
                            </button>
                            <button class="btn btn-success" onclick="dismissReports(3)">
                                <i class="fas fa-check"></i> 驳回举报
                            </button>
                            <button class="btn btn-danger" onclick="confirmViolation(3)">
                                <i class="fas fa-ban"></i> 确认违规
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容审核 -->
            <div class="tab-content hidden" id="reviewTab">
                <h3>自动审核设置</h3>
                <div class="settings-grid">
                    <div class="setting-card">
                        <div class="setting-title">敏感词过滤</div>
                        <div class="setting-description">自动检测和过滤动态中的敏感词汇</div>
                        <div class="form-group">
                            <label class="form-label">敏感词库</label>
                            <textarea class="form-textarea" placeholder="每行一个敏感词..."># 政治敏感词
政治
政府
官方

# 商业敏感词
传销
诈骗
违法

# 色情暴力
色情
暴力
血腥</textarea>
                        </div>
                        <button class="btn btn-primary">更新词库</button>
                    </div>

                    <div class="setting-card">
                        <div class="setting-title">图像识别</div>
                        <div class="setting-description">使用AI技术识别图片中的不当内容</div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            启用图像内容识别
                        </div>
                        <div class="form-group">
                            <label class="form-label">识别阈值</label>
                            <input type="range" min="1" max="100" value="75" class="form-input">
                            <small>当前设置: 75% (置信度)</small>
                        </div>
                    </div>

                    <div class="setting-card">
                        <div class="setting-title">自动审核规则</div>
                        <div class="setting-description">设定自动审核的条件和操作</div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            包含敏感词自动拒绝
                        </div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input">
                                <span class="toggle-slider"></span>
                            </label>
                            新用户动态需要审核
                        </div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            商品分享自动通过
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据分析 -->
            <div class="tab-content hidden" id="analyticsTab">
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>动态发布趋势</h3>
                        <div class="chart-container">
                            <canvas id="publishTrendChart" width="400" height="300"></canvas>
                        </div>
                        <div class="chart-summary">
                            <div class="summary-item">
                                <span class="label">今日发布:</span>
                                <span class="value">156条</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">较昨日:</span>
                                <span class="value trend-up">+23%</span>
                            </div>
                        </div>
                    </div>

                    <div class="analytics-card">
                        <h3>用户活跃度</h3>
                        <div class="chart-container">
                            <canvas id="userActivityChart" width="400" height="300"></canvas>
                        </div>
                        <div class="chart-summary">
                            <div class="summary-item">
                                <span class="label">活跃用户:</span>
                                <span class="value">2,341人</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">新增用户:</span>
                                <span class="value">89人</span>
                            </div>
                        </div>
                    </div>

                    <div class="analytics-card">
                        <h3>内容类型分布</h3>
                        <div class="chart-container">
                            <canvas id="contentTypeChart" width="400" height="300"></canvas>
                        </div>
                        <div class="chart-legend">
                            <div class="legend-item">
                                <span class="color-dot" style="background: #3b82f6;"></span>
                                <span>图文动态 (45%)</span>
                            </div>
                            <div class="legend-item">
                                <span class="color-dot" style="background: #10b981;"></span>
                                <span>纯文字 (30%)</span>
                            </div>
                            <div class="legend-item">
                                <span class="color-dot" style="background: #f59e0b;"></span>
                                <span>视频 (15%)</span>
                            </div>
                            <div class="legend-item">
                                <span class="color-dot" style="background: #ef4444;"></span>
                                <span>其他 (10%)</span>
                            </div>
                        </div>
                    </div>

                    <div class="analytics-card">
                        <h3>审核效率统计</h3>
                        <div class="chart-container">
                            <canvas id="reviewEfficiencyChart" width="400" height="300"></canvas>
                        </div>
                        <div class="efficiency-stats">
                            <div class="stat-row">
                                <span class="stat-label">平均审核时间:</span>
                                <span class="stat-value">12分钟</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-label">审核通过率:</span>
                                <span class="stat-value">87.3%</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-label">自动审核率:</span>
                                <span class="stat-value">65.8%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analytics-card" style="margin-top: 20px;">
                    <h3>实时监控</h3>
                    <div class="real-time-stats">
                        <div class="real-time-item">
                            <div class="stat-number">23</div>
                            <div class="stat-description">待审核动态</div>
                            <div class="stat-change">+5 (最近1小时)</div>
                        </div>
                        <div class="real-time-item">
                            <div class="stat-number">8</div>
                            <div class="stat-description">举报待处理</div>
                            <div class="stat-change">-2 (最近1小时)</div>
                        </div>
                        <div class="real-time-item">
                            <div class="stat-number">156</div>
                            <div class="stat-description">今日新增动态</div>
                            <div class="stat-change">+34 (最近1小时)</div>
                        </div>
                        <div class="real-time-item">
                            <div class="stat-number">99.2%</div>
                            <div class="stat-description">系统稳定性</div>
                            <div class="stat-change">正常运行</div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn btn-primary" onclick="exportAnalyticsReport()">
                        <i class="fas fa-download"></i> 导出分析报告
                    </button>
                    <button class="btn btn-secondary" onclick="refreshAnalytics()">
                        <i class="fas fa-sync"></i> 刷新数据
                    </button>
                </div>
            </div>

            <!-- 系统设置 -->
            <div class="tab-content hidden" id="settingsTab">
                <div class="settings-grid">
                    <div class="setting-card">
                        <div class="setting-title">发布权限设置</div>
                        <div class="setting-description">控制用户发布动态的权限和限制</div>
                        <div class="form-group">
                            <label class="form-label">单日发布限制</label>
                            <input type="number" class="form-input" value="10" min="1" max="100">
                        </div>
                        <div class="form-group">
                            <label class="form-label">图片数量限制</label>
                            <input type="number" class="form-input" value="9" min="1" max="20">
                        </div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            允许视频上传
                        </div>
                        <div class="form-group">
                            <label class="form-label">视频大小限制 (MB)</label>
                            <input type="number" class="form-input" value="50" min="1" max="500">
                        </div>
                    </div>

                    <div class="setting-card">
                        <div class="setting-title">互动功能设置</div>
                        <div class="setting-description">配置点赞、评论、分享等互动功能</div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            允许匿名点赞
                        </div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            允许评论回复
                        </div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input">
                                <span class="toggle-slider"></span>
                            </label>
                            评论需要审核
                        </div>
                        <div class="form-group">
                            <label class="form-label">评论长度限制</label>
                            <input type="number" class="form-input" value="500" min="50" max="2000">
                        </div>
                    </div>

                    <div class="setting-card">
                        <div class="setting-title">通知设置</div>
                        <div class="setting-description">配置各种通知的发送方式</div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            审核结果通知用户
                        </div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            违规内容警告通知
                        </div>
                        <div class="form-group">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-input">
                                <span class="toggle-slider"></span>
                            </label>
                            管理员审核提醒
                        </div>
                    </div>

                    <div class="setting-card">
                        <div class="setting-title">数据保留设置</div>
                        <div class="setting-description">设置已删除内容的保留期限</div>
                        <div class="form-group">
                            <label class="form-label">已删除动态保留天数</label>
                            <input type="number" class="form-input" value="30" min="1" max="365">
                        </div>
                        <div class="form-group">
                            <label class="form-label">用户数据备份频率</label>
                            <select class="filter-select">
                                <option value="daily">每天</option>
                                <option value="weekly" selected>每周</option>
                                <option value="monthly">每月</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn btn-primary" style="padding: 12px 30px;">
                        <i class="fas fa-save"></i> 保存所有设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看媒体弹窗 -->
    <div class="modal" id="mediaModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">媒体预览</h3>
                <button class="close-btn" onclick="closeModal('mediaModal')">&times;</button>
            </div>
            <div style="text-align: center;">
                <img id="modalImage" src="" alt="预览" style="max-width: 100%; max-height: 400px;">
            </div>
        </div>
    </div>

    <!-- 举报详情弹窗 -->
    <div class="modal" id="reportsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">举报详情</h3>
                <button class="close-btn" onclick="closeModal('reportsModal')">&times;</button>
            </div>
            <div id="reportsContent">
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <strong>举报用户: Reporter123</strong>
                        <span style="color: #64748b;">2024-01-15 09:30</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>举报原因:</strong> 不当内容
                    </div>
                    <div>
                        <strong>详细说明:</strong> 该动态包含不适宜的内容，影响平台环境
                    </div>
                </div>
                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <strong>举报用户: AnotherUser</strong>
                        <span style="color: #64748b;">2024-01-15 11:15</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>举报原因:</strong> 垃圾信息
                    </div>
                    <div>
                        <strong>详细说明:</strong> 疑似广告推广内容
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 移除所有按钮的活动状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + 'Tab').classList.remove('hidden');
            event.target.classList.add('active');
        }

        // 搜索动态
        function searchMoments() {
            const searchTerm = document.getElementById('searchInput').value;
            const status = document.getElementById('statusFilter').value;
            const time = document.getElementById('timeFilter').value;
            const type = document.getElementById('typeFilter').value;
            
            console.log('搜索条件:', { searchTerm, status, time, type });
            
            // 实际的搜索逻辑
            const moments = document.querySelectorAll('.moment-card');
            let visibleCount = 0;
            
            moments.forEach(moment => {
                const username = moment.querySelector('.username').textContent.toLowerCase();
                const content = moment.querySelector('.moment-text').textContent.toLowerCase();
                const statusEl = moment.querySelector('[class*="status-"]');
                const momentStatus = statusEl ? statusEl.className.replace('status-', '') : '';
                
                let show = true;
                
                // 文本搜索
                if (searchTerm && !username.includes(searchTerm.toLowerCase()) && !content.includes(searchTerm.toLowerCase())) {
                    show = false;
                }
                
                // 状态筛选
                if (status && status !== 'all' && momentStatus !== status) {
                    show = false;
                }
                
                if (show) {
                    moment.style.display = 'block';
                    visibleCount++;
                } else {
                    moment.style.display = 'none';
                }
            });
            
            showNotification(`找到 ${visibleCount} 条符合条件的动态`, 'info');
        }

        // 查看动态详情
        function viewMomentDetail(momentId) {
            showMomentDetailModal(momentId);
        }

        // 编辑动态
        function editMoment(momentId) {
            showEditMomentModal(momentId);
        }

        // 删除动态
        function deleteMoment(momentId) {
            if (confirm('确定要删除这条动态吗？此操作不可恢复。')) {
                // 模拟删除
                const momentCard = document.querySelector(`.moment-card[data-id="${momentId}"]`);
                if (momentCard) {
                    momentCard.remove();
                    showNotification('动态已删除', 'success');
                } else {
                    // 如果没有data-id，就找到对应的卡片
                    showNotification('动态删除成功', 'success');
                    // 这里可以添加实际的删除逻辑
                }
            }
        }

        // 审核通过
        function approveMoment(momentId) {
            if (confirm('确定通过这条动态吗？')) {
                // 更新动态状态
                updateMomentStatus(momentId, 'approved', '已通过');
                showNotification('动态审核通过', 'success');
            }
        }

        // 审核拒绝
        function rejectMoment(momentId) {
            const reason = prompt('请输入拒绝原因:');
            if (reason) {
                updateMomentStatus(momentId, 'rejected', '已拒绝');
                showNotification(`动态已拒绝，原因: ${reason}`, 'warning');
            }
        }

        // 编辑后通过
        function editAndApprove(momentId) {
            showEditApproveModal(momentId);
        }

        // 查看举报
        function viewReports(momentId) {
            document.getElementById('reportsModal').classList.add('show');
        }

        // 驳回举报
        function dismissReports(momentId) {
            if (confirm('确定驳回所有举报吗？')) {
                updateMomentStatus(momentId, 'approved', '已通过');
                showNotification('举报已驳回，动态恢复正常', 'success');
            }
        }

        // 确认违规
        function confirmViolation(momentId) {
            if (confirm('确定该动态违规吗？这将删除动态并警告用户。')) {
                updateMomentStatus(momentId, 'violation', '违规');
                showNotification('动态已标记为违规并删除', 'error');
            }
        }
        
        // 动态详情模态框
        function showMomentDetailModal(momentId) {
            const modal = createAdvancedModal('动态详情', `
                <div class="moment-detail">
                    <div class="detail-header">
                        <img src="https://via.placeholder.com/60x60/74b9ff/ffffff?text=U" alt="头像" class="detail-avatar">
                        <div class="detail-user-info">
                            <h4>用户名: TestUser${momentId}</h4>
                            <p>用户ID: ${1000 + momentId} | 等级: VIP</p>
                            <p>发布时间: 2024-01-15 14:30:00</p>
                        </div>
                    </div>
                    
                    <div class="detail-content">
                        <h5>动态内容</h5>
                        <div class="content-text">
                            这是动态 ${momentId} 的完整内容。用户在此分享了生活中的美好时刻，包含了一些图片和文字描述。内容经过系统初步检测，未发现明显违规内容。
                        </div>
                        
                        <div class="content-media">
                            <h6>媒体文件</h6>
                            <div class="media-grid">
                                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=100&h=100&fit=crop" onclick="viewLargeImage(this)" class="media-thumb">
                                <img src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=100&h=100&fit=crop" onclick="viewLargeImage(this)" class="media-thumb">
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-meta">
                        <h5>互动数据</h5>
                        <div class="meta-stats">
                            <div class="stat-item">
                                <i class="fas fa-heart"></i>
                                <span>点赞: 45</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-comment"></i>
                                <span>评论: 12</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-share"></i>
                                <span>分享: 8</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-eye"></i>
                                <span>浏览: 289</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-comments">
                        <h5>最新评论</h5>
                        <div class="comment-list">
                            <div class="comment-item">
                                <img src="https://via.placeholder.com/30x30" class="comment-avatar">
                                <div class="comment-content">
                                    <div class="comment-user">张三</div>
                                    <div class="comment-text">很棒的分享！</div>
                                    <div class="comment-time">2小时前</div>
                                </div>
                            </div>
                            <div class="comment-item">
                                <img src="https://via.placeholder.com/30x30" class="comment-avatar">
                                <div class="comment-content">
                                    <div class="comment-user">李四</div>
                                    <div class="comment-text">同感，风景很美</div>
                                    <div class="comment-time">3小时前</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-system">
                        <h5>系统信息</h5>
                        <div class="system-info">
                            <p><strong>IP地址:</strong> *************</p>
                            <p><strong>设备信息:</strong> iPhone 13 Pro, iOS 15.0</p>
                            <p><strong>发布位置:</strong> 北京市朝阳区</p>
                            <p><strong>审核状态:</strong> 待审核</p>
                            <p><strong>风险评分:</strong> 低风险 (2/10)</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button onclick="downloadMomentData(${momentId})" class="btn btn-info">导出数据</button>
                    <button onclick="editMoment(${momentId})" class="btn btn-warning">编辑</button>
                    <button onclick="approveMoment(${momentId})" class="btn btn-success">通过</button>
                    <button onclick="rejectMoment(${momentId})" class="btn btn-danger">拒绝</button>
                    <button onclick="closeAdvancedModal()" class="btn btn-secondary">关闭</button>
                </div>
            `);
        }
        
        // 编辑动态模态框
        function showEditMomentModal(momentId) {
            const modal = createAdvancedModal('编辑动态', `
                <div class="edit-moment">
                    <div class="form-group">
                        <label>用户信息</label>
                        <div class="user-info-display">
                            <img src="https://via.placeholder.com/40x40" class="user-avatar">
                            <div>
                                <div class="username">TestUser${momentId}</div>
                                <div class="user-id">ID: ${1000 + momentId}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>动态内容</label>
                        <textarea id="editMomentContent" rows="6" class="form-control" placeholder="编辑动态内容...">这是动态 ${momentId} 的原始内容，管理员可以进行编辑和修改。</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>媒体文件</label>
                        <div class="media-edit">
                            <div class="current-media">
                                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=100&h=100&fit=crop" class="media-thumb">
                                <button onclick="removeMedia(this)" class="remove-media">&times;</button>
                            </div>
                            <div class="current-media">
                                <img src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=100&h=100&fit=crop" class="media-thumb">
                                <button onclick="removeMedia(this)" class="remove-media">&times;</button>
                            </div>
                            <div class="add-media">
                                <i class="fas fa-plus"></i>
                                <span>添加媒体</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>标签管理</label>
                        <div class="tag-input">
                            <input type="text" placeholder="添加标签..." class="form-control">
                            <button class="btn btn-sm btn-outline">添加</button>
                        </div>
                        <div class="current-tags">
                            <span class="tag">#旅行 <button onclick="removeTag(this)">&times;</button></span>
                            <span class="tag">#摄影 <button onclick="removeTag(this)">&times;</button></span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>可见性设置</label>
                        <select class="form-control">
                            <option value="public">公开</option>
                            <option value="friends">仅好友可见</option>
                            <option value="private">仅自己可见</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>审核备注</label>
                        <textarea id="adminNote" rows="3" class="form-control" placeholder="管理员审核备注..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button onclick="saveMomentEdit(${momentId})" class="btn btn-primary">保存修改</button>
                    <button onclick="saveMomentEditAndApprove(${momentId})" class="btn btn-success">保存并通过</button>
                    <button onclick="closeAdvancedModal()" class="btn btn-secondary">取消</button>
                </div>
            `);
        }
        
        // 编辑后通过模态框
        function showEditApproveModal(momentId) {
            const modal = createAdvancedModal('编辑并通过审核', `
                <div class="edit-approve">
                    <div class="form-group">
                        <label>快速修改建议</label>
                        <div class="quick-fixes">
                            <button onclick="applySuggestion('remove-sensitive')" class="btn btn-sm btn-outline">移除敏感词</button>
                            <button onclick="applySuggestion('add-warning')" class="btn btn-sm btn-outline">添加警告标签</button>
                            <button onclick="applySuggestion('blur-image')" class="btn btn-sm btn-outline">模糊化图片</button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>修改内容</label>
                        <textarea id="quickEditContent" rows="4" class="form-control">这是一条包含敏感词汇的动态内容，需要人工审核...</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>修改说明</label>
                        <textarea id="modificationNote" rows="2" class="form-control" placeholder="请说明修改的原因和内容..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="notifyUser" checked>
                            <span>通知用户修改内容</span>
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button onclick="executeEditAndApprove(${momentId})" class="btn btn-success">确认修改并通过</button>
                    <button onclick="closeAdvancedModal()" class="btn btn-secondary">取消</button>
                </div>
            `);
        }
        
        // 更新动态状态的辅助函数
        function updateMomentStatus(momentId, status, statusText) {
            // 这里应该发送API请求更新数据库
            console.log(`更新动态 ${momentId} 状态为: ${status}`);
            
            // 更新页面显示
            const statusEl = document.querySelector(`.moment-card:nth-child(${momentId}) .status-${status.replace('approved', 'approved').replace('rejected', 'rejected').replace('violation', 'violation')}`);
            if (statusEl) {
                statusEl.textContent = statusText;
                statusEl.className = `status-${status}`;
            }
        }
        
        // 创建高级模态框
        function createAdvancedModal(title, content) {
            const modal = document.createElement('div');
            modal.className = 'advanced-modal';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.6); z-index: 1000;
                display: flex; align-items: center; justify-content: center;
            `;
            
            modal.innerHTML = `
                <div class="advanced-modal-content" style="
                    background: white; border-radius: 12px; max-width: 800px; 
                    max-height: 90vh; overflow-y: auto; margin: 20px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
                ">
                    <div class="advanced-modal-header" style="
                        padding: 24px; border-bottom: 1px solid #e5e7eb;
                        display: flex; justify-content: space-between; align-items: center;
                    ">
                        <h3 style="margin: 0; color: #1f2937; font-size: 20px; font-weight: 600;">${title}</h3>
                        <button onclick="closeAdvancedModal()" style="
                            background: none; border: none; font-size: 24px; 
                            color: #6b7280; cursor: pointer; padding: 0;
                        ">&times;</button>
                    </div>
                    <div class="advanced-modal-body" style="padding: 30px;">
                        ${content}
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 添加样式
            if (!document.getElementById('advancedModalStyles')) {
                const style = document.createElement('style');
                style.id = 'advancedModalStyles';
                style.textContent = `
                    .moment-detail .detail-header {
                        display: flex; align-items: center; margin-bottom: 20px;
                        padding: 15px; background: #f8fafc; border-radius: 8px;
                    }
                    .detail-avatar { width: 60px; height: 60px; border-radius: 50%; margin-right: 15px; }
                    .detail-user-info h4 { margin: 0 0 5px 0; color: #1f2937; }
                    .detail-user-info p { margin: 2px 0; color: #6b7280; font-size: 14px; }
                    .detail-content { margin-bottom: 20px; }
                    .content-text { 
                        background: #f9fafb; padding: 15px; border-radius: 8px; 
                        line-height: 1.6; margin-bottom: 15px;
                    }
                    .media-grid { display: flex; gap: 10px; }
                    .media-thumb { 
                        width: 100px; height: 100px; object-fit: cover; 
                        border-radius: 8px; cursor: pointer; transition: transform 0.2s;
                    }
                    .media-thumb:hover { transform: scale(1.05); }
                    .meta-stats { 
                        display: grid; grid-template-columns: repeat(4, 1fr); 
                        gap: 15px; margin: 15px 0;
                    }
                    .stat-item { 
                        text-align: center; padding: 10px; 
                        background: #f3f4f6; border-radius: 8px;
                    }
                    .stat-item i { color: #3b82f6; margin-bottom: 5px; }
                    .comment-list { max-height: 200px; overflow-y: auto; }
                    .comment-item { 
                        display: flex; align-items: flex-start; 
                        margin-bottom: 15px; padding-bottom: 15px;
                        border-bottom: 1px solid #e5e7eb;
                    }
                    .comment-avatar { 
                        width: 30px; height: 30px; border-radius: 50%; 
                        margin-right: 10px; flex-shrink: 0;
                    }
                    .comment-user { font-weight: 600; color: #1f2937; margin-bottom: 2px; }
                    .comment-text { color: #4b5563; margin-bottom: 2px; }
                    .comment-time { color: #9ca3af; font-size: 12px; }
                    .system-info p { margin: 8px 0; color: #4b5563; }
                    .system-info strong { color: #1f2937; }
                    .edit-moment .user-info-display {
                        display: flex; align-items: center; 
                        padding: 10px; background: #f8fafc; border-radius: 6px;
                    }
                    .media-edit { 
                        display: flex; gap: 10px; flex-wrap: wrap; 
                        margin-top: 10px;
                    }
                    .current-media { 
                        position: relative; width: 100px; height: 100px;
                    }
                    .remove-media {
                        position: absolute; top: -5px; right: -5px;
                        background: #ef4444; color: white; border: none;
                        width: 20px; height: 20px; border-radius: 50%;
                        cursor: pointer; font-size: 12px;
                    }
                    .add-media {
                        display: flex; flex-direction: column; align-items: center;
                        justify-content: center; width: 100px; height: 100px;
                        border: 2px dashed #d1d5db; border-radius: 8px;
                        cursor: pointer; color: #6b7280;
                    }
                    .tag-input { display: flex; gap: 10px; margin-bottom: 10px; }
                    .current-tags { display: flex; gap: 8px; flex-wrap: wrap; }
                    .tag {
                        background: #3b82f6; color: white; padding: 4px 8px;
                        border-radius: 4px; font-size: 12px; display: flex;
                        align-items: center; gap: 5px;
                    }
                    .tag button {
                        background: none; border: none; color: white;
                        cursor: pointer; font-size: 14px; padding: 0;
                    }
                    .quick-fixes { display: flex; gap: 10px; margin-bottom: 15px; }
                    .checkbox-label { 
                        display: flex; align-items: center; gap: 8px; 
                        cursor: pointer; color: #4b5563;
                    }
                `;
                document.head.appendChild(style);
            }
            
            return modal;
        }
        
        function closeAdvancedModal() {
            const modal = document.querySelector('.advanced-modal');
            if (modal) modal.remove();
        }
        
        // 其他功能函数
        function downloadMomentData(momentId) {
            alert(`正在导出动态 ${momentId} 的完整数据...`);
        }
        
        function saveMomentEdit(momentId) {
            const content = document.getElementById('editMomentContent').value;
            const note = document.getElementById('adminNote').value;
            
            if (confirm('确定保存修改吗？')) {
                showNotification('动态修改已保存', 'success');
                closeAdvancedModal();
            }
        }
        
        function saveMomentEditAndApprove(momentId) {
            const content = document.getElementById('editMomentContent').value;
            const note = document.getElementById('adminNote').value;
            
            if (confirm('确定保存修改并通过审核吗？')) {
                updateMomentStatus(momentId, 'approved', '已通过');
                showNotification('动态修改已保存并通过审核', 'success');
                closeAdvancedModal();
            }
        }
        
        function executeEditAndApprove(momentId) {
            const content = document.getElementById('quickEditContent').value;
            const note = document.getElementById('modificationNote').value;
            const notify = document.getElementById('notifyUser').checked;
            
            if (confirm('确定执行修改并通过审核吗？')) {
                updateMomentStatus(momentId, 'approved', '已通过');
                showNotification('动态已修改并通过审核' + (notify ? '，已通知用户' : ''), 'success');
                closeAdvancedModal();
            }
        }
        
        function applySuggestion(type) {
            const textarea = document.getElementById('quickEditContent');
            let content = textarea.value;
            
            switch(type) {
                case 'remove-sensitive':
                    content = content.replace(/敏感词汇|不当内容/g, '***');
                    break;
                case 'add-warning':
                    content = '[内容经过编辑] ' + content;
                    break;
                case 'blur-image':
                    showNotification('图片已设为模糊化处理', 'info');
                    break;
            }
            
            textarea.value = content;
        }
        
        function removeMedia(button) {
            button.parentElement.remove();
        }
        
        function removeTag(button) {
            button.parentElement.remove();
        }
        
        function viewLargeImage(img) {
            const modal = createAdvancedModal('图片预览', `
                <div style="text-align: center;">
                    <img src="${img.src.replace('w=100&h=100', 'w=800&h=600')}" 
                         style="max-width: 100%; max-height: 70vh; border-radius: 8px;">
                </div>
            `);
        }
        
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                padding: 15px 20px; border-radius: 8px; color: white;
                font-size: 14px; max-width: 300px;
                ${type === 'success' ? 'background: #10b981;' : 
                  type === 'error' ? 'background: #ef4444;' : 
                  type === 'warning' ? 'background: #f59e0b;' : 'background: #3b82f6;'}
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }
        
        // 查看媒体
        function viewMedia(img) {
            document.getElementById('modalImage').src = img.src.replace('60x60', '600x600');
            document.getElementById('mediaModal').classList.add('show');
        }

        // 关闭弹窗
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // 点击背景关闭弹窗
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('show');
                }
            });
        });

        // 筛选器变化监听
        document.getElementById('statusFilter').addEventListener('change', function() {
            console.log('状态筛选:', this.value);
            searchMoments(); // 自动触发搜索
        });

        document.getElementById('timeFilter').addEventListener('change', function() {
            console.log('时间筛选:', this.value);
            searchMoments(); // 自动触发搜索
        });

        document.getElementById('typeFilter').addEventListener('change', function() {
            console.log('类型筛选:', this.value);
            searchMoments(); // 自动触发搜索
        });

        // 搜索输入监听
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchMoments();
            }
        });
        
        // 图表绘制功能
        function initAnalyticsCharts() {
            // 动态发布趋势图
            drawPublishTrendChart();
            // 用户活跃度图
            drawUserActivityChart();
            // 内容类型分布图
            drawContentTypeChart();
            // 审核效率统计图
            drawReviewEfficiencyChart();
        }
        
        function drawPublishTrendChart() {
            const canvas = document.getElementById('publishTrendChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            
            // 清空画布
            ctx.clearRect(0, 0, width, height);
            
            // 模拟数据 (7天的发布趋势)
            const data = [120, 135, 98, 156, 142, 168, 156];
            const labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
            const maxValue = Math.max(...data);
            
            // 设置样式
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 3;
            ctx.fillStyle = 'rgba(59, 130, 246, 0.1)';
            
            // 计算坐标
            const padding = 40;
            const chartWidth = width - padding * 2;
            const chartHeight = height - padding * 2;
            
            // 绘制网格线
            ctx.strokeStyle = '#e5e7eb';
            ctx.lineWidth = 1;
            for (let i = 0; i <= 5; i++) {
                const y = padding + (chartHeight / 5) * i;
                ctx.beginPath();
                ctx.moveTo(padding, y);
                ctx.lineTo(width - padding, y);
                ctx.stroke();
            }
            
            // 绘制趋势线
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 3;
            ctx.beginPath();
            
            const points = [];
            data.forEach((value, index) => {
                const x = padding + (chartWidth / (data.length - 1)) * index;
                const y = height - padding - (value / maxValue) * chartHeight;
                points.push({ x, y });
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
            
            // 绘制填充区域
            ctx.fillStyle = 'rgba(59, 130, 246, 0.1)';
            ctx.beginPath();
            ctx.moveTo(points[0].x, height - padding);
            points.forEach(point => {
                ctx.lineTo(point.x, point.y);
            });
            ctx.lineTo(points[points.length - 1].x, height - padding);
            ctx.closePath();
            ctx.fill();
            
            // 绘制数据点
            ctx.fillStyle = '#3b82f6';
            points.forEach((point, index) => {
                ctx.beginPath();
                ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
                ctx.fill();
                
                // 绘制标签
                ctx.fillStyle = '#6b7280';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(labels[index], point.x, height - padding + 20);
                ctx.fillStyle = '#3b82f6';
            });
        }
        
        function drawUserActivityChart() {
            const canvas = document.getElementById('userActivityChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            
            ctx.clearRect(0, 0, width, height);
            
            // 模拟数据 (24小时活跃度)
            const data = [45, 32, 28, 35, 52, 68, 89, 124, 156, 178, 165, 142, 138, 145, 156, 162, 158, 149, 134, 118, 95, 76, 62, 51];
            const maxValue = Math.max(...data);
            
            const padding = 40;
            const chartWidth = width - padding * 2;
            const chartHeight = height - padding * 2;
            const barWidth = chartWidth / data.length;
            
            // 绘制柱状图
            data.forEach((value, index) => {
                const barHeight = (value / maxValue) * chartHeight;
                const x = padding + index * barWidth;
                const y = height - padding - barHeight;
                
                // 设置渐变色
                const gradient = ctx.createLinearGradient(0, y, 0, height - padding);
                gradient.addColorStop(0, '#10b981');
                gradient.addColorStop(1, 'rgba(16, 185, 129, 0.3)');
                
                ctx.fillStyle = gradient;
                ctx.fillRect(x + 1, y, barWidth - 2, barHeight);
                
                // 标签 (每4小时显示一次)
                if (index % 4 === 0) {
                    ctx.fillStyle = '#6b7280';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`${index}:00`, x + barWidth/2, height - padding + 15);
                }
            });
        }
        
        function drawContentTypeChart() {
            const canvas = document.getElementById('contentTypeChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            
            ctx.clearRect(0, 0, width, height);
            
            // 饼图数据
            const data = [
                { label: '图文动态', value: 45, color: '#3b82f6' },
                { label: '纯文字', value: 30, color: '#10b981' },
                { label: '视频', value: 15, color: '#f59e0b' },
                { label: '其他', value: 10, color: '#ef4444' }
            ];
            
            const centerX = width / 2;
            const centerY = height / 2;
            const radius = Math.min(width, height) / 2 - 40;
            
            let currentAngle = -Math.PI / 2; // 从顶部开始
            
            data.forEach(item => {
                const sliceAngle = (item.value / 100) * 2 * Math.PI;
                
                // 绘制扇形
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                ctx.closePath();
                ctx.fillStyle = item.color;
                ctx.fill();
                
                // 绘制边框
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                currentAngle += sliceAngle;
            });
            
            // 绘制中心圆
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius * 0.4, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();
            ctx.strokeStyle = '#e5e7eb';
            ctx.lineWidth = 1;
            ctx.stroke();
        }
        
        function drawReviewEfficiencyChart() {
            const canvas = document.getElementById('reviewEfficiencyChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            
            ctx.clearRect(0, 0, width, height);
            
            // 模拟数据 (最近7天的审核效率)
            const approved = [89, 92, 85, 94, 87, 91, 88];
            const rejected = [8, 6, 12, 4, 9, 7, 8];
            const pending = [3, 2, 3, 2, 4, 2, 4];
            const labels = ['1/9', '1/10', '1/11', '1/12', '1/13', '1/14', '1/15'];
            
            const padding = 40;
            const chartWidth = width - padding * 2;
            const chartHeight = height - padding * 2;
            const barWidth = chartWidth / labels.length / 3;
            
            labels.forEach((label, index) => {
                const x = padding + index * (chartWidth / labels.length);
                
                // 绘制通过的柱子
                const approvedHeight = (approved[index] / 100) * chartHeight;
                ctx.fillStyle = '#10b981';
                ctx.fillRect(x, height - padding - approvedHeight, barWidth, approvedHeight);
                
                // 绘制拒绝的柱子
                const rejectedHeight = (rejected[index] / 100) * chartHeight;
                ctx.fillStyle = '#ef4444';
                ctx.fillRect(x + barWidth, height - padding - rejectedHeight, barWidth, rejectedHeight);
                
                // 绘制待审核的柱子
                const pendingHeight = (pending[index] / 100) * chartHeight;
                ctx.fillStyle = '#f59e0b';
                ctx.fillRect(x + barWidth * 2, height - padding - pendingHeight, barWidth, pendingHeight);
                
                // 绘制标签
                ctx.fillStyle = '#6b7280';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(label, x + barWidth * 1.5, height - padding + 20);
            });
        }
        
        // 导出分析报告
        function exportAnalyticsReport() {
            const reportData = {
                generatedAt: new Date().toISOString(),
                summary: {
                    totalMoments: 2841,
                    pendingReview: 23,
                    approvedToday: 156,
                    rejectedToday: 12,
                    reportedContent: 8
                },
                trends: {
                    publishTrend: [120, 135, 98, 156, 142, 168, 156],
                    userActivity: 'Increasing',
                    contentTypes: {
                        imageText: '45%',
                        textOnly: '30%',
                        video: '15%',
                        other: '10%'
                    }
                },
                performance: {
                    avgReviewTime: '12分钟',
                    approvalRate: '87.3%',
                    autoReviewRate: '65.8%',
                    systemStability: '99.2%'
                }
            };
            
            // 创建下载链接
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(reportData, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", `moments_analytics_report_${new Date().toISOString().split('T')[0]}.json`);
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
            
            showNotification('分析报告已导出', 'success');
        }
        
        // 刷新分析数据
        function refreshAnalytics() {
            showNotification('正在刷新数据...', 'info');
            
            // 模拟数据刷新
            setTimeout(() => {
                initAnalyticsCharts();
                
                // 更新实时统计数据
                const realTimeItems = document.querySelectorAll('.real-time-item .stat-number');
                const newNumbers = ['25', '6', '178', '99.5%'];
                realTimeItems.forEach((item, index) => {
                    if (newNumbers[index]) {
                        item.textContent = newNumbers[index];
                    }
                });
                
                showNotification('数据已刷新', 'success');
            }, 1500);
        }
        
        // 页面加载时初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            console.log('朋友圈管理页面加载完成');
            
            // 监听标签切换，当切换到分析标签时初始化图表
            const analyticsTab = document.querySelector('[onclick="switchTab(\'analytics\')"]');
            if (analyticsTab) {
                analyticsTab.addEventListener('click', function() {
                    setTimeout(() => {
                        initAnalyticsCharts();
                    }, 100);
                });
            }
        });
    </script>
</body>
</html> 