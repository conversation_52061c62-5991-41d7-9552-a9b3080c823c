<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知系统管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .notification-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .notification-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .overview-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .overview-title {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }
        
        .overview-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .overview-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .overview-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .change-positive {
            color: #10b981;
        }
        
        .change-negative {
            color: #ef4444;
        }
        
        .notification-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .action-panel {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .panel-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .panel-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .panel-description {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .panel-controls {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .control-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            flex: 1;
            min-width: 120px;
        }
        
        .control-btn:hover {
            background: #f1f5f9;
        }
        
        .control-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .control-btn.success {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }
        
        .control-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .notification-history {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 32px;
        }
        
        .history-item {
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .history-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }
        
        .history-content {
            flex: 1;
            min-width: 0;
        }
        
        .history-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .history-description {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 4px;
        }
        
        .history-meta {
            font-size: 12px;
            color: #94a3b8;
            display: flex;
            gap: 16px;
        }
        
        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            min-width: 60px;
            text-align: center;
        }
        
        .status-sent {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .templates-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .template-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .template-preview {
            padding: 16px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .template-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .template-content {
            font-size: 13px;
            color: #64748b;
            line-height: 1.4;
        }
        
        .template-actions {
            padding: 16px;
            display: flex;
            gap: 8px;
        }
        
        .template-btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            flex: 1;
        }
        
        .template-btn:hover {
            background: #f1f5f9;
        }
        
        .template-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .statistics-charts {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }
        
        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        @media (max-width: 768px) {
            .notification-page {
                padding: 16px;
            }
            
            .notification-overview {
                grid-template-columns: 1fr;
            }
            
            .notification-actions {
                grid-template-columns: 1fr;
            }
            
            .templates-section {
                grid-template-columns: 1fr;
            }
            
            .statistics-charts {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="notification-page">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-bell"></i>
                通知系统管理
            </h1>
            <div class="page-actions">
                <button class="action-btn" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    刷新数据
                </button>
                <button class="action-btn primary" onclick="sendNotification()">
                    <i class="fas fa-paper-plane"></i>
                    发送通知
                </button>
            </div>
        </div>

        <!-- 通知概览 -->
        <div class="notification-overview">
            <div class="overview-card">
                <div class="overview-header">
                    <div class="overview-title">今日发送</div>
                    <div class="overview-icon" style="background: #dbeafe; color: #1d4ed8;">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                </div>
                <div class="overview-value">8,542</div>
                <div class="overview-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    较昨日增长 12%
                </div>
            </div>

            <div class="overview-card">
                <div class="overview-header">
                    <div class="overview-title">送达率</div>
                    <div class="overview-icon" style="background: #dcfce7; color: #166534;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="overview-value">96.8%</div>
                <div class="overview-change change-positive">
                    <i class="fas fa-arrow-up"></i>
                    提升 0.5%
                </div>
            </div>

            <div class="overview-card">
                <div class="overview-header">
                    <div class="overview-title">点击率</div>
                    <div class="overview-icon" style="background: #fef3c7; color: #92400e;">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                </div>
                <div class="overview-value">28.5%</div>
                <div class="overview-change change-negative">
                    <i class="fas fa-arrow-down"></i>
                    下降 2.3%
                </div>
            </div>

            <div class="overview-card">
                <div class="overview-header">
                    <div class="overview-title">活跃模板</div>
                    <div class="overview-icon" style="background: #f3e8ff; color: #7c3aed;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                </div>
                <div class="overview-value">24</div>
                <div class="overview-change">
                    <i class="fas fa-calendar"></i>
                    本月使用
                </div>
            </div>
        </div>

        <!-- 通知操作面板 -->
        <div class="notification-actions">
            <div class="action-panel">
                <div class="panel-header">
                    <div class="panel-icon" style="background: #dbeafe; color: #1d4ed8;">
                        <i class="fas fa-broadcast-tower"></i>
                    </div>
                    <div class="panel-title">群发通知</div>
                </div>
                <div class="panel-description">
                    向所有用户或特定用户群体发送重要通知消息
                </div>
                <div class="panel-controls">
                    <div class="control-group">
                        <button class="control-btn primary">立即发送</button>
                        <button class="control-btn">定时发送</button>
                    </div>
                    <div class="control-group">
                        <button class="control-btn">选择用户</button>
                        <button class="control-btn">选择模板</button>
                    </div>
                </div>
            </div>

            <div class="action-panel">
                <div class="panel-header">
                    <div class="panel-icon" style="background: #dcfce7; color: #166534;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="panel-title">推送设置</div>
                </div>
                <div class="panel-description">
                    配置APP推送、短信、邮件等通知渠道的参数
                </div>
                <div class="panel-controls">
                    <div class="control-group">
                        <button class="control-btn">APP推送</button>
                        <button class="control-btn">短信配置</button>
                    </div>
                    <div class="control-group">
                        <button class="control-btn">邮件设置</button>
                        <button class="control-btn success">保存配置</button>
                    </div>
                </div>
            </div>

            <div class="action-panel">
                <div class="panel-header">
                    <div class="panel-icon" style="background: #fef3c7; color: #92400e;">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="panel-title">自动化规则</div>
                </div>
                <div class="panel-description">
                    设置基于用户行为的自动通知触发规则
                </div>
                <div class="panel-controls">
                    <div class="control-group">
                        <button class="control-btn">注册欢迎</button>
                        <button class="control-btn">订单通知</button>
                    </div>
                    <div class="control-group">
                        <button class="control-btn">活动提醒</button>
                        <button class="control-btn primary">新增规则</button>
                    </div>
                </div>
            </div>

            <div class="action-panel">
                <div class="panel-header">
                    <div class="panel-icon" style="background: #f3e8ff; color: #7c3aed;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="panel-title">效果分析</div>
                </div>
                <div class="panel-description">
                    分析通知发送效果，优化通知策略和内容
                </div>
                <div class="panel-controls">
                    <div class="control-group">
                        <button class="control-btn">发送报告</button>
                        <button class="control-btn">用户反馈</button>
                    </div>
                    <div class="control-group">
                        <button class="control-btn">A/B测试</button>
                        <button class="control-btn primary">查看详情</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发送历史 -->
        <div class="notification-history">
            <div class="content-header">
                <h2 class="content-title">最近发送记录</h2>
                <div class="search-filters">
                    <select class="filter-select">
                        <option>全部状态</option>
                        <option>已发送</option>
                        <option>发送中</option>
                        <option>发送失败</option>
                    </select>
                </div>
            </div>
            
            <div class="history-item">
                <div class="history-icon" style="background: #dcfce7; color: #166534;">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="history-content">
                    <div class="history-title">春季大促销活动通知</div>
                    <div class="history-description">全场商品5-8折优惠，活动即将开始！</div>
                    <div class="history-meta">
                        <span>发送时间: 2024-03-15 14:30</span>
                        <span>接收用户: 8,542人</span>
                        <span>送达率: 96.8%</span>
                    </div>
                </div>
                <div class="history-status status-sent">已发送</div>
            </div>

            <div class="history-item">
                <div class="history-icon" style="background: #fef3c7; color: #92400e;">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="history-content">
                    <div class="history-title">订单确认通知</div>
                    <div class="history-description">您的订单已确认，我们将尽快为您处理</div>
                    <div class="history-meta">
                        <span>发送时间: 2024-03-15 14:25</span>
                        <span>接收用户: 156人</span>
                        <span>预计送达: 2分钟内</span>
                    </div>
                </div>
                <div class="history-status status-pending">发送中</div>
            </div>

            <div class="history-item">
                <div class="history-icon" style="background: #dbeafe; color: #1d4ed8;">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="history-content">
                    <div class="history-title">新用户欢迎消息</div>
                    <div class="history-description">欢迎加入TeleShop，享受优质购物体验！</div>
                    <div class="history-meta">
                        <span>发送时间: 2024-03-15 14:20</span>
                        <span>接收用户: 45人</span>
                        <span>送达率: 100%</span>
                    </div>
                </div>
                <div class="history-status status-sent">已发送</div>
            </div>

            <div class="history-item">
                <div class="history-icon" style="background: #fee2e2; color: #991b1b;">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="history-content">
                    <div class="history-title">系统维护通知</div>
                    <div class="history-description">因短信通道异常，部分消息发送失败</div>
                    <div class="history-meta">
                        <span>发送时间: 2024-03-15 14:15</span>
                        <span>接收用户: 1,200人</span>
                        <span>失败率: 15.2%</span>
                    </div>
                </div>
                <div class="history-status status-failed">发送失败</div>
            </div>
        </div>

        <!-- 消息模板 -->
        <div class="content-header">
            <h2 class="content-title">消息模板</h2>
            <button class="action-btn primary" onclick="createTemplate()">
                <i class="fas fa-plus"></i>
                新建模板
            </button>
        </div>
        
        <div class="templates-section">
            <div class="template-card">
                <div class="template-preview">
                    <div class="template-title">订单确认通知</div>
                    <div class="template-content">
                        亲爱的用户，您的订单 #{order_id} 已确认，我们将在24小时内为您发货。感谢您的信任！
                    </div>
                </div>
                <div class="template-actions">
                    <button class="template-btn primary">使用模板</button>
                    <button class="template-btn">编辑</button>
                    <button class="template-btn">预览</button>
                </div>
            </div>

            <div class="template-card">
                <div class="template-preview">
                    <div class="template-title">促销活动提醒</div>
                    <div class="template-content">
                        {user_name}，您关注的商品正在促销！限时优惠{discount}折，仅剩{time_left}小时，立即购买享受优惠！
                    </div>
                </div>
                <div class="template-actions">
                    <button class="template-btn primary">使用模板</button>
                    <button class="template-btn">编辑</button>
                    <button class="template-btn">预览</button>
                </div>
            </div>

            <div class="template-card">
                <div class="template-preview">
                    <div class="template-title">新用户欢迎</div>
                    <div class="template-content">
                        欢迎加入TeleShop！注册即送{coupon_amount}元优惠券，首单更享受免邮费服务。开启您的购物之旅！
                    </div>
                </div>
                <div class="template-actions">
                    <button class="template-btn primary">使用模板</button>
                    <button class="template-btn">编辑</button>
                    <button class="template-btn">预览</button>
                </div>
            </div>

            <div class="template-card">
                <div class="template-preview">
                    <div class="template-title">物流更新通知</div>
                    <div class="template-content">
                        您的包裹已从{location}发出，快递单号：{tracking_number}，预计{delivery_date}送达。
                    </div>
                </div>
                <div class="template-actions">
                    <button class="template-btn primary">使用模板</button>
                    <button class="template-btn">编辑</button>
                    <button class="template-btn">预览</button>
                </div>
            </div>

            <div class="template-card">
                <div class="template-preview">
                    <div class="template-title">会员升级通知</div>
                    <div class="template-content">
                        恭喜您升级为{vip_level}会员！专享{discount}折优惠、免邮费、优先客服等特权已生效。
                    </div>
                </div>
                <div class="template-actions">
                    <button class="template-btn primary">使用模板</button>
                    <button class="template-btn">编辑</button>
                    <button class="template-btn">预览</button>
                </div>
            </div>

            <div class="template-card">
                <div class="template-preview">
                    <div class="template-title">系统维护通知</div>
                    <div class="template-content">
                        系统将于{maintenance_date}进行维护升级，服务暂停约{duration}小时。给您带来的不便敬请谅解。
                    </div>
                </div>
                <div class="template-actions">
                    <button class="template-btn primary">使用模板</button>
                    <button class="template-btn">编辑</button>
                    <button class="template-btn">预览</button>
                </div>
            </div>
        </div>

        <!-- 统计图表 -->
        <div class="statistics-charts">
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">发送量趋势</div>
                    <div class="chart-filters">
                        <button class="filter-btn active" onclick="setTimeRange('7d')">7天</button>
                        <button class="filter-btn" onclick="setTimeRange('30d')">30天</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="sendTrendChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">渠道分布</div>
                </div>
                <div class="chart-container">
                    <canvas id="channelChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        function refreshData() {
            console.log('刷新通知数据...');
        }

        function sendNotification() {
            console.log('发送新通知...');
        }

        function createTemplate() {
            console.log('创建新模板...');
        }

        function setTimeRange(range) {
            const buttons = document.querySelectorAll('.filter-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            console.log('设置时间范围:', range);
        }

        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 发送量趋势图
            const sendTrendCtx = document.getElementById('sendTrendChart').getContext('2d');
            new Chart(sendTrendCtx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [{
                        label: '发送量',
                        data: [6500, 7200, 8100, 8500, 9200, 8800, 7600],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: '送达量',
                        data: [6240, 6890, 7850, 8230, 8904, 8514, 7364],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 渠道分布图
            const channelCtx = document.getElementById('channelChart').getContext('2d');
            new Chart(channelCtx, {
                type: 'doughnut',
                data: {
                    labels: ['APP推送', '短信', '邮件', '站内信'],
                    datasets: [{
                        data: [45, 25, 20, 10],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#8b5cf6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        });
    </script>
</body>
</html> 