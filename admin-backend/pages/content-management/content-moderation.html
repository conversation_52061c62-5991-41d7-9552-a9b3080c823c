<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容审核系统 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .moderation-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color, #667eea);
        }

        .stat-card.pending::before { background: #f59e0b; }
        .stat-card.approved::before { background: #10b981; }
        .stat-card.rejected::before { background: #ef4444; }
        .stat-card.auto::before { background: #8b5cf6; }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
        }

        .stat-card.pending .stat-icon { background: #f59e0b; }
        .stat-card.approved .stat-icon { background: #10b981; }
        .stat-card.rejected .stat-icon { background: #ef4444; }
        .stat-card.auto .stat-icon { background: #8b5cf6; }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
        }

        .content-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-button {
            flex: 1;
            padding: 12px 20px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            color: #64748b;
            transition: all 0.3s;
            position: relative;
        }

        .tab-button.active {
            background: #667eea;
            color: white;
        }

        .tab-badge {
            position: absolute;
            top: -5px;
            right: 5px;
            background: #ef4444;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
        }

        .moderation-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .content-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .filter-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }

        .content-list {
            padding: 20px;
        }

        .content-item {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.3s;
        }

        .content-item:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .content-item.pending {
            border-left: 4px solid #f59e0b;
        }

        .content-item.approved {
            border-left: 4px solid #10b981;
        }

        .content-item.rejected {
            border-left: 4px solid #ef4444;
        }

        .item-header {
            padding: 16px;
            background: #f8fafc;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .item-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-info h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .user-info p {
            margin: 0;
            font-size: 12px;
            color: #64748b;
        }

        .content-type {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-text {
            background: #dbeafe;
            color: #1e40af;
        }

        .type-image {
            background: #dcfce7;
            color: #166534;
        }

        .type-video {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-approved {
            background: #dcfce7;
            color: #166534;
        }

        .status-rejected {
            background: #fecaca;
            color: #991b1b;
        }

        .item-content {
            padding: 16px;
        }

        .content-text {
            color: #374151;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .content-media {
            display: flex;
            gap: 10px;
            margin-bottom: 12px;
        }

        .media-thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover;
            cursor: pointer;
        }

        .risk-indicators {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }

        .risk-tag {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
        }

        .risk-high {
            background: #fecaca;
            color: #991b1b;
        }

        .risk-medium {
            background: #fef3c7;
            color: #92400e;
        }

        .risk-low {
            background: #e0e7ff;
            color: #3730a3;
        }

        .item-actions {
            display: flex;
            gap: 10px;
            padding: 16px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-approve {
            background: #10b981;
            color: white;
        }

        .btn-approve:hover {
            background: #059669;
        }

        .btn-reject {
            background: #ef4444;
            color: white;
        }

        .btn-reject:hover {
            background: #dc2626;
        }

        .btn-detail {
            background: #667eea;
            color: white;
        }

        .btn-detail:hover {
            background: #5a67d8;
        }

        .sensitive-words-panel {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .panel-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .add-word-form {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            gap: 10px;
        }

        .word-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
        }

        .words-list {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
        }

        .word-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }

        .word-text {
            font-size: 14px;
            color: #374151;
        }

        .delete-word {
            color: #ef4444;
            cursor: pointer;
            font-size: 12px;
        }

        .auto-review-settings {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
            padding: 20px;
        }

        .setting-group {
            margin-bottom: 20px;
        }

        .setting-label {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
            display: block;
        }

        .setting-description {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 12px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #667eea;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .threshold-slider {
            width: 100%;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .moderation-dashboard {
                grid-template-columns: 1fr;
            }

            .filter-controls {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .item-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .item-actions {
                flex-direction: column;
            }

            .words-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>内容审核系统</h1>
            <p>智能审核与人工审核相结合，确保平台内容安全</p>
        </div>

        <!-- 审核统计 -->
        <div class="moderation-dashboard">
            <div class="stat-card pending">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number">156</div>
                <div class="stat-label">待审核</div>
            </div>

            <div class="stat-card approved">
                <div class="stat-icon">
                    <i class="fas fa-check"></i>
                </div>
                <div class="stat-number">2,847</div>
                <div class="stat-label">今日通过</div>
            </div>

            <div class="stat-card rejected">
                <div class="stat-icon">
                    <i class="fas fa-times"></i>
                </div>
                <div class="stat-number">89</div>
                <div class="stat-label">今日拒绝</div>
            </div>

            <div class="stat-card auto">
                <div class="stat-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="stat-number">92%</div>
                <div class="stat-label">自动审核率</div>
            </div>
        </div>

        <!-- 内容标签页 -->
        <div class="content-tabs">
            <button class="tab-button active" data-tab="pending">
                待审核内容
                <span class="tab-badge">156</span>
            </button>
            <button class="tab-button" data-tab="approved">已通过</button>
            <button class="tab-button" data-tab="rejected">已拒绝</button>
            <button class="tab-button" data-tab="sensitive">敏感词管理</button>
            <button class="tab-button" data-tab="settings">审核设置</button>
        </div>

        <!-- 待审核内容 -->
        <div id="pending-content" class="moderation-content">
            <div class="content-header">
                <h3 class="content-title">待审核内容</h3>
                <div class="filter-controls">
                    <select class="filter-select">
                        <option>全部类型</option>
                        <option>文字动态</option>
                        <option>图片动态</option>
                        <option>视频动态</option>
                        <option>商品评论</option>
                    </select>
                    <select class="filter-select">
                        <option>风险等级</option>
                        <option>高风险</option>
                        <option>中风险</option>
                        <option>低风险</option>
                    </select>
                </div>
            </div>

            <div class="content-list">
                <!-- 待审核项目1 -->
                <div class="content-item pending">
                    <div class="item-header">
                        <div class="item-meta">
                            <img src="https://via.placeholder.com/40x40/667eea/ffffff?text=U1" alt="用户头像" class="user-avatar">
                            <div class="user-info">
                                <h4>张小明</h4>
                                <p>2024-01-15 14:30:25</p>
                            </div>
                            <span class="content-type type-text">文字动态</span>
                        </div>
                        <span class="status-badge status-pending">待审核</span>
                    </div>

                    <div class="item-content">
                        <div class="content-text">
                            今天买到了超级棒的iPhone 15！价格真的很优惠，推荐给大家。店家服务也很好，物流很快。
                        </div>

                        <div class="risk-indicators">
                            <span class="risk-tag risk-low">商品推广</span>
                            <span class="risk-tag risk-medium">包含联系方式</span>
                        </div>
                    </div>

                    <div class="item-actions">
                        <button class="action-btn btn-approve" onclick="approveContent(1)">
                            <i class="fas fa-check"></i> 通过
                        </button>
                        <button class="action-btn btn-reject" onclick="rejectContent(1)">
                            <i class="fas fa-times"></i> 拒绝
                        </button>
                        <button class="action-btn btn-detail" onclick="viewDetail(1)">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </div>
                </div>

                <!-- 待审核项目2 -->
                <div class="content-item pending">
                    <div class="item-header">
                        <div class="item-meta">
                            <img src="https://via.placeholder.com/40x40/764ba2/ffffff?text=U2" alt="用户头像" class="user-avatar">
                            <div class="user-info">
                                <h4>李小红</h4>
                                <p>2024-01-15 14:25:18</p>
                            </div>
                            <span class="content-type type-image">图片动态</span>
                        </div>
                        <span class="status-badge status-pending">待审核</span>
                    </div>

                    <div class="item-content">
                        <div class="content-text">
                            分享一下我的新购物体验，真的太棒了！
                        </div>

                        <div class="content-media">
                            <img src="https://via.placeholder.com/80x80/ff6b6b/ffffff?text=IMG1" alt="图片1" class="media-thumbnail">
                            <img src="https://via.placeholder.com/80x80/4ecdc4/ffffff?text=IMG2" alt="图片2" class="media-thumbnail">
                            <img src="https://via.placeholder.com/80x80/45b7d1/ffffff?text=IMG3" alt="图片3" class="media-thumbnail">
                        </div>

                        <div class="risk-indicators">
                            <span class="risk-tag risk-high">疑似广告</span>
                            <span class="risk-tag risk-medium">图片质量检测</span>
                        </div>
                    </div>

                    <div class="item-actions">
                        <button class="action-btn btn-approve" onclick="approveContent(2)">
                            <i class="fas fa-check"></i> 通过
                        </button>
                        <button class="action-btn btn-reject" onclick="rejectContent(2)">
                            <i class="fas fa-times"></i> 拒绝
                        </button>
                        <button class="action-btn btn-detail" onclick="viewDetail(2)">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </div>
                </div>

                <!-- 待审核项目3 -->
                <div class="content-item pending">
                    <div class="item-header">
                        <div class="item-meta">
                            <img src="https://via.placeholder.com/40x40/2ed573/ffffff?text=U3" alt="用户头像" class="user-avatar">
                            <div class="user-info">
                                <h4>王大力</h4>
                                <p>2024-01-15 14:20:45</p>
                            </div>
                            <span class="content-type type-video">视频动态</span>
                        </div>
                        <span class="status-badge status-pending">待审核</span>
                    </div>

                    <div class="item-content">
                        <div class="content-text">
                            开箱视频来啦！这次买的MacBook真的太香了，性能爆表！
                        </div>

                        <div class="content-media">
                            <img src="https://via.placeholder.com/80x80/ff9ff3/ffffff?text=VIDEO" alt="视频缩略图" class="media-thumbnail">
                        </div>

                        <div class="risk-indicators">
                            <span class="risk-tag risk-low">开箱内容</span>
                            <span class="risk-tag risk-low">商品评测</span>
                        </div>
                    </div>

                    <div class="item-actions">
                        <button class="action-btn btn-approve" onclick="approveContent(3)">
                            <i class="fas fa-check"></i> 通过
                        </button>
                        <button class="action-btn btn-reject" onclick="rejectContent(3)">
                            <i class="fas fa-times"></i> 拒绝
                        </button>
                        <button class="action-btn btn-detail" onclick="viewDetail(3)">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 敏感词管理 -->
        <div id="sensitive-content" class="sensitive-words-panel" style="display: none;">
            <div class="panel-header">
                <h3 class="content-title">敏感词管理</h3>
                <span class="status-badge status-approved">共计 1,247 个敏感词</span>
            </div>

            <div class="add-word-form">
                <input type="text" class="word-input" placeholder="输入敏感词..." id="newSensitiveWord">
                <button class="action-btn btn-approve" onclick="addSensitiveWord()">
                    <i class="fas fa-plus"></i> 添加
                </button>
            </div>

            <div class="words-list">
                <div class="word-item">
                    <span class="word-text">违法</span>
                    <span class="delete-word" onclick="deleteSensitiveWord(this)">
                        <i class="fas fa-times"></i>
                    </span>
                </div>
                <div class="word-item">
                    <span class="word-text">欺诈</span>
                    <span class="delete-word" onclick="deleteSensitiveWord(this)">
                        <i class="fas fa-times"></i>
                    </span>
                </div>
                <div class="word-item">
                    <span class="word-text">虚假宣传</span>
                    <span class="delete-word" onclick="deleteSensitiveWord(this)">
                        <i class="fas fa-times"></i>
                    </span>
                </div>
                <div class="word-item">
                    <span class="word-text">恶意刷单</span>
                    <span class="delete-word" onclick="deleteSensitiveWord(this)">
                        <i class="fas fa-times"></i>
                    </span>
                </div>
                <div class="word-item">
                    <span class="word-text">色情</span>
                    <span class="delete-word" onclick="deleteSensitiveWord(this)">
                        <i class="fas fa-times"></i>
                    </span>
                </div>
                <div class="word-item">
                    <span class="word-text">赌博</span>
                    <span class="delete-word" onclick="deleteSensitiveWord(this)">
                        <i class="fas fa-times"></i>
                    </span>
                </div>
            </div>
        </div>

        <!-- 审核设置 -->
        <div id="settings-content" class="auto-review-settings" style="display: none;">
            <h3 class="content-title">自动审核设置</h3>

            <div class="setting-group">
                <label class="setting-label">启用自动审核</label>
                <div class="setting-description">开启后，系统将自动审核低风险内容</div>
                <label class="toggle-switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-group">
                <label class="setting-label">敏感词自动拦截</label>
                <div class="setting-description">包含敏感词的内容将自动被拦截</div>
                <label class="toggle-switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-group">
                <label class="setting-label">图片内容识别</label>
                <div class="setting-description">使用AI识别图片中的不当内容</div>
                <label class="toggle-switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="setting-group">
                <label class="setting-label">风险阈值设置</label>
                <div class="setting-description">设置自动审核的风险阈值（0-100）</div>
                <input type="range" min="0" max="100" value="75" class="threshold-slider" id="riskThreshold">
                <div style="text-align: center; margin-top: 10px;">
                    当前阈值: <span id="thresholdValue">75</span>
                </div>
            </div>

            <div class="setting-group">
                <button class="action-btn btn-approve" onclick="saveSettings()">
                    <i class="fas fa-save"></i> 保存设置
                </button>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const tab = this.dataset.tab;
                
                // 更新标签页状态
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // 显示对应内容
                document.querySelectorAll('.moderation-content, .sensitive-words-panel, .auto-review-settings').forEach(content => {
                    content.style.display = 'none';
                });
                
                if (tab === 'pending' || tab === 'approved' || tab === 'rejected') {
                    document.getElementById('pending-content').style.display = 'block';
                } else if (tab === 'sensitive') {
                    document.getElementById('sensitive-content').style.display = 'block';
                } else if (tab === 'settings') {
                    document.getElementById('settings-content').style.display = 'block';
                }
            });
        });

        // 审核操作
        function approveContent(id) {
            if (confirm('确认通过此内容？')) {
                alert(`内容 ${id} 已通过审核`);
                // 这里可以添加实际的API调用
            }
        }

        function rejectContent(id) {
            const reason = prompt('请输入拒绝理由：');
            if (reason) {
                alert(`内容 ${id} 已拒绝，理由：${reason}`);
                // 这里可以添加实际的API调用
            }
        }

        function viewDetail(id) {
            alert(`查看内容 ${id} 的详细信息`);
            // 这里可以打开详情弹窗或跳转到详情页面
        }

        // 敏感词管理
        function addSensitiveWord() {
            const word = document.getElementById('newSensitiveWord').value.trim();
            if (word) {
                const wordsList = document.querySelector('.words-list');
                const wordItem = document.createElement('div');
                wordItem.className = 'word-item';
                wordItem.innerHTML = `
                    <span class="word-text">${word}</span>
                    <span class="delete-word" onclick="deleteSensitiveWord(this)">
                        <i class="fas fa-times"></i>
                    </span>
                `;
                wordsList.appendChild(wordItem);
                document.getElementById('newSensitiveWord').value = '';
                alert(`敏感词 "${word}" 已添加`);
            }
        }

        function deleteSensitiveWord(element) {
            const word = element.parentElement.querySelector('.word-text').textContent;
            if (confirm(`确认删除敏感词 "${word}"？`)) {
                element.parentElement.remove();
                alert(`敏感词 "${word}" 已删除`);
            }
        }

        // 设置管理
        function saveSettings() {
            alert('设置已保存');
        }

        // 风险阈值滑块
        document.getElementById('riskThreshold').addEventListener('input', function() {
            document.getElementById('thresholdValue').textContent = this.value;
        });

        // 模拟实时数据更新
        setInterval(() => {
            const pendingCount = document.querySelector('.stat-card.pending .stat-number');
            const currentCount = parseInt(pendingCount.textContent);
            if (Math.random() > 0.7) {
                pendingCount.textContent = currentCount + 1;
                document.querySelector('.tab-badge').textContent = currentCount + 1;
            }
        }, 10000);
    </script>
</body>
</html> 