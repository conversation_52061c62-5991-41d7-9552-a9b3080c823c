<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="../../assets/css/header-button-fix.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <script src="../../assets/js/image-error-handler.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .chat-management-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }
        
        .page-actions {
            display: flex;
            gap: 12px;
        }
        
        .action-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-btn:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }
        
        .action-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .action-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .stat-title {
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .stat-desc {
            font-size: 12px;
            color: #64748b;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 24px;
        }
        
        .content-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .section-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }
        
        .section-actions {
            display: flex;
            gap: 8px;
        }
        
        .filter-tabs {
            display: flex;
            padding: 0 24px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .filter-tab {
            padding: 12px 16px;
            border: none;
            background: none;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }
        
        .filter-tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }
        
        .chat-list {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .chat-item {
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .chat-item:hover {
            background: #f8fafc;
        }
        
        .chat-item.selected {
            background: #eff6ff;
            border-left: 4px solid #3b82f6;
        }
        
        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .chat-participants {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: #1e293b;
        }
        
        .participant-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .chat-time {
            font-size: 12px;
            color: #64748b;
        }
        
        .chat-preview {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .chat-tags {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }
        
        .chat-tag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
        }
        
        .tag-risk {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .tag-commercial {
            background: #fef3c7;
            color: #92400e;
        }
        
        .tag-normal {
            background: #dcfce7;
            color: #166534;
        }
        
        .tag-bot {
            background: #e0e7ff;
            color: #3730a3;
        }
        
        .chat-detail-panel {
            display: flex;
            flex-direction: column;
            height: 700px;
        }
        
        .detail-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .detail-user-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .detail-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .detail-user-name {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .detail-user-status {
            font-size: 12px;
            color: #64748b;
        }
        
        .detail-actions {
            display: flex;
            gap: 8px;
        }
        
        .messages-container {
            flex: 1;
            padding: 16px 24px;
            overflow-y: auto;
            background: #f8fafc;
        }
        
        .message-item {
            margin-bottom: 16px;
        }
        
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }
        
        .message-sender {
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
        }
        
        .message-time {
            font-size: 11px;
            color: #94a3b8;
        }
        
        .message-content {
            background: white;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            font-size: 14px;
            color: #1e293b;
            line-height: 1.5;
        }
        
        .message-flags {
            display: flex;
            gap: 4px;
            margin-top: 4px;
        }
        
        .message-flag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
        }
        
        .flag-spam {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .flag-commercial {
            background: #fef3c7;
            color: #92400e;
        }
        
        .bot-management {
            padding: 24px;
        }
        
        .bot-list {
            display: grid;
            gap: 16px;
        }
        
        .bot-item {
            background: #f8fafc;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .bot-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .bot-name {
            font-weight: 600;
            color: #1e293b;
        }
        
        .bot-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .bot-description {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
        }
        
        .bot-stats {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #64748b;
        }
        
        .search-bar {
            padding: 16px 24px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .alert-item {
            padding: 12px 16px;
            border-left: 4px solid #ef4444;
            background: #fef2f2;
            margin-bottom: 8px;
            border-radius: 0 6px 6px 0;
        }
        
        .alert-title {
            font-weight: 500;
            color: #991b1b;
            margin-bottom: 4px;
        }
        
        .alert-desc {
            font-size: 12px;
            color: #7f1d1d;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="chat-management-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">聊天管理</h1>
                <p style="color: #64748b; margin: 4px 0 0 0;">监控和管理 TeleShop 聊天系统</p>
            </div>
            <div class="page-actions">
                <button class="action-btn" onclick="exportChatData()">
                    <i class="fas fa-download"></i> 导出数据
                </button>
                <button class="action-btn" onclick="showSettings()">
                    <i class="fas fa-cog"></i> 聊天设置
                </button>
                <button class="action-btn primary" onclick="showBotManagement()">
                    <i class="fas fa-robot"></i> 机器人管理
                </button>
            </div>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">今日消息</span>
                    <div class="stat-icon" style="background: #dbeafe; color: #3b82f6;">
                        <i class="fas fa-comments"></i>
                    </div>
                </div>
                <div class="stat-value">45,234</div>
                <div class="stat-desc">较昨日 +12.5%</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">活跃会话</span>
                    <div class="stat-icon" style="background: #dcfce7; color: #10b981;">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value">8,547</div>
                <div class="stat-desc">当前在线用户</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">风险检测</span>
                    <div class="stat-icon" style="background: #fee2e2; color: #ef4444;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stat-value">23</div>
                <div class="stat-desc">待处理风险消息</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">机器人响应</span>
                    <div class="stat-icon" style="background: #f3e8ff; color: #8b5cf6;">
                        <i class="fas fa-robot"></i>
                    </div>
                </div>
                <div class="stat-value">12,690</div>
                <div class="stat-desc">AI自动处理</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">商品分享</span>
                    <div class="stat-icon" style="background: #fef3c7; color: #f59e0b;">
                        <i class="fas fa-share"></i>
                    </div>
                </div>
                <div class="stat-value">3,456</div>
                <div class="stat-desc">今日商品推广</div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 聊天监控列表 -->
            <div class="content-section">
                <div class="section-header">
                    <h3 class="section-title">聊天监控</h3>
                    <div class="section-actions">
                        <button class="action-btn" onclick="refreshChatList()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="action-btn" onclick="showFilters()">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索用户名、关键词或ID..." 
                           onkeyup="searchChats(this.value)">
                </div>
                
                <!-- 过滤标签 -->
                <div class="filter-tabs">
                    <button class="filter-tab active" onclick="filterChats('all')">全部</button>
                    <button class="filter-tab" onclick="filterChats('risk')">风险 (23)</button>
                    <button class="filter-tab" onclick="filterChats('commercial')">商业 (156)</button>
                    <button class="filter-tab" onclick="filterChats('bot')">机器人 (89)</button>
                    <button class="filter-tab" onclick="filterChats('group')">群聊 (245)</button>
                </div>
                
                <!-- 聊天列表 -->
                <div class="chat-list" id="chatList">
                    <!-- 风险消息示例 -->
                    <div class="chat-item" onclick="selectChat('risk-001')">
                        <div class="chat-header">
                            <div class="chat-participants">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b332844c?w=24&h=24&fit=crop" alt="Alice" class="participant-avatar">
                                <span>Alice Chen</span>
                                <i class="fas fa-arrow-right" style="font-size: 10px; color: #94a3b8; margin: 0 4px;"></i>
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=24&h=24&fit=crop" alt="Bob" class="participant-avatar">
                                <span>Bob Wang</span>
                            </div>
                            <span class="chat-time">2分钟前</span>
                        </div>
                        <div class="chat-preview">
                            这个商品的价格确实有问题，我觉得可能是假货。而且这个商家的信誉度也不高...
                        </div>
                        <div class="chat-tags">
                            <span class="chat-tag tag-risk">风险检测</span>
                            <span class="chat-tag tag-commercial">商品讨论</span>
                        </div>
                    </div>
                    
                    <!-- 商业消息示例 -->
                    <div class="chat-item" onclick="selectChat('commercial-001')">
                        <div class="chat-header">
                            <div class="chat-participants">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=24&h=24&fit=crop" alt="Charlie" class="participant-avatar">
                                <span>Charlie Li</span>
                                <i class="fas fa-arrow-right" style="font-size: 10px; color: #94a3b8; margin: 0 4px;"></i>
                                <span>官方客服</span>
                            </div>
                            <span class="chat-time">5分钟前</span>
                        </div>
                        <div class="chat-preview">
                            [商品推荐] iPhone 15 Pro Max 深空黑色 256GB - ¥9,999 限时优惠！
                        </div>
                        <div class="chat-tags">
                            <span class="chat-tag tag-commercial">商品推广</span>
                            <span class="chat-tag tag-bot">机器人</span>
                        </div>
                    </div>
                    
                    <!-- 正常聊天示例 -->
                    <div class="chat-item" onclick="selectChat('normal-001')">
                        <div class="chat-header">
                            <div class="chat-participants">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=24&h=24&fit=crop" alt="David" class="participant-avatar">
                                <span>David Zhang</span>
                                <i class="fas fa-arrow-right" style="font-size: 10px; color: #94a3b8; margin: 0 4px;"></i>
                                <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=24&h=24&fit=crop" alt="Eve" class="participant-avatar">
                                <span>Eve Johnson</span>
                            </div>
                            <span class="chat-time">12分钟前</span>
                        </div>
                        <div class="chat-preview">
                            你好！请问这个商品还有现货吗？我想了解一下配送时间。
                        </div>
                        <div class="chat-tags">
                            <span class="chat-tag tag-normal">正常对话</span>
                        </div>
                    </div>
                    
                    <!-- 群聊示例 -->
                    <div class="chat-item" onclick="selectChat('group-001')">
                        <div class="chat-header">
                            <div class="chat-participants">
                                <i class="fas fa-users" style="color: #64748b; margin-right: 4px;"></i>
                                <span>iPhone 爱好者群</span>
                                <span style="font-size: 12px; color: #64748b;">(15人)</span>
                            </div>
                            <span class="chat-time">18分钟前</span>
                        </div>
                        <div class="chat-preview">
                            Frank Miller: 大家觉得 iPhone 15 Pro 和 Pro Max 哪个更值得买？
                        </div>
                        <div class="chat-tags">
                            <span class="chat-tag tag-normal">群聊</span>
                            <span class="chat-tag tag-commercial">产品讨论</span>
                        </div>
                    </div>
                    
                    <!-- 机器人对话示例 -->
                    <div class="chat-item" onclick="selectChat('bot-001')">
                        <div class="chat-header">
                            <div class="chat-participants">
                                <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=24&h=24&fit=crop" alt="Grace" class="participant-avatar">
                                <span>Grace Wilson</span>
                                <i class="fas fa-arrow-right" style="font-size: 10px; color: #94a3b8; margin: 0 4px;"></i>
                                <i class="fas fa-robot" style="color: #8b5cf6; margin-right: 4px;"></i>
                                <span>24h客服助手</span>
                            </div>
                            <span class="chat-time">25分钟前</span>
                        </div>
                        <div class="chat-preview">
                            您好！我是TeleShop智能客服，很高兴为您服务。请问有什么可以帮您的吗？
                        </div>
                        <div class="chat-tags">
                            <span class="chat-tag tag-bot">AI客服</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 聊天详情面板 -->
            <div class="content-section chat-detail-panel">
                <div class="detail-header">
                    <div class="detail-user-info">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b332844c?w=48&h=48&fit=crop" alt="Alice" class="detail-avatar">
                        <div>
                            <div class="detail-user-name">Alice Chen</div>
                            <div class="detail-user-status">在线 · 最后活跃 2分钟前</div>
                        </div>
                    </div>
                    <div class="detail-actions">
                        <button class="action-btn" onclick="exportChat()" title="导出聊天记录">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="action-btn" onclick="flagChat()" title="标记消息">
                            <i class="fas fa-flag"></i>
                        </button>
                        <button class="action-btn danger" onclick="blockUser()" title="封禁用户">
                            <i class="fas fa-ban"></i>
                        </button>
                    </div>
                </div>
                
                <div class="messages-container" id="messagesContainer">
                    <div class="message-item">
                        <div class="message-header">
                            <span class="message-sender">Alice Chen</span>
                            <span class="message-time">14:23</span>
                        </div>
                        <div class="message-content">
                            这个商品的价格确实有问题，我觉得可能是假货。
                        </div>
                        <div class="message-flags">
                            <span class="message-flag flag-spam">风险检测</span>
                        </div>
                    </div>
                    
                    <div class="message-item">
                        <div class="message-header">
                            <span class="message-sender">Bob Wang</span>
                            <span class="message-time">14:25</span>
                        </div>
                        <div class="message-content">
                            我也觉得这个价格太便宜了，正品不可能这个价格。
                        </div>
                    </div>
                    
                    <div class="message-item">
                        <div class="message-header">
                            <span class="message-sender">Alice Chen</span>
                            <span class="message-time">14:26</span>
                        </div>
                        <div class="message-content">
                            而且这个商家的信誉度也不高，我查了一下只有3星评价。
                        </div>
                        <div class="message-flags">
                            <span class="message-flag flag-commercial">商家讨论</span>
                        </div>
                    </div>
                    
                    <div class="message-item">
                        <div class="message-header">
                            <span class="message-sender">Bob Wang</span>
                            <span class="message-time">14:28</span>
                        </div>
                        <div class="message-content">
                            建议还是买官方旗舰店的，虽然贵一点但是有保障。
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 风险警报面板 -->
        <div class="content-section" style="margin-top: 24px;">
            <div class="section-header">
                <h3 class="section-title">实时风险警报</h3>
                <div class="section-actions">
                    <button class="action-btn" onclick="clearAlerts()">
                        <i class="fas fa-check"></i> 全部已读
                    </button>
                </div>
            </div>
            <div style="padding: 16px 24px;">
                <div class="alert-item">
                    <div class="alert-title">检测到疑似欺诈消息</div>
                    <div class="alert-desc">用户 @scammer123 发送了包含"100%保证盈利"的消息 - 5分钟前</div>
                </div>
                <div class="alert-item">
                    <div class="alert-title">异常商品价格警告</div>
                    <div class="alert-desc">iPhone 15 Pro Max 以 ¥500 价格出售，可能为欺诈信息 - 12分钟前</div>
                </div>
                <div class="alert-item">
                    <div class="alert-title">大量垃圾消息检测</div>
                    <div class="alert-desc">用户 @spammer456 在短时间内发送了20条相似消息 - 18分钟前</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 机器人管理弹窗 -->
    <div id="botManagementModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <div class="section-header">
                <h3 class="section-title">机器人管理</h3>
                <button class="action-btn" onclick="closeBotManagement()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="bot-management">
                <div class="bot-list">
                    <div class="bot-item">
                        <div class="bot-header">
                            <span class="bot-name">24h客服助手</span>
                            <span class="bot-status status-active">运行中</span>
                        </div>
                        <div class="bot-description">
                            智能客服机器人，7x24小时为用户提供即时客服支持
                        </div>
                        <div class="bot-stats">
                            <span>今日处理: 1,234 次对话</span>
                            <span>满意度: 92%</span>
                            <span>响应时间: < 2秒</span>
                        </div>
                    </div>
                    
                    <div class="bot-item">
                        <div class="bot-header">
                            <span class="bot-name">加密货币顾问</span>
                            <span class="bot-status status-active">运行中</span>
                        </div>
                        <div class="bot-description">
                            提供实时汇率查询和加密货币支付指导
                        </div>
                        <div class="bot-stats">
                            <span>今日查询: 456 次</span>
                            <span>准确率: 99.8%</span>
                            <span>支持币种: 15种</span>
                        </div>
                    </div>
                    
                    <div class="bot-item">
                        <div class="bot-header">
                            <span class="bot-name">内容审核机器人</span>
                            <span class="bot-status status-active">运行中</span>
                        </div>
                        <div class="bot-description">
                            自动检测和过滤违规内容、垃圾信息和欺诈消息
                        </div>
                        <div class="bot-stats">
                            <span>今日检测: 45,234 条消息</span>
                            <span>拦截风险: 127 条</span>
                            <span>误报率: < 0.1%</span>
                        </div>
                    </div>
                    
                    <div class="bot-item">
                        <div class="bot-header">
                            <span class="bot-name">商品推荐机器人</span>
                            <span class="bot-status status-inactive">已暂停</span>
                        </div>
                        <div class="bot-description">
                            基于用户兴趣和购买历史提供个性化商品推荐
                        </div>
                        <div class="bot-stats">
                            <span>推荐成功率: 23%</span>
                            <span>点击率: 8.5%</span>
                            <span>转化率: 2.1%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 选择聊天
        function selectChat(chatId) {
            // 移除之前的选中状态
            document.querySelectorAll('.chat-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 设置当前选中状态
            event.currentTarget.classList.add('selected');
            
            // 这里可以加载具体的聊天详情
            console.log('选中聊天:', chatId);
        }
        
        // 过滤聊天
        function filterChats(type) {
            const tabs = document.querySelectorAll('.filter-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // 这里可以添加过滤逻辑
            showNotification(`已切换到 ${type} 视图`, 'info');
        }
        
        // 搜索聊天
        function searchChats(query) {
            if (query.trim()) {
                showNotification(`搜索: ${query}`, 'info');
                // 这里可以添加搜索逻辑
            }
        }
        
        // 刷新聊天列表
        function refreshChatList() {
            showNotification('正在刷新聊天列表...', 'info');
            setTimeout(() => {
                showNotification('聊天列表已更新！', 'success');
            }, 1500);
        }
        
        // 显示过滤器
        function showFilters() {
            showNotification('过滤器功能开发中...', 'info');
        }
        
        // 导出聊天数据
        function exportChatData() {
            showNotification('正在导出聊天数据...', 'info');
            setTimeout(() => {
                showNotification('聊天数据导出成功！', 'success');
            }, 2000);
        }
        
        // 显示设置
        function showSettings() {
            showNotification('聊天设置功能开发中...', 'info');
        }
        
        // 显示机器人管理
        function showBotManagement() {
            document.getElementById('botManagementModal').style.display = 'flex';
        }
        
        // 关闭机器人管理
        function closeBotManagement() {
            document.getElementById('botManagementModal').style.display = 'none';
        }
        
        // 导出聊天记录
        function exportChat() {
            showNotification('正在导出聊天记录...', 'info');
        }
        
        // 标记聊天
        function flagChat() {
            showNotification('聊天已标记', 'success');
        }
        
        // 封禁用户
        function blockUser() {
            if (confirm('确定要封禁这个用户吗？')) {
                showNotification('用户已被封禁', 'success');
            }
        }
        
        // 清除警报
        function clearAlerts() {
            showNotification('所有警报已标记为已读', 'success');
        }
        
        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg text-white text-sm font-medium transition-all duration-300 ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 
                type === 'warning' ? 'bg-orange-500' : 'bg-blue-500'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
        
        // 模态框样式
        const modalStyle = document.createElement('style');
        modalStyle.textContent = `
            .modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            
            .modal-content {
                background: white;
                border-radius: 12px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
            }
        `;
        document.head.appendChild(modalStyle);
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认选中第一个聊天项
            const firstChat = document.querySelector('.chat-item');
            if (firstChat) {
                firstChat.classList.add('selected');
            }
        });
    </script>
</body>
</html> 