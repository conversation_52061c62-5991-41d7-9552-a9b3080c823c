<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>群组和频道管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .group-management-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .search-filters {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .filter-row {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .filter-input {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .groups-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #64748b;
            font-size: 12px;
            text-transform: uppercase;
        }
        
        .group-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .group-avatar {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #e2e8f0;
        }
        
        .group-details {
            flex: 1;
        }
        
        .group-name {
            font-weight: 600;
            color: #1e293b;
        }
        
        .group-description {
            font-size: 12px;
            color: #64748b;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-suspended {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-reported {
            background: #fef3c7;
            color: #92400e;
        }
        
        .action-buttons {
            display: flex;
            gap: 4px;
        }
        
        .action-icon-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .action-icon-btn.primary {
            background: #eff6ff;
            color: #2563eb;
        }
        
        .action-icon-btn.warning {
            background: #fef3c7;
            color: #d97706;
        }
        
        .action-icon-btn.danger {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .member-count {
            font-weight: 600;
            color: #374151;
        }
        
        .member-growth {
            font-size: 12px;
            color: #10b981;
        }
    </style>
</head>
<body>
    <div class="group-management-page">
        <div class="page-header">
            <h1 class="page-title">群组和频道管理</h1>
            <div class="page-actions">
                <button class="action-btn" onclick="exportGroups()">
                    <i class="fas fa-download"></i> 导出数据
                </button>
                <button class="action-btn primary" onclick="createGroup()">
                    <i class="fas fa-plus"></i> 创建群组
                </button>
            </div>
        </div>
        
        <div class="search-filters">
            <div class="filter-row">
                <div class="filter-group">
                    <label class="filter-label">搜索群组</label>
                    <input type="text" class="filter-input" placeholder="群组名称或ID" onchange="searchGroups(this.value)">
                </div>
                <div class="filter-group">
                    <label class="filter-label">类型</label>
                    <select class="filter-input" onchange="filterByType(this.value)">
                        <option value="">全部类型</option>
                        <option value="group">群组</option>
                        <option value="channel">频道</option>
                        <option value="supergroup">超级群组</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">状态</label>
                    <select class="filter-input" onchange="filterByStatus(this.value)">
                        <option value="">全部状态</option>
                        <option value="active">活跃</option>
                        <option value="suspended">暂停</option>
                        <option value="reported">被举报</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">成员数量</label>
                    <select class="filter-input" onchange="filterByMemberCount(this.value)">
                        <option value="">全部</option>
                        <option value="small">小于100</option>
                        <option value="medium">100-1000</option>
                        <option value="large">大于1000</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="groups-table">
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>群组信息</th>
                            <th>类型</th>
                            <th>创建者</th>
                            <th>成员数量</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>最后活跃</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="group-info">
                                    <img src="https://via.placeholder.com/40" alt="群组头像" class="group-avatar">
                                    <div class="group-details">
                                        <div class="group-name">TeleShop官方交流群</div>
                                        <div class="group-description">官方产品交流和客服支持</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="type-badge">超级群组</span>
                            </td>
                            <td>
                                <div class="creator-info">
                                    <div class="creator-name">管理员</div>
                                    <div class="creator-id">@admin</div>
                                </div>
                            </td>
                            <td>
                                <div class="member-count">2,547</div>
                                <div class="member-growth">+15 本周</div>
                            </td>
                            <td>
                                <span class="status-badge status-active">活跃</span>
                            </td>
                            <td>2024-01-15</td>
                            <td>2分钟前</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-icon-btn primary" onclick="viewGroup(1)" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-icon-btn" onclick="editGroup(1)" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-icon-btn warning" onclick="suspendGroup(1)" title="暂停">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="action-icon-btn danger" onclick="deleteGroup(1)" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr>
                            <td>
                                <div class="group-info">
                                    <img src="https://via.placeholder.com/40" alt="群组头像" class="group-avatar">
                                    <div class="group-details">
                                        <div class="group-name">商品推广频道</div>
                                        <div class="group-description">最新商品推广和优惠信息</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="type-badge">频道</span>
                            </td>
                            <td>
                                <div class="creator-info">
                                    <div class="creator-name">运营团队</div>
                                    <div class="creator-id">@marketing</div>
                                </div>
                            </td>
                            <td>
                                <div class="member-count">8,923</div>
                                <div class="member-growth">+45 本周</div>
                            </td>
                            <td>
                                <span class="status-badge status-active">活跃</span>
                            </td>
                            <td>2024-02-01</td>
                            <td>1小时前</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-icon-btn primary" onclick="viewGroup(2)" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-icon-btn" onclick="editGroup(2)" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-icon-btn warning" onclick="suspendGroup(2)" title="暂停">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="action-icon-btn danger" onclick="deleteGroup(2)" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr>
                            <td>
                                <div class="group-info">
                                    <img src="https://via.placeholder.com/40" alt="群组头像" class="group-avatar">
                                    <div class="group-details">
                                        <div class="group-name">违规群组示例</div>
                                        <div class="group-description">被举报的群组</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="type-badge">群组</span>
                            </td>
                            <td>
                                <div class="creator-info">
                                    <div class="creator-name">用户123</div>
                                    <div class="creator-id">@user123</div>
                                </div>
                            </td>
                            <td>
                                <div class="member-count">156</div>
                                <div class="member-growth" style="color: #ef4444;">-8 本周</div>
                            </td>
                            <td>
                                <span class="status-badge status-reported">被举报</span>
                            </td>
                            <td>2024-02-28</td>
                            <td>3天前</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-icon-btn primary" onclick="viewGroup(3)" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-icon-btn warning" onclick="reviewReport(3)" title="处理举报">
                                        <i class="fas fa-gavel"></i>
                                    </button>
                                    <button class="action-icon-btn danger" onclick="deleteGroup(3)" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="pagination">
                <div class="pagination-info">
                    显示 1-20 条，共 156 条记录
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>上一页</button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function searchGroups(query) {
            showNotification(`搜索群组: ${query}`, 'info');
        }
        
        function filterByType(type) {
            showNotification(`按类型筛选: ${type}`, 'info');
        }
        
        function filterByStatus(status) {
            showNotification(`按状态筛选: ${status}`, 'info');
        }
        
        function filterByMemberCount(range) {
            showNotification(`按成员数筛选: ${range}`, 'info');
        }
        
        function viewGroup(id) {
            showNotification(`查看群组 ${id} 详情`, 'info');
        }
        
        function editGroup(id) {
            showNotification(`编辑群组 ${id}`, 'info');
        }
        
        function suspendGroup(id) {
            if (confirm('确定要暂停这个群组吗？')) {
                showNotification(`群组 ${id} 已暂停`, 'warning');
            }
        }
        
        function deleteGroup(id) {
            if (confirm('确定要删除这个群组吗？此操作无法撤销！')) {
                showNotification(`群组 ${id} 已删除`, 'error');
            }
        }
        
        function reviewReport(id) {
            showNotification(`正在处理群组 ${id} 的举报`, 'info');
        }
        
        function exportGroups() {
            showNotification('正在导出群组数据...', 'info');
        }
        
        function createGroup() {
            showNotification('打开创建群组页面', 'info');
        }
        
        function showNotification(message, type) {
            console.log(`${type}: ${message}`);
        }
    </script>
</body>
</html> 