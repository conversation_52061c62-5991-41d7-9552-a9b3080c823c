<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>消息内容审核 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="../../assets/js/image-error-handler.js"></script>
    <style>
        /* 消息内容审核页面专用样式 */
        .audit-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
            width: 100%;
            overflow-x: hidden;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }
        
        .page-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }
        
        .action-btn:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
            text-decoration: none;
        }
        
        .action-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .action-btn.primary:hover {
            background: #2563eb;
        }
        
        .action-btn.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .action-btn.danger:hover {
            background: #dc2626;
        }
        
        /* 审核模式切换 */
        .audit-mode-selector {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .mode-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }
        
        .mode-tabs {
            display: flex;
            gap: 4px;
            background: #f1f5f9;
            padding: 4px;
            border-radius: 8px;
            margin-bottom: 16px;
        }
        
        .mode-tab {
            flex: 1;
            padding: 8px 16px;
            background: transparent;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .mode-tab.active {
            background: white;
            color: #3b82f6;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        /* 统计卡片 */
        .audit-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .stat-title {
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .stat-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .stat-change.positive {
            color: #10b981;
        }
        
        .stat-change.negative {
            color: #ef4444;
        }
        
        /* 审核队列 */
        .audit-queue {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
            margin-bottom: 24px;
        }
        
        .queue-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .queue-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }
        
        .queue-filters {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .filter-select {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }
        
        .audit-item {
            padding: 20px 24px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            gap: 16px;
            align-items: flex-start;
            transition: background 0.2s;
        }
        
        .audit-item:hover {
            background: #f8fafc;
        }
        
        .audit-item:last-child {
            border-bottom: none;
        }
        
        .message-content {
            flex: 1;
            min-width: 0;
        }
        
        .message-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
            background: #e2e8f0;
        }
        
        .user-name {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }
        
        .message-time {
            font-size: 12px;
            color: #64748b;
        }
        
        .message-type-badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .type-text {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .type-image {
            background: #fef3c7;
            color: #92400e;
        }
        
        .type-voice {
            background: #ecfdf5;
            color: #166534;
        }
        
        .type-video {
            background: #fce7f3;
            color: #be185d;
        }
        
        .message-text {
            margin: 8px 0;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.5;
            color: #374151;
        }
        
        .message-media {
            margin: 8px 0;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .media-item {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            background: #f1f5f9;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .media-item:hover {
            transform: scale(1.05);
        }
        
        .media-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .risk-indicators {
            display: flex;
            gap: 8px;
            margin: 8px 0;
            flex-wrap: wrap;
        }
        
        .risk-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .risk-high {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .risk-medium {
            background: #fef3c7;
            color: #92400e;
        }
        
        .risk-low {
            background: #f0fdf4;
            color: #166534;
        }
        
        .audit-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex-shrink: 0;
        }
        
        .audit-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }
        
        .audit-btn.approve {
            background: #dcfce7;
            color: #166534;
        }
        
        .audit-btn.approve:hover {
            background: #bbf7d0;
        }
        
        .audit-btn.reject {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .audit-btn.reject:hover {
            background: #fecaca;
        }
        
        .audit-btn.pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .audit-btn.pending:hover {
            background: #fde68a;
        }
        
        /* 审核规则配置 */
        .rules-config {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .rules-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .rules-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }
        
        .rules-content {
            padding: 24px;
        }
        
        .rule-section {
            margin-bottom: 24px;
        }
        
        .rule-section:last-child {
            margin-bottom: 0;
        }
        
        .rule-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
        }
        
        .keyword-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
        }
        
        .keyword-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .rule-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .rule-toggle:last-child {
            border-bottom: none;
        }
        
        .toggle-label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }
        
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #cbd5e1;
            transition: .4s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: #3b82f6;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .audit-page {
                padding: 16px;
            }
            
            .page-header {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
            
            .audit-stats {
                grid-template-columns: 1fr;
            }
            
            .audit-item {
                flex-direction: column;
                gap: 12px;
            }
            
            .audit-actions {
                flex-direction: row;
                justify-content: flex-end;
            }
            
            .mode-tabs {
                flex-direction: column;
            }
            
            .queue-filters {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="audit-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">消息内容审核</h1>
            <div class="page-actions">
                <a href="../dashboard/index.html" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    仪表板
                </a>
                <a href="content-moderation.html" class="action-btn">
                    <i class="fas fa-shield-alt"></i>
                    内容审核
                </a>
                <a href="group-channel-management.html" class="action-btn">
                    <i class="fas fa-users"></i>
                    群组管理
                </a>
                <a href="chat-management.html" class="action-btn">
                    <i class="fas fa-comments"></i>
                    聊天管理
                </a>
                <button class="action-btn" onclick="exportAuditReport()">
                    <i class="fas fa-download"></i>
                    导出报告
                </button>
                <button class="action-btn" onclick="refreshQueue()">
                    <i class="fas fa-sync-alt"></i>
                    刷新队列
                </button>
                <button class="action-btn primary" onclick="showRulesConfig()">
                    <i class="fas fa-cog"></i>
                    审核配置
                </button>
            </div>
        </div>
        
        <!-- 审核模式选择 -->
        <div class="audit-mode-selector">
            <h3 class="mode-title">审核模式</h3>
            <div class="mode-tabs">
                <button class="mode-tab active" data-mode="realtime">实时审核</button>
                <button class="mode-tab" data-mode="batch">批量审核</button>
                <button class="mode-tab" data-mode="manual">人工审核</button>
            </div>
            <p style="color: #64748b; font-size: 14px; margin: 0;">
                当前模式：<strong>实时审核</strong> - 消息发送时立即进行AI智能审核
            </p>
        </div>
        
        <!-- 统计概览 -->
        <div class="audit-stats">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">今日待审核</span>
                    <div class="stat-icon" style="background: #fef3c7; color: #d97706;">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value">1,247</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    12.5%
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">今日已审核</span>
                    <div class="stat-icon" style="background: #dcfce7; color: #16a34a;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-value">3,428</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    8.3%
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">违规内容</span>
                    <div class="stat-icon" style="background: #fee2e2; color: #dc2626;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stat-value">89</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i>
                    3.2%
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">审核准确率</span>
                    <div class="stat-icon" style="background: #dbeafe; color: #2563eb;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="stat-value">96.8%</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    1.5%
                </div>
            </div>
        </div>
        
        <!-- 审核队列 -->
        <div class="audit-queue">
            <div class="queue-header">
                <h3 class="queue-title">待审核消息</h3>
                <div class="queue-filters">
                    <select class="filter-select" onchange="filterByType(this.value)">
                        <option value="">全部类型</option>
                        <option value="text">文本消息</option>
                        <option value="image">图片消息</option>
                        <option value="voice">语音消息</option>
                        <option value="video">视频消息</option>
                    </select>
                    <select class="filter-select" onchange="filterByRisk(this.value)">
                        <option value="">全部风险等级</option>
                        <option value="high">高风险</option>
                        <option value="medium">中风险</option>
                        <option value="low">低风险</option>
                    </select>
                </div>
            </div>
            
            <!-- 审核项目 -->
            <div class="audit-item">
                <div class="message-content">
                    <div class="message-meta">
                        <div class="user-info">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop" alt="用户头像" class="user-avatar">
                            <span class="user-name">张三</span>
                        </div>
                        <span class="message-time">2分钟前</span>
                        <span class="message-type-badge type-text">文本</span>
                    </div>
                    <div class="message-text">
                        这是一条包含敏感词汇的测试消息，需要人工审核确认是否违规...
                    </div>
                    <div class="risk-indicators">
                        <span class="risk-tag risk-high">敏感词汇</span>
                        <span class="risk-tag risk-medium">内容可疑</span>
                    </div>
                </div>
                <div class="audit-actions">
                    <button class="audit-btn approve" onclick="approveMessage(1)">通过</button>
                    <button class="audit-btn reject" onclick="rejectMessage(1)">拒绝</button>
                    <button class="audit-btn pending" onclick="pendingMessage(1)">待定</button>
                </div>
            </div>
            
            <div class="audit-item">
                <div class="message-content">
                    <div class="message-meta">
                        <div class="user-info">
                            <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=32&h=32&fit=crop" alt="用户头像" class="user-avatar">
                            <span class="user-name">李四</span>
                        </div>
                        <span class="message-time">5分钟前</span>
                        <span class="message-type-badge type-image">图片</span>
                    </div>
                    <div class="message-media">
                        <div class="media-item">
                            <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?w=80&h=80&fit=crop" alt="消息图片">
                        </div>
                        <div class="media-item">
                            <img src="https://images.unsplash.com/photo-1557845750-2b9ccc68b92a?w=80&h=80&fit=crop" alt="消息图片">
                        </div>
                    </div>
                    <div class="risk-indicators">
                        <span class="risk-tag risk-medium">图片识别待确认</span>
                    </div>
                </div>
                <div class="audit-actions">
                    <button class="audit-btn approve" onclick="approveMessage(2)">通过</button>
                    <button class="audit-btn reject" onclick="rejectMessage(2)">拒绝</button>
                    <button class="audit-btn pending" onclick="pendingMessage(2)">待定</button>
                </div>
            </div>
            
            <div class="audit-item">
                <div class="message-content">
                    <div class="message-meta">
                        <div class="user-info">
                            <img src="https://images.unsplash.com/photo-1527980965255-d3b416303d12?w=32&h=32&fit=crop" alt="用户头像" class="user-avatar">
                            <span class="user-name">王五</span>
                        </div>
                        <span class="message-time">8分钟前</span>
                        <span class="message-type-badge type-voice">语音</span>
                    </div>
                    <div class="message-text">
                        语音转文字：你好，我想了解一下这个产品的具体信息，请问价格是多少？
                    </div>
                    <div class="risk-indicators">
                        <span class="risk-tag risk-low">内容正常</span>
                    </div>
                </div>
                <div class="audit-actions">
                    <button class="audit-btn approve" onclick="approveMessage(3)">通过</button>
                    <button class="audit-btn reject" onclick="rejectMessage(3)">拒绝</button>
                    <button class="audit-btn pending" onclick="pendingMessage(3)">待定</button>
                </div>
            </div>
        </div>
        
        <!-- 审核规则配置模态框 -->
        <div id="rulesModal" class="modal-overlay" style="display: none;">
            <div class="modal-content" style="width: 90%; max-width: 800px;">
                <div class="rules-config">
                    <div class="rules-header">
                        <h3 class="rules-title">审核规则配置</h3>
                        <button class="action-btn" onclick="closeRulesConfig()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="rules-content">
                        <div class="rule-section">
                            <h4 class="rule-section-title">关键词过滤</h4>
                            <textarea class="keyword-input" placeholder="请输入需要过滤的关键词，每行一个">
敏感词1
敏感词2
敏感词3</textarea>
                        </div>
                        
                        <div class="rule-section">
                            <h4 class="rule-section-title">自动审核设置</h4>
                            <div class="rule-toggle">
                                <span class="toggle-label">启用实时审核</span>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="rule-toggle">
                                <span class="toggle-label">启用图片内容识别</span>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="rule-toggle">
                                <span class="toggle-label">启用语音转文字审核</span>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="rule-toggle">
                                <span class="toggle-label">启用AI智能审核</span>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="rule-section">
                            <h4 class="rule-section-title">审核阈值设置</h4>
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">高风险阈值: <span id="highRiskValue">80</span>%</label>
                                <input type="range" min="50" max="100" value="80" style="width: 100%;" onchange="updateThreshold('high', this.value)">
                            </div>
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">中风险阈值: <span id="mediumRiskValue">60</span>%</label>
                                <input type="range" min="30" max="80" value="60" style="width: 100%;" onchange="updateThreshold('medium', this.value)">
                            </div>
                        </div>
                        
                        <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">
                            <button class="action-btn" onclick="closeRulesConfig()">取消</button>
                            <button class="action-btn primary" onclick="saveRulesConfig()">保存配置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 审核模式切换
        document.querySelectorAll('.mode-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.mode-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                const mode = this.getAttribute('data-mode');
                updateAuditMode(mode);
            });
        });
        
        // 更新审核模式
        function updateAuditMode(mode) {
            const modes = {
                'realtime': '实时审核 - 消息发送时立即进行AI智能审核',
                'batch': '批量审核 - 定时批量处理待审核消息',
                'manual': '人工审核 - 所有消息均需人工审核确认'
            };
            
            document.querySelector('.audit-mode-selector p').innerHTML = `当前模式：<strong>${modes[mode]}</strong>`;
            showNotification(`已切换到${modes[mode].split(' - ')[0]}`, 'success');
        }
        
        // 消息审核操作
        function approveMessage(messageId) {
            showNotification(`消息 ${messageId} 已通过审核`, 'success');
            removeAuditItem(messageId);
        }
        
        function rejectMessage(messageId) {
            if (confirm('确定要拒绝这条消息吗？用户将收到通知。')) {
                showNotification(`消息 ${messageId} 已被拒绝`, 'warning');
                removeAuditItem(messageId);
            }
        }
        
        function pendingMessage(messageId) {
            showNotification(`消息 ${messageId} 已标记为待定`, 'info');
        }
        
        function removeAuditItem(messageId) {
            // 这里可以添加移除审核项的逻辑
            console.log(`移除审核项 ${messageId}`);
        }
        
        // 筛选功能
        function filterByType(type) {
            if (type) {
                showNotification(`已筛选 ${type} 类型消息`, 'info');
            } else {
                showNotification('显示所有类型消息', 'info');
            }
        }
        
        function filterByRisk(risk) {
            if (risk) {
                showNotification(`已筛选 ${risk} 风险等级消息`, 'info');
            } else {
                showNotification('显示所有风险等级消息', 'info');
            }
        }
        
        // 刷新队列
        function refreshQueue() {
            showNotification('正在刷新审核队列...', 'info');
            setTimeout(() => {
                showNotification('审核队列已更新！', 'success');
            }, 1500);
        }
        
        // 导出审核报告
        function exportAuditReport() {
            showNotification('正在生成审核报告...', 'info');
            setTimeout(() => {
                showNotification('审核报告已导出！', 'success');
            }, 2000);
        }
        
        // 显示规则配置
        function showRulesConfig() {
            document.getElementById('rulesModal').style.display = 'flex';
            setTimeout(() => {
                document.getElementById('rulesModal').classList.add('show');
            }, 10);
        }
        
        // 关闭规则配置
        function closeRulesConfig() {
            document.getElementById('rulesModal').classList.remove('show');
            setTimeout(() => {
                document.getElementById('rulesModal').style.display = 'none';
            }, 300);
        }
        
        // 保存规则配置
        function saveRulesConfig() {
            showNotification('审核规则配置已保存！', 'success');
            closeRulesConfig();
        }
        
        // 更新阈值显示
        function updateThreshold(type, value) {
            document.getElementById(`${type}RiskValue`).textContent = value;
        }
        
        // 通知系统
        function showNotification(message, type = 'info') {
            const colors = {
                'success': '#10b981',
                'error': '#ef4444',
                'warning': '#f59e0b',
                'info': '#3b82f6'
            };
            
            const notification = document.createElement('div');
            notification.className = 'notification-toast show';
            notification.style.cssText = `
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                z-index: 9999 !important;
                max-width: 350px;
                padding: 12px 16px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                font-size: 14px;
                font-weight: 500;
                color: white;
                background: ${colors[type]};
                opacity: 1;
                transform: translateX(0);
                transition: all 0.3s ease;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            console.log('消息内容审核页面初始化完成');
        });
    </script>
</body>
</html> 