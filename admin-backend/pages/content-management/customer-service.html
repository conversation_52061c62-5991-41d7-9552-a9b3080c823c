<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服与纠纷处理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .service-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
            margin-top: 4px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .stat-icon.tickets { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.disputes { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.response { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-icon.satisfaction { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .stat-change.positive {
            color: #10b981;
        }

        .stat-change.negative {
            color: #ef4444;
        }

        .service-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            margin-bottom: 24px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .tab-btn {
            flex: 1;
            padding: 16px 24px;
            border: none;
            background: white;
            color: #64748b;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .tab-btn.active {
            color: #3b82f6;
            background: #eff6ff;
            border-bottom: 3px solid #3b82f6;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .content-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }

        .ticket-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .ticket-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .ticket-item:hover {
            background: #f1f5f9;
        }

        .ticket-priority {
            width: 8px;
            height: 48px;
            border-radius: 4px;
        }

        .priority-high { background: #ef4444; }
        .priority-medium { background: #f59e0b; }
        .priority-low { background: #10b981; }

        .ticket-info {
            flex: 1;
        }

        .ticket-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .ticket-meta {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 8px;
        }

        .ticket-tags {
            display: flex;
            gap: 8px;
        }

        .ticket-tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .tag-urgent {
            background: #fee2e2;
            color: #dc2626;
        }

        .tag-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .tag-resolved {
            background: #dcfce7;
            color: #16a34a;
        }

        .ticket-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            min-width: 80px;
        }

        .status-open {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-processing {
            background: #fef3c7;
            color: #d97706;
        }

        .status-closed {
            background: #dcfce7;
            color: #16a34a;
        }

        .agent-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .agent-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .agent-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .agent-info {
            flex: 1;
        }

        .agent-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .agent-stats {
            font-size: 12px;
            color: #64748b;
        }

        .agent-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-online {
            background: #dcfce7;
            color: #166534;
        }

        .status-busy {
            background: #fef3c7;
            color: #ca8a04;
        }

        .status-offline {
            background: #f3f4f6;
            color: #374151;
        }

        .dispute-item {
            display: flex;
            gap: 16px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #ef4444;
        }

        .dispute-content {
            flex: 1;
        }

        .dispute-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .dispute-description {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 12px;
            line-height: 1.5;
        }

        .dispute-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #64748b;
        }

        .dispute-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: white;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .service-page {
                padding: 16px;
            }

            .header-content {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }

            .service-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="service-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title">客服与纠纷处理</h1>
                    <p class="page-subtitle">多级客服体系、纠纷解决、工单管理与客服绩效分析</p>
                </div>
                
                <div class="header-actions">
                    <button class="btn btn-secondary">
                        <i class="fas fa-headset"></i>
                        客服培训
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        新建工单
                    </button>
                </div>
            </div>
        </div>

        <!-- 核心指标概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon tickets">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                </div>
                <div class="stat-value">1,234</div>
                <div class="stat-label">待处理工单</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-up"></i>
                    +12 较昨日
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon disputes">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stat-value">89</div>
                <div class="stat-label">纠纷案例</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-down"></i>
                    -5 较昨日
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon response">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value">2.5分钟</div>
                <div class="stat-label">平均响应时间</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-down"></i>
                    -0.3分钟
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon satisfaction">
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <div class="stat-value">4.8</div>
                <div class="stat-label">客户满意度</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +0.2 较上周
                </div>
            </div>
        </div>

        <!-- 功能标签页 -->
        <div class="service-tabs">
            <button class="tab-btn active" onclick="switchTab('tickets')">
                <i class="fas fa-ticket-alt"></i>
                工单管理
            </button>
            <button class="tab-btn" onclick="switchTab('disputes')">
                <i class="fas fa-gavel"></i>
                纠纷处理
            </button>
            <button class="tab-btn" onclick="switchTab('agents')">
                <i class="fas fa-user-tie"></i>
                客服管理
            </button>
            <button class="tab-btn" onclick="switchTab('analytics')">
                <i class="fas fa-chart-bar"></i>
                绩效分析
            </button>
        </div>

        <!-- 工单管理内容 -->
        <div class="tab-content active" id="tickets-content">
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">待处理工单</h3>
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-small btn-secondary">全部</button>
                            <button class="btn btn-small btn-primary">紧急</button>
                        </div>
                    </div>
                    <div class="ticket-list">
                        <div class="ticket-item">
                            <div class="ticket-priority priority-high"></div>
                            <div class="ticket-info">
                                <div class="ticket-title">商品质量问题投诉</div>
                                <div class="ticket-meta">工单 #12345 • 华为旗舰专卖店 • 2小时前</div>
                                <div class="ticket-tags">
                                    <span class="ticket-tag tag-urgent">紧急</span>
                                </div>
                            </div>
                            <div class="ticket-status status-open">待处理</div>
                        </div>
                        
                        <div class="ticket-item">
                            <div class="ticket-priority priority-medium"></div>
                            <div class="ticket-info">
                                <div class="ticket-title">退款申请处理</div>
                                <div class="ticket-meta">工单 #12344 • 小米生活馆 • 4小时前</div>
                                <div class="ticket-tags">
                                    <span class="ticket-tag tag-pending">处理中</span>
                                </div>
                            </div>
                            <div class="ticket-status status-processing">处理中</div>
                        </div>
                        
                        <div class="ticket-item">
                            <div class="ticket-priority priority-low"></div>
                            <div class="ticket-info">
                                <div class="ticket-title">商品信息咨询</div>
                                <div class="ticket-meta">工单 #12343 • 时尚潮牌屋 • 6小时前</div>
                                <div class="ticket-tags">
                                    <span class="ticket-tag tag-resolved">已解决</span>
                                </div>
                            </div>
                            <div class="ticket-status status-closed">已关闭</div>
                        </div>
                    </div>
                </div>
                
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">在线客服</h3>
                        <a href="#" class="btn btn-small btn-secondary">管理客服</a>
                    </div>
                    <div class="agent-list">
                        <div class="agent-item">
                            <div class="agent-avatar">张</div>
                            <div class="agent-info">
                                <div class="agent-name">张小丽</div>
                                <div class="agent-stats">今日处理: 25个 | 满意度: 4.9</div>
                            </div>
                            <div class="agent-status status-online">在线</div>
                        </div>
                        
                        <div class="agent-item">
                            <div class="agent-avatar">李</div>
                            <div class="agent-info">
                                <div class="agent-name">李明</div>
                                <div class="agent-stats">今日处理: 18个 | 满意度: 4.7</div>
                            </div>
                            <div class="agent-status status-busy">忙碌</div>
                        </div>
                        
                        <div class="agent-item">
                            <div class="agent-avatar">王</div>
                            <div class="agent-info">
                                <div class="agent-name">王芳</div>
                                <div class="agent-stats">今日处理: 32个 | 满意度: 4.8</div>
                            </div>
                            <div class="agent-status status-offline">离线</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 纠纷处理内容 -->
        <div class="tab-content" id="disputes-content">
            <div class="content-card full-width">
                <div class="card-header">
                    <h3 class="card-title">纠纷案例</h3>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-small btn-secondary">全部</button>
                        <button class="btn btn-small btn-secondary">待处理</button>
                        <button class="btn btn-small btn-primary">紧急</button>
                    </div>
                </div>
                <div class="ticket-list">
                    <div class="dispute-item">
                        <div class="dispute-content">
                            <div class="dispute-title">商品质量纠纷</div>
                            <div class="dispute-description">
                                用户反映收到的华为手机存在质量问题，要求退换货。商家认为是用户使用不当导致，双方产生分歧。
                            </div>
                            <div class="dispute-meta">
                                <span>纠纷编号: #D12345</span>
                                <span>涉及商家: 华为旗舰专卖店</span>
                                <span>争议金额: ¥3,999</span>
                                <span>创建时间: 2024-01-15 14:30</span>
                            </div>
                        </div>
                        <div class="dispute-actions">
                            <button class="btn btn-small btn-primary">立即处理</button>
                            <button class="btn btn-small btn-secondary">查看详情</button>
                        </div>
                    </div>
                    
                    <div class="dispute-item">
                        <div class="dispute-content">
                            <div class="dispute-title">服务态度投诉</div>
                            <div class="dispute-description">
                                用户投诉小米生活馆客服态度恶劣，处理问题不积极，要求平台介入处理。
                            </div>
                            <div class="dispute-meta">
                                <span>纠纷编号: #D12344</span>
                                <span>涉及商家: 小米生活馆</span>
                                <span>争议金额: ¥1,299</span>
                                <span>创建时间: 2024-01-15 10:15</span>
                            </div>
                        </div>
                        <div class="dispute-actions">
                            <button class="btn btn-small btn-primary">立即处理</button>
                            <button class="btn btn-small btn-secondary">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客服管理内容 -->
        <div class="tab-content" id="agents-content">
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">客服团队</h3>
                        <button class="btn btn-small btn-primary">
                            <i class="fas fa-plus"></i>
                            添加客服
                        </button>
                    </div>
                    <div class="agent-list">
                        <div class="agent-item">
                            <div class="agent-avatar">张</div>
                            <div class="agent-info">
                                <div class="agent-name">张小丽 - 高级客服</div>
                                <div class="agent-stats">工作时长: 2年 | 专业: 售后服务</div>
                            </div>
                            <div class="agent-status status-online">在线</div>
                        </div>
                        
                        <div class="agent-item">
                            <div class="agent-avatar">李</div>
                            <div class="agent-info">
                                <div class="agent-name">李明 - 中级客服</div>
                                <div class="agent-stats">工作时长: 1年 | 专业: 订单处理</div>
                            </div>
                            <div class="agent-status status-busy">忙碌</div>
                        </div>
                        
                        <div class="agent-item">
                            <div class="agent-avatar">王</div>
                            <div class="agent-info">
                                <div class="agent-name">王芳 - 资深客服</div>
                                <div class="agent-stats">工作时长: 3年 | 专业: 纠纷处理</div>
                            </div>
                            <div class="agent-status status-offline">离线</div>
                        </div>
                    </div>
                </div>
                
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">客服工作量</h3>
                        <a href="#" class="btn btn-small btn-secondary">详细报告</a>
                    </div>
                    <div class="ticket-list">
                        <div class="ticket-item">
                            <div class="ticket-info">
                                <div class="ticket-title">张小丽</div>
                                <div class="ticket-meta">今日处理: 25个工单 | 平均响应: 1.8分钟</div>
                            </div>
                            <div class="ticket-status status-closed">优秀</div>
                        </div>
                        
                        <div class="ticket-item">
                            <div class="ticket-info">
                                <div class="ticket-title">李明</div>
                                <div class="ticket-meta">今日处理: 18个工单 | 平均响应: 2.1分钟</div>
                            </div>
                            <div class="ticket-status status-processing">良好</div>
                        </div>
                        
                        <div class="ticket-item">
                            <div class="ticket-info">
                                <div class="ticket-title">王芳</div>
                                <div class="ticket-meta">今日处理: 32个工单 | 平均响应: 1.5分钟</div>
                            </div>
                            <div class="ticket-status status-closed">优秀</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 绩效分析内容 -->
        <div class="tab-content" id="analytics-content">
            <div class="content-card full-width">
                <div class="card-header">
                    <h3 class="card-title">客服绩效分析</h3>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-small btn-secondary">本周</button>
                        <button class="btn btn-small btn-primary">本月</button>
                        <button class="btn btn-small btn-secondary">本季度</button>
                    </div>
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">95.2%</div>
                        <div class="stat-label">工单解决率</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +2.1% 较上月
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value">2.3分钟</div>
                        <div class="stat-label">平均响应时间</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-down"></i>
                            -0.5分钟
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value">4.8</div>
                        <div class="stat-label">客户满意度</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +0.3 较上月
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value">1,456</div>
                        <div class="stat-label">本月处理工单</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +156 较上月
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            document.getElementById(tabName + '-content').classList.add('active');
            event.target.classList.add('active');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('客服与纠纷处理页面已加载');
        });
    </script>
</body>
</html> 