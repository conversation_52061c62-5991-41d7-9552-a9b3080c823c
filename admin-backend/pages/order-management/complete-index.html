<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 基础样式 */
        .order-page { padding: 24px; background: #f8fafc; min-height: 100vh; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 28px; font-weight: 700; color: #1e293b; margin: 0; }
        .action-btn { padding: 10px 20px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 8px; text-decoration: none; }
        .action-btn:hover { background: #f1f5f9; border-color: #cbd5e1; }
        .action-btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .action-btn.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .action-btn.danger { background: #ef4444; color: white; border-color: #ef4444; }
        
        /* 统计卡片 */
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 24px; }
        .stat-card { background: white; padding: 20px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .stat-title { font-size: 14px; font-weight: 500; color: #64748b; margin-bottom: 8px; }
        .stat-value { font-size: 24px; font-weight: 700; color: #1e293b; margin-bottom: 4px; }
        .stat-desc { font-size: 12px; color: #64748b; }
        
        /* 主内容区 */
        .main-content { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .content-header { padding: 20px 24px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center; }
        .content-title { font-size: 18px; font-weight: 600; color: #1e293b; margin: 0; }
        
        /* 标签页 */
        .filter-tabs { display: flex; padding: 0 24px; border-bottom: 1px solid #e2e8f0; }
        .filter-tab { padding: 12px 16px; border: none; background: none; color: #64748b; font-size: 14px; cursor: pointer; border-bottom: 2px solid transparent; transition: all 0.2s; }
        .filter-tab.active { color: #3b82f6; border-bottom-color: #3b82f6; }
        
        /* 筛选区域 */
        .filter-section { padding: 20px 24px; border-bottom: 1px solid #e2e8f0; background: #f8fafc; }
        .filter-row { display: flex; gap: 16px; align-items: center; flex-wrap: wrap; }
        .filter-group { display: flex; flex-direction: column; gap: 4px; }
        .filter-label { font-size: 12px; font-weight: 500; color: #64748b; }
        .filter-input { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; min-width: 150px; }
        .filter-select { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; background: white; min-width: 120px; }
        
        /* 批量操作 */
        .bulk-actions { padding: 16px 24px; background: #eff6ff; border-bottom: 1px solid #e2e8f0; display: none; align-items: center; justify-content: space-between; }
        .bulk-actions.show { display: flex; }
        .bulk-info { font-size: 14px; color: #1e40af; }
        .bulk-buttons { display: flex; gap: 8px; }
        
        /* 表格样式 */
        .orders-table { width: 100%; border-collapse: collapse; }
        .orders-table th, .orders-table td { padding: 12px 16px; text-align: left; border-bottom: 1px solid #f1f5f9; }
        .orders-table th { background: #f8fafc; font-weight: 600; color: #374151; position: sticky; top: 0; font-size: 12px; }
        .order-checkbox { width: 18px; height: 18px; }
        .order-id { font-family: monospace; font-weight: 600; color: #3b82f6; cursor: pointer; }
        .order-id:hover { text-decoration: underline; }
        .customer-info { display: flex; align-items: center; gap: 8px; }
        .customer-avatar { width: 32px; height: 32px; border-radius: 50%; background: #f1f5f9; }
        .customer-name { font-weight: 500; color: #1e293b; margin-bottom: 2px; }
        .customer-id { font-size: 12px; color: #64748b; }
        .product-summary { display: flex; align-items: center; gap: 8px; }
        .product-image { width: 40px; height: 40px; border-radius: 6px; object-fit: cover; background: #f1f5f9; }
        .product-count { font-size: 12px; color: #64748b; }
        
        /* 状态标签 */
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-paid { background: #dbeafe; color: #1e40af; }
        .status-shipped { background: #e0e7ff; color: #3730a3; }
        .status-delivered { background: #dcfce7; color: #166534; }
        .status-refunding { background: #fed7aa; color: #ea580c; }
        .status-cancelled { background: #fecaca; color: #dc2626; }
        .risk-flag { background: #fef2f2; color: #dc2626; padding: 2px 6px; border-radius: 4px; font-size: 10px; font-weight: 600; margin-left: 4px; }
        
        /* 操作按钮 */
        .amount-value { font-weight: 600; color: #1e293b; }
        .action-buttons { display: flex; gap: 4px; }
        .btn-sm { padding: 6px 12px; border: 1px solid #e2e8f0; border-radius: 4px; background: white; color: #64748b; font-size: 12px; cursor: pointer; transition: all 0.2s; }
        .btn-sm:hover { background: #f1f5f9; }
        .btn-sm.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .btn-sm.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .btn-sm.danger { background: #ef4444; color: white; border-color: #ef4444; }
        
        /* 分页 */
        .pagination { padding: 20px 24px; display: flex; justify-content: space-between; align-items: center; }
        .pagination-info { font-size: 14px; color: #64748b; }
        .pagination-controls { display: flex; gap: 8px; }
        .page-btn { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 4px; background: white; color: #64748b; cursor: pointer; font-size: 14px; }
        .page-btn.active { background: #3b82f6; color: white; border-color: #3b82f6; }
        
        /* 模态框 */
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal.show { display: flex; align-items: center; justify-content: center; }
        .modal-content { background: white; border-radius: 12px; padding: 24px; width: 600px; max-width: 90vw; max-height: 90vh; overflow-y: auto; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .modal-title { font-size: 18px; font-weight: 600; color: #1e293b; margin: 0; }
        .modal-close { background: none; border: none; font-size: 24px; color: #64748b; cursor: pointer; }
        .form-group { margin-bottom: 16px; }
        .form-label { display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px; }
        .form-input { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; }
        .form-select { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; background: white; }
        .form-textarea { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; resize: vertical; min-height: 80px; }
        .modal-actions { display: flex; gap: 12px; justify-content: flex-end; margin-top: 20px; }
        .btn { padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.2s; }
        .btn.primary { background: #3b82f6; color: white; border: 1px solid #3b82f6; }
        .btn.secondary { background: white; color: #64748b; border: 1px solid #e2e8f0; }
        
        /* 兼容性样式 */
        @media (max-width: 768px) {
            .page-header { flex-direction: column; gap: 12px; align-items: stretch; }
            .stats-grid { grid-template-columns: 1fr 1fr; }
            .filter-row { flex-direction: column; align-items: stretch; }
            .orders-table { font-size: 12px; }
            .orders-table th, .orders-table td { padding: 8px 4px; }
            .modal-content { width: 95vw; padding: 16px; }
        }
    </style>
</head>
<body>
    <div class="order-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">订单管理</h1>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <button class="action-btn" onclick="exportOrders()">
                    <i class="fas fa-download"></i>
                    导出订单
                </button>
                <button class="action-btn warning" onclick="showBatchShip()">
                    <i class="fas fa-shipping-fast"></i>
                    批量发货
                </button>
                <button class="action-btn" onclick="showLogistics()">
                    <i class="fas fa-truck"></i>
                    物流管理
                </button>
                <button class="action-btn primary" onclick="refreshData()">
                    <i class="fas fa-sync"></i>
                    刷新数据
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-title">今日订单</div>
                <div class="stat-value">234</div>
                <div class="stat-desc">相比昨日 +12.5%</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">待发货</div>
                <div class="stat-value" style="color: #f59e0b;">89</div>
                <div class="stat-desc">需要及时处理</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">今日销售额</div>
                <div class="stat-value" style="color: #10b981;">¥156,847</div>
                <div class="stat-desc">达成率 89.2%</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">退款待审</div>
                <div class="stat-value" style="color: #ef4444;">12</div>
                <div class="stat-desc">需要人工审核</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 内容头部 -->
            <div class="content-header">
                <h2 class="content-title">订单列表</h2>
                <div style="display: flex; gap: 8px;">
                    <button class="action-btn" onclick="toggleAutoRefresh()" id="autoRefreshBtn">
                        <i class="fas fa-clock"></i>
                        自动刷新
                    </button>
                    <button class="action-btn" onclick="showAdvancedFilter()">
                        <i class="fas fa-sliders-h"></i>
                        高级筛选
                    </button>
                </div>
            </div>

            <!-- 状态标签页 -->
            <div class="filter-tabs">
                <button class="filter-tab active" onclick="filterByStatus('', this)">全部订单 (1234)</button>
                <button class="filter-tab" onclick="filterByStatus('pending', this)">待支付 (12)</button>
                <button class="filter-tab" onclick="filterByStatus('paid', this)">待发货 (89)</button>
                <button class="filter-tab" onclick="filterByStatus('shipped', this)">已发货 (156)</button>
                <button class="filter-tab" onclick="filterByStatus('refunding', this)">退款中 (8)</button>
                <button class="filter-tab" onclick="filterByStatus('cancelled', this)">已取消 (23)</button>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">订单号</label>
                        <input type="text" class="filter-input" placeholder="请输入订单号" id="orderSearch">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">用户ID</label>
                        <input type="text" class="filter-input" placeholder="请输入用户ID" id="userSearch">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">支付方式</label>
                        <select class="filter-select" id="paymentFilter">
                            <option value="">全部方式</option>
                            <option value="wechat">微信支付</option>
                            <option value="alipay">支付宝</option>
                            <option value="wallet">余额支付</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">时间范围</label>
                        <div style="display: flex; gap: 8px; align-items: center;">
                            <input type="date" class="filter-input" style="width: 140px;" id="startDate">
                            <span>至</span>
                            <input type="date" class="filter-input" style="width: 140px;" id="endDate">
                        </div>
                    </div>
                    <button class="action-btn primary" style="align-self: flex-end;" onclick="searchOrders()">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                    <button class="action-btn" style="align-self: flex-end;" onclick="resetFilters()">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                </div>
            </div>

            <!-- 批量操作栏 -->
            <div id="bulkActions" class="bulk-actions">
                <div class="bulk-info">
                    已选择 <span id="selectedCount">0</span> 个订单
                </div>
                <div class="bulk-buttons">
                    <button class="action-btn primary" onclick="batchShip()">
                        <i class="fas fa-shipping-fast"></i>
                        批量发货
                    </button>
                    <button class="action-btn warning" onclick="batchExport()">
                        <i class="fas fa-download"></i>
                        导出选中
                    </button>
                    <button class="action-btn danger" onclick="batchCancel()">
                        <i class="fas fa-times"></i>
                        批量取消
                    </button>
                </div>
            </div>

            <!-- 订单表格 -->
            <div style="overflow-x: auto;">
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                            <th>订单号</th>
                            <th>客户信息</th>
                            <th>商品</th>
                            <th>金额</th>
                            <th>状态</th>
                            <th>支付方式</th>
                            <th>物流信息</th>
                            <th>下单时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                        <!-- 订单数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 <span id="pageStart">1</span>-<span id="pageEnd">20</span> 条，共 <span id="totalCount">1,234</span> 条记录 | 总金额: ¥<span id="totalAmount">1,234,567.89</span>
                </div>
                <div class="pagination-controls" id="paginationControls">
                    <!-- 分页按钮将通过JavaScript生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 批量发货弹窗 -->
    <div id="batchShipModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">批量发货</h3>
                <button class="modal-close" onclick="closeModal('batchShipModal')">&times;</button>
            </div>
            
            <div class="form-group">
                <label class="form-label">快递公司</label>
                <select class="form-select" id="courierCompany">
                    <option value="SF">顺丰速运</option>
                    <option value="YTO">圆通速递</option>
                    <option value="ZTO">中通快递</option>
                    <option value="STO">申通快递</option>
                    <option value="JD">京东快递</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">运单号生成规则</label>
                <input type="text" class="form-input" id="trackingRule" value="SF{DATE}{RANDOM4}" placeholder="例: SF{DATE}{RANDOM4}">
                <small style="color: #64748b; font-size: 12px;">
                    可用变量: {DATE}=日期, {RANDOM4}=4位随机数, {ORDER}=订单号后4位
                </small>
            </div>
            
            <div class="form-group">
                <label class="form-label">发货仓库</label>
                <select class="form-select" id="warehouse">
                    <option value="main">主仓库（北京）</option>
                    <option value="backup">备用仓库（上海）</option>
                    <option value="third">第三方仓库（广州）</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">发货备注</label>
                <textarea class="form-textarea" id="shipNote" placeholder="请输入发货备注"></textarea>
            </div>
            
            <div class="modal-actions">
                <button class="btn secondary" onclick="closeModal('batchShipModal')">取消</button>
                <button class="btn primary" onclick="confirmBatchShip()">确认发货</button>
            </div>
        </div>
    </div>

    <!-- 物流信息弹窗 -->
    <div id="logisticsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">物流信息</h3>
                <button class="modal-close" onclick="closeModal('logisticsModal')">&times;</button>
            </div>
            
            <div id="logisticsContent">
                <!-- 物流信息内容 -->
            </div>
            
            <div class="modal-actions">
                <button class="btn primary" onclick="closeModal('logisticsModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 退款审核弹窗 -->
    <div id="refundModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">退款审核</h3>
                <button class="modal-close" onclick="closeModal('refundModal')">&times;</button>
            </div>
            
            <div id="refundContent">
                <!-- 退款审核内容 -->
            </div>
            
            <div class="modal-actions">
                <button class="btn secondary" onclick="closeModal('refundModal')">取消</button>
                <button class="btn primary" onclick="confirmRefund()">确认审核</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedOrders = new Set();
        let currentPage = 1;
        let pageSize = 20;
        let autoRefreshTimer = null;
        let currentFilter = {};

        // 示例订单数据
        const sampleOrders = [
            {
                id: 'OD202312150001',
                customer: { name: '张小明', id: 12345, avatar: '../../assets/images/avatar-1.jpg' },
                products: [{ name: 'iPhone 14 Pro Max', image: '../../assets/images/iphone-1.jpg', count: 3 }],
                amount: 8999.00,
                status: 'paid',
                payment: '微信支付',
                logistics: { company: '顺丰', number: 'SF123456789', status: '待发货' },
                createTime: '2023-12-15 14:32',
                isRisk: true
            },
            {
                id: 'OD202312150002',
                customer: { name: '李小华', id: 12346, avatar: '../../assets/images/avatar-2.jpg' },
                products: [{ name: 'Nike Air Max 270', image: '../../assets/images/nike-shoes.jpg', count: 1 }],
                amount: 1299.00,
                status: 'refunding',
                payment: '支付宝',
                logistics: { company: '圆通', number: 'YT987654321', status: '运输中' },
                createTime: '2023-12-15 12:15',
                isRisk: false
            },
            {
                id: 'OD202312150003',
                customer: { name: '王小红', id: 12347, avatar: '../../assets/images/avatar-3.jpg' },
                products: [{ name: '无印良品羽绒被', image: '../../assets/images/muji-pillow.jpg', count: 2 }],
                amount: 1798.00,
                status: 'shipped',
                payment: '余额支付',
                logistics: { company: '中通', number: 'ZT456789123', status: '已签收' },
                createTime: '2023-12-15 10:45',
                isRisk: false
            }
        ];

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadOrders();
            initPagination();
            setDefaultDates();
        });

        // 设置默认日期
        function setDefaultDates() {
            const today = new Date();
            const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            
            document.getElementById('startDate').value = lastWeek.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }

        // 加载订单数据
        function loadOrders() {
            const tbody = document.getElementById('ordersTableBody');
            tbody.innerHTML = '';
            
            sampleOrders.forEach(order => {
                const row = createOrderRow(order);
                tbody.appendChild(row);
            });
        }

        // 创建订单行
        function createOrderRow(order) {
            const row = document.createElement('tr');
            
            const statusClass = `status-${order.status}`;
            const statusText = getStatusText(order.status);
            
            row.innerHTML = `
                <td><input type="checkbox" class="order-checkbox" data-order="${order.id}" onchange="updateBulkActions()"></td>
                <td>
                    <span class="order-id" onclick="viewOrderDetail('${order.id}')">${order.id}</span>
                    ${order.isRisk ? '<span class="risk-flag">异常</span>' : ''}
                </td>
                <td>
                    <div class="customer-info">
                        <div class="customer-avatar" style="background-image: url('${order.customer.avatar}'); background-size: cover;"></div>
                        <div>
                            <div class="customer-name">${order.customer.name}</div>
                            <div class="customer-id">UID: ${order.customer.id}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="product-summary">
                        <img src="${order.products[0].image}" alt="商品" class="product-image">
                        <div>
                            <div>${order.products[0].name}</div>
                            <div class="product-count">${order.products[0].count > 1 ? `等${order.products[0].count}件商品` : '1件商品'}</div>
                        </div>
                    </div>
                </td>
                <td><span class="amount-value">¥${order.amount.toFixed(2)}</span></td>
                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                <td>${order.payment}</td>
                <td>
                    ${order.logistics.number ? `
                        <div style="font-size: 12px;">
                            <div>${order.logistics.company}: ${order.logistics.number}</div>
                            <div style="color: #64748b;">${order.logistics.status}</div>
                        </div>
                    ` : '-'}
                </td>
                <td>${order.createTime}</td>
                <td>
                    <div class="action-buttons">
                        ${getActionButtons(order)}
                    </div>
                </td>
            `;
            
            return row;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待支付',
                'paid': '已支付',
                'shipped': '已发货',
                'delivered': '已完成',
                'refunding': '退款中',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        // 获取操作按钮
        function getActionButtons(order) {
            switch(order.status) {
                case 'paid':
                    return `
                        <button class="btn-sm primary" onclick="shipOrder('${order.id}')">发货</button>
                        <button class="btn-sm" onclick="viewOrderDetail('${order.id}')">详情</button>
                    `;
                case 'shipped':
                    return `
                        <button class="btn-sm" onclick="trackOrder('${order.id}')">追踪</button>
                        <button class="btn-sm" onclick="viewOrderDetail('${order.id}')">详情</button>
                    `;
                case 'refunding':
                    return `
                        <button class="btn-sm warning" onclick="processRefund('${order.id}')">处理退款</button>
                        <button class="btn-sm" onclick="viewOrderDetail('${order.id}')">详情</button>
                    `;
                default:
                    return `
                        <button class="btn-sm" onclick="viewOrderDetail('${order.id}')">详情</button>
                    `;
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.order-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                if (selectAll.checked) {
                    selectedOrders.add(checkbox.dataset.order);
                } else {
                    selectedOrders.delete(checkbox.dataset.order);
                }
            });
            
            updateBulkActions();
        }

        // 更新批量操作栏
        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.order-checkbox:checked');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            
            selectedOrders.clear();
            checkboxes.forEach(cb => selectedOrders.add(cb.dataset.order));
            
            if (selectedOrders.size > 0) {
                bulkActions.classList.add('show');
                selectedCount.textContent = selectedOrders.size;
            } else {
                bulkActions.classList.remove('show');
            }
        }

        // 状态筛选
        function filterByStatus(status, element) {
            const tabs = document.querySelectorAll('.filter-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            element.classList.add('active');
            
            currentFilter.status = status;
            loadOrders(); // 重新加载订单数据
        }

        // 搜索订单
        function searchOrders() {
            const orderNum = document.getElementById('orderSearch').value;
            const userId = document.getElementById('userSearch').value;
            const payment = document.getElementById('paymentFilter').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            currentFilter = {
                orderNum,
                userId,
                payment,
                startDate,
                endDate
            };
            
            loadOrders(); // 重新加载订单数据
            alert('搜索完成！');
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('orderSearch').value = '';
            document.getElementById('userSearch').value = '';
            document.getElementById('paymentFilter').value = '';
            setDefaultDates();
            
            currentFilter = {};
            loadOrders();
        }

        // 批量发货
        function batchShip() {
            if (selectedOrders.size === 0) {
                alert('请先选择要发货的订单');
                return;
            }
            document.getElementById('batchShipModal').classList.add('show');
        }

        // 确认批量发货
        function confirmBatchShip() {
            const courier = document.getElementById('courierCompany').value;
            const rule = document.getElementById('trackingRule').value;
            const warehouse = document.getElementById('warehouse').value;
            const note = document.getElementById('shipNote').value;
            
            if (!courier || !rule) {
                alert('请填写完整信息');
                return;
            }
            
            // 模拟批量发货处理
            alert(`批量发货成功！\n共处理 ${selectedOrders.size} 个订单\n快递公司: ${courier}\n发货仓库: ${warehouse}`);
            
            closeModal('batchShipModal');
            selectedOrders.clear();
            updateBulkActions();
            loadOrders();
        }

        // 单个发货
        function shipOrder(orderId) {
            if (confirm(`确定要发货订单 ${orderId} 吗？`)) {
                alert('发货成功！');
                loadOrders();
            }
        }

        // 追踪订单
        function trackOrder(orderId) {
            document.getElementById('logisticsModal').classList.add('show');
            
            // 模拟物流信息
            const content = document.getElementById('logisticsContent');
            content.innerHTML = `
                <div style="margin-bottom: 16px;">
                    <h4>订单号: ${orderId}</h4>
                    <p>快递公司: 顺丰速运</p>
                    <p>运单号: SF123456789</p>
                </div>
                
                <div style="background: #f8fafc; padding: 16px; border-radius: 8px;">
                    <h5 style="margin: 0 0 12px 0;">物流轨迹</h5>
                    <div style="border-left: 2px solid #e2e8f0; padding-left: 16px;">
                        <div style="margin-bottom: 12px;">
                            <div style="font-weight: 600; color: #1e293b;">2023-12-15 16:30</div>
                            <div style="color: #64748b;">快件已送达，签收人：本人签收</div>
                        </div>
                        <div style="margin-bottom: 12px;">
                            <div style="font-weight: 600; color: #1e293b;">2023-12-15 14:20</div>
                            <div style="color: #64748b;">快件正在派送中，派送员：张师傅</div>
                        </div>
                        <div style="margin-bottom: 12px;">
                            <div style="font-weight: 600; color: #1e293b;">2023-12-15 08:15</div>
                            <div style="color: #64748b;">快件已到达目的地网点</div>
                        </div>
                        <div style="margin-bottom: 12px;">
                            <div style="font-weight: 600; color: #1e293b;">2023-12-14 20:30</div>
                            <div style="color: #64748b;">快件已发出</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 处理退款
        function processRefund(orderId) {
            document.getElementById('refundModal').classList.add('show');
            
            // 模拟退款信息
            const content = document.getElementById('refundContent');
            content.innerHTML = `
                <div style="background: #fef3c7; padding: 12px; border-radius: 8px; margin-bottom: 16px;">
                    <div style="font-size: 18px; font-weight: 700; color: #92400e;">退款金额: ¥1,299.00</div>
                    <div style="font-size: 14px; color: #92400e; margin-top: 4px;">退款原因: 商品质量问题</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">订单信息</label>
                    <div style="background: #f8fafc; padding: 12px; border-radius: 8px; border: 1px solid #e2e8f0;">
                        <p><strong>订单号:</strong> ${orderId}</p>
                        <p><strong>商品:</strong> Nike Air Max 270 运动鞋</p>
                        <p><strong>购买时间:</strong> 2023-12-15 12:15</p>
                        <p><strong>支付方式:</strong> 支付宝</p>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">审核决定</label>
                    <select class="form-select" id="refundDecision">
                        <option value="">请选择审核结果</option>
                        <option value="approve">同意退款</option>
                        <option value="partial">部分退款</option>
                        <option value="reject">拒绝退款</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">审核备注</label>
                    <textarea class="form-textarea" id="refundNote" placeholder="请输入审核意见"></textarea>
                </div>
            `;
        }

        // 确认退款审核
        function confirmRefund() {
            const decision = document.getElementById('refundDecision').value;
            const note = document.getElementById('refundNote').value;
            
            if (!decision || !note) {
                alert('请填写完整信息');
                return;
            }
            
            alert(`退款审核完成: ${decision === 'approve' ? '同意退款' : decision === 'partial' ? '部分退款' : '拒绝退款'}`);
            closeModal('refundModal');
            loadOrders();
        }

        // 查看订单详情
        function viewOrderDetail(orderId) {
            // 跳转到订单详情页面
            window.open(`order-detail.html?id=${orderId}`, '_blank');
        }

        // 自动刷新
        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            
            if (autoRefreshTimer) {
                clearInterval(autoRefreshTimer);
                autoRefreshTimer = null;
                btn.style.backgroundColor = '';
                btn.style.color = '';
                alert('自动刷新已关闭');
            } else {
                autoRefreshTimer = setInterval(() => {
                    loadOrders();
                    console.log('自动刷新订单数据...');
                }, 30000); // 30秒刷新一次
                
                btn.style.backgroundColor = '#10b981';
                btn.style.color = 'white';
                alert('自动刷新已开启（30秒间隔）');
            }
        }

        // 初始化分页
        function initPagination() {
            const controls = document.getElementById('paginationControls');
            controls.innerHTML = `
                <button class="page-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>
                <button class="page-btn active">1</button>
                <button class="page-btn" onclick="changePage(2)">2</button>
                <button class="page-btn" onclick="changePage(3)">3</button>
                <button class="page-btn" onclick="changePage(${currentPage + 1})">下一页</button>
            `;
        }

        // 切换页面
        function changePage(page) {
            if (page < 1) return;
            currentPage = page;
            loadOrders();
            initPagination();
        }

        // 其他功能
        function exportOrders() { alert('导出订单功能'); }
        function showBatchShip() { batchShip(); }
        function showLogistics() { alert('物流管理功能'); }
        function refreshData() { loadOrders(); alert('数据已刷新'); }
        function showAdvancedFilter() { alert('高级筛选功能'); }
        function batchExport() { alert('批量导出功能'); }
        function batchCancel() { 
            if (confirm(`确定要取消选中的 ${selectedOrders.size} 个订单吗？`)) {
                alert('批量取消成功');
                selectedOrders.clear();
                updateBulkActions();
                loadOrders();
            }
        }

        // 关闭弹窗
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html> 