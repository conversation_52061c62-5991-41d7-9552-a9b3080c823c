// 订单管理JavaScript功能

class OrderManagement {
    constructor() {
        this.orders = [];
        this.filteredOrders = [];
        this.currentFilter = 'all';
        this.selectedOrders = new Set();
        this.init();
    }
    
    init() {
        this.loadOrderData();
        this.renderOrders();
        this.setupEventListeners();
    }
    
    // 加载订单数据
    loadOrderData() {
        // 模拟订单数据
        this.orders = [
            {
                id: 'TS2024001',
                customer: {
                    name: '<PERSON>',
                    email: '<EMAIL>',
                    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b332844c?w=32&h=32&fit=crop'
                },
                product: {
                    name: 'iPhone 15 Pro Max',
                    sku: 'SKU001',
                    image: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=40&h=40&fit=crop',
                    quantity: 1
                },
                amount: 9999.00,
                paymentMethod: 'alipay',
                status: 'delivered',
                createdAt: '2024-03-10 10:30',
                updatedAt: '2024-03-12 15:45'
            },
            {
                id: 'TS2024002',
                customer: {
                    name: '<PERSON>',
                    email: '<EMAIL>',
                    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop'
                },
                product: {
                    name: 'MacBook Pro 16"',
                    sku: 'SKU002',
                    image: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=40&h=40&fit=crop',
                    quantity: 1
                },
                amount: 15999.00,
                paymentMethod: 'crypto',
                status: 'shipped',
                createdAt: '2024-03-09 14:20',
                updatedAt: '2024-03-11 09:15'
            },
            {
                id: 'TS2024003',
                customer: {
                    name: 'Charlie Li',
                    email: '<EMAIL>',
                    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop'
                },
                product: {
                    name: 'AirPods Pro 2',
                    sku: 'SKU003',
                    image: 'https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=40&h=40&fit=crop',
                    quantity: 2
                },
                amount: 3798.00,
                paymentMethod: 'wechat',
                status: 'paid',
                createdAt: '2024-03-08 16:45',
                updatedAt: '2024-03-08 16:47'
            },
            {
                id: 'TS2024004',
                customer: {
                    name: 'David Zhang',
                    email: '<EMAIL>',
                    avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=32&h=32&fit=crop'
                },
                product: {
                    name: 'Apple Watch Ultra',
                    sku: 'SKU004',
                    image: 'https://images.unsplash.com/photo-*************-2f02dc6ca35d?w=40&h=40&fit=crop',
                    quantity: 1
                },
                amount: 6299.00,
                paymentMethod: 'bank',
                status: 'pending',
                createdAt: '2024-03-07 11:30',
                updatedAt: '2024-03-07 11:30'
            },
            {
                id: 'TS2024005',
                customer: {
                    name: 'Eve Johnson',
                    email: '<EMAIL>',
                    avatar: 'https://images.unsplash.com/photo-*************-53994a69daeb?w=32&h=32&fit=crop'
                },
                product: {
                    name: 'iPad Pro 12.9"',
                    sku: 'SKU005',
                    image: 'https://images.unsplash.com/photo-**********-0df4b3ffc6b0?w=40&h=40&fit=crop',
                    quantity: 1
                },
                amount: 8799.00,
                paymentMethod: 'alipay',
                status: 'cancelled',
                createdAt: '2024-03-06 09:15',
                updatedAt: '2024-03-06 20:30'
            }
        ];
        
        this.filteredOrders = [...this.orders];
    }
    
    // 渲染订单列表
    renderOrders() {
        const tbody = document.getElementById('ordersTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = this.filteredOrders.map(order => `
            <tr>
                <td><input type="checkbox" class="order-checkbox" data-order-id="${order.id}"></td>
                <td>
                    <span class="order-id">${order.id}</span>
                </td>
                <td>
                    <div class="customer-info">
                        <img src="${order.customer.avatar}" alt="${order.customer.name}" class="customer-avatar">
                        <div class="customer-details">
                            <div class="customer-name">${order.customer.name}</div>
                            <div class="customer-email">${order.customer.email}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="product-info">
                        <img src="${order.product.image}" alt="${order.product.name}" class="product-image">
                        <div class="product-details">
                            <div class="product-name">${order.product.name}</div>
                            <div class="product-sku">SKU: ${order.product.sku} | 数量: ${order.product.quantity}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="amount-value">¥${order.amount.toFixed(2)}</div>
                </td>
                <td>
                    <span class="payment-method">${this.getPaymentMethodText(order.paymentMethod)}</span>
                </td>
                <td>
                    <span class="status-badge status-${order.status}">${this.getStatusText(order.status)}</span>
                </td>
                <td>${order.createdAt}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-icon-btn primary" onclick="orderManager.viewOrder('${order.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-icon-btn" onclick="orderManager.editOrder('${order.id}')" title="编辑订单">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${this.getStatusActions(order)}
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    // 获取支付方式文本
    getPaymentMethodText(method) {
        const methods = {
            'alipay': '支付宝',
            'wechat': '微信支付',
            'crypto': '加密货币',
            'bank': '银行转账'
        };
        return methods[method] || method;
    }
    
    // 获取状态文本
    getStatusText(status) {
        const statuses = {
            'pending': '待付款',
            'paid': '已付款',
            'shipped': '配送中',
            'delivered': '已送达',
            'cancelled': '已取消',
            'refunded': '已退款'
        };
        return statuses[status] || status;
    }
    
    // 获取状态相关操作按钮
    getStatusActions(order) {
        switch(order.status) {
            case 'pending':
                return `
                    <button class="action-icon-btn success" onclick="orderManager.confirmPayment('${order.id}')" title="确认付款">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="action-icon-btn danger" onclick="orderManager.cancelOrder('${order.id}')" title="取消订单">
                        <i class="fas fa-times"></i>
                    </button>
                `;
            case 'paid':
                return `
                    <button class="action-icon-btn" onclick="orderManager.shipOrder('${order.id}')" title="发货">
                        <i class="fas fa-truck"></i>
                    </button>
                    <button class="action-icon-btn danger" onclick="orderManager.refundOrder('${order.id}')" title="退款">
                        <i class="fas fa-undo"></i>
                    </button>
                `;
            case 'shipped':
                return `
                    <button class="action-icon-btn" onclick="orderManager.trackOrder('${order.id}')" title="物流跟踪">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                    <button class="action-icon-btn success" onclick="orderManager.confirmDelivery('${order.id}')" title="确认送达">
                        <i class="fas fa-check-circle"></i>
                    </button>
                `;
            case 'delivered':
                return `
                    <button class="action-icon-btn" onclick="orderManager.viewReview('${order.id}')" title="查看评价">
                        <i class="fas fa-star"></i>
                    </button>
                `;
            case 'cancelled':
            case 'refunded':
                return `
                    <button class="action-icon-btn" onclick="orderManager.viewReason('${order.id}')" title="查看原因">
                        <i class="fas fa-info-circle"></i>
                    </button>
                `;
            default:
                return '';
        }
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 订单选择事件
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('order-checkbox')) {
                const orderId = e.target.getAttribute('data-order-id');
                if (e.target.checked) {
                    this.selectedOrders.add(orderId);
                } else {
                    this.selectedOrders.delete(orderId);
                }
            }
        });
    }
    
    // 订单操作方法
    viewOrder(orderId) {
        showNotification(`查看订单 ${orderId} 详情`, 'info');
    }
    
    editOrder(orderId) {
        showNotification(`编辑订单 ${orderId}`, 'info');
    }
    
    confirmPayment(orderId) {
        if (confirm('确认该订单已付款？')) {
            this.updateOrderStatus(orderId, 'paid');
            showNotification(`订单 ${orderId} 已确认付款`, 'success');
        }
    }
    
    cancelOrder(orderId) {
        if (confirm('确定要取消这个订单吗？')) {
            this.updateOrderStatus(orderId, 'cancelled');
            showNotification(`订单 ${orderId} 已取消`, 'success');
        }
    }
    
    shipOrder(orderId) {
        if (confirm('确认发货？')) {
            this.updateOrderStatus(orderId, 'shipped');
            showNotification(`订单 ${orderId} 已发货`, 'success');
        }
    }
    
    refundOrder(orderId) {
        if (confirm('确定要退款吗？')) {
            this.updateOrderStatus(orderId, 'refunded');
            showNotification(`订单 ${orderId} 已退款`, 'success');
        }
    }
    
    trackOrder(orderId) {
        showNotification(`查看订单 ${orderId} 物流信息`, 'info');
    }
    
    confirmDelivery(orderId) {
        if (confirm('确认订单已送达？')) {
            this.updateOrderStatus(orderId, 'delivered');
            showNotification(`订单 ${orderId} 已确认送达`, 'success');
        }
    }
    
    viewReview(orderId) {
        showNotification(`查看订单 ${orderId} 的客户评价`, 'info');
    }
    
    viewReason(orderId) {
        showNotification(`查看订单 ${orderId} 的取消/退款原因`, 'info');
    }
    
    // 更新订单状态
    updateOrderStatus(orderId, newStatus) {
        const orderIndex = this.orders.findIndex(order => order.id === orderId);
        if (orderIndex !== -1) {
            this.orders[orderIndex].status = newStatus;
            this.orders[orderIndex].updatedAt = new Date().toLocaleString('zh-CN');
            this.applyCurrentFilter();
            this.renderOrders();
        }
    }
    
    // 应用当前过滤条件
    applyCurrentFilter() {
        if (this.currentFilter === 'all') {
            this.filteredOrders = [...this.orders];
        } else {
            this.filteredOrders = this.orders.filter(order => order.status === this.currentFilter);
        }
    }
}

// 全局函数
function filterByStatus(status) {
    const tabs = document.querySelectorAll('.filter-tab');
    tabs.forEach(tab => tab.classList.remove('active'));
    event.target.classList.add('active');
    
    orderManager.currentFilter = status;
    orderManager.applyCurrentFilter();
    orderManager.renderOrders();
    
    showNotification(`已过滤到 ${status === 'all' ? '全部' : orderManager.getStatusText(status)} 订单`, 'info');
}

function searchOrders(query) {
    if (query.trim()) {
        orderManager.filteredOrders = orderManager.orders.filter(order => 
            order.id.toLowerCase().includes(query.toLowerCase()) ||
            order.customer.name.toLowerCase().includes(query.toLowerCase()) ||
            order.customer.email.toLowerCase().includes(query.toLowerCase()) ||
            order.product.name.toLowerCase().includes(query.toLowerCase())
        );
    } else {
        orderManager.applyCurrentFilter();
    }
    orderManager.renderOrders();
}

function filterByOrderStatus(status) {
    if (status) {
        orderManager.filteredOrders = orderManager.orders.filter(order => order.status === status);
    } else {
        orderManager.applyCurrentFilter();
    }
    orderManager.renderOrders();
}

function filterByPayment(payment) {
    if (payment) {
        orderManager.filteredOrders = orderManager.orders.filter(order => order.paymentMethod === payment);
    } else {
        orderManager.applyCurrentFilter();
    }
    orderManager.renderOrders();
}

function filterByTime(time) {
    if (time) {
        const now = new Date();
        let startDate;
        
        switch(time) {
            case 'today':
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                break;
            case 'week':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'month':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                break;
            case 'quarter':
                startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
                break;
        }
        
        orderManager.filteredOrders = orderManager.orders.filter(order => {
            const orderDate = new Date(order.createdAt);
            return orderDate >= startDate;
        });
    } else {
        orderManager.applyCurrentFilter();
    }
    orderManager.renderOrders();
}

function filterByAmount(range) {
    if (range) {
        orderManager.filteredOrders = orderManager.orders.filter(order => {
            switch(range) {
                case '0-100':
                    return order.amount <= 100;
                case '100-500':
                    return order.amount > 100 && order.amount <= 500;
                case '500-1000':
                    return order.amount > 500 && order.amount <= 1000;
                case '1000+':
                    return order.amount > 1000;
                default:
                    return true;
            }
        });
    } else {
        orderManager.applyCurrentFilter();
    }
    orderManager.renderOrders();
}

function refreshOrders() {
    showNotification('正在刷新订单数据...', 'info');
    setTimeout(() => {
        orderManager.loadOrderData();
        orderManager.renderOrders();
        showNotification('订单数据已更新！', 'success');
    }, 1500);
}

function showFilters() {
    showNotification('高级过滤器功能开发中...', 'info');
}

function exportOrders() {
    showNotification('正在导出订单数据...', 'info');
    setTimeout(() => {
        showNotification('订单数据导出成功！', 'success');
    }, 2000);
}

function bulkProcess() {
    if (orderManager.selectedOrders.size === 0) {
        showNotification('请先选择要处理的订单', 'warning');
        return;
    }
    showNotification(`批量处理 ${orderManager.selectedOrders.size} 个订单`, 'info');
}

function createOrder() {
    showNotification('创建订单功能开发中...', 'info');
}

function toggleSelectAll(checked) {
    document.querySelectorAll('.order-checkbox').forEach(cb => {
        cb.checked = checked;
        const orderId = cb.getAttribute('data-order-id');
        if (checked) {
            orderManager.selectedOrders.add(orderId);
        } else {
            orderManager.selectedOrders.delete(orderId);
        }
    });
}

// 通知系统
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        color: white;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        max-width: 350px;
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        background-color: ${getNotificationColor(type)};
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getNotificationColor(type) {
    const colors = {
        'success': '#10b981',
        'error': '#ef4444',
        'warning': '#f59e0b',
        'info': '#3b82f6'
    };
    return colors[type] || colors.info;
}

// 初始化订单管理
let orderManager;
document.addEventListener('DOMContentLoaded', function() {
    orderManager = new OrderManagement();
}); 