<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>订单管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="../../assets/css/header-button-fix.css">
    <link rel="stylesheet" href="../../assets/css/search-button-fix.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="../../assets/js/image-error-handler.js"></script>
    <style>
        body { margin: 0; padding: 0; font-family: 'Inter', sans-serif; background: #f8fafc; }
        .order-page { padding: 24px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; flex-wrap: wrap; gap: 16px; }
        .page-title { font-size: 28px; font-weight: 700; color: #1e293b; margin: 0; }
        .header-actions { display: flex; gap: 12px; flex-wrap: wrap; }
        .action-btn { padding: 10px 20px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 8px; text-decoration: none; }
        .action-btn:hover { background: #f1f5f9; border-color: #cbd5e1; }
        .action-btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .action-btn.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        
        /* 统计卡片 */
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 24px; }
        .stat-card { background: white; padding: 20px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .stat-title { font-size: 14px; font-weight: 500; color: #64748b; margin-bottom: 8px; }
        .stat-value { font-size: 24px; font-weight: 700; color: #1e293b; margin-bottom: 4px; }
        .stat-desc { font-size: 12px; color: #64748b; }
        
        /* 主内容区 */
        .main-content { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .content-header { padding: 20px 24px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 12px; }
        .content-title { font-size: 18px; font-weight: 600; color: #1e293b; margin: 0; }
        
        /* 筛选标签页 */
        .filter-tabs { display: flex; padding: 0 24px; border-bottom: 1px solid #e2e8f0; overflow-x: auto; }
        .filter-tab { padding: 12px 16px; border: none; background: none; color: #64748b; font-size: 14px; cursor: pointer; border-bottom: 2px solid transparent; transition: all 0.2s; white-space: nowrap; }
        .filter-tab.active { color: #3b82f6; border-bottom-color: #3b82f6; }
        
        /* 筛选区域 */
        .filter-section { padding: 20px 24px; border-bottom: 1px solid #e2e8f0; background: #f8fafc; }
        .filter-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; align-items: end; }
        .filter-group { display: flex; flex-direction: column; gap: 4px; }
        .filter-label { font-size: 12px; font-weight: 500; color: #64748b; }
        .filter-input, .filter-select { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; background: white; }
        
        /* 批量操作栏 */
        .bulk-actions { padding: 16px 24px; background: #eff6ff; border-bottom: 1px solid #e2e8f0; display: none; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 12px; }
        .bulk-actions.show { display: flex; }
        .bulk-info { font-size: 14px; color: #1e40af; }
        .bulk-buttons { display: flex; gap: 8px; flex-wrap: wrap; }
        
        /* 表格样式 */
        .table-container { overflow-x: auto; }
        .orders-table { width: 100%; border-collapse: collapse; min-width: 1200px; }
        .orders-table th, .orders-table td { padding: 12px 16px; text-align: left; border-bottom: 1px solid #f1f5f9; vertical-align: middle; }
        .orders-table th { background: #f8fafc; font-weight: 600; color: #374151; position: sticky; top: 0; font-size: 12px; z-index: 10; }
        .order-checkbox { width: 16px; height: 16px; }
        .order-id { font-family: monospace; font-weight: 600; color: #3b82f6; cursor: pointer; text-decoration: none; }
        .order-id:hover { text-decoration: underline; }
        .customer-info { display: flex; align-items: center; gap: 8px; min-width: 120px; }
        .customer-avatar { width: 32px; height: 32px; border-radius: 50%; background: #f1f5f9; flex-shrink: 0; }
        .customer-name { font-weight: 500; color: #1e293b; font-size: 14px; }
        .customer-id { font-size: 12px; color: #64748b; }
        .product-info { display: flex; align-items: center; gap: 8px; max-width: 200px; }
        .product-image { width: 40px; height: 40px; border-radius: 6px; object-fit: cover; background: #f1f5f9; flex-shrink: 0; }
        .product-name { font-size: 14px; color: #1e293b; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .product-count { font-size: 12px; color: #64748b; }
        
        /* 状态标签 */
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; white-space: nowrap; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-paid { background: #dbeafe; color: #1e40af; }
        .status-shipped { background: #e0e7ff; color: #3730a3; }
        .status-delivered { background: #dcfce7; color: #166534; }
        .status-refunding { background: #fed7aa; color: #ea580c; }
        .status-cancelled { background: #fecaca; color: #dc2626; }
        .risk-flag { background: #fef2f2; color: #dc2626; padding: 2px 6px; border-radius: 4px; font-size: 10px; font-weight: 600; margin-left: 4px; }
        
        /* 金额样式 */
        .amount-value { font-weight: 600; color: #1e293b; font-family: monospace; }
        
        /* 操作按钮 */
        .action-buttons { display: flex; gap: 4px; flex-wrap: wrap; }
        .btn-sm { padding: 6px 12px; border: 1px solid #e2e8f0; border-radius: 4px; background: white; color: #64748b; font-size: 12px; cursor: pointer; transition: all 0.2s; white-space: nowrap; }
        .btn-sm:hover { background: #f1f5f9; }
        .btn-sm.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .btn-sm.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .btn-sm.danger { background: #ef4444; color: white; border-color: #ef4444; }
        
        /* 物流信息 */
        .logistics-info { font-size: 12px; min-width: 120px; }
        .logistics-company { color: #1e293b; font-weight: 500; }
        .logistics-status { color: #64748b; margin-top: 2px; }
        
        /* 分页 */
        .pagination { padding: 20px 24px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 16px; }
        .pagination-info { font-size: 14px; color: #64748b; }
        .pagination-controls { display: flex; gap: 8px; }
        .page-btn { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 4px; background: white; color: #64748b; cursor: pointer; font-size: 14px; }
        .page-btn.active { background: #3b82f6; color: white; border-color: #3b82f6; }
        .page-btn:disabled { opacity: 0.5; cursor: not-allowed; }
        
        /* 响应式适配 */
        @media (max-width: 768px) {
            .order-page { padding: 16px; }
            .page-header { flex-direction: column; align-items: stretch; }
            .stats-grid { grid-template-columns: 1fr 1fr; gap: 12px; }
            .filter-row { grid-template-columns: 1fr; gap: 12px; }
            .orders-table { font-size: 12px; }
            .orders-table th, .orders-table td { padding: 8px 4px; }
            .customer-info, .product-info { flex-direction: column; align-items: flex-start; }
        }
    </style>
</head>
<body>
    <div class="order-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">订单管理中心</h1>
            <div class="header-actions">
                <a href="../dashboard/index.html" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    仪表板
                </a>
                <a href="enhanced-index.html" class="action-btn">
                    <i class="fas fa-star"></i>
                    高级管理
                </a>
                <a href="b2b2c-order-management.html" class="action-btn">
                    <i class="fas fa-building"></i>
                    B2B2C订单
                </a>
                <a href="order-detail.html" class="action-btn">
                    <i class="fas fa-info-circle"></i>
                    订单详情
                </a>
                <button class="action-btn" onclick="exportOrders()">
                    <i class="fas fa-download"></i>
                    导出订单
                </button>
                <button class="action-btn warning" onclick="showBatchShip()">
                    <i class="fas fa-shipping-fast"></i>
                    批量发货
                </button>
                <button class="action-btn" onclick="showLogistics()">
                    <i class="fas fa-truck"></i>
                    物流追踪
                </button>
                <button class="action-btn primary" onclick="refreshData()">
                    <i class="fas fa-sync"></i>
                    刷新数据
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-title">今日订单</div>
                <div class="stat-value">234</div>
                <div class="stat-desc">相比昨日 +12.5%</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">待发货订单</div>
                <div class="stat-value" style="color: #f59e0b;">89</div>
                <div class="stat-desc">需要及时处理</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">今日销售额</div>
                <div class="stat-value" style="color: #10b981;">¥156,847</div>
                <div class="stat-desc">目标达成 89.2%</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">异常订单</div>
                <div class="stat-value" style="color: #ef4444;">5</div>
                <div class="stat-desc">需要风险审核</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 内容头部 -->
            <div class="content-header">
                <h2 class="content-title">订单列表</h2>
                <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                    <button class="action-btn" onclick="toggleAutoRefresh()" id="autoRefreshBtn">
                        <i class="fas fa-clock"></i>
                        自动刷新
                    </button>
                    <button class="action-btn" onclick="showAdvancedFilter()">
                        <i class="fas fa-sliders-h"></i>
                        高级筛选
                    </button>
                </div>
            </div>

            <!-- 状态标签页 -->
            <div class="filter-tabs">
                <button class="filter-tab active" onclick="filterByStatus('', this)">全部 (1234)</button>
                <button class="filter-tab" onclick="filterByStatus('pending', this)">待支付 (12)</button>
                <button class="filter-tab" onclick="filterByStatus('paid', this)">待发货 (89)</button>
                <button class="filter-tab" onclick="filterByStatus('shipped', this)">已发货 (156)</button>
                <button class="filter-tab" onclick="filterByStatus('delivered', this)">已完成 (967)</button>
                <button class="filter-tab" onclick="filterByStatus('refunding', this)">退款中 (8)</button>
                <button class="filter-tab" onclick="filterByStatus('cancelled', this)">已取消 (23)</button>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">订单号</label>
                        <input type="text" class="filter-input" placeholder="请输入订单号" id="orderSearch">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">用户ID/姓名</label>
                        <input type="text" class="filter-input" placeholder="请输入用户信息" id="userSearch">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">支付方式</label>
                        <select class="filter-select" id="paymentFilter">
                            <option value="">全部方式</option>
                            <option value="wechat">微信支付</option>
                            <option value="alipay">支付宝</option>
                            <option value="wallet">余额支付</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">开始日期</label>
                        <input type="date" class="filter-input" id="startDate">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">结束日期</label>
                        <input type="date" class="filter-input" id="endDate">
                    </div>
                    <div class="filter-group">
                        <button class="action-btn primary" onclick="searchOrders()">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                        <button class="action-btn" onclick="resetFilters()">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 批量操作栏 -->
            <div id="bulkActions" class="bulk-actions">
                <div class="bulk-info">
                    已选择 <span id="selectedCount">0</span> 个订单
                </div>
                <div class="bulk-buttons">
                    <button class="action-btn primary" onclick="batchShip()">
                        <i class="fas fa-shipping-fast"></i>
                        批量发货
                    </button>
                    <button class="action-btn warning" onclick="batchExport()">
                        <i class="fas fa-download"></i>
                        导出选中
                    </button>
                    <button class="action-btn danger" onclick="batchCancel()">
                        <i class="fas fa-times"></i>
                        批量取消
                    </button>
                </div>
            </div>

            <!-- 订单表格 -->
            <div class="table-container">
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th style="width: 40px;"><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                            <th style="width: 140px;">订单号</th>
                            <th style="width: 120px;">客户信息</th>
                            <th style="width: 200px;">商品信息</th>
                            <th style="width: 100px;">订单金额</th>
                            <th style="width: 80px;">订单状态</th>
                            <th style="width: 80px;">支付方式</th>
                            <th style="width: 120px;">物流信息</th>
                            <th style="width: 140px;">下单时间</th>
                            <th style="width: 120px;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                        <!-- 订单数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 <span id="pageStart">1</span>-<span id="pageEnd">20</span> 条，共 <span id="totalCount">1,234</span> 条记录 | 总金额: ¥<span id="totalAmount">1,234,567.89</span>
                </div>
                <div class="pagination-controls" id="paginationControls">
                    <!-- 分页按钮将通过JavaScript生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedOrders = new Set();
        let currentPage = 1;
        let pageSize = 20;
        let autoRefreshTimer = null;
        let currentFilter = {};

        // 示例订单数据
        const sampleOrders = [
            {
                id: 'OD202312150001',
                customer: { name: '张小明', id: 'U12345', avatar: '../../assets/images/avatar-default.png' },
                products: [{ name: 'iPhone 15 Pro Max 1TB 深空黑色', image: '../../assets/images/product-1.jpg', count: 1 }],
                amount: 9999.00,
                status: 'paid',
                payment: '微信支付',
                logistics: { company: '顺丰速运', number: 'SF202312150001', status: '待发货' },
                createTime: '2023-12-15 14:32:15',
                isRisk: true
            },
            {
                id: 'OD202312150002',
                customer: { name: '李小华', id: 'U12346', avatar: '../../assets/images/avatar-default.png' },
                products: [{ name: 'Nike Air Jordan 1 经典款运动鞋', image: '../../assets/images/product-2.jpg', count: 2 }],
                amount: 2598.00,
                status: 'refunding',
                payment: '支付宝',
                logistics: { company: '圆通速递', number: 'YT202312150002', status: '运输中' },
                createTime: '2023-12-15 12:15:30',
                isRisk: false
            },
            {
                id: 'OD202312150003',
                customer: { name: '王小红', id: 'U12347', avatar: '../../assets/images/avatar-default.png' },
                products: [{ name: '小米14 Ultra 白色 16GB+1TB', image: '../../assets/images/product-3.jpg', count: 1 }],
                amount: 6999.00,
                status: 'shipped',
                payment: '余额支付',
                logistics: { company: '中通快递', number: 'ZT202312150003', status: '已签收' },
                createTime: '2023-12-15 10:45:22',
                isRisk: false
            }
        ];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadOrders();
            initPagination();
            setDefaultDates();
        });

        // 设置默认日期
        function setDefaultDates() {
            const today = new Date();
            const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            
            document.getElementById('startDate').value = lastWeek.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }

        // 加载订单数据
        function loadOrders() {
            const tbody = document.getElementById('ordersTableBody');
            tbody.innerHTML = '';
            
            sampleOrders.forEach(order => {
                const row = createOrderRow(order);
                tbody.appendChild(row);
            });
        }

        // 创建订单行
        function createOrderRow(order) {
            const row = document.createElement('tr');
            
            const statusClass = `status-${order.status}`;
            const statusText = getStatusText(order.status);
            
            row.innerHTML = `
                <td><input type="checkbox" class="order-checkbox" data-order="${order.id}" onchange="updateBulkActions()"></td>
                <td>
                    <a href="#" class="order-id" onclick="viewOrderDetail('${order.id}')">${order.id}</a>
                    ${order.isRisk ? '<span class="risk-flag">异常</span>' : ''}
                </td>
                <td>
                    <div class="customer-info">
                        <div class="customer-avatar" style="background-image: url('${order.customer.avatar}'); background-size: cover;"></div>
                        <div>
                            <div class="customer-name">${order.customer.name}</div>
                            <div class="customer-id">${order.customer.id}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="product-info">
                        <img src="${order.products[0].image}" alt="商品" class="product-image" onerror="this.src='../../assets/images/product-default.jpg'">
                        <div>
                            <div class="product-name" title="${order.products[0].name}">${order.products[0].name}</div>
                            <div class="product-count">${order.products[0].count > 1 ? `等${order.products[0].count}件商品` : '1件商品'}</div>
                        </div>
                    </div>
                </td>
                <td><span class="amount-value">¥${order.amount.toFixed(2)}</span></td>
                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                <td>${order.payment}</td>
                <td>
                    ${order.logistics.number ? `
                        <div class="logistics-info">
                            <div class="logistics-company">${order.logistics.company}</div>
                            <div class="logistics-status">${order.logistics.status}</div>
                        </div>
                    ` : '-'}
                </td>
                <td>${order.createTime}</td>
                <td>
                    <div class="action-buttons">
                        ${getActionButtons(order)}
                    </div>
                </td>
            `;
            
            return row;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待支付',
                'paid': '待发货',
                'shipped': '已发货',
                'delivered': '已完成',
                'refunding': '退款中',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        // 获取操作按钮
        function getActionButtons(order) {
            switch(order.status) {
                case 'paid':
                    return `
                        <button class="btn-sm primary" onclick="shipOrder('${order.id}')">发货</button>
                        <button class="btn-sm" onclick="viewOrderDetail('${order.id}')">详情</button>
                    `;
                case 'shipped':
                    return `
                        <button class="btn-sm" onclick="trackOrder('${order.id}')">追踪</button>
                        <button class="btn-sm" onclick="viewOrderDetail('${order.id}')">详情</button>
                    `;
                case 'refunding':
                    return `
                        <button class="btn-sm warning" onclick="processRefund('${order.id}')">审核</button>
                        <button class="btn-sm" onclick="viewOrderDetail('${order.id}')">详情</button>
                    `;
                default:
                    return `<button class="btn-sm" onclick="viewOrderDetail('${order.id}')">详情</button>`;
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.order-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                if (selectAll.checked) {
                    selectedOrders.add(checkbox.dataset.order);
                } else {
                    selectedOrders.delete(checkbox.dataset.order);
                }
            });
            
            updateBulkActions();
        }

        // 更新批量操作栏
        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.order-checkbox:checked');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            
            selectedOrders.clear();
            checkboxes.forEach(cb => selectedOrders.add(cb.dataset.order));
            
            if (selectedOrders.size > 0) {
                bulkActions.classList.add('show');
                selectedCount.textContent = selectedOrders.size;
            } else {
                bulkActions.classList.remove('show');
            }
        }

        // 状态筛选
        function filterByStatus(status, element) {
            const tabs = document.querySelectorAll('.filter-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            element.classList.add('active');
            
            currentFilter.status = status;
            loadOrders();
        }

        // 搜索订单
        function searchOrders() {
            const orderNum = document.getElementById('orderSearch').value;
            const userId = document.getElementById('userSearch').value;
            const payment = document.getElementById('paymentFilter').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            currentFilter = { orderNum, userId, payment, startDate, endDate };
            loadOrders();
            alert('搜索完成！');
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('orderSearch').value = '';
            document.getElementById('userSearch').value = '';
            document.getElementById('paymentFilter').value = '';
            setDefaultDates();
            
            currentFilter = {};
            loadOrders();
        }

        // 批量发货
        function batchShip() {
            if (selectedOrders.size === 0) {
                alert('请先选择要发货的订单');
                return;
            }
            
            if (confirm(`确定要批量发货选中的 ${selectedOrders.size} 个订单吗？`)) {
                alert(`批量发货成功！共处理 ${selectedOrders.size} 个订单`);
                selectedOrders.clear();
                updateBulkActions();
                loadOrders();
            }
        }

        // 单个发货
        function shipOrder(orderId) {
            if (confirm(`确定要发货订单 ${orderId} 吗？`)) {
                alert('发货成功！');
                loadOrders();
            }
        }

        // 追踪订单
        function trackOrder(orderId) {
            alert(`查看订单 ${orderId} 的物流信息`);
        }

        // 处理退款
        function processRefund(orderId) {
            alert(`处理订单 ${orderId} 的退款申请`);
        }

        // 查看订单详情
        function viewOrderDetail(orderId) {
            window.open(`order-detail.html?id=${orderId}`, '_blank');
        }

        // 自动刷新
        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            
            if (autoRefreshTimer) {
                clearInterval(autoRefreshTimer);
                autoRefreshTimer = null;
                btn.style.backgroundColor = '';
                btn.style.color = '';
                alert('自动刷新已关闭');
            } else {
                autoRefreshTimer = setInterval(() => {
                    loadOrders();
                    console.log('自动刷新订单数据...');
                }, 30000);
                
                btn.style.backgroundColor = '#10b981';
                btn.style.color = 'white';
                alert('自动刷新已开启（30秒间隔）');
            }
        }

        // 初始化分页
        function initPagination() {
            const controls = document.getElementById('paginationControls');
            controls.innerHTML = `
                <button class="page-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>
                <button class="page-btn active">1</button>
                <button class="page-btn" onclick="changePage(2)">2</button>
                <button class="page-btn" onclick="changePage(3)">3</button>
                <button class="page-btn" onclick="changePage(${currentPage + 1})">下一页</button>
            `;
        }

        // 切换页面
        function changePage(page) {
            if (page < 1) return;
            currentPage = page;
            loadOrders();
            initPagination();
        }

        // 其他功能
        function exportOrders() { 
            showExportModal();
        }
        
        function showBatchShip() { batchShip(); }
        
        function showLogistics() { 
            showLogisticsModal();
        }
        
        function refreshData() { loadOrders(); alert('数据已刷新'); }
        
        function showAdvancedFilter() { 
            showAdvancedFilterModal();
        }
        
        function batchExport() { 
            if (selectedOrders.size === 0) {
                alert('请先选择要导出的订单');
                return;
            }
            
            const exportData = sampleOrders.filter(order => selectedOrders.has(order.id));
            downloadExcel(exportData, `订单数据_${new Date().toISOString().split('T')[0]}.xlsx`);
            alert(`成功导出 ${selectedOrders.size} 个订单数据`); 
        }
        
        // 导出功能实现
        function showExportModal() {
            const modal = createModal('导出订单', `
                <div class="export-options">
                    <div class="form-group">
                        <label>导出格式：</label>
                        <select id="exportFormat" class="form-control">
                            <option value="xlsx">Excel (.xlsx)</option>
                            <option value="csv">CSV (.csv)</option>
                            <option value="pdf">PDF报告</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>导出范围：</label>
                        <div class="radio-group">
                            <label><input type="radio" name="exportRange" value="all" checked> 所有订单</label>
                            <label><input type="radio" name="exportRange" value="current"> 当前页面</label>
                            <label><input type="radio" name="exportRange" value="filtered"> 当前筛选结果</label>
                            <label><input type="radio" name="exportRange" value="selected"> 已选择订单</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>包含字段：</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" checked> 订单号</label>
                            <label><input type="checkbox" checked> 客户信息</label>
                            <label><input type="checkbox" checked> 商品信息</label>
                            <label><input type="checkbox" checked> 金额</label>
                            <label><input type="checkbox" checked> 状态</label>
                            <label><input type="checkbox" checked> 支付方式</label>
                            <label><input type="checkbox"> 物流信息</label>
                            <label><input type="checkbox"> 创建时间</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button onclick="closeExportModal()" class="btn btn-secondary">取消</button>
                    <button onclick="executeExport()" class="btn btn-primary">导出</button>
                </div>
            `);
        }
        
        function executeExport() {
            const format = document.getElementById('exportFormat').value;
            const range = document.querySelector('input[name="exportRange"]:checked').value;
            const fields = Array.from(document.querySelectorAll('.checkbox-group input:checked')).map(cb => cb.parentElement.textContent.trim());
            
            let exportData = [];
            switch(range) {
                case 'all':
                    exportData = sampleOrders;
                    break;
                case 'current':
                    exportData = sampleOrders.slice((currentPage - 1) * pageSize, currentPage * pageSize);
                    break;
                case 'filtered':
                    exportData = sampleOrders;
                    break;
                case 'selected':
                    exportData = sampleOrders.filter(order => selectedOrders.has(order.id));
                    break;
            }
            
            if (format === 'xlsx') {
                downloadExcel(exportData, `订单数据_${new Date().toISOString().split('T')[0]}.xlsx`);
            } else if (format === 'csv') {
                downloadCSV(exportData, `订单数据_${new Date().toISOString().split('T')[0]}.csv`);
            } else if (format === 'pdf') {
                downloadPDF(exportData, `订单报告_${new Date().toISOString().split('T')[0]}.pdf`);
            }
            
            closeExportModal();
            showNotification(`成功导出 ${exportData.length} 条订单数据`, 'success');
        }
        
        // 物流管理功能实现
        function showLogisticsModal() {
            const modal = createModal('物流管理', `
                <div class="logistics-management">
                    <div class="tabs">
                        <button class="tab-btn active" onclick="switchLogisticsTab('track')">物流追踪</button>
                        <button class="tab-btn" onclick="switchLogisticsTab('ship')">批量发货</button>
                        <button class="tab-btn" onclick="switchLogisticsTab('settings')">物流设置</button>
                    </div>
                    
                    <div id="trackTab" class="tab-content active">
                        <div class="form-group">
                            <label>订单号或运单号：</label>
                            <div class="input-group">
                                <input type="text" id="trackingNumber" class="form-control" placeholder="请输入订单号或运单号">
                                <button onclick="trackPackage()" class="btn btn-primary">查询</button>
                            </div>
                        </div>
                        <div id="trackingResult" class="tracking-result"></div>
                    </div>
                    
                    <div id="shipTab" class="tab-content" style="display:none;">
                        <div class="form-group">
                            <label>快递公司：</label>
                            <select id="expressCompany" class="form-control">
                                <option value="sf">顺丰速运</option>
                                <option value="yt">圆通速递</option>
                                <option value="zt">中通快递</option>
                                <option value="st">申通快递</option>
                                <option value="jd">京东物流</option>
                                <option value="yd">韵达速递</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>发货仓库：</label>
                            <select id="warehouse" class="form-control">
                                <option value="main">主仓库（北京）</option>
                                <option value="south">南方仓库（深圳）</option>
                                <option value="west">西部仓库（成都）</option>
                            </select>
                        </div>
                        <div class="selected-orders">
                            <h4>待发货订单（${selectedOrders.size}个）</h4>
                            <div class="order-list" id="selectedOrdersList">
                                <!-- 订单列表将在这里显示 -->
                            </div>
                        </div>
                        <button onclick="executeBatchShip()" class="btn btn-primary">批量发货</button>
                    </div>
                    
                    <div id="settingsTab" class="tab-content" style="display:none;">
                        <div class="form-group">
                            <label>默认快递公司：</label>
                            <select class="form-control">
                                <option value="sf">顺丰速运</option>
                                <option value="yt">圆通速递</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>自动发货时间：</label>
                            <select class="form-control">
                                <option value="immediate">立即发货</option>
                                <option value="24h">24小时后</option>
                                <option value="48h">48小时后</option>
                            </select>
                        </div>
                        <button onclick="saveLogisticsSettings()" class="btn btn-primary">保存设置</button>
                    </div>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button onclick="closeLogisticsModal()" class="btn btn-secondary">关闭</button>
                </div>
            `);
        }
        
        // 高级筛选功能实现
        function showAdvancedFilterModal() {
            const modal = createModal('高级筛选', `
                <div class="advanced-filter">
                    <div class="filter-section">
                        <h4>订单信息</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>订单号：</label>
                                <input type="text" id="advOrderId" class="form-control" placeholder="支持模糊搜索">
                            </div>
                            <div class="form-group">
                                <label>订单状态：</label>
                                <select id="advOrderStatus" class="form-control" multiple>
                                    <option value="pending">待支付</option>
                                    <option value="paid">待发货</option>
                                    <option value="shipped">已发货</option>
                                    <option value="delivered">已完成</option>
                                    <option value="refunding">退款中</option>
                                    <option value="cancelled">已取消</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="filter-section">
                        <h4>客户信息</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>客户ID：</label>
                                <input type="text" id="advCustomerId" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>客户等级：</label>
                                <select id="advCustomerLevel" class="form-control">
                                    <option value="">全部</option>
                                    <option value="normal">普通用户</option>
                                    <option value="vip">VIP用户</option>
                                    <option value="svip">超级VIP</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="filter-section">
                        <h4>金额范围</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>最小金额：</label>
                                <input type="number" id="minAmount" class="form-control" min="0" step="0.01">
                            </div>
                            <div class="form-group">
                                <label>最大金额：</label>
                                <input type="number" id="maxAmount" class="form-control" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    
                    <div class="filter-section">
                        <h4>时间范围</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>开始时间：</label>
                                <input type="datetime-local" id="advStartTime" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>结束时间：</label>
                                <input type="datetime-local" id="advEndTime" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="filter-section">
                        <h4>其他条件</h4>
                        <div class="checkbox-group">
                            <label><input type="checkbox" id="isRisk"> 仅显示异常订单</label>
                            <label><input type="checkbox" id="hasRefund"> 包含退款记录</label>
                            <label><input type="checkbox" id="isVip"> 仅VIP客户</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button onclick="resetAdvancedFilter()" class="btn btn-secondary">重置</button>
                    <button onclick="closeAdvancedFilterModal()" class="btn btn-secondary">取消</button>
                    <button onclick="applyAdvancedFilter()" class="btn btn-primary">应用筛选</button>
                </div>
            `);
        }
        
        // 辅助功能
        function createModal(title, content) {
            const modal = document.createElement('div');
            modal.className = 'custom-modal';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.5); z-index: 1000;
                display: flex; align-items: center; justify-content: center;
            `;
            
            modal.innerHTML = `
                <div class="modal-content" style="background: white; border-radius: 8px; max-width: 800px; max-height: 90vh; overflow-y: auto; margin: 20px;">
                    <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #eee;">
                        <h3 style="margin: 0;">${title}</h3>
                    </div>
                    <div class="modal-body" style="padding: 20px;">
                        ${content}
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            return modal;
        }
        
        function closeExportModal() {
            const modal = document.querySelector('.custom-modal');
            if (modal) modal.remove();
        }
        
        function closeLogisticsModal() {
            const modal = document.querySelector('.custom-modal');
            if (modal) modal.remove();
        }
        
        function closeAdvancedFilterModal() {
            const modal = document.querySelector('.custom-modal');
            if (modal) modal.remove();
        }
        
        function downloadExcel(data, filename) {
            console.log('导出Excel:', filename, data);
            showNotification('Excel文件导出完成', 'success');
        }
        
        function downloadCSV(data, filename) {
            const csvContent = convertToCSV(data);
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            link.click();
        }
        
        function downloadPDF(data, filename) {
            console.log('导出PDF:', filename, data);
            showNotification('PDF报告导出完成', 'success');
        }
        
        function convertToCSV(data) {
            if (!data || data.length === 0) return '';
            
            const headers = ['订单号', '客户', '商品', '金额', '状态', '支付方式', '创建时间'];
            const rows = data.map(order => [
                order.id,
                order.customer.name,
                order.products.map(p => p.name).join(';'),
                order.amount,
                getStatusText(order.status),
                order.payment,
                order.createTime
            ]);
            
            return [headers, ...rows].map(row => row.join(',')).join('\n');
        }
        
        function trackPackage() {
            const trackingNumber = document.getElementById('trackingNumber').value.trim();
            if (!trackingNumber) {
                alert('请输入订单号或运单号');
                return;
            }
            
            // 模拟物流查询
            const resultDiv = document.getElementById('trackingResult');
            resultDiv.innerHTML = `
                <div class="tracking-info">
                    <h4>物流信息 - ${trackingNumber}</h4>
                    <div class="tracking-timeline">
                        <div class="timeline-item">
                            <span class="time">2023-12-15 14:30</span>
                            <span class="status">已签收</span>
                            <span class="location">北京市朝阳区</span>
                        </div>
                        <div class="timeline-item">
                            <span class="time">2023-12-15 09:15</span>
                            <span class="status">派送中</span>
                            <span class="location">北京市朝阳区派送点</span>
                        </div>
                        <div class="timeline-item">
                            <span class="time">2023-12-14 20:30</span>
                            <span class="status">到达派送站</span>
                            <span class="location">北京市朝阳区分拣中心</span>
                        </div>
                        <div class="timeline-item">
                            <span class="time">2023-12-14 15:20</span>
                            <span class="status">运输中</span>
                            <span class="location">北京分拣中心</span>
                        </div>
                        <div class="timeline-item">
                            <span class="time">2023-12-14 10:00</span>
                            <span class="status">已发货</span>
                            <span class="location">上海发货仓库</span>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function switchLogisticsTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // 移除所有按钮的active类
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + 'Tab').style.display = 'block';
            
            // 添加active类到对应按钮
            event.target.classList.add('active');
        }
        
        function executeBatchShip() {
            const company = document.getElementById('expressCompany').value;
            const warehouse = document.getElementById('warehouse').value;
            
            if (selectedOrders.size === 0) {
                alert('请先选择要发货的订单');
                return;
            }
            
            if (confirm(`确定使用${document.querySelector('#expressCompany option:checked').text}批量发货${selectedOrders.size}个订单吗？`)) {
                // 模拟批量发货
                selectedOrders.forEach(orderId => {
                    console.log(`发货订单 ${orderId}，快递公司：${company}，仓库：${warehouse}`);
                });
                
                showNotification(`成功发货${selectedOrders.size}个订单`, 'success');
                selectedOrders.clear();
                closeLogisticsModal();
                loadOrders();
            }
        }
        
        function saveLogisticsSettings() {
            showNotification('物流设置已保存', 'success');
        }
        
        function applyAdvancedFilter() {
            const filters = {
                orderId: document.getElementById('advOrderId').value,
                status: Array.from(document.getElementById('advOrderStatus').selectedOptions).map(option => option.value),
                customerId: document.getElementById('advCustomerId').value,
                customerLevel: document.getElementById('advCustomerLevel').value,
                minAmount: document.getElementById('minAmount').value,
                maxAmount: document.getElementById('maxAmount').value,
                startTime: document.getElementById('advStartTime').value,
                endTime: document.getElementById('advEndTime').value,
                isRisk: document.getElementById('isRisk').checked,
                hasRefund: document.getElementById('hasRefund').checked,
                isVip: document.getElementById('isVip').checked
            };
            
            console.log('应用高级筛选:', filters);
            currentFilter = filters;
            loadOrders();
            closeAdvancedFilterModal();
            showNotification('筛选条件已应用', 'success');
        }
        
        function resetAdvancedFilter() {
            document.getElementById('advOrderId').value = '';
            document.getElementById('advOrderStatus').selectedIndex = -1;
            document.getElementById('advCustomerId').value = '';
            document.getElementById('advCustomerLevel').value = '';
            document.getElementById('minAmount').value = '';
            document.getElementById('maxAmount').value = '';
            document.getElementById('advStartTime').value = '';
            document.getElementById('advEndTime').value = '';
            document.getElementById('isRisk').checked = false;
            document.getElementById('hasRefund').checked = false;
            document.getElementById('isVip').checked = false;
        }
        
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                padding: 15px 20px; border-radius: 6px; color: white;
                font-size: 14px; max-width: 300px; word-wrap: break-word;
                ${type === 'success' ? 'background: #10b981;' : 
                  type === 'error' ? 'background: #ef4444;' : 
                  type === 'warning' ? 'background: #f59e0b;' : 'background: #3b82f6;'}
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }
        
        function batchCancel() { 
            if (selectedOrders.size === 0) {
                alert('请先选择要取消的订单');
                return;
            }
            if (confirm(`确定要取消选中的 ${selectedOrders.size} 个订单吗？`)) {
                showNotification('批量取消成功', 'success');
                selectedOrders.clear();
                updateBulkActions();
                loadOrders();
            }
        }
    </script>
</body>
</html> 