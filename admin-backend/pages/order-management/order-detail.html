<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单详情 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .order-detail-page { padding: 24px; background: #f8fafc; min-height: 100vh; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 28px; font-weight: 700; color: #1e293b; margin: 0; }
        .order-number { font-family: monospace; color: #3b82f6; font-weight: 600; }
        .action-btn { padding: 10px 20px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 8px; text-decoration: none; }
        .action-btn:hover { background: #f1f5f9; border-color: #cbd5e1; }
        .action-btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .action-btn.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .action-btn.danger { background: #ef4444; color: white; border-color: #ef4444; }
        .content-grid { display: grid; grid-template-columns: 2fr 1fr; gap: 24px; }
        .content-section { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; margin-bottom: 24px; }
        .section-header { padding: 20px 24px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center; }
        .section-title { font-size: 18px; font-weight: 600; color: #1e293b; margin: 0; }
        .section-content { padding: 24px; }
        .status-badge { padding: 6px 12px; border-radius: 16px; font-size: 12px; font-weight: 500; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-paid { background: #dbeafe; color: #1e40af; }
        .status-shipped { background: #e0e7ff; color: #3730a3; }
        .status-delivered { background: #dcfce7; color: #166534; }
        .status-refunding { background: #fed7aa; color: #ea580c; }
        .status-cancelled { background: #fecaca; color: #dc2626; }
        .info-item { display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f1f5f9; }
        .info-item:last-child { border-bottom: none; }
        .info-label { font-weight: 500; color: #64748b; }
        .info-value { color: #1e293b; }
        .product-table { width: 100%; border-collapse: collapse; }
        .product-table th, .product-table td { padding: 16px; text-align: left; border-bottom: 1px solid #f1f5f9; }
        .product-table th { background: #f8fafc; font-weight: 600; color: #374151; }
        .product-info { display: flex; align-items: center; gap: 12px; }
        .product-image { width: 60px; height: 60px; border-radius: 8px; object-fit: cover; background: #f1f5f9; }
        .product-details h4 { margin: 0 0 4px 0; font-size: 14px; font-weight: 600; color: #1e293b; }
        .product-details p { margin: 0; font-size: 12px; color: #64748b; }
        .amount-summary { background: #f8fafc; padding: 20px; border-radius: 8px; margin-top: 16px; }
        .amount-row { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
        .amount-row:last-child { margin-bottom: 0; font-size: 18px; font-weight: 700; color: #1e293b; border-top: 1px solid #e2e8f0; padding-top: 8px; }
        .timeline { position: relative; }
        .timeline-item { position: relative; padding: 16px 0 16px 40px; }
        .timeline-item::before { content: ''; position: absolute; left: 8px; top: 20px; width: 8px; height: 8px; border-radius: 50%; background: #e2e8f0; }
        .timeline-item.active::before { background: #3b82f6; }
        .timeline-item.completed::before { background: #10b981; }
        .timeline-item::after { content: ''; position: absolute; left: 11px; top: 32px; width: 2px; height: calc(100% - 12px); background: #e2e8f0; }
        .timeline-item:last-child::after { display: none; }
        .timeline-time { font-size: 12px; color: #64748b; margin-bottom: 4px; }
        .timeline-title { font-weight: 600; color: #1e293b; margin-bottom: 4px; }
        .timeline-desc { font-size: 14px; color: #64748b; }
        .risk-alert { background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px; margin-bottom: 16px; }
        .risk-title { color: #dc2626; font-weight: 600; margin-bottom: 8px; display: flex; align-items: center; gap: 8px; }
        .risk-desc { color: #991b1b; font-size: 14px; }
        .notes-section { margin-top: 16px; }
        .note-item { background: #f8fafc; border-left: 4px solid #3b82f6; padding: 12px 16px; margin-bottom: 8px; border-radius: 0 8px 8px 0; }
        .note-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px; }
        .note-author { font-weight: 500; color: #1e293b; font-size: 14px; }
        .note-time { font-size: 12px; color: #64748b; }
        .note-content { color: #374151; font-size: 14px; line-height: 1.5; }
        .add-note { margin-top: 12px; }
        .note-input { width: 100%; padding: 12px; border: 1px solid #e2e8f0; border-radius: 8px; font-size: 14px; resize: vertical; min-height: 80px; }
        .note-actions { margin-top: 8px; display: flex; justify-content: flex-end; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal.show { display: flex; align-items: center; justify-content: center; }
        .modal-content { background: white; border-radius: 12px; padding: 24px; width: 500px; max-width: 90vw; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .modal-title { font-size: 18px; font-weight: 600; color: #1e293b; margin: 0; }
        .modal-close { background: none; border: none; font-size: 24px; color: #64748b; cursor: pointer; }
        .form-group { margin-bottom: 16px; }
        .form-label { display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px; }
        .form-input { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; }
        .form-select { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; background: white; }
        .form-textarea { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; resize: vertical; min-height: 80px; }
        .modal-actions { display: flex; gap: 12px; justify-content: flex-end; margin-top: 20px; }
        .btn { padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.2s; }
        .btn.primary { background: #3b82f6; color: white; border: 1px solid #3b82f6; }
        .btn.secondary { background: white; color: #64748b; border: 1px solid #e2e8f0; }
    </style>
</head>
<body>
    <div class="order-detail-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">订单详情</h1>
                <div class="order-number">OD202312150001</div>
            </div>
            <div style="display: flex; gap: 12px;">
                <a href="index.html" class="action-btn">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
                <button class="action-btn" onclick="printOrder()">
                    <i class="fas fa-print"></i>
                    打印订单
                </button>
                <button class="action-btn warning" onclick="showShipModal()">
                    <i class="fas fa-shipping-fast"></i>
                    发货
                </button>
            </div>
        </div>

        <div class="content-grid">
            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 风险提醒 -->
                <div class="risk-alert">
                    <div class="risk-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        异常订单提醒
                    </div>
                    <div class="risk-desc">
                        系统检测到该订单存在风险：用户10分钟内连续下单5笔，建议人工审核后再处理。
                    </div>
                </div>

                <!-- 订单基本信息 -->
                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">订单信息</h3>
                        <span class="status-badge status-paid">已支付</span>
                    </div>
                    <div class="section-content">
                        <div class="info-item">
                            <span class="info-label">订单号</span>
                            <span class="info-value">OD202312150001</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">下单时间</span>
                            <span class="info-value">2023-12-15 14:32:15</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付时间</span>
                            <span class="info-value">2023-12-15 14:35:22</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付方式</span>
                            <span class="info-value">微信支付</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付流水号</span>
                            <span class="info-value">4200001234567890123456789</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">订单来源</span>
                            <span class="info-value">Telegram Bot</span>
                        </div>
                    </div>
                </div>

                <!-- 客户信息 -->
                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">客户信息</h3>
                        <button class="action-btn" onclick="viewCustomer(12345)">
                            <i class="fas fa-user"></i>
                            查看客户
                        </button>
                    </div>
                    <div class="section-content">
                        <div class="info-item">
                            <span class="info-label">客户姓名</span>
                            <span class="info-value">张小明</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">用户ID</span>
                            <span class="info-value">12345</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Telegram用户名</span>
                            <span class="info-value">@zhangxiaoming</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">联系电话</span>
                            <span class="info-value">138****1234</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">收货地址</span>
                            <span class="info-value">北京市朝阳区建国门外大街1号 国贸大厦A座1001室</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">客户等级</span>
                            <span class="info-value">VIP会员</span>
                        </div>
                    </div>
                </div>

                <!-- 商品清单 -->
                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">商品清单</h3>
                    </div>
                    <div class="section-content" style="padding: 0; overflow-x: auto;">
                        <table class="product-table">
                            <thead>
                                <tr>
                                    <th>商品信息</th>
                                    <th>SKU</th>
                                    <th>单价</th>
                                    <th>数量</th>
                                    <th>小计</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="product-info">
                                            <img src="../../assets/images/iphone-1.jpg" alt="商品" class="product-image">
                                            <div class="product-details">
                                                <h4>iPhone 14 Pro Max 128GB 深空黑色</h4>
                                                <p>Apple官方正品 | 全国联保</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>IP14PM128BK</td>
                                    <td>¥8,999.00</td>
                                    <td>1</td>
                                    <td style="font-weight: 600;">¥8,999.00</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="product-info">
                                            <img src="../../assets/images/case-1.jpg" alt="商品" class="product-image">
                                            <div class="product-details">
                                                <h4>iPhone 14 Pro Max 硅胶保护壳</h4>
                                                <p>Apple原装配件 | 深空黑色</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>CASE14PMBK</td>
                                    <td>¥399.00</td>
                                    <td>1</td>
                                    <td style="font-weight: 600;">¥399.00</td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <div class="amount-summary">
                            <div class="amount-row">
                                <span>商品小计</span>
                                <span>¥9,398.00</span>
                            </div>
                            <div class="amount-row">
                                <span>运费</span>
                                <span>¥0.00</span>
                            </div>
                            <div class="amount-row">
                                <span>优惠券折扣</span>
                                <span>-¥100.00</span>
                            </div>
                            <div class="amount-row">
                                <span>会员折扣</span>
                                <span>-¥299.00</span>
                            </div>
                            <div class="amount-row">
                                <span>订单总额</span>
                                <span>¥8,999.00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 订单状态流程 -->
                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">订单状态</h3>
                    </div>
                    <div class="section-content">
                        <div class="timeline">
                            <div class="timeline-item completed">
                                <div class="timeline-time">2023-12-15 14:32</div>
                                <div class="timeline-title">订单创建</div>
                                <div class="timeline-desc">客户通过Telegram Bot下单</div>
                            </div>
                            <div class="timeline-item completed">
                                <div class="timeline-time">2023-12-15 14:35</div>
                                <div class="timeline-title">支付完成</div>
                                <div class="timeline-desc">微信支付成功 ¥8,999.00</div>
                            </div>
                            <div class="timeline-item active">
                                <div class="timeline-time">等待处理</div>
                                <div class="timeline-title">待发货</div>
                                <div class="timeline-desc">订单已确认，等待仓库发货</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-time">-</div>
                                <div class="timeline-title">已发货</div>
                                <div class="timeline-desc">商品已出库并发货</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-time">-</div>
                                <div class="timeline-title">已完成</div>
                                <div class="timeline-desc">客户确认收货</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作记录 -->
                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">操作记录</h3>
                    </div>
                    <div class="section-content">
                        <div class="notes-section">
                            <div class="note-item">
                                <div class="note-header">
                                    <span class="note-author">系统</span>
                                    <span class="note-time">2023-12-15 14:35</span>
                                </div>
                                <div class="note-content">订单支付成功，金额¥8,999.00</div>
                            </div>
                            <div class="note-item">
                                <div class="note-header">
                                    <span class="note-author">张客服</span>
                                    <span class="note-time">2023-12-15 14:40</span>
                                </div>
                                <div class="note-content">检测到异常订单，已标记为风险订单，需要人工审核。</div>
                            </div>
                            
                            <div class="add-note">
                                <textarea class="note-input" id="noteInput" placeholder="添加备注..."></textarea>
                                <div class="note-actions">
                                    <button class="btn primary" onclick="addNote()">添加备注</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">快速操作</h3>
                    </div>
                    <div class="section-content">
                        <div style="display: grid; gap: 8px;">
                            <button class="action-btn primary" onclick="showShipModal()">
                                <i class="fas fa-shipping-fast"></i>
                                立即发货
                            </button>
                            <button class="action-btn warning" onclick="holdOrder()">
                                <i class="fas fa-pause"></i>
                                暂停订单
                            </button>
                            <button class="action-btn danger" onclick="cancelOrder()">
                                <i class="fas fa-times"></i>
                                取消订单
                            </button>
                            <button class="action-btn" onclick="contactCustomer()">
                                <i class="fas fa-comments"></i>
                                联系客户
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 发货弹窗 -->
    <div id="shipModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">订单发货</h3>
                <button class="modal-close" onclick="closeModal('shipModal')">&times;</button>
            </div>
            
            <div class="form-group">
                <label class="form-label">快递公司</label>
                <select class="form-select" id="courierCompany">
                    <option value="SF">顺丰速运</option>
                    <option value="YTO">圆通速递</option>
                    <option value="ZTO">中通快递</option>
                    <option value="STO">申通快递</option>
                    <option value="JD">京东快递</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">运单号</label>
                <input type="text" class="form-input" id="trackingNumber" placeholder="请输入快递运单号">
            </div>
            
            <div class="form-group">
                <label class="form-label">发货仓库</label>
                <select class="form-select" id="warehouse">
                    <option value="main">主仓库（北京）</option>
                    <option value="backup">备用仓库（上海）</option>
                    <option value="third">第三方仓库（广州）</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">发货备注</label>
                <textarea class="form-textarea" id="shipNote" placeholder="请输入发货备注"></textarea>
            </div>
            
            <div class="modal-actions">
                <button class="btn secondary" onclick="closeModal('shipModal')">取消</button>
                <button class="btn primary" onclick="confirmShip()">确认发货</button>
            </div>
        </div>
    </div>

    <script>
        // 显示发货弹窗
        function showShipModal() {
            document.getElementById('shipModal').classList.add('show');
        }

        // 确认发货
        function confirmShip() {
            const courier = document.getElementById('courierCompany').value;
            const tracking = document.getElementById('trackingNumber').value;
            const warehouse = document.getElementById('warehouse').value;
            const note = document.getElementById('shipNote').value;
            
            if (!tracking) {
                alert('请输入运单号');
                return;
            }
            
            alert(`发货成功！\n快递公司: ${courier}\n运单号: ${tracking}\n发货仓库: ${warehouse}`);
            closeModal('shipModal');
            
            // 更新页面状态
            updateOrderStatus('shipped');
        }

        // 更新订单状态
        function updateOrderStatus(status) {
            // 这里更新页面上的状态显示
            console.log('更新订单状态:', status);
        }

        // 添加备注
        function addNote() {
            const note = document.getElementById('noteInput').value.trim();
            if (!note) {
                alert('请输入备注内容');
                return;
            }
            
            const notesSection = document.querySelector('.notes-section');
            const addNoteDiv = document.querySelector('.add-note');
            
            const noteItem = document.createElement('div');
            noteItem.className = 'note-item';
            noteItem.innerHTML = `
                <div class="note-header">
                    <span class="note-author">当前用户</span>
                    <span class="note-time">${new Date().toLocaleString()}</span>
                </div>
                <div class="note-content">${note}</div>
            `;
            
            notesSection.insertBefore(noteItem, addNoteDiv);
            document.getElementById('noteInput').value = '';
        }

        // 其他操作
        function printOrder() {
            window.print();
        }

        function viewCustomer(customerId) {
            alert(`查看客户 ${customerId} 的详细信息`);
        }

        function holdOrder() {
            if (confirm('确定要暂停该订单吗？')) {
                alert('订单已暂停');
            }
        }

        function cancelOrder() {
            if (confirm('确定要取消该订单吗？此操作不可撤销！')) {
                alert('订单已取消');
            }
        }

        function contactCustomer() {
            alert('正在联系客户...');
        }

        // 关闭弹窗
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html> 