<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B2B2C订单管理 - TeleShop</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .order-management-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 10px 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .action-btn.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .action-btn.success { background: #10b981; color: white; border-color: #10b981; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .stat-icon.orders { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.split { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.commission { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-icon.settlement { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        
        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .stat-label {
            color: #64748b;
            font-size: 14px;
        }
        
        .main-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .content-tabs {
            display: flex;
            border-bottom: 1px solid #e2e8f0;
            overflow-x: auto;
        }
        
        .tab-button {
            padding: 16px 24px;
            border: none;
            background: none;
            color: #64748b;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            white-space: nowrap;
            font-size: 14px;
            font-weight: 500;
        }
        
        .tab-button.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }
        
        .tab-content {
            padding: 24px;
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .filter-section {
            margin-bottom: 24px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
        }
        
        .filter-input, .filter-select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .order-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .order-table th,
        .order-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }
        
        .order-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            font-size: 12px;
            position: sticky;
            top: 0;
        }
        
        .order-card {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        
        .order-header {
            background: #f8fafc;
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .order-id {
            font-family: monospace;
            font-weight: 600;
            color: #3b82f6;
        }
        
        .order-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-paid { background: #dbeafe; color: #1e40af; }
        .status-split { background: #e0e7ff; color: #3730a3; }
        .status-shipping { background: #f3e8ff; color: #7c3aed; }
        .status-completed { background: #dcfce7; color: #166534; }
        .status-cancelled { background: #fecaca; color: #dc2626; }
        
        .order-merchants {
            padding: 16px;
        }
        
        .merchant-section {
            border: 1px solid #f1f5f9;
            border-radius: 6px;
            margin-bottom: 12px;
            overflow: hidden;
        }
        
        .merchant-header {
            background: #fafbfc;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .merchant-name {
            font-weight: 500;
            color: #1e293b;
        }
        
        .merchant-commission {
            font-size: 12px;
            color: #64748b;
        }
        
        .product-list {
            padding: 16px;
        }
        
        .product-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #f8fafc;
        }
        
        .product-item:last-child {
            border-bottom: none;
        }
        
        .product-image {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            object-fit: cover;
        }
        
        .product-info {
            flex: 1;
        }
        
        .product-name {
            font-size: 14px;
            color: #1e293b;
            margin-bottom: 2px;
        }
        
        .product-spec {
            font-size: 12px;
            color: #64748b;
        }
        
        .product-price {
            font-weight: 600;
            color: #1e293b;
            text-align: right;
        }
        
        .commission-summary {
            background: #f8fafc;
            padding: 16px;
            border-top: 1px solid #e2e8f0;
        }
        
        .commission-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .commission-row:last-child {
            margin-bottom: 0;
            font-weight: 600;
            color: #1e293b;
            border-top: 1px solid #e2e8f0;
            padding-top: 8px;
        }
        
        .settlement-panel {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }
        
        .settlement-title {
            font-size: 16px;
            font-weight: 600;
            color: #0c4a6e;
            margin-bottom: 12px;
        }
        
        .settlement-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-sm.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .btn-sm.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .btn-sm.danger { background: #ef4444; color: white; border-color: #ef4444; }
        .btn-sm.success { background: #10b981; color: white; border-color: #10b981; }
        
        @media (max-width: 768px) {
            .order-management-page { padding: 16px; }
            .page-header { flex-direction: column; align-items: stretch; }
            .stats-grid { grid-template-columns: 1fr 1fr; gap: 16px; }
            .filter-grid { grid-template-columns: 1fr; gap: 12px; }
        }
    </style>
</head>
<body>
    <div class="order-management-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">B2B2C订单管理</h1>
            <div class="header-actions">
                <button class="action-btn" onclick="exportOrders()">
                    <i class="fas fa-download"></i>
                    导出订单
                </button>
                <button class="action-btn warning" onclick="batchSettlement()">
                    <i class="fas fa-calculator"></i>
                    批量结算
                </button>
                <button class="action-btn success" onclick="commissionReport()">
                    <i class="fas fa-chart-line"></i>
                    佣金报表
                </button>
                <button class="action-btn primary" onclick="refreshData()">
                    <i class="fas fa-sync"></i>
                    刷新数据
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon orders">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="stat-value">1,234</div>
                <div class="stat-label">今日订单总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon split">
                        <i class="fas fa-cut"></i>
                    </div>
                </div>
                <div class="stat-value">89</div>
                <div class="stat-label">拆分订单数</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon commission">
                        <i class="fas fa-percentage"></i>
                    </div>
                </div>
                <div class="stat-value">¥12,456</div>
                <div class="stat-label">今日佣金收入</div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon settlement">
                        <i class="fas fa-credit-card"></i>
                    </div>
                </div>
                <div class="stat-value">¥89,123</div>
                <div class="stat-label">待结算金额</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 标签页导航 -->
            <div class="content-tabs">
                <button class="tab-button active" onclick="switchTab('all-orders', this)">全部订单</button>
                <button class="tab-button" onclick="switchTab('split-orders', this)">拆分订单</button>
                <button class="tab-button" onclick="switchTab('commission', this)">佣金管理</button>
                <button class="tab-button" onclick="switchTab('settlement', this)">结算管理</button>
            </div>

            <!-- 全部订单标签页 -->
            <div id="all-orders" class="tab-content active">
                <div class="filter-section">
                    <div class="filter-grid">
                        <div class="filter-group">
                            <label class="filter-label">订单号</label>
                            <input type="text" class="filter-input" placeholder="输入订单号">
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">订单状态</label>
                            <select class="filter-select">
                                <option>全部状态</option>
                                <option>待支付</option>
                                <option>已支付</option>
                                <option>已拆分</option>
                                <option>配送中</option>
                                <option>已完成</option>
                                <option>已取消</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">商家</label>
                            <select class="filter-select">
                                <option>全部商家</option>
                                <option>星辰数码专营店</option>
                                <option>时尚服饰旗舰店</option>
                                <option>美妆护肤专卖</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">下单时间</label>
                            <input type="date" class="filter-input">
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">&nbsp;</label>
                            <button class="action-btn primary">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 订单列表 -->
                <div class="order-list">
                    <!-- 混合订单示例 -->
                    <div class="order-card">
                        <div class="order-header">
                            <div>
                                <span class="order-id">ORD202312150001</span>
                                <span style="margin-left: 12px; color: #64748b; font-size: 14px;">2023-12-15 10:30</span>
                            </div>
                            <div>
                                <span class="order-status status-split">已拆分</span>
                                <span style="margin-left: 12px; font-weight: 600;">总金额: ¥1,299</span>
                            </div>
                        </div>
                        <div class="order-merchants">
                            <!-- 商家1 -->
                            <div class="merchant-section">
                                <div class="merchant-header">
                                    <span class="merchant-name">星辰数码专营店</span>
                                    <span class="merchant-commission">佣金率: 3%</span>
                                </div>
                                <div class="product-list">
                                    <div class="product-item">
                                        <img src="../../assets/images/product-default.jpg" alt="商品" class="product-image">
                                        <div class="product-info">
                                            <div class="product-name">iPhone 15 Pro 128GB</div>
                                            <div class="product-spec">天然钛色 × 1</div>
                                        </div>
                                        <div class="product-price">¥899</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 商家2 -->
                            <div class="merchant-section">
                                <div class="merchant-header">
                                    <span class="merchant-name">时尚服饰旗舰店</span>
                                    <span class="merchant-commission">佣金率: 8%</span>
                                </div>
                                <div class="product-list">
                                    <div class="product-item">
                                        <img src="../../assets/images/product-default.jpg" alt="商品" class="product-image">
                                        <div class="product-info">
                                            <div class="product-name">休闲运动套装</div>
                                            <div class="product-spec">黑色 L码 × 1</div>
                                        </div>
                                        <div class="product-price">¥400</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="commission-summary">
                            <div class="commission-row">
                                <span>商品总额:</span>
                                <span>¥1,299</span>
                            </div>
                            <div class="commission-row">
                                <span>平台佣金:</span>
                                <span>¥59 (星辰数码¥27 + 时尚服饰¥32)</span>
                            </div>
                            <div class="commission-row">
                                <span>商家实收:</span>
                                <span>¥1,240</span>
                            </div>
                        </div>
                        <div style="padding: 16px; border-top: 1px solid #e2e8f0;">
                            <div class="action-buttons">
                                <button class="btn-sm">查看详情</button>
                                <button class="btn-sm primary">处理订单</button>
                                <button class="btn-sm warning">批量发货</button>
                                <button class="btn-sm success">确认结算</button>
                            </div>
                        </div>
                    </div>

                    <!-- 单商家订单示例 -->
                    <div class="order-card">
                        <div class="order-header">
                            <div>
                                <span class="order-id">ORD202312150002</span>
                                <span style="margin-left: 12px; color: #64748b; font-size: 14px;">2023-12-15 11:15</span>
                            </div>
                            <div>
                                <span class="order-status status-paid">已支付</span>
                                <span style="margin-left: 12px; font-weight: 600;">总金额: ¥799</span>
                            </div>
                        </div>
                        <div class="order-merchants">
                            <div class="merchant-section">
                                <div class="merchant-header">
                                    <span class="merchant-name">美妆护肤专卖</span>
                                    <span class="merchant-commission">佣金率: 12%</span>
                                </div>
                                <div class="product-list">
                                    <div class="product-item">
                                        <img src="../../assets/images/product-default.jpg" alt="商品" class="product-image">
                                        <div class="product-info">
                                            <div class="product-name">兰蔻小黑瓶精华</div>
                                            <div class="product-spec">30ml × 1</div>
                                        </div>
                                        <div class="product-price">¥799</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="commission-summary">
                            <div class="commission-row">
                                <span>商品总额:</span>
                                <span>¥799</span>
                            </div>
                            <div class="commission-row">
                                <span>平台佣金:</span>
                                <span>¥96</span>
                            </div>
                            <div class="commission-row">
                                <span>商家实收:</span>
                                <span>¥703</span>
                            </div>
                        </div>
                        <div style="padding: 16px; border-top: 1px solid #e2e8f0;">
                            <div class="action-buttons">
                                <button class="btn-sm">查看详情</button>
                                <button class="btn-sm primary">发货</button>
                                <button class="btn-sm success">确认结算</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 拆分订单标签页 -->
            <div id="split-orders" class="tab-content">
                <h3 style="color: #1e293b; margin-bottom: 20px;">订单拆分规则</h3>
                <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 24px;">
                    <p style="color: #64748b; margin: 0; line-height: 1.6;">
                        当客户购买来自不同商家的商品时，系统会自动将订单拆分为多个子订单，每个商家负责处理自己的商品，
                        平台统一收款并按照预设的佣金率分配给各商家。
                    </p>
                </div>
                
                <div class="filter-section">
                    <div class="filter-grid">
                        <div class="filter-group">
                            <label class="filter-label">拆分状态</label>
                            <select class="filter-select">
                                <option>全部</option>
                                <option>已拆分</option>
                                <option>部分发货</option>
                                <option>全部发货</option>
                                <option>已完成</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">商家数量</label>
                            <select class="filter-select">
                                <option>全部</option>
                                <option>2个商家</option>
                                <option>3个商家</option>
                                <option>4个以上</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">&nbsp;</label>
                            <button class="action-btn primary">筛选</button>
                        </div>
                    </div>
                </div>

                <!-- 拆分订单列表显示已有的拆分订单 -->
                <div class="order-list">
                    <!-- 这里显示需要或已经拆分的订单 -->
                </div>
            </div>

            <!-- 佣金管理标签页 -->
            <div id="commission" class="tab-content">
                <div class="settlement-panel">
                    <div class="settlement-title">佣金统计概览</div>
                    <div class="settlement-item">
                        <span>今日佣金收入:</span>
                        <span style="color: #10b981; font-weight: 600;">¥12,456</span>
                    </div>
                    <div class="settlement-item">
                        <span>本月佣金收入:</span>
                        <span style="color: #10b981; font-weight: 600;">¥345,678</span>
                    </div>
                    <div class="settlement-item">
                        <span>待结算佣金:</span>
                        <span style="color: #f59e0b; font-weight: 600;">¥89,123</span>
                    </div>
                    <div class="settlement-item">
                        <span>已结算佣金:</span>
                        <span style="color: #64748b;">¥256,555</span>
                    </div>
                </div>

                <table class="order-table" style="margin-top: 24px;">
                    <thead>
                        <tr>
                            <th>商家名称</th>
                            <th>佣金率</th>
                            <th>订单金额</th>
                            <th>佣金金额</th>
                            <th>结算状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>星辰数码专营店</td>
                            <td>3%</td>
                            <td>¥23,456</td>
                            <td>¥704</td>
                            <td><span class="order-status status-pending">待结算</span></td>
                            <td><button class="btn-sm success">立即结算</button></td>
                        </tr>
                        <tr>
                            <td>时尚服饰旗舰店</td>
                            <td>8%</td>
                            <td>¥18,900</td>
                            <td>¥1,512</td>
                            <td><span class="order-status status-completed">已结算</span></td>
                            <td><button class="btn-sm">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>美妆护肤专卖</td>
                            <td>12%</td>
                            <td>¥15,678</td>
                            <td>¥1,881</td>
                            <td><span class="order-status status-pending">待结算</span></td>
                            <td><button class="btn-sm success">立即结算</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 结算管理标签页 -->
            <div id="settlement" class="tab-content">
                <div class="action-buttons" style="margin-bottom: 24px;">
                    <button class="action-btn success" onclick="batchSettlement()">
                        <i class="fas fa-calculator"></i>
                        批量结算
                    </button>
                    <button class="action-btn" onclick="exportSettlement()">
                        <i class="fas fa-file-excel"></i>
                        导出结算单
                    </button>
                    <button class="action-btn warning" onclick="settingsCommission()">
                        <i class="fas fa-cog"></i>
                        结算设置
                    </button>
                </div>

                <table class="order-table">
                    <thead>
                        <tr>
                            <th>结算单号</th>
                            <th>商家</th>
                            <th>结算周期</th>
                            <th>订单数量</th>
                            <th>订单金额</th>
                            <th>佣金扣除</th>
                            <th>实际结算</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="font-family: monospace;">STL202312150001</td>
                            <td>星辰数码专营店</td>
                            <td>2023-12-08 ~ 2023-12-14</td>
                            <td>156</td>
                            <td>¥89,456</td>
                            <td>¥2,684</td>
                            <td>¥86,772</td>
                            <td><span class="order-status status-completed">已结算</span></td>
                            <td><button class="btn-sm">查看详情</button></td>
                        </tr>
                        <tr>
                            <td style="font-family: monospace;">STL202312150002</td>
                            <td>时尚服饰旗舰店</td>
                            <td>2023-12-08 ~ 2023-12-14</td>
                            <td>89</td>
                            <td>¥45,678</td>
                            <td>¥3,654</td>
                            <td>¥42,024</td>
                            <td><span class="order-status status-pending">待结算</span></td>
                            <td><button class="btn-sm success">确认结算</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabId, buttonElement) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有按钮的激活状态
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabId).classList.add('active');
            
            // 激活选中的按钮
            buttonElement.classList.add('active');
        }
        
        function exportOrders() {
            alert('导出订单数据');
        }
        
        function batchSettlement() {
            alert('执行批量结算');
        }
        
        function commissionReport() {
            alert('生成佣金报表');
        }
        
        function refreshData() {
            alert('刷新数据');
        }
        
        function exportSettlement() {
            alert('导出结算单');
        }
        
        function settingsCommission() {
            alert('打开结算设置');
        }
    </script>
</body>
</html>