<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .order-management-page { padding: 24px; background: #f8fafc; min-height: 100vh; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .page-title { font-size: 28px; font-weight: 700; color: #1e293b; margin: 0; }
        .action-btn { padding: 10px 20px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 8px; text-decoration: none; }
        .action-btn:hover { background: #f1f5f9; border-color: #cbd5e1; }
        .action-btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .action-btn.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .action-btn.danger { background: #ef4444; color: white; border-color: #ef4444; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 24px; }
        .stat-card { background: white; padding: 20px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .stat-title { font-size: 14px; font-weight: 500; color: #64748b; margin-bottom: 8px; }
        .stat-value { font-size: 24px; font-weight: 700; color: #1e293b; margin-bottom: 4px; }
        .stat-desc { font-size: 12px; color: #64748b; }
        .main-content { background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .content-header { padding: 20px 24px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center; }
        .content-title { font-size: 18px; font-weight: 600; color: #1e293b; margin: 0; }
        .filter-tabs { display: flex; padding: 0 24px; border-bottom: 1px solid #e2e8f0; }
        .filter-tab { padding: 12px 16px; border: none; background: none; color: #64748b; font-size: 14px; cursor: pointer; border-bottom: 2px solid transparent; transition: all 0.2s; }
        .filter-tab.active { color: #3b82f6; border-bottom-color: #3b82f6; }
        .filter-section { padding: 20px 24px; border-bottom: 1px solid #e2e8f0; background: #f8fafc; }
        .filter-row { display: flex; gap: 16px; align-items: center; flex-wrap: wrap; }
        .filter-group { display: flex; flex-direction: column; gap: 4px; }
        .filter-label { font-size: 12px; font-weight: 500; color: #64748b; }
        .filter-input { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; min-width: 150px; }
        .filter-select { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; background: white; min-width: 120px; }
        .bulk-actions { padding: 16px 24px; background: #eff6ff; border-bottom: 1px solid #e2e8f0; display: none; align-items: center; justify-content: space-between; }
        .bulk-actions.show { display: flex; }
        .bulk-info { font-size: 14px; color: #1e40af; }
        .bulk-buttons { display: flex; gap: 8px; }
        .orders-table { width: 100%; border-collapse: collapse; }
        .orders-table th, .orders-table td { padding: 12px 16px; text-align: left; border-bottom: 1px solid #f1f5f9; }
        .orders-table th { background: #f8fafc; font-weight: 600; color: #374151; position: sticky; top: 0; font-size: 12px; text-transform: uppercase; }
        .order-checkbox { width: 18px; height: 18px; }
        .order-id { font-family: monospace; font-weight: 600; color: #3b82f6; cursor: pointer; }
        .order-id:hover { text-decoration: underline; }
        .customer-info { display: flex; align-items: center; gap: 8px; }
        .customer-avatar { width: 32px; height: 32px; border-radius: 50%; background: #f1f5f9; }
        .customer-name { font-weight: 500; color: #1e293b; margin-bottom: 2px; }
        .customer-id { font-size: 12px; color: #64748b; }
        .product-summary { display: flex; align-items: center; gap: 8px; }
        .product-image { width: 40px; height: 40px; border-radius: 6px; object-fit: cover; background: #f1f5f9; }
        .product-count { font-size: 12px; color: #64748b; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-paid { background: #dbeafe; color: #1e40af; }
        .status-shipped { background: #e0e7ff; color: #3730a3; }
        .status-delivered { background: #dcfce7; color: #166534; }
        .status-refunding { background: #fed7aa; color: #ea580c; }
        .status-cancelled { background: #fecaca; color: #dc2626; }
        .risk-flag { background: #fef2f2; color: #dc2626; padding: 2px 6px; border-radius: 4px; font-size: 10px; font-weight: 600; margin-left: 4px; }
        .amount-value { font-weight: 600; color: #1e293b; }
        .action-buttons { display: flex; gap: 4px; }
        .btn-sm { padding: 6px 12px; border: 1px solid #e2e8f0; border-radius: 4px; background: white; color: #64748b; font-size: 12px; cursor: pointer; transition: all 0.2s; }
        .btn-sm:hover { background: #f1f5f9; }
        .btn-sm.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .btn-sm.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .btn-sm.danger { background: #ef4444; color: white; border-color: #ef4444; }
        .pagination { padding: 20px 24px; display: flex; justify-content: space-between; align-items: center; }
        .pagination-info { font-size: 14px; color: #64748b; }
        .pagination-controls { display: flex; gap: 8px; }
        .page-btn { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 4px; background: white; color: #64748b; cursor: pointer; font-size: 14px; }
        .page-btn.active { background: #3b82f6; color: white; border-color: #3b82f6; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal.show { display: flex; align-items: center; justify-content: center; }
        .modal-content { background: white; border-radius: 12px; padding: 24px; width: 600px; max-width: 90vw; max-height: 90vh; overflow-y: auto; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .modal-title { font-size: 18px; font-weight: 600; color: #1e293b; margin: 0; }
        .modal-close { background: none; border: none; font-size: 24px; color: #64748b; cursor: pointer; }
        .form-group { margin-bottom: 16px; }
        .form-label { display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 6px; }
        .form-input { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; }
        .form-select { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; background: white; }
        .form-textarea { width: 100%; padding: 10px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; resize: vertical; min-height: 80px; }
        .modal-actions { display: flex; gap: 12px; justify-content: flex-end; margin-top: 20px; padding-top: 16px; border-top: 1px solid #e2e8f0; }
        .btn { padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.2s; }
        .btn.primary { background: #3b82f6; color: white; border: 1px solid #3b82f6; }
        .btn.secondary { background: white; color: #64748b; border: 1px solid #e2e8f0; }
        .progress-container { margin: 16px 0; }
        .progress-bar { width: 100%; height: 8px; background: #f1f5f9; border-radius: 4px; overflow: hidden; }
        .progress-fill { height: 100%; background: #3b82f6; transition: width 0.3s; }
        .progress-text { font-size: 14px; color: #64748b; margin-top: 8px; text-align: center; }
        .refund-info { background: #fef3c7; padding: 12px; border-radius: 8px; margin-bottom: 16px; }
        .refund-amount { font-size: 18px; font-weight: 700; color: #92400e; }
        .refund-reason { font-size: 14px; color: #92400e; margin-top: 4px; }
    </style>
</head>
<body>
    <div class="order-management-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">订单管理</h1>
            <div style="display: flex; gap: 12px;">
                <button class="action-btn" onclick="exportOrders()">
                    <i class="fas fa-download"></i>
                    导出订单
                </button>
                <button class="action-btn warning" onclick="showBatchShip()">
                    <i class="fas fa-shipping-fast"></i>
                    批量发货
                </button>
                <button class="action-btn primary" onclick="refreshOrders()">
                    <i class="fas fa-sync"></i>
                    刷新数据
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-title">今日订单</div>
                <div class="stat-value">234</div>
                <div class="stat-desc">相比昨日 +12.5%</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">待发货</div>
                <div class="stat-value" style="color: #f59e0b;">89</div>
                <div class="stat-desc">需要及时处理</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">今日销售额</div>
                <div class="stat-value" style="color: #10b981;">¥156,847</div>
                <div class="stat-desc">达成率 89.2%</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">异常订单</div>
                <div class="stat-value" style="color: #ef4444;">5</div>
                <div class="stat-desc">需要人工审核</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 内容头部 -->
            <div class="content-header">
                <h2 class="content-title">订单列表</h2>
                <div style="display: flex; gap: 8px;">
                    <button class="action-btn" onclick="toggleAutoRefresh()">
                        <i class="fas fa-clock" id="autoRefreshIcon"></i>
                        自动刷新
                    </button>
                </div>
            </div>

            <!-- 状态标签页 -->
            <div class="filter-tabs">
                <button class="filter-tab active" onclick="filterByStatus('')">全部订单</button>
                <button class="filter-tab" onclick="filterByStatus('pending')">待支付 (12)</button>
                <button class="filter-tab" onclick="filterByStatus('paid')">已支付 (89)</button>
                <button class="filter-tab" onclick="filterByStatus('shipped')">已发货 (156)</button>
                <button class="filter-tab" onclick="filterByStatus('refunding')">退款中 (8)</button>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">订单号</label>
                        <input type="text" class="filter-input" placeholder="请输入订单号" id="orderSearch">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">用户ID</label>
                        <input type="text" class="filter-input" placeholder="请输入用户ID" id="userSearch">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">支付方式</label>
                        <select class="filter-select" id="paymentFilter">
                            <option value="">全部方式</option>
                            <option value="wechat">微信支付</option>
                            <option value="alipay">支付宝</option>
                            <option value="wallet">余额支付</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">时间范围</label>
                        <input type="date" class="filter-input" style="width: 120px;" id="startDate">
                        <span style="margin: 0 8px;">至</span>
                        <input type="date" class="filter-input" style="width: 120px;" id="endDate">
                    </div>
                    <button class="action-btn primary" style="align-self: flex-end;" onclick="searchOrders()">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                </div>
            </div>

            <!-- 批量操作栏 -->
            <div id="bulkActions" class="bulk-actions">
                <div class="bulk-info">
                    已选择 <span id="selectedCount">0</span> 个订单
                </div>
                <div class="bulk-buttons">
                    <button class="action-btn primary" onclick="batchShip()">
                        <i class="fas fa-shipping-fast"></i>
                        批量发货
                    </button>
                    <button class="action-btn danger" onclick="batchCancel()">
                        <i class="fas fa-times"></i>
                        批量取消
                    </button>
                    <button class="action-btn" onclick="batchExport()">
                        <i class="fas fa-download"></i>
                        导出选中
                    </button>
                </div>
            </div>

            <!-- 订单表格 -->
            <div style="overflow-x: auto;">
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                            <th>订单号</th>
                            <th>客户信息</th>
                            <th>商品</th>
                            <th>金额</th>
                            <th>状态</th>
                            <th>支付方式</th>
                            <th>下单时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 示例订单数据 -->
                        <tr>
                            <td><input type="checkbox" class="order-checkbox" data-order="OD202312150001" onchange="updateBulkActions()"></td>
                            <td>
                                <span class="order-id" onclick="viewOrderDetail('OD202312150001')">OD202312150001</span>
                                <span class="risk-flag">异常</span>
                            </td>
                            <td>
                                <div class="customer-info">
                                    <div class="customer-avatar"></div>
                                    <div>
                                        <div class="customer-name">张小明</div>
                                        <div class="customer-id">UID: 12345</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="product-summary">
                                    <img src="../../assets/images/iphone-1.jpg" alt="商品" class="product-image">
                                    <div>
                                        <div>iPhone 14 Pro Max</div>
                                        <div class="product-count">等3件商品</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="amount-value">¥8,999.00</span></td>
                            <td><span class="status-badge status-paid">已支付</span></td>
                            <td>微信支付</td>
                            <td>2023-12-15 14:32</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-sm primary" onclick="shipOrder('OD202312150001')">发货</button>
                                    <button class="btn-sm" onclick="viewOrderDetail('OD202312150001')">详情</button>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td><input type="checkbox" class="order-checkbox" data-order="OD202312150002" onchange="updateBulkActions()"></td>
                            <td><span class="order-id" onclick="viewOrderDetail('OD202312150002')">OD202312150002</span></td>
                            <td>
                                <div class="customer-info">
                                    <div class="customer-avatar"></div>
                                    <div>
                                        <div class="customer-name">李小华</div>
                                        <div class="customer-id">UID: 12346</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="product-summary">
                                    <img src="../../assets/images/nike-shoes.jpg" alt="商品" class="product-image">
                                    <div>
                                        <div>Nike Air Max 270</div>
                                        <div class="product-count">1件商品</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="amount-value">¥1,299.00</span></td>
                            <td><span class="status-badge status-refunding">退款中</span></td>
                            <td>支付宝</td>
                            <td>2023-12-15 12:15</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-sm warning" onclick="processRefund('OD202312150002')">处理退款</button>
                                    <button class="btn-sm" onclick="viewOrderDetail('OD202312150002')">详情</button>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td><input type="checkbox" class="order-checkbox" data-order="OD202312150003" onchange="updateBulkActions()"></td>
                            <td><span class="order-id" onclick="viewOrderDetail('OD202312150003')">OD202312150003</span></td>
                            <td>
                                <div class="customer-info">
                                    <div class="customer-avatar"></div>
                                    <div>
                                        <div class="customer-name">王小红</div>
                                        <div class="customer-id">UID: 12347</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="product-summary">
                                    <img src="../../assets/images/muji-pillow.jpg" alt="商品" class="product-image">
                                    <div>
                                        <div>无印良品羽绒被</div>
                                        <div class="product-count">2件商品</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="amount-value">¥1,798.00</span></td>
                            <td><span class="status-badge status-shipped">已发货</span></td>
                            <td>余额支付</td>
                            <td>2023-12-15 10:45</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-sm" onclick="trackOrder('OD202312150003')">追踪</button>
                                    <button class="btn-sm" onclick="viewOrderDetail('OD202312150003')">详情</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 1-20 条，共 1,234 条记录 | 总金额: ¥1,234,567.89
                </div>
                <div class="pagination-controls">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量发货弹窗 -->
    <div id="batchShipModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">批量发货</h3>
                <button class="modal-close" onclick="closeModal('batchShipModal')">&times;</button>
            </div>
            
            <div class="form-group">
                <label class="form-label">快递公司</label>
                <select class="form-select" id="courierCompany">
                    <option value="SF">顺丰速运</option>
                    <option value="YTO">圆通速递</option>
                    <option value="ZTO">中通快递</option>
                    <option value="STO">申通快递</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">运单号规则</label>
                <input type="text" class="form-input" id="trackingRule" value="SF20231215" placeholder="例: SF+日期+4位随机数">
                <small style="color: #64748b;">系统将自动生成运单号</small>
            </div>
            
            <div class="form-group">
                <label class="form-label">发货备注</label>
                <textarea class="form-textarea" id="shipNote" placeholder="请输入发货备注"></textarea>
            </div>

            <div class="progress-container" id="batchProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">正在处理...</div>
            </div>
            
            <div class="modal-actions">
                <button class="btn secondary" onclick="closeModal('batchShipModal')">取消</button>
                <button class="btn primary" onclick="confirmBatchShip()">确认发货</button>
            </div>
        </div>
    </div>

    <!-- 退款审核弹窗 -->
    <div id="refundModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">退款审核</h3>
                <button class="modal-close" onclick="closeModal('refundModal')">&times;</button>
            </div>
            
            <div class="refund-info">
                <div class="refund-amount">退款金额: ¥1,299.00</div>
                <div class="refund-reason">退款原因: 商品质量问题</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">订单信息</label>
                <div style="background: #f8fafc; padding: 12px; border-radius: 8px; border: 1px solid #e2e8f0;">
                    <p><strong>订单号:</strong> OD202312150002</p>
                    <p><strong>商品:</strong> Nike Air Max 270 运动鞋</p>
                    <p><strong>购买时间:</strong> 2023-12-15 12:15</p>
                    <p><strong>支付方式:</strong> 支付宝</p>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">用户凭证</label>
                <div style="display: flex; gap: 8px;">
                    <img src="../../assets/images/refund-proof-1.jpg" alt="退款凭证" style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px;">
                    <img src="../../assets/images/refund-proof-2.jpg" alt="退款凭证" style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px;">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">审核决定</label>
                <select class="form-select" id="refundDecision">
                    <option value="">请选择审核结果</option>
                    <option value="approve">同意退款</option>
                    <option value="partial">部分退款</option>
                    <option value="reject">拒绝退款</option>
                </select>
            </div>
            
            <div class="form-group" id="partialAmountGroup" style="display: none;">
                <label class="form-label">部分退款金额</label>
                <input type="number" class="form-input" id="partialAmount" placeholder="请输入退款金额">
            </div>
            
            <div class="form-group">
                <label class="form-label">审核备注</label>
                <textarea class="form-textarea" id="refundNote" placeholder="请输入审核意见"></textarea>
            </div>
            
            <div class="modal-actions">
                <button class="btn secondary" onclick="closeModal('refundModal')">取消</button>
                <button class="btn primary" onclick="confirmRefund()">确认审核</button>
            </div>
        </div>
    </div>

    <!-- 订单详情弹窗 -->
    <div id="orderDetailModal" class="modal">
        <div class="modal-content" style="width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">订单详情</h3>
                <button class="modal-close" onclick="closeModal('orderDetailModal')">&times;</button>
            </div>
            
            <div id="orderDetailContent">
                <!-- 订单详情内容将通过JS动态加载 -->
            </div>
        </div>
    </div>

    <script>
        let selectedOrders = new Set();
        let autoRefreshTimer = null;

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.order-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                if (selectAll.checked) {
                    selectedOrders.add(checkbox.dataset.order);
                } else {
                    selectedOrders.delete(checkbox.dataset.order);
                }
            });
            
            updateBulkActions();
        }

        // 更新批量操作栏
        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.order-checkbox:checked');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            
            selectedOrders.clear();
            checkboxes.forEach(cb => selectedOrders.add(cb.dataset.order));
            
            if (selectedOrders.size > 0) {
                bulkActions.classList.add('show');
                selectedCount.textContent = selectedOrders.size;
            } else {
                bulkActions.classList.remove('show');
            }
        }

        // 状态筛选
        function filterByStatus(status) {
            const tabs = document.querySelectorAll('.filter-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // 这里添加实际的筛选逻辑
            console.log('筛选状态:', status);
        }

        // 搜索订单
        function searchOrders() {
            const orderNum = document.getElementById('orderSearch').value;
            const userId = document.getElementById('userSearch').value;
            const payment = document.getElementById('paymentFilter').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            console.log('搜索条件:', { orderNum, userId, payment, startDate, endDate });
            alert('搜索功能执行中...');
        }

        // 批量发货
        function batchShip() {
            if (selectedOrders.size === 0) {
                alert('请先选择要发货的订单');
                return;
            }
            document.getElementById('batchShipModal').classList.add('show');
        }

        // 确认批量发货
        function confirmBatchShip() {
            const courier = document.getElementById('courierCompany').value;
            const rule = document.getElementById('trackingRule').value;
            const note = document.getElementById('shipNote').value;
            
            if (!courier || !rule) {
                alert('请填写完整信息');
                return;
            }
            
            // 显示进度条
            document.getElementById('batchProgress').style.display = 'block';
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            let progress = 0;
            const total = selectedOrders.size;
            
            const timer = setInterval(() => {
                progress += Math.random() * 20;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(timer);
                    
                    progressText.textContent = `批量发货完成！共处理 ${total} 个订单`;
                    
                    setTimeout(() => {
                        closeModal('batchShipModal');
                        document.getElementById('batchProgress').style.display = 'none';
                        progressFill.style.width = '0%';
                        selectedOrders.clear();
                        updateBulkActions();
                        alert('批量发货成功！');
                    }, 2000);
                } else {
                    const processed = Math.floor((progress / 100) * total);
                    progressText.textContent = `正在处理第 ${processed} / ${total} 个订单...`;
                }
                
                progressFill.style.width = progress + '%';
            }, 200);
        }

        // 处理退款
        function processRefund(orderId) {
            document.getElementById('refundModal').classList.add('show');
            
            // 监听退款决定变化
            document.getElementById('refundDecision').addEventListener('change', function() {
                const partialGroup = document.getElementById('partialAmountGroup');
                if (this.value === 'partial') {
                    partialGroup.style.display = 'block';
                } else {
                    partialGroup.style.display = 'none';
                }
            });
        }

        // 确认退款审核
        function confirmRefund() {
            const decision = document.getElementById('refundDecision').value;
            const note = document.getElementById('refundNote').value;
            
            if (!decision || !note) {
                alert('请填写完整信息');
                return;
            }
            
            alert(`退款审核完成: ${decision}`);
            closeModal('refundModal');
        }

        // 查看订单详情
        function viewOrderDetail(orderId) {
            document.getElementById('orderDetailModal').classList.add('show');
            
            // 模拟加载订单详情
            const content = document.getElementById('orderDetailContent');
            content.innerHTML = `
                <div style="display: grid; gap: 20px;">
                    <div>
                        <h4>基础信息</h4>
                        <div style="background: #f8fafc; padding: 16px; border-radius: 8px;">
                            <p><strong>订单号:</strong> ${orderId}</p>
                            <p><strong>下单时间:</strong> 2023-12-15 14:32:15</p>
                            <p><strong>订单状态:</strong> <span class="status-badge status-paid">已支付</span></p>
                            <p><strong>支付方式:</strong> 微信支付</p>
                            <p><strong>订单金额:</strong> ¥8,999.00</p>
                        </div>
                    </div>
                    
                    <div>
                        <h4>客户信息</h4>
                        <div style="background: #f8fafc; padding: 16px; border-radius: 8px;">
                            <p><strong>客户姓名:</strong> 张小明</p>
                            <p><strong>用户ID:</strong> 12345</p>
                            <p><strong>联系电话:</strong> 138****1234</p>
                            <p><strong>收货地址:</strong> 北京市朝阳区某某街道123号</p>
                        </div>
                    </div>
                    
                    <div>
                        <h4>商品清单</h4>
                        <div style="background: white; border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead style="background: #f8fafc;">
                                    <tr>
                                        <th style="padding: 12px; text-align: left;">商品</th>
                                        <th style="padding: 12px; text-align: center;">单价</th>
                                        <th style="padding: 12px; text-align: center;">数量</th>
                                        <th style="padding: 12px; text-align: right;">小计</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <img src="../../assets/images/iphone-1.jpg" style="width: 40px; height: 40px; border-radius: 4px;">
                                                <div>
                                                    <div style="font-weight: 500;">iPhone 14 Pro Max 128GB 深空黑</div>
                                                    <div style="font-size: 12px; color: #64748b;">SKU: IP14PM128BK</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td style="padding: 12px; text-align: center; border-bottom: 1px solid #f1f5f9;">¥8,999.00</td>
                                        <td style="padding: 12px; text-align: center; border-bottom: 1px solid #f1f5f9;">1</td>
                                        <td style="padding: 12px; text-align: right; border-bottom: 1px solid #f1f5f9; font-weight: 600;">¥8,999.00</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 20px; padding-top: 16px; border-top: 1px solid #e2e8f0;">
                    <button class="btn secondary" onclick="closeModal('orderDetailModal')">关闭</button>
                    <button class="btn primary" onclick="shipOrder('${orderId}')">立即发货</button>
                </div>
            `;
        }

        // 发货操作
        function shipOrder(orderId) {
            if (confirm(`确定要发货订单 ${orderId} 吗？`)) {
                alert('发货成功！');
                closeModal('orderDetailModal');
            }
        }

        // 追踪订单
        function trackOrder(orderId) {
            alert(`查看订单 ${orderId} 的物流信息`);
        }

        // 自动刷新
        function toggleAutoRefresh() {
            const icon = document.getElementById('autoRefreshIcon');
            
            if (autoRefreshTimer) {
                clearInterval(autoRefreshTimer);
                autoRefreshTimer = null;
                icon.style.color = '#64748b';
                alert('自动刷新已关闭');
            } else {
                autoRefreshTimer = setInterval(() => {
                    console.log('自动刷新订单数据...');
                }, 30000); // 30秒刷新一次
                icon.style.color = '#10b981';
                alert('自动刷新已开启（30秒间隔）');
            }
        }

        // 其他功能
        function exportOrders() { alert('导出订单功能'); }
        function refreshOrders() { alert('刷新订单数据'); }
        function batchCancel() { alert('批量取消订单'); }
        function batchExport() { alert('导出选中订单'); }

        // 关闭弹窗
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html> 