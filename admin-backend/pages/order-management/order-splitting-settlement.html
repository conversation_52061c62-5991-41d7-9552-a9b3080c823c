<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单拆分与结算管理 - TeleShop B2B2C</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .settlement-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 10px 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .action-btn.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .action-btn.success { background: #10b981; color: white; border-color: #10b981; }
        .action-btn.danger { background: #ef4444; color: white; border-color: #ef4444; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .stat-icon.orders { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.split { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.commission { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-icon.settlement { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        
        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .stat-label {
            color: #64748b;
            font-size: 14px;
        }
        
        .stat-trend {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: 8px;
        }
        
        .trend-up { color: #10b981; }
        .trend-down { color: #ef4444; }
        
        .main-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .content-tabs {
            display: flex;
            border-bottom: 1px solid #e2e8f0;
            overflow-x: auto;
        }
        
        .tab-button {
            padding: 16px 24px;
            border: none;
            background: none;
            color: #64748b;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            white-space: nowrap;
            font-size: 14px;
            font-weight: 500;
        }
        
        .tab-button.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }
        
        .tab-content {
            padding: 24px;
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .config-section {
            background: #f8fafc;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 24px;
        }
        
        .config-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }
        
        .config-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .config-item h4 {
            margin: 0 0 12px 0;
            color: #374151;
            font-size: 16px;
        }
        
        .config-item p {
            margin: 0 0 16px 0;
            color: #64748b;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .switch-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #cbd5e1;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .switch.active {
            background: #3b82f6;
        }
        
        .switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
        }
        
        .switch.active::after {
            transform: translateX(20px);
        }
        
        .order-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .order-table th,
        .order-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }
        
        .order-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            font-size: 12px;
        }
        
        .order-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .order-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 12px;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .order-id {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .order-time {
            color: #64748b;
            font-size: 14px;
        }
        
        .split-info {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
        }
        
        .split-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .merchant-name {
            font-weight: 500;
            color: #374151;
        }
        
        .commission-rate {
            background: #eff6ff;
            color: #1d4ed8;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .amount-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            font-size: 14px;
        }
        
        .amount-item {
            text-align: center;
        }
        
        .amount-label {
            color: #64748b;
            font-size: 12px;
        }
        
        .amount-value {
            font-weight: 600;
            color: #1e293b;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-processing { background: #dbeafe; color: #1e40af; }
        .status-completed { background: #dcfce7; color: #166534; }
        .status-failed { background: #fecaca; color: #dc2626; }
        
        .btn-sm {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-sm.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        .btn-sm.warning { background: #f59e0b; color: white; border-color: #f59e0b; }
        .btn-sm.success { background: #10b981; color: white; border-color: #10b981; }
        
        .rule-editor {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }
        
        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .rule-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .rule-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .rule-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .rule-item:last-child {
            border-bottom: none;
        }
        
        .rule-desc {
            color: #374151;
            font-size: 14px;
        }
        
        .rule-actions {
            display: flex;
            gap: 8px;
        }
        
        @media (max-width: 768px) {
            .settlement-page { padding: 16px; }
            .page-header { flex-direction: column; align-items: stretch; }
            .stats-grid { grid-template-columns: 1fr; gap: 16px; }
            .config-grid { grid-template-columns: 1fr; gap: 16px; }
            .amount-breakdown { grid-template-columns: repeat(2, 1fr); }
        }
    </style>
</head>
<body>
    <div class="settlement-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">订单拆分与结算管理</h1>
            <div class="header-actions">
                <button class="action-btn" onclick="exportReport()">
                    <i class="fas fa-download"></i>
                    导出报表
                </button>
                <button class="action-btn warning" onclick="batchSettle()">
                    <i class="fas fa-calculator"></i>
                    批量结算
                </button>
                <button class="action-btn success" onclick="configRules()">
                    <i class="fas fa-cog"></i>
                    规则配置
                </button>
                <button class="action-btn primary" onclick="refreshData()">
                    <i class="fas fa-sync"></i>
                    刷新数据
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon orders">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="stat-value">2,456</div>
                <div class="stat-label">今日订单总数</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    较昨日 +12.5%
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon split">
                        <i class="fas fa-cut"></i>
                    </div>
                </div>
                <div class="stat-value">189</div>
                <div class="stat-label">拆分订单数</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    拆分率 7.7%
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon commission">
                        <i class="fas fa-percentage"></i>
                    </div>
                </div>
                <div class="stat-value">¥18,456</div>
                <div class="stat-label">今日佣金收入</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    较昨日 +8.3%
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon settlement">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
                <div class="stat-value">¥126,789</div>
                <div class="stat-label">待结算金额</div>
                <div class="stat-trend">
                    <i class="fas fa-clock"></i>
                    345笔待处理
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 标签页导航 -->
            <div class="content-tabs">
                <button class="tab-button active" onclick="switchTab('split-config', this)">拆分配置</button>
                <button class="tab-button" onclick="switchTab('commission-rules', this)">佣金规则</button>
                <button class="tab-button" onclick="switchTab('order-splitting', this)">订单拆分 <span style="background: #3b82f6; color: white; padding: 2px 6px; border-radius: 10px; font-size: 10px; margin-left: 4px;">189</span></button>
                <button class="tab-button" onclick="switchTab('settlement-manage', this)">结算管理</button>
                <button class="tab-button" onclick="switchTab('financial-report', this)">财务报表</button>
            </div>

            <!-- 拆分配置标签页 -->
            <div id="split-config" class="tab-content active">
                <div class="config-section">
                    <div class="config-title">订单拆分算法配置</div>
                    <div class="config-grid">
                        <div class="config-item">
                            <h4>基础拆分规则</h4>
                            <p>设置订单自动拆分的基本规则和条件</p>
                            
                            <div class="form-group">
                                <div class="switch-container">
                                    <label class="form-label">启用自动拆分</label>
                                    <div class="switch active" onclick="toggleSwitch(this)"></div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">拆分触发条件</label>
                                <select class="form-select">
                                    <option>商品来自不同商家</option>
                                    <option>配送方式不同</option>
                                    <option>商品类型不同</option>
                                    <option>自定义规则</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">最小拆分金额</label>
                                <input type="number" class="form-input" value="50" placeholder="输入最小金额">
                            </div>
                        </div>
                        
                        <div class="config-item">
                            <h4>配送拆分策略</h4>
                            <p>根据配送区域和方式进行订单拆分</p>
                            
                            <div class="form-group">
                                <div class="switch-container">
                                    <label class="form-label">按配送区域拆分</label>
                                    <div class="switch active" onclick="toggleSwitch(this)"></div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="switch-container">
                                    <label class="form-label">按配送方式拆分</label>
                                    <div class="switch" onclick="toggleSwitch(this)"></div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">跨区域运费处理</label>
                                <select class="form-select">
                                    <option>按实际运费计算</option>
                                    <option>平摊运费</option>
                                    <option>商家承担</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="config-item">
                            <h4>商家优先级</h4>
                            <p>设置商家在拆分中的优先级和权重</p>
                            
                            <div class="form-group">
                                <label class="form-label">优先级算法</label>
                                <select class="form-select">
                                    <option>按商家等级</option>
                                    <option>按佣金率</option>
                                    <option>按库存充足度</option>
                                    <option>按配送时效</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">权重分配</label>
                                <input type="range" class="form-input" min="0" max="100" value="70">
                            </div>
                        </div>
                        
                        <div class="config-item">
                            <h4>特殊商品处理</h4>
                            <p>对特殊类型商品的拆分处理规则</p>
                            
                            <div class="form-group">
                                <div class="switch-container">
                                    <label class="form-label">预售商品单独拆分</label>
                                    <div class="switch active" onclick="toggleSwitch(this)"></div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="switch-container">
                                    <label class="form-label">定制商品单独拆分</label>
                                    <div class="switch active" onclick="toggleSwitch(this)"></div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">危险品处理</label>
                                <select class="form-select">
                                    <option>禁止拆分</option>
                                    <option>专门物流</option>
                                    <option>特殊标记</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: right; margin-top: 24px;">
                        <button class="action-btn">重置配置</button>
                        <button class="action-btn primary" style="margin-left: 12px;">保存配置</button>
                    </div>
                </div>
            </div>

            <!-- 佣金规则标签页 -->
            <div id="commission-rules" class="tab-content">
                <div class="rule-editor">
                    <div class="rule-header">
                        <div class="rule-title">佣金计算规则</div>
                        <button class="action-btn primary" onclick="addCommissionRule()">
                            <i class="fas fa-plus"></i>
                            添加规则
                        </button>
                    </div>
                    
                    <ul class="rule-list">
                        <li class="rule-item">
                            <div class="rule-desc">数码电器类商品：佣金率 3%，最低佣金 ¥5</div>
                            <div class="rule-actions">
                                <button class="btn-sm">编辑</button>
                                <button class="btn-sm warning">禁用</button>
                            </div>
                        </li>
                        <li class="rule-item">
                            <div class="rule-desc">服装配饰类商品：佣金率 8%，最低佣金 ¥2</div>
                            <div class="rule-actions">
                                <button class="btn-sm">编辑</button>
                                <button class="btn-sm warning">禁用</button>
                            </div>
                        </li>
                        <li class="rule-item">
                            <div class="rule-desc">美妆护肤类商品：佣金率 12%，最低佣金 ¥3</div>
                            <div class="rule-actions">
                                <button class="btn-sm">编辑</button>
                                <button class="btn-sm warning">禁用</button>
                            </div>
                        </li>
                    </ul>
                </div>
                
                <div class="config-section">
                    <div class="config-title">佣金结算配置</div>
                    <div class="config-grid">
                        <div class="config-item">
                            <h4>结算周期</h4>
                            <p>设置佣金结算的时间周期</p>
                            
                            <div class="form-group">
                                <label class="form-label">默认结算周期</label>
                                <select class="form-select">
                                    <option>T+1（次日结算）</option>
                                    <option>T+3（3日后结算）</option>
                                    <option>T+7（7日后结算）</option>
                                    <option>周结算</option>
                                    <option>月结算</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">最低结算金额</label>
                                <input type="number" class="form-input" value="100" placeholder="输入最低金额">
                            </div>
                        </div>
                        
                        <div class="config-item">
                            <h4>手续费设置</h4>
                            <p>配置结算过程中的手续费规则</p>
                            
                            <div class="form-group">
                                <label class="form-label">提现手续费率</label>
                                <input type="number" class="form-input" value="0.6" step="0.1" placeholder="输入费率%">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">最低手续费</label>
                                <input type="number" class="form-input" value="2" placeholder="输入最低费用">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">最高手续费</label>
                                <input type="number" class="form-input" value="50" placeholder="输入最高费用">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单拆分标签页 -->
            <div id="order-splitting" class="tab-content">
                <div style="background: #eff6ff; padding: 16px; border-radius: 8px; margin-bottom: 24px;">
                    <h3 style="color: #1e40af; margin: 0 0 8px 0;">拆分订单监控</h3>
                    <p style="color: #1e40af; margin: 0;">实时监控订单拆分情况，确保拆分逻辑正确执行</p>
                </div>

                <!-- 拆分订单列表 -->
                <div class="order-card">
                    <div class="order-header">
                        <div class="order-id">订单号：TS20231215001</div>
                        <div class="order-time">2023-12-15 14:30:25</div>
                        <span class="status-badge status-processing">处理中</span>
                    </div>
                    
                    <div style="margin-bottom: 12px; color: #64748b; font-size: 14px;">
                        原订单金额：¥1,299 → 拆分为 2 个子订单
                    </div>
                    
                    <div class="split-info">
                        <div class="split-header">
                            <span class="merchant-name">星辰数码专营店</span>
                            <span class="commission-rate">佣金率 3%</span>
                        </div>
                        <div class="amount-breakdown">
                            <div class="amount-item">
                                <div class="amount-label">商品金额</div>
                                <div class="amount-value">¥899</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">平台佣金</div>
                                <div class="amount-value">¥27</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">商家实收</div>
                                <div class="amount-value">¥872</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">配送费</div>
                                <div class="amount-value">¥8</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="split-info">
                        <div class="split-header">
                            <span class="merchant-name">时尚服饰旗舰店</span>
                            <span class="commission-rate">佣金率 8%</span>
                        </div>
                        <div class="amount-breakdown">
                            <div class="amount-item">
                                <div class="amount-label">商品金额</div>
                                <div class="amount-value">¥400</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">平台佣金</div>
                                <div class="amount-value">¥32</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">商家实收</div>
                                <div class="amount-value">¥368</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">配送费</div>
                                <div class="amount-value">¥12</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: right; margin-top: 12px;">
                        <button class="btn-sm">查看详情</button>
                        <button class="btn-sm primary">确认拆分</button>
                        <button class="btn-sm warning">重新拆分</button>
                    </div>
                </div>

                <div class="order-card">
                    <div class="order-header">
                        <div class="order-id">订单号：TS20231215002</div>
                        <div class="order-time">2023-12-15 15:45:12</div>
                        <span class="status-badge status-completed">已完成</span>
                    </div>
                    
                    <div style="margin-bottom: 12px; color: #64748b; font-size: 14px;">
                        原订单金额：¥2,156 → 拆分为 3 个子订单
                    </div>
                    
                    <div class="split-info">
                        <div class="split-header">
                            <span class="merchant-name">美妆护肤专卖</span>
                            <span class="commission-rate">佣金率 12%</span>
                        </div>
                        <div class="amount-breakdown">
                            <div class="amount-item">
                                <div class="amount-label">商品金额</div>
                                <div class="amount-value">¥799</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">平台佣金</div>
                                <div class="amount-value">¥96</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">商家实收</div>
                                <div class="amount-value">¥703</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: right; margin-top: 12px;">
                        <button class="btn-sm">查看详情</button>
                        <button class="btn-sm success">已确认</button>
                    </div>
                </div>
            </div>

            <!-- 结算管理标签页 -->
            <div id="settlement-manage" class="tab-content">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; flex-wrap: wrap; gap: 16px;">
                    <div>
                        <h3 style="margin: 0; color: #1e293b;">结算管理</h3>
                        <p style="margin: 4px 0 0 0; color: #64748b;">管理商家佣金结算和财务对账</p>
                    </div>
                    <div style="display: flex; gap: 12px;">
                        <button class="action-btn" onclick="generateSettlement()">
                            <i class="fas fa-file-invoice"></i>
                            生成结算单
                        </button>
                        <button class="action-btn primary" onclick="batchSettle()">
                            <i class="fas fa-money-bill-wave"></i>
                            批量结算
                        </button>
                    </div>
                </div>

                <table class="order-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox"></th>
                            <th>商家名称</th>
                            <th>结算周期</th>
                            <th>订单数量</th>
                            <th>总销售额</th>
                            <th>平台佣金</th>
                            <th>应结算金额</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox"></td>
                            <td>星辰数码专营店</td>
                            <td>2023-12-11 ~ 2023-12-15</td>
                            <td>156</td>
                            <td>¥89,456</td>
                            <td>¥2,684</td>
                            <td style="font-weight: 600; color: #10b981;">¥86,772</td>
                            <td><span class="status-badge status-pending">待结算</span></td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn-sm">详情</button>
                                    <button class="btn-sm success">结算</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox"></td>
                            <td>时尚服饰旗舰店</td>
                            <td>2023-12-11 ~ 2023-12-15</td>
                            <td>89</td>
                            <td>¥34,567</td>
                            <td>¥2,765</td>
                            <td style="font-weight: 600; color: #10b981;">¥31,802</td>
                            <td><span class="status-badge status-processing">结算中</span></td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn-sm">详情</button>
                                    <button class="btn-sm" disabled>处理中</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox"></td>
                            <td>美妆护肤专卖</td>
                            <td>2023-12-04 ~ 2023-12-10</td>
                            <td>67</td>
                            <td>¥23,890</td>
                            <td>¥2,867</td>
                            <td style="font-weight: 600; color: #64748b;">¥21,023</td>
                            <td><span class="status-badge status-completed">已结算</span></td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn-sm">详情</button>
                                    <button class="btn-sm">凭证</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 财务报表标签页 -->
            <div id="financial-report" class="tab-content">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                    <h3 style="margin: 0; color: #1e293b;">财务报表</h3>
                    <div style="display: flex; gap: 12px;">
                        <select style="padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px;">
                            <option>本月</option>
                            <option>上月</option>
                            <option>本季度</option>
                            <option>自定义</option>
                        </select>
                        <button class="action-btn primary">
                            <i class="fas fa-chart-line"></i>
                            生成报表
                        </button>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">¥456,789</div>
                        <div class="stat-label">总销售额</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            环比 +15.6%
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥34,567</div>
                        <div class="stat-label">平台佣金收入</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            环比 +12.3%
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥422,222</div>
                        <div class="stat-label">商家结算金额</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            环比 +16.1%
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">7.57%</div>
                        <div class="stat-label">平均佣金率</div>
                        <div class="stat-trend">
                            <i class="fas fa-minus"></i>
                            环比持平
                        </div>
                    </div>
                </div>

                <div style="background: white; border: 1px solid #e2e8f0; border-radius: 8px; padding: 24px; margin-top: 24px;">
                    <h4 style="margin: 0 0 16px 0; color: #1e293b;">商家佣金排行</h4>
                    <table class="order-table">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>商家名称</th>
                                <th>订单数</th>
                                <th>销售额</th>
                                <th>佣金收入</th>
                                <th>佣金率</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="font-weight: 600; color: #f59e0b;">1</td>
                                <td>星辰数码专营店</td>
                                <td>456</td>
                                <td>¥156,789</td>
                                <td>¥4,704</td>
                                <td>3.0%</td>
                            </tr>
                            <tr>
                                <td style="font-weight: 600; color: #94a3b8;">2</td>
                                <td>美妆护肤专卖</td>
                                <td>234</td>
                                <td>¥89,456</td>
                                <td>¥10,735</td>
                                <td>12.0%</td>
                            </tr>
                            <tr>
                                <td style="font-weight: 600; color: #cd7f32;">3</td>
                                <td>时尚服饰旗舰店</td>
                                <td>345</td>
                                <td>¥67,890</td>
                                <td>¥5,431</td>
                                <td>8.0%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabId, buttonElement) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有按钮的激活状态
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabId).classList.add('active');
            
            // 激活选中的按钮
            buttonElement.classList.add('active');
        }
        
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }
        
        function exportReport() {
            alert('导出财务报表');
        }
        
        function batchSettle() {
            alert('批量结算处理');
        }
        
        function configRules() {
            alert('配置拆分规则');
        }
        
        function refreshData() {
            alert('刷新数据');
        }
        
        function addCommissionRule() {
            alert('添加佣金规则');
        }
        
        function generateSettlement() {
            alert('生成结算单');
        }
    </script>
</body>
</html>