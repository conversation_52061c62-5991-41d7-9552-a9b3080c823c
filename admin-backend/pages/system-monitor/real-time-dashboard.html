<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时监控仪表板 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --background-color: #0f172a;
            --card-background: #1e293b;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --border-color: #334155;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            --radius: 0.75rem;
            --transition: all 0.3s ease;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .dashboard-container {
            padding: 1.5rem;
            max-width: 1600px;
            margin: 0 auto;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem 0;
        }

        .header-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: var(--radius);
            background: var(--card-background);
            border: 1px solid var(--border-color);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .refresh-btn {
            padding: 0.5rem 1rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .refresh-btn:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .metric-title {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .metric-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .metric-change {
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .change-positive {
            color: var(--success-color);
        }

        .change-negative {
            color: var(--danger-color);
        }

        .chart-container {
            height: 60px;
            margin-top: 1rem;
            position: relative;
        }

        .mini-chart {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
            border-radius: var(--radius);
            position: relative;
            overflow: hidden;
        }

        .chart-line {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background: linear-gradient(to right, var(--primary-color), var(--success-color));
            clip-path: polygon(0 100%, 10% 80%, 20% 60%, 30% 70%, 40% 50%, 50% 40%, 60% 60%, 70% 30%, 80% 45%, 90% 20%, 100% 35%, 100% 100%);
        }

        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        .content-left {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .content-right {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .system-overview {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 2rem;
            box-shadow: var(--shadow);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .system-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            border-radius: var(--radius);
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .alerts-panel {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: var(--radius);
            margin-bottom: 0.75rem;
            border-left: 4px solid;
            transition: var(--transition);
        }

        .alert-critical {
            background: rgba(239, 68, 68, 0.1);
            border-color: var(--danger-color);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: var(--warning-color);
        }

        .alert-info {
            background: rgba(6, 182, 212, 0.1);
            border-color: var(--info-color);
        }

        .alert-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .alert-desc {
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .alert-time {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .performance-chart {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 2rem;
            box-shadow: var(--shadow);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .chart-legend {
            display: flex;
            gap: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        .chart-area {
            height: 200px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: var(--radius);
            position: relative;
            overflow: hidden;
        }

        .activity-feed {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }

        .activity-item {
            display: flex;
            gap: 1rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .activity-content {
            flex: 1;
        }

        .activity-action {
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 1rem;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .dashboard-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .system-stats {
                grid-template-columns: 1fr;
            }
        }

        .realtime-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .loading-skeleton {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 仪表板头部 -->
        <div class="dashboard-header">
            <div>
                <h1 class="header-title">实时监控仪表板</h1>
                <span class="realtime-badge">
                    <span class="status-dot"></span>
                    实时更新
                </span>
            </div>
            <div class="header-controls">
                <div class="status-indicator">
                    <span class="status-dot"></span>
                    系统正常运行
                </div>
                <button class="refresh-btn" onclick="refreshDashboard()">
                    🔄 刷新数据
                </button>
            </div>
        </div>

        <!-- 关键指标网格 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">在线用户数</span>
                    <div class="metric-icon" style="background: rgba(59, 130, 246, 0.1); color: var(--primary-color);">
                        👥
                    </div>
                </div>
                <div class="metric-value" id="online-users">12,847</div>
                <div class="metric-change change-positive">
                    ↗ +5.2% 较昨日
                </div>
                <div class="chart-container">
                    <div class="mini-chart">
                        <div class="chart-line"></div>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">订单处理率</span>
                    <div class="metric-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                        📊
                    </div>
                </div>
                <div class="metric-value" id="order-rate">98.6%</div>
                <div class="metric-change change-positive">
                    ↗ +1.2% 较昨日
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 98.6%"></div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">系统响应时间</span>
                    <div class="metric-icon" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                        ⚡
                    </div>
                </div>
                <div class="metric-value" id="response-time">247ms</div>
                <div class="metric-change change-negative">
                    ↘ +12ms 较昨日
                </div>
                <div class="chart-container">
                    <div class="mini-chart">
                        <div class="chart-line"></div>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">收入统计</span>
                    <div class="metric-icon" style="background: rgba(6, 182, 212, 0.1); color: var(--info-color);">
                        💰
                    </div>
                </div>
                <div class="metric-value" id="revenue">￥324.7K</div>
                <div class="metric-change change-positive">
                    ↗ +8.5% 较昨日
                </div>
                <div class="chart-container">
                    <div class="mini-chart">
                        <div class="chart-line"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <div class="content-left">
                <!-- 系统概览 -->
                <div class="system-overview">
                    <h2 class="section-title">
                        🖥️ 系统概览
                    </h2>
                    <div class="system-stats">
                        <div class="stat-item">
                            <div class="stat-value" style="color: var(--primary-color);">99.9%</div>
                            <div class="stat-label">系统稳定性</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 99.9%"></div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" style="color: var(--success-color);">68%</div>
                            <div class="stat-label">CPU使用率</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 68%"></div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" style="color: var(--warning-color);">84%</div>
                            <div class="stat-label">内存使用率</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 84%"></div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" style="color: var(--info-color);">42%</div>
                            <div class="stat-label">磁盘使用率</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 42%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 性能图表 -->
                <div class="performance-chart">
                    <div class="chart-header">
                        <h2 class="section-title">📈 性能趋势</h2>
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background: var(--primary-color);"></div>
                                CPU
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: var(--success-color);"></div>
                                内存
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: var(--warning-color);"></div>
                                网络
                            </div>
                        </div>
                    </div>
                    <div class="chart-area">
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: var(--text-secondary);">
                            📊 实时性能图表<br>
                            <small>Chart.js 集成位置</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-right">
                <!-- 告警面板 -->
                <div class="alerts-panel">
                    <h2 class="section-title">🚨 系统告警</h2>
                    
                    <div class="alert-item alert-warning">
                        <div class="alert-icon" style="background: var(--warning-color); color: white;">
                            ⚠️
                        </div>
                        <div class="alert-content">
                            <div class="alert-title">内存使用率过高</div>
                            <div class="alert-desc">服务器内存使用率达到84%，建议优化</div>
                            <div class="alert-time">5分钟前</div>
                        </div>
                    </div>

                    <div class="alert-item alert-info">
                        <div class="alert-icon" style="background: var(--info-color); color: white;">
                            ℹ️
                        </div>
                        <div class="alert-content">
                            <div class="alert-title">数据库连接池</div>
                            <div class="alert-desc">连接池使用率正常，性能稳定</div>
                            <div class="alert-time">10分钟前</div>
                        </div>
                    </div>

                    <div class="alert-item alert-critical">
                        <div class="alert-icon" style="background: var(--danger-color); color: white;">
                            🔴
                        </div>
                        <div class="alert-content">
                            <div class="alert-title">支付服务异常</div>
                            <div class="alert-desc">检测到支付接口响应超时</div>
                            <div class="alert-time">2小时前</div>
                        </div>
                    </div>
                </div>

                <!-- 活动动态 -->
                <div class="activity-feed">
                    <h2 class="section-title">📋 系统活动</h2>
                    
                    <div class="activity-item">
                        <div class="activity-avatar">JS</div>
                        <div class="activity-content">
                            <div class="activity-action">系统管理员启动了数据备份</div>
                            <div class="activity-time">3分钟前</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-avatar">DB</div>
                        <div class="activity-content">
                            <div class="activity-action">数据库自动优化完成</div>
                            <div class="activity-time">15分钟前</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-avatar">SV</div>
                        <div class="activity-content">
                            <div class="activity-action">服务器负载均衡重新配置</div>
                            <div class="activity-time">1小时前</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-avatar">UP</div>
                        <div class="activity-content">
                            <div class="activity-action">系统版本更新部署完成</div>
                            <div class="activity-time">2小时前</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-avatar">SC</div>
                        <div class="activity-content">
                            <div class="activity-action">安全扫描检查通过</div>
                            <div class="activity-time">4小时前</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 实时数据更新
        let updateInterval;

        function initializeRealTimeUpdates() {
            updateInterval = setInterval(() => {
                updateMetrics();
                updateSystemStats();
                addActivityLog();
            }, 5000); // 每5秒更新一次
        }

        function updateMetrics() {
            // 模拟实时数据更新
            const onlineUsers = document.getElementById('online-users');
            const orderRate = document.getElementById('order-rate');
            const responseTime = document.getElementById('response-time');
            const revenue = document.getElementById('revenue');

            if (onlineUsers) {
                const currentUsers = parseInt(onlineUsers.textContent.replace(',', ''));
                const newUsers = currentUsers + Math.floor(Math.random() * 20 - 10);
                onlineUsers.textContent = newUsers.toLocaleString();
            }

            if (orderRate) {
                const rate = (98 + Math.random() * 2).toFixed(1);
                orderRate.textContent = rate + '%';
            }

            if (responseTime) {
                const time = Math.floor(200 + Math.random() * 100);
                responseTime.textContent = time + 'ms';
            }

            if (revenue) {
                const current = parseFloat(revenue.textContent.replace(/[￥K,]/g, ''));
                const newRevenue = (current + Math.random() * 0.5).toFixed(1);
                revenue.textContent = '￥' + newRevenue + 'K';
            }
        }

        function updateSystemStats() {
            // 更新系统统计数据
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                if (bar.style.width.includes('%')) {
                    const currentWidth = parseFloat(bar.style.width);
                    const newWidth = Math.min(100, Math.max(0, currentWidth + (Math.random() * 6 - 3)));
                    bar.style.width = newWidth.toFixed(1) + '%';
                    
                    // 更新对应的数值
                    const statValue = bar.closest('.stat-item')?.querySelector('.stat-value');
                    if (statValue) {
                        statValue.textContent = newWidth.toFixed(1) + '%';
                    }
                }
            });
        }

        function addActivityLog() {
            // 随机添加新的活动日志
            const activities = [
                '自动备份任务完成',
                '缓存清理操作执行',
                '日志文件轮转',
                '健康检查通过',
                '定时任务调度',
                '数据同步完成'
            ];
            
            const activityFeed = document.querySelector('.activity-feed');
            const items = activityFeed.querySelectorAll('.activity-item');
            
            // 如果活动项超过5个，移除最后一个
            if (items.length > 5) {
                items[items.length - 1].remove();
            }
            
            // 随机生成新活动
            if (Math.random() < 0.3) {
                const activity = activities[Math.floor(Math.random() * activities.length)];
                const newItem = document.createElement('div');
                newItem.className = 'activity-item';
                newItem.innerHTML = `
                    <div class="activity-avatar">SY</div>
                    <div class="activity-content">
                        <div class="activity-action">${activity}</div>
                        <div class="activity-time">刚刚</div>
                    </div>
                `;
                
                const firstItem = activityFeed.querySelector('.activity-item');
                if (firstItem) {
                    firstItem.parentNode.insertBefore(newItem, firstItem);
                }
            }
        }

        function refreshDashboard() {
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.innerHTML = '🔄 刷新中...';
            refreshBtn.disabled = true;
            
            // 模拟刷新延迟
            setTimeout(() => {
                updateMetrics();
                updateSystemStats();
                refreshBtn.innerHTML = '🔄 刷新数据';
                refreshBtn.disabled = false;
                
                // 显示刷新成功提示
                showNotification('数据刷新成功', 'success');
            }, 1000);
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                background: var(--card-background);
                border: 1px solid var(--border-color);
                border-radius: var(--radius);
                color: var(--text-primary);
                box-shadow: var(--shadow);
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 图表动画效果
        function animateCharts() {
            const chartLines = document.querySelectorAll('.chart-line');
            chartLines.forEach(line => {
                line.style.animation = 'none';
                line.offsetHeight; // 触发重排
                line.style.animation = 'chartPulse 3s ease-in-out infinite';
            });
        }

        // 添加图表动画CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes chartPulse {
                0%, 100% { opacity: 0.8; transform: scaleY(1); }
                50% { opacity: 1; transform: scaleY(1.1); }
            }
            
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后启动实时更新
        document.addEventListener('DOMContentLoaded', () => {
            initializeRealTimeUpdates();
            animateCharts();
            
            // 显示欢迎消息
            setTimeout(() => {
                showNotification('实时监控仪表板已启动', 'success');
            }, 500);
        });

        // 页面失焦时停止更新，重新聚焦时恢复
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                clearInterval(updateInterval);
            } else {
                initializeRealTimeUpdates();
            }
        });

        // 窗口关闭前清理定时器
        window.addEventListener('beforeunload', () => {
            clearInterval(updateInterval);
        });
    </script>
</body>
</html> 