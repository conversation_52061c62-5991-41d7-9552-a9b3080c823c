<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员权限 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="permission-management-fixes.css">
    <style>
        .permission-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .overview-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .overview-card h3 {
            color: #666;
            font-size: 14px;
            margin: 0 0 10px 0;
        }
        
        .overview-card .value {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .management-tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .tab-header {
            display: flex;
            border-bottom: 1px solid #e9ecef;
        }
        
        .tab-button {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: #f8f9fa;
        }
        
        .tab-content {
            padding: 20px;
        }
        
        .admin-table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .admin-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .admin-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
        }
        
        .admin-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .admin-table th:nth-child(1) { width: 20%; }
        .admin-table th:nth-child(2) { width: 15%; }
        .admin-table th:nth-child(3) { width: 15%; }
        .admin-table th:nth-child(4) { width: 15%; }
        .admin-table th:nth-child(5) { width: 10%; }
        .admin-table th:nth-child(6) { width: 15%; }
        .admin-table th:nth-child(7) { width: 10%; }
        
        .role-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .role-super { background: #dc3545; color: white; }
        .role-admin { background: #007bff; color: white; }
        .role-manager { background: #28a745; color: white; }
        .role-operator { background: #ffc107; color: #212529; }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-locked { background: #fff3cd; color: #856404; }
        
        .permission-grid {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .module-list {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .module-item {
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .module-item:hover,
        .module-item.active {
            background: #e9ecef;
        }
        
        .permission-details {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .permission-group {
            margin-bottom: 20px;
        }
        
        .permission-group h4 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .permission-checkboxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .permission-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .close {
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .role-permissions {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }
        
        @media (max-width: 768px) {
            .permission-grid {
                grid-template-columns: 1fr;
            }
            
            .admin-table-container {
                overflow-x: auto;
            }
            
            .admin-table {
                min-width: 800px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-user-shield"></i> 管理员权限</h2>
            </div>
            <nav class="sidebar-nav">
                <div class="menu-section">
                    <h3>核心功能</h3>
                    <ul class="menu-list">
                        <li><a href="../dashboard/index.html"><i class="fas fa-tachometer-alt"></i> 仪表板总览</a></li>
                        <li><a href="../user-management/index.html"><i class="fas fa-users"></i> 用户管理</a></li>
                        <li><a href="../product-management/index.html"><i class="fas fa-box"></i> 商品管理</a></li>
                        <li><a href="../order-management/index.html"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>业务管理</h3>
                    <ul class="menu-list">
                        <li><a href="../content-management/chat-management.html"><i class="fas fa-comments"></i> 内容管理</a></li>
                        <li><a href="../merchant-management/index.html"><i class="fas fa-store"></i> 商户管理</a></li>
                        <li><a href="../financial-management/index.html"><i class="fas fa-chart-line"></i> 财务管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>钱包管理</h3>
                    <ul class="menu-list">
                        <li><a href="../wallet-management/user-wallet-management.html"><i class="fas fa-wallet"></i> 用户钱包管理</a></li>
                        <li><a href="../wallet-management/transaction-management.html"><i class="fas fa-exchange-alt"></i> 交易管理</a></li>
                        <li><a href="../wallet-management/deposit-withdrawal.html"><i class="fas fa-money-bill-wave"></i> 充值提现管理</a></li>
                        <li><a href="../wallet-management/risk-control.html"><i class="fas fa-shield-alt"></i> 风控管理</a></li>
                        <li><a href="../wallet-management/digital-currency.html"><i class="fas fa-coins"></i> 数字货币管理</a></li>
                        <li><a href="../wallet-management/financial-reconciliation.html"><i class="fas fa-calculator"></i> 财务对账</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>权限管理</h3>
                    <ul class="menu-list">
                        <li class="active"><a href="admin-permissions.html"><i class="fas fa-user-shield"></i> 管理员权限</a></li>
                        <li><a href="api-permissions.html"><i class="fas fa-key"></i> API权限管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>系统设置</h3>
                    <ul class="menu-list">
                        <li><a href="../system-settings/index.html"><i class="fas fa-cog"></i> 系统配置</a></li>
                        <li><a href="../reports-analytics/index.html"><i class="fas fa-chart-bar"></i> 数据分析</a></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="main-header">
                <h1>管理员权限管理</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="addAdmin()">新增管理员</button>
                    <button class="btn btn-success" onclick="exportPermissions()">导出权限</button>
                </div>
            </div>

            <!-- 权限概览 -->
            <div class="permission-overview">
                <div class="overview-card">
                    <h3>总管理员</h3>
                    <p class="value">25</p>
                </div>
                <div class="overview-card">
                    <h3>活跃管理员</h3>
                    <p class="value">22</p>
                </div>
                <div class="overview-card">
                    <h3>角色类型</h3>
                    <p class="value">4</p>
                </div>
                <div class="overview-card">
                    <h3>权限模块</h3>
                    <p class="value">8</p>
                </div>
            </div>

            <!-- 管理标签 -->
            <div class="management-tabs">
                <div class="tab-header">
                    <button class="tab-button active" onclick="switchTab('admins')">管理员列表</button>
                    <button class="tab-button" onclick="switchTab('permissions')">权限配置</button>
                    <button class="tab-button" onclick="switchTab('approval')">审批流程</button>
                </div>

                <!-- 管理员列表 -->
                <div id="admins-tab" class="tab-content">
                    <div class="admin-table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>管理员信息</th>
                                    <th>角色</th>
                                    <th>部门</th>
                                    <th>最后登录</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div><strong>张管理员</strong></div>
                                        <div style="color: #666; font-size: 12px;"><EMAIL></div>
                                        <div style="color: #666; font-size: 12px;">135****7890</div>
                                    </td>
                                    <td><span class="role-badge role-super">超级管理员</span></td>
                                    <td>技术部</td>
                                    <td>2024-01-15 14:30</td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td>2023-08-15</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewAdmin(1)">详情</button>
                                            <button class="btn-xs btn-warning" onclick="editAdmin(1)">编辑</button>
                                            <button class="btn-xs btn-danger" onclick="lockAdmin(1)">锁定</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div><strong>李运营</strong></div>
                                        <div style="color: #666; font-size: 12px;"><EMAIL></div>
                                        <div style="color: #666; font-size: 12px;">138****1234</div>
                                    </td>
                                    <td><span class="role-badge role-manager">运营经理</span></td>
                                    <td>运营部</td>
                                    <td>2024-01-15 10:15</td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td>2023-09-20</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewAdmin(2)">详情</button>
                                            <button class="btn-xs btn-warning" onclick="editAdmin(2)">编辑</button>
                                            <button class="btn-xs btn-danger" onclick="lockAdmin(2)">锁定</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div><strong>王客服</strong></div>
                                        <div style="color: #666; font-size: 12px;"><EMAIL></div>
                                        <div style="color: #666; font-size: 12px;">139****5678</div>
                                    </td>
                                    <td><span class="role-badge role-operator">客服专员</span></td>
                                    <td>客服部</td>
                                    <td>2024-01-10 16:45</td>
                                    <td><span class="status-badge status-locked">锁定</span></td>
                                    <td>2023-10-12</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewAdmin(3)">详情</button>
                                            <button class="btn-xs btn-success" onclick="unlockAdmin(3)">解锁</button>
                                            <button class="btn-xs btn-danger" onclick="deleteAdmin(3)">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 权限配置 -->
                <div id="permissions-tab" class="tab-content" style="display: none;">
                    <div class="permission-grid">
                        <div class="module-list">
                            <h4>系统模块</h4>
                            <div class="module-item active" onclick="selectModule('dashboard')">
                                📊 仪表板
                            </div>
                            <div class="module-item" onclick="selectModule('users')">
                                👥 用户管理
                            </div>
                            <div class="module-item" onclick="selectModule('content')">
                                💬 内容管理
                            </div>
                            <div class="module-item" onclick="selectModule('merchants')">
                                🏪 商户管理
                            </div>
                            <div class="module-item" onclick="selectModule('orders')">
                                📦 订单管理
                            </div>
                            <div class="module-item" onclick="selectModule('finance')">
                                💰 财务管理
                            </div>
                            <div class="module-item" onclick="selectModule('wallet')">
                                💳 钱包管理
                            </div>
                            <div class="module-item" onclick="selectModule('system')">
                                ⚙️ 系统设置
                            </div>
                        </div>

                        <div class="permission-details">
                            <h4>仪表板权限</h4>
                            <div class="permission-group">
                                <h5>数据查看</h5>
                                <div class="permission-checkboxes">
                                    <div class="permission-checkbox">
                                        <input type="checkbox" id="dashboard_view" checked>
                                        <label for="dashboard_view">查看仪表板</label>
                                    </div>
                                    <div class="permission-checkbox">
                                        <input type="checkbox" id="dashboard_stats" checked>
                                        <label for="dashboard_stats">查看统计数据</label>
                                    </div>
                                    <div class="permission-checkbox">
                                        <input type="checkbox" id="dashboard_charts">
                                        <label for="dashboard_charts">查看图表分析</label>
                                    </div>
                                    <div class="permission-checkbox">
                                        <input type="checkbox" id="dashboard_export">
                                        <label for="dashboard_export">导出数据</label>
                                    </div>
                                </div>
                            </div>
                            <div class="permission-group">
                                <h5>快捷操作</h5>
                                <div class="permission-checkboxes">
                                    <div class="permission-checkbox">
                                        <input type="checkbox" id="dashboard_shortcuts">
                                        <label for="dashboard_shortcuts">使用快捷操作</label>
                                    </div>
                                    <div class="permission-checkbox">
                                        <input type="checkbox" id="dashboard_alerts">
                                        <label for="dashboard_alerts">管理系统警报</label>
                                    </div>
                                </div>
                            </div>
                            <div style="margin-top: 20px;">
                                <button class="btn btn-primary" onclick="savePermissions()">保存权限配置</button>
                                <button class="btn btn-secondary" onclick="resetPermissions()">重置</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审批流程 -->
                <div id="approval-tab" class="tab-content" style="display: none;">
                    <div class="approval-settings">
                        <h4>审批流程配置</h4>
                        <div class="form-group">
                            <label>大额交易审批金额阈值（元）</label>
                            <input type="number" value="10000" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>商户入驻审批流程</label>
                            <select class="form-control">
                                <option>一级审批（运营经理）</option>
                                <option>二级审批（运营经理→超级管理员）</option>
                                <option>三级审批（客服→运营经理→超级管理员）</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>用户举报处理审批</label>
                            <select class="form-control">
                                <option>客服专员处理</option>
                                <option>运营经理审批</option>
                                <option>超级管理员审批</option>
                            </select>
                        </div>
                        <button class="btn btn-primary">保存审批配置</button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增/编辑管理员模态框 -->
    <div id="adminModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增管理员</h3>
                <span class="close" onclick="closeModal('adminModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="adminForm">
                    <div class="form-group">
                        <label>姓名 *</label>
                        <input type="text" name="name" required class="form-control">
                    </div>
                    <div class="form-group">
                        <label>邮箱 *</label>
                        <input type="email" name="email" required class="form-control">
                    </div>
                    <div class="form-group">
                        <label>手机号 *</label>
                        <input type="tel" name="phone" required class="form-control">
                    </div>
                    <div class="form-group">
                        <label>角色 *</label>
                        <select name="role" required class="form-control">
                            <option value="">请选择角色</option>
                            <option value="super">超级管理员</option>
                            <option value="admin">系统管理员</option>
                            <option value="manager">运营经理</option>
                            <option value="operator">客服专员</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>部门</label>
                        <select name="department" class="form-control">
                            <option value="">请选择部门</option>
                            <option value="tech">技术部</option>
                            <option value="operation">运营部</option>
                            <option value="service">客服部</option>
                            <option value="finance">财务部</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>权限配置</label>
                        <div class="role-permissions">
                            <div class="permission-checkbox">
                                <input type="checkbox" id="perm_users">
                                <label for="perm_users">用户管理</label>
                            </div>
                            <div class="permission-checkbox">
                                <input type="checkbox" id="perm_content">
                                <label for="perm_content">内容管理</label>
                            </div>
                            <div class="permission-checkbox">
                                <input type="checkbox" id="perm_merchants">
                                <label for="perm_merchants">商户管理</label>
                            </div>
                            <div class="permission-checkbox">
                                <input type="checkbox" id="perm_orders">
                                <label for="perm_orders">订单管理</label>
                            </div>
                            <div class="permission-checkbox">
                                <input type="checkbox" id="perm_finance">
                                <label for="perm_finance">财务管理</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>备注</label>
                        <textarea name="notes" rows="3" class="form-control" placeholder="请输入备注信息..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('adminModal')">取消</button>
                <button class="btn btn-primary" onclick="saveAdmin()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 菜单交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化菜单状态
            const menuItems = document.querySelectorAll('.admin-sidebar .menu-list li');
            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && link.getAttribute('href') === '#') {
                    // 如果是父菜单项，添加展开/收起功能
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const submenu = item.querySelector('.submenu');
                        if (submenu) {
                            item.classList.toggle('open');
                            submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
                        }
                    });
                }
            });
        });

        // 切换标签
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            
            // 移除所有按钮的active类
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').style.display = 'block';
            
            // 添加active类到对应按钮
            event.target.classList.add('active');
        }

        // 选择模块
        function selectModule(moduleName) {
            document.querySelectorAll('.module-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 这里可以加载对应模块的权限配置
            loadModulePermissions(moduleName);
        }

        // 加载模块权限
        function loadModulePermissions(moduleName) {
            // 根据模块名加载不同的权限配置
            console.log('加载模块权限:', moduleName);
        }

        // 新增管理员
        function addAdmin() {
            document.getElementById('modalTitle').textContent = '新增管理员';
            document.getElementById('adminForm').reset();
            document.getElementById('adminModal').style.display = 'block';
        }

        // 编辑管理员
        function editAdmin(adminId) {
            document.getElementById('modalTitle').textContent = '编辑管理员';
            // 这里应该加载管理员数据
            document.getElementById('adminModal').style.display = 'block';
        }

        // 查看管理员详情
        function viewAdmin(adminId) {
            alert('查看管理员详情 ID: ' + adminId);
        }

        // 锁定管理员
        function lockAdmin(adminId) {
            if (confirm('确定要锁定该管理员吗？')) {
                alert('管理员已锁定');
                location.reload();
            }
        }

        // 解锁管理员
        function unlockAdmin(adminId) {
            if (confirm('确定要解锁该管理员吗？')) {
                alert('管理员已解锁');
                location.reload();
            }
        }

        // 删除管理员
        function deleteAdmin(adminId) {
            if (confirm('确定要删除该管理员吗？此操作不可恢复！')) {
                alert('管理员已删除');
                location.reload();
            }
        }

        // 保存管理员
        function saveAdmin() {
            const form = document.getElementById('adminForm');
            const formData = new FormData(form);
            
            // 验证表单
            if (!form.checkValidity()) {
                alert('请填写完整信息');
                return;
            }
            
            alert('管理员信息已保存');
            closeModal('adminModal');
            location.reload();
        }

        // 保存权限配置
        function savePermissions() {
            alert('权限配置已保存');
        }

        // 重置权限配置
        function resetPermissions() {
            if (confirm('确定要重置权限配置吗？')) {
                document.querySelectorAll('.permission-checkboxes input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = false;
                });
            }
        }

        // 导出权限
        function exportPermissions() {
            alert('正在导出权限配置...');
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html> 