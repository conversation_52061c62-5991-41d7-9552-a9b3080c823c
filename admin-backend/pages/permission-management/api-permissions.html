<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API权限管理 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="permission-management-fixes.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .api-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .overview-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .overview-card h3 {
            color: #666;
            font-size: 14px;
            margin: 0 0 10px 0;
        }
        
        .overview-card .value {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .api-tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .tab-header {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            overflow-x: auto;
        }
        
        .tab-button {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            white-space: nowrap;
        }
        
        .tab-button.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: #f8f9fa;
        }
        
        .tab-content {
            padding: 20px;
        }
        
        .api-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .api-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
        }
        
        .api-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .api-key {
            font-family: monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-expired { background: #e2e3e5; color: #383d41; }
        
        .rate-limit {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }
        
        .app-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .app-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .app-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-item label {
            font-weight: 500;
            color: #666;
        }
        
        .security-config {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .config-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        
        .config-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .config-switch {
            width: 40px;
            height: 20px;
            border-radius: 10px;
            background: #ccc;
            position: relative;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .config-switch.active {
            background: #28a745;
        }
        
        .config-switch::after {
            content: '';
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: white;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s;
        }
        
        .config-switch.active::after {
            left: 22px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        
        .usage-chart {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 700px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .close {
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        @media (max-width: 768px) {
            .security-config {
                grid-template-columns: 1fr;
            }
            
            .api-overview {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .api-table-container {
                overflow-x: auto;
            }
            
            .api-table {
                min-width: 900px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-key"></i> API权限管理</h2>
            </div>
            <nav class="sidebar-nav">
                <div class="menu-section">
                    <h3>核心功能</h3>
                    <ul class="menu-list">
                        <li><a href="../dashboard/index.html"><i class="fas fa-tachometer-alt"></i> 仪表板总览</a></li>
                        <li><a href="../user-management/index.html"><i class="fas fa-users"></i> 用户管理</a></li>
                        <li><a href="../product-management/index.html"><i class="fas fa-box"></i> 商品管理</a></li>
                        <li><a href="../order-management/index.html"><i class="fas fa-shopping-cart"></i> 订单管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>业务管理</h3>
                    <ul class="menu-list">
                        <li><a href="../content-management/chat-management.html"><i class="fas fa-comments"></i> 内容管理</a></li>
                        <li><a href="../merchant-management/index.html"><i class="fas fa-store"></i> 商户管理</a></li>
                        <li><a href="../financial-management/index.html"><i class="fas fa-chart-line"></i> 财务管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>钱包管理</h3>
                    <ul class="menu-list">
                        <li><a href="../wallet-management/user-wallet-management.html"><i class="fas fa-wallet"></i> 用户钱包管理</a></li>
                        <li><a href="../wallet-management/transaction-management.html"><i class="fas fa-exchange-alt"></i> 交易管理</a></li>
                        <li><a href="../wallet-management/deposit-withdrawal.html"><i class="fas fa-money-bill-wave"></i> 充值提现管理</a></li>
                        <li><a href="../wallet-management/risk-control.html"><i class="fas fa-shield-alt"></i> 风控管理</a></li>
                        <li><a href="../wallet-management/digital-currency.html"><i class="fas fa-coins"></i> 数字货币管理</a></li>
                        <li><a href="../wallet-management/financial-reconciliation.html"><i class="fas fa-calculator"></i> 财务对账</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>权限管理</h3>
                    <ul class="menu-list">
                        <li><a href="admin-permissions.html"><i class="fas fa-user-shield"></i> 管理员权限</a></li>
                        <li class="active"><a href="api-permissions.html"><i class="fas fa-key"></i> API权限管理</a></li>
                    </ul>
                </div>
                
                <div class="menu-section">
                    <h3>系统设置</h3>
                    <ul class="menu-list">
                        <li><a href="../system-settings/index.html"><i class="fas fa-cog"></i> 系统配置</a></li>
                        <li><a href="../reports-analytics/index.html"><i class="fas fa-chart-bar"></i> 数据分析</a></li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="main-header">
                <h1>API权限管理</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="createApiKey()">创建API密钥</button>
                    <button class="btn btn-success" onclick="exportApiReport()">导出报告</button>
                </div>
            </div>

            <!-- API概览 -->
            <div class="api-overview">
                <div class="overview-card">
                    <h3>API密钥总数</h3>
                    <p class="value">45</p>
                </div>
                <div class="overview-card">
                    <h3>活跃密钥</h3>
                    <p class="value">38</p>
                </div>
                <div class="overview-card">
                    <h3>今日调用</h3>
                    <p class="value">125,678</p>
                </div>
                <div class="overview-card">
                    <h3>第三方应用</h3>
                    <p class="value">12</p>
                </div>
                <div class="overview-card">
                    <h3>待审核应用</h3>
                    <p class="value" style="color: #ffc107;">3</p>
                </div>
            </div>

            <!-- API管理标签 -->
            <div class="api-tabs">
                <div class="tab-header">
                    <button class="tab-button active" onclick="switchTab('keys')">API密钥</button>
                    <button class="tab-button" onclick="switchTab('applications')">第三方应用</button>
                    <button class="tab-button" onclick="switchTab('security')">安全策略</button>
                    <button class="tab-button" onclick="switchTab('statistics')">使用统计</button>
                </div>

                <!-- API密钥 -->
                <div id="keys-tab" class="tab-content">
                    <div class="api-table-container">
                        <table class="api-table">
                            <thead>
                                <tr>
                                    <th width="15%">应用名称</th>
                                    <th width="25%">API密钥</th>
                                    <th width="12%">创建时间</th>
                                    <th width="12%">过期时间</th>
                                    <th width="12%">调用限制</th>
                                    <th width="8%">状态</th>
                                    <th width="8%">今日调用</th>
                                    <th width="8%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div><strong>TeleShop移动端</strong></div>
                                        <div style="font-size: 12px; color: #666;">官方应用</div>
                                    </td>
                                    <td>
                                        <div class="api-key">sk-live-51cc****8d19</div>
                                        <button class="btn-xs btn-info" onclick="copyApiKey()">复制</button>
                                    </td>
                                    <td>2023-08-15</td>
                                    <td>2025-08-15</td>
                                    <td><span class="rate-limit">1000/分钟</span></td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td>8,456</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewApiDetails(1)">详情</button>
                                            <button class="btn-xs btn-warning" onclick="regenerateKey(1)">重新生成</button>
                                            <button class="btn-xs btn-danger" onclick="revokeKey(1)">吊销</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div><strong>电商数据分析</strong></div>
                                        <div style="font-size: 12px; color: #666;">第三方应用</div>
                                    </td>
                                    <td>
                                        <div class="api-key">sk-test-42ab****7f21</div>
                                        <button class="btn-xs btn-info" onclick="copyApiKey()">复制</button>
                                    </td>
                                    <td>2024-01-10</td>
                                    <td>2024-07-10</td>
                                    <td><span class="rate-limit">100/分钟</span></td>
                                    <td><span class="status-badge status-active">活跃</span></td>
                                    <td>234</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-info" onclick="viewApiDetails(2)">详情</button>
                                            <button class="btn-xs btn-warning" onclick="editRateLimit(2)">限制</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div><strong>库存管理系统</strong></div>
                                        <div style="font-size: 12px; color: #666;">已过期</div>
                                    </td>
                                    <td>
                                        <div class="api-key">sk-live-83f5****2c8a</div>
                                        <button class="btn-xs btn-info" onclick="copyApiKey()">复制</button>
                                    </td>
                                    <td>2023-12-01</td>
                                    <td>2024-01-01</td>
                                    <td><span class="rate-limit">500/分钟</span></td>
                                    <td><span class="status-badge status-expired">过期</span></td>
                                    <td>0</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-xs btn-success" onclick="renewKey(3)">续期</button>
                                            <button class="btn-xs btn-danger" onclick="deleteKey(3)">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 第三方应用 -->
                <div id="applications-tab" class="tab-content" style="display: none;">
                    <div class="app-card">
                        <div class="app-header">
                            <h4>电商助手Pro</h4>
                            <span class="status-badge status-pending">待审核</span>
                        </div>
                        <div class="app-info">
                            <div class="info-item">
                                <label>开发者:</label>
                                <span>深圳科技有限公司</span>
                            </div>
                            <div class="info-item">
                                <label>联系邮箱:</label>
                                <span><EMAIL></span>
                            </div>
                            <div class="info-item">
                                <label>申请时间:</label>
                                <span>2024-01-15 10:30</span>
                            </div>
                            <div class="info-item">
                                <label>申请权限:</label>
                                <span>用户数据、订单数据、商品信息</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>应用描述:</label>
                            <p>一款专业的电商数据分析工具，帮助商家优化运营策略，提升销售业绩。需要访问用户数据进行用户画像分析。</p>
                        </div>
                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="approveApp(1)">批准</button>
                            <button class="btn btn-danger" onclick="rejectApp(1)">拒绝</button>
                            <button class="btn btn-info" onclick="reviewApp(1)">详细审核</button>
                        </div>
                    </div>

                    <div class="app-card">
                        <div class="app-header">
                            <h4>智能客服机器人</h4>
                            <span class="status-badge status-active">已批准</span>
                        </div>
                        <div class="app-info">
                            <div class="info-item">
                                <label>开发者:</label>
                                <span>AI科技公司</span>
                            </div>
                            <div class="info-item">
                                <label>批准时间:</label>
                                <span>2024-01-10 14:20</span>
                            </div>
                            <div class="info-item">
                                <label>API密钥:</label>
                                <span class="api-key">sk-prod-9a87****f1d2</span>
                            </div>
                            <div class="info-item">
                                <label>调用统计:</label>
                                <span>今日: 1,234 / 月度: 45,678</span>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button class="btn btn-info" onclick="viewAppStats(2)">查看统计</button>
                            <button class="btn btn-warning" onclick="modifyPermissions(2)">修改权限</button>
                            <button class="btn btn-danger" onclick="suspendApp(2)">暂停</button>
                        </div>
                    </div>
                </div>

                <!-- 安全策略 -->
                <div id="security-tab" class="tab-content" style="display: none;">
                    <div class="security-config">
                        <!-- IP白名单 -->
                        <div class="config-card">
                            <div class="config-header">
                                <h4>IP白名单</h4>
                                <div class="config-switch active" onclick="toggleConfig(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>允许的IP地址</label>
                                <textarea rows="4" placeholder="每行一个IP地址或CIDR块" class="form-control">***********/24
127.0.0.1
***********/28</textarea>
                            </div>
                            <div class="form-group">
                                <label>违规处理方式</label>
                                <select class="form-control">
                                    <option>直接拒绝</option>
                                    <option>记录并通知</option>
                                    <option>临时封禁</option>
                                </select>
                            </div>
                            <button class="btn btn-primary btn-sm">保存配置</button>
                        </div>

                        <!-- 频率限制 -->
                        <div class="config-card">
                            <div class="config-header">
                                <h4>频率限制</h4>
                                <div class="config-switch active" onclick="toggleConfig(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>全局限制 (次/分钟)</label>
                                <input type="number" value="1000" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>单个IP限制 (次/分钟)</label>
                                <input type="number" value="100" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>超限处理</label>
                                <select class="form-control">
                                    <option>返回429错误</option>
                                    <option>延迟响应</option>
                                    <option>临时封禁</option>
                                </select>
                            </div>
                            <button class="btn btn-primary btn-sm">保存配置</button>
                        </div>

                        <!-- 签名验证 -->
                        <div class="config-card">
                            <div class="config-header">
                                <h4>签名验证</h4>
                                <div class="config-switch active" onclick="toggleConfig(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>签名算法</label>
                                <select class="form-control">
                                    <option>HMAC-SHA256</option>
                                    <option>HMAC-SHA1</option>
                                    <option>RSA-SHA256</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>时间戳有效期 (秒)</label>
                                <input type="number" value="300" class="form-control">
                            </div>
                            <div class="form-group">
                                <label>签名失败处理</label>
                                <select class="form-control">
                                    <option>拒绝请求</option>
                                    <option>记录并通知</option>
                                    <option>要求重新认证</option>
                                </select>
                            </div>
                            <button class="btn btn-primary btn-sm">保存配置</button>
                        </div>

                        <!-- HTTPS强制 -->
                        <div class="config-card">
                            <div class="config-header">
                                <h4>HTTPS强制</h4>
                                <div class="config-switch active" onclick="toggleConfig(this)"></div>
                            </div>
                            <div class="form-group">
                                <label>最小TLS版本</label>
                                <select class="form-control">
                                    <option>TLS 1.2</option>
                                    <option>TLS 1.3</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>证书验证</label>
                                <select class="form-control">
                                    <option>严格验证</option>
                                    <option>标准验证</option>
                                    <option>宽松验证</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>HTTP重定向</label>
                                <select class="form-control">
                                    <option>自动重定向到HTTPS</option>
                                    <option>直接拒绝HTTP请求</option>
                                </select>
                            </div>
                            <button class="btn btn-primary btn-sm">保存配置</button>
                        </div>
                    </div>
                </div>

                <!-- 使用统计 -->
                <div id="statistics-tab" class="tab-content" style="display: none;">
                    <div class="usage-chart">
                        <h4>API调用统计</h4>
                        <canvas id="apiUsageChart"></canvas>
                    </div>
                    
                    <div class="usage-chart">
                        <h4>热门接口TOP 10</h4>
                        <canvas id="topApisChart"></canvas>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 创建API密钥模态框 -->
    <div id="apiKeyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>创建API密钥</h3>
                <span class="close" onclick="closeModal('apiKeyModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="apiKeyForm">
                    <div class="form-group">
                        <label>应用名称 *</label>
                        <input type="text" name="appName" required class="form-control">
                    </div>
                    <div class="form-group">
                        <label>应用类型 *</label>
                        <select name="appType" required class="form-control">
                            <option value="">请选择</option>
                            <option value="official">官方应用</option>
                            <option value="partner">合作伙伴</option>
                            <option value="third-party">第三方应用</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>权限范围</label>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                            <label><input type="checkbox" name="permissions" value="users"> 用户数据</label>
                            <label><input type="checkbox" name="permissions" value="orders"> 订单数据</label>
                            <label><input type="checkbox" name="permissions" value="products"> 商品信息</label>
                            <label><input type="checkbox" name="permissions" value="payments"> 支付数据</label>
                            <label><input type="checkbox" name="permissions" value="analytics"> 分析数据</label>
                            <label><input type="checkbox" name="permissions" value="messages"> 消息数据</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>调用限制 (次/分钟)</label>
                        <input type="number" name="rateLimit" value="100" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>有效期 (月)</label>
                        <select name="expiration" class="form-control">
                            <option value="1">1个月</option>
                            <option value="3">3个月</option>
                            <option value="6">6个月</option>
                            <option value="12">12个月</option>
                            <option value="24">24个月</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>IP白名单 (可选)</label>
                        <textarea name="ipWhitelist" rows="3" placeholder="每行一个IP地址" class="form-control"></textarea>
                    </div>
                    <div class="form-group">
                        <label>备注</label>
                        <textarea name="notes" rows="2" class="form-control"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('apiKeyModal')">取消</button>
                <button class="btn btn-primary" onclick="saveApiKey()">创建密钥</button>
            </div>
        </div>
    </div>

    <script>
        // 菜单交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化菜单状态
            const menuItems = document.querySelectorAll('.admin-sidebar .menu-list li');
            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && link.getAttribute('href') === '#') {
                    // 如果是父菜单项，添加展开/收起功能
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const submenu = item.querySelector('.submenu');
                        if (submenu) {
                            item.classList.toggle('open');
                            submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
                        }
                    });
                }
            });
        });

        // 切换标签
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            document.getElementById(tabName + '-tab').style.display = 'block';
            event.target.classList.add('active');
            
            if (tabName === 'statistics') {
                initCharts();
            }
        }

        // 切换配置开关
        function toggleConfig(element) {
            element.classList.toggle('active');
        }

        // 创建API密钥
        function createApiKey() {
            document.getElementById('apiKeyModal').style.display = 'block';
        }

        // 保存API密钥
        function saveApiKey() {
            const form = document.getElementById('apiKeyForm');
            const formData = new FormData(form);
            
            if (!form.checkValidity()) {
                alert('请填写完整信息');
                return;
            }
            
            // 生成API密钥
            const apiKey = 'sk-live-' + Math.random().toString(36).substr(2, 8) + '****' + Math.random().toString(36).substr(2, 4);
            alert('API密钥创建成功：' + apiKey);
            closeModal('apiKeyModal');
            location.reload();
        }

        // 复制API密钥
        function copyApiKey() {
            // 实际应用中应该复制完整的API密钥
            alert('API密钥已复制到剪贴板');
        }

        // 重新生成密钥
        function regenerateKey(id) {
            if (confirm('重新生成密钥将使原密钥失效，确定要继续吗？')) {
                alert('新密钥已生成');
                location.reload();
            }
        }

        // 吊销密钥
        function revokeKey(id) {
            if (confirm('确定要吊销该API密钥吗？此操作不可恢复！')) {
                alert('API密钥已吊销');
                location.reload();
            }
        }

        // 续期密钥
        function renewKey(id) {
            if (confirm('确定要续期该API密钥吗？')) {
                alert('API密钥续期成功');
                location.reload();
            }
        }

        // 审批应用
        function approveApp(id) {
            if (confirm('确定要批准该应用吗？')) {
                alert('应用已批准');
                location.reload();
            }
        }

        function rejectApp(id) {
            const reason = prompt('请输入拒绝原因：');
            if (reason) {
                alert('应用已拒绝');
                location.reload();
            }
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 导出API报告
        function exportApiReport() {
            alert('正在导出API使用报告...');
        }

        // 初始化图表
        function initCharts() {
            // API调用统计图表
            const ctx1 = document.getElementById('apiUsageChart').getContext('2d');
            new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: 'API调用次数',
                        data: [1200, 800, 1500, 2200, 1800, 1000],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 热门接口图表
            const ctx2 = document.getElementById('topApisChart').getContext('2d');
            new Chart(ctx2, {
                type: 'bar',
                data: {
                    labels: ['/api/users', '/api/orders', '/api/products', '/api/payments', '/api/analytics'],
                    datasets: [{
                        label: '调用次数',
                        data: [25000, 18000, 15000, 12000, 8000],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1']
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html> 