/* 权限管理模块样式修复 - 与主界面一致 */

/* 基础布局修复 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
    background-color: #f8fafc;
    color: #333;
    line-height: 1.6;
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 - 与主界面admin-sidebar完全一致 */
.admin-sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e2e8f0;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    position: fixed;
    z-index: 1002;
}

.admin-sidebar .sidebar-header {
    padding: 24px;
    border-bottom: 1px solid #e2e8f0;
}

.admin-sidebar .sidebar-header h2 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    color: #1e293b;
}

.admin-sidebar .sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 24px 0;
}

/* 菜单分组 */
.admin-sidebar .menu-section {
    margin-bottom: 32px;
}

.admin-sidebar .menu-section h3 {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 24px 16px 24px;
}

.admin-sidebar .menu-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.admin-sidebar .menu-list li {
    margin-bottom: 4px;
}

.admin-sidebar .menu-list li a {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    color: #64748b;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    border-right: 3px solid transparent;
    cursor: pointer;
}

.admin-sidebar .menu-list li a:hover {
    background: #f8fafc;
    color: #1e293b;
}

.admin-sidebar .menu-list li.active a {
    background: #eff6ff;
    color: #3b82f6;
    border-right-color: #3b82f6;
}

.admin-sidebar .menu-list li a i {
    width: 20px;
    margin-right: 12px;
    font-size: 16px;
}

.menu-count {
    margin-left: auto;
    background: #f1f5f9;
    color: #64748b;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.menu-item.active .menu-count {
    background: #dbeafe;
    color: #3b82f6;
}

/* 子菜单样式 */
.menu-item.has-submenu .menu-arrow {
    margin-left: auto;
    font-size: 12px;
    transition: transform 0.2s;
}

.menu-item.has-submenu.open .menu-arrow {
    transform: rotate(180deg);
}

.submenu {
    background: #f8fafc;
    display: none;
    padding-left: 0;
    list-style: none;
    margin: 0;
}

.menu-item.has-submenu.open .submenu {
    display: block;
}

.submenu li a {
    padding: 10px 24px 10px 56px;
    font-size: 13px;
    color: #64748b;
}

.submenu li.active a {
    background: #eff6ff;
    color: #3b82f6;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 280px;
    padding: 20px;
    background: #f8fafc;
    min-height: 100vh;
}

.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.main-header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #1e293b;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-success {
    background: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.btn-warning {
    background: #f59e0b;
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-info {
    background: #06b6d4;
    color: white;
}

.btn-info:hover {
    background: #0891b2;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
}

.btn-xs {
    padding: 4px 8px;
    font-size: 12px;
}

/* 表单控件 */
.form-control {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 表格样式 */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.table tbody tr:hover {
    background: #f9fafb;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.modal-body {
    margin-bottom: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* 权限网格样式 */
.permission-grid {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 20px;
    margin-top: 20px;
}

.module-list {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.module-item {
    padding: 10px;
    margin-bottom: 5px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.module-item:hover,
.module-item.active {
    background: #e9ecef;
}

.permission-details {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.permission-group {
    margin-bottom: 20px;
}

.permission-group h4 {
    margin-bottom: 10px;
    color: #333;
}

.permission-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.permission-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 状态徽章 */
.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.badge-success {
    background: #f0fdf4;
    color: #166534;
}

.badge-warning {
    background: #fffbeb;
    color: #92400e;
}

.badge-danger {
    background: #fef2f2;
    color: #991b1b;
}

.badge-info {
    background: #eff6ff;
    color: #1e40af;
}

.badge-primary {
    background: #dbeafe;
    color: #1e40af;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .main-content {
        margin-left: 0;
        padding: 15px;
    }
    
    .main-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
    
    .permission-grid {
        grid-template-columns: 1fr;
    }
    
    .permission-overview {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通知样式 */
.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid;
}

.alert-success {
    background: #f0fdf4;
    color: #166534;
    border-color: #bbf7d0;
}

.alert-warning {
    background: #fffbeb;
    color: #92400e;
    border-color: #fde68a;
}

.alert-danger {
    background: #fef2f2;
    color: #991b1b;
    border-color: #fecaca;
}

.alert-info {
    background: #eff6ff;
    color: #1e40af;
    border-color: #dbeafe;
}

/* 表单样式增强 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #1f2937;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 分页器 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    background: white;
    cursor: pointer;
    border-radius: 6px;
}

.pagination button:hover {
    background: #f9fafb;
}

.pagination button.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
} 