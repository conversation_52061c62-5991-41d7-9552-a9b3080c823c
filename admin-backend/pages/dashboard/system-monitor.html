<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>系统监控 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../../assets/js/image-error-handler.js"></script>
    <style>
        /* 系统监控页面专用样式 */
        .monitor-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
            width: 100%;
            overflow-x: hidden;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
            100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
        }
        
        .refresh-controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #64748b;
        }
        
        .refresh-toggle {
            width: 40px;
            height: 20px;
            background: #10b981;
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .refresh-toggle::after {
            content: '';
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            right: 2px;
            transition: all 0.3s;
        }
        
        .refresh-toggle.off {
            background: #cbd5e1;
        }
        
        .refresh-toggle.off::after {
            right: 22px;
        }
        
        .action-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-btn:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }
        
        .action-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .action-btn.primary:hover {
            background: #2563eb;
        }
        
        /* 系统状态概览 */
        .system-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .status-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .status-card.healthy {
            border-left: 4px solid #10b981;
        }
        
        .status-card.warning {
            border-left: 4px solid #f59e0b;
        }
        
        .status-card.critical {
            border-left: 4px solid #ef4444;
        }
        
        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .status-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .status-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }
        
        .status-icon.healthy {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-icon.warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-icon.critical {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .metric-item {
            text-align: center;
        }
        
        .metric-value {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .metric-label {
            font-size: 12px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-details {
            padding-top: 16px;
            border-top: 1px solid #f1f5f9;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .detail-label {
            color: #64748b;
        }
        
        .detail-value {
            color: #1e293b;
            font-weight: 500;
        }
        
        /* 性能图表 */
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .chart-filters {
            display: flex;
            gap: 8px;
        }
        
        .filter-btn {
            padding: 4px 8px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        /* 实时日志 */
        .logs-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .log-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .log-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .log-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .log-count {
            font-size: 12px;
            color: #64748b;
            background: #f1f5f9;
            padding: 4px 8px;
            border-radius: 12px;
        }
        
        .log-content {
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #1e293b;
            color: #f8fafc;
        }
        
        .log-entry {
            padding: 8px 16px;
            border-bottom: 1px solid #334155;
            display: flex;
            gap: 12px;
        }
        
        .log-timestamp {
            color: #94a3b8;
            min-width: 80px;
        }
        
        .log-level {
            min-width: 50px;
            font-weight: 600;
        }
        
        .log-level.info {
            color: #38bdf8;
        }
        
        .log-level.warning {
            color: #fbbf24;
        }
        
        .log-level.error {
            color: #fb7185;
        }
        
        .log-message {
            flex: 1;
            color: #e2e8f0;
        }
        
        /* 系统服务状态 */
        .services-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .services-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .services-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1px;
            background: #f1f5f9;
        }
        
        .service-item {
            background: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .service-info {
            flex: 1;
        }
        
        .service-name {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .service-description {
            font-size: 12px;
            color: #64748b;
        }
        
        .service-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .service-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .service-indicator.running {
            background: #10b981;
        }
        
        .service-indicator.stopped {
            background: #ef4444;
        }
        
        .service-indicator.warning {
            background: #f59e0b;
        }
        
        .service-uptime {
            font-size: 12px;
            color: #64748b;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .monitor-page {
                padding: 16px;
            }
            
            .system-overview {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .logs-section {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .services-grid {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .refresh-controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="monitor-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-desktop"></i>
                系统监控
                <div class="status-indicator"></div>
            </h1>
            <div class="refresh-controls">
                <div class="auto-refresh">
                    <span>自动刷新</span>
                    <div class="refresh-toggle" onclick="toggleAutoRefresh()"></div>
                </div>
                <button class="action-btn" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    手动刷新
                </button>
                <button class="action-btn primary" onclick="exportReport()">
                    <i class="fas fa-download"></i>
                    导出报告
                </button>
            </div>
        </div>

        <!-- 系统状态概览 -->
        <div class="system-overview">
            <div class="status-card healthy">
                <div class="status-header">
                    <div class="status-title">服务器性能</div>
                    <div class="status-icon healthy">
                        <i class="fas fa-server"></i>
                    </div>
                </div>
                <div class="status-metrics">
                    <div class="metric-item">
                        <div class="metric-value">85%</div>
                        <div class="metric-label">CPU 使用率</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">64%</div>
                        <div class="metric-label">内存使用率</div>
                    </div>
                </div>
                <div class="status-details">
                    <div class="detail-item">
                        <span class="detail-label">磁盘空间</span>
                        <span class="detail-value">76% (2.3TB / 3TB)</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">网络延迟</span>
                        <span class="detail-value">12ms</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">运行时间</span>
                        <span class="detail-value">15天 6小时</span>
                    </div>
                </div>
            </div>

            <div class="status-card healthy">
                <div class="status-header">
                    <div class="status-title">数据库状态</div>
                    <div class="status-icon healthy">
                        <i class="fas fa-database"></i>
                    </div>
                </div>
                <div class="status-metrics">
                    <div class="metric-item">
                        <div class="metric-value">1,250</div>
                        <div class="metric-label">每秒查询</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">45ms</div>
                        <div class="metric-label">平均响应</div>
                    </div>
                </div>
                <div class="status-details">
                    <div class="detail-item">
                        <span class="detail-label">连接数</span>
                        <span class="detail-value">156 / 500</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">缓存命中率</span>
                        <span class="detail-value">94.2%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">数据大小</span>
                        <span class="detail-value">128.5GB</span>
                    </div>
                </div>
            </div>

            <div class="status-card warning">
                <div class="status-header">
                    <div class="status-title">消息队列</div>
                    <div class="status-icon warning">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                </div>
                <div class="status-metrics">
                    <div class="metric-item">
                        <div class="metric-value">2,845</div>
                        <div class="metric-label">待处理</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">856</div>
                        <div class="metric-label">每分钟处理</div>
                    </div>
                </div>
                <div class="status-details">
                    <div class="detail-item">
                        <span class="detail-label">处理成功率</span>
                        <span class="detail-value">98.7%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">平均延迟</span>
                        <span class="detail-value">125ms</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">失败重试</span>
                        <span class="detail-value">23个</span>
                    </div>
                </div>
            </div>

            <div class="status-card healthy">
                <div class="status-header">
                    <div class="status-title">在线用户</div>
                    <div class="status-icon healthy">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="status-metrics">
                    <div class="metric-item">
                        <div class="metric-value">8,542</div>
                        <div class="metric-label">当前在线</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">15,260</div>
                        <div class="metric-label">今日峰值</div>
                    </div>
                </div>
                <div class="status-details">
                    <div class="detail-item">
                        <span class="detail-label">并发连接</span>
                        <span class="detail-value">8,542</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">会话时长</span>
                        <span class="detail-value">平均 24分钟</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">消息吞吐</span>
                        <span class="detail-value">2,840/秒</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 性能图表 -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">系统性能趋势</div>
                    <div class="chart-filters">
                        <button class="filter-btn active" onclick="setTimeRange('1h')">1小时</button>
                        <button class="filter-btn" onclick="setTimeRange('6h')">6小时</button>
                        <button class="filter-btn" onclick="setTimeRange('24h')">24小时</button>
                        <button class="filter-btn" onclick="setTimeRange('7d')">7天</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">资源使用分布</div>
                </div>
                <div class="chart-container">
                    <canvas id="resourceChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 实时日志 -->
        <div class="logs-section">
            <div class="log-card">
                <div class="log-header">
                    <div class="log-title">系统日志</div>
                    <div class="log-count">最近 100 条</div>
                </div>
                <div class="log-content" id="systemLogs">
                    <div class="log-entry">
                        <span class="log-timestamp">14:25:32</span>
                        <span class="log-level info">INFO</span>
                        <span class="log-message">系统启动完成，所有服务正常运行</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-timestamp">14:24:18</span>
                        <span class="log-level info">INFO</span>
                        <span class="log-message">数据库连接池初始化成功 [连接数: 50]</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-timestamp">14:23:45</span>
                        <span class="log-level warning">WARN</span>
                        <span class="log-message">消息队列积压超过阈值 [当前: 2845, 阈值: 2000]</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-timestamp">14:22:11</span>
                        <span class="log-level info">INFO</span>
                        <span class="log-message">缓存清理任务执行完成 [清理: 256MB]</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-timestamp">14:21:05</span>
                        <span class="log-level error">ERROR</span>
                        <span class="log-message">支付服务API调用失败 [错误代码: 503, 重试中...]</span>
                    </div>
                </div>
            </div>

            <div class="log-card">
                <div class="log-header">
                    <div class="log-title">安全日志</div>
                    <div class="log-count">最近 50 条</div>
                </div>
                <div class="log-content" id="securityLogs">
                    <div class="log-entry">
                        <span class="log-timestamp">14:26:15</span>
                        <span class="log-level info">INFO</span>
                        <span class="log-message">用户登录成功 [用户: <EMAIL>, IP: *************]</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-timestamp">14:25:40</span>
                        <span class="log-level warning">WARN</span>
                        <span class="log-message">异常登录尝试 [IP: ************, 尝试次数: 5]</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-timestamp">14:24:22</span>
                        <span class="log-level info">INFO</span>
                        <span class="log-message">API访问频率正常 [当前QPS: 1250]</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-timestamp">14:23:18</span>
                        <span class="log-level warning">WARN</span>
                        <span class="log-message">检测到可疑文件上传 [文件类型: .exe, 已拦截]</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-timestamp">14:22:55</span>
                        <span class="log-level info">INFO</span>
                        <span class="log-message">权限验证成功 [角色: SuperAdmin, 操作: 用户管理]</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统服务状态 -->
        <div class="services-section">
            <div class="services-header">
                <div class="services-title">系统服务状态</div>
                <button class="action-btn" onclick="refreshServices()">
                    <i class="fas fa-sync-alt"></i>
                    刷新状态
                </button>
            </div>
            <div class="services-grid">
                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">Web服务器 (Nginx)</div>
                        <div class="service-description">处理HTTP请求和静态资源</div>
                    </div>
                    <div class="service-status">
                        <div class="service-indicator running"></div>
                        <div class="service-uptime">运行 15天</div>
                    </div>
                </div>
                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">应用服务器 (Node.js)</div>
                        <div class="service-description">处理业务逻辑和API请求</div>
                    </div>
                    <div class="service-status">
                        <div class="service-indicator running"></div>
                        <div class="service-uptime">运行 15天</div>
                    </div>
                </div>
                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">数据库 (PostgreSQL)</div>
                        <div class="service-description">主数据存储服务</div>
                    </div>
                    <div class="service-status">
                        <div class="service-indicator running"></div>
                        <div class="service-uptime">运行 15天</div>
                    </div>
                </div>
                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">Redis缓存</div>
                        <div class="service-description">缓存和会话存储</div>
                    </div>
                    <div class="service-status">
                        <div class="service-indicator running"></div>
                        <div class="service-uptime">运行 15天</div>
                    </div>
                </div>
                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">消息队列 (RabbitMQ)</div>
                        <div class="service-description">异步消息处理</div>
                    </div>
                    <div class="service-status">
                        <div class="service-indicator warning"></div>
                        <div class="service-uptime">队列积压</div>
                    </div>
                </div>
                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">搜索引擎 (Elasticsearch)</div>
                        <div class="service-description">全文搜索和日志分析</div>
                    </div>
                    <div class="service-status">
                        <div class="service-indicator running"></div>
                        <div class="service-uptime">运行 14天</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 自动刷新功能
        let autoRefreshEnabled = true;
        let refreshInterval;

        function toggleAutoRefresh() {
            const toggle = document.querySelector('.refresh-toggle');
            autoRefreshEnabled = !autoRefreshEnabled;
            
            if (autoRefreshEnabled) {
                toggle.classList.remove('off');
                startAutoRefresh();
            } else {
                toggle.classList.add('off');
                clearInterval(refreshInterval);
            }
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                if (autoRefreshEnabled) {
                    refreshData();
                }
            }, 30000); // 30秒刷新一次
        }

        function refreshData() {
            // 模拟数据刷新
            console.log('正在刷新系统监控数据...');
            
            // 这里可以添加实际的数据获取逻辑
            // 更新状态卡片、图表、日志等
            
            // 显示刷新动画
            const refreshBtn = document.querySelector('.action-btn i');
            refreshBtn.style.animation = 'spin 1s linear';
            setTimeout(() => {
                refreshBtn.style.animation = '';
            }, 1000);
        }

        function refreshServices() {
            console.log('正在刷新服务状态...');
            // 实际项目中这里会调用API获取最新的服务状态
        }

        function exportReport() {
            console.log('正在导出系统监控报告...');
            // 实际项目中这里会生成并下载报告文件
        }

        function setTimeRange(range) {
            // 更新图表时间范围
            const buttons = document.querySelectorAll('.filter-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            console.log('设置时间范围:', range);
            // 更新图表数据
            updateCharts(range);
        }

        // 初始化图表
        function initCharts() {
            // 性能趋势图
            const performanceCtx = document.getElementById('performanceChart').getContext('2d');
            new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: ['12:00', '12:30', '13:00', '13:30', '14:00', '14:30'],
                    datasets: [{
                        label: 'CPU使用率',
                        data: [65, 78, 85, 82, 85, 83],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: '内存使用率',
                        data: [55, 62, 64, 61, 64, 66],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // 资源使用分布图
            const resourceCtx = document.getElementById('resourceChart').getContext('2d');
            new Chart(resourceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['已使用CPU', '已使用内存', '已使用磁盘', '网络带宽'],
                    datasets: [{
                        data: [85, 64, 76, 45],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateCharts(timeRange) {
            // 根据时间范围更新图表数据
            console.log('更新图表数据，时间范围:', timeRange);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            startAutoRefresh();
            
            // 添加CSS动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html> 