<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>仪表盘 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="../../assets/css/dashboard-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../../assets/js/image-error-handler.js"></script>
    <style>
        /* 仪表盘专用样式 */
        * {
            box-sizing: border-box;
        }
        
        .dashboard-container {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
            width: 100%;
            overflow-x: hidden;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 32px;
            flex-wrap: wrap;
            gap: 20px;
            padding: 0;
            width: 100%;
        }
        
        .dashboard-info {
            flex: 1;
            min-width: 0;
        }
        
        .dashboard-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin: 0 0 4px 0;
            line-height: 1.2;
        }
        
        .dashboard-subtitle {
            color: #64748b;
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .dashboard-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            flex-shrink: 0;
            align-items: center;
            justify-content: flex-end;
            min-width: 0;
        }
        
        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            text-decoration: none;
            min-height: 40px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .action-btn:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }
        
        .action-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .action-btn.primary:hover {
            background: #2563eb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .stat-title {
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            word-break: break-all;
        }
        
        .stat-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
        }
        
        .stat-change.positive {
            color: #10b981;
        }
        
        .stat-change.negative {
            color: #ef4444;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .chart-filter {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .activity-feed {
            max-height: 400px;
            overflow-y: auto;
            overflow-x: hidden;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .activity-content {
            flex: 1;
            min-width: 0;
        }
        
        .activity-title {
            font-size: 14px;
            font-weight: 500;
            color: #1e293b;
            margin: 0 0 4px 0;
        }
        
        .activity-description {
            font-size: 12px;
            color: #64748b;
            margin: 0;
        }
        
        .activity-time {
            font-size: 12px;
            color: #9ca3af;
            white-space: nowrap;
        }
        
        /* 数据表格样式 */
        .tables-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .table-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }
        
        .table-action {
            color: #3b82f6;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: color 0.2s;
        }
        
        .table-action:hover {
            color: #2563eb;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background: #f8fafc;
            color: #64748b;
            font-weight: 500;
            font-size: 12px;
            text-align: left;
            padding: 12px 24px;
            border-bottom: 1px solid #e2e8f0;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .data-table td {
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
            font-size: 14px;
            color: #1e293b;
        }
        
        .data-table tr:hover {
            background: #f8fafc;
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        .product-thumb,
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            object-fit: cover;
            flex-shrink: 0;
        }
        
        .user-avatar {
            border-radius: 50%;
            width: 32px;
            height: 32px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            line-height: 1;
        }
        
        .status-badge.status-success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-badge.status-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-badge.status-error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .tables-section {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                padding: 16px;
            }
            
            .dashboard-header {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
                margin-bottom: 24px;
            }
            
            .dashboard-title {
                font-size: 24px;
            }
            
            .dashboard-actions {
                justify-content: stretch;
                gap: 8px;
            }
            
            .action-btn {
                flex: 1;
                text-align: center;
                justify-content: center;
                padding: 12px 16px;
                min-height: 44px;
            }
            
            .action-btn span {
                display: none;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .stat-card {
                padding: 16px;
            }
            
            .stat-value {
                font-size: 24px;
            }
            
            .chart-card {
                padding: 16px;
            }
            
            .data-table {
                font-size: 12px;
            }
            
            .data-table th,
            .data-table td {
                padding: 8px 12px;
            }
        }
        
        @media (max-width: 480px) {
            .dashboard-container {
                padding: 12px;
            }
            
            .dashboard-header {
                gap: 12px;
                margin-bottom: 20px;
            }
            
            .dashboard-title {
                font-size: 20px;
            }
            
            .dashboard-subtitle {
                font-size: 13px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .dashboard-actions {
                flex-direction: row;
                gap: 6px;
            }
            
            .action-btn {
                padding: 10px 8px;
                font-size: 13px;
                min-height: 40px;
            }
            
            .action-btn i {
                font-size: 14px;
            }
            
            .chart-filter {
                width: 100%;
                justify-content: center;
                gap: 4px;
            }
            
            .filter-btn {
                padding: 4px 8px;
                font-size: 11px;
            }
        }
        
        /* 图表容器修复 */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        .chart-container canvas {
            max-width: 100% !important;
            height: auto !important;
        }
        
        /* 固定定位通知样式 */
        .fixed {
            position: fixed;
        }
        
        .top-4 {
            top: 1rem;
        }
        
        .right-4 {
            right: 1rem;
        }
        
        .z-50 {
            z-index: 50;
        }
        
        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem;
        }
        
        .py-3 {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
        }
        
        .rounded-lg {
            border-radius: 0.5rem;
        }
        
        .text-white {
            color: white;
        }
        
        .text-sm {
            font-size: 0.875rem;
        }
        
        .font-medium {
            font-weight: 500;
        }
        
        .transition-all {
            transition: all 0.3s;
        }
        
        .duration-300 {
            transition-duration: 300ms;
        }
        
        .bg-green-500 {
            background-color: #10b981;
        }
        
        .bg-red-500 {
            background-color: #ef4444;
        }
        
        .bg-orange-500 {
            background-color: #f97316;
        }
        
        .bg-blue-500 {
            background-color: #3b82f6;
        }
        
        /* 确保所有元素都有正确的盒模型 */
        *, *::before, *::after {
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 仪表盘头部 -->
        <div class="dashboard-header">
            <div class="dashboard-info">
                <h1 class="dashboard-title">仪表盘总览</h1>
                <p class="dashboard-subtitle">欢迎回来，TeleShop 管理系统运行正常</p>
            </div>
            <div class="dashboard-actions">
                <button class="action-btn" onclick="exportReport()">
                    <i class="fas fa-download"></i>
                    <span>导出报告</span>
                </button>
                <button class="action-btn" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    <span>刷新数据</span>
                </button>
                <button class="action-btn primary" onclick="showSettings()">
                    <i class="fas fa-cog"></i>
                    <span>设置</span>
                </button>
            </div>
        </div>
        
        <!-- 核心指标统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">总用户数</span>
                    <div class="stat-icon" style="background: #dbeafe; color: #3b82f6;">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value">12,847</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12.5%</span>
                    <span style="color: #64748b; margin-left: 8px;">本月</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">活跃用户</span>
                    <div class="stat-icon" style="background: #dcfce7; color: #10b981;">
                        <i class="fas fa-user-check"></i>
                    </div>
                </div>
                <div class="stat-value">8,234</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8.2%</span>
                    <span style="color: #64748b; margin-left: 8px;">今日</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">总交易额</span>
                    <div class="stat-icon" style="background: #fef3c7; color: #f59e0b;">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="stat-value">¥2.4M</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+24.1%</span>
                    <span style="color: #64748b; margin-left: 8px;">本周</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">订单总数</span>
                    <div class="stat-icon" style="background: #fce7f3; color: #ec4899;">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="stat-value">1,892</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i>
                    <span>-2.4%</span>
                    <span style="color: #64748b; margin-left: 8px;">今日</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">聊天消息</span>
                    <div class="stat-icon" style="background: #e0e7ff; color: #6366f1;">
                        <i class="fas fa-comments"></i>
                    </div>
                </div>
                <div class="stat-value">45.2K</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+18.7%</span>
                    <span style="color: #64748b; margin-left: 8px;">今日</span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">钱包余额</span>
                    <div class="stat-icon" style="background: #f3e8ff; color: #8b5cf6;">
                        <i class="fas fa-wallet"></i>
                    </div>
                </div>
                <div class="stat-value">$1.8M</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+15.3%</span>
                    <span style="color: #64748b; margin-left: 8px;">总计</span>
                </div>
            </div>
        </div>
        
        <!-- 图表分析区域 -->
        <div class="charts-section">
            <!-- 营收趋势图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">营收趋势分析</h3>
                    <div class="chart-filter">
                        <button class="filter-btn active" onclick="setTimeRange('7d')">7天</button>
                        <button class="filter-btn" onclick="setTimeRange('30d')">30天</button>
                        <button class="filter-btn" onclick="setTimeRange('90d')">90天</button>
                        <button class="filter-btn" onclick="setTimeRange('1y')">1年</button>
                    </div>
                </div>
                <div style="height: 300px;">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
            
            <!-- 实时活动动态 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">实时活动动态</h3>
                    <div class="chart-filter">
                        <button class="filter-btn active">全部</button>
                        <button class="filter-btn" onclick="filterActivity('order')">订单</button>
                        <button class="filter-btn" onclick="filterActivity('user')">用户</button>
                    </div>
                </div>
                <div class="activity-feed">
                    <div class="activity-item">
                        <div class="activity-icon" style="background: #dcfce7; color: #10b981;">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">新订单创建</div>
                            <div class="activity-description">用户 Alice Chen 下单 iPhone 15 Pro Max</div>
                        </div>
                        <div class="activity-time">2分钟前</div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon" style="background: #dbeafe; color: #3b82f6;">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">新用户注册</div>
                            <div class="activity-description">用户 Bob Wang 完成注册</div>
                        </div>
                        <div class="activity-time">5分钟前</div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon" style="background: #fef3c7; color: #f59e0b;">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">支付完成</div>
                            <div class="activity-description">订单 #TS2024001 支付成功</div>
                        </div>
                        <div class="activity-time">8分钟前</div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon" style="background: #e0e7ff; color: #6366f1;">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">客服对话</div>
                            <div class="activity-description">用户 Charlie Li 发起客服咨询</div>
                        </div>
                        <div class="activity-time">12分钟前</div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon" style="background: #fce7f3; color: #ec4899;">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">商品评价</div>
                            <div class="activity-description">用户 David Zhang 评价了商品</div>
                        </div>
                        <div class="activity-time">15分钟前</div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon" style="background: #f3e8ff; color: #8b5cf6;">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">钱包转账</div>
                            <div class="activity-description">用户间转账 $500 完成</div>
                        </div>
                        <div class="activity-time">18分钟前</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 数据表格区域 -->
        <div class="tables-section">
            <!-- 热门商品排行 -->
            <div class="table-card">
                <div class="table-header">
                    <h3 class="table-title">热门商品排行</h3>
                    <a href="#" class="table-action">查看全部</a>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>商品</th>
                            <th>销量</th>
                            <th>收入</th>
                            <th>趋势</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <img src="https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=40&h=40&fit=crop" alt="iPhone" class="product-thumb">
                                    <div>
                                        <div style="font-weight: 500;">iPhone 15 Pro Max</div>
                                        <div style="font-size: 12px; color: #64748b;">深空黑色 256GB</div>
                                    </div>
                                </div>
                            </td>
                            <td>234</td>
                            <td>¥2,337,660</td>
                            <td><span class="status-badge status-success">+15%</span></td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <img src="https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=40&h=40&fit=crop" alt="AirPods" class="product-thumb">
                                    <div>
                                        <div style="font-weight: 500;">AirPods Pro 2</div>
                                        <div style="font-size: 12px; color: #64748b;">USB-C充电盒</div>
                                    </div>
                                </div>
                            </td>
                            <td>189</td>
                            <td>¥358,911</td>
                            <td><span class="status-badge status-success">+8%</span></td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=40&h=40&fit=crop" alt="Nike" class="product-thumb">
                                    <div>
                                        <div style="font-weight: 500;">Nike Air Max 90</div>
                                        <div style="font-size: 12px; color: #64748b;">白色/黑色</div>
                                    </div>
                                </div>
                            </td>
                            <td>156</td>
                            <td>¥124,800</td>
                            <td><span class="status-badge status-warning">-3%</span></td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=40&h=40&fit=crop" alt="Audio" class="product-thumb">
                                    <div>
                                        <div style="font-weight: 500;">Sony WH-1000XM4</div>
                                        <div style="font-size: 12px; color: #64748b;">降噪耳机</div>
                                    </div>
                                </div>
                            </td>
                            <td>142</td>
                            <td>¥355,000</td>
                            <td><span class="status-badge status-success">+12%</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 最新用户注册 -->
            <div class="table-card">
                <div class="table-header">
                    <h3 class="table-title">最新用户注册</h3>
                    <a href="#" class="table-action">查看全部</a>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>用户</th>
                            <th>注册时间</th>
                            <th>状态</th>
                            <th>来源</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <img src="https://images.unsplash.com/photo-1494790108755-2616b332844c?w=32&h=32&fit=crop" alt="Alice" class="user-avatar">
                                    <div>
                                        <div style="font-weight: 500;">Alice Chen</div>
                                        <div style="font-size: 12px; color: #64748b;"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td>5分钟前</td>
                            <td><span class="status-badge status-success">已验证</span></td>
                            <td>Telegram</td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop" alt="Bob" class="user-avatar">
                                    <div>
                                        <div style="font-weight: 500;">Bob Wang</div>
                                        <div style="font-size: 12px; color: #64748b;"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td>12分钟前</td>
                            <td><span class="status-badge status-warning">待验证</span></td>
                            <td>Web</td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop" alt="Charlie" class="user-avatar">
                                    <div>
                                        <div style="font-weight: 500;">Charlie Li</div>
                                        <div style="font-size: 12px; color: #64748b;"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td>28分钟前</td>
                            <td><span class="status-badge status-success">已验证</span></td>
                            <td>推荐</td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop" alt="David" class="user-avatar">
                                    <div>
                                        <div style="font-weight: 500;">David Zhang</div>
                                        <div style="font-size: 12px; color: #64748b;"><EMAIL></div>
                                    </div>
                                </div>
                            </td>
                            <td>1小时前</td>
                            <td><span class="status-badge status-success">已验证</span></td>
                            <td>Telegram</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <script>
        // 初始化营收趋势图表
        function initRevenueChart() {
            const ctx = document.getElementById('revenueChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [{
                        label: '营收 (万元)',
                        data: [120, 135, 158, 142, 188, 205, 234, 252, 298, 324, 378, 402],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '订单数',
                        data: [80, 95, 112, 98, 125, 140, 162, 178, 195, 218, 245, 267],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#f1f5f9'
                            }
                        },
                        x: {
                            grid: {
                                color: '#f1f5f9'
                            }
                        }
                    }
                }
            });
        }
        
        // 设置时间范围
        function setTimeRange(range) {
            const buttons = document.querySelectorAll('.chart-filter .filter-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 这里可以重新加载图表数据
            showNotification(`已切换到 ${range} 视图`, 'info');
        }
        
        // 过滤活动类型
        function filterActivity(type) {
            const buttons = document.querySelectorAll('.chart-filter .filter-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            showNotification(`已过滤到 ${type} 活动`, 'info');
        }
        
        // 导出报告
        function exportReport() {
            showNotification('正在导出报告...', 'info');
            setTimeout(() => {
                showNotification('报告导出成功！', 'success');
            }, 2000);
        }
        
        // 刷新数据
        function refreshData() {
            showNotification('正在刷新数据...', 'info');
            setTimeout(() => {
                showNotification('数据刷新完成！', 'success');
            }, 1500);
        }
        
        // 显示设置
        function showSettings() {
            showNotification('设置功能开发中...', 'info');
        }
        
        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification-toast ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // 触发显示动画
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);
            
            // 自动隐藏
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initRevenueChart();
            
            // 定时刷新实时数据
            setInterval(() => {
                // 这里可以添加实时数据更新逻辑
                console.log('实时数据更新...');
            }, 30000); // 每30秒更新一次
        });
    </script>
</body>
</html> 