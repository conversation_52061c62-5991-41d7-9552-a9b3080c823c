<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级报表 - TeleShop后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .reports-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .reports-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            flex-wrap: wrap;
            gap: 16px;
        }

        .reports-title {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
        }

        .report-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .report-tabs {
            display: flex;
            background: #f1f5f9;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 24px;
            overflow-x: auto;
        }

        .report-tab {
            padding: 8px 16px;
            border-radius: 8px;
            background: none;
            border: none;
            color: #64748b;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }

        .report-tab.active {
            background: white;
            color: #1e293b;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .report-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 24px;
        }

        .report-sidebar {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 20px;
            height: fit-content;
        }

        .report-main {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 20px;
            min-height: 600px;
        }

        .filter-section {
            margin-bottom: 24px;
        }

        .filter-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
        }

        .filter-group {
            margin-bottom: 16px;
        }

        .filter-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }

        .filter-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
        }

        .custom-report-builder {
            display: none;
        }

        .custom-report-builder.active {
            display: block;
        }

        .builder-section {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }

        .builder-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .field-selector {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 8px;
        }

        .field-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .field-item:hover {
            background: #f9fafb;
            border-color: #3b82f6;
        }

        .field-item.selected {
            background: #eff6ff;
            border-color: #3b82f6;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 24px;
        }

        .export-options {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .export-btn:hover {
            background: #f9fafb;
            border-color: #3b82f6;
        }

        @media (max-width: 1024px) {
            .report-content {
                grid-template-columns: 1fr;
            }
            
            .report-sidebar {
                order: 2;
            }
            
            .report-main {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .reports-container {
                padding: 16px;
            }
            
            .reports-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .report-actions {
                width: 100%;
                justify-content: space-between;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="reports-container">
        <!-- 页面头部 -->
        <div class="reports-header">
            <h1 class="reports-title">高级报表</h1>
            <div class="report-actions">
                <button class="btn-secondary" onclick="reportManager.refreshData()">
                    <i class="fas fa-sync"></i>
                    刷新数据
                </button>
                <button class="btn-primary" onclick="reportManager.createCustomReport()">
                    <i class="fas fa-plus"></i>
                    自定义报表
                </button>
            </div>
        </div>

        <!-- 报表标签页 -->
        <div class="report-tabs">
            <button class="report-tab active" data-tab="overview" onclick="reportManager.switchTab('overview')">
                概览报表
            </button>
            <button class="report-tab" data-tab="sales" onclick="reportManager.switchTab('sales')">
                销售分析
            </button>
            <button class="report-tab" data-tab="users" onclick="reportManager.switchTab('users')">
                用户分析
            </button>
            <button class="report-tab" data-tab="products" onclick="reportManager.switchTab('products')">
                商品分析
            </button>
            <button class="report-tab" data-tab="custom" onclick="reportManager.switchTab('custom')">
                自定义报表
            </button>
        </div>

        <!-- 主要内容区域 -->
        <div class="report-content">
            <!-- 侧边栏筛选 -->
            <div class="report-sidebar">
                <div class="filter-section">
                    <h3 class="filter-title">时间筛选</h3>
                    <div class="filter-group">
                        <label class="filter-label">时间范围</label>
                        <select class="filter-input" id="timeRange" onchange="reportManager.updateFilters()">
                            <option value="7d">最近7天</option>
                            <option value="30d">最近30天</option>
                            <option value="90d">最近90天</option>
                            <option value="12m">最近12个月</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    <div class="filter-group" id="customDateRange" style="display: none;">
                        <label class="filter-label">开始日期</label>
                        <input type="date" class="filter-input" id="startDate">
                        <label class="filter-label" style="margin-top: 8px;">结束日期</label>
                        <input type="date" class="filter-input" id="endDate">
                    </div>
                </div>

                <div class="filter-section">
                    <h3 class="filter-title">数据筛选</h3>
                    <div class="filter-group">
                        <label class="filter-label">商品分类</label>
                        <select class="filter-input" id="categoryFilter" onchange="reportManager.updateFilters()">
                            <option value="">全部分类</option>
                            <option value="electronics">电子产品</option>
                            <option value="clothing">服装配饰</option>
                            <option value="home">家居用品</option>
                            <option value="books">图书文娱</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">用户类型</label>
                        <select class="filter-input" id="userTypeFilter" onchange="reportManager.updateFilters()">
                            <option value="">全部用户</option>
                            <option value="new">新用户</option>
                            <option value="returning">回头客</option>
                            <option value="vip">VIP用户</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">订单状态</label>
                        <select class="filter-input" id="orderStatusFilter" onchange="reportManager.updateFilters()">
                            <option value="">全部状态</option>
                            <option value="completed">已完成</option>
                            <option value="pending">待处理</option>
                            <option value="cancelled">已取消</option>
                        </select>
                    </div>
                </div>

                <div class="filter-section">
                    <h3 class="filter-title">导出选项</h3>
                    <div class="export-options">
                        <button class="export-btn" onclick="reportManager.exportData('pdf')">
                            <i class="fas fa-file-pdf"></i>
                            PDF
                        </button>
                        <button class="export-btn" onclick="reportManager.exportData('excel')">
                            <i class="fas fa-file-excel"></i>
                            Excel
                        </button>
                        <button class="export-btn" onclick="reportManager.exportData('csv')">
                            <i class="fas fa-file-csv"></i>
                            CSV
                        </button>
                        <button class="export-btn" onclick="reportManager.exportData('png')">
                            <i class="fas fa-image"></i>
                            图片
                        </button>
                    </div>
                </div>
            </div>

            <!-- 主要报表区域 -->
            <div class="report-main">
                <div id="reportContent">
                    <!-- 报表内容将通过JavaScript动态生成 -->
                    <div class="chart-container">
                        <canvas id="mainChart"></canvas>
                    </div>
                    
                    <div class="data-tables">
                        <table class="table" id="reportTable">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>销售额</th>
                                    <th>订单数</th>
                                    <th>用户数</th>
                                    <th>转化率</th>
                                </tr>
                            </thead>
                            <tbody id="reportTableBody">
                                <!-- 表格数据将通过JavaScript填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 自定义报表构建器 -->
                <div class="custom-report-builder" id="customReportBuilder">
                    <div class="builder-section">
                        <h3 class="builder-title">选择数据字段</h3>
                        <div class="field-selector" id="fieldSelector">
                            <!-- 字段选择器将通过JavaScript生成 -->
                        </div>
                    </div>

                    <div class="builder-section">
                        <h3 class="builder-title">图表类型</h3>
                        <div class="field-selector">
                            <div class="field-item" data-chart="line">
                                <i class="fas fa-chart-line"></i>
                                折线图
                            </div>
                            <div class="field-item" data-chart="bar">
                                <i class="fas fa-chart-bar"></i>
                                柱状图
                            </div>
                            <div class="field-item" data-chart="pie">
                                <i class="fas fa-chart-pie"></i>
                                饼图
                            </div>
                            <div class="field-item" data-chart="doughnut">
                                <i class="fas fa-chart-donut-alt"></i>
                                环形图
                            </div>
                        </div>
                    </div>

                    <div class="builder-section">
                        <div style="display: flex; gap: 12px;">
                            <button class="btn-secondary" onclick="reportManager.previewCustomReport()">
                                <i class="fas fa-eye"></i>
                                预览
                            </button>
                            <button class="btn-primary" onclick="reportManager.saveCustomReport()">
                                <i class="fas fa-save"></i>
                                保存报表
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 高级报表管理器
        class AdvancedReportManager {
            constructor() {
                this.currentTab = 'overview';
                this.currentChart = null;
                this.filters = {
                    timeRange: '7d',
                    category: '',
                    userType: '',
                    orderStatus: ''
                };
                this.customReportConfig = {
                    fields: [],
                    chartType: 'line'
                };
                
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadReportData();
                this.setupDateRangeToggle();
            }

            setupEventListeners() {
                // 监听时间范围变化
                document.getElementById('timeRange').addEventListener('change', (e) => {
                    this.filters.timeRange = e.target.value;
                    this.toggleCustomDateRange();
                    this.updateFilters();
                });

                // 监听自定义报表字段选择
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.field-item[data-field]')) {
                        this.toggleFieldSelection(e.target.closest('.field-item'));
                    }
                    
                    if (e.target.closest('.field-item[data-chart]')) {
                        this.selectChartType(e.target.closest('.field-item'));
                    }
                });
            }

            setupDateRangeToggle() {
                const timeRange = document.getElementById('timeRange');
                const customDateRange = document.getElementById('customDateRange');
                
                timeRange.addEventListener('change', () => {
                    if (timeRange.value === 'custom') {
                        customDateRange.style.display = 'block';
                    } else {
                        customDateRange.style.display = 'none';
                    }
                });
            }

            switchTab(tabName) {
                // 更新标签页状态
                document.querySelectorAll('.report-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

                this.currentTab = tabName;

                // 显示/隐藏自定义报表构建器
                const builder = document.getElementById('customReportBuilder');
                const content = document.getElementById('reportContent');
                
                if (tabName === 'custom') {
                    builder.classList.add('active');
                    content.style.display = 'none';
                    this.initCustomReportBuilder();
                } else {
                    builder.classList.remove('active');
                    content.style.display = 'block';
                    this.loadReportData();
                }
            }

            initCustomReportBuilder() {
                const fieldSelector = document.getElementById('fieldSelector');
                const availableFields = [
                    { id: 'date', name: '日期', type: 'datetime' },
                    { id: 'revenue', name: '销售额', type: 'number' },
                    { id: 'orders', name: '订单数', type: 'number' },
                    { id: 'users', name: '用户数', type: 'number' },
                    { id: 'conversion', name: '转化率', type: 'percentage' },
                    { id: 'traffic', name: '访问量', type: 'number' },
                    { id: 'bounce_rate', name: '跳出率', type: 'percentage' },
                    { id: 'avg_order', name: '平均订单金额', type: 'currency' },
                    { id: 'retention', name: '用户留存率', type: 'percentage' }
                ];

                fieldSelector.innerHTML = availableFields.map(field => `
                    <div class="field-item" data-field="${field.id}">
                        <input type="checkbox" id="field_${field.id}">
                        <label for="field_${field.id}">${field.name}</label>
                    </div>
                `).join('');
            }

            toggleFieldSelection(fieldItem) {
                const checkbox = fieldItem.querySelector('input[type="checkbox"]');
                const fieldId = fieldItem.dataset.field;
                
                checkbox.checked = !checkbox.checked;
                fieldItem.classList.toggle('selected', checkbox.checked);
                
                if (checkbox.checked) {
                    this.customReportConfig.fields.push(fieldId);
                } else {
                    this.customReportConfig.fields = this.customReportConfig.fields.filter(f => f !== fieldId);
                }
            }

            selectChartType(chartItem) {
                document.querySelectorAll('.field-item[data-chart]').forEach(item => {
                    item.classList.remove('selected');
                });
                
                chartItem.classList.add('selected');
                this.customReportConfig.chartType = chartItem.dataset.chart;
            }

            updateFilters() {
                this.filters = {
                    timeRange: document.getElementById('timeRange').value,
                    category: document.getElementById('categoryFilter').value,
                    userType: document.getElementById('userTypeFilter').value,
                    orderStatus: document.getElementById('orderStatusFilter').value
                };
                
                this.loadReportData();
            }

            async loadReportData() {
                try {
                    // 模拟数据加载
                    const data = await this.fetchReportData(this.currentTab, this.filters);
                    this.renderChart(data);
                    this.renderTable(data);
                } catch (error) {
                    console.error('加载报表数据失败:', error);
                }
            }

            async fetchReportData(tab, filters) {
                // 模拟API调用
                return new Promise(resolve => {
                    setTimeout(() => {
                        const mockData = this.generateMockData(tab, filters);
                        resolve(mockData);
                    }, 500);
                });
            }

            generateMockData(tab, filters) {
                const days = this.getDaysFromRange(filters.timeRange);
                const data = {
                    labels: [],
                    datasets: []
                };

                // 生成日期标签
                for (let i = days - 1; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    data.labels.push(date.toLocaleDateString());
                }

                // 根据标签页生成不同数据
                switch (tab) {
                    case 'overview':
                        data.datasets = [
                            {
                                label: '销售额',
                                data: Array.from({length: days}, () => Math.floor(Math.random() * 50000) + 10000),
                                borderColor: '#3b82f6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)'
                            },
                            {
                                label: '订单数',
                                data: Array.from({length: days}, () => Math.floor(Math.random() * 200) + 50),
                                borderColor: '#10b981',
                                backgroundColor: 'rgba(16, 185, 129, 0.1)'
                            }
                        ];
                        break;
                    case 'sales':
                        data.datasets = [
                            {
                                label: '销售额',
                                data: Array.from({length: days}, () => Math.floor(Math.random() * 80000) + 20000),
                                borderColor: '#3b82f6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)'
                            }
                        ];
                        break;
                    case 'users':
                        data.datasets = [
                            {
                                label: '新用户',
                                data: Array.from({length: days}, () => Math.floor(Math.random() * 100) + 20),
                                borderColor: '#8b5cf6',
                                backgroundColor: 'rgba(139, 92, 246, 0.1)'
                            },
                            {
                                label: '活跃用户',
                                data: Array.from({length: days}, () => Math.floor(Math.random() * 500) + 100),
                                borderColor: '#f59e0b',
                                backgroundColor: 'rgba(245, 158, 11, 0.1)'
                            }
                        ];
                        break;
                    case 'products':
                        data.datasets = [
                            {
                                label: '商品浏览量',
                                data: Array.from({length: days}, () => Math.floor(Math.random() * 1000) + 200),
                                borderColor: '#ef4444',
                                backgroundColor: 'rgba(239, 68, 68, 0.1)'
                            }
                        ];
                        break;
                }

                return data;
            }

            getDaysFromRange(range) {
                switch (range) {
                    case '7d': return 7;
                    case '30d': return 30;
                    case '90d': return 90;
                    case '12m': return 365;
                    default: return 7;
                }
            }

            renderChart(data) {
                const ctx = document.getElementById('mainChart');
                
                if (this.currentChart) {
                    this.currentChart.destroy();
                }

                this.currentChart = new Chart(ctx, {
                    type: 'line',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: this.getChartTitle()
                            },
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            renderTable(data) {
                const tableBody = document.getElementById('reportTableBody');
                let html = '';

                data.labels.forEach((label, index) => {
                    const revenue = data.datasets.find(d => d.label === '销售额')?.data[index] || 0;
                    const orders = data.datasets.find(d => d.label === '订单数')?.data[index] || 0;
                    const users = data.datasets.find(d => d.label === '新用户')?.data[index] || Math.floor(Math.random() * 50) + 10;
                    const conversion = ((orders / (orders + Math.random() * 100)) * 100).toFixed(2);

                    html += `
                        <tr>
                            <td>${label}</td>
                            <td>¥${revenue.toLocaleString()}</td>
                            <td>${orders}</td>
                            <td>${users}</td>
                            <td>${conversion}%</td>
                        </tr>
                    `;
                });

                tableBody.innerHTML = html;
            }

            getChartTitle() {
                const titles = {
                    overview: '业务概览',
                    sales: '销售分析',
                    users: '用户分析',
                    products: '商品分析'
                };
                return titles[this.currentTab] || '数据分析';
            }

            refreshData() {
                this.loadReportData();
                this.showNotification('数据已刷新', 'success');
            }

            createCustomReport() {
                this.switchTab('custom');
            }

            previewCustomReport() {
                if (this.customReportConfig.fields.length === 0) {
                    this.showNotification('请至少选择一个数据字段', 'warning');
                    return;
                }

                // 生成预览数据
                const previewData = this.generateCustomReportData();
                this.renderCustomChart(previewData);
            }

            generateCustomReportData() {
                // 模拟自定义报表数据生成
                return {
                    labels: this.getDaysArray(7),
                    datasets: this.customReportConfig.fields.map(field => ({
                        label: this.getFieldDisplayName(field),
                        data: Array.from({length: 7}, () => Math.floor(Math.random() * 1000) + 100),
                        borderColor: this.getRandomColor(),
                        backgroundColor: this.getRandomColor(0.1)
                    }))
                };
            }

            renderCustomChart(data) {
                const ctx = document.getElementById('mainChart');
                
                if (this.currentChart) {
                    this.currentChart.destroy();
                }

                this.currentChart = new Chart(ctx, {
                    type: this.customReportConfig.chartType,
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: '自定义报表预览'
                            }
                        }
                    }
                });
            }

            saveCustomReport() {
                if (this.customReportConfig.fields.length === 0) {
                    this.showNotification('请至少选择一个数据字段', 'warning');
                    return;
                }

                const reportName = prompt('请输入报表名称:');
                if (!reportName) return;

                // 模拟保存报表
                console.log('保存自定义报表:', {
                    name: reportName,
                    config: this.customReportConfig
                });

                this.showNotification('自定义报表已保存', 'success');
            }

            exportData(format) {
                // 模拟数据导出
                this.showNotification(`正在导出${format.toUpperCase()}格式...`, 'info');
                
                setTimeout(() => {
                    this.showNotification(`${format.toUpperCase()}文件导出完成`, 'success');
                }, 2000);
            }

            // 辅助方法
            getDaysArray(days) {
                const dates = [];
                for (let i = days - 1; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    dates.push(date.toLocaleDateString());
                }
                return dates;
            }

            getFieldDisplayName(fieldId) {
                const names = {
                    date: '日期',
                    revenue: '销售额',
                    orders: '订单数',
                    users: '用户数',
                    conversion: '转化率',
                    traffic: '访问量',
                    bounce_rate: '跳出率',
                    avg_order: '平均订单金额',
                    retention: '用户留存率'
                };
                return names[fieldId] || fieldId;
            }

            getRandomColor(alpha = 1) {
                const colors = [
                    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
                    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
                ];
                const color = colors[Math.floor(Math.random() * colors.length)];
                
                if (alpha < 1) {
                    // 转换为rgba格式
                    const r = parseInt(color.slice(1, 3), 16);
                    const g = parseInt(color.slice(3, 5), 16);
                    const b = parseInt(color.slice(5, 7), 16);
                    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
                }
                
                return color;
            }

            showNotification(message, type = 'info') {
                // 简单的通知实现
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 8px;
                    color: white;
                    font-size: 14px;
                    font-weight: 500;
                    z-index: 10001;
                    transform: translateX(100%);
                    transition: transform 0.3s;
                `;
                
                const colors = {
                    success: '#10b981',
                    error: '#ef4444',
                    warning: '#f59e0b',
                    info: '#3b82f6'
                };
                
                notification.style.background = colors[type] || colors.info;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 10);
                
                setTimeout(() => {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }
        }

        // 初始化报表管理器
        const reportManager = new AdvancedReportManager();
    </script>
</body>
</html> 