<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>报表分析 - TeleShop后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-text-fix.css">
    <link rel="stylesheet" href="../../assets/css/header-button-fix.css">
    <link rel="stylesheet" href="../../assets/css/search-button-fix.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { margin: 0; padding: 0; font-family: 'Inter', sans-serif; background: #f8fafc; }
        .reports-page { padding: 24px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 32px; flex-wrap: wrap; gap: 16px; }
        .page-title { font-size: 28px; font-weight: 700; color: #1e293b; margin: 0; }
        .header-actions { display: flex; gap: 12px; flex-wrap: wrap; }
        .action-btn { padding: 10px 20px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px; cursor: pointer; transition: all 0.2s; display: flex; align-items: center; gap: 8px; text-decoration: none; }
        .action-btn:hover { background: #f1f5f9; border-color: #cbd5e1; }
        .action-btn.primary { background: #3b82f6; color: white; border-color: #3b82f6; }
        
        /* 时间选择器 */
        .time-selector { background: white; padding: 20px 24px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; margin-bottom: 24px; }
        .time-options { display: flex; gap: 8px; align-items: center; flex-wrap: wrap; }
        .time-btn { padding: 8px 16px; border: 1px solid #e2e8f0; border-radius: 6px; background: white; color: #64748b; font-size: 14px; cursor: pointer; transition: all 0.2s; }
        .time-btn.active { background: #3b82f6; color: white; border-color: #3b82f6; }
        .custom-date { display: flex; gap: 8px; align-items: center; margin-left: 16px; }
        .date-input { padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px; }
        
        /* 关键指标卡片 */
        .kpi-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-bottom: 32px; }
        .kpi-card { background: white; padding: 24px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; position: relative; overflow: hidden; transition: transform 0.2s; }
        .kpi-card:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
        .kpi-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px; }
        .kpi-card.revenue::before { background: linear-gradient(90deg, #10b981, #34d399); }
        .kpi-card.orders::before { background: linear-gradient(90deg, #3b82f6, #60a5fa); }
        .kpi-card.customers::before { background: linear-gradient(90deg, #8b5cf6, #a78bfa); }
        .kpi-card.conversion::before { background: linear-gradient(90deg, #f59e0b, #fbbf24); }
        .kpi-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px; }
        .kpi-title { font-size: 14px; color: #64748b; font-weight: 500; }
        .kpi-trend { display: flex; align-items: center; gap: 4px; font-size: 12px; padding: 4px 8px; border-radius: 12px; }
        .kpi-trend.up { background: #dcfce7; color: #166534; }
        .kpi-trend.down { background: #fee2e2; color: #991b1b; }
        .kpi-value { font-size: 32px; font-weight: 700; color: #1e293b; margin-bottom: 8px; }
        .kpi-compare { font-size: 14px; color: #64748b; }
        .kpi-compare .change { font-weight: 500; }
        .kpi-compare .change.positive { color: #10b981; }
        .kpi-compare .change.negative { color: #ef4444; }
        
        /* 图表区域 */
        .charts-section { display: grid; grid-template-columns: 2fr 1fr; gap: 24px; margin-bottom: 32px; }
        .chart-card { background: white; padding: 24px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .chart-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
        .chart-title { font-size: 18px; font-weight: 600; color: #1e293b; }
        .chart-filters { display: flex; gap: 8px; }
        .chart-filter { padding: 6px 12px; border: 1px solid #e2e8f0; border-radius: 6px; background: white; color: #64748b; font-size: 12px; cursor: pointer; transition: all 0.2s; }
        .chart-filter.active { background: #3b82f6; color: white; border-color: #3b82f6; }
        .chart-container { position: relative; height: 300px; }
        
        /* 报表表格 */
        .reports-table-section { background: white; padding: 24px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; margin-bottom: 24px; }
        .table-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .table-title { font-size: 18px; font-weight: 600; color: #1e293b; }
        .table-actions { display: flex; gap: 8px; }
        .table-container { overflow-x: auto; }
        .data-table { width: 100%; border-collapse: collapse; min-width: 800px; }
        .data-table th, .data-table td { padding: 12px; text-align: left; border-bottom: 1px solid #f1f5f9; vertical-align: middle; }
        .data-table th { background: #f8fafc; font-weight: 600; color: #374151; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px; position: sticky; top: 0; }
        .trend-cell { display: flex; align-items: center; gap: 8px; }
        .trend-icon { width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; border-radius: 4px; }
        .trend-up { background: #dcfce7; color: #166534; }
        .trend-down { background: #fee2e2; color: #991b1b; }
        .trend-flat { background: #f1f5f9; color: #64748b; }
        
        /* 分析洞察 */
        .insights-section { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 20px; margin-bottom: 32px; }
        .insight-card { background: white; padding: 20px; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .insight-header { display: flex; align-items: center; gap: 12px; margin-bottom: 16px; }
        .insight-icon { width: 40px; height: 40px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 18px; }
        .insight-icon.success { background: #dcfce7; color: #166534; }
        .insight-icon.warning { background: #fef3c7; color: #92400e; }
        .insight-icon.info { background: #dbeafe; color: #1e40af; }
        .insight-title { font-size: 16px; font-weight: 600; color: #1e293b; }
        .insight-desc { color: #64748b; font-size: 14px; line-height: 1.5; }
        
        /* 响应式 */
        @media (max-width: 1024px) {
            .charts-section { grid-template-columns: 1fr; }
            .time-options { flex-direction: column; align-items: flex-start; }
            .custom-date { margin-left: 0; margin-top: 12px; }
        }
        
        @media (max-width: 768px) {
            .reports-page { padding: 16px; }
            .kpi-grid { grid-template-columns: 1fr; }
            .page-header { flex-direction: column; align-items: stretch; }
        }
    </style>
</head>
<body>
    <div class="reports-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">报表分析中心</h1>
            <div class="header-actions">
                <button class="action-btn" onclick="exportReport('pdf')">
                    <i class="fas fa-file-pdf"></i>
                    导出PDF
                </button>
                <button class="action-btn" onclick="exportReport('excel')">
                    <i class="fas fa-file-excel"></i>
                    导出Excel
                </button>
                <button class="action-btn" onclick="scheduleReport()">
                    <i class="fas fa-clock"></i>
                    定时报表
                </button>
                <button class="action-btn primary" onclick="refreshData()">
                    <i class="fas fa-sync"></i>
                    刷新数据
                </button>
            </div>
        </div>

        <!-- 时间选择器 -->
        <div class="time-selector">
            <div class="time-options">
                <span style="font-weight: 500; color: #1e293b; margin-right: 16px;">时间范围:</span>
                <button class="time-btn active" onclick="selectTimeRange('today', this)">今日</button>
                <button class="time-btn" onclick="selectTimeRange('week', this)">本周</button>
                <button class="time-btn" onclick="selectTimeRange('month', this)">本月</button>
                <button class="time-btn" onclick="selectTimeRange('quarter', this)">本季度</button>
                <button class="time-btn" onclick="selectTimeRange('year', this)">本年</button>
                <div class="custom-date">
                    <span style="color: #64748b;">自定义:</span>
                    <input type="date" class="date-input" value="2024-01-01">
                    <span style="color: #64748b;">至</span>
                    <input type="date" class="date-input" value="2024-12-31">
                </div>
            </div>
        </div>

        <!-- 关键指标 -->
        <div class="kpi-grid">
            <div class="kpi-card revenue">
                <div class="kpi-header">
                    <div class="kpi-title">总营收</div>
                    <div class="kpi-trend up">
                        <i class="fas fa-arrow-up"></i>
                        +12.5%
                    </div>
                </div>
                <div class="kpi-value">¥2,847,392</div>
                <div class="kpi-compare">
                    较上期 <span class="change positive">+¥312,847</span>
                </div>
            </div>
            
            <div class="kpi-card orders">
                <div class="kpi-header">
                    <div class="kpi-title">订单数量</div>
                    <div class="kpi-trend up">
                        <i class="fas fa-arrow-up"></i>
                        +8.3%
                    </div>
                </div>
                <div class="kpi-value">15,234</div>
                <div class="kpi-compare">
                    较上期 <span class="change positive">+1,167</span> 单
                </div>
            </div>
            
            <div class="kpi-card customers">
                <div class="kpi-header">
                    <div class="kpi-title">新增用户</div>
                    <div class="kpi-trend up">
                        <i class="fas fa-arrow-up"></i>
                        +15.7%
                    </div>
                </div>
                <div class="kpi-value">3,892</div>
                <div class="kpi-compare">
                    较上期 <span class="change positive">+529</span> 人
                </div>
            </div>
            
            <div class="kpi-card conversion">
                <div class="kpi-header">
                    <div class="kpi-title">转化率</div>
                    <div class="kpi-trend down">
                        <i class="fas fa-arrow-down"></i>
                        -2.1%
                    </div>
                </div>
                <div class="kpi-value">3.42%</div>
                <div class="kpi-compare">
                    较上期 <span class="change negative">-0.07%</span>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <!-- 营收趋势图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">营收趋势分析</h3>
                    <div class="chart-filters">
                        <button class="chart-filter active">营收</button>
                        <button class="chart-filter">订单</button>
                        <button class="chart-filter">用户</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>

            <!-- 渠道分析 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">渠道来源分析</h3>
                </div>
                <div class="chart-container">
                    <canvas id="channelChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 商品销售排行 -->
        <div class="reports-table-section">
            <div class="table-header">
                <h3 class="table-title">热销商品TOP20</h3>
                <div class="table-actions">
                    <button class="action-btn">
                        <i class="fas fa-download"></i>
                        导出
                    </button>
                </div>
            </div>
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>商品名称</th>
                            <th>分类</th>
                            <th>销量</th>
                            <th>营收</th>
                            <th>趋势</th>
                            <th>库存</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>1</strong></td>
                            <td>iPhone 14 Pro Max</td>
                            <td>电子产品</td>
                            <td>2,847</td>
                            <td>¥28,470,000</td>
                            <td>
                                <div class="trend-cell">
                                    <div class="trend-icon trend-up">
                                        <i class="fas fa-arrow-up"></i>
                                    </div>
                                    +15.2%
                                </div>
                            </td>
                            <td>234</td>
                        </tr>
                        <tr>
                            <td><strong>2</strong></td>
                            <td>MacBook Pro 16寸</td>
                            <td>电子产品</td>
                            <td>1,234</td>
                            <td>¥24,680,000</td>
                            <td>
                                <div class="trend-cell">
                                    <div class="trend-icon trend-up">
                                        <i class="fas fa-arrow-up"></i>
                                    </div>
                                    ****%
                                </div>
                            </td>
                            <td>89</td>
                        </tr>
                        <tr>
                            <td><strong>3</strong></td>
                            <td>AirPods Pro</td>
                            <td>电子产品</td>
                            <td>3,456</td>
                            <td>¥6,912,000</td>
                            <td>
                                <div class="trend-cell">
                                    <div class="trend-icon trend-flat">
                                        <i class="fas fa-minus"></i>
                                    </div>
                                    +0.3%
                                </div>
                            </td>
                            <td>567</td>
                        </tr>
                        <tr>
                            <td><strong>4</strong></td>
                            <td>iPad Air 5</td>
                            <td>电子产品</td>
                            <td>1,789</td>
                            <td>¥8,945,000</td>
                            <td>
                                <div class="trend-cell">
                                    <div class="trend-icon trend-down">
                                        <i class="fas fa-arrow-down"></i>
                                    </div>
                                    -3.2%
                                </div>
                            </td>
                            <td>123</td>
                        </tr>
                        <tr>
                            <td><strong>5</strong></td>
                            <td>Apple Watch Series 8</td>
                            <td>电子产品</td>
                            <td>2,345</td>
                            <td>¥9,380,000</td>
                            <td>
                                <div class="trend-cell">
                                    <div class="trend-icon trend-up">
                                        <i class="fas fa-arrow-up"></i>
                                    </div>
                                    +22.1%
                                </div>
                            </td>
                            <td>345</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 用户分析表 -->
        <div class="reports-table-section">
            <div class="table-header">
                <h3 class="table-title">用户活跃度分析</h3>
                <div class="table-actions">
                    <button class="action-btn">
                        <i class="fas fa-filter"></i>
                        筛选
                    </button>
                </div>
            </div>
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>用户群体</th>
                            <th>用户数量</th>
                            <th>占比</th>
                            <th>平均订单金额</th>
                            <th>复购率</th>
                            <th>活跃度</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>新用户</td>
                            <td>15,234</td>
                            <td>45.2%</td>
                            <td>¥234</td>
                            <td>12.3%</td>
                            <td>
                                <div class="trend-cell">
                                    <div class="trend-icon trend-up">
                                        <i class="fas fa-arrow-up"></i>
                                    </div>
                                    高
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>老用户</td>
                            <td>12,456</td>
                            <td>36.9%</td>
                            <td>¥567</td>
                            <td>67.8%</td>
                            <td>
                                <div class="trend-cell">
                                    <div class="trend-icon trend-up">
                                        <i class="fas fa-arrow-up"></i>
                                    </div>
                                    高
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>沉睡用户</td>
                            <td>6,012</td>
                            <td>17.9%</td>
                            <td>¥89</td>
                            <td>5.2%</td>
                            <td>
                                <div class="trend-cell">
                                    <div class="trend-icon trend-down">
                                        <i class="fas fa-arrow-down"></i>
                                    </div>
                                    低
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分析洞察 -->
        <div class="insights-section">
            <div class="insight-card">
                <div class="insight-header">
                    <div class="insight-icon success">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4 class="insight-title">营收增长强劲</h4>
                </div>
                <p class="insight-desc">本月营收相比上月增长12.5%，主要得益于新品iPhone 14系列的热销。建议继续加大推广力度，同时关注库存补充。</p>
            </div>
            
            <div class="insight-card">
                <div class="insight-header">
                    <div class="insight-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h4 class="insight-title">转化率有所下降</h4>
                </div>
                <p class="insight-desc">转化率较上期下降2.1%，可能与页面加载速度和用户体验有关。建议优化商品详情页面，提升用户购买体验。</p>
            </div>
            
            <div class="insight-card">
                <div class="insight-header">
                    <div class="insight-icon info">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4 class="insight-title">用户增长稳定</h4>
                </div>
                <p class="insight-desc">新用户数量持续增长，其中45.2%为新用户。建议针对新用户制定专门的运营策略，提高首次购买转化率。</p>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initRevenueChart();
            initChannelChart();
        });

        // 营收趋势图
        function initRevenueChart() {
            const ctx = document.getElementById('revenueChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [{
                        label: '营收 (万元)',
                        data: [120, 135, 148, 162, 178, 195, 210, 225, 240, 258, 275, 285],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // 渠道来源饼图
        function initChannelChart() {
            const ctx = document.getElementById('channelChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['直接访问', '搜索引擎', '社交媒体', '邮件营销', '付费广告'],
                    datasets: [{
                        data: [35, 25, 20, 12, 8],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#8b5cf6',
                            '#ef4444'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }

        // 时间范围选择
        function selectTimeRange(range, button) {
            document.querySelectorAll('.time-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
            
            // 这里可以添加数据刷新逻辑
            console.log('选择时间范围:', range);
        }

        // 导出报表
        function exportReport(format) {
            alert(`正在导出${format.toUpperCase()}格式报表...`);
        }

        // 定时报表
        function scheduleReport() {
            alert('定时报表设置功能开发中...');
        }

        // 刷新数据
        function refreshData() {
            alert('正在刷新数据...');
        }
    </script>
</body>
</html> 