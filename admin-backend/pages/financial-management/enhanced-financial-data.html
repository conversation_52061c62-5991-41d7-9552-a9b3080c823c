<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务数据管理 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/button-layout-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .financial-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .financial-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .revenue-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
        }
        
        .revenue-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }
        
        .revenue-value {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .revenue-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 12px;
        }
        
        .revenue-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .subscription-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .chart-container {
            position: relative;
            height: 350px;
        }
        
        .premium-users {
            max-height: 350px;
            overflow-y: auto;
        }
        
        .user-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e2e8f0;
        }
        
        .user-details {
            flex: 1;
        }
        
        .user-name {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }
        
        .user-plan {
            font-size: 12px;
            color: #64748b;
        }
        
        .user-revenue {
            text-align: right;
            font-weight: 600;
            color: #374151;
        }
        
        .refund-management {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            margin-bottom: 32px;
        }
        
        .refund-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .refund-info {
            flex: 1;
        }
        
        .refund-id {
            font-weight: 600;
            color: #1e293b;
        }
        
        .refund-reason {
            font-size: 12px;
            color: #64748b;
            margin-top: 4px;
        }
        
        .refund-amount {
            font-weight: 600;
            color: #ef4444;
        }
        
        .refund-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-approved {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-rejected {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .refund-actions {
            display: flex;
            gap: 8px;
        }
        
        .refund-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .btn-approve {
            background: #dcfce7;
            color: #166534;
        }
        
        .btn-reject {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .financial-reports {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .report-controls {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .report-filters {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .filter-select {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .report-table {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #64748b;
            font-size: 12px;
            text-transform: uppercase;
        }
        
        .amount-positive {
            color: #10b981;
            font-weight: 600;
        }
        
        .amount-negative {
            color: #ef4444;
            font-weight: 600;
        }
        
        .plan-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .plan-basic {
            background: #f1f5f9;
            color: #64748b;
        }
        
        .plan-premium {
            background: #fef3c7;
            color: #92400e;
        }
        
        .plan-pro {
            background: #e0e7ff;
            color: #4338ca;
        }
        
        .plan-enterprise {
            background: #fce7f3;
            color: #be185d;
        }
    </style>
</head>
<body>
    <div class="financial-page">
        <div class="page-header">
            <h1 class="page-title">财务数据管理</h1>
            <div class="page-actions">
                <button class="action-btn" onclick="exportFinancialReport()">
                    <i class="fas fa-download"></i> 导出报告
                </button>
                <button class="action-btn primary" onclick="generateReport()">
                    <i class="fas fa-chart-bar"></i> 生成报表
                </button>
            </div>
        </div>
        
        <!-- 收入概览 -->
        <div class="financial-overview">
            <div class="revenue-card">
                <div class="revenue-value">¥2,847,569</div>
                <div class="revenue-label">本月总收入</div>
                <div class="revenue-change">
                    <i class="fas fa-arrow-up"></i>
                    +23.5% 较上月
                </div>
            </div>
            
            <div class="metric-card">
                <div class="stat-header">
                    <span class="stat-title">Premium订阅收入</span>
                    <div class="stat-icon" style="background: #fef3c7; color: #d97706;">
                        <i class="fas fa-crown"></i>
                    </div>
                </div>
                <div class="stat-value">¥1,845,230</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +18.2%
                </div>
            </div>
            
            <div class="metric-card">
                <div class="stat-header">
                    <span class="stat-title">商品交易佣金</span>
                    <div class="stat-icon" style="background: #dcfce7; color: #16a34a;">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="stat-value">¥892,156</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +31.7%
                </div>
            </div>
            
            <div class="metric-card">
                <div class="stat-header">
                    <span class="stat-title">广告收入</span>
                    <div class="stat-icon" style="background: #e0e7ff; color: #4338ca;">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                </div>
                <div class="stat-value">¥110,183</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +8.9%
                </div>
            </div>
        </div>
        
        <!-- 订阅数据详情 -->
        <div class="subscription-metrics">
            <div class="metric-card">
                <h4 style="margin: 0 0 12px 0; color: #374151;">活跃Premium用户</h4>
                <div class="stat-value" style="color: #f59e0b;">2,847</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +156 本月
                </div>
            </div>
            
            <div class="metric-card">
                <h4 style="margin: 0 0 12px 0; color: #374151;">平均订阅时长</h4>
                <div class="stat-value" style="color: #8b5cf6;">8.3月</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +0.5月
                </div>
            </div>
            
            <div class="metric-card">
                <h4 style="margin: 0 0 12px 0; color: #374151;">续费率</h4>
                <div class="stat-value" style="color: #10b981;">73.2%</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +2.1%
                </div>
            </div>
            
            <div class="metric-card">
                <h4 style="margin: 0 0 12px 0; color: #374151;">用户生命周期价值</h4>
                <div class="stat-value" style="color: #ef4444;">¥1,234</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +¥89
                </div>
            </div>
        </div>
        
        <!-- 收入趋势和高价值用户 -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">收入趋势分析</h3>
                    <select onchange="updateRevenueChart(this.value)" style="padding: 4px 8px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        <option value="monthly">按月</option>
                        <option value="weekly">按周</option>
                        <option value="daily">按天</option>
                    </select>
                </div>
                <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">高价值付费用户</h3>
                </div>
                <div class="premium-users">
                    <div class="user-item">
                        <div class="user-info">
                            <img src="https://via.placeholder.com/32" alt="用户" class="user-avatar">
                            <div class="user-details">
                                <div class="user-name">张三</div>
                                <div class="user-plan">Enterprise Plan</div>
                            </div>
                        </div>
                        <div class="user-revenue">¥12,890</div>
                    </div>
                    
                    <div class="user-item">
                        <div class="user-info">
                            <img src="https://via.placeholder.com/32" alt="用户" class="user-avatar">
                            <div class="user-details">
                                <div class="user-name">李四</div>
                                <div class="user-plan">Premium Plan</div>
                            </div>
                        </div>
                        <div class="user-revenue">¥8,540</div>
                    </div>
                    
                    <div class="user-item">
                        <div class="user-info">
                            <img src="https://via.placeholder.com/32" alt="用户" class="user-avatar">
                            <div class="user-details">
                                <div class="user-name">王五</div>
                                <div class="user-plan">Pro Plan</div>
                            </div>
                        </div>
                        <div class="user-revenue">¥6,720</div>
                    </div>
                    
                    <div class="user-item">
                        <div class="user-info">
                            <img src="https://via.placeholder.com/32" alt="用户" class="user-avatar">
                            <div class="user-details">
                                <div class="user-name">赵六</div>
                                <div class="user-plan">Premium Plan</div>
                            </div>
                        </div>
                        <div class="user-revenue">¥5,890</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 退款处理 -->
        <div class="refund-management">
            <div class="chart-header" style="padding: 20px 24px; border-bottom: 1px solid #e2e8f0;">
                <h3 class="chart-title">退款处理记录</h3>
            </div>
            
            <div class="refund-item">
                <div class="refund-info">
                    <div class="refund-id">REF-2024-001</div>
                    <div class="refund-reason">服务不满意，要求退款</div>
                </div>
                <div class="refund-amount">-¥299.00</div>
                <div class="refund-status status-pending">待处理</div>
                <div class="refund-actions">
                    <button class="refund-btn btn-approve" onclick="approveRefund('REF-2024-001')">批准</button>
                    <button class="refund-btn btn-reject" onclick="rejectRefund('REF-2024-001')">拒绝</button>
                </div>
            </div>
            
            <div class="refund-item">
                <div class="refund-info">
                    <div class="refund-id">REF-2024-002</div>
                    <div class="refund-reason">误操作充值</div>
                </div>
                <div class="refund-amount">-¥99.00</div>
                <div class="refund-status status-approved">已批准</div>
                <div class="refund-actions">
                    <span style="font-size: 12px; color: #64748b;">已处理</span>
                </div>
            </div>
            
            <div class="refund-item">
                <div class="refund-info">
                    <div class="refund-id">REF-2024-003</div>
                    <div class="refund-reason">重复扣费</div>
                </div>
                <div class="refund-amount">-¥199.00</div>
                <div class="refund-status status-rejected">已拒绝</div>
                <div class="refund-actions">
                    <span style="font-size: 12px; color: #64748b;">已处理</span>
                </div>
            </div>
        </div>
        
        <!-- 财务报表 -->
        <div class="financial-reports">
            <div class="report-controls">
                <h3 class="chart-title">财务报表</h3>
                <div class="report-filters">
                    <select class="filter-select">
                        <option>本月</option>
                        <option>上月</option>
                        <option>本季度</option>
                        <option>本年度</option>
                    </select>
                    <select class="filter-select">
                        <option>所有用户</option>
                        <option>Premium用户</option>
                        <option>企业用户</option>
                    </select>
                </div>
            </div>
            
            <div class="report-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>用户</th>
                            <th>订阅计划</th>
                            <th>交易类型</th>
                            <th>金额</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2024-03-10</td>
                            <td>张三</td>
                            <td><span class="plan-badge plan-enterprise">Enterprise</span></td>
                            <td>订阅续费</td>
                            <td><span class="amount-positive">+¥999.00</span></td>
                            <td><span class="status-badge status-success">已完成</span></td>
                        </tr>
                        
                        <tr>
                            <td>2024-03-10</td>
                            <td>李四</td>
                            <td><span class="plan-badge plan-premium">Premium</span></td>
                            <td>新订阅</td>
                            <td><span class="amount-positive">+¥299.00</span></td>
                            <td><span class="status-badge status-success">已完成</span></td>
                        </tr>
                        
                        <tr>
                            <td>2024-03-09</td>
                            <td>王五</td>
                            <td><span class="plan-badge plan-pro">Pro</span></td>
                            <td>退款</td>
                            <td><span class="amount-negative">-¥199.00</span></td>
                            <td><span class="status-badge status-warning">处理中</span></td>
                        </tr>
                        
                        <tr>
                            <td>2024-03-09</td>
                            <td>赵六</td>
                            <td><span class="plan-badge plan-basic">Basic</span></td>
                            <td>升级订阅</td>
                            <td><span class="amount-positive">+¥99.00</span></td>
                            <td><span class="status-badge status-success">已完成</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <script>
        // 初始化收入趋势图表
        document.addEventListener('DOMContentLoaded', function() {
            initializeRevenueChart();
        });
        
        function initializeRevenueChart() {
            const ctx = document.getElementById('revenueChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: 'Premium订阅',
                        data: [1200000, 1450000, 1680000, 1520000, 1750000, 1845230],
                        backgroundColor: '#fbbf24',
                        borderRadius: 4
                    }, {
                        label: '交易佣金',
                        data: [650000, 720000, 850000, 780000, 920000, 892156],
                        backgroundColor: '#10b981',
                        borderRadius: 4
                    }, {
                        label: '广告收入',
                        data: [85000, 92000, 98000, 88000, 105000, 110183],
                        backgroundColor: '#6366f1',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });
        }
        
        function updateRevenueChart(period) {
            showNotification(`图表已切换到${period}视图`, 'info');
        }
        
        function approveRefund(refundId) {
            if (confirm('确定要批准这笔退款吗？')) {
                showNotification(`退款 ${refundId} 已批准`, 'success');
            }
        }
        
        function rejectRefund(refundId) {
            if (confirm('确定要拒绝这笔退款吗？')) {
                showNotification(`退款 ${refundId} 已拒绝`, 'warning');
            }
        }
        
        function exportFinancialReport() {
            showNotification('正在生成财务报告...', 'info');
            setTimeout(() => {
                showNotification('财务报告已导出!', 'success');
            }, 2000);
        }
        
        function generateReport() {
            showNotification('正在生成报表...', 'info');
            setTimeout(() => {
                showNotification('报表生成完成!', 'success');
            }, 1500);
        }
        
        function showNotification(message, type) {
            console.log(`${type}: ${message}`);
        }
    </script>
</body>
</html> 