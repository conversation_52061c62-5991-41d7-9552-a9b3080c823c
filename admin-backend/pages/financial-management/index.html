<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>财务管理 - TeleShop后台管理</title>
    <link rel="stylesheet" href="../../assets/css/universal-fixes.css">
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="../../assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="stylesheet" href="../../assets/css/image-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
        }

        /* 财务统计卡片 */
        .financial-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-title {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .stat-change {
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-change.positive {
            color: #059669;
        }

        .stat-change.negative {
            color: #dc2626;
        }

        .stat-period {
            font-size: 12px;
            color: #64748b;
            margin-top: 4px;
        }

        /* 图表容器 */
        .chart-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }

        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .chart-actions {
            display: flex;
            gap: 8px;
        }

        .chart-btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .chart-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
        }

        /* 钱包管理 */
        .wallet-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }

        .wallet-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .wallet-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .wallet-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .wallet-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .wallet-item:last-child {
            border-bottom: none;
        }

        .wallet-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .wallet-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .wallet-name {
            font-weight: 500;
            color: #1e293b;
        }

        .wallet-type {
            font-size: 12px;
            color: #64748b;
        }

        .wallet-balance {
            text-align: right;
        }

        .wallet-amount {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .wallet-status {
            font-size: 12px;
            color: #059669;
        }

        /* 交易记录 */
        .transaction-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .transaction-header {
            background: #f8fafc;
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .transaction-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .transaction-filters {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .transaction-table {
            width: 100%;
            border-collapse: collapse;
        }

        .transaction-table th,
        .transaction-table td {
            padding: 16px 24px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .transaction-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        .transaction-table tr:hover {
            background: #f8fafc;
        }

        .transaction-id {
            font-family: monospace;
            font-size: 12px;
            color: #64748b;
        }

        .transaction-type {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .transaction-type.income {
            background: #dcfce7;
            color: #166534;
        }

        .transaction-type.expense {
            background: #fee2e2;
            color: #991b1b;
        }

        .transaction-type.transfer {
            background: #dbeafe;
            color: #1e40af;
        }

        .transaction-amount {
            font-weight: 600;
            font-size: 16px;
        }

        .transaction-amount.positive {
            color: #059669;
        }

        .transaction-amount.negative {
            color: #dc2626;
        }

        .transaction-status {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .transaction-status.completed {
            background: #dcfce7;
            color: #166534;
        }

        .transaction-status.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .transaction-status.failed {
            background: #fee2e2;
            color: #991b1b;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-success {
            background: #059669;
            color: white;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-ghost {
            background: transparent;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }

        .btn-ghost:hover {
            background: #f8fafc;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            background: white;
            border-top: 1px solid #e2e8f0;
        }

        .pagination-info {
            color: #64748b;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .pagination-btn:hover {
            background: #f8fafc;
        }

        .pagination-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .chart-container {
                grid-template-columns: 1fr;
            }

            .wallet-section {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .financial-stats {
                grid-template-columns: 1fr;
            }

            .transaction-table {
                font-size: 14px;
            }

            .transaction-table th,
            .transaction-table td {
                padding: 12px 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
                <div>
                    <h1 class="page-title">财务管理</h1>
                    <p class="page-subtitle">管理平台财务数据、钱包余额和交易记录</p>
                </div>
                <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                    <a href="../dashboard/index.html" style="padding: 8px 16px; background: white; border: 1px solid #e2e8f0; border-radius: 6px; color: #64748b; text-decoration: none; font-size: 14px; display: flex; align-items: center; gap: 6px; transition: all 0.2s;" onmouseover="this.style.background='#f1f5f9'" onmouseout="this.style.background='white'">
                        <i class="fas fa-tachometer-alt"></i> 仪表板
                    </a>
                    <a href="enhanced-financial-data.html" style="padding: 8px 16px; background: white; border: 1px solid #e2e8f0; border-radius: 6px; color: #64748b; text-decoration: none; font-size: 14px; display: flex; align-items: center; gap: 6px; transition: all 0.2s;" onmouseover="this.style.background='#f1f5f9'" onmouseout="this.style.background='white'">
                        <i class="fas fa-chart-area"></i> 财务数据
                    </a>
                    <a href="../data-analytics/index.html" style="padding: 8px 16px; background: white; border: 1px solid #e2e8f0; border-radius: 6px; color: #64748b; text-decoration: none; font-size: 14px; display: flex; align-items: center; gap: 6px; transition: all 0.2s;" onmouseover="this.style.background='#f1f5f9'" onmouseout="this.style.background='white'">
                        <i class="fas fa-chart-bar"></i> 数据分析
                    </a>
                    <button style="padding: 8px 16px; background: #3b82f6; border: none; border-radius: 6px; color: white; font-size: 14px; cursor: pointer; display: flex; align-items: center; gap: 6px; transition: all 0.2s;" onmouseover="this.style.background='#2563eb'" onmouseout="this.style.background='#3b82f6'" onclick="exportFinancialReport()">
                        <i class="fas fa-download"></i> 导出报告
                    </button>
                </div>
            </div>
        </div>

        <!-- 财务统计 -->
        <div class="financial-stats">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">今日收入</span>
                    <div class="stat-icon" style="background: #059669;">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                </div>
                <div class="stat-value">¥156,890</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +15.3% 较昨日
                </div>
                <div class="stat-period">截至今日 18:00</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">今日支出</span>
                    <div class="stat-icon" style="background: #dc2626;">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                </div>
                <div class="stat-value">¥23,450</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-up"></i>
                    +8.7% 较昨日
                </div>
                <div class="stat-period">退款及费用支出</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">净利润</span>
                    <div class="stat-icon" style="background: #3b82f6;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="stat-value">¥133,440</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +18.2% 较昨日
                </div>
                <div class="stat-period">今日净收益</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">待处理金额</span>
                    <div class="stat-icon" style="background: #d97706;">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value">¥45,280</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-down"></i>
                    -12.5% 较昨日
                </div>
                <div class="stat-period">等待确认交易</div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="chart-container">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">收支趋势</h3>
                    <div class="chart-actions">
                        <button class="chart-btn active">7天</button>
                        <button class="chart-btn">30天</button>
                        <button class="chart-btn">90天</button>
                    </div>
                </div>
                <div class="chart-wrapper">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">收入构成</h3>
                </div>
                <div class="chart-wrapper">
                    <canvas id="incomeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 钱包管理 -->
        <div class="wallet-section">
            <div class="wallet-card">
                <div class="wallet-header">
                    <h3 class="wallet-title">平台钱包</h3>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        充值
                    </button>
                </div>
                
                <div class="wallet-item">
                    <div class="wallet-info">
                        <div class="wallet-icon" style="background: #3b82f6;">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div>
                            <div class="wallet-name">主钱包</div>
                            <div class="wallet-type">人民币</div>
                        </div>
                    </div>
                    <div class="wallet-balance">
                        <div class="wallet-amount">¥2,458,690</div>
                        <div class="wallet-status">可用余额</div>
                    </div>
                </div>

                <div class="wallet-item">
                    <div class="wallet-info">
                        <div class="wallet-icon" style="background: #059669;">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div>
                            <div class="wallet-name">冻结资金</div>
                            <div class="wallet-type">人民币</div>
                        </div>
                    </div>
                    <div class="wallet-balance">
                        <div class="wallet-amount">¥156,780</div>
                        <div class="wallet-status">等待解冻</div>
                    </div>
                </div>

                <div class="wallet-item">
                    <div class="wallet-info">
                        <div class="wallet-icon" style="background: #d97706;">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <div class="wallet-name">待结算</div>
                            <div class="wallet-type">人民币</div>
                        </div>
                    </div>
                    <div class="wallet-balance">
                        <div class="wallet-amount">¥89,450</div>
                        <div class="wallet-status">2天后到账</div>
                    </div>
                </div>
            </div>

            <div class="wallet-card">
                <div class="wallet-header">
                    <h3 class="wallet-title">第三方账户</h3>
                    <button class="btn btn-ghost">
                        <i class="fas fa-link"></i>
                        绑定账户
                    </button>
                </div>
                
                <div class="wallet-item">
                    <div class="wallet-info">
                        <div class="wallet-icon" style="background: #1da1f2;">
                            <i class="fab fa-alipay"></i>
                        </div>
                        <div>
                            <div class="wallet-name">支付宝</div>
                            <div class="wallet-type">***@gmail.com</div>
                        </div>
                    </div>
                    <div class="wallet-balance">
                        <div class="wallet-amount">已绑定</div>
                        <div class="wallet-status">正常</div>
                    </div>
                </div>

                <div class="wallet-item">
                    <div class="wallet-info">
                        <div class="wallet-icon" style="background: #00d924;">
                            <i class="fab fa-weixin"></i>
                        </div>
                        <div>
                            <div class="wallet-name">微信支付</div>
                            <div class="wallet-type">***1234</div>
                        </div>
                    </div>
                    <div class="wallet-balance">
                        <div class="wallet-amount">已绑定</div>
                        <div class="wallet-status">正常</div>
                    </div>
                </div>

                <div class="wallet-item">
                    <div class="wallet-info">
                        <div class="wallet-icon" style="background: #6772e5;">
                            <i class="fab fa-stripe"></i>
                        </div>
                        <div>
                            <div class="wallet-name">Stripe</div>
                            <div class="wallet-type">国际支付</div>
                        </div>
                    </div>
                    <div class="wallet-balance">
                        <div class="wallet-amount">未绑定</div>
                        <div class="wallet-status">待配置</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易记录 -->
        <div class="transaction-section">
            <div class="transaction-header">
                <h3 class="transaction-title">交易记录</h3>
                <div class="transaction-filters">
                    <select class="filter-select">
                        <option value="">全部类型</option>
                        <option value="income">收入</option>
                        <option value="expense">支出</option>
                        <option value="transfer">转账</option>
                    </select>
                    <select class="filter-select">
                        <option value="">全部状态</option>
                        <option value="completed">已完成</option>
                        <option value="pending">处理中</option>
                        <option value="failed">失败</option>
                    </select>
                    <button class="btn btn-primary">
                        <i class="fas fa-download"></i>
                        导出记录
                    </button>
                </div>
            </div>

            <table class="transaction-table">
                <thead>
                    <tr>
                        <th>交易ID</th>
                        <th>类型</th>
                        <th>描述</th>
                        <th>金额</th>
                        <th>账户</th>
                        <th>状态</th>
                        <th>时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="transaction-id">TXN20240115001</td>
                        <td><span class="transaction-type income">收入</span></td>
                        <td>订单支付 - iPhone 15 Pro Max</td>
                        <td class="transaction-amount positive">+¥9,999.00</td>
                        <td>支付宝</td>
                        <td><span class="transaction-status completed">已完成</span></td>
                        <td>2024-01-15 14:30:25</td>
                        <td>
                            <button class="btn btn-ghost">查看详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td class="transaction-id">TXN20240115002</td>
                        <td><span class="transaction-type expense">支出</span></td>
                        <td>退款处理 - 小米智能音箱</td>
                        <td class="transaction-amount negative">-¥199.00</td>
                        <td>主钱包</td>
                        <td><span class="transaction-status completed">已完成</span></td>
                        <td>2024-01-15 13:45:12</td>
                        <td>
                            <button class="btn btn-ghost">查看详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td class="transaction-id">TXN20240115003</td>
                        <td><span class="transaction-type income">收入</span></td>
                        <td>订单支付 - Nike Air Max 270</td>
                        <td class="transaction-amount positive">+¥899.00</td>
                        <td>微信支付</td>
                        <td><span class="transaction-status pending">处理中</span></td>
                        <td>2024-01-15 12:20:08</td>
                        <td>
                            <button class="btn btn-ghost">查看详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td class="transaction-id">TXN20240115004</td>
                        <td><span class="transaction-type transfer">转账</span></td>
                        <td>资金转移 - 主钱包到冻结账户</td>
                        <td class="transaction-amount">¥50,000.00</td>
                        <td>内部转账</td>
                        <td><span class="transaction-status completed">已完成</span></td>
                        <td>2024-01-15 11:15:42</td>
                        <td>
                            <button class="btn btn-ghost">查看详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td class="transaction-id">TXN20240115005</td>
                        <td><span class="transaction-type expense">支出</span></td>
                        <td>平台服务费</td>
                        <td class="transaction-amount negative">-¥2,500.00</td>
                        <td>主钱包</td>
                        <td><span class="transaction-status completed">已完成</span></td>
                        <td>2024-01-15 10:00:00</td>
                        <td>
                            <button class="btn btn-ghost">查看详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td class="transaction-id">TXN20240115006</td>
                        <td><span class="transaction-type income">收入</span></td>
                        <td>订单支付 - 戴森吸尘器 V15</td>
                        <td class="transaction-amount positive">+¥3,999.00</td>
                        <td>支付宝</td>
                        <td><span class="transaction-status failed">失败</span></td>
                        <td>2024-01-15 09:30:15</td>
                        <td>
                            <button class="btn btn-ghost">查看详情</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示 1-20 条，共 1,567 条记录
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>上一页</button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn">4</button>
                    <button class="pagination-btn">5</button>
                    <button class="pagination-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 收支趋势图表
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['1月9日', '1月10日', '1月11日', '1月12日', '1月13日', '1月14日', '1月15日'],
                datasets: [{
                    label: '收入',
                    data: [120000, 135000, 128000, 145000, 162000, 148000, 156890],
                    borderColor: '#059669',
                    backgroundColor: 'rgba(5, 150, 105, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '支出',
                    data: [25000, 28000, 22000, 30000, 35000, 28000, 23450],
                    borderColor: '#dc2626',
                    backgroundColor: 'rgba(220, 38, 38, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '¥' + (value / 1000) + 'K';
                            }
                        }
                    }
                }
            }
        });

        // 收入构成图表
        const incomeCtx = document.getElementById('incomeChart').getContext('2d');
        const incomeChart = new Chart(incomeCtx, {
            type: 'doughnut',
            data: {
                labels: ['商品销售', '服务费', '广告收入', '其他'],
                datasets: [{
                    data: [85, 8, 5, 2],
                    backgroundColor: [
                        '#3b82f6',
                        '#059669',
                        '#d97706',
                        '#8b5cf6'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });

        // 图表时间范围切换
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // 这里可以添加重新加载图表数据的逻辑
                console.log('切换到时间范围:', this.textContent);
            });
        });

        // 交易记录筛选
        document.querySelectorAll('.filter-select').forEach(select => {
            select.addEventListener('change', function() {
                console.log('筛选条件变更:', this.value);
                // 这里可以添加筛选逻辑
            });
        });

        // 实时数据更新
        function updateFinancialData() {
            // 模拟实时数据更新
            console.log('更新财务数据...');
        }

        // 每30秒更新一次数据
        setInterval(updateFinancialData, 30000);

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('财务管理页面已加载');
            
            // 这里可以添加页面初始化逻辑
            updateFinancialData();
        });
    </script>
</body>
</html> 