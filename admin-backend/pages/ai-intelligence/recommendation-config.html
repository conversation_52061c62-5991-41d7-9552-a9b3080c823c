<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能推荐配置 - TeleShop后台管理</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .config-section {
            background: white;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .config-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .config-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .config-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
        }
        
        .status-indicator.disabled {
            background: #f44336;
        }
        
        .algorithm-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            transition: border-color 0.2s;
        }
        
        .algorithm-card:hover {
            border-color: #2196f3;
        }
        
        .algorithm-card.active {
            border-color: #2196f3;
            background: #f8f9ff;
        }
        
        .algorithm-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .algorithm-name {
            font-weight: 600;
            color: #333;
        }
        
        .algorithm-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 12px;
        }
        
        .algorithm-metrics {
            display: flex;
            gap: 20px;
        }
        
        .metric {
            text-align: center;
        }
        
        .metric-value {
            font-size: 18px;
            font-weight: 600;
            color: #2196f3;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
        }
        
        .config-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        
        .form-input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-range {
            margin: 10px 0;
        }
        
        .range-labels {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #2196f3;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 600;
            color: #2196f3;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .chart-container {
            height: 300px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="admin-dashboard">
        <div class="page-header">
            <h1>智能推荐配置</h1>
            <div class="page-actions">
                <button class="btn-primary" onclick="saveConfig()">保存配置</button>
                <button class="btn-secondary" onclick="resetConfig()">重置配置</button>
            </div>
        </div>
        
        <!-- 推荐效果统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">68.5%</div>
                <div class="stat-label">推荐点击率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">23.2%</div>
                <div class="stat-label">推荐转化率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">¥1,234.56</div>
                <div class="stat-label">推荐GMV</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">156,789</div>
                <div class="stat-label">推荐曝光量</div>
            </div>
        </div>
        
        <!-- 推荐算法配置 -->
        <div class="config-section">
            <div class="config-header">
                <h2 class="config-title">推荐算法配置</h2>
                <div class="config-status">
                    <span class="status-indicator"></span>
                    <span>算法运行中</span>
                </div>
            </div>
            
            <div class="algorithm-card active">
                <div class="algorithm-header">
                    <div class="algorithm-name">协同过滤算法</div>
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                <div class="algorithm-description">
                    基于用户行为相似度和物品相似度进行推荐，适合有一定用户行为数据的场景
                </div>
                <div class="algorithm-metrics">
                    <div class="metric">
                        <div class="metric-value">85%</div>
                        <div class="metric-label">准确率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">23%</div>
                        <div class="metric-label">覆盖率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">67%</div>
                        <div class="metric-label">新颖度</div>
                    </div>
                </div>
            </div>
            
            <div class="algorithm-card">
                <div class="algorithm-header">
                    <div class="algorithm-name">内容推荐算法</div>
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                <div class="algorithm-description">
                    基于商品特征和用户偏好匹配，解决冷启动问题
                </div>
                <div class="algorithm-metrics">
                    <div class="metric">
                        <div class="metric-value">78%</div>
                        <div class="metric-label">准确率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">45%</div>
                        <div class="metric-label">覆盖率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">89%</div>
                        <div class="metric-label">新颖度</div>
                    </div>
                </div>
            </div>
            
            <div class="algorithm-card">
                <div class="algorithm-header">
                    <div class="algorithm-name">深度学习算法</div>
                    <label class="switch">
                        <input type="checkbox">
                        <span class="slider"></span>
                    </label>
                </div>
                <div class="algorithm-description">
                    使用神经网络进行复杂特征学习，提供更精准的个性化推荐
                </div>
                <div class="algorithm-metrics">
                    <div class="metric">
                        <div class="metric-value">92%</div>
                        <div class="metric-label">准确率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">34%</div>
                        <div class="metric-label">覆盖率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">74%</div>
                        <div class="metric-label">新颖度</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 推荐参数配置 -->
        <div class="config-section">
            <div class="config-header">
                <h2 class="config-title">推荐参数配置</h2>
            </div>
            
            <div class="config-form">
                <div class="form-group">
                    <label class="form-label">推荐商品数量</label>
                    <input type="number" class="form-input" value="10" min="1" max="50">
                </div>
                
                <div class="form-group">
                    <label class="form-label">推荐场景</label>
                    <select class="form-select">
                        <option value="homepage">首页推荐</option>
                        <option value="category">分类页推荐</option>
                        <option value="product">商品详情推荐</option>
                        <option value="cart">购物车推荐</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">新品权重</label>
                    <input type="range" class="form-range" min="0" max="100" value="30">
                    <div class="range-labels">
                        <span>0%</span>
                        <span>30%</span>
                        <span>100%</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">热门商品权重</label>
                    <input type="range" class="form-range" min="0" max="100" value="50">
                    <div class="range-labels">
                        <span>0%</span>
                        <span>50%</span>
                        <span>100%</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">用户行为权重</label>
                    <input type="range" class="form-range" min="0" max="100" value="70">
                    <div class="range-labels">
                        <span>0%</span>
                        <span>70%</span>
                        <span>100%</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">更新频率</label>
                    <select class="form-select">
                        <option value="realtime">实时更新</option>
                        <option value="hourly">每小时</option>
                        <option value="daily">每日</option>
                        <option value="weekly">每周</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- 推荐效果分析 -->
        <div class="config-section">
            <div class="config-header">
                <h2 class="config-title">推荐效果分析</h2>
            </div>
            
            <div class="chart-container">
                <div>
                    <i class="fas fa-chart-line" style="font-size: 48px; margin-bottom: 16px;"></i>
                    <div>推荐效果趋势图</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function saveConfig() {
            // 收集配置数据
            const config = {
                algorithms: [],
                params: {},
                timestamp: new Date().toISOString()
            };
            
            // 收集算法配置
            document.querySelectorAll('.algorithm-card').forEach(card => {
                const name = card.querySelector('.algorithm-name').textContent;
                const enabled = card.querySelector('input[type="checkbox"]').checked;
                config.algorithms.push({ name, enabled });
            });
            
            // 收集参数配置
            document.querySelectorAll('.form-input, .form-select, .form-range').forEach(input => {
                const label = input.closest('.form-group').querySelector('.form-label').textContent;
                config.params[label] = input.value;
            });
            
            // 模拟保存
            console.log('保存配置:', config);
            
            // 显示保存成功
            const btn = document.querySelector('.btn-primary');
            const originalText = btn.textContent;
            btn.textContent = '保存成功';
            btn.style.background = '#4caf50';
            
            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.background = '';
            }, 2000);
        }
        
        function resetConfig() {
            if (confirm('确定要重置所有配置吗？')) {
                // 重置所有表单
                document.querySelectorAll('.form-input, .form-select, .form-range').forEach(input => {
                    if (input.type === 'number') {
                        input.value = '10';
                    } else if (input.type === 'range') {
                        input.value = '50';
                    } else {
                        input.selectedIndex = 0;
                    }
                });
                
                // 重置算法开关
                document.querySelectorAll('.algorithm-card input[type="checkbox"]').forEach((checkbox, index) => {
                    checkbox.checked = index < 2; // 前两个算法默认开启
                });
                
                alert('配置已重置');
            }
        }
        
        // 实时更新范围输入显示
        document.querySelectorAll('.form-range').forEach(range => {
            const labels = range.nextElementSibling.querySelectorAll('span');
            const updateDisplay = () => {
                labels[1].textContent = range.value + '%';
            };
            
            range.addEventListener('input', updateDisplay);
            updateDisplay();
        });
        
        // 模拟实时数据更新
        setInterval(() => {
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(stat => {
                const currentValue = parseFloat(stat.textContent);
                if (!isNaN(currentValue)) {
                    const change = (Math.random() - 0.5) * 2; // -1 到 1 的随机变化
                    const newValue = Math.max(0, currentValue + change);
                    if (stat.textContent.includes('%')) {
                        stat.textContent = newValue.toFixed(1) + '%';
                    } else if (stat.textContent.includes('¥')) {
                        stat.textContent = '¥' + newValue.toFixed(2);
                    } else {
                        stat.textContent = Math.round(newValue).toLocaleString();
                    }
                }
            });
        }, 5000);
    </script>
</body>
</html> 