<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能推荐引擎 - TeleShop管理后台</title>
    <link rel="stylesheet" href="../../assets/css/admin-styles.css">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --purple-color: #8b5cf6;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            --radius: 0.75rem;
            --transition: all 0.3s ease;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--background-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1.5rem 0;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--purple-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-top: 0.25rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.625rem 1.25rem;
            border: none;
            border-radius: var(--radius);
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--purple-color));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
        }

        .btn-secondary {
            background: var(--card-background);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: #f1f5f9;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
        }

        .content-area {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .algorithm-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .algorithm-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 1.5rem;
            position: relative;
            transition: var(--transition);
        }

        .algorithm-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .algorithm-status {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-inactive {
            background: rgba(100, 116, 139, 0.1);
            color: var(--secondary-color);
        }

        .status-testing {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .algorithm-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .algorithm-name {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .algorithm-desc {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .algorithm-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .metric-item {
            text-align: center;
        }

        .metric-value {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .algorithm-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.8rem;
        }

        .performance-chart {
            height: 200px;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.05));
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            border: 2px dashed var(--border-color);
        }

        .ab-test-grid {
            display: grid;
            gap: 1rem;
        }

        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            transition: var(--transition);
        }

        .test-item:hover {
            background: #f8fafc;
        }

        .test-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .test-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }

        .test-details h4 {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .test-details p {
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .test-results {
            text-align: right;
        }

        .conversion-rate {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .improvement {
            font-size: 0.8rem;
        }

        .improvement-positive {
            color: var(--success-color);
        }

        .improvement-negative {
            color: var(--danger-color);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .stat-card {
            text-align: center;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.8));
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--purple-color));
            border-radius: 3px;
            transition: width 0.5s ease;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 48px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(24px);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .algorithm-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .page-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">🤖 AI智能推荐引擎</h1>
                <p class="page-subtitle">管理和优化推荐算法，提升用户体验和转化率</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="exportData()">
                    📊 导出报告
                </button>
                <button class="btn btn-primary" onclick="createNewTest()">
                    ➕ 创建A/B测试
                </button>
            </div>
        </div>

        <div class="main-grid">
            <!-- 主内容区域 -->
            <div class="content-area">
                <!-- 推荐算法管理 -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            🧠 推荐算法配置
                        </h2>
                        <button class="btn btn-secondary btn-sm" onclick="refreshAlgorithms()">
                            🔄 刷新
                        </button>
                    </div>
                    
                    <div class="algorithm-grid">
                        <div class="algorithm-card">
                            <div class="algorithm-status status-active">运行中</div>
                            <div class="algorithm-icon" style="background: rgba(99, 102, 241, 0.1); color: var(--primary-color);">
                                🎯
                            </div>
                            <div class="algorithm-name">协同过滤算法</div>
                            <div class="algorithm-desc">基于用户行为相似性进行商品推荐，适用于个性化推荐场景</div>
                            <div class="algorithm-metrics">
                                <div class="metric-item">
                                    <div class="metric-value" style="color: var(--success-color);">92.5%</div>
                                    <div class="metric-label">准确率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" style="color: var(--info-color);">3.2%</div>
                                    <div class="metric-label">点击率</div>
                                </div>
                            </div>
                            <div class="algorithm-actions">
                                <button class="btn btn-primary btn-sm">配置</button>
                                <button class="btn btn-secondary btn-sm">分析</button>
                            </div>
                        </div>

                        <div class="algorithm-card">
                            <div class="algorithm-status status-testing">测试中</div>
                            <div class="algorithm-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                                📈
                            </div>
                            <div class="algorithm-name">深度学习模型</div>
                            <div class="algorithm-desc">基于神经网络的推荐算法，能够处理复杂的用户行为模式</div>
                            <div class="algorithm-metrics">
                                <div class="metric-item">
                                    <div class="metric-value" style="color: var(--warning-color);">89.3%</div>
                                    <div class="metric-label">准确率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" style="color: var(--info-color);">3.8%</div>
                                    <div class="metric-label">点击率</div>
                                </div>
                            </div>
                            <div class="algorithm-actions">
                                <button class="btn btn-primary btn-sm">配置</button>
                                <button class="btn btn-secondary btn-sm">分析</button>
                            </div>
                        </div>

                        <div class="algorithm-card">
                            <div class="algorithm-status status-inactive">已停用</div>
                            <div class="algorithm-icon" style="background: rgba(139, 92, 246, 0.1); color: var(--purple-color);">
                                🔍
                            </div>
                            <div class="algorithm-name">内容推荐算法</div>
                            <div class="algorithm-desc">基于商品属性和用户偏好的内容相似性推荐算法</div>
                            <div class="algorithm-metrics">
                                <div class="metric-item">
                                    <div class="metric-value" style="color: var(--secondary-color);">85.7%</div>
                                    <div class="metric-label">准确率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" style="color: var(--secondary-color);">2.4%</div>
                                    <div class="metric-label">点击率</div>
                                </div>
                            </div>
                            <div class="algorithm-actions">
                                <button class="btn btn-primary btn-sm">启用</button>
                                <button class="btn btn-secondary btn-sm">删除</button>
                            </div>
                        </div>

                        <div class="algorithm-card">
                            <div class="algorithm-status status-active">运行中</div>
                            <div class="algorithm-icon" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                                ⚡
                            </div>
                            <div class="algorithm-name">实时推荐引擎</div>
                            <div class="algorithm-desc">基于用户实时行为的动态推荐系统，响应速度快</div>
                            <div class="algorithm-metrics">
                                <div class="metric-item">
                                    <div class="metric-value" style="color: var(--success-color);">94.1%</div>
                                    <div class="metric-label">准确率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" style="color: var(--success-color);">4.2%</div>
                                    <div class="metric-label">点击率</div>
                                </div>
                            </div>
                            <div class="algorithm-actions">
                                <button class="btn btn-primary btn-sm">配置</button>
                                <button class="btn btn-secondary btn-sm">分析</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 性能分析 -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            📊 性能分析
                        </h2>
                        <div style="display: flex; gap: 1rem;">
                            <select class="form-control" style="width: auto;">
                                <option>最近7天</option>
                                <option>最近30天</option>
                                <option>最近90天</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="performance-chart">
                        📈 推荐算法性能趋势图<br>
                        <small>Chart.js 集成位置 - 显示点击率、转化率、收入等指标</small>
                    </div>
                </div>

                <!-- A/B测试管理 -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            🧪 A/B测试管理
                        </h2>
                        <button class="btn btn-primary btn-sm" onclick="createNewTest()">
                            ➕ 新建测试
                        </button>
                    </div>
                    
                    <div class="ab-test-grid">
                        <div class="test-item">
                            <div class="test-info">
                                <div class="test-icon" style="background: var(--success-color); color: white;">
                                    A
                                </div>
                                <div class="test-details">
                                    <h4>首页推荐位置优化</h4>
                                    <p>测试不同推荐位置对用户点击率的影响 • 运行中</p>
                                </div>
                            </div>
                            <div class="test-results">
                                <div class="conversion-rate">3.8%</div>
                                <div class="improvement improvement-positive">+0.5% 提升</div>
                            </div>
                        </div>

                        <div class="test-item">
                            <div class="test-info">
                                <div class="test-icon" style="background: var(--warning-color); color: white;">
                                    B
                                </div>
                                <div class="test-details">
                                    <h4>算法权重调整测试</h4>
                                    <p>对比协同过滤和深度学习算法效果 • 待启动</p>
                                </div>
                            </div>
                            <div class="test-results">
                                <div class="conversion-rate">-</div>
                                <div class="improvement">等待开始</div>
                            </div>
                        </div>

                        <div class="test-item">
                            <div class="test-info">
                                <div class="test-icon" style="background: var(--info-color); color: white;">
                                    C
                                </div>
                                <div class="test-details">
                                    <h4>个性化推荐数量测试</h4>
                                    <p>测试推荐商品数量对转化率的影响 • 已完成</p>
                                </div>
                            </div>
                            <div class="test-results">
                                <div class="conversion-rate">4.2%</div>
                                <div class="improvement improvement-positive">+1.2% 提升</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 关键指标 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            📈 关键指标
                        </h3>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" style="color: var(--primary-color);">92.5%</div>
                            <div class="stat-label">整体准确率</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 92.5%"></div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-value" style="color: var(--success-color);">3.8%</div>
                            <div class="stat-label">平均点击率</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 76%"></div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-value" style="color: var(--warning-color);">1.2%</div>
                            <div class="stat-label">转化率</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 60%"></div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-value" style="color: var(--info-color);">247ms</div>
                            <div class="stat-label">响应时间</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速配置 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            ⚙️ 快速配置
                        </h3>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">推荐算法优先级</label>
                        <select class="form-control">
                            <option>协同过滤 (主要)</option>
                            <option>深度学习 (辅助)</option>
                            <option>内容推荐 (备用)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">推荐商品数量</label>
                        <input type="number" class="form-control" value="12" min="1" max="50">
                    </div>

                    <div class="form-group">
                        <label class="form-label">更新频率 (分钟)</label>
                        <input type="number" class="form-control" value="30" min="5" max="1440">
                    </div>

                    <div class="form-group" style="display: flex; justify-content: space-between; align-items: center;">
                        <label class="form-label" style="margin-bottom: 0;">启用实时推荐</label>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="form-group" style="display: flex; justify-content: space-between; align-items: center;">
                        <label class="form-label" style="margin-bottom: 0;">个性化推荐</label>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>

                    <button class="btn btn-primary" style="width: 100%;" onclick="saveConfiguration()">
                        💾 保存配置
                    </button>
                </div>

                <!-- 系统状态 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            🔍 系统状态
                        </h3>
                    </div>
                    
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>推荐服务</span>
                            <span style="color: var(--success-color); font-weight: 600;">● 正常</span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>数据管道</span>
                            <span style="color: var(--success-color); font-weight: 600;">● 正常</span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>模型训练</span>
                            <span style="color: var(--warning-color); font-weight: 600;">● 进行中</span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>缓存系统</span>
                            <span style="color: var(--success-color); font-weight: 600;">● 正常</span>
                        </div>
                    </div>
                    
                    <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid var(--border-color);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <span style="font-size: 0.9rem;">CPU使用率</span>
                            <span style="font-size: 0.9rem; font-weight: 600;">68%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 68%"></div>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center; margin: 0.5rem 0;">
                            <span style="font-size: 0.9rem;">内存使用率</span>
                            <span style="font-size: 0.9rem; font-weight: 600;">84%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 84%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 刷新算法数据
        function refreshAlgorithms() {
            showNotification('正在刷新算法数据...', 'info');
            
            setTimeout(() => {
                // 模拟数据更新
                const metricValues = document.querySelectorAll('.metric-value');
                metricValues.forEach(value => {
                    if (value.textContent.includes('%')) {
                        const current = parseFloat(value.textContent);
                        const newValue = (current + (Math.random() * 2 - 1)).toFixed(1);
                        value.textContent = newValue + '%';
                    }
                });
                
                showNotification('算法数据已更新', 'success');
            }, 1000);
        }

        // 创建新的A/B测试
        function createNewTest() {
            const testName = prompt('请输入测试名称：');
            if (testName) {
                showNotification(`A/B测试 "${testName}" 创建成功`, 'success');
            }
        }

        // 保存配置
        function saveConfiguration() {
            showNotification('正在保存配置...', 'info');
            
            setTimeout(() => {
                showNotification('配置保存成功', 'success');
            }, 800);
        }

        // 导出数据
        function exportData() {
            showNotification('正在生成报告...', 'info');
            
            setTimeout(() => {
                showNotification('报告导出成功', 'success');
            }, 1500);
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = 'notification';
            
            const colors = {
                success: 'var(--success-color)',
                warning: 'var(--warning-color)',
                error: 'var(--danger-color)',
                info: 'var(--info-color)'
            };
            
            notification.style.borderLeftColor = colors[type] || colors.info;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 实时更新指标
        function updateMetrics() {
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(value => {
                if (value.textContent.includes('%')) {
                    const current = parseFloat(value.textContent);
                    const change = (Math.random() * 0.4 - 0.2); // -0.2% 到 +0.2% 的变化
                    const newValue = Math.max(0, Math.min(100, current + change));
                    value.textContent = newValue.toFixed(1) + '%';
                    
                    // 更新对应的进度条
                    const progressBar = value.closest('.stat-card')?.querySelector('.progress-fill');
                    if (progressBar) {
                        progressBar.style.width = newValue + '%';
                    }
                } else if (value.textContent.includes('ms')) {
                    const current = parseInt(value.textContent);
                    const change = Math.floor(Math.random() * 20 - 10); // -10ms 到 +10ms
                    const newValue = Math.max(50, current + change);
                    value.textContent = newValue + 'ms';
                }
            });
        }

        // 页面加载完成后启动实时更新
        document.addEventListener('DOMContentLoaded', () => {
            // 每30秒更新一次指标
            setInterval(updateMetrics, 30000);
            
            // 显示欢迎消息
            setTimeout(() => {
                showNotification('AI推荐引擎管理系统已启动', 'success');
            }, 500);
        });

        // 开关切换处理
        document.querySelectorAll('.toggle-switch input').forEach(toggle => {
            toggle.addEventListener('change', function() {
                const label = this.closest('.form-group').querySelector('.form-label').textContent;
                const status = this.checked ? '已启用' : '已禁用';
                showNotification(`${label} ${status}`, 'info');
            });
        });

        // 表单输入变化监听
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('change', function() {
                if (this.type === 'number') {
                    const value = this.value;
                    const label = this.previousElementSibling.textContent;
                    showNotification(`${label}已更新为 ${value}`, 'info');
                }
            });
        });
    </script>
</body>
</html> 