<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>TeleShop 后台管理系统</title>
    <!-- 内联核心样式，避免外部资源加载问题 -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-page {
            width: 100%;
            max-width: 420px;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .admin-logo {
            width: 64px;
            height: 64px;
            background: #3b82f6;
            border-radius: 12px;
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        h1 {
            font-size: 24px;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .subtitle {
            color: #64748b;
            margin-bottom: 32px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }
        
        .input-wrapper {
            position: relative;
        }
        
        input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .login-btn {
            width: 100%;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-top: 24px;
        }
        
        .login-btn:hover {
            background: #2563eb;
        }
        
        .login-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 16px 0;
            font-size: 14px;
        }
        
        .checkbox-wrapper {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .forgot-password {
            color: #3b82f6;
            text-decoration: none;
        }
        
        .security-notice {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 12px;
            margin-top: 20px;
            font-size: 12px;
            color: #0369a1;
        }
        
        .admin-dashboard {
            display: none;
            min-height: 100vh;
            background: #f8fafc;
        }
        
        .admin-header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .header-logo {
            width: 32px;
            height: 32px;
            background: #3b82f6;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .brand-name {
            font-weight: 600;
            color: #1e293b;
        }
        
        .welcome-content {
            padding: 40px;
            text-align: center;
            max-width: 600px;
            margin: 40px auto;
        }
        
        .welcome-title {
            font-size: 32px;
            color: #1e293b;
            margin-bottom: 16px;
        }
        
        .welcome-desc {
            color: #64748b;
            font-size: 16px;
            margin-bottom: 32px;
        }
        
        .nav-menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        
        .nav-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            text-decoration: none;
            color: #1e293b;
            transition: all 0.2s;
        }
        
        .nav-item:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .nav-icon {
            width: 48px;
            height: 48px;
            background: #f1f5f9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #3b82f6;
            margin-bottom: 16px;
        }
        
        .nav-title {
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .nav-desc {
            font-size: 14px;
            color: #64748b;
        }
        
        .logout-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .login-container {
                padding: 24px;
            }
            
            .nav-menu {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="login-page">
        <div class="login-container">
            <div class="admin-logo">
                🏪
            </div>
            <h1>TeleShop 管理后台</h1>
            <p class="subtitle">欢迎使用 TeleShop 后台管理系统</p>
            
            <form id="adminLoginForm">
                <div class="form-group">
                    <label for="adminEmail">邮箱地址</label>
                    <div class="input-wrapper">
                        <input type="email" id="adminEmail" placeholder="请输入管理员邮箱" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="adminPassword">密码</label>
                    <div class="input-wrapper">
                        <input type="password" id="adminPassword" placeholder="请输入密码" required>
                    </div>
                </div>
                
                <div class="form-options">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" id="rememberMe">
                        记住登录状态
                    </label>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>
                
                <button type="submit" class="login-btn">
                    登录后台
                </button>
                
                <div class="security-notice">
                    🛡️ 安全提醒：管理员账户具有最高权限，请妥善保管登录凭证
                </div>
            </form>
        </div>
    </div>
    
    <!-- 主后台界面 -->
    <div id="adminDashboard" class="admin-dashboard">
        <header class="admin-header">
            <div class="logo-section">
                <div class="header-logo">🏪</div>
                <span class="brand-name">TeleShop Admin</span>
            </div>
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </header>
        
        <div class="welcome-content">
            <h1 class="welcome-title">欢迎来到 TeleShop 管理后台</h1>
            <p class="welcome-desc">系统已成功登录，您可以开始管理您的电商平台</p>
            
            <div class="nav-menu">
                <a href="pages/dashboard/index.html" class="nav-item" target="_blank">
                    <div class="nav-icon">📊</div>
                    <div class="nav-title">仪表盘</div>
                    <div class="nav-desc">查看业务概况和关键指标</div>
                </a>
                
                <a href="pages/user-management/index.html" class="nav-item" target="_blank">
                    <div class="nav-icon">👥</div>
                    <div class="nav-title">用户管理</div>
                    <div class="nav-desc">管理用户账户和权限</div>
                </a>
                
                <a href="pages/product-management/index.html" class="nav-item" target="_blank">
                    <div class="nav-icon">📦</div>
                    <div class="nav-title">商品管理</div>
                    <div class="nav-desc">管理商品信息和库存</div>
                </a>
                
                <a href="pages/order-management/index.html" class="nav-item" target="_blank">
                    <div class="nav-icon">🛒</div>
                    <div class="nav-title">订单管理</div>
                    <div class="nav-desc">处理订单和物流信息</div>
                </a>
                
                <a href="pages/financial-management/index.html" class="nav-item" target="_blank">
                    <div class="nav-icon">💰</div>
                    <div class="nav-title">财务管理</div>
                    <div class="nav-desc">管理财务数据和报表</div>
                </a>
                
                <a href="pages/reports-analytics/advanced-reports.html" class="nav-item" target="_blank">
                    <div class="nav-icon">📈</div>
                    <div class="nav-title">高级报表</div>
                    <div class="nav-desc">查看详细的数据分析</div>
                </a>
                
                <a href="pages/system-settings/role-management.html" class="nav-item" target="_blank">
                    <div class="nav-icon">⚙️</div>
                    <div class="nav-title">权限管理</div>
                    <div class="nav-desc">配置用户角色和权限</div>
                </a>
                
                <a href="pages/system-settings/index.html" class="nav-item" target="_blank">
                    <div class="nav-icon">🔧</div>
                    <div class="nav-title">系统设置</div>
                    <div class="nav-desc">配置系统参数和选项</div>
                </a>
            </div>
        </div>
    </div>
    
    <!-- 轻量级JavaScript，无外部依赖 -->
    <script>
        console.log('🚀 TeleShop Admin Lite 启动');
        
        // 确保页面立即可用
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ 页面加载完成');
            
            // 绑定登录表单
            const loginForm = document.getElementById('adminLoginForm');
            const emailInput = document.getElementById('adminEmail');
            const passwordInput = document.getElementById('adminPassword');
            
            // 确保输入框可用
            emailInput.removeAttribute('disabled');
            passwordInput.removeAttribute('disabled');
            
            console.log('✅ 输入框已启用');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = emailInput.value.trim();
                const password = passwordInput.value.trim();
                
                console.log('🔐 尝试登录:', email);
                
                if (!email || !password) {
                    alert('请输入邮箱和密码');
                    return;
                }
                
                // 简单验证（演示用）
                if (email.includes('@') && password.length >= 4) {
                    console.log('✅ 登录成功');
                    
                    // 隐藏登录页面
                    document.getElementById('loginPage').style.display = 'none';
                    // 显示后台界面
                    document.getElementById('adminDashboard').style.display = 'block';
                    
                    alert('登录成功！欢迎来到 TeleShop 后台管理系统');
                } else {
                    alert('邮箱格式不正确或密码太短');
                }
            });
            
            // 测试输入框焦点
            emailInput.focus();
            console.log('✅ 输入框焦点设置完成');
        });
        
        // 退出登录
        function logout() {
            console.log('👋 退出登录');
            document.getElementById('adminDashboard').style.display = 'none';
            document.getElementById('loginPage').style.display = 'flex';
            
            // 清空表单
            document.getElementById('adminEmail').value = '';
            document.getElementById('adminPassword').value = '';
        }
        
        console.log('✅ TeleShop Admin Lite 初始化完成');
    </script>
</body>
</html> 