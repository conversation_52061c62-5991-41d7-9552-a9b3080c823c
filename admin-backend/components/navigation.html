<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TeleShop 后台管理导航</title>
    <link rel="stylesheet" href="../assets/css/admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
            background: #f8fafc;
        }
        
        .sidebar {
            width: 280px;
            background: white;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
        }
        
        .sidebar.collapsed {
            transform: translateX(-280px);
        }
        
        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid #e2e8f0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .subtitle {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .nav-menu {
            padding: 16px 0;
        }
        
        .nav-section {
            margin-bottom: 32px;
        }
        
        .nav-section-title {
            padding: 8px 24px;
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .nav-item {
            margin-bottom: 4px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            position: relative;
        }
        
        .nav-link:hover {
            background: #f1f5f9;
            color: #1e293b;
            text-decoration: none;
        }
        
        .nav-link.active {
            background: #3b82f6;
            color: white;
        }
        
        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #1d4ed8;
        }
        
        .nav-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .nav-text {
            flex: 1;
        }
        
        .nav-badge {
            background: #ef4444;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }
        
        .nav-arrow {
            transition: transform 0.2s;
        }
        
        .nav-item.expanded .nav-arrow {
            transform: rotate(90deg);
        }
        
        .sub-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8fafc;
        }
        
        .nav-item.expanded .sub-menu {
            max-height: 500px;
        }
        
        .sub-nav-link {
            display: flex;
            align-items: center;
            padding: 8px 24px 8px 56px;
            color: #64748b;
            text-decoration: none;
            font-size: 13px;
            transition: all 0.2s;
        }
        
        .sub-nav-link:hover {
            background: #e2e8f0;
            color: #1e293b;
            text-decoration: none;
        }
        
        .sub-nav-link.active {
            background: #dbeafe;
            color: #1d4ed8;
        }
        
        .main-content {
            flex: 1;
            margin-left: 280px;
            transition: margin-left 0.3s ease;
        }
        
        .main-content.expanded {
            margin-left: 0;
        }
        
        .top-bar {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .toggle-sidebar {
            display: none;
            background: none;
            border: none;
            font-size: 18px;
            color: #64748b;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            margin-right: 16px;
        }
        
        .toggle-sidebar:hover {
            background: #f1f5f9;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #64748b;
        }
        
        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .breadcrumb-separator {
            color: #cbd5e1;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-left: auto;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 18px;
            color: #64748b;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
        }
        
        .notification-btn:hover {
            background: #f1f5f9;
        }
        
        .notification-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: background 0.2s;
        }
        
        .user-profile:hover {
            background: #f1f5f9;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e2e8f0;
        }
        
        .user-info {
            display: flex;
            flex-direction: column;
        }
        
        .user-name {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .user-role {
            font-size: 12px;
            color: #64748b;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-280px);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .toggle-sidebar {
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div>
                        <div>TeleShop</div>
                        <div class="subtitle">后台管理系统</div>
                    </div>
                </div>
            </div>
            
            <nav class="nav-menu">
                <!-- 仪表板 -->
                <div class="nav-section">
                    <div class="nav-section-title">仪表板</div>
                    <div class="nav-item">
                        <a href="../pages/dashboard/index.html" class="nav-link">
                            <i class="fas fa-tachometer-alt nav-icon"></i>
                            <span class="nav-text">概览</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="../pages/dashboard/system-monitor.html" class="nav-link">
                            <i class="fas fa-desktop nav-icon"></i>
                            <span class="nav-text">系统监控</span>
                            <span class="nav-badge">实时</span>
                        </a>
                    </div>
                </div>
                
                <!-- 用户管理 -->
                <div class="nav-section">
                    <div class="nav-section-title">用户管理</div>
                    <div class="nav-item">
                        <a href="#" class="nav-link" onclick="toggleSubMenu(this)">
                            <i class="fas fa-users nav-icon"></i>
                            <span class="nav-text">用户管理</span>
                            <i class="fas fa-chevron-right nav-arrow"></i>
                        </a>
                        <div class="sub-menu">
                            <a href="../pages/user-management/index.html" class="sub-nav-link">用户列表</a>
                            <a href="../pages/user-management/enhanced.html" class="sub-nav-link">高级管理</a>
                            <a href="../pages/user-management/batch-operations.html" class="sub-nav-link">批量操作</a>
                            <a href="../pages/user-management/member-marketing.html" class="sub-nav-link">会员营销</a>
                        </div>
                    </div>
                </div>
                
                <!-- 内容管理 -->
                <div class="nav-section">
                    <div class="nav-section-title">内容管理</div>
                    <div class="nav-item">
                        <a href="#" class="nav-link" onclick="toggleSubMenu(this)">
                            <i class="fas fa-shield-alt nav-icon"></i>
                            <span class="nav-text">内容审核</span>
                            <span class="nav-badge">5</span>
                            <i class="fas fa-chevron-right nav-arrow"></i>
                        </a>
                        <div class="sub-menu">
                            <a href="../pages/content-management/message-content-audit.html" class="sub-nav-link">消息审核</a>
                            <a href="../pages/content-management/content-moderation.html" class="sub-nav-link">内容审核</a>
                            <a href="../pages/content-management/group-channel-management.html" class="sub-nav-link">群组管理</a>
                        </div>
                    </div>
                    <div class="nav-item">
                        <a href="../pages/content-management/chat-management.html" class="nav-link">
                            <i class="fas fa-comments nav-icon"></i>
                            <span class="nav-text">聊天管理</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="../pages/content-management/notification-system.html" class="nav-link">
                            <i class="fas fa-bell nav-icon"></i>
                            <span class="nav-text">通知系统</span>
                        </a>
                    </div>
                </div>
                
                <!-- 商户管理 -->
                <div class="nav-section">
                    <div class="nav-section-title">商户管理</div>
                    <div class="nav-item">
                        <a href="#" class="nav-link" onclick="toggleSubMenu(this)">
                            <i class="fas fa-store nav-icon"></i>
                            <span class="nav-text">商户管理</span>
                            <i class="fas fa-chevron-right nav-arrow"></i>
                        </a>
                        <div class="sub-menu">
                            <a href="../pages/merchant-management/index.html" class="sub-nav-link">商户列表</a>
                            <a href="../pages/merchant-management/merchant-application.html" class="sub-nav-link">入驻申请</a>
                            <a href="../pages/merchant-management/commission-management.html" class="sub-nav-link">佣金管理</a>
                            <a href="../pages/merchant-management/advanced-marketing.html" class="sub-nav-link">营销管理</a>
                        </div>
                    </div>
                </div>
                
                <!-- 订单管理 -->
                <div class="nav-section">
                    <div class="nav-section-title">订单管理</div>
                    <div class="nav-item">
                        <a href="#" class="nav-link" onclick="toggleSubMenu(this)">
                            <i class="fas fa-shopping-bag nav-icon"></i>
                            <span class="nav-text">订单管理</span>
                            <i class="fas fa-chevron-right nav-arrow"></i>
                        </a>
                        <div class="sub-menu">
                            <a href="../pages/order-management/index.html" class="sub-nav-link">订单列表</a>
                            <a href="../pages/order-management/enhanced-index.html" class="sub-nav-link">高级管理</a>
                            <a href="../pages/order-management/b2b2c-order-management.html" class="sub-nav-link">B2B2C管理</a>
                        </div>
                    </div>
                </div>
                
                <!-- 数据分析 -->
                <div class="nav-section">
                    <div class="nav-section-title">数据分析</div>
                    <div class="nav-item">
                        <a href="#" class="nav-link" onclick="toggleSubMenu(this)">
                            <i class="fas fa-chart-bar nav-icon"></i>
                            <span class="nav-text">数据分析</span>
                            <i class="fas fa-chevron-right nav-arrow"></i>
                        </a>
                        <div class="sub-menu">
                            <a href="../pages/data-analytics/index.html" class="sub-nav-link">分析首页</a>
                            <a href="../pages/data-analytics/user-data-analysis.html" class="sub-nav-link">用户分析</a>
                            <a href="../pages/data-analytics/message-data-statistics.html" class="sub-nav-link">消息统计</a>
                            <a href="../pages/data-analytics/enhanced-analytics.html" class="sub-nav-link">高级分析</a>
                        </div>
                    </div>
                </div>
                
                <!-- 财务管理 -->
                <div class="nav-section">
                    <div class="nav-section-title">财务管理</div>
                    <div class="nav-item">
                        <a href="#" class="nav-link" onclick="toggleSubMenu(this)">
                            <i class="fas fa-coins nav-icon"></i>
                            <span class="nav-text">财务管理</span>
                            <i class="fas fa-chevron-right nav-arrow"></i>
                        </a>
                        <div class="sub-menu">
                            <a href="../pages/financial-management/index.html" class="sub-nav-link">财务首页</a>
                            <a href="../pages/financial-management/enhanced-financial-data.html" class="sub-nav-link">财务数据</a>
                        </div>
                    </div>
                </div>
                
                <!-- 系统设置 -->
                <div class="nav-section">
                    <div class="nav-section-title">系统设置</div>
                    <div class="nav-item">
                        <a href="../pages/system-settings/security-management.html" class="nav-link">
                            <i class="fas fa-shield-alt nav-icon"></i>
                            <span class="nav-text">安全管控</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link" onclick="toggleSubMenu(this)">
                            <i class="fas fa-cogs nav-icon"></i>
                            <span class="nav-text">系统配置</span>
                            <i class="fas fa-chevron-right nav-arrow"></i>
                        </a>
                        <div class="sub-menu">
                            <a href="../pages/system-settings/index.html" class="sub-nav-link">基础设置</a>
                            <a href="../pages/system-settings/advanced-settings.html" class="sub-nav-link">高级设置</a>
                            <a href="../pages/system-settings/role-management.html" class="sub-nav-link">角色权限</a>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content" id="mainContent">
            <div class="top-bar">
                <button class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </div>
                    <div class="breadcrumb-separator">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <div class="breadcrumb-item">
                        <span id="currentPage">仪表板</span>
                    </div>
                </div>
                
                <div class="user-menu">
                    <button class="notification-btn" onclick="showNotifications()">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <div class="user-profile" onclick="showUserMenu()">
                        <img src="../assets/images/avatar-default.png" alt="用户头像" class="user-avatar">
                        <div class="user-info">
                            <div class="user-name">管理员</div>
                            <div class="user-role">超级管理员</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 页面内容将在这里加载 -->
            <div id="pageContent">
                <div style="padding: 24px; text-align: center; color: #64748b;">
                    <i class="fas fa-home" style="font-size: 48px; margin-bottom: 16px;"></i>
                    <h2>欢迎使用 TeleShop 后台管理系统</h2>
                    <p>请从左侧菜单选择要管理的功能模块</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        }
        
        function toggleSubMenu(element) {
            const navItem = element.parentElement;
            navItem.classList.toggle('expanded');
        }
        
        function showNotifications() {
            console.log('显示通知');
            // 这里可以添加通知弹窗逻辑
        }
        
        function showUserMenu() {
            console.log('显示用户菜单');
            // 这里可以添加用户菜单逻辑
        }
        
        // 设置当前激活的菜单项
        function setActiveMenu(menuPath) {
            // 移除所有激活状态
            document.querySelectorAll('.nav-link, .sub-nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // 设置当前激活状态
            const activeLink = document.querySelector(`[href="${menuPath}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
                
                // 如果是子菜单，展开父菜单
                const subMenu = activeLink.closest('.sub-menu');
                if (subMenu) {
                    subMenu.parentElement.classList.add('expanded');
                }
            }
        }
        
        // 响应式处理
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
                if (!sidebar.classList.contains('collapsed')) {
                    mainContent.classList.remove('expanded');
                }
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 根据当前页面URL设置激活菜单
            const currentPath = window.location.pathname;
            setActiveMenu(currentPath);
        });
    </script>
</body>
</html> 