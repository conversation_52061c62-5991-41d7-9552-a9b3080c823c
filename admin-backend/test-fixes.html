<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>修复功能测试页面 - TeleShop 后台管理</title>
    <link rel="stylesheet" href="assets/css/universal-fixes.css">
    <link rel="stylesheet" href="assets/css/admin-styles.css">
    <link rel="stylesheet" href="assets/css/mobile-enhancements.css">
    <link rel="stylesheet" href="assets/css/image-fixes.css">
    <link rel="stylesheet" href="assets/css/button-layout-fixes.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="assets/js/image-error-handler.js"></script>
    <style>
        .test-page {
            padding: 24px;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 12px;
        }
        
        .test-label {
            font-weight: 500;
            color: #475569;
            min-width: 120px;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .status-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 16px;
        }
        
        .sample-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }
        
        .sample-table th,
        .sample-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .sample-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }
        
        .test-log {
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 6px;
            padding: 12px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 16px;
        }
    </style>
</head>
<body>
    <div class="test-page">
        <h1 style="font-size: 28px; font-weight: 700; color: #1e293b; margin-bottom: 32px;">
            <i class="fas fa-bug" style="color: #3b82f6; margin-right: 12px;"></i>
            TeleShop 后台管理系统 - 修复功能测试
        </h1>
        
        <!-- 按钮布局测试 -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-mouse-pointer"></i>
                按钮布局修复测试
            </h2>
            
            <div class="test-item">
                <span class="test-label">基础按钮:</span>
                <div class="button-grid">
                    <button class="action-btn">
                        <i class="fas fa-plus"></i>
                        添加用户
                    </button>
                    <button class="action-btn primary">
                        <i class="fas fa-save"></i>
                        保存数据
                    </button>
                    <button class="action-btn danger">
                        <i class="fas fa-trash"></i>
                        删除项目
                    </button>
                    <button class="action-btn warning">
                        <i class="fas fa-edit"></i>
                        编辑内容
                    </button>
                </div>
            </div>
            
            <div class="test-item">
                <span class="test-label">小尺寸按钮:</span>
                <div class="button-grid">
                    <button class="btn-sm">
                        <i class="fas fa-eye"></i>
                        查看
                    </button>
                    <button class="btn-sm primary">
                        <i class="fas fa-check"></i>
                        确认
                    </button>
                    <button class="btn-sm danger">
                        <i class="fas fa-ban"></i>
                        拒绝
                    </button>
                    <button class="btn-sm warning">
                        <i class="fas fa-clock"></i>
                        待处理
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 图片占位符测试 -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-image"></i>
                图片占位符修复测试
            </h2>
            
            <div class="test-item">
                <span class="test-label">用户头像:</span>
                <div class="image-grid">
                    <div class="user-avatar">
                        <img src="non-existent-avatar.jpg" alt="用户头像" data-name="张三">
                    </div>
                    <div class="user-avatar">
                        <img src="invalid-image.png" alt="管理员头像" data-name="李四">
                    </div>
                    <div class="user-avatar">
                        <!-- 无图片的情况 -->
                    </div>
                </div>
            </div>
            
            <div class="test-item">
                <span class="test-label">商品图片:</span>
                <div class="image-grid">
                    <div class="product-image">
                        <img src="non-existent-product.jpg" alt="商品图片">
                    </div>
                    <div class="product-image">
                        <img src="invalid-product.png" alt="产品图片">
                    </div>
                    <div class="product-image">
                        <!-- 无图片的情况 -->
                    </div>
                </div>
            </div>
            
            <div class="test-item">
                <span class="test-label">直接占位符:</span>
                <div class="image-grid">
                    <div class="image-placeholder type-avatar size-md">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="image-placeholder type-product size-md">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="image-placeholder type-logo size-lg">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 状态标签测试 -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-tags"></i>
                状态标签显示测试
            </h2>
            
            <div class="test-item">
                <span class="test-label">订单状态:</span>
                <div class="status-grid">
                    <span class="status-badge status-pending">待付款</span>
                    <span class="status-badge status-paid">已付款</span>
                    <span class="status-badge status-shipped">已发货</span>
                    <span class="status-badge status-delivered">已完成</span>
                    <span class="status-badge status-refunding">退款中</span>
                    <span class="status-badge status-cancelled">已取消</span>
                </div>
            </div>
            
            <div class="test-item">
                <span class="test-label">用户状态:</span>
                <div class="status-grid">
                    <span class="status-badge" style="background: #dcfce7; color: #166534;">活跃</span>
                    <span class="status-badge" style="background: #fef3c7; color: #92400e;">待激活</span>
                    <span class="status-badge" style="background: #fee2e2; color: #dc2626;">已禁用</span>
                    <span class="status-badge" style="background: #e0e7ff; color: #3730a3;">VIP用户</span>
                </div>
            </div>
        </div>
        
        <!-- 表格显示测试 -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-table"></i>
                表格数据显示测试
            </h2>
            
            <table class="sample-table">
                <thead>
                    <tr>
                        <th>用户</th>
                        <th>商品</th>
                        <th>状态</th>
                        <th>金额</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="customer-info">
                                <div class="user-avatar">
                                    <img src="invalid-user.jpg" alt="用户头像">
                                </div>
                                <div>
                                    <div class="customer-name">张三</div>
                                    <div class="customer-id">#U001</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="product-info">
                                <div class="product-image">
                                    <img src="invalid-product.jpg" alt="商品图片">
                                </div>
                                <div>
                                    <div class="product-name">iPhone 15 Pro Max 256GB 深空黑色</div>
                                    <div class="product-count">数量: 1</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-paid">已付款</span>
                        </td>
                        <td>
                            <span class="amount-value">¥8,999</span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-sm">
                                    <i class="fas fa-eye"></i>
                                    查看
                                </button>
                                <button class="btn-sm primary">
                                    <i class="fas fa-edit"></i>
                                    编辑
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="customer-info">
                                <div class="user-avatar">
                                    <img src="another-invalid.jpg" alt="用户头像">
                                </div>
                                <div>
                                    <div class="customer-name">李四</div>
                                    <div class="customer-id">#U002</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="product-info">
                                <div class="product-image">
                                    <img src="another-invalid-product.jpg" alt="商品图片">
                                </div>
                                <div>
                                    <div class="product-name">Nike Air Max 270 白色运动鞋</div>
                                    <div class="product-count">数量: 2</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-shipped">已发货</span>
                        </td>
                        <td>
                            <span class="amount-value">¥1,298</span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-sm">
                                    <i class="fas fa-truck"></i>
                                    物流
                                </button>
                                <button class="btn-sm warning">
                                    <i class="fas fa-phone"></i>
                                    联系
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 移动端适配测试 -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-mobile-alt"></i>
                移动端适配测试
            </h2>
            
            <div class="test-item">
                <span class="test-label">响应式按钮:</span>
                <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                    <button class="action-btn">
                        <i class="fas fa-plus"></i>
                        添加新项目
                    </button>
                    <button class="action-btn primary">
                        <i class="fas fa-download"></i>
                        下载报表数据
                    </button>
                    <button class="action-btn danger">
                        <i class="fas fa-trash-alt"></i>
                        批量删除选中项
                    </button>
                </div>
            </div>
            
            <div class="test-item">
                <span class="test-label">提示信息:</span>
                <p style="margin: 0; color: #64748b; font-size: 14px;">
                    在移动设备上，长按钮文字会自动缩短，按钮会适配触摸操作。
                    请调整浏览器窗口大小或使用开发者工具的设备模拟器来测试。
                </p>
            </div>
        </div>
        
        <!-- 测试日志 -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-clipboard-list"></i>
                测试日志
            </h2>
            
            <div class="test-log" id="testLog">
                正在初始化测试环境...<br>
            </div>
            
            <div style="margin-top: 16px;">
                <button class="action-btn primary" onclick="runTests()">
                    <i class="fas fa-play"></i>
                    运行测试
                </button>
                <button class="action-btn" onclick="clearLog()">
                    <i class="fas fa-eraser"></i>
                    清空日志
                </button>
                <button class="action-btn" onclick="reloadImages()">
                    <i class="fas fa-sync"></i>
                    重新处理图片
                </button>
            </div>
        </div>
    </div>
    
    <script>
        let testLog = document.getElementById('testLog');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            testLog.innerHTML += `[${timestamp}] ${message}<br>`;
            testLog.scrollTop = testLog.scrollHeight;
        }
        
        function clearLog() {
            testLog.innerHTML = '日志已清空<br>';
        }
        
        function runTests() {
            log('开始运行测试...');
            
            // 测试图片处理
            log('测试图片错误处理功能...');
            const images = document.querySelectorAll('img');
            log(`发现 ${images.length} 张图片`);
            
            if (window.ImageHandler) {
                window.ImageHandler.processImages();
                log('图片处理器已执行');
            } else {
                log('警告: 图片处理器未加载');
            }
            
            // 测试按钮
            log('测试按钮布局...');
            const buttons = document.querySelectorAll('.action-btn, .btn-sm');
            log(`发现 ${buttons.length} 个按钮`);
            
            // 测试状态标签
            log('测试状态标签...');
            const badges = document.querySelectorAll('.status-badge');
            log(`发现 ${badges.length} 个状态标签`);
            
            // 测试响应式
            log('测试响应式设计...');
            const viewportWidth = window.innerWidth;
            log(`当前视口宽度: ${viewportWidth}px`);
            
            if (viewportWidth < 768) {
                log('检测到移动设备视口');
            } else if (viewportWidth < 1024) {
                log('检测到平板设备视口');
            } else {
                log('检测到桌面设备视口');
            }
            
            log('测试完成！');
        }
        
        function reloadImages() {
            log('重新处理所有图片...');
            if (window.ImageHandler) {
                // 重置所有图片的处理状态
                document.querySelectorAll('img').forEach(img => {
                    delete img.dataset.processed;
                });
                window.ImageHandler.processImages();
                log('图片重新处理完成');
            } else {
                log('错误: 图片处理器不可用');
            }
        }
        
        // 页面加载完成后的初始日志
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            log('CSS样式文件已加载');
            log('图片错误处理脚本已初始化');
            log('准备就绪，可以开始测试');
        });
        
        // 窗口大小改变时的日志
        window.addEventListener('resize', function() {
            const width = window.innerWidth;
            log(`窗口大小已改变: ${width}px`);
        });
    </script>
</body>
</html> 