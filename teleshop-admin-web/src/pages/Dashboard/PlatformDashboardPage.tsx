import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Button,
  Space,
  Select,
  DatePicker,
  Tabs,
  Alert,
  Typography,
  Spin,
  Badge,
  Timeline,
  List,
  Avatar,
  Tooltip,
  Divider
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  UserOutlined,
  ShopOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  TruckOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  FireOutlined,
  RiseOutlined,
  FallOutlined,
  EyeOutlined,
  BellOutlined,
  SafetyOutlined,
  CustomerServiceOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { usePlatformDashboard } from '../../hooks/usePlatformDashboard';
import { RealTimeChart } from './components/RealTimeChart';
import { TrendChart } from './components/TrendChart';
import { GeographicChart } from './components/GeographicChart';
import { RiskAlertPanel } from './components/RiskAlertPanel';

const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Title, Text } = Typography;
const { Option } = Select;

const PlatformDashboardPage: React.FC = () => {
  const {
    dashboardData,
    realTimeMetrics,
    coreKPIs,
    businessTrends,
    riskAlerts,
    recentActivities,
    loading,
    timeRange,
    setTimeRange,
    refreshData
  } = usePlatformDashboard();

  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    refreshData();
    // 设置定时刷新
    const interval = setInterval(refreshData, 30000); // 30秒刷新一次
    return () => clearInterval(interval);
  }, []);

  const renderOverviewTab = () => (
    <div>
      {/* 核心指标卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={dashboardData?.totalUsers || 0}
              precision={0}
              valueStyle={{ color: '#3f8600' }}
              prefix={<UserOutlined />}
              suffix={
                <span style={{ fontSize: 12 }}>
                  <ArrowUpOutlined style={{ color: '#3f8600' }} />
                  {dashboardData?.userGrowthRate || 0}%
                </span>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃商家"
              value={dashboardData?.activeMerchants || 0}
              precision={0}
              valueStyle={{ color: '#1890ff' }}
              prefix={<ShopOutlined />}
              suffix={
                <span style={{ fontSize: 12 }}>
                  <ArrowUpOutlined style={{ color: '#1890ff' }} />
                  {dashboardData?.merchantGrowthRate || 0}%
                </span>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日交易额"
              value={dashboardData?.todayRevenue || 0}
              precision={2}
              valueStyle={{ color: '#cf1322' }}
              prefix={<DollarOutlined />}
              suffix="万元"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日订单"
              value={dashboardData?.todayOrders || 0}
              precision={0}
              valueStyle={{ color: '#722ed1' }}
              prefix={<ShoppingCartOutlined />}
              suffix={
                <span style={{ fontSize: 12 }}>
                  <ArrowUpOutlined style={{ color: '#722ed1' }} />
                  {dashboardData?.orderGrowthRate || 0}%
                </span>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 实时监控 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="实时交易监控" extra={<Badge status="processing" text="实时更新" />}>
            <RealTimeChart data={realTimeMetrics?.transactionData} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="用户活跃度" extra={<Badge status="processing" text="实时更新" />}>
            <RealTimeChart data={realTimeMetrics?.userActivityData} />
          </Card>
        </Col>
      </Row>

      {/* 业务趋势 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={16}>
          <Card 
            title="业务趋势分析" 
            extra={
              <Space>
                <Select defaultValue="revenue" style={{ width: 120 }}>
                  <Option value="revenue">收入</Option>
                  <Option value="orders">订单</Option>
                  <Option value="users">用户</Option>
                </Select>
                <RangePicker size="small" />
              </Space>
            }
          >
            <TrendChart data={businessTrends} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="地域分布" style={{ height: 400 }}>
            <GeographicChart data={dashboardData?.geographicData} />
          </Card>
        </Col>
      </Row>

      {/* 风险预警和最近活动 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card 
            title={
              <Space>
                <WarningOutlined style={{ color: '#faad14' }} />
                风险预警
                <Badge count={riskAlerts?.length || 0} />
              </Space>
            }
          >
            <RiskAlertPanel alerts={riskAlerts} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="最近活动">
            <Timeline>
              {recentActivities?.map((activity: any, index: number) => (
                <Timeline.Item
                  key={index}
                  dot={getActivityIcon(activity.type)}
                  color={getActivityColor(activity.type)}
                >
                  <div>
                    <Text strong>{activity.title}</Text>
                    <div style={{ color: '#666', fontSize: 12 }}>
                      {activity.description}
                    </div>
                    <div style={{ color: '#999', fontSize: 11 }}>
                      {activity.timestamp}
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        </Col>
      </Row>
    </div>
  );

  const renderBusinessTab = () => (
    <div>
      {/* 业务核心指标 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="GMV (总交易额)"
              value={coreKPIs?.gmv || 0}
              precision={2}
              valueStyle={{ color: '#cf1322' }}
              prefix="¥"
              suffix="亿"
            />
            <Progress 
              percent={coreKPIs?.gmvGrowthRate || 0} 
              size="small" 
              status={coreKPIs?.gmvGrowthRate > 0 ? 'active' : 'exception'}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="客单价"
              value={coreKPIs?.averageOrderValue || 0}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
              prefix="¥"
            />
            <Progress 
              percent={coreKPIs?.aovGrowthRate || 0} 
              size="small" 
              status={coreKPIs?.aovGrowthRate > 0 ? 'active' : 'exception'}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="转化率"
              value={coreKPIs?.conversionRate || 0}
              precision={2}
              valueStyle={{ color: '#52c41a' }}
              suffix="%"
            />
            <Progress 
              percent={coreKPIs?.conversionGrowthRate || 0} 
              size="small" 
              status={coreKPIs?.conversionGrowthRate > 0 ? 'active' : 'exception'}
            />
          </Card>
        </Col>
      </Row>

      {/* 分类业务数据 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="热门品类排行">
            <Table
              dataSource={dashboardData?.topCategories}
              columns={[
                {
                  title: '品类',
                  dataIndex: 'categoryName',
                  key: 'categoryName',
                },
                {
                  title: '销售额',
                  dataIndex: 'revenue',
                  key: 'revenue',
                  render: (value) => `¥${value?.toFixed(2)}万`,
                },
                {
                  title: '增长率',
                  dataIndex: 'growthRate',
                  key: 'growthRate',
                  render: (value) => (
                    <span style={{ color: value > 0 ? '#52c41a' : '#ff4d4f' }}>
                      {value > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                      {Math.abs(value)}%
                    </span>
                  ),
                },
              ]}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="商家表现排行">
            <Table
              dataSource={dashboardData?.topMerchants}
              columns={[
                {
                  title: '商家',
                  dataIndex: 'merchantName',
                  key: 'merchantName',
                },
                {
                  title: '销售额',
                  dataIndex: 'revenue',
                  key: 'revenue',
                  render: (value) => `¥${value?.toFixed(2)}万`,
                },
                {
                  title: '等级',
                  dataIndex: 'level',
                  key: 'level',
                  render: (level) => (
                    <Tag color={getMerchantLevelColor(level)}>
                      {getMerchantLevelText(level)}
                    </Tag>
                  ),
                },
              ]}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );

  const renderOperationTab = () => (
    <div>
      {/* 运营指标 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={dashboardData?.activeUsers || 0}
              valueStyle={{ color: '#1890ff' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="新增用户"
              value={dashboardData?.newUsers || 0}
              valueStyle={{ color: '#52c41a' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="用户留存率"
              value={dashboardData?.userRetentionRate || 0}
              precision={2}
              valueStyle={{ color: '#722ed1' }}
              suffix="%"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均会话时长"
              value={dashboardData?.averageSessionDuration || 0}
              precision={1}
              valueStyle={{ color: '#fa8c16' }}
              suffix="分钟"
            />
          </Card>
        </Col>
      </Row>

      {/* 内容运营 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card title="内容审核">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>待审核</span>
                <Badge count={dashboardData?.pendingReviews || 0} />
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>今日审核</span>
                <span>{dashboardData?.todayReviews || 0}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>审核通过率</span>
                <span>{dashboardData?.reviewPassRate || 0}%</span>
              </div>
            </Space>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="活动运营">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>进行中活动</span>
                <Badge count={dashboardData?.runningActivities || 0} status="processing" />
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>活动参与人数</span>
                <span>{dashboardData?.activityParticipants || 0}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>活动转化率</span>
                <span>{dashboardData?.activityConversionRate || 0}%</span>
              </div>
            </Space>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="客服服务">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>待处理工单</span>
                <Badge count={dashboardData?.pendingTickets || 0} />
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>平均响应时间</span>
                <span>{dashboardData?.averageResponseTime || 0}分钟</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>客户满意度</span>
                <span>{dashboardData?.customerSatisfaction || 0}%</span>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );

  const getActivityIcon = (type: string) => {
    const icons: Record<string, React.ReactNode> = {
      'order': <ShoppingCartOutlined />,
      'user': <UserOutlined />,
      'merchant': <ShopOutlined />,
      'system': <WarningOutlined />,
      'payment': <DollarOutlined />,
    };
    return icons[type] || <CheckCircleOutlined />;
  };

  const getActivityColor = (type: string) => {
    const colors: Record<string, string> = {
      'order': 'blue',
      'user': 'green',
      'merchant': 'orange',
      'system': 'red',
      'payment': 'purple',
    };
    return colors[type] || 'gray';
  };

  const getMerchantLevelColor = (level: string) => {
    const colors: Record<string, string> = {
      'DIAMOND': 'purple',
      'GOLD': 'gold',
      'SILVER': 'default',
      'BRONZE': 'orange',
    };
    return colors[level] || 'default';
  };

  const getMerchantLevelText = (level: string) => {
    const texts: Record<string, string> = {
      'DIAMOND': '钻石',
      'GOLD': '黄金',
      'SILVER': '白银',
      'BRONZE': '青铜',
    };
    return texts[level] || level;
  };

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>
          <DashboardOutlined /> 平台运营大屏
        </Title>
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 120 }}
          >
            <Option value="today">今日</Option>
            <Option value="week">本周</Option>
            <Option value="month">本月</Option>
            <Option value="quarter">本季度</Option>
          </Select>
          <Button onClick={refreshData} loading={loading}>
            刷新数据
          </Button>
        </Space>
      </div>

      {/* 系统状态提醒 */}
      {riskAlerts && riskAlerts.length > 0 && (
        <Alert
          message="系统风险提醒"
          description={`检测到 ${riskAlerts.length} 个风险项，请及时处理`}
          type="warning"
          showIcon
          closable
          style={{ marginBottom: 24 }}
          action={
            <Button size="small" type="primary" ghost>
              查看详情
            </Button>
          }
        />
      )}

      <Spin spinning={loading}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <BarChartOutlined />
                业务概览
              </span>
            } 
            key="overview"
          >
            {renderOverviewTab()}
          </TabPane>
          <TabPane 
            tab={
              <span>
                <LineChartOutlined />
                业务分析
              </span>
            } 
            key="business"
          >
            {renderBusinessTab()}
          </TabPane>
          <TabPane 
            tab={
              <span>
                <PieChartOutlined />
                运营分析
              </span>
            } 
            key="operation"
          >
            {renderOperationTab()}
          </TabPane>
          <TabPane 
            tab={
              <span>
                <SafetyOutlined />
                风控监控
              </span>
            } 
            key="risk"
          >
            {/* 风控监控内容 */}
          </TabPane>
          <TabPane 
            tab={
              <span>
                <CustomerServiceOutlined />
                服务质量
              </span>
            } 
            key="service"
          >
            {/* 服务质量内容 */}
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default PlatformDashboardPage;
