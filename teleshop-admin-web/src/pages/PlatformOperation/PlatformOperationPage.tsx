import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  InputNumber,
  Switch,
  message,
  Tabs,
  Tag,
  Tooltip,
  Dropdown,
  Progress,
  Statistic,
  Row,
  Col,
  Badge,
  Drawer,
  Radio,
  Upload,
  Image,
  Timeline,
  Steps,
  Alert,
  Typography,
  Empty,
  Spin,
  Popconfirm,
  Rate,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  UploadOutlined,
  DownloadOutlined,
  BarChartOutlined,
  SettingOutlined,
  BellOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  FireOutlined,
  TrophyOutlined,
  StarOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  SortAscendingOutlined,
  PictureOutlined,
  LinkOutlined,
  TagOutlined,
  UserOutlined,
  CalendarOutlined,
  Thunder<PERSON>Outlined,
  CrownOutlined,
  GiftOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { usePlatformOperation } from '../../hooks/usePlatformOperation';
import { ActivityModal } from './components/ActivityModal';
import { BannerModal } from './components/BannerModal';
import { RecommendationModal } from './components/RecommendationModal';
import { SearchKeywordModal } from './components/SearchKeywordModal';
import { ContentReviewModal } from './components/ContentReviewModal';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Text, Title } = Typography;
const { Dragger } = Upload;

const PlatformOperationPage: React.FC = () => {
  const {
    activities,
    banners,
    recommendations,
    searchKeywords,
    contentReviews,
    operationOverview,
    loading,
    pagination,
    filters,
    selectedRowKeys,
    fetchActivities,
    fetchBanners,
    fetchRecommendations,
    fetchSearchKeywords,
    fetchContentReviews,
    fetchOperationOverview,
    createActivity,
    updateActivity,
    publishActivity,
    stopActivity,
    createBanner,
    updateBanner,
    deleteBanner,
    reorderBanners,
    createRecommendation,
    updateRecommendation,
    createSearchKeyword,
    updateSearchKeyword,
    reviewContent,
    batchReviewContent,
    setFilters,
    setSelectedRowKeys
  } = usePlatformOperation();

  const [activeTab, setActiveTab] = useState('overview');
  const [activityModalVisible, setActivityModalVisible] = useState(false);
  const [bannerModalVisible, setBannerModalVisible] = useState(false);
  const [recommendationModalVisible, setRecommendationModalVisible] = useState(false);
  const [keywordModalVisible, setKeywordModalVisible] = useState(false);
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    fetchOperationOverview();
  }, []);

  useEffect(() => {
    switch (activeTab) {
      case 'activities':
        fetchActivities();
        break;
      case 'banners':
        fetchBanners();
        break;
      case 'recommendations':
        fetchRecommendations();
        break;
      case 'keywords':
        fetchSearchKeywords();
        break;
      case 'content-review':
        fetchContentReviews();
        break;
    }
  }, [activeTab]);

  const activityColumns: ColumnsType<any> = [
    {
      title: '活动信息',
      key: 'activityInfo',
      width: 300,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img
            src={record.coverImage}
            alt={record.activityName}
            style={{ width: 60, height: 60, objectFit: 'cover', marginRight: 12, borderRadius: 4 }}
          />
          <div>
            <div style={{ fontWeight: 500, marginBottom: 4 }}>{record.activityName}</div>
            <div style={{ color: '#666', fontSize: 12 }}>
              <Tag color={getActivityTypeColor(record.activityType)}>
                {getActivityTypeText(record.activityType)}
              </Tag>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '活动时间',
      key: 'activityTime',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: 12 }}>
            开始：{new Date(record.startTime).toLocaleString()}
          </div>
          <div style={{ fontSize: 12 }}>
            结束：{new Date(record.endTime).toLocaleString()}
          </div>
        </div>
      ),
    },
    {
      title: '参与情况',
      key: 'participation',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.statistics?.totalParticipants || 0}人</div>
          <div style={{ fontSize: 12, color: '#666' }}>
            订单：{record.statistics?.totalOrders || 0}
          </div>
          <div style={{ fontSize: 12, color: '#666' }}>
            收入：¥{record.statistics?.totalRevenue?.toFixed(2) || 0}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getActivityStatusColor(status)}>
          {getActivityStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewActivity(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditActivity(record)}
              disabled={record.status === 'RUNNING'}
            />
          </Tooltip>
          <Tooltip title="数据分析">
            <Button
              type="text"
              icon={<BarChartOutlined />}
              onClick={() => handleViewActivityAnalysis(record)}
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: getActivityActionItems(record),
            }}
          >
            <Button type="text" icon={<SettingOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  const bannerColumns: ColumnsType<any> = [
    {
      title: '轮播图',
      key: 'bannerImage',
      width: 200,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Image
            src={record.imageUrl}
            alt={record.title}
            width={80}
            height={45}
            style={{ objectFit: 'cover', borderRadius: 4 }}
          />
          <div style={{ marginLeft: 12 }}>
            <div style={{ fontWeight: 500 }}>{record.title}</div>
            <div style={{ fontSize: 12, color: '#666' }}>
              位置：{getBannerPositionText(record.position)}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '跳转设置',
      key: 'linkInfo',
      width: 150,
      render: (_, record) => (
        <div>
          <Tag color="blue">{getLinkTypeText(record.linkType)}</Tag>
          <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
            {record.linkUrl}
          </div>
        </div>
      ),
    },
    {
      title: '展示时间',
      key: 'displayTime',
      width: 180,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: 12 }}>
            开始：{new Date(record.startTime).toLocaleString()}
          </div>
          <div style={{ fontSize: 12 }}>
            结束：{new Date(record.endTime).toLocaleString()}
          </div>
        </div>
      ),
    },
    {
      title: '点击统计',
      key: 'clickStats',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.statistics?.totalClicks || 0}</div>
          <div style={{ fontSize: 12, color: '#666' }}>
            今日：{record.statistics?.todayClicks || 0}
          </div>
          <div style={{ fontSize: 12, color: '#666' }}>
            点击率：{(record.statistics?.clickRate * 100)?.toFixed(1) || 0}%
          </div>
        </div>
      ),
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
      sorter: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>
          {status === 'ACTIVE' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditBanner(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个轮播图吗？"
              onConfirm={() => handleDeleteBanner(record)}
            >
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const getActivityTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'PROMOTION': 'red',
      'FESTIVAL': 'orange',
      'BRAND': 'blue',
      'CATEGORY': 'green',
    };
    return colors[type] || 'default';
  };

  const getActivityTypeText = (type: string) => {
    const texts: Record<string, string> = {
      'PROMOTION': '促销活动',
      'FESTIVAL': '节日活动',
      'BRAND': '品牌活动',
      'CATEGORY': '品类活动',
    };
    return texts[type] || type;
  };

  const getActivityStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'DRAFT': 'default',
      'PUBLISHED': 'blue',
      'RUNNING': 'green',
      'ENDED': 'orange',
      'CANCELLED': 'red',
    };
    return colors[status] || 'default';
  };

  const getActivityStatusText = (status: string) => {
    const texts: Record<string, string> = {
      'DRAFT': '草稿',
      'PUBLISHED': '已发布',
      'RUNNING': '进行中',
      'ENDED': '已结束',
      'CANCELLED': '已取消',
    };
    return texts[status] || status;
  };

  const getBannerPositionText = (position: string) => {
    const texts: Record<string, string> = {
      'HOME_BANNER': '首页轮播',
      'CATEGORY_BANNER': '分类页轮播',
    };
    return texts[position] || position;
  };

  const getLinkTypeText = (type: string) => {
    const texts: Record<string, string> = {
      'PRODUCT': '商品',
      'CATEGORY': '分类',
      'ACTIVITY': '活动',
      'EXTERNAL': '外部链接',
    };
    return texts[type] || type;
  };

  const getActivityActionItems = (record: any) => {
    const items = [];

    if (record.status === 'DRAFT') {
      items.push({
        key: 'publish',
        label: '发布',
        icon: <PlayCircleOutlined />,
        onClick: () => handlePublishActivity(record),
      });
    }

    if (record.status === 'RUNNING') {
      items.push({
        key: 'stop',
        label: '停止',
        icon: <StopOutlined />,
        onClick: () => handleStopActivity(record),
      });
    }

    items.push(
      {
        key: 'copy',
        label: '复制',
        icon: <PlusOutlined />,
        onClick: () => handleCopyActivity(record),
      },
      {
        type: 'divider',
      },
      {
        key: 'delete',
        label: '删除',
        icon: <DeleteOutlined />,
        danger: true,
        onClick: () => handleDeleteActivity(record),
        disabled: record.status === 'RUNNING',
      }
    );

    return items;
  };

  const handleViewActivity = (activity: any) => {
    // 查看活动详情
  };

  const handleEditActivity = (activity: any) => {
    setSelectedItem(activity);
    setModalMode('edit');
    setActivityModalVisible(true);
  };

  const handleViewActivityAnalysis = (activity: any) => {
    // 查看活动数据分析
  };

  const handlePublishActivity = (activity: any) => {
    Modal.confirm({
      title: '发布活动',
      content: '确定要发布这个活动吗？发布后活动将开始生效。',
      onOk: () => {
        publishActivity(activity.activityId, {});
      },
    });
  };

  const handleStopActivity = (activity: any) => {
    Modal.confirm({
      title: '停止活动',
      content: '确定要停止这个活动吗？停止后活动将立即结束。',
      onOk: () => {
        stopActivity(activity.activityId, '手动停止');
      },
    });
  };

  const handleCopyActivity = (activity: any) => {
    setSelectedItem({ ...activity, activityName: `${activity.activityName} - 副本` });
    setModalMode('create');
    setActivityModalVisible(true);
  };

  const handleDeleteActivity = (activity: any) => {
    // 删除活动逻辑
  };

  const handleEditBanner = (banner: any) => {
    setSelectedItem(banner);
    setModalMode('edit');
    setBannerModalVisible(true);
  };

  const handleDeleteBanner = (banner: any) => {
    deleteBanner(banner.bannerId);
  };

  const renderOverviewTab = () => (
    <div>
      {/* 运营概览统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中活动"
              value={operationOverview?.runningActivities || 0}
              prefix={<FireOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="轮播图数量"
              value={operationOverview?.activeBanners || 0}
              prefix={<PictureOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待审核内容"
              value={operationOverview?.pendingReviews || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="热门搜索词"
              value={operationOverview?.hotKeywords || 0}
              prefix={<SearchOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedItem(null);
                setActivityModalVisible(true);
              }}
            >
              <GiftOutlined style={{ fontSize: 32, color: '#f5222d', marginBottom: 8 }} />
              <div>创建活动</div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedItem(null);
                setBannerModalVisible(true);
              }}
            >
              <PictureOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 8 }} />
              <div>添加轮播图</div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedItem(null);
                setRecommendationModalVisible(true);
              }}
            >
              <StarOutlined style={{ fontSize: 32, color: '#faad14', marginBottom: 8 }} />
              <div>设置推荐位</div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setActiveTab('content-review');
              }}
            >
              <CheckCircleOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 8 }} />
              <div>内容审核</div>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 运营数据趋势 */}
      <Card title="运营数据趋势">
        <Row gutter={16}>
          <Col span={12}>
            <Card size="small" title="活动效果">
              {/* 活动效果图表 */}
              <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                活动效果趋势图
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" title="内容审核">
              {/* 内容审核图表 */}
              <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                内容审核统计图
              </div>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );

  const renderActivitiesTab = () => (
    <div>
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setModalMode('create');
                  setSelectedItem(null);
                  setActivityModalVisible(true);
                }}
              >
                创建活动
              </Button>
              <Button icon={<BarChartOutlined />}>
                活动分析
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索活动名称"
                style={{ width: 250 }}
                onSearch={(value) => setFilters({ ...filters, keyword: value })}
              />
              <Select
                placeholder="活动类型"
                style={{ width: 120 }}
                allowClear
                onChange={(value) => setFilters({ ...filters, activityType: value })}
              >
                <Option value="PROMOTION">促销活动</Option>
                <Option value="FESTIVAL">节日活动</Option>
                <Option value="BRAND">品牌活动</Option>
                <Option value="CATEGORY">品类活动</Option>
              </Select>
              <Select
                placeholder="状态筛选"
                style={{ width: 120 }}
                allowClear
                onChange={(value) => setFilters({ ...filters, status: value })}
              >
                <Option value="DRAFT">草稿</Option>
                <Option value="PUBLISHED">已发布</Option>
                <Option value="RUNNING">进行中</Option>
                <Option value="ENDED">已结束</Option>
                <Option value="CANCELLED">已取消</Option>
              </Select>
              <Button icon={<ReloadOutlined />} onClick={() => fetchActivities()} />
            </Space>
          </Col>
        </Row>
      </Card>

      <Card>
        <Table
          columns={activityColumns}
          dataSource={activities}
          loading={loading}
          pagination={pagination}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          scroll={{ x: 1200 }}
          rowKey="activityId"
        />
      </Card>
    </div>
  );

  const renderBannersTab = () => (
    <div>
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setModalMode('create');
                  setSelectedItem(null);
                  setBannerModalVisible(true);
                }}
              >
                添加轮播图
              </Button>
              <Button icon={<SortAscendingOutlined />}>
                排序管理
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Select
                placeholder="显示位置"
                style={{ width: 150 }}
                allowClear
                onChange={(value) => setFilters({ ...filters, position: value })}
              >
                <Option value="HOME_BANNER">首页轮播</Option>
                <Option value="CATEGORY_BANNER">分类页轮播</Option>
              </Select>
              <Select
                placeholder="状态筛选"
                style={{ width: 120 }}
                allowClear
                onChange={(value) => setFilters({ ...filters, status: value })}
              >
                <Option value="ACTIVE">启用</Option>
                <Option value="INACTIVE">禁用</Option>
              </Select>
              <Button icon={<ReloadOutlined />} onClick={() => fetchBanners()} />
            </Space>
          </Col>
        </Row>
      </Card>

      <Card>
        <Table
          columns={bannerColumns}
          dataSource={banners}
          loading={loading}
          pagination={pagination}
          scroll={{ x: 1000 }}
          rowKey="bannerId"
        />
      </Card>
    </div>
  );

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>平台运营管理</Title>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="运营概览" key="overview">
          {renderOverviewTab()}
        </TabPane>
        <TabPane tab="平台活动" key="activities">
          {renderActivitiesTab()}
        </TabPane>
        <TabPane tab="轮播图管理" key="banners">
          {renderBannersTab()}
        </TabPane>
        <TabPane tab="推荐位管理" key="recommendations">
          {/* 推荐位管理内容 */}
        </TabPane>
        <TabPane tab="搜索热词" key="keywords">
          {/* 搜索热词内容 */}
        </TabPane>
        <TabPane
          tab={
            <Badge count={operationOverview?.pendingReviews || 0} size="small">
              内容审核
            </Badge>
          }
          key="content-review"
        >
          {/* 内容审核内容 */}
        </TabPane>
      </Tabs>

      {/* 活动管理弹窗 */}
      <ActivityModal
        visible={activityModalVisible}
        mode={modalMode}
        activity={selectedItem}
        onCancel={() => setActivityModalVisible(false)}
        onSuccess={() => {
          setActivityModalVisible(false);
          fetchActivities();
        }}
      />

      {/* 轮播图管理弹窗 */}
      <BannerModal
        visible={bannerModalVisible}
        mode={modalMode}
        banner={selectedItem}
        onCancel={() => setBannerModalVisible(false)}
        onSuccess={() => {
          setBannerModalVisible(false);
          fetchBanners();
        }}
      />

      {/* 推荐位管理弹窗 */}
      <RecommendationModal
        visible={recommendationModalVisible}
        mode={modalMode}
        recommendation={selectedItem}
        onCancel={() => setRecommendationModalVisible(false)}
        onSuccess={() => {
          setRecommendationModalVisible(false);
          fetchRecommendations();
        }}
      />

      {/* 搜索热词弹窗 */}
      <SearchKeywordModal
        visible={keywordModalVisible}
        mode={modalMode}
        keyword={selectedItem}
        onCancel={() => setKeywordModalVisible(false)}
        onSuccess={() => {
          setKeywordModalVisible(false);
          fetchSearchKeywords();
        }}
      />

      {/* 内容审核弹窗 */}
      <ContentReviewModal
        visible={reviewModalVisible}
        review={selectedItem}
        onCancel={() => setReviewModalVisible(false)}
        onSuccess={() => {
          setReviewModalVisible(false);
          fetchContentReviews();
        }}
      />
    </div>
  );
};

export default PlatformOperationPage;