document.addEventListener('DOMContentLoaded', function () {
    const api = new ApiService();

    let salesChart;
    const chartCanvas = document.getElementById('salesTrendChart');
    const chartToggleButtons = document.querySelectorAll('.chart-toggle .toggle-btn');
    
    const kpiContainer = document.getElementById('kpiCards');
    const salesTableBody = document.getElementById('salesTableBody');
    const productTableBody = document.getElementById('productTableBody');
    
    const loadingOverlays = document.querySelectorAll('.loading-overlay');

    const showLoading = (show = true) => {
        loadingOverlays.forEach(overlay => {
            overlay.classList.toggle('show', show);
        });
    };

    const formatCurrency = (value) => `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    
    const renderKpiCards = (data) => {
        kpiContainer.innerHTML = `
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="kpi-card kpi-revenue">
                    <div class="kpi-icon"><i class="fas fa-dollar-sign"></i></div>
                    <div class="kpi-value">${formatCurrency(data.totalRevenue)}</div>
                    <div class="kpi-label">销售额</div>
                    <div class="kpi-change ${data.revenueChange >= 0 ? 'positive' : 'negative'}">
                        <i class="fas fa-arrow-${data.revenueChange >= 0 ? 'up' : 'down'}"></i>
                        ${data.revenueChange.toFixed(1)}%
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="kpi-card kpi-orders">
                    <div class="kpi-icon"><i class="fas fa-shopping-cart"></i></div>
                    <div class="kpi-value">${data.totalOrders.toLocaleString()}</div>
                    <div class="kpi-label">订单数量</div>
                    <div class="kpi-change ${data.ordersChange >= 0 ? 'positive' : 'negative'}">
                        <i class="fas fa-arrow-${data.ordersChange >= 0 ? 'up' : 'down'}"></i>
                        ${data.ordersChange.toFixed(1)}%
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="kpi-card kpi-customers">
                    <div class="kpi-icon"><i class="fas fa-users"></i></div>
                    <div class="kpi-value">${data.totalCustomers.toLocaleString()}</div>
                    <div class="kpi-label">客户数量</div>
                    <div class="kpi-change ${data.customersChange >= 0 ? 'positive' : 'negative'}">
                        <i class="fas fa-arrow-${data.customersChange >= 0 ? 'up' : 'down'}"></i>
                        ${data.customersChange.toFixed(1)}%
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="kpi-card kpi-conversion">
                    <div class="kpi-icon"><i class="fas fa-bullseye"></i></div>
                    <div class="kpi-value">${(data.conversionRate * 100).toFixed(2)}%</div>
                    <div class="kpi-label">转化率</div>
                     <div class="kpi-change ${data.conversionChange >= 0 ? 'positive' : 'negative'}">
                        <i class="fas fa-arrow-${data.conversionChange >= 0 ? 'up' : 'down'}"></i>
                        ${data.conversionChange.toFixed(1)}%
                    </div>
                </div>
            </div>
        `;
    };

    const renderChart = (data, type = 'revenue') => {
        const labels = data.trend.map(d => d.date);
        const chartData = data.trend.map(d => d[type]);
        const label = type === 'revenue' ? '销售额' : '订单数';

        if (salesChart) {
            salesChart.destroy();
        }

        salesChart = new Chart(chartCanvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: label,
                    data: chartData,
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#0d6efd',
                    pointBorderColor: '#fff',
                    pointHoverRadius: 7,
                    pointHoverBackgroundColor: '#0d6efd',
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return type === 'revenue' ? `¥${value/1000}k` : value;
                            }
                        }
                    },
                    x: {
                        grid: { display: false }
                    }
                },
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += type === 'revenue' ? formatCurrency(context.parsed.y) : context.parsed.y;
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    };
    
    const renderTables = (data) => {
        salesTableBody.innerHTML = data.salesByDay.map(d => `
            <tr>
                <td>${d.date}</td>
                <td>${formatCurrency(d.revenue)}</td>
                <td>${d.orders.toLocaleString()}</td>
                <td>${formatCurrency(d.avgOrderValue)}</td>
                <td class="${d.change >= 0 ? 'trend-up' : 'trend-down'}">
                    <i class="fas fa-arrow-${d.change >= 0 ? 'up' : 'down'}"></i>
                    ${d.change.toFixed(1)}%
                </td>
            </tr>
        `).join('');

        productTableBody.innerHTML = data.topProducts.map((p, index) => `
            <tr>
                <td>${index + 1}</td>
                <td>${p.name}</td>
                <td>${p.unitsSold.toLocaleString()}</td>
                <td>${formatCurrency(p.revenue)}</td>
            </tr>
        `).join('');
    };
    
    const fetchData = async () => {
        showLoading(true);
        try {
            const period = document.getElementById('periodSelect').value;
            const data = await api.getSalesReport(period);
            renderKpiCards(data.kpi);
            renderChart(data.chart, 'revenue');
            renderTables(data.tables);
        } catch (error) {
            console.error('Error fetching sales report data:', error);
            // You could show an error message to the user here
        } finally {
            setTimeout(() => showLoading(false), 500); // Simulate network delay
        }
    };

    chartToggleButtons.forEach(button => {
        button.addEventListener('click', async () => {
            chartToggleButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            const type = button.dataset.type;
            const period = document.getElementById('periodSelect').value;
            const data = await api.getSalesReport(period);
            renderChart(data.chart, type);
        });
    });

    document.getElementById('refreshDataBtn').addEventListener('click', fetchData);
    document.getElementById('periodSelect').addEventListener('change', fetchData);
    
    const init = () => {
        const periodSelect = document.getElementById('periodSelect');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        
        const today = new Date();
        endDateInput.value = today.toISOString().split('T')[0];
        const thirtyDaysAgo = new Date(today.setDate(today.getDate() - 30));
        startDateInput.value = thirtyDaysAgo.toISOString().split('T')[0];

        periodSelect.addEventListener('change', () => {
            const isCustom = periodSelect.value === 'custom';
            startDateInput.style.display = isCustom ? 'inline-block' : 'none';
            document.getElementById('dateSeparator').style.display = isCustom ? 'inline-block' : 'none';
            endDateInput.style.display = isCustom ? 'inline-block' : 'none';
        });

        fetchData();
    };

    init();
}); 