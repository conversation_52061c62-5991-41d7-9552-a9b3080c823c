<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流量分析 - TeleShop商家后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../styles/supplier-styles.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky">
                    <div class="sidebar-header">
                        <h4><i class="fas fa-store"></i> 商家后台</h4>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../index.html">
                                <i class="fas fa-home"></i> 首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="sales-report.html">
                                <i class="fas fa-chart-line"></i> 销售报表
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="traffic-analysis.html">
                                <i class="fas fa-chart-bar"></i> 流量分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="product-analysis.html">
                                <i class="fas fa-chart-pie"></i> 商品分析
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-chart-bar"></i> 流量分析</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <select class="form-select" id="dateRange" onchange="updateDateRange()">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                                <option value="custom">自定义时间</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-secondary" onclick="exportTrafficData()">
                                <i class="fas fa-download"></i> 导出报表
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 核心指标卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-muted">总访客数</h6>
                                        <h3 class="text-primary">12,456</h3>
                                        <small class="text-success">
                                            <i class="fas fa-arrow-up"></i> +15.3%
                                        </small>
                                    </div>
                                    <div class="text-primary">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-muted">页面浏览量</h6>
                                        <h3 class="text-success">28,934</h3>
                                        <small class="text-success">
                                            <i class="fas fa-arrow-up"></i> +8.7%
                                        </small>
                                    </div>
                                    <div class="text-success">
                                        <i class="fas fa-eye fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-muted">平均停留时间</h6>
                                        <h3 class="text-info">3分26秒</h3>
                                        <small class="text-danger">
                                            <i class="fas fa-arrow-down"></i> -2.1%
                                        </small>
                                    </div>
                                    <div class="text-info">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-muted">跳出率</h6>
                                        <h3 class="text-warning">32.5%</h3>
                                        <small class="text-success">
                                            <i class="fas fa-arrow-down"></i> -3.2%
                                        </small>
                                    </div>
                                    <div class="text-warning">
                                        <i class="fas fa-sign-out-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 流量趋势图表 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">流量趋势</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="trafficTrendChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 流量来源和设备分析 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">流量来源</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="trafficSourceChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">设备类型</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="deviceTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 地域分布和热门页面 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">地域分布 TOP10</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>地区</th>
                                                <th>访客数</th>
                                                <th>占比</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>1</td>
                                                <td>北京市</td>
                                                <td>2,456</td>
                                                <td>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar" style="width: 85%">19.7%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>2</td>
                                                <td>上海市</td>
                                                <td>2,134</td>
                                                <td>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar bg-success" style="width: 75%">17.1%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>3</td>
                                                <td>广东省</td>
                                                <td>1,876</td>
                                                <td>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar bg-info" style="width: 65%">15.1%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>4</td>
                                                <td>江苏省</td>
                                                <td>1,234</td>
                                                <td>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar bg-warning" style="width: 45%">9.9%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>5</td>
                                                <td>浙江省</td>
                                                <td>987</td>
                                                <td>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar bg-danger" style="width: 35%">7.9%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">热门页面 TOP10</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>页面</th>
                                                <th>浏览量</th>
                                                <th>占比</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>1</td>
                                                <td>店铺首页</td>
                                                <td>8,456</td>
                                                <td>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar" style="width: 90%">29.2%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>2</td>
                                                <td>商品详情页</td>
                                                <td>6,234</td>
                                                <td>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar bg-success" style="width: 70%">21.5%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>3</td>
                                                <td>商品列表页</td>
                                                <td>4,567</td>
                                                <td>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar bg-info" style="width: 50%">15.8%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>4</td>
                                                <td>购物车页</td>
                                                <td>2,345</td>
                                                <td>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar bg-warning" style="width: 30%">8.1%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>5</td>
                                                <td>结算页面</td>
                                                <td>1,876</td>
                                                <td>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar bg-danger" style="width: 25%">6.5%</div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时访客和搜索关键词 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">实时访客</h5>
                                <span class="badge bg-success">在线: 126人</span>
                            </div>
                            <div class="card-body">
                                <div class="real-time-visitors">
                                    <div class="visitor-item d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <i class="fas fa-circle text-success"></i>
                                            <span class="ms-2">北京市朝阳区</span>
                                        </div>
                                        <small class="text-muted">刚刚</small>
                                    </div>
                                    <div class="visitor-item d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <i class="fas fa-circle text-success"></i>
                                            <span class="ms-2">上海市浦东新区</span>
                                        </div>
                                        <small class="text-muted">1分钟前</small>
                                    </div>
                                    <div class="visitor-item d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <i class="fas fa-circle text-warning"></i>
                                            <span class="ms-2">广州市天河区</span>
                                        </div>
                                        <small class="text-muted">2分钟前</small>
                                    </div>
                                    <div class="visitor-item d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <i class="fas fa-circle text-success"></i>
                                            <span class="ms-2">深圳市南山区</span>
                                        </div>
                                        <small class="text-muted">3分钟前</small>
                                    </div>
                                    <div class="visitor-item d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <i class="fas fa-circle text-info"></i>
                                            <span class="ms-2">杭州市西湖区</span>
                                        </div>
                                        <small class="text-muted">5分钟前</small>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <button class="btn btn-outline-primary btn-sm" onclick="viewRealTimeDetails()">
                                        查看详细数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">热门搜索关键词</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>关键词</th>
                                                <th>搜索次数</th>
                                                <th>转化率</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>蓝牙耳机</td>
                                                <td>1,234</td>
                                                <td><span class="badge bg-success">12.5%</span></td>
                                            </tr>
                                            <tr>
                                                <td>智能手表</td>
                                                <td>987</td>
                                                <td><span class="badge bg-info">8.7%</span></td>
                                            </tr>
                                            <tr>
                                                <td>充电宝</td>
                                                <td>756</td>
                                                <td><span class="badge bg-warning">6.3%</span></td>
                                            </tr>
                                            <tr>
                                                <td>手机壳</td>
                                                <td>654</td>
                                                <td><span class="badge bg-primary">9.8%</span></td>
                                            </tr>
                                            <tr>
                                                <td>数据线</td>
                                                <td>543</td>
                                                <td><span class="badge bg-secondary">4.2%</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../scripts/supplier-dashboard.js"></script>
    <script>
        // 流量趋势图表
        function initTrafficTrendChart() {
            const ctx = document.getElementById('trafficTrendChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['12-01', '12-02', '12-03', '12-04', '12-05', '12-06', '12-07', '12-08', '12-09', '12-10'],
                    datasets: [{
                        label: '访客数',
                        data: [420, 380, 450, 520, 480, 560, 490, 630, 580, 650],
                        borderColor: '#0d6efd',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '页面浏览量',
                        data: [980, 850, 1020, 1150, 1080, 1250, 1100, 1400, 1300, 1450],
                        borderColor: '#198754',
                        backgroundColor: 'rgba(25, 135, 84, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 流量来源图表
        function initTrafficSourceChart() {
            const ctx = document.getElementById('trafficSourceChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['搜索引擎', '直接访问', '社交媒体', '外部链接', '广告推广'],
                    datasets: [{
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: [
                            '#0d6efd',
                            '#198754',
                            '#ffc107',
                            '#dc3545',
                            '#6c757d'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 设备类型图表
        function initDeviceTypeChart() {
            const ctx = document.getElementById('deviceTypeChart').getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['移动设备', '桌面设备', '平板设备'],
                    datasets: [{
                        data: [65, 28, 7],
                        backgroundColor: [
                            '#0d6efd',
                            '#198754',
                            '#ffc107'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 更新时间范围
        function updateDateRange() {
            const dateRange = document.getElementById('dateRange').value;
            console.log('更新时间范围:', dateRange);
            showToast('数据更新中...', 'info');
            
            // 这里应该重新加载数据和图表
            setTimeout(() => {
                showToast('数据已更新', 'success');
            }, 1000);
        }

        // 导出流量数据
        function exportTrafficData() {
            showToast('正在导出流量分析报表...', 'info');
            // 这里应该调用API导出数据
            setTimeout(() => {
                showToast('报表导出成功！', 'success');
            }, 2000);
        }

        // 查看实时详细数据
        function viewRealTimeDetails() {
            showToast('实时数据详情功能开发中...', 'info');
        }

        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initTrafficTrendChart();
            initTrafficSourceChart();
            initDeviceTypeChart();
            
            // 模拟实时更新访客数据
            setInterval(updateRealTimeVisitors, 30000); // 每30秒更新一次
        });

        // 更新实时访客数据
        function updateRealTimeVisitors() {
            // 这里可以添加实时更新访客数据的逻辑
            console.log('更新实时访客数据');
        }
    </script>

    <style>
        .visitor-item {
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .visitor-item:hover {
            background-color: #f8f9fa;
        }

        .progress {
            background-color: #e9ecef;
        }

        .real-time-visitors {
            max-height: 300px;
            overflow-y: auto;
        }

        .fas.fa-circle {
            font-size: 8px;
        }
    </style>
</body>
</html> 