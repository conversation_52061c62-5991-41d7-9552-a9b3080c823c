/* General Body & Layout */
body {
    background-color: #f4f7fc;
    color: #5d677a;
}

.container-fluid {
    padding: 2rem;
}

/* Header */
.main-header {
    background-color: #ffffff;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.main-header h1 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #343a40;
    margin: 0;
}

/* Stat Cards */
.stat-card {
    background-color: #ffffff;
    border-radius: 0.75rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.04);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    padding: 1.5rem;
    height: 100%;
}
.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
}
.stat-card-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.stat-card-title {
    font-size: 0.9rem;
    color: #8898aa;
    font-weight: 500;
    margin-bottom: 0.5rem;
}
.stat-card-value {
    font-size: 2rem;
    font-weight: 600;
    color: #32325d;
}
.stat-card-change {
    font-size: 0.85rem;
    font-weight: 500;
}
.stat-card-change .fa-arrow-up { color: #2dce89; }
.stat-card-change .fa-arrow-down { color: #f5365c; }
.stat-card-icon {
    font-size: 2.5rem;
    opacity: 0.3;
}
.text-primary .stat-card-icon { color: #5e72e4; }
.text-success .stat-card-icon { color: #2dce89; }
.text-info .stat-card-icon { color: #11cdef; }
.text-warning .stat-card-icon { color: #fb6340; }

/* Chart & Table Cards */
.chart-card, .table-card {
    background-color: #ffffff;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.04);
    height: 100%;
}
.chart-card-header, .table-card-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}
.chart-card-header h5, .table-card-header h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #32325d;
    margin: 0;
}

/* Table Specific */
.table-responsive {
    max-height: 350px; /* Or adjust as needed */
}
.table thead th {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    background-color: #f6f9fc;
}
.table td, .table th {
    border-top: 1px solid #e9ecef;
    vertical-align: middle;
}
.progress-bar-container .progress {
    height: 8px;
    background-color: #e9ecef;
}

/* Loader and Empty State */
.loading-overlay, .empty-state {
    display: none; /* Hidden by default */
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 10;
}
.loading-overlay.show, .empty-state.show {
    display: flex;
}
.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #5e72e4;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.empty-state .fa-sad-tear {
    font-size: 3rem;
    color: #cad1de;
    margin-bottom: 1rem;
} 