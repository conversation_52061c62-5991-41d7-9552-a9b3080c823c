class TrafficAnalytics {
    constructor(apiService) {
        this.apiService = apiService;
        this.chartInstances = {};
        this.elements = {
            statCardsContainer: document.getElementById('statCardsContainer'),
            trafficTrendChart: document.getElementById('trafficTrendChart'),
            deviceTypeChart: document.getElementById('deviceTypeChart'),
            trafficSourceTableBody: document.querySelector('#trafficSourceTable tbody'),
            dateRangeSelect: document.getElementById('dateRangeSelect'),
        };
        this.init();
    }

    init() {
        this.addEventListeners();
        this.loadData();
    }

    addEventListeners() {
        this.elements.dateRangeSelect.addEventListener('change', () => this.loadData());
    }

    toggleLoading(chartElement, show) {
        const overlay = chartElement.parentElement.querySelector('.loading-overlay');
        if (overlay) {
            overlay.classList.toggle('show', show);
        }
    }

    async loadData() {
        this.toggleLoading(this.elements.trafficTrendChart, true);
        this.toggleLoading(this.elements.deviceTypeChart, true);
        // ... toggle loading for other elements as needed

        try {
            const days = this.elements.dateRangeSelect.value;
            const data = await this.apiService.getTrafficData(days);

            this.renderStatCards(data.stats);
            this.renderTrafficTrendChart(data.trend);
            this.renderTrafficSourceTable(data.sources);
            this.renderDeviceTypeChart(data.devices);

        } catch (error) {
            console.error('Failed to load traffic data:', error);
            // Here you could show an error state
        } finally {
            this.toggleLoading(this.elements.trafficTrendChart, false);
            this.toggleLoading(this.elements.deviceTypeChart, false);
             // ... toggle loading for other elements as needed
        }
    }

    renderStatCards(stats) {
        this.elements.statCardsContainer.innerHTML = '';
        const statConfig = [
            { id: 'visitors', title: '总访客数', icon: 'fa-users', color: 'primary' },
            { id: 'pageviews', title: '页面浏览量', icon: 'fa-eye', color: 'success' },
            { id: 'avgTime', title: '平均停留时间', icon: 'fa-clock', color: 'info' },
            { id: 'bounceRate', title: '跳出率', icon: 'fa-sign-out-alt', color: 'warning' },
        ];

        statConfig.forEach(config => {
            const statData = stats[config.id];
            const changeIcon = statData.change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
            const changeColor = statData.change >= 0 ? 'text-success' : 'text-danger';
            
            const cardHtml = `
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-card-body">
                            <div>
                                <div class="stat-card-title">${config.title}</div>
                                <div class="stat-card-value">${statData.value}</div>
                                <div class="stat-card-change ${changeColor}">
                                    <i class="fas ${changeIcon}"></i> ${statData.change.toFixed(1)}%
                                </div>
                            </div>
                            <div class="text-${config.color}">
                                <i class="fas ${config.icon} stat-card-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>`;
            this.elements.statCardsContainer.innerHTML += cardHtml;
        });
    }

    renderTrafficTrendChart(trendData) {
        if (this.chartInstances.trafficTrend) {
            this.chartInstances.trafficTrend.destroy();
        }
        const ctx = this.elements.trafficTrendChart.getContext('2d');
        this.chartInstances.trafficTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.labels,
                datasets: [
                    {
                        label: '访客数 (UV)',
                        data: trendData.uv,
                        borderColor: '#5e72e4',
                        backgroundColor: 'rgba(94, 114, 228, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '浏览量 (PV)',
                        data: trendData.pv,
                        borderColor: '#2dce89',
                        backgroundColor: 'rgba(45, 206, 137, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { type: 'time', time: { unit: 'day' } },
                    y: { beginAtZero: true }
                }
            }
        });
    }

    renderTrafficSourceTable(sourceData) {
        this.elements.trafficSourceTableBody.innerHTML = '';
        const totalVisitors = sourceData.reduce((sum, s) => sum + s.uv, 0);

        sourceData.forEach(source => {
            const percentage = totalVisitors > 0 ? (source.uv / totalVisitors * 100).toFixed(1) : 0;
            const row = `
                <tr>
                    <td>${source.name}</td>
                    <td>${source.uv.toLocaleString()}</td>
                    <td>${source.pv.toLocaleString()}</td>
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="me-2">${percentage}%</span>
                            <div class="progress flex-grow-1 progress-bar-container">
                                <div class="progress-bar" role="progressbar" style="width: ${percentage}%;" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </td>
                </tr>`;
            this.elements.trafficSourceTableBody.innerHTML += row;
        });
    }

    renderDeviceTypeChart(deviceData) {
        if (this.chartInstances.deviceType) {
            this.chartInstances.deviceType.destroy();
        }
        const ctx = this.elements.deviceTypeChart.getContext('2d');
        this.chartInstances.deviceType = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: deviceData.map(d => d.name),
                datasets: [{
                    data: deviceData.map(d => d.value),
                    backgroundColor: ['#5e72e4', '#11cdef', '#fb6340'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' }
                }
            }
        });
    }
}

// Mock ApiService for demonstration
if (typeof ApiService === 'undefined') {
    class ApiService {
        _generateTrendData(days) {
            const labels = [];
            const uv = [];
            const pv = [];
            for (let i = days - 1; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                labels.push(date.toISOString().split('T')[0]);
                uv.push(Math.floor(Math.random() * (500 - 200 + 1) + 200) + (days - i) * 10);
                pv.push(Math.floor(Math.random() * (1500 - 800 + 1) + 800) + (days - i) * 30);
            }
            return { labels, uv, pv };
        }

        getTrafficData(days = 30) {
            const trend = this._generateTrendData(days);
            const totalVisitors = trend.uv.reduce((a,b) => a+b, 0);

            return Promise.resolve({
                stats: {
                    visitors: { value: totalVisitors.toLocaleString(), change: 15.3 },
                    pageviews: { value: trend.pv.reduce((a,b) => a+b, 0).toLocaleString(), change: 8.7 },
                    avgTime: { value: '3m 26s', change: -2.1 },
                    bounceRate: { value: '32.5%', change: -3.2 }
                },
                trend: trend,
                sources: [
                    { name: '直接访问', uv: Math.floor(totalVisitors * 0.45), pv: Math.floor(totalVisitors * 0.45 * 2.5) },
                    { name: '搜索引擎', uv: Math.floor(totalVisitors * 0.30), pv: Math.floor(totalVisitors * 0.30 * 3) },
                    { name: '社交媒体', uv: Math.floor(totalVisitors * 0.15), pv: Math.floor(totalVisitors * 0.15 * 2) },
                    { name: '外部链接', uv: Math.floor(totalVisitors * 0.08), pv: Math.floor(totalVisitors * 0.08 * 1.8) },
                    { name: '广告推广', uv: Math.floor(totalVisitors * 0.02), pv: Math.floor(totalVisitors * 0.02 * 1.5) },
                ].sort((a,b) => b.uv - a.uv),
                devices: [
                    { name: '移动设备', value: 65 },
                    { name: '桌面设备', value: 28 },
                    { name: '平板设备', value: 7 }
                ]
            });
        }
    }
    window.ApiService = ApiService;
}


document.addEventListener('DOMContentLoaded', () => {
    const apiService = new ApiService();
    new TrafficAnalytics(apiService);
}); 