.analytics-container {
    padding: 24px;
}

.date-selector {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.kpi-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    text-align: center;
    height: 100%;
    transition: transform 0.2s, box-shadow 0.2s;
}

.kpi-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.08);
}

.kpi-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 20px;
    color: white;
}

.kpi-revenue .kpi-icon { background: #198754; }
.kpi-orders .kpi-icon { background: #0d6efd; }
.kpi-customers .kpi-icon { background: #ffc107; }
.kpi-conversion .kpi-icon { background: #6f42c1; }

.kpi-value {
    font-size: 28px;
    font-weight: 700;
    color: #212121;
    margin-bottom: 8px;
}

.kpi-label {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 8px;
    text-transform: uppercase;
}

.kpi-change {
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.kpi-change.positive {
    color: #198754;
}

.kpi-change.negative {
    color: #dc3545;
}

.chart-section, .data-table {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: #212121;
}

.chart-toggle {
    display: flex;
    gap: 8px;
}

.toggle-btn {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
}

.toggle-btn.active {
    background: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

.chart-container {
    position: relative;
    height: 320px;
    margin-bottom: 16px;
}

.table-wrapper {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: middle;
}

th {
    background: #f9f9f9;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
    text-transform: uppercase;
}

.trend-up {
    color: #198754;
}

.trend-down {
    color: #dc3545;
}

/* Loading overlay styles */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    border-radius: 12px;
}
.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}
.loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #0d6efd;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} 