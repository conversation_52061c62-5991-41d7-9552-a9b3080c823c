<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>销售报表 - TeleShop</title>
    <link rel="stylesheet" href="../styles/supplier-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .analytics-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        .date-selector {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .kpi-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .kpi-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 20px;
            color: white;
        }

        .kpi-revenue .kpi-icon { background: #4CAF50; }
        .kpi-orders .kpi-icon { background: #2196F3; }
        .kpi-customers .kpi-icon { background: #FF9800; }
        .kpi-conversion .kpi-icon { background: #9C27B0; }

        .kpi-value {
            font-size: 28px;
            font-weight: 700;
            color: #212121;
            margin-bottom: 8px;
        }

        .kpi-label {
            color: #757575;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .kpi-change {
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .kpi-change.positive {
            color: #4CAF50;
        }

        .kpi-change.negative {
            color: #f44336;
        }

        .chart-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #212121;
        }

        .chart-toggle {
            display: flex;
            gap: 8px;
        }

        .toggle-btn {
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }

        .toggle-btn.active {
            background: #2196F3;
            color: white;
            border-color: #2196F3;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 16px;
        }

        .data-table {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .table-wrapper {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        th {
            background: #f9f9f9;
            font-weight: 600;
            color: #212121;
            font-size: 14px;
        }

        .trend-up {
            color: #4CAF50;
        }

        .trend-down {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="supplier-dashboard">
        <header class="dashboard-header">
            <div class="header-content">
                <div class="logo-section">
                    <img src="../../admin-backend/assets/images/logo.png" alt="TeleShop" class="logo">
                    <h1>销售报表</h1>
                </div>
                <div class="header-actions">
                    <a href="../index.html" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回首页
                    </a>
                </div>
            </div>
        </header>

        <div class="dashboard-main">
            <main class="main-content">
                <div class="analytics-container">
                    <!-- 日期选择器 -->
                    <div class="date-selector">
                        <label style="font-weight: 500;">时间范围：</label>
                        <select class="filter-input" onchange="changePeriod(this.value)" style="min-width: 120px;">
                            <option value="7">最近7天</option>
                            <option value="30" selected>最近30天</option>
                            <option value="90">最近90天</option>
                            <option value="custom">自定义</option>
                        </select>
                        <input type="date" class="filter-input" id="startDate" value="2024-10-01">
                        <span>至</span>
                        <input type="date" class="filter-input" id="endDate" value="2024-10-31">
                        <button class="btn btn-primary" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                        <button class="btn btn-secondary" onclick="exportReport()">
                            <i class="fas fa-download"></i> 导出报表
                        </button>
                    </div>

                    <!-- KPI指标 -->
                    <div class="kpi-grid">
                        <div class="kpi-card kpi-revenue">
                            <div class="kpi-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="kpi-value">¥156,789</div>
                            <div class="kpi-label">销售额</div>
                            <div class="kpi-change positive">
                                <i class="fas fa-arrow-up"></i>
                                +12.5%
                            </div>
                        </div>

                        <div class="kpi-card kpi-orders">
                            <div class="kpi-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="kpi-value">1,234</div>
                            <div class="kpi-label">订单数量</div>
                            <div class="kpi-change positive">
                                <i class="fas fa-arrow-up"></i>
                                +8.3%
                            </div>
                        </div>

                        <div class="kpi-card kpi-customers">
                            <div class="kpi-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="kpi-value">856</div>
                            <div class="kpi-label">客户数量</div>
                            <div class="kpi-change positive">
                                <i class="fas fa-arrow-up"></i>
                                +15.2%
                            </div>
                        </div>

                        <div class="kpi-card kpi-conversion">
                            <div class="kpi-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="kpi-value">12.8%</div>
                            <div class="kpi-label">转化率</div>
                            <div class="kpi-change negative">
                                <i class="fas fa-arrow-down"></i>
                                -2.1%
                            </div>
                        </div>
                    </div>

                    <!-- 销售趋势图表 -->
                    <div class="chart-section">
                        <div class="chart-header">
                            <h3 class="chart-title">销售趋势</h3>
                            <div class="chart-toggle">
                                <button class="toggle-btn active" onclick="switchChart('revenue')">销售额</button>
                                <button class="toggle-btn" onclick="switchChart('orders')">订单量</button>
                                <button class="toggle-btn" onclick="switchChart('customers')">客户数</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>

                    <!-- 商品销售排行 -->
                    <div class="data-table">
                        <h3 style="margin-bottom: 20px;">热销商品排行</h3>
                        <div class="table-wrapper">
                            <table>
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>商品名称</th>
                                        <th>销售额</th>
                                        <th>销量</th>
                                        <th>访客数</th>
                                        <th>转化率</th>
                                        <th>趋势</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>iPhone 15 Pro 256GB</td>
                                        <td>¥89,990</td>
                                        <td>10</td>
                                        <td>1,256</td>
                                        <td>0.8%</td>
                                        <td class="trend-up">
                                            <i class="fas fa-arrow-up"></i> +15%
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>MacBook Pro 14寸</td>
                                        <td>¥74,995</td>
                                        <td>5</td>
                                        <td>892</td>
                                        <td>0.6%</td>
                                        <td class="trend-up">
                                            <i class="fas fa-arrow-up"></i> +8%
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>AirPods Pro 第3代</td>
                                        <td>¥37,980</td>
                                        <td>20</td>
                                        <td>2,156</td>
                                        <td>0.9%</td>
                                        <td class="trend-down">
                                            <i class="fas fa-arrow-down"></i> -3%
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>4</td>
                                        <td>iPad Air 第5代</td>
                                        <td>¥29,592</td>
                                        <td>8</td>
                                        <td>1,045</td>
                                        <td>0.8%</td>
                                        <td class="trend-up">
                                            <i class="fas fa-arrow-up"></i> +12%
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>5</td>
                                        <td>Apple Watch Series 9</td>
                                        <td>¥23,940</td>
                                        <td>12</td>
                                        <td>856</td>
                                        <td>1.4%</td>
                                        <td class="trend-up">
                                            <i class="fas fa-arrow-up"></i> +25%
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        let currentChart = null;

        // 页面加载时初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initTrendChart();
        });

        // 初始化趋势图表
        function initTrendChart() {
            const ctx = document.getElementById('trendChart').getContext('2d');
            
            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['10-01', '10-08', '10-15', '10-22', '10-29'],
                    datasets: [{
                        label: '销售额',
                        data: [8500, 12000, 15600, 18900, 22000],
                        borderColor: '#4CAF50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#4CAF50',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#f0f0f0'
                            },
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: '#f0f0f0'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 切换图表类型
        function switchChart(type) {
            // 更新按钮状态
            document.querySelectorAll('.toggle-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            let data, label, color;
            
            switch(type) {
                case 'revenue':
                    data = [8500, 12000, 15600, 18900, 22000];
                    label = '销售额';
                    color = '#4CAF50';
                    break;
                case 'orders':
                    data = [45, 68, 82, 95, 108];
                    label = '订单量';
                    color = '#2196F3';
                    break;
                case 'customers':
                    data = [28, 42, 56, 63, 75];
                    label = '客户数';
                    color = '#FF9800';
                    break;
            }

            currentChart.data.datasets[0] = {
                label: label,
                data: data,
                borderColor: color,
                backgroundColor: color + '20',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: color,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            };

            currentChart.update();
        }

        // 改变时间周期
        function changePeriod(period) {
            if (period === 'custom') {
                document.getElementById('startDate').style.display = 'block';
                document.getElementById('endDate').style.display = 'block';
            } else {
                document.getElementById('startDate').style.display = 'none';
                document.getElementById('endDate').style.display = 'none';
            }
            showNotification(`已切换到${period === '7' ? '最近7天' : period === '30' ? '最近30天' : period === '90' ? '最近90天' : '自定义'}数据`, 'info');
        }

        // 刷新数据
        function refreshData() {
            showNotification('正在刷新数据...', 'info');
            
            // 模拟数据刷新
            setTimeout(() => {
                showNotification('数据已更新', 'success');
            }, 1500);
        }

        // 导出报表
        function exportReport() {
            showNotification('正在生成报表...', 'info');
            
            setTimeout(() => {
                showNotification('报表导出完成', 'success');
            }, 2000);
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                color: white;
                padding: 16px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                z-index: 1000;
                font-weight: 500;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>
</body>
</html> 