/**
 * 商家中心统一身份认证工具
 * 负责与认证服务器的交互和令牌管理
 */

import axios from 'axios';
import { ElMessage } from 'element-plus';

// 认证配置
const AUTH_CONFIG = {
  baseURL: process.env.VUE_APP_AUTH_BASE_URL || 'http://localhost:8081/auth-service',
  timeout: 30000,
  clientId: 'teleshop-supplier-web',
};

// 存储键常量
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'teleshop_access_token',
  REFRESH_TOKEN: 'teleshop_refresh_token',
  USER_INFO: 'teleshop_user_info',
  REMEMBER_ME: 'teleshop_remember_me',
  LAST_LOGIN_TIME: 'teleshop_last_login_time',
};

// 创建认证API客户端
const authApi = axios.create({
  baseURL: AUTH_CONFIG.baseURL,
  timeout: AUTH_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证头
authApi.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理令牌过期
authApi.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        await refreshToken();
        const newToken = getAccessToken();
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return authApi(originalRequest);
      } catch (refreshError) {
        // 刷新失败，跳转到登录页
        logout();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

/**
 * 用户登录
 * @param {Object} loginData 登录数据
 * @param {string} loginData.username 用户名
 * @param {string} loginData.password 密码
 * @param {string} loginData.captcha 验证码
 * @param {string} loginData.captchaId 验证码ID
 * @param {boolean} loginData.rememberMe 记住我
 * @returns {Promise<Object>} 登录结果
 */
export async function login(loginData) {
  try {
    const response = await authApi.post('/api/v1/auth/login', {
      ...loginData,
      loginType: 'USERNAME',
      clientId: AUTH_CONFIG.clientId,
    });
    
    if (response.data.code === 200) {
      const tokenInfo = response.data.data;
      
      // 保存令牌和用户信息
      saveTokens(tokenInfo);
      saveUserInfo(tokenInfo.userInfo);
      
      // 保存记住我状态
      if (loginData.rememberMe) {
        localStorage.setItem(STORAGE_KEYS.REMEMBER_ME, 'true');
      }
      
      // 保存登录时间
      localStorage.setItem(STORAGE_KEYS.LAST_LOGIN_TIME, new Date().toISOString());
      
      return {
        success: true,
        data: tokenInfo,
        message: '登录成功',
      };
    } else {
      throw new Error(response.data.message || '登录失败');
    }
  } catch (error) {
    console.error('登录失败:', error);
    throw new Error(error.response?.data?.message || error.message || '登录失败');
  }
}

/**
 * 用户登出
 */
export async function logout() {
  try {
    const token = getAccessToken();
    if (token) {
      await authApi.post('/api/v1/auth/logout');
    }
  } catch (error) {
    console.error('登出请求失败:', error);
  } finally {
    // 清除本地存储
    clearAuthData();
  }
}

/**
 * 刷新访问令牌
 */
export async function refreshToken() {
  const refreshTokenValue = getRefreshToken();
  if (!refreshTokenValue) {
    throw new Error('刷新令牌不存在');
  }
  
  try {
    const response = await authApi.post('/api/v1/auth/refresh', {
      refreshToken: refreshTokenValue,
      clientId: AUTH_CONFIG.clientId,
    });
    
    if (response.data.code === 200) {
      const tokenInfo = response.data.data;
      saveTokens(tokenInfo);
      return tokenInfo;
    } else {
      throw new Error(response.data.message || '令牌刷新失败');
    }
  } catch (error) {
    console.error('令牌刷新失败:', error);
    throw error;
  }
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser() {
  try {
    const response = await authApi.get('/api/v1/auth/me');
    
    if (response.data.code === 200) {
      const userInfo = response.data.data;
      saveUserInfo(userInfo);
      return userInfo;
    } else {
      throw new Error(response.data.message || '获取用户信息失败');
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw error;
  }
}

/**
 * 验证令牌有效性
 */
export async function validateToken(token) {
  try {
    const response = await authApi.post('/api/v1/auth/validate', null, {
      params: { token },
    });
    
    return response.data.data;
  } catch (error) {
    console.error('令牌验证失败:', error);
    return { valid: false, errorMessage: error.message };
  }
}

/**
 * 检查用户名是否可用
 */
export async function checkUsernameAvailable(username) {
  try {
    const response = await authApi.get('/api/v1/auth/check-username', {
      params: { username },
    });
    
    return response.data.data === true;
  } catch (error) {
    console.error('检查用户名失败:', error);
    return false;
  }
}

/**
 * 检查邮箱是否可用
 */
export async function checkEmailAvailable(email) {
  try {
    const response = await authApi.get('/api/v1/auth/check-email', {
      params: { email },
    });
    
    return response.data.data === true;
  } catch (error) {
    console.error('检查邮箱失败:', error);
    return false;
  }
}

/**
 * 检查手机号是否可用
 */
export async function checkPhoneAvailable(phone) {
  try {
    const response = await authApi.get('/api/v1/auth/check-phone', {
      params: { phone },
    });
    
    return response.data.data === true;
  } catch (error) {
    console.error('检查手机号失败:', error);
    return false;
  }
}

// ============ 本地存储管理 ============

/**
 * 保存令牌
 */
function saveTokens(tokenInfo) {
  if (tokenInfo.accessToken) {
    localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, tokenInfo.accessToken);
  }
  if (tokenInfo.refreshToken) {
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokenInfo.refreshToken);
  }
}

/**
 * 获取访问令牌
 */
export function getAccessToken() {
  return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
}

/**
 * 获取刷新令牌
 */
export function getRefreshToken() {
  return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
}

/**
 * 保存用户信息
 */
function saveUserInfo(userInfo) {
  if (userInfo) {
    localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
  }
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  const userInfoStr = localStorage.getItem(STORAGE_KEYS.USER_INFO);
  if (userInfoStr) {
    try {
      return JSON.parse(userInfoStr);
    } catch (error) {
      console.error('解析用户信息失败:', error);
      localStorage.removeItem(STORAGE_KEYS.USER_INFO);
      return null;
    }
  }
  return null;
}

/**
 * 检查是否已登录
 */
export function isAuthenticated() {
  const token = getAccessToken();
  const userInfo = getUserInfo();
  return !!(token && userInfo);
}

/**
 * 检查是否记住登录状态
 */
export function isRememberMe() {
  return localStorage.getItem(STORAGE_KEYS.REMEMBER_ME) === 'true';
}

/**
 * 获取最后登录时间
 */
export function getLastLoginTime() {
  const timeStr = localStorage.getItem(STORAGE_KEYS.LAST_LOGIN_TIME);
  return timeStr ? new Date(timeStr) : null;
}

/**
 * 清除认证数据
 */
function clearAuthData() {
  Object.values(STORAGE_KEYS).forEach(key => {
    localStorage.removeItem(key);
  });
}

/**
 * 检查用户权限
 */
export function hasPermission(permission) {
  const userInfo = getUserInfo();
  if (!userInfo || !userInfo.permissions) {
    return false;
  }
  return userInfo.permissions.includes(permission);
}

/**
 * 检查用户角色
 */
export function hasRole(role) {
  const userInfo = getUserInfo();
  if (!userInfo || !userInfo.roles) {
    return false;
  }
  return userInfo.roles.includes(role);
}

/**
 * 检查是否为商家用户
 */
export function isMerchant() {
  return hasRole('MERCHANT') || hasRole('MERCHANT_USER');
}

/**
 * 检查是否为管理员
 */
export function isAdmin() {
  return hasRole('ADMIN') || hasRole('SUPER_ADMIN');
}

// ============ 初始化检查 ============

/**
 * 初始化认证状态
 */
export async function initAuth() {
  const token = getAccessToken();
  const refreshTokenValue = getRefreshToken();
  
  if (!token || !refreshTokenValue) {
    return false;
  }
  
  try {
    // 验证令牌有效性
    const validation = await validateToken(token);
    
    if (validation.valid) {
      // 令牌有效，更新用户信息
      await getCurrentUser();
      return true;
    } else {
      // 令牌无效，尝试刷新
      await refreshToken();
      await getCurrentUser();
      return true;
    }
  } catch (error) {
    console.error('认证初始化失败:', error);
    clearAuthData();
    return false;
  }
}

// 导出认证API客户端供其他模块使用
export { authApi };
