/**
 * 商家中心WebSocket客户端
 * 负责与消息服务器的实时通信
 */

import { ElMessage, ElNotification } from 'element-plus';
import { getUserInfo, getAccessToken } from './auth';

class WebSocketClient {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.shouldReconnect = true;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000; // 5秒
    this.heartbeatInterval = 30000; // 30秒
    this.heartbeatTimer = null;
    this.reconnectTimer = null;
    
    // 事件监听器
    this.eventListeners = new Map();
    
    // 消息队列（连接断开时暂存）
    this.messageQueue = [];
  }
  
  /**
   * 连接WebSocket
   */
  async connect() {
    if (this.isConnected || this.isConnecting) {
      return;
    }
    
    try {
      this.isConnecting = true;
      
      // 获取用户信息和访问令牌
      const userInfo = getUserInfo();
      const accessToken = getAccessToken();
      
      if (!userInfo || !accessToken) {
        throw new Error('用户未认证');
      }
      
      // 构建WebSocket URL
      const wsUrl = this.buildWebSocketUrl(userInfo.userId, accessToken);
      
      // 创建WebSocket连接
      this.ws = new WebSocket(wsUrl, ['teleshop-protocol']);
      
      // 设置事件监听器
      this.ws.onopen = this.onOpen.bind(this);
      this.ws.onmessage = this.onMessage.bind(this);
      this.ws.onerror = this.onError.bind(this);
      this.ws.onclose = this.onClose.bind(this);
      
      console.log('WebSocket连接中...', wsUrl);
      
    } catch (error) {
      this.isConnecting = false;
      console.error('WebSocket连接失败:', error);
      this.emit('error', error);
      
      if (this.shouldReconnect) {
        this.scheduleReconnect();
      }
      
      throw error;
    }
  }
  
  /**
   * 断开WebSocket连接
   */
  disconnect() {
    this.shouldReconnect = false;
    
    // 清除定时器
    this.clearTimers();
    
    // 关闭WebSocket连接
    if (this.ws) {
      this.ws.close(1000, '正常关闭');
      this.ws = null;
    }
    
    // 重置状态
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    this.emit('disconnected');
    console.log('WebSocket连接已断开');
  }
  
  /**
   * 发送消息
   */
  sendMessage(message) {
    if (!this.isConnected) {
      // 连接断开时，将消息加入队列
      this.messageQueue.push(message);
      console.warn('WebSocket未连接，消息已加入队列');
      return;
    }
    
    try {
      const messageData = {
        messageId: this.generateMessageId(),
        timestamp: new Date().toISOString(),
        ...message,
      };
      
      this.ws.send(JSON.stringify(messageData));
      console.log('WebSocket消息发送成功:', messageData.messageId);
      
    } catch (error) {
      console.error('WebSocket消息发送失败:', error);
      throw error;
    }
  }
  
  /**
   * 发送文本消息
   */
  sendTextMessage(content, receiverId, conversationId) {
    this.sendMessage({
      messageType: 'TEXT',
      content,
      receiverId,
      conversationId,
      priority: 'NORMAL',
    });
  }
  
  /**
   * 发送客服消息
   */
  sendCustomerServiceMessage(content, userId) {
    this.sendMessage({
      messageType: 'CUSTOMER_SERVICE',
      content,
      receiverId: userId,
      priority: 'HIGH',
    });
  }
  
  /**
   * 发送订单通知
   */
  sendOrderNotification(orderData, userId) {
    this.sendMessage({
      messageType: 'ORDER',
      title: '订单状态更新',
      content: `您的订单 ${orderData.orderNo} 状态已更新为：${orderData.status}`,
      receiverId: userId,
      priority: 'HIGH',
      extraData: orderData,
    });
  }
  
  /**
   * 发送心跳消息
   */
  sendHeartbeat() {
    if (!this.isConnected) return;
    
    try {
      this.sendMessage({
        messageType: 'HEARTBEAT',
        content: 'ping',
      });
    } catch (error) {
      console.error('发送心跳失败:', error);
    }
  }
  
  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }
  
  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (!this.eventListeners.has(event)) return;
    
    const listeners = this.eventListeners.get(event);
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }
  
  /**
   * 触发事件
   */
  emit(event, data) {
    if (!this.eventListeners.has(event)) return;
    
    const listeners = this.eventListeners.get(event);
    listeners.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('事件监听器执行失败:', error);
      }
    });
  }
  
  /**
   * WebSocket连接打开
   */
  onOpen(event) {
    console.log('WebSocket连接成功');
    
    this.isConnected = true;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    // 启动心跳
    this.startHeartbeat();
    
    // 发送队列中的消息
    this.flushMessageQueue();
    
    this.emit('connected', event);
    
    ElMessage.success('实时通信连接成功');
  }
  
  /**
   * 接收WebSocket消息
   */
  onMessage(event) {
    try {
      const message = JSON.parse(event.data);
      
      console.log('WebSocket消息接收:', message);
      
      // 处理特殊消息类型
      switch (message.messageType) {
        case 'HEARTBEAT':
          this.handleHeartbeat(message);
          break;
        case 'ACK':
          this.handleAck(message);
          break;
        case 'ERROR':
          this.handleError(message);
          break;
        case 'CUSTOMER_SERVICE':
          this.handleCustomerServiceMessage(message);
          break;
        case 'ORDER':
          this.handleOrderMessage(message);
          break;
        case 'SYSTEM':
          this.handleSystemMessage(message);
          break;
        default:
          this.handleGeneralMessage(message);
          break;
      }
      
      this.emit('message', message);
      
    } catch (error) {
      console.error('解析WebSocket消息失败:', error);
    }
  }
  
  /**
   * WebSocket连接错误
   */
  onError(error) {
    console.error('WebSocket连接错误:', error);
    
    this.isConnected = false;
    this.emit('error', error);
    
    ElMessage.error('实时通信连接异常');
  }
  
  /**
   * WebSocket连接关闭
   */
  onClose(event) {
    console.log('WebSocket连接关闭:', event.code, event.reason);
    
    this.isConnected = false;
    this.clearTimers();
    
    this.emit('disconnected', event);
    
    // 自动重连
    if (this.shouldReconnect && event.code !== 1000) {
      this.scheduleReconnect();
    }
  }
  
  /**
   * 处理心跳消息
   */
  handleHeartbeat(message) {
    if (message.content === 'ping') {
      // 回复pong
      this.sendMessage({
        messageType: 'HEARTBEAT',
        content: 'pong',
        relatedMessageId: message.messageId,
      });
    }
  }
  
  /**
   * 处理确认消息
   */
  handleAck(message) {
    console.log('收到消息确认:', message.relatedMessageId);
  }
  
  /**
   * 处理错误消息
   */
  handleError(message) {
    console.error('收到错误消息:', message.content);
    ElMessage.error(message.content);
  }
  
  /**
   * 处理客服消息
   */
  handleCustomerServiceMessage(message) {
    ElNotification({
      title: '新的客服咨询',
      message: message.content,
      type: 'info',
      duration: 0,
      onClick: () => {
        // 跳转到客服页面
        this.$router.push('/customer-service');
      },
    });
  }
  
  /**
   * 处理订单消息
   */
  handleOrderMessage(message) {
    ElNotification({
      title: '订单通知',
      message: message.content,
      type: 'success',
      duration: 5000,
    });
  }
  
  /**
   * 处理系统消息
   */
  handleSystemMessage(message) {
    ElNotification({
      title: '系统通知',
      message: message.content,
      type: 'warning',
      duration: 0,
    });
  }
  
  /**
   * 处理一般消息
   */
  handleGeneralMessage(message) {
    // 可以根据需要处理其他类型的消息
  }
  
  /**
   * 启动心跳
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      this.sendHeartbeat();
    }, this.heartbeatInterval);
  }
  
  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数超过限制，停止重连');
      ElMessage.error('连接失败，请刷新页面重试');
      return;
    }
    
    this.reconnectAttempts++;
    
    this.reconnectTimer = setTimeout(() => {
      console.log(`WebSocket开始第 ${this.reconnectAttempts} 次重连`);
      this.connect();
    }, this.reconnectDelay * this.reconnectAttempts);
  }
  
  /**
   * 清除定时器
   */
  clearTimers() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }
  
  /**
   * 发送队列中的消息
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.sendMessage(message);
    }
  }
  
  /**
   * 构建WebSocket URL
   */
  buildWebSocketUrl(userId, accessToken) {
    const baseUrl = process.env.VUE_APP_WEBSOCKET_BASE_URL || 'ws://localhost:8082/message-service';
    return `${baseUrl}/websocket/${userId}?token=${accessToken}`;
  }
  
  /**
   * 生成消息ID
   */
  generateMessageId() {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 创建全局WebSocket客户端实例
const webSocketClient = new WebSocketClient();

// 导出WebSocket客户端
export default webSocketClient;

// 导出便捷方法
export const connectWebSocket = () => webSocketClient.connect();
export const disconnectWebSocket = () => webSocketClient.disconnect();
export const sendMessage = (message) => webSocketClient.sendMessage(message);
export const onMessage = (callback) => webSocketClient.on('message', callback);
export const onConnected = (callback) => webSocketClient.on('connected', callback);
export const onDisconnected = (callback) => webSocketClient.on('disconnected', callback);
export const onError = (callback) => webSocketClient.on('error', callback);
