<template>
  <div class="marketing-tools">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">营销工具</h1>
        <p class="page-description">创建和管理优惠券、限时折扣、满减活动等营销工具</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="createCoupon">
          <el-icon><Plus /></el-icon>
          创建优惠券
        </el-button>
        <el-button @click="createPromotion">
          <el-icon><Star /></el-icon>
          创建促销活动
        </el-button>
      </div>
    </div>

    <!-- 营销概览 -->
    <div class="marketing-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon coupon">
                <el-icon><Ticket /></el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-value">{{ overview.totalCoupons }}</div>
                <div class="overview-label">优惠券总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon promotion">
                <el-icon><Trophy /></el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-value">{{ overview.activePromotions }}</div>
                <div class="overview-label">进行中活动</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon usage">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-value">{{ overview.usageRate }}%</div>
                <div class="overview-label">优惠券使用率</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon revenue">
                <el-icon><Money /></el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-value">¥{{ formatNumber(overview.promotionRevenue) }}</div>
                <div class="overview-label">促销带来收入</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 营销工具标签页 -->
    <el-card class="tools-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="优惠券管理" name="coupons">
          <CouponManagement />
        </el-tab-pane>
        <el-tab-pane label="限时折扣" name="discounts">
          <DiscountManagement />
        </el-tab-pane>
        <el-tab-pane label="满减活动" name="fullReduction">
          <FullReductionManagement />
        </el-tab-pane>
        <el-tab-pane label="团购活动" name="groupBuy">
          <GroupBuyManagement />
        </el-tab-pane>
        <el-tab-pane label="会员专享" name="memberOnly">
          <MemberOnlyManagement />
        </el-tab-pane>
        <el-tab-pane label="营销分析" name="analytics">
          <MarketingAnalytics />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 创建优惠券对话框 -->
    <el-dialog v-model="couponDialogVisible" title="创建优惠券" width="800px">
      <el-form :model="couponForm" :rules="couponRules" ref="couponFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优惠券名称" prop="name">
              <el-input v-model="couponForm.name" placeholder="请输入优惠券名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优惠券类型" prop="type">
              <el-select v-model="couponForm.type" placeholder="请选择类型">
                <el-option label="满减券" value="full_reduction" />
                <el-option label="折扣券" value="discount" />
                <el-option label="免邮券" value="free_shipping" />
                <el-option label="现金券" value="cash" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="面额/折扣" prop="value">
              <el-input-number
                v-model="couponForm.value"
                :min="0"
                :max="couponForm.type === 'discount' ? 10 : 9999"
                :precision="couponForm.type === 'discount' ? 1 : 2"
                style="width: 100%"
              />
              <span class="input-suffix">
                {{ couponForm.type === 'discount' ? '折' : '元' }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用门槛" prop="threshold">
              <el-input-number
                v-model="couponForm.threshold"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
              <span class="input-suffix">元</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发放数量" prop="totalQuantity">
              <el-input-number
                v-model="couponForm.totalQuantity"
                :min="1"
                style="width: 100%"
              />
              <span class="input-suffix">张</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每人限领" prop="limitPerUser">
              <el-input-number
                v-model="couponForm.limitPerUser"
                :min="1"
                style="width: 100%"
              />
              <span class="input-suffix">张</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="有效期类型" prop="validityType">
              <el-radio-group v-model="couponForm.validityType">
                <el-radio label="fixed">固定时间</el-radio>
                <el-radio label="relative">相对时间</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="couponForm.validityType === 'fixed'">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="couponForm.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="couponForm.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="couponForm.validityType === 'relative'">
          <el-col :span="12">
            <el-form-item label="有效天数" prop="validDays">
              <el-input-number
                v-model="couponForm.validDays"
                :min="1"
                style="width: 100%"
              />
              <span class="input-suffix">天</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="适用商品" prop="applicableProducts">
          <el-radio-group v-model="couponForm.applicableProducts">
            <el-radio label="all">全部商品</el-radio>
            <el-radio label="category">指定分类</el-radio>
            <el-radio label="specific">指定商品</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="couponForm.applicableProducts === 'category'" label="商品分类">
          <el-cascader
            v-model="couponForm.categoryIds"
            :options="categoryOptions"
            :props="{ multiple: true }"
            placeholder="请选择商品分类"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item v-if="couponForm.applicableProducts === 'specific'" label="指定商品">
          <el-button @click="selectProducts">选择商品</el-button>
          <div v-if="couponForm.productIds.length > 0" class="selected-products">
            已选择 {{ couponForm.productIds.length }} 个商品
          </div>
        </el-form-item>

        <el-form-item label="发放方式" prop="distributionType">
          <el-checkbox-group v-model="couponForm.distributionType">
            <el-checkbox label="manual">手动发放</el-checkbox>
            <el-checkbox label="auto">自动发放</el-checkbox>
            <el-checkbox label="code">优惠码</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="优惠券描述">
          <el-input
            v-model="couponForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入优惠券使用说明"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="couponDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCoupon" :loading="savingCoupon">
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 创建促销活动对话框 -->
    <el-dialog v-model="promotionDialogVisible" title="创建促销活动" width="800px">
      <el-form :model="promotionForm" :rules="promotionRules" ref="promotionFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动名称" prop="name">
              <el-input v-model="promotionForm.name" placeholder="请输入活动名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动类型" prop="type">
              <el-select v-model="promotionForm.type" placeholder="请选择活动类型">
                <el-option label="限时折扣" value="flash_discount" />
                <el-option label="满减活动" value="full_reduction" />
                <el-option label="买N送N" value="buy_n_get_n" />
                <el-option label="第二件半价" value="second_half_price" />
                <el-option label="团购活动" value="group_buy" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="promotionForm.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="promotionForm.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 根据活动类型显示不同的配置项 -->
        <div v-if="promotionForm.type === 'flash_discount'">
          <el-form-item label="折扣力度" prop="discountRate">
            <el-input-number
              v-model="promotionForm.discountRate"
              :min="0.1"
              :max="9.9"
              :precision="1"
              style="width: 200px"
            />
            <span class="input-suffix">折</span>
          </el-form-item>
        </div>

        <div v-if="promotionForm.type === 'full_reduction'">
          <el-form-item label="满减规则">
            <div v-for="(rule, index) in promotionForm.fullReductionRules" :key="index" class="rule-item">
              <el-input-number v-model="rule.threshold" placeholder="满" style="width: 120px" />
              <span class="rule-separator">减</span>
              <el-input-number v-model="rule.reduction" placeholder="减" style="width: 120px" />
              <el-button @click="removeRule(index)" type="danger" size="small" style="margin-left: 10px">
                删除
              </el-button>
            </div>
            <el-button @click="addRule" type="primary" size="small">添加规则</el-button>
          </el-form-item>
        </div>

        <div v-if="promotionForm.type === 'group_buy'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="团购价格" prop="groupPrice">
                <el-input-number
                  v-model="promotionForm.groupPrice"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
                <span class="input-suffix">元</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="成团人数" prop="groupSize">
                <el-input-number
                  v-model="promotionForm.groupSize"
                  :min="2"
                  style="width: 100%"
                />
                <span class="input-suffix">人</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <el-form-item label="参与商品" prop="participatingProducts">
          <el-radio-group v-model="promotionForm.participatingProducts">
            <el-radio label="all">全部商品</el-radio>
            <el-radio label="category">指定分类</el-radio>
            <el-radio label="specific">指定商品</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="活动描述">
          <el-input
            v-model="promotionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入活动描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="promotionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePromotion" :loading="savingPromotion">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>