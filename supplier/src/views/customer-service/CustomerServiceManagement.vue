<template>
  <div class="customer-service-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">客服管理</h1>
        <p class="page-description">管理客服会话、查看服务统计、处理客户咨询</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="openBroadcastDialog">
          <el-icon><Promotion /></el-icon>
          群发消息
        </el-button>
        <el-button @click="exportServiceReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon online">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.activeChats }}</div>
                <div class="stat-label">进行中会话</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon waiting">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.waitingChats }}</div>
                <div class="stat-label">等待处理</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon resolved">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.resolvedToday }}</div>
                <div class="stat-label">今日已解决</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon satisfaction">
                <el-icon><Star /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.satisfaction }}%</div>
                <div class="stat-label">满意度</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 会话列表 -->
        <el-col :span="8">
          <el-card class="session-list-card">
            <template #header>
              <div class="card-header">
                <span>客服会话</span>
                <el-select v-model="sessionFilter" size="small" style="width: 120px">
                  <el-option label="全部" value="all" />
                  <el-option label="进行中" value="active" />
                  <el-option label="等待中" value="waiting" />
                  <el-option label="已结束" value="closed" />
                </el-select>
              </div>
            </template>
            
            <div class="session-list">
              <div
                v-for="session in filteredSessions"
                :key="session.id"
                class="session-item"
                :class="{ active: selectedSession?.id === session.id }"
                @click="selectSession(session)"
              >
                <div class="session-avatar">
                  <el-avatar :size="40" :src="session.user.avatar">
                    {{ session.user.name.charAt(0) }}
                  </el-avatar>
                  <div class="status-indicator" :class="session.status"></div>
                </div>
                
                <div class="session-info">
                  <div class="session-header">
                    <span class="user-name">{{ session.user.name }}</span>
                    <span class="session-time">{{ formatTime(session.lastMessageTime) }}</span>
                  </div>
                  <div class="last-message">{{ session.lastMessage }}</div>
                  <div class="session-meta">
                    <el-tag :type="getSessionTagType(session.status)" size="small">
                      {{ getSessionStatusText(session.status) }}
                    </el-tag>
                    <span v-if="session.unreadCount > 0" class="unread-count">
                      {{ session.unreadCount }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 聊天区域 -->
        <el-col :span="16">
          <el-card class="chat-card" v-if="selectedSession">
            <template #header>
              <div class="chat-header">
                <div class="user-info">
                  <el-avatar :size="32" :src="selectedSession.user.avatar">
                    {{ selectedSession.user.name.charAt(0) }}
                  </el-avatar>
                  <div class="user-details">
                    <div class="user-name">{{ selectedSession.user.name }}</div>
                    <div class="user-status">
                      <el-icon><User /></el-icon>
                      用户ID: {{ selectedSession.user.id }}
                    </div>
                  </div>
                </div>
                
                <div class="chat-actions">
                  <el-button size="small" @click="viewUserProfile">
                    <el-icon><View /></el-icon>
                    用户资料
                  </el-button>
                  <el-button size="small" @click="transferSession">
                    <el-icon><Switch /></el-icon>
                    转接
                  </el-button>
                  <el-button size="small" type="danger" @click="closeSession">
                    <el-icon><Close /></el-icon>
                    结束会话
                  </el-button>
                </div>
              </div>
            </template>

            <!-- 消息列表 -->
            <div class="message-list" ref="messageListRef">
              <div
                v-for="message in selectedSession.messages"
                :key="message.id"
                class="message-item"
                :class="{ 'is-user': message.senderType === 'user' }"
              >
                <div class="message-avatar">
                  <el-avatar :size="32" v-if="message.senderType === 'user'" :src="selectedSession.user.avatar">
                    {{ selectedSession.user.name.charAt(0) }}
                  </el-avatar>
                  <el-avatar :size="32" v-else>
                    <el-icon><Service /></el-icon>
                  </el-avatar>
                </div>
                
                <div class="message-content">
                  <div class="message-header">
                    <span class="sender-name">
                      {{ message.senderType === 'user' ? selectedSession.user.name : '客服' }}
                    </span>
                    <span class="message-time">{{ formatMessageTime(message.timestamp) }}</span>
                  </div>
                  
                  <div class="message-body">
                    <!-- 文本消息 -->
                    <div v-if="message.type === 'text'" class="text-message">
                      {{ message.content }}
                    </div>
                    
                    <!-- 图片消息 -->
                    <div v-else-if="message.type === 'image'" class="image-message">
                      <el-image
                        :src="message.content"
                        :preview-src-list="[message.content]"
                        fit="cover"
                        style="width: 200px; height: 150px; border-radius: 8px;"
                      />
                    </div>
                    
                    <!-- 系统消息 -->
                    <div v-else-if="message.type === 'system'" class="system-message">
                      <el-icon><InfoFilled /></el-icon>
                      {{ message.content }}
                    </div>
                  </div>
                  
                  <!-- AI置信度显示 -->
                  <div v-if="message.senderType === 'ai' && message.confidence" class="ai-confidence">
                    <el-tooltip :content="`AI回复置信度: ${(message.confidence * 100).toFixed(1)}%`">
                      <el-progress
                        :percentage="message.confidence * 100"
                        :stroke-width="4"
                        :show-text="false"
                        :color="getConfidenceColor(message.confidence)"
                      />
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>

            <!-- 输入区域 -->
            <div class="message-input-area">
              <!-- 快捷回复 -->
              <div class="quick-replies" v-if="quickReplies.length > 0">
                <el-tag
                  v-for="reply in quickReplies"
                  :key="reply"
                  class="quick-reply-tag"
                  @click="sendQuickReply(reply)"
                  style="cursor: pointer; margin-right: 8px; margin-bottom: 8px;"
                >
                  {{ reply }}
                </el-tag>
              </div>
              
              <!-- 输入框 -->
              <div class="input-container">
                <el-input
                  v-model="messageInput"
                  type="textarea"
                  :rows="3"
                  placeholder="输入回复消息..."
                  @keydown.ctrl.enter="sendMessage"
                />
                <div class="input-actions">
                  <el-button size="small" @click="showEmojiPicker">
                    <el-icon><Sunny /></el-icon>
                  </el-button>
                  <el-button size="small" @click="uploadImage">
                    <el-icon><Picture /></el-icon>
                  </el-button>
                  <el-button type="primary" @click="sendMessage" :loading="sending">
                    发送 (Ctrl+Enter)
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
          
          <!-- 空状态 -->
          <el-card class="chat-card" v-else>
            <div class="empty-chat">
              <el-icon size="64" color="#C0C4CC"><ChatDotRound /></el-icon>
              <p>选择一个会话开始聊天</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 群发消息对话框 -->
    <el-dialog v-model="broadcastDialogVisible" title="群发消息" width="600px">
      <el-form :model="broadcastForm" label-width="80px">
        <el-form-item label="发送对象">
          <el-select v-model="broadcastForm.target" placeholder="选择发送对象">
            <el-option label="所有用户" value="all" />
            <el-option label="VIP用户" value="vip" />
            <el-option label="活跃用户" value="active" />
          </el-select>
        </el-form-item>
        <el-form-item label="消息内容">
          <el-input
            v-model="broadcastForm.content"
            type="textarea"
            :rows="4"
            placeholder="输入要群发的消息内容..."
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="broadcastDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sendBroadcast" :loading="broadcasting">
          发送
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ChatDotRound,
  Clock,
  Check,
  Star,
  Promotion,
  Download,
  User,
  View,
  Switch,
  Close,
  Service,
  InfoFilled,
  Sunny,
  Picture
} from '@element-plus/icons-vue'
import { customerServiceApi } from '@/api/customer-service'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 响应式数据
const stats = reactive({
  activeChats: 0,
  waitingChats: 0,
  resolvedToday: 0,
  satisfaction: 0
})

const sessions = ref([])
const selectedSession = ref(null)
const sessionFilter = ref('all')
const messageInput = ref('')
const sending = ref(false)
const messageListRef = ref(null)

const broadcastDialogVisible = ref(false)
const broadcastForm = reactive({
  target: 'all',
  content: ''
})
const broadcasting = ref(false)

const quickReplies = ref([
  '您好，有什么可以帮助您的吗？',
  '请稍等，我为您查询一下',
  '感谢您的咨询，还有其他问题吗？',
  '您的问题已经记录，我们会尽快处理',
  '如有其他疑问，请随时联系我们'
])

// 计算属性
const filteredSessions = computed(() => {
  if (sessionFilter.value === 'all') {
    return sessions.value
  }
  return sessions.value.filter(session => session.status === sessionFilter.value)
})

// 生命周期
onMounted(() => {
  loadStats()
  loadSessions()
  // 定时刷新数据
  setInterval(() => {
    loadStats()
    loadSessions()
  }, 30000)
})

// 方法
const loadStats = async () => {
  try {
    const response = await customerServiceApi.getStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadSessions = async () => {
  try {
    const response = await customerServiceApi.getSessions()
    sessions.value = response.data
  } catch (error) {
    console.error('加载会话列表失败:', error)
  }
}

const selectSession = async (session) => {
  selectedSession.value = session
  // 加载会话消息
  try {
    const response = await customerServiceApi.getSessionMessages(session.id)
    session.messages = response.data
    // 滚动到底部
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('加载会话消息失败:', error)
  }
}

const sendMessage = async () => {
  if (!messageInput.value.trim() || !selectedSession.value) return
  
  sending.value = true
  try {
    await customerServiceApi.sendMessage({
      sessionId: selectedSession.value.id,
      content: messageInput.value,
      type: 'text'
    })
    
    // 添加消息到本地列表
    selectedSession.value.messages.push({
      id: Date.now(),
      content: messageInput.value,
      type: 'text',
      senderType: 'agent',
      timestamp: new Date()
    })
    
    messageInput.value = ''
    await nextTick()
    scrollToBottom()
    
    ElMessage.success('消息发送成功')
  } catch (error) {
    ElMessage.error('消息发送失败')
  } finally {
    sending.value = false
  }
}

const sendQuickReply = (reply) => {
  messageInput.value = reply
  sendMessage()
}

const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

const formatTime = (time) => {
  return formatDistanceToNow(new Date(time), { 
    addSuffix: true, 
    locale: zhCN 
  })
}

const formatMessageTime = (time) => {
  const date = new Date(time)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

const getSessionTagType = (status) => {
  const types = {
    active: 'success',
    waiting: 'warning',
    closed: 'info'
  }
  return types[status] || 'info'
}

const getSessionStatusText = (status) => {
  const texts = {
    active: '进行中',
    waiting: '等待中',
    closed: '已结束'
  }
  return texts[status] || '未知'
}

const getConfidenceColor = (confidence) => {
  if (confidence >= 0.8) return '#67C23A'
  if (confidence >= 0.6) return '#E6A23C'
  return '#F56C6C'
}

const openBroadcastDialog = () => {
  broadcastDialogVisible.value = true
}

const sendBroadcast = async () => {
  if (!broadcastForm.content.trim()) {
    ElMessage.warning('请输入消息内容')
    return
  }
  
  broadcasting.value = true
  try {
    await customerServiceApi.sendBroadcast(broadcastForm)
    ElMessage.success('群发消息发送成功')
    broadcastDialogVisible.value = false
    broadcastForm.content = ''
  } catch (error) {
    ElMessage.error('群发消息发送失败')
  } finally {
    broadcasting.value = false
  }
}

const exportServiceReport = () => {
  // 实现导出报告功能
  ElMessage.info('导出功能开发中...')
}

const viewUserProfile = () => {
  // 实现查看用户资料功能
  ElMessage.info('用户资料功能开发中...')
}

const transferSession = () => {
  // 实现转接会话功能
  ElMessage.info('转接功能开发中...')
}

const closeSession = async () => {
  try {
    await ElMessageBox.confirm('确定要结束这个会话吗？', '确认', {
      type: 'warning'
    })
    
    await customerServiceApi.closeSession(selectedSession.value.id)
    selectedSession.value.status = 'closed'
    ElMessage.success('会话已结束')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('结束会话失败')
    }
  }
}

const showEmojiPicker = () => {
  // 实现表情选择器
  ElMessage.info('表情功能开发中...')
}

const uploadImage = () => {
  // 实现图片上传
  ElMessage.info('图片上传功能开发中...')
}
</script>

<style scoped>
.customer-service-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 4px 0 0 0;
  color: #666;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.online { background: #67C23A; }
.stat-icon.waiting { background: #E6A23C; }
.stat-icon.resolved { background: #409EFF; }
.stat-icon.satisfaction { background: #F56C6C; }

.stat-value {
  font-size: 28px;
  font-weight: 600;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.session-list-card,
.chat-card {
  height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.session-list {
  height: 520px;
  overflow-y: auto;
}

.session-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.session-item:hover {
  background-color: #f5f7fa;
}

.session-item.active {
  background-color: #e6f7ff;
}

.session-avatar {
  position: relative;
  margin-right: 12px;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-indicator.active { background: #52c41a; }
.status-indicator.waiting { background: #faad14; }
.status-indicator.closed { background: #d9d9d9; }

.session-info {
  flex: 1;
  min-width: 0;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.user-name {
  font-weight: 500;
  font-size: 14px;
}

.session-time {
  font-size: 12px;
  color: #999;
}

.last-message {
  font-size: 13px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 8px;
}

.session-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.unread-count {
  background: #ff4d4f;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  margin-left: 12px;
}

.user-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.user-status {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.message-list {
  height: 400px;
  overflow-y: auto;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
}

.message-item.is-user {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 8px;
}

.message-content {
  max-width: 70%;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
}

.message-item.is-user .message-header {
  flex-direction: row-reverse;
}

.sender-name {
  font-weight: 500;
  margin-right: 8px;
}

.message-item.is-user .sender-name {
  margin-right: 0;
  margin-left: 8px;
}

.message-body {
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 8px;
  word-break: break-word;
}

.message-item.is-user .message-body {
  background: #1890ff;
  color: white;
}

.system-message {
  background: #fff7e6 !important;
  color: #fa8c16;
  display: flex;
  align-items: center;
  gap: 4px;
}

.ai-confidence {
  margin-top: 4px;
  width: 100px;
}

.message-input-area {
  padding: 16px;
}

.quick-replies {
  margin-bottom: 12px;
}

.quick-reply-tag {
  transition: all 0.2s;
}

.quick-reply-tag:hover {
  background: #1890ff;
  color: white;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.input-container .el-textarea {
  flex: 1;
}

.input-actions {
  display: flex;
  gap: 8px;
}

.empty-chat {
  text-align: center;
  padding: 100px 0;
  color: #999;
}

.empty-chat p {
  margin-top: 16px;
  font-size: 16px;
}
</style>
