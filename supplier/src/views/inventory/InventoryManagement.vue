<template>
  <div class="inventory-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">库存管理</h1>
        <p class="page-description">管理商品库存、设置预警、执行盘点调拨等操作</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="openBatchImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="openInventoryCheck">
          <el-icon><DocumentChecked /></el-icon>
          库存盘点
        </el-button>
        <el-button @click="openStockTransfer">
          <el-icon><Switch /></el-icon>
          库存调拨
        </el-button>
        <el-button @click="exportInventoryReport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Box /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalProducts }}</div>
                <div class="stat-label">商品总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon warning">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.lowStockProducts }}</div>
                <div class="stat-label">库存预警</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon danger">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.outOfStockProducts }}</div>
                <div class="stat-label">缺货商品</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">¥{{ formatNumber(stats.totalValue) }}</div>
                <div class="stat-label">库存总值</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="商品名称">
          <el-input
            v-model="filterForm.productName"
            placeholder="请输入商品名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="商品分类">
          <el-cascader
            v-model="filterForm.categoryId"
            :options="categoryOptions"
            placeholder="请选择分类"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="库存状态">
          <el-select v-model="filterForm.stockStatus" placeholder="请选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="正常" value="normal" />
            <el-option label="预警" value="warning" />
            <el-option label="缺货" value="out_of_stock" />
          </el-select>
        </el-form-item>
        <el-form-item label="仓库">
          <el-select v-model="filterForm.warehouseId" placeholder="请选择仓库" clearable>
            <el-option
              v-for="warehouse in warehouses"
              :key="warehouse.id"
              :label="warehouse.name"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchInventory">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 库存列表 -->
    <el-card class="inventory-table-card">
      <template #header>
        <div class="card-header">
          <span>库存列表</span>
          <div class="header-actions">
            <el-button size="small" @click="refreshInventory">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button size="small" @click="batchUpdateStock">
              批量调整
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="inventoryList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="商品信息" min-width="300">
          <template #default="{ row }">
            <div class="product-info">
              <el-image
                :src="row.product.imageUrl"
                :preview-src-list="[row.product.imageUrl]"
                fit="cover"
                style="width: 60px; height: 60px; border-radius: 4px;"
              />
              <div class="product-details">
                <div class="product-name">{{ row.product.name }}</div>
                <div class="product-sku">SKU: {{ row.product.sku }}</div>
                <div class="product-specs" v-if="row.product.specs">
                  {{ formatSpecs(row.product.specs) }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="分类" prop="product.categoryName" width="120" />

        <el-table-column label="仓库" prop="warehouse.name" width="100" />

        <el-table-column label="当前库存" width="120">
          <template #default="{ row }">
            <div class="stock-info">
              <span :class="getStockStatusClass(row.currentStock, row.safeStock)">
                {{ row.currentStock }}
              </span>
              <div class="stock-unit">{{ row.product.unit || '件' }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="安全库存" prop="safeStock" width="100" />

        <el-table-column label="预警阈值" prop="warningThreshold" width="100" />

        <el-table-column label="在途库存" prop="inTransitStock" width="100" />

        <el-table-column label="锁定库存" prop="lockedStock" width="100" />

        <el-table-column label="可用库存" width="100">
          <template #default="{ row }">
            <span class="available-stock">
              {{ row.currentStock - row.lockedStock }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="库存价值" width="120">
          <template #default="{ row }">
            <span class="stock-value">
              ¥{{ formatNumber(row.currentStock * row.product.costPrice) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="最后更新" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.lastUpdateTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="adjustStock(row)">
              调整库存
            </el-button>
            <el-button size="small" @click="viewStockHistory(row)">
              库存记录
            </el-button>
            <el-dropdown @command="handleMoreAction">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`setWarning_${row.id}`">
                    设置预警
                  </el-dropdown-item>
                  <el-dropdown-item :command="`transfer_${row.id}`">
                    库存调拨
                  </el-dropdown-item>
                  <el-dropdown-item :command="`freeze_${row.id}`">
                    冻结库存
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 库存调整对话框 -->
    <el-dialog v-model="adjustDialogVisible" title="库存调整" width="600px">
      <el-form :model="adjustForm" :rules="adjustRules" ref="adjustFormRef" label-width="100px">
        <el-form-item label="商品信息">
          <div class="product-info">
            <el-image
              :src="selectedProduct?.imageUrl"
              style="width: 50px; height: 50px; border-radius: 4px;"
            />
            <div class="product-details">
              <div>{{ selectedProduct?.name }}</div>
              <div class="text-gray-500">SKU: {{ selectedProduct?.sku }}</div>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="当前库存">
          <span class="current-stock">{{ currentInventory?.currentStock || 0 }} {{ selectedProduct?.unit || '件' }}</span>
        </el-form-item>
        
        <el-form-item label="调整类型" prop="adjustType">
          <el-radio-group v-model="adjustForm.adjustType">
            <el-radio label="increase">增加库存</el-radio>
            <el-radio label="decrease">减少库存</el-radio>
            <el-radio label="set">设置库存</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="调整数量" prop="quantity">
          <el-input-number
            v-model="adjustForm.quantity"
            :min="adjustForm.adjustType === 'decrease' ? 1 : 0"
            :max="adjustForm.adjustType === 'decrease' ? currentInventory?.currentStock : 999999"
            style="width: 200px"
          />
          <span class="ml-2">{{ selectedProduct?.unit || '件' }}</span>
        </el-form-item>
        
        <el-form-item label="调整原因" prop="reason">
          <el-select v-model="adjustForm.reason" placeholder="请选择调整原因">
            <el-option label="盘点调整" value="inventory_check" />
            <el-option label="损耗调整" value="loss_adjustment" />
            <el-option label="退货入库" value="return_inbound" />
            <el-option label="采购入库" value="purchase_inbound" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="adjustForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入调整备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="adjustDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAdjustStock" :loading="adjusting">
          确认调整
        </el-button>
      </template>
    </el-dialog>

    <!-- 库存预警设置对话框 -->
    <el-dialog v-model="warningDialogVisible" title="库存预警设置" width="500px">
      <el-form :model="warningForm" :rules="warningRules" ref="warningFormRef" label-width="120px">
        <el-form-item label="安全库存" prop="safeStock">
          <el-input-number v-model="warningForm.safeStock" :min="0" style="width: 200px" />
          <span class="ml-2">{{ selectedProduct?.unit || '件' }}</span>
        </el-form-item>
        
        <el-form-item label="预警阈值" prop="warningThreshold">
          <el-input-number v-model="warningForm.warningThreshold" :min="0" style="width: 200px" />
          <span class="ml-2">{{ selectedProduct?.unit || '件' }}</span>
        </el-form-item>
        
        <el-form-item label="自动补货">
          <el-switch v-model="warningForm.autoReplenish" />
        </el-form-item>
        
        <el-form-item label="补货数量" v-if="warningForm.autoReplenish" prop="replenishQuantity">
          <el-input-number v-model="warningForm.replenishQuantity" :min="1" style="width: 200px" />
          <span class="ml-2">{{ selectedProduct?.unit || '件' }}</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="warningDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmWarningSettings" :loading="settingWarning">
          保存设置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Box,
  Warning,
  CircleClose,
  TrendCharts,
  Upload,
  DocumentChecked,
  Switch,
  Download,
  Search,
  Refresh
} from '@element-plus/icons-vue'
import { inventoryApi } from '@/api/inventory'
import { formatDateTime, formatNumber } from '@/utils/format'

// 响应式数据
const loading = ref(false)
const stats = reactive({
  totalProducts: 0,
  lowStockProducts: 0,
  outOfStockProducts: 0,
  totalValue: 0
})

const filterForm = reactive({
  productName: '',
  categoryId: [],
  stockStatus: '',
  warehouseId: ''
})

const inventoryList = ref([])
const selectedRows = ref([])
const categoryOptions = ref([])
const warehouses = ref([])

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 库存调整相关
const adjustDialogVisible = ref(false)
const selectedProduct = ref(null)
const currentInventory = ref(null)
const adjusting = ref(false)

const adjustForm = reactive({
  adjustType: 'increase',
  quantity: 0,
  reason: '',
  remark: ''
})

const adjustRules = {
  adjustType: [{ required: true, message: '请选择调整类型', trigger: 'change' }],
  quantity: [{ required: true, message: '请输入调整数量', trigger: 'blur' }],
  reason: [{ required: true, message: '请选择调整原因', trigger: 'change' }]
}

// 预警设置相关
const warningDialogVisible = ref(false)
const settingWarning = ref(false)

const warningForm = reactive({
  safeStock: 0,
  warningThreshold: 0,
  autoReplenish: false,
  replenishQuantity: 0
})

const warningRules = {
  safeStock: [{ required: true, message: '请输入安全库存', trigger: 'blur' }],
  warningThreshold: [{ required: true, message: '请输入预警阈值', trigger: 'blur' }]
}

// 生命周期
onMounted(() => {
  loadInventoryData()
  loadCategoryOptions()
  loadWarehouses()
  loadStats()
})

// 方法
const loadInventoryData = async () => {
  loading.value = true
  try {
    const params = {
      ...filterForm,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    }
    const response = await inventoryApi.getInventoryList(params)
    inventoryList.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载库存数据失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await inventoryApi.getInventoryStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadCategoryOptions = async () => {
  try {
    const response = await inventoryApi.getCategoryOptions()
    categoryOptions.value = response.data
  } catch (error) {
    console.error('加载分类选项失败:', error)
  }
}

const loadWarehouses = async () => {
  try {
    const response = await inventoryApi.getWarehouses()
    warehouses.value = response.data
  } catch (error) {
    console.error('加载仓库列表失败:', error)
  }
}

const searchInventory = () => {
  pagination.currentPage = 1
  loadInventoryData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    productName: '',
    categoryId: [],
    stockStatus: '',
    warehouseId: ''
  })
  searchInventory()
}

const refreshInventory = () => {
  loadInventoryData()
  loadStats()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  loadInventoryData()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadInventoryData()
}

const adjustStock = (row) => {
  selectedProduct.value = row.product
  currentInventory.value = row
  adjustForm.adjustType = 'increase'
  adjustForm.quantity = 0
  adjustForm.reason = ''
  adjustForm.remark = ''
  adjustDialogVisible.value = true
}

const confirmAdjustStock = async () => {
  // 表单验证逻辑
  adjusting.value = true
  try {
    await inventoryApi.adjustStock({
      inventoryId: currentInventory.value.id,
      ...adjustForm
    })
    ElMessage.success('库存调整成功')
    adjustDialogVisible.value = false
    loadInventoryData()
    loadStats()
  } catch (error) {
    ElMessage.error('库存调整失败')
  } finally {
    adjusting.value = false
  }
}

const getStockStatusClass = (currentStock, safeStock) => {
  if (currentStock <= 0) return 'stock-out'
  if (currentStock <= safeStock) return 'stock-warning'
  return 'stock-normal'
}

const formatSpecs = (specs) => {
  return Object.values(specs).join(' ')
}

const handleMoreAction = (command) => {
  const [action, id] = command.split('_')
  const row = inventoryList.value.find(item => item.id === parseInt(id))
  
  switch (action) {
    case 'setWarning':
      setWarningThreshold(row)
      break
    case 'transfer':
      transferStock(row)
      break
    case 'freeze':
      freezeStock(row)
      break
  }
}

const setWarningThreshold = (row) => {
  selectedProduct.value = row.product
  currentInventory.value = row
  warningForm.safeStock = row.safeStock
  warningForm.warningThreshold = row.warningThreshold
  warningForm.autoReplenish = row.autoReplenish || false
  warningForm.replenishQuantity = row.replenishQuantity || 0
  warningDialogVisible.value = true
}

const confirmWarningSettings = async () => {
  settingWarning.value = true
  try {
    await inventoryApi.updateWarningSettings({
      inventoryId: currentInventory.value.id,
      ...warningForm
    })
    ElMessage.success('预警设置保存成功')
    warningDialogVisible.value = false
    loadInventoryData()
  } catch (error) {
    ElMessage.error('预警设置保存失败')
  } finally {
    settingWarning.value = false
  }
}

// 其他功能方法
const openBatchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

const openInventoryCheck = () => {
  ElMessage.info('库存盘点功能开发中...')
}

const openStockTransfer = () => {
  ElMessage.info('库存调拨功能开发中...')
}

const exportInventoryReport = () => {
  ElMessage.info('导出报表功能开发中...')
}

const batchUpdateStock = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要调整的库存记录')
    return
  }
  ElMessage.info('批量调整功能开发中...')
}

const viewStockHistory = (row) => {
  ElMessage.info('库存记录功能开发中...')
}

const transferStock = (row) => {
  ElMessage.info('库存调拨功能开发中...')
}

const freezeStock = (row) => {
  ElMessage.info('冻结库存功能开发中...')
}
</script>

<style scoped>
.inventory-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 4px 0 0 0;
  color: #666;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total { background: #409EFF; }
.stat-icon.warning { background: #E6A23C; }
.stat-icon.danger { background: #F56C6C; }
.stat-icon.success { background: #67C23A; }

.stat-value {
  font-size: 28px;
  font-weight: 600;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.filter-card {
  margin-bottom: 20px;
}

.inventory-table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-details {
  flex: 1;
}

.product-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.product-sku {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.product-specs {
  font-size: 12px;
  color: #666;
}

.stock-info {
  text-align: center;
}

.stock-unit {
  font-size: 12px;
  color: #999;
}

.stock-normal { color: #67C23A; }
.stock-warning { color: #E6A23C; }
.stock-out { color: #F56C6C; }

.available-stock {
  font-weight: 500;
  color: #409EFF;
}

.stock-value {
  font-weight: 500;
  color: #67C23A;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.current-stock {
  font-size: 16px;
  font-weight: 500;
  color: #409EFF;
}
</style>
