package com.teleshop.wallet;

import com.teleshop.wallet.entity.UserWallet;
import com.teleshop.wallet.entity.WalletTransaction;
import com.teleshop.wallet.repository.UserWalletRepository;
import com.teleshop.wallet.repository.WalletTransactionRepository;
import com.teleshop.wallet.service.impl.WalletServiceImpl;
import com.teleshop.wallet.dto.request.RechargeDTO;
import com.teleshop.wallet.dto.request.WithdrawDTO;
import com.teleshop.wallet.dto.response.TransactionStatisticsVO;
import com.teleshop.wallet.dto.response.WalletStatisticsVO;
import com.teleshop.wallet.enums.TransactionType;
import com.teleshop.wallet.enums.WalletStatus;
import com.teleshop.wallet.enums.TransactionStatus;
import com.teleshop.common.utils.RedisUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 钱包应用集成测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
public class WalletApplicationTest {

    @Mock
    private UserWalletRepository userWalletRepository;

    @Mock
    private WalletTransactionRepository walletTransactionRepository;

    @Mock
    private RedisUtil redisUtil;

    @InjectMocks
    private WalletServiceImpl walletService;

    private UserWallet testWallet;
    private WalletTransaction testTransaction;

    @BeforeEach
    void setUp() {
        testWallet = new UserWallet();
        testWallet.setId(1L);
        testWallet.setUserId(1001L);
        testWallet.setBalance(new BigDecimal("1000.00"));
        testWallet.setFrozenAmount(new BigDecimal("100.00"));
        testWallet.setStatus(WalletStatus.ACTIVE);
        testWallet.setCreatedAt(LocalDateTime.now());
        testWallet.setUpdatedAt(LocalDateTime.now());

        testTransaction = new WalletTransaction();
        testTransaction.setId(1L);
        testTransaction.setUserId(1001L);
        testTransaction.setType(TransactionType.RECHARGE);
        testTransaction.setAmount(new BigDecimal("100.00"));
        testTransaction.setStatus(TransactionStatus.SUCCESS);
        testTransaction.setCreatedAt(LocalDateTime.now());
    }

    @Test
    void testGetWalletByUserId() {
        when(userWalletRepository.findByUserId(1001L)).thenReturn(Optional.of(testWallet));

        Optional<UserWallet> result = Optional.ofNullable(walletService.getWalletByUserId(1001L));

        assertTrue(result.isPresent());
        assertEquals(testWallet.getUserId(), result.get().getUserId());
        assertEquals(testWallet.getBalance(), result.get().getBalance());
        verify(userWalletRepository, times(1)).findByUserId(1001L);
    }

    @Test
    void testCreateWallet() {
        when(userWalletRepository.save(any(UserWallet.class))).thenReturn(testWallet);

        UserWallet result = walletService.createWallet(1001L);

        assertNotNull(result);
        assertEquals(1001L, result.getUserId());
        assertEquals(WalletStatus.ACTIVE, result.getStatus());
        verify(userWalletRepository, times(1)).save(any(UserWallet.class));
    }

    @Test
    void testGetTransactionStatistics() {
        // Mock repository methods
        when(walletTransactionRepository.countByUserIdAndType(1001L, TransactionType.RECHARGE))
                .thenReturn(5L);
        when(walletTransactionRepository.sumAmountByUserIdAndType(1001L, TransactionType.RECHARGE))
                .thenReturn(new BigDecimal("500.00"));
        when(walletTransactionRepository.countByUserIdAndType(1001L, TransactionType.WITHDRAW))
                .thenReturn(3L);
        when(walletTransactionRepository.sumAmountByUserIdAndType(1001L, TransactionType.WITHDRAW))
                .thenReturn(new BigDecimal("200.00"));
        when(walletTransactionRepository.countByUserIdAndType(1001L, TransactionType.CONSUME))
                .thenReturn(10L);
        when(walletTransactionRepository.sumAmountByUserIdAndType(1001L, TransactionType.CONSUME))
                .thenReturn(new BigDecimal("800.00"));
        when(walletTransactionRepository.countByUserIdAndType(1001L, TransactionType.TRANSFER))
                .thenReturn(2L);
        when(walletTransactionRepository.sumAmountByUserIdAndType(1001L, TransactionType.TRANSFER))
                .thenReturn(new BigDecimal("150.00"));
        when(walletTransactionRepository.countByUserIdAndType(1001L, TransactionType.REFUND))
                .thenReturn(1L);
        when(walletTransactionRepository.sumAmountByUserIdAndType(1001L, TransactionType.REFUND))
                .thenReturn(new BigDecimal("50.00"));

        LocalDateTime now = LocalDateTime.now();
        when(walletTransactionRepository.sumAmountByUserIdAndTime(eq(1001L), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(new BigDecimal("100.00"));
        when(walletTransactionRepository.findMaxAmountByUserId(1001L))
                .thenReturn(new BigDecimal("200.00"));

        TransactionStatisticsVO result = walletService.getTransactionStatistics(1001L);

        assertNotNull(result);
        assertEquals(21L, result.getTotalTransactionCount());
        assertEquals(new BigDecimal("1700.00"), result.getTotalTransactionAmount());
        assertEquals(5L, result.getRechargeCount());
        assertEquals(new BigDecimal("500.00"), result.getRechargeAmount());
        assertEquals(3L, result.getWithdrawCount());
        assertEquals(new BigDecimal("200.00"), result.getWithdrawAmount());
        assertEquals(10L, result.getConsumeCount());
        assertEquals(new BigDecimal("800.00"), result.getConsumeAmount());
        assertEquals(2L, result.getTransferCount());
        assertEquals(new BigDecimal("150.00"), result.getTransferAmount());
        assertEquals(1L, result.getRefundCount());
        assertEquals(new BigDecimal("50.00"), result.getRefundAmount());
        assertEquals(new BigDecimal("100.00"), result.getTodayTransactionAmount());
        assertEquals(new BigDecimal("100.00"), result.getMonthTransactionAmount());
        assertEquals(new BigDecimal("200.00"), result.getMaxTransactionAmount());
        assertEquals("HIGH", result.getRiskLevel());
    }

    @Test
    void testGetWalletStatistics() {
        // Mock repository methods
        when(userWalletRepository.count()).thenReturn(1000L);
        when(userWalletRepository.countByStatus(WalletStatus.ACTIVE)).thenReturn(950L);
        when(userWalletRepository.countByStatus(WalletStatus.FROZEN)).thenReturn(30L);
        when(userWalletRepository.countByStatus(WalletStatus.INACTIVE)).thenReturn(20L);
        when(userWalletRepository.sumAllBalance()).thenReturn(new BigDecimal("1000000.00"));
        when(userWalletRepository.sumAllFrozenAmount()).thenReturn(new BigDecimal("50000.00"));
        when(userWalletRepository.findAvgBalance()).thenReturn(new BigDecimal("1000.00"));
        when(userWalletRepository.findMaxBalance()).thenReturn(new BigDecimal("50000.00"));
        when(userWalletRepository.findMinBalance()).thenReturn(new BigDecimal("0.00"));

        when(walletTransactionRepository.sumAmountByTypeAndTime(eq(TransactionType.RECHARGE), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(new BigDecimal("5000.00"));
        when(walletTransactionRepository.sumAmountByTypeAndTime(eq(TransactionType.WITHDRAW), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(new BigDecimal("3000.00"));
        when(walletTransactionRepository.sumAmountByTypeAndTime(eq(TransactionType.CONSUME), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(new BigDecimal("8000.00"));
        when(walletTransactionRepository.sumAmountByTypeAndTime(eq(TransactionType.TRANSFER), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(new BigDecimal("2000.00"));
        when(walletTransactionRepository.sumAmountByTypeAndTime(eq(TransactionType.REFUND), any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(new BigDecimal("1000.00"));

        when(walletTransactionRepository.countByAmountGreaterThan(new BigDecimal("10000.00")))
                .thenReturn(50L);
        when(walletTransactionRepository.sumByAmountGreaterThan(new BigDecimal("10000.00")))
                .thenReturn(new BigDecimal("800000.00"));
        when(walletTransactionRepository.countByStatus(TransactionStatus.FAILED))
                .thenReturn(25L);
        when(userWalletRepository.countByBalanceGreaterThan(new BigDecimal("50000.00")))
                .thenReturn(10L);

        WalletStatisticsVO result = walletService.getWalletStatistics();

        assertNotNull(result);
        assertEquals(1000L, result.getTotalWalletCount());
        assertEquals(950L, result.getActiveWalletCount());
        assertEquals(30L, result.getFrozenWalletCount());
        assertEquals(20L, result.getInactiveWalletCount());
        assertEquals(new BigDecimal("1000000.00"), result.getTotalBalance());
        assertEquals(new BigDecimal("50000.00"), result.getTotalFrozenAmount());
        assertEquals(new BigDecimal("950000.00"), result.getTotalAvailableBalance());
        assertEquals(new BigDecimal("1000.00"), result.getAvgBalance());
        assertEquals(new BigDecimal("50000.00"), result.getMaxBalance());
        assertEquals(new BigDecimal("0.00"), result.getMinBalance());
        assertEquals(new BigDecimal("5000.00"), result.getTodayRechargeAmount());
        assertEquals(new BigDecimal("3000.00"), result.getTodayWithdrawAmount());
        assertEquals(new BigDecimal("8000.00"), result.getTodayConsumeAmount());
        assertEquals(new BigDecimal("2000.00"), result.getTodayTransferAmount());
        assertEquals(new BigDecimal("1000.00"), result.getTodayRefundAmount());
        assertEquals(50L, result.getLargeTransactionCount());
        assertEquals(new BigDecimal("800000.00"), result.getLargeTransactionAmount());
        assertEquals(25L, result.getAbnormalTransactionCount());
        assertEquals(10L, result.getHighRiskWalletCount());
    }

    @Test
    void testCalculateRiskLevel() {
        // Test LOW risk level
        assertEquals("LOW", walletService.calculateRiskLevel(1L, new BigDecimal("500.00")));
        
        // Test MEDIUM risk level
        assertEquals("MEDIUM", walletService.calculateRiskLevel(10L, new BigDecimal("5000.00")));
        
        // Test HIGH risk level
        assertEquals("HIGH", walletService.calculateRiskLevel(50L, new BigDecimal("50000.00")));
    }

    @Test
    void testValidateWalletOperations() {
        // Test basic assertions
        assertNotNull(walletService);
        assertTrue(testWallet.getBalance().compareTo(BigDecimal.ZERO) > 0);
        assertTrue(testTransaction.getAmount().compareTo(BigDecimal.ZERO) > 0);
        assertEquals(TransactionType.RECHARGE, testTransaction.getType());
        assertEquals(TransactionStatus.SUCCESS, testTransaction.getStatus());
    }
} 