package com.teleshop.wallet.config;

import com.teleshop.common.utils.RedisUtil;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@TestConfiguration
public class TestConfig {

    @Bean
    @Primary
    public RedisUtil redisUtil() {
        return new MockRedisUtil();
    }

    /**
     * Mock Redis工具类，用于测试环境
     */
    public static class MockRedisUtil extends RedisUtil {
        
        @Override
        public boolean set(String key, Object value) {
            // 模拟实现，返回true
            return true;
        }

        @Override
        public boolean set(String key, Object value, long timeout) {
            // 模拟实现，返回true
            return true;
        }

        @Override
        public Object get(String key) {
            // 模拟实现，返回null
            return null;
        }

        @Override
        public boolean delete(String key) {
            // 模拟实现，返回true
            return true;
        }

        @Override
        public boolean expire(String key, long timeout) {
            // 模拟实现，返回true
            return true;
        }

        @Override
        public long getExpire(String key) {
            // 模拟实现，返回-1
            return -1;
        }

        @Override
        public boolean exists(String key) {
            // 模拟实现，返回false
            return false;
        }

        @Override
        public boolean hSet(String key, String field, Object value) {
            // 模拟实现，返回true
            return true;
        }

        @Override
        public Object hGet(String key, String field) {
            // 模拟实现，返回null
            return null;
        }

        @Override
        public boolean lock(String key, long timeout, TimeUnit unit) {
            // 模拟实现，总是返回true
            return true;
        }

        @Override
        public boolean unlock(String key) {
            // 模拟实现，返回true
            return true;
        }

        @Override
        public boolean tryLock(String key, long waitTime, long leaseTime, TimeUnit unit) {
            // 模拟实现，总是返回true
            return true;
        }

        @Override
        public long increment(String key, long delta) {
            // 模拟实现，返回delta
            return delta;
        }

        @Override
        public long decrement(String key, long delta) {
            // 模拟实现，返回-delta
            return -delta;
        }

        @Override
        public void delete(String... keys) {
            // 模拟实现，什么都不做
        }

        @Override
        public boolean hasKey(String key) {
            // 模拟实现，返回false
            return false;
        }

        @Override
        public Set<String> keys(String pattern) {
            // 模拟实现，返回空集合
            return java.util.Collections.emptySet();
        }

        @Override
        public long size(String key) {
            // 模拟实现，返回0
            return 0;
        }

        @Override
        public void hDelete(String key, Object... fields) {
            // 模拟实现，什么都不做
        }

        @Override
        public boolean hExists(String key, String field) {
            // 模拟实现，返回false
            return false;
        }

        @Override
        public long hSize(String key) {
            // 模拟实现，返回0
            return 0;
        }

        @Override
        public Set<String> hKeys(String key) {
            // 模拟实现，返回空集合
            return java.util.Collections.emptySet();
        }

        @Override
        public java.util.List<Object> hValues(String key) {
            // 模拟实现，返回空列表
            return java.util.Collections.emptyList();
        }

        @Override
        public java.util.Map<String, Object> hGetAll(String key) {
            // 模拟实现，返回空Map
            return java.util.Collections.emptyMap();
        }

        @Override
        public void hMSet(String key, java.util.Map<String, Object> map) {
            // 模拟实现，什么都不做
        }

        @Override
        public java.util.List<Object> hMGet(String key, String... fields) {
            // 模拟实现，返回空列表
            return java.util.Collections.emptyList();
        }

        @Override
        public long hIncrement(String key, String field, long delta) {
            // 模拟实现，返回delta
            return delta;
        }

        @Override
        public double hIncrement(String key, String field, double delta) {
            // 模拟实现，返回delta
            return delta;
        }

        @Override
        public long lPush(String key, Object... values) {
            // 模拟实现，返回values长度
            return values.length;
        }

        @Override
        public long rPush(String key, Object... values) {
            // 模拟实现，返回values长度
            return values.length;
        }

        @Override
        public Object lPop(String key) {
            // 模拟实现，返回null
            return null;
        }

        @Override
        public Object rPop(String key) {
            // 模拟实现，返回null
            return null;
        }

        @Override
        public long lSize(String key) {
            // 模拟实现，返回0
            return 0;
        }

        @Override
        public java.util.List<Object> lRange(String key, long start, long end) {
            // 模拟实现，返回空列表
            return java.util.Collections.emptyList();
        }

        @Override
        public Object lIndex(String key, long index) {
            // 模拟实现，返回null
            return null;
        }

        @Override
        public void lSet(String key, long index, Object value) {
            // 模拟实现，什么都不做
        }

        @Override
        public long lRemove(String key, long count, Object value) {
            // 模拟实现，返回0
            return 0;
        }

        @Override
        public void lTrim(String key, long start, long end) {
            // 模拟实现，什么都不做
        }

        @Override
        public long sAdd(String key, Object... values) {
            // 模拟实现，返回values长度
            return values.length;
        }

        @Override
        public long sRemove(String key, Object... values) {
            // 模拟实现，返回values长度
            return values.length;
        }

        @Override
        public Object sPop(String key) {
            // 模拟实现，返回null
            return null;
        }

        @Override
        public long sSize(String key) {
            // 模拟实现，返回0
            return 0;
        }

        @Override
        public boolean sIsMember(String key, Object value) {
            // 模拟实现，返回false
            return false;
        }

        @Override
        public Set<Object> sMembers(String key) {
            // 模拟实现，返回空集合
            return java.util.Collections.emptySet();
        }

        @Override
        public Object sRandomMember(String key) {
            // 模拟实现，返回null
            return null;
        }

        @Override
        public java.util.List<Object> sRandomMembers(String key, long count) {
            // 模拟实现，返回空列表
            return java.util.Collections.emptyList();
        }

        @Override
        public Set<Object> sIntersect(String key, String otherKey) {
            // 模拟实现，返回空集合
            return java.util.Collections.emptySet();
        }

        @Override
        public Set<Object> sUnion(String key, String otherKey) {
            // 模拟实现，返回空集合
            return java.util.Collections.emptySet();
        }

        @Override
        public Set<Object> sDifference(String key, String otherKey) {
            // 模拟实现，返回空集合
            return java.util.Collections.emptySet();
        }

        @Override
        public boolean zAdd(String key, double score, Object value) {
            // 模拟实现，返回true
            return true;
        }

        @Override
        public long zRemove(String key, Object... values) {
            // 模拟实现，返回values长度
            return values.length;
        }

        @Override
        public double zIncrement(String key, double delta, Object value) {
            // 模拟实现，返回delta
            return delta;
        }

        @Override
        public Long zRank(String key, Object value) {
            // 模拟实现，返回null
            return null;
        }

        @Override
        public Long zReverseRank(String key, Object value) {
            // 模拟实现，返回null
            return null;
        }

        @Override
        public Set<Object> zRange(String key, long start, long end) {
            // 模拟实现，返回空集合
            return java.util.Collections.emptySet();
        }

        @Override
        public Set<Object> zReverseRange(String key, long start, long end) {
            // 模拟实现，返回空集合
            return java.util.Collections.emptySet();
        }

        @Override
        public long zCount(String key, double min, double max) {
            // 模拟实现，返回0
            return 0;
        }

        @Override
        public long zSize(String key) {
            // 模拟实现，返回0
            return 0;
        }

        @Override
        public Double zScore(String key, Object value) {
            // 模拟实现，返回null
            return null;
        }

        @Override
        public long zRemoveRange(String key, long start, long end) {
            // 模拟实现，返回0
            return 0;
        }

        @Override
        public long zRemoveRangeByScore(String key, double min, double max) {
            // 模拟实现，返回0
            return 0;
        }
    }
} 