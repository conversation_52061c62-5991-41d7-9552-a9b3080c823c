# 测试环境配置
# 数据库配置
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.h2.console.enabled=true

# JPA配置
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false

# 禁用Redis和Redisson自动配置
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,org.redisson.spring.starter.RedissonAutoConfiguration

# 允许Bean定义覆盖
spring.main.allow-bean-definition-overriding=true

# 禁用Nacos配置
spring.cloud.nacos.discovery.enabled=false
spring.cloud.nacos.config.enabled=false

# 禁用Feign
spring.cloud.openfeign.enabled=false

# 钱包服务测试配置
teleshop.wallet.transaction.large-amount-threshold=50000
teleshop.wallet.risk.enabled=false

# 日志配置
logging.level.com.teleshop.wallet=DEBUG
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE 