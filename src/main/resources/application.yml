server:
  port: 8082
  servlet:
    context-path: /api/product

spring:
  application:
    name: teleshop-product-service
  
  datasource:
    url: ***************************************************************************************************************
    username: teleshop
    password: teleshop123
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# JWT配置
jwt:
  secret: teleshop-product-service-secret-key-2024
  expiration: 86400000

# 文件上传配置
file:
  upload:
    path: /tmp/teleshop/product/
    max-size: 10MB

# 日志配置
logging:
  level:
    com.teleshop: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always 