# TeleShop 项目配置文档

## 项目基本信息

```yaml
project:
  name: TeleShop
  version: 1.0.0
  description: 集即时通讯、社交网络、电商购物和数字钱包于一体的综合性移动应用平台
  type: B2B2C社交电商平台
  license: MIT
  author: TeleShop Team
  created: 2024-12-20
  updated: 2024-12-20
```

## 技术架构配置

### 前端技术栈
```yaml
frontend:
  mobile:
    framework: Flutter
    version: 3.16.0+
    language: Dart
    state_management: Riverpod
    ui_framework: Material Design 3
    http_client: Dio
    local_storage: Hive
    
  merchant:
    framework: Vue.js
    version: 3.4.0+
    language: TypeScript
    build_tool: Vite
    ui_library: Ant Design Vue
    state_management: Pinia
    router: Vue Router 4
    
  admin:
    framework: Vue.js
    version: 3.4.0+
    language: TypeScript
    build_tool: Vite
    ui_library: Element Plus
    state_management: Pinia
    router: Vue Router 4
```

### 后端技术栈
```yaml
backend:
  framework: Spring Boot
  version: 2.7.x
  language: Java
  java_version: 11
  architecture: 微服务架构
  
  database:
    primary: MySQL
    version: 8.0
    cache: Redis
    cache_version: 6.x
    search: Elasticsearch
    search_version: 7.x
    
  middleware:
    message_queue: RabbitMQ
    api_gateway: Spring Cloud Gateway
    service_discovery: Nacos
    config_center: Nacos
    
  security:
    authentication: JWT
    authorization: Spring Security
    oauth2: Spring Security OAuth2
```

## 项目结构配置

```yaml
structure:
  root: teleshop/
  modules:
    - name: teleshop-app
      type: Flutter移动端
      path: teleshop-app/
      tech: Flutter + Dart
      
    - name: teleshop-merchant
      type: Vue.js商家端
      path: teleshop-merchant/
      tech: Vue.js + TypeScript
      
    - name: teleshop-admin
      type: Vue.js管理端
      path: teleshop-admin/
      tech: Vue.js + TypeScript
      
    - name: teleshop-backend
      type: Spring Boot后端
      path: teleshop-backend/
      tech: Java + Spring Boot
      
    - name: teleshop-docs
      type: 项目文档
      path: docs/
      tech: Markdown
```

## 开发环境配置

```yaml
development:
  required_tools:
    - Flutter SDK 3.16.0+
    - Node.js 18+
    - Java 11+
    - MySQL 8.0+
    - Redis 6.x+
    - Docker 20.10+
    - Git 2.30+
    
  recommended_ide:
    - VS Code
    - IntelliJ IDEA
    - Android Studio
    
  vs_code_extensions:
    - Flutter
    - Dart
    - Vue Language Features (Volar)
    - TypeScript Vue Plugin (Volar)
    - ESLint
    - Prettier
    - GitLens
    
  idea_plugins:
    - Spring Boot
    - MyBatis
    - Lombok
    - Vue.js
    - Flutter
```

## 数据库配置

```yaml
database:
  mysql:
    host: localhost
    port: 3306
    charset: utf8mb4
    collation: utf8mb4_unicode_ci
    engine: InnoDB
    
  redis:
    host: localhost
    port: 6379
    database: 0
    
  elasticsearch:
    host: localhost
    port: 9200
    cluster_name: teleshop-es
```

## API接口配置

```yaml
api:
  base_url: /api/v1
  version: 1.0
  format: RESTful
  
  authentication:
    type: JWT
    header: Authorization
    prefix: Bearer
    
  response_format:
    success:
      code: 200
      message: string
      data: object
      timestamp: string
      traceId: string
      
    error:
      code: 4xx/5xx
      message: string
      errors: array
      timestamp: string
      traceId: string
      
  rate_limiting:
    enabled: true
    requests_per_minute: 100
    burst_size: 200
```

## 部署配置

```yaml
deployment:
  containerization:
    platform: Docker
    orchestration: Kubernetes
    
  environments:
    development:
      domain: dev.teleshop.com
      database: teleshop_dev
      
    staging:
      domain: staging.teleshop.com
      database: teleshop_staging
      
    production:
      domain: teleshop.com
      database: teleshop_prod
      
  monitoring:
    apm: SkyWalking
    metrics: Prometheus
    visualization: Grafana
    logging: ELK Stack
```

## 安全配置

```yaml
security:
  authentication:
    jwt:
      secret: ${JWT_SECRET}
      expiration: 7200 # 2小时
      refresh_expiration: 604800 # 7天
      
  encryption:
    password: BCrypt
    sensitive_data: AES-256
    
  cors:
    allowed_origins:
      - http://localhost:3000
      - https://teleshop.com
    allowed_methods:
      - GET
      - POST
      - PUT
      - DELETE
    allowed_headers:
      - Authorization
      - Content-Type
      
  rate_limiting:
    login_attempts: 5
    lockout_duration: 900 # 15分钟
```

## 测试配置

```yaml
testing:
  unit_tests:
    framework: JUnit 5 (Java), Jest (JS), Flutter Test (Dart)
    coverage_threshold: 80%
    
  integration_tests:
    framework: Spring Boot Test, Cypress
    coverage_threshold: 70%
    
  e2e_tests:
    framework: Selenium, Flutter Driver
    coverage_threshold: 60%
    
  performance_tests:
    framework: JMeter, Artillery
    load_threshold: 1000 concurrent users
```

## 代码质量配置

```yaml
code_quality:
  linting:
    java: Checkstyle, SpotBugs
    javascript: ESLint
    dart: Dart Analysis
    
  formatting:
    java: Google Java Format
    javascript: Prettier
    dart: dartfmt
    
  static_analysis:
    java: SonarQube
    javascript: SonarJS
    dart: Dart Analysis
    
  code_review:
    required_reviewers: 2
    checks:
      - functionality
      - performance
      - security
      - maintainability
```

## 文档配置

```yaml
documentation:
  api_docs:
    tool: Swagger/OpenAPI
    path: /api/docs
    
  code_docs:
    java: JavaDoc
    javascript: JSDoc
    dart: DartDoc
    
  project_docs:
    format: Markdown
    location: docs/
    
  changelog:
    format: Keep a Changelog
    file: CHANGELOG.md
```

## 性能配置

```yaml
performance:
  frontend:
    bundle_size_limit: 2MB
    first_contentful_paint: < 1.5s
    largest_contentful_paint: < 2.5s
    
  backend:
    api_response_time: < 200ms
    database_query_time: < 100ms
    memory_usage: < 80%
    
  database:
    connection_pool_size: 20
    query_timeout: 30s
    slow_query_threshold: 1s
```

## 监控配置

```yaml
monitoring:
  health_checks:
    endpoint: /actuator/health
    interval: 30s
    
  metrics:
    jvm_metrics: true
    custom_metrics: true
    business_metrics: true
    
  alerting:
    channels:
      - email
      - slack
      - webhook
    conditions:
      - cpu_usage > 80%
      - memory_usage > 85%
      - error_rate > 5%
      - response_time > 500ms
```

## 日志配置

```yaml
logging:
  level:
    root: INFO
    com.teleshop: DEBUG
    
  format: JSON
  
  appenders:
    console:
      enabled: true
      pattern: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
      
    file:
      enabled: true
      path: logs/teleshop.log
      max_size: 100MB
      max_history: 30
      
  correlation_id:
    enabled: true
    header: X-Trace-ID
```

## 缓存配置

```yaml
caching:
  redis:
    default_ttl: 3600 # 1小时
    max_connections: 100
    
  application:
    caffeine:
      maximum_size: 10000
      expire_after_write: 300 # 5分钟
      
  strategies:
    user_cache: 1800 # 30分钟
    product_cache: 3600 # 1小时
    category_cache: 7200 # 2小时
```

## 消息队列配置

```yaml
message_queue:
  rabbitmq:
    host: localhost
    port: 5672
    virtual_host: /teleshop
    
  queues:
    order_queue:
      name: order.processing
      durable: true
      
    notification_queue:
      name: notification.send
      durable: true
      
    email_queue:
      name: email.send
      durable: true
```

## 文件存储配置

```yaml
file_storage:
  local:
    path: /uploads
    max_size: 10MB
    allowed_types:
      - image/jpeg
      - image/png
      - image/gif
      - application/pdf
      
  cloud:
    provider: AWS S3
    bucket: teleshop-files
    region: us-east-1
    
  cdn:
    provider: CloudFront
    domain: cdn.teleshop.com
```

## 国际化配置

```yaml
i18n:
  default_locale: zh-CN
  supported_locales:
    - zh-CN
    - en-US
    - ja-JP
    - ko-KR
    
  message_files:
    - messages_zh_CN.properties
    - messages_en_US.properties
    - messages_ja_JP.properties
    - messages_ko_KR.properties
```

## 第三方集成配置

```yaml
integrations:
  payment:
    alipay:
      app_id: ${ALIPAY_APP_ID}
      private_key: ${ALIPAY_PRIVATE_KEY}
      
    wechat_pay:
      app_id: ${WECHAT_APP_ID}
      mch_id: ${WECHAT_MCH_ID}
      
  sms:
    provider: 阿里云短信
    access_key: ${SMS_ACCESS_KEY}
    secret_key: ${SMS_SECRET_KEY}
    
  email:
    provider: SMTP
    host: smtp.gmail.com
    port: 587
    username: ${EMAIL_USERNAME}
    password: ${EMAIL_PASSWORD}
    
  map:
    provider: 高德地图
    api_key: ${MAP_API_KEY}
    
  push_notification:
    fcm:
      server_key: ${FCM_SERVER_KEY}
    apns:
      key_id: ${APNS_KEY_ID}
      team_id: ${APNS_TEAM_ID}
```

## 业务规则配置

```yaml
business_rules:
  user:
    max_login_attempts: 5
    password_min_length: 8
    session_timeout: 7200 # 2小时
    
  order:
    auto_confirm_days: 7
    refund_deadline_days: 15
    max_items_per_order: 100
    
  wallet:
    min_recharge_amount: 1.00
    max_recharge_amount: 50000.00
    daily_withdraw_limit: 10000.00
    
  chat:
    max_group_members: 20000
    message_retention_days: 365
    file_size_limit: 100MB
    
  product:
    max_images_per_product: 10
    max_variants_per_product: 50
    inventory_warning_threshold: 10
```

## 版本控制配置

```yaml
version_control:
  git:
    main_branch: main
    develop_branch: develop
    feature_prefix: feature/
    hotfix_prefix: hotfix/
    
  commit_message:
    format: "type(scope): subject"
    types:
      - feat
      - fix
      - docs
      - style
      - refactor
      - test
      - chore
      
  release:
    versioning: semantic
    changelog: auto-generated
    tagging: auto
```

## 构建配置

```yaml
build:
  flutter:
    build_mode: release
    target_platform: android-arm64,ios
    obfuscate: true
    split_debug_info: true
    
  vue:
    build_command: npm run build
    output_dir: dist
    source_map: false
    
  java:
    build_tool: Maven
    java_version: 11
    packaging: jar
    
  docker:
    base_image: openjdk:11-jre-slim
    expose_port: 8080
    health_check: true
```

## 配置管理

```yaml
config_management:
  profiles:
    - dev
    - staging
    - prod
    
  external_config:
    nacos:
      server_addr: localhost:8848
      namespace: teleshop
      group: DEFAULT_GROUP
      
  sensitive_data:
    encryption: jasypt
    key_source: environment
    
  feature_flags:
    enabled: true
    provider: custom
    refresh_interval: 300 # 5分钟
```

## 备份配置

```yaml
backup:
  database:
    frequency: daily
    retention: 30 days
    location: s3://teleshop-backups/db/
    
  files:
    frequency: daily
    retention: 90 days
    location: s3://teleshop-backups/files/
    
  logs:
    frequency: weekly
    retention: 180 days
    location: s3://teleshop-backups/logs/
```

## 联系信息

```yaml
contact:
  team_lead: <EMAIL>
  dev_team: <EMAIL>
  support: <EMAIL>
  
  documentation: https://docs.teleshop.com
  repository: https://github.com/teleshop/teleshop
  issue_tracker: https://github.com/teleshop/teleshop/issues
```
description:
globs:
alwaysApply: false
---
