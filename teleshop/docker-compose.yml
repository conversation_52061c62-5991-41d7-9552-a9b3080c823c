version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: teleshop-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: teleshop123
      MYSQL_DATABASE: teleshop
      MYSQL_USER: teleshop
      MYSQL_PASSWORD: teleshop123
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/conf:/etc/mysql/conf.d
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - teleshop-network

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    container_name: teleshop-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf
    networks:
      - teleshop-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.11-management-alpine
    container_name: teleshop-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: teleshop
      RABBITMQ_DEFAULT_PASS: teleshop123
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - teleshop-network

  # Elasticsearch搜索引擎
  elasticsearch:
    image: elasticsearch:7.17.15
    container_name: teleshop-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - teleshop-network

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: teleshop-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: teleshop
      MINIO_ROOT_PASSWORD: teleshop123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - teleshop-network

  # Nacos配置中心
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: teleshop-nacos
    restart: unless-stopped
    environment:
      MODE: standalone
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: serverIdentity
      NACOS_AUTH_IDENTITY_VALUE: security
      JVM_XMS: 256m
      JVM_XMX: 256m
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_data:/home/<USER>/data
    networks:
      - teleshop-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: teleshop-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - teleshop-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: teleshop-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: teleshop123
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - teleshop-network

  # Jaeger链路追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: teleshop-jaeger
    restart: unless-stopped
    environment:
      COLLECTOR_OTLP_ENABLED: true
      SPAN_STORAGE_TYPE: memory
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
      - "6831:6831/udp"
      - "6832:6832/udp"
    networks:
      - teleshop-network

  # 网关服务
  gateway:
    build:
      context: ./teleshop-backend/teleshop-gateway
      dockerfile: Dockerfile
    container_name: teleshop-gateway
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      REDIS_HOST: redis
      REDIS_PASSWORD: teleshop123
      TELESHOP_JWT_SECRET: teleshop2024_production_jwt_secret_key_very_long_and_secure
    ports:
      - "8080:8080"
    depends_on:
      - nacos
      - redis
    networks:
      - teleshop-network

  # 用户服务
  user-service:
    build:
      context: ./teleshop-backend/teleshop-user-service
      dockerfile: Dockerfile
    container_name: teleshop-user-service
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      MYSQL_DATABASE: teleshop
      MYSQL_USERNAME: teleshop
      MYSQL_PASSWORD: teleshop123
      REDIS_HOST: redis
      REDIS_PASSWORD: teleshop123
      NACOS_SERVER_ADDR: nacos:8848
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - teleshop-network

  # 商品服务
  product-service:
    build:
      context: ./teleshop-backend/teleshop-product-service
      dockerfile: Dockerfile
    container_name: teleshop-product-service
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      ELASTICSEARCH_HOST: elasticsearch
      NACOS_HOST: nacos
    ports:
      - "8082:8082"
    depends_on:
      - mysql
      - redis
      - elasticsearch
      - nacos
    networks:
      - teleshop-network

  # 订单服务
  order-service:
    build:
      context: ./teleshop-backend/teleshop-order-service
      dockerfile: Dockerfile
    container_name: teleshop-order-service
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      RABBITMQ_HOST: rabbitmq
      NACOS_HOST: nacos
    ports:
      - "8083:8083"
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - nacos
    networks:
      - teleshop-network

  # 钱包服务
  wallet-service:
    build:
      context: ./teleshop-backend/teleshop-wallet-service
      dockerfile: Dockerfile
    container_name: teleshop-wallet-service
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      NACOS_HOST: nacos
    ports:
      - "8084:8084"
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - teleshop-network

  # 消息服务
  message-service:
    build:
      context: ./teleshop-backend/teleshop-message-service
      dockerfile: Dockerfile
    container_name: teleshop-message-service
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      RABBITMQ_HOST: rabbitmq
      NACOS_HOST: nacos
    ports:
      - "8085:8085"
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - nacos
    networks:
      - teleshop-network

  # 商家服务
  merchant-service:
    build:
      context: ./teleshop-backend/teleshop-merchant-service
      dockerfile: Dockerfile
    container_name: teleshop-merchant-service
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      NACOS_HOST: nacos
    ports:
      - "8086:8086"
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - teleshop-network



  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: teleshop-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - ./teleshop-merchant/dist:/usr/share/nginx/html/merchant
      - ./teleshop-admin/dist:/usr/share/nginx/html/admin
    depends_on:
      - gateway
    networks:
      - teleshop-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  elasticsearch_data:
    driver: local
  minio_data:
    driver: local
  nacos_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  teleshop-network:
    driver: bridge 