#!/bin/bash

# TeleShop系统部署脚本
# 作者: TeleShop团队
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_prerequisites() {
    log_info "检查系统环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p docker/mysql/init
    mkdir -p docker/mysql/data
    mkdir -p docker/redis/data
    mkdir -p docker/nacos/data
    mkdir -p docker/nacos/logs
    mkdir -p docker/elasticsearch/data
    mkdir -p docker/rabbitmq/data
    mkdir -p docker/minio/data
    mkdir -p docker/prometheus/data
    mkdir -p docker/grafana/data
    mkdir -p docker/grafana/dashboards/teleshop
    mkdir -p docker/grafana/dashboards/infrastructure
    mkdir -p docker/grafana/dashboards/business
    mkdir -p logs
    
    log_success "目录创建完成"
}

# 设置权限
set_permissions() {
    log_info "设置目录权限..."
    
    # Elasticsearch需要特殊权限
    sudo chown -R 1000:1000 docker/elasticsearch/data
    
    # Grafana需要特殊权限
    sudo chown -R 472:472 docker/grafana/data
    
    # 其他目录权限
    chmod -R 755 docker/
    chmod +x scripts/*.sh
    
    log_success "权限设置完成"
}

# 构建镜像
build_images() {
    log_info "构建应用镜像..."
    
    # 构建后端服务镜像
    services=("teleshop-gateway" "teleshop-user-service" "teleshop-product-service" 
              "teleshop-order-service" "teleshop-wallet-service" "teleshop-message-service" 
              "teleshop-merchant-service")
    
    for service in "${services[@]}"; do
        log_info "构建 $service 镜像..."
        docker build -t $service:latest ./teleshop-backend/$service/
        log_success "$service 镜像构建完成"
    done
}

# 启动基础设施服务
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 启动数据库和中间件
    docker-compose up -d mysql redis rabbitmq elasticsearch nacos
    
    log_info "等待基础设施服务启动..."
    sleep 30
    
    # 检查服务状态
    check_service_health "mysql" "3306"
    check_service_health "redis" "6379"
    check_service_health "rabbitmq" "5672"
    check_service_health "elasticsearch" "9200"
    check_service_health "nacos" "8848"
    
    log_success "基础设施服务启动完成"
}

# 启动监控服务
start_monitoring() {
    log_info "启动监控服务..."
    
    docker-compose up -d prometheus grafana jaeger
    
    log_info "等待监控服务启动..."
    sleep 20
    
    check_service_health "prometheus" "9090"
    check_service_health "grafana" "3000"
    check_service_health "jaeger" "16686"
    
    log_success "监控服务启动完成"
}

# 启动应用服务
start_applications() {
    log_info "启动应用服务..."
    
    # 按依赖顺序启动服务
    docker-compose up -d user-service
    sleep 10
    
    docker-compose up -d product-service order-service wallet-service message-service merchant-service
    sleep 20
    
    # 最后启动网关
    docker-compose up -d gateway
    sleep 10
    
    log_success "应用服务启动完成"
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    log_info "检查 $service_name 服务健康状态..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z localhost $port; then
            log_success "$service_name 服务健康检查通过"
            return 0
        fi
        
        log_warning "$service_name 服务未就绪，等待中... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    log_error "$service_name 服务健康检查失败"
    return 1
}

# 显示服务状态
show_status() {
    log_info "显示服务状态..."
    
    echo ""
    echo "=== TeleShop系统服务状态 ==="
    docker-compose ps
    
    echo ""
    echo "=== 服务访问地址 ==="
    echo "网关服务: http://localhost:8080"
    echo "Nacos控制台: http://localhost:8848/nacos (用户名/密码: nacos/nacos)"
    echo "Grafana监控: http://localhost:3000 (用户名/密码: admin/teleshop123)"
    echo "Prometheus: http://localhost:9090"
    echo "Jaeger链路追踪: http://localhost:16686"
    echo "Kibana日志: http://localhost:5601"
    echo "RabbitMQ管理: http://localhost:15672 (用户名/密码: teleshop/teleshop123)"
    echo "MinIO控制台: http://localhost:9001 (用户名/密码: teleshop/teleshop123)"
    echo ""
}

# 停止所有服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose down
    log_success "所有服务已停止"
}

# 清理数据
clean_data() {
    log_warning "这将删除所有数据，请确认是否继续？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "清理数据..."
        docker-compose down -v
        docker system prune -f
        sudo rm -rf docker/*/data/*
        log_success "数据清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 查看日志
view_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$service"
    fi
}

# 主函数
main() {
    case "$1" in
        "start")
            check_prerequisites
            create_directories
            set_permissions
            build_images
            start_infrastructure
            start_monitoring
            start_applications
            show_status
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 5
            main start
            ;;
        "status")
            show_status
            ;;
        "logs")
            view_logs "$2"
            ;;
        "clean")
            clean_data
            ;;
        "build")
            build_images
            ;;
        *)
            echo "使用方法: $0 {start|stop|restart|status|logs [service]|clean|build}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动所有服务"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  status  - 显示服务状态"
            echo "  logs    - 查看日志 (可指定服务名)"
            echo "  clean   - 清理所有数据"
            echo "  build   - 构建应用镜像"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
