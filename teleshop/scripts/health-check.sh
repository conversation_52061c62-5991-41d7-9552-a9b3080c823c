#!/bin/bash

# TeleShop系统健康检查脚本
# 作者: TeleShop团队
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
TIMEOUT=10
RETRY_COUNT=3

# 服务配置
declare -A SERVICES=(
    ["MySQL"]="localhost:3306"
    ["Redis"]="localhost:6379"
    ["RabbitMQ"]="localhost:5672"
    ["Elasticsearch"]="localhost:9200"
    ["Nacos"]="localhost:8848"
    ["Prometheus"]="localhost:9090"
    ["Grafana"]="localhost:3000"
    ["Jaeger"]="localhost:16686"
    ["Gateway"]="localhost:8080"
)

declare -A HTTP_SERVICES=(
    ["Nacos"]="http://localhost:8848/nacos"
    ["Prometheus"]="http://localhost:9090/-/healthy"
    ["Grafana"]="http://localhost:3000/api/health"
    ["Jaeger"]="http://localhost:16686/"
    ["Gateway"]="http://localhost:8080/actuator/health"
    ["Elasticsearch"]="http://localhost:9200/_cluster/health"
)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口连通性
check_port() {
    local service=$1
    local host_port=$2
    local host=$(echo $host_port | cut -d: -f1)
    local port=$(echo $host_port | cut -d: -f2)
    
    if timeout $TIMEOUT bash -c "echo >/dev/tcp/$host/$port" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查HTTP服务
check_http_service() {
    local service=$1
    local url=$2
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout $TIMEOUT "$url" 2>/dev/null)
    
    if [[ "$response" =~ ^[2-3][0-9][0-9]$ ]]; then
        return 0
    else
        return 1
    fi
}

# 检查Docker容器状态
check_docker_containers() {
    log_info "检查Docker容器状态..."
    
    local containers=$(docker-compose ps --services 2>/dev/null)
    local all_healthy=true
    
    if [ -z "$containers" ]; then
        log_error "未找到Docker Compose服务"
        return 1
    fi
    
    echo ""
    printf "%-20s %-15s %-10s\n" "容器名称" "状态" "健康状态"
    echo "================================================"
    
    for container in $containers; do
        local status=$(docker-compose ps $container 2>/dev/null | tail -n +3 | awk '{print $4}')
        local health="N/A"
        
        # 获取容器健康状态
        local container_id=$(docker-compose ps -q $container 2>/dev/null)
        if [ ! -z "$container_id" ]; then
            health=$(docker inspect --format='{{.State.Health.Status}}' $container_id 2>/dev/null || echo "N/A")
        fi
        
        if [[ "$status" == "Up" ]]; then
            printf "%-20s ${GREEN}%-15s${NC} %-10s\n" "$container" "$status" "$health"
        else
            printf "%-20s ${RED}%-15s${NC} %-10s\n" "$container" "$status" "$health"
            all_healthy=false
        fi
    done
    
    echo ""
    
    if $all_healthy; then
        log_success "所有Docker容器运行正常"
        return 0
    else
        log_error "部分Docker容器状态异常"
        return 1
    fi
}

# 检查服务端口
check_service_ports() {
    log_info "检查服务端口连通性..."
    
    local all_healthy=true
    
    echo ""
    printf "%-15s %-20s %-10s\n" "服务名称" "地址" "状态"
    echo "============================================="
    
    for service in "${!SERVICES[@]}"; do
        local host_port=${SERVICES[$service]}
        local retry=0
        local success=false
        
        while [ $retry -lt $RETRY_COUNT ]; do
            if check_port "$service" "$host_port"; then
                success=true
                break
            fi
            ((retry++))
            sleep 1
        done
        
        if $success; then
            printf "%-15s %-20s ${GREEN}%-10s${NC}\n" "$service" "$host_port" "正常"
        else
            printf "%-15s %-20s ${RED}%-10s${NC}\n" "$service" "$host_port" "异常"
            all_healthy=false
        fi
    done
    
    echo ""
    
    if $all_healthy; then
        log_success "所有服务端口连通正常"
        return 0
    else
        log_error "部分服务端口连通异常"
        return 1
    fi
}

# 检查HTTP服务健康状态
check_http_services() {
    log_info "检查HTTP服务健康状态..."
    
    local all_healthy=true
    
    echo ""
    printf "%-15s %-40s %-10s\n" "服务名称" "健康检查URL" "状态"
    echo "================================================================="
    
    for service in "${!HTTP_SERVICES[@]}"; do
        local url=${HTTP_SERVICES[$service]}
        local retry=0
        local success=false
        
        while [ $retry -lt $RETRY_COUNT ]; do
            if check_http_service "$service" "$url"; then
                success=true
                break
            fi
            ((retry++))
            sleep 2
        done
        
        if $success; then
            printf "%-15s %-40s ${GREEN}%-10s${NC}\n" "$service" "$url" "正常"
        else
            printf "%-15s %-40s ${RED}%-10s${NC}\n" "$service" "$url" "异常"
            all_healthy=false
        fi
    done
    
    echo ""
    
    if $all_healthy; then
        log_success "所有HTTP服务健康检查通过"
        return 0
    else
        log_error "部分HTTP服务健康检查失败"
        return 1
    fi
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源使用情况..."
    
    echo ""
    echo "=== CPU使用率 ==="
    top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4"%"}'
    
    echo ""
    echo "=== 内存使用情况 ==="
    free -h
    
    echo ""
    echo "=== 磁盘使用情况 ==="
    df -h
    
    echo ""
    echo "=== Docker资源使用情况 ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"
    
    echo ""
}

# 检查日志错误
check_logs_for_errors() {
    log_info "检查最近的错误日志..."
    
    local services=("gateway" "user-service" "product-service" "order-service" "wallet-service" "message-service")
    local error_found=false
    
    for service in "${services[@]}"; do
        local errors=$(docker-compose logs --tail=100 $service 2>/dev/null | grep -i "error\|exception\|failed" | wc -l)
        
        if [ $errors -gt 0 ]; then
            log_warning "$service 发现 $errors 条错误日志"
            error_found=true
        fi
    done
    
    if ! $error_found; then
        log_success "未发现明显的错误日志"
    fi
}

# 生成健康报告
generate_health_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="health-report-$(date '+%Y%m%d-%H%M%S').txt"
    
    log_info "生成健康检查报告: $report_file"
    
    {
        echo "TeleShop系统健康检查报告"
        echo "生成时间: $timestamp"
        echo "========================================"
        echo ""
        
        echo "Docker容器状态:"
        docker-compose ps
        echo ""
        
        echo "系统资源使用情况:"
        echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4"%"}')"
        echo "内存:"
        free -h
        echo ""
        echo "磁盘:"
        df -h
        echo ""
        
        echo "服务端口检查结果:"
        for service in "${!SERVICES[@]}"; do
            local host_port=${SERVICES[$service]}
            if check_port "$service" "$host_port"; then
                echo "$service ($host_port): 正常"
            else
                echo "$service ($host_port): 异常"
            fi
        done
        echo ""
        
        echo "HTTP服务检查结果:"
        for service in "${!HTTP_SERVICES[@]}"; do
            local url=${HTTP_SERVICES[$service]}
            if check_http_service "$service" "$url"; then
                echo "$service: 正常"
            else
                echo "$service: 异常"
            fi
        done
        
    } > "$report_file"
    
    log_success "健康检查报告已生成: $report_file"
}

# 主函数
main() {
    echo "========================================"
    echo "TeleShop系统健康检查"
    echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "========================================"
    
    local overall_health=true
    
    # 执行各项检查
    if ! check_docker_containers; then
        overall_health=false
    fi
    
    if ! check_service_ports; then
        overall_health=false
    fi
    
    if ! check_http_services; then
        overall_health=false
    fi
    
    check_system_resources
    check_logs_for_errors
    
    echo ""
    echo "========================================"
    
    if $overall_health; then
        log_success "系统整体健康状态: 良好"
        echo -e "${GREEN}✓ 所有核心服务运行正常${NC}"
    else
        log_error "系统整体健康状态: 异常"
        echo -e "${RED}✗ 部分服务存在问题，请检查上述输出${NC}"
    fi
    
    echo "检查完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "========================================"
    
    # 根据参数决定是否生成报告
    if [[ "$1" == "--report" ]]; then
        generate_health_report
    fi
    
    # 返回适当的退出码
    if $overall_health; then
        exit 0
    else
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "TeleShop系统健康检查脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --report    生成详细的健康检查报告文件"
    echo "  --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                # 执行健康检查"
    echo "  $0 --report       # 执行健康检查并生成报告"
}

# 处理命令行参数
case "$1" in
    "--help")
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
