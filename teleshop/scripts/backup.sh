#!/bin/bash

# TeleShop系统备份脚本
# 作者: TeleShop团队
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BACKUP_DIR="./backups"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
BACKUP_NAME="teleshop_backup_$TIMESTAMP"
MYSQL_CONTAINER="teleshop-mysql"
REDIS_CONTAINER="teleshop-redis"
MYSQL_USER="teleshop"
MYSQL_PASSWORD="teleshop123"
MYSQL_DATABASE="teleshop"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录..."
    
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME"
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME/mysql"
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME/redis"
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME/config"
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME/logs"
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME/volumes"
    
    log_success "备份目录创建完成: $BACKUP_DIR/$BACKUP_NAME"
}

# 备份MySQL数据库
backup_mysql() {
    log_info "备份MySQL数据库..."
    
    # 检查MySQL容器是否运行
    if ! docker ps | grep -q "$MYSQL_CONTAINER"; then
        log_error "MySQL容器未运行，跳过数据库备份"
        return 1
    fi
    
    # 备份主数据库
    log_info "备份TeleShop主数据库..."
    docker exec "$MYSQL_CONTAINER" mysqldump \
        -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        "$MYSQL_DATABASE" > "$BACKUP_DIR/$BACKUP_NAME/mysql/teleshop.sql"
    
    # 备份Nacos数据库
    log_info "备份Nacos配置数据库..."
    docker exec "$MYSQL_CONTAINER" mysqldump \
        -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" \
        --single-transaction \
        "nacos" > "$BACKUP_DIR/$BACKUP_NAME/mysql/nacos.sql"
    
    # 备份所有数据库结构
    log_info "备份数据库结构..."
    docker exec "$MYSQL_CONTAINER" mysqldump \
        -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" \
        --no-data \
        --all-databases > "$BACKUP_DIR/$BACKUP_NAME/mysql/schema.sql"
    
    log_success "MySQL数据库备份完成"
}

# 备份Redis数据
backup_redis() {
    log_info "备份Redis数据..."
    
    # 检查Redis容器是否运行
    if ! docker ps | grep -q "$REDIS_CONTAINER"; then
        log_error "Redis容器未运行，跳过Redis备份"
        return 1
    fi
    
    # 触发Redis保存
    docker exec "$REDIS_CONTAINER" redis-cli -a teleshop123 BGSAVE
    
    # 等待保存完成
    sleep 5
    
    # 复制RDB文件
    docker cp "$REDIS_CONTAINER:/data/dump.rdb" "$BACKUP_DIR/$BACKUP_NAME/redis/"
    
    log_success "Redis数据备份完成"
}

# 备份配置文件
backup_configs() {
    log_info "备份配置文件..."
    
    # 备份Docker Compose文件
    cp docker-compose.yml "$BACKUP_DIR/$BACKUP_NAME/config/"
    
    # 备份环境配置
    if [ -f .env ]; then
        cp .env "$BACKUP_DIR/$BACKUP_NAME/config/"
    fi
    
    # 备份Docker配置目录
    if [ -d docker ]; then
        cp -r docker "$BACKUP_DIR/$BACKUP_NAME/config/"
    fi
    
    # 备份脚本目录
    if [ -d scripts ]; then
        cp -r scripts "$BACKUP_DIR/$BACKUP_NAME/config/"
    fi
    
    log_success "配置文件备份完成"
}

# 备份日志文件
backup_logs() {
    log_info "备份日志文件..."
    
    # 备份应用日志
    if [ -d logs ]; then
        cp -r logs "$BACKUP_DIR/$BACKUP_NAME/"
    fi
    
    # 导出容器日志
    local services=("gateway" "user-service" "product-service" "order-service" "wallet-service" "message-service")
    
    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "$service"; then
            log_info "导出 $service 容器日志..."
            docker-compose logs --no-color "$service" > "$BACKUP_DIR/$BACKUP_NAME/logs/${service}.log" 2>/dev/null || true
        fi
    done
    
    log_success "日志文件备份完成"
}

# 备份Docker卷数据
backup_volumes() {
    log_info "备份Docker卷数据..."
    
    # 获取所有卷
    local volumes=$(docker volume ls --format "{{.Name}}" | grep teleshop || true)
    
    if [ -z "$volumes" ]; then
        log_warning "未找到TeleShop相关的Docker卷"
        return 0
    fi
    
    for volume in $volumes; do
        log_info "备份卷: $volume"
        
        # 创建临时容器来备份卷数据
        docker run --rm \
            -v "$volume:/source:ro" \
            -v "$(pwd)/$BACKUP_DIR/$BACKUP_NAME/volumes:/backup" \
            alpine:latest \
            tar czf "/backup/${volume}.tar.gz" -C /source .
    done
    
    log_success "Docker卷数据备份完成"
}

# 创建备份元数据
create_metadata() {
    log_info "创建备份元数据..."
    
    local metadata_file="$BACKUP_DIR/$BACKUP_NAME/backup_metadata.json"
    
    cat > "$metadata_file" << EOF
{
    "backup_name": "$BACKUP_NAME",
    "backup_time": "$(date -Iseconds)",
    "backup_version": "1.0.0",
    "system_info": {
        "hostname": "$(hostname)",
        "os": "$(uname -s)",
        "kernel": "$(uname -r)",
        "architecture": "$(uname -m)"
    },
    "docker_info": {
        "docker_version": "$(docker --version)",
        "compose_version": "$(docker-compose --version)"
    },
    "services": [
$(docker-compose ps --services | sed 's/^/        "/' | sed 's/$/",/' | sed '$ s/,$//')
    ],
    "volumes": [
$(docker volume ls --format "{{.Name}}" | grep teleshop | sed 's/^/        "/' | sed 's/$/",/' | sed '$ s/,$//' || echo "")
    ]
}
EOF
    
    log_success "备份元数据创建完成"
}

# 压缩备份
compress_backup() {
    log_info "压缩备份文件..."
    
    cd "$BACKUP_DIR"
    tar czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
    
    # 计算文件大小和校验和
    local size=$(du -h "${BACKUP_NAME}.tar.gz" | cut -f1)
    local checksum=$(sha256sum "${BACKUP_NAME}.tar.gz" | cut -d' ' -f1)
    
    # 创建校验和文件
    echo "$checksum  ${BACKUP_NAME}.tar.gz" > "${BACKUP_NAME}.sha256"
    
    # 删除未压缩的目录
    rm -rf "$BACKUP_NAME"
    
    cd - > /dev/null
    
    log_success "备份压缩完成: ${BACKUP_NAME}.tar.gz (大小: $size)"
    log_info "校验和: $checksum"
}

# 清理旧备份
cleanup_old_backups() {
    local keep_days=${1:-7}
    
    log_info "清理 $keep_days 天前的备份文件..."
    
    if [ -d "$BACKUP_DIR" ]; then
        find "$BACKUP_DIR" -name "teleshop_backup_*.tar.gz" -mtime +$keep_days -delete
        find "$BACKUP_DIR" -name "teleshop_backup_*.sha256" -mtime +$keep_days -delete
        
        local remaining=$(find "$BACKUP_DIR" -name "teleshop_backup_*.tar.gz" | wc -l)
        log_success "清理完成，剩余备份文件: $remaining 个"
    fi
}

# 验证备份
verify_backup() {
    log_info "验证备份文件..."
    
    local backup_file="$BACKUP_DIR/${BACKUP_NAME}.tar.gz"
    local checksum_file="$BACKUP_DIR/${BACKUP_NAME}.sha256"
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    if [ ! -f "$checksum_file" ]; then
        log_error "校验和文件不存在: $checksum_file"
        return 1
    fi
    
    # 验证校验和
    cd "$BACKUP_DIR"
    if sha256sum -c "${BACKUP_NAME}.sha256" > /dev/null 2>&1; then
        log_success "备份文件校验通过"
        cd - > /dev/null
        return 0
    else
        log_error "备份文件校验失败"
        cd - > /dev/null
        return 1
    fi
}

# 列出备份文件
list_backups() {
    log_info "现有备份文件列表:"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warning "备份目录不存在"
        return 0
    fi
    
    echo ""
    printf "%-30s %-15s %-20s\n" "备份文件" "大小" "创建时间"
    echo "================================================================="
    
    find "$BACKUP_DIR" -name "teleshop_backup_*.tar.gz" -printf "%f %s %TY-%Tm-%Td %TH:%TM\n" | \
    while read filename size datetime; do
        local human_size=$(numfmt --to=iec-i --suffix=B $size)
        printf "%-30s %-15s %-20s\n" "$filename" "$human_size" "$datetime"
    done
    
    echo ""
}

# 主备份函数
perform_backup() {
    log_info "开始TeleShop系统备份..."
    echo "备份时间: $(date)"
    echo "备份名称: $BACKUP_NAME"
    echo ""
    
    # 执行备份步骤
    create_backup_dir
    backup_mysql
    backup_redis
    backup_configs
    backup_logs
    backup_volumes
    create_metadata
    compress_backup
    
    # 验证备份
    if verify_backup; then
        log_success "备份完成并验证通过"
        echo ""
        echo "备份文件: $BACKUP_DIR/${BACKUP_NAME}.tar.gz"
        echo "校验文件: $BACKUP_DIR/${BACKUP_NAME}.sha256"
    else
        log_error "备份验证失败"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "TeleShop系统备份脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  backup              执行完整备份"
    echo "  list                列出现有备份"
    echo "  cleanup [天数]      清理指定天数前的备份 (默认7天)"
    echo "  verify <备份名>     验证指定备份文件"
    echo ""
    echo "示例:"
    echo "  $0 backup                           # 执行完整备份"
    echo "  $0 list                             # 列出备份文件"
    echo "  $0 cleanup 30                       # 清理30天前的备份"
    echo "  $0 verify teleshop_backup_20240101  # 验证指定备份"
}

# 主函数
main() {
    case "$1" in
        "backup")
            perform_backup
            cleanup_old_backups 7
            ;;
        "list")
            list_backups
            ;;
        "cleanup")
            cleanup_old_backups "${2:-7}"
            ;;
        "verify")
            if [ -z "$2" ]; then
                log_error "请指定要验证的备份名称"
                exit 1
            fi
            BACKUP_NAME="$2"
            verify_backup
            ;;
        *)
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
