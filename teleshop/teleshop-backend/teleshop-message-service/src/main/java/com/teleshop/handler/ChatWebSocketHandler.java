package com.teleshop.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teleshop.dto.WebSocketMessage;
import com.teleshop.service.MessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 聊天WebSocket处理器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChatWebSocketHandler implements WebSocketHandler {

    private final MessageService messageService;
    private final ObjectMapper objectMapper;

    // 存储用户会话
    private static final ConcurrentHashMap<Long, WebSocketSession> USER_SESSIONS = new ConcurrentHashMap<>();
    
    // 存储群组会话
    private static final ConcurrentHashMap<Long, CopyOnWriteArraySet<WebSocketSession>> GROUP_SESSIONS = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("WebSocket连接建立: {}", session.getId());
        
        // 从session中获取用户ID
        Long userId = getUserIdFromSession(session);
        if (userId != null) {
            USER_SESSIONS.put(userId, session);
            log.info("用户{}连接成功", userId);
            
            // 发送连接成功消息
            sendMessage(session, WebSocketMessage.builder()
                    .type("CONNECT_SUCCESS")
                    .content("连接成功")
                    .timestamp(System.currentTimeMillis())
                    .build());
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        try {
            String payload = message.getPayload().toString();
            WebSocketMessage wsMessage = objectMapper.readValue(payload, WebSocketMessage.class);
            
            log.info("收到WebSocket消息: {}", wsMessage);
            
            Long userId = getUserIdFromSession(session);
            if (userId == null) {
                log.warn("未找到用户ID，忽略消息");
                return;
            }
            
            // 根据消息类型处理
            switch (wsMessage.getType()) {
                case "PRIVATE_MESSAGE":
                    handlePrivateMessage(userId, wsMessage);
                    break;
                case "GROUP_MESSAGE":
                    handleGroupMessage(userId, wsMessage);
                    break;
                case "JOIN_GROUP":
                    handleJoinGroup(session, userId, wsMessage);
                    break;
                case "LEAVE_GROUP":
                    handleLeaveGroup(session, userId, wsMessage);
                    break;
                case "TYPING":
                    handleTyping(userId, wsMessage);
                    break;
                case "READ_MESSAGE":
                    handleReadMessage(userId, wsMessage);
                    break;
                case "HEARTBEAT":
                    handleHeartbeat(session);
                    break;
                default:
                    log.warn("未知消息类型: {}", wsMessage.getType());
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息失败", e);
            sendErrorMessage(session, "消息处理失败: " + e.getMessage());
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket传输错误: {}", session.getId(), exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        log.info("WebSocket连接关闭: {}, 状态: {}", session.getId(), closeStatus);
        
        Long userId = getUserIdFromSession(session);
        if (userId != null) {
            USER_SESSIONS.remove(userId);
            
            // 从所有群组中移除该用户
            GROUP_SESSIONS.values().forEach(sessions -> sessions.remove(session));
            
            log.info("用户{}断开连接", userId);
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 处理私聊消息
     */
    private void handlePrivateMessage(Long senderId, WebSocketMessage message) {
        try {
            // 保存消息到数据库
            Long messageId = messageService.savePrivateMessage(senderId, message);
            
            // 发送给接收者
            Long receiverId = (Long) message.getData().get("receiverId");
            WebSocketSession receiverSession = USER_SESSIONS.get(receiverId);
            
            if (receiverSession != null && receiverSession.isOpen()) {
                message.getData().put("messageId", messageId);
                message.getData().put("senderId", senderId);
                sendMessage(receiverSession, message);
            }
            
            // 发送确认消息给发送者
            WebSocketSession senderSession = USER_SESSIONS.get(senderId);
            if (senderSession != null && senderSession.isOpen()) {
                sendMessage(senderSession, WebSocketMessage.builder()
                        .type("MESSAGE_SENT")
                        .data(Map.of("messageId", messageId, "status", "sent"))
                        .timestamp(System.currentTimeMillis())
                        .build());
            }
            
        } catch (Exception e) {
            log.error("处理私聊消息失败", e);
        }
    }

    /**
     * 处理群聊消息
     */
    private void handleGroupMessage(Long senderId, WebSocketMessage message) {
        try {
            // 保存消息到数据库
            Long messageId = messageService.saveGroupMessage(senderId, message);
            
            // 发送给群组所有成员
            Long groupId = (Long) message.getData().get("groupId");
            CopyOnWriteArraySet<WebSocketSession> groupSessions = GROUP_SESSIONS.get(groupId);
            
            if (groupSessions != null) {
                message.getData().put("messageId", messageId);
                message.getData().put("senderId", senderId);
                
                groupSessions.forEach(session -> {
                    if (session.isOpen()) {
                        try {
                            sendMessage(session, message);
                        } catch (Exception e) {
                            log.error("发送群聊消息失败", e);
                        }
                    }
                });
            }
            
        } catch (Exception e) {
            log.error("处理群聊消息失败", e);
        }
    }

    /**
     * 处理加入群组
     */
    private void handleJoinGroup(WebSocketSession session, Long userId, WebSocketMessage message) {
        Long groupId = (Long) message.getData().get("groupId");
        GROUP_SESSIONS.computeIfAbsent(groupId, k -> new CopyOnWriteArraySet<>()).add(session);
        
        log.info("用户{}加入群组{}", userId, groupId);
        
        // 通知群组其他成员
        broadcastToGroup(groupId, WebSocketMessage.builder()
                .type("USER_JOINED")
                .data(Map.of("userId", userId, "groupId", groupId))
                .timestamp(System.currentTimeMillis())
                .build(), session);
    }

    /**
     * 处理离开群组
     */
    private void handleLeaveGroup(WebSocketSession session, Long userId, WebSocketMessage message) {
        Long groupId = (Long) message.getData().get("groupId");
        CopyOnWriteArraySet<WebSocketSession> groupSessions = GROUP_SESSIONS.get(groupId);
        
        if (groupSessions != null) {
            groupSessions.remove(session);
            if (groupSessions.isEmpty()) {
                GROUP_SESSIONS.remove(groupId);
            }
        }
        
        log.info("用户{}离开群组{}", userId, groupId);
        
        // 通知群组其他成员
        broadcastToGroup(groupId, WebSocketMessage.builder()
                .type("USER_LEFT")
                .data(Map.of("userId", userId, "groupId", groupId))
                .timestamp(System.currentTimeMillis())
                .build(), session);
    }

    /**
     * 处理正在输入状态
     */
    private void handleTyping(Long userId, WebSocketMessage message) {
        String chatType = (String) message.getData().get("chatType");
        
        if ("private".equals(chatType)) {
            Long receiverId = (Long) message.getData().get("receiverId");
            WebSocketSession receiverSession = USER_SESSIONS.get(receiverId);
            if (receiverSession != null && receiverSession.isOpen()) {
                sendMessage(receiverSession, WebSocketMessage.builder()
                        .type("TYPING")
                        .data(Map.of("senderId", userId, "isTyping", message.getData().get("isTyping")))
                        .timestamp(System.currentTimeMillis())
                        .build());
            }
        } else if ("group".equals(chatType)) {
            Long groupId = (Long) message.getData().get("groupId");
            broadcastToGroup(groupId, WebSocketMessage.builder()
                    .type("TYPING")
                    .data(Map.of("senderId", userId, "groupId", groupId, "isTyping", message.getData().get("isTyping")))
                    .timestamp(System.currentTimeMillis())
                    .build(), USER_SESSIONS.get(userId));
        }
    }

    /**
     * 处理消息已读
     */
    private void handleReadMessage(Long userId, WebSocketMessage message) {
        try {
            Long messageId = (Long) message.getData().get("messageId");
            messageService.markMessageAsRead(messageId, userId);
            
            // 通知发送者消息已读
            // 这里需要根据消息ID查询发送者，然后发送已读回执
            
        } catch (Exception e) {
            log.error("处理消息已读失败", e);
        }
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat(WebSocketSession session) {
        sendMessage(session, WebSocketMessage.builder()
                .type("HEARTBEAT_RESPONSE")
                .timestamp(System.currentTimeMillis())
                .build());
    }

    /**
     * 向群组广播消息
     */
    private void broadcastToGroup(Long groupId, WebSocketMessage message, WebSocketSession excludeSession) {
        CopyOnWriteArraySet<WebSocketSession> groupSessions = GROUP_SESSIONS.get(groupId);
        if (groupSessions != null) {
            groupSessions.forEach(session -> {
                if (session.isOpen() && !session.equals(excludeSession)) {
                    try {
                        sendMessage(session, message);
                    } catch (Exception e) {
                        log.error("广播群组消息失败", e);
                    }
                }
            });
        }
    }

    /**
     * 发送消息
     */
    private void sendMessage(WebSocketSession session, WebSocketMessage message) {
        try {
            if (session.isOpen()) {
                String json = objectMapper.writeValueAsString(message);
                session.sendMessage(new TextMessage(json));
            }
        } catch (IOException e) {
            log.error("发送WebSocket消息失败", e);
        }
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String error) {
        sendMessage(session, WebSocketMessage.builder()
                .type("ERROR")
                .content(error)
                .timestamp(System.currentTimeMillis())
                .build());
    }

    /**
     * 从session中获取用户ID
     */
    private Long getUserIdFromSession(WebSocketSession session) {
        Object userId = session.getAttributes().get("userId");
        return userId != null ? (Long) userId : null;
    }

    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return USER_SESSIONS.size();
    }

    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(Long userId) {
        WebSocketSession session = USER_SESSIONS.get(userId);
        return session != null && session.isOpen();
    }

    /**
     * 向指定用户发送消息
     */
    public void sendMessageToUser(Long userId, WebSocketMessage message) {
        WebSocketSession session = USER_SESSIONS.get(userId);
        if (session != null && session.isOpen()) {
            sendMessage(session, message);
        }
    }
}
