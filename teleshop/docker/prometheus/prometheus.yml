# Prometheus配置文件
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'teleshop'
    replica: 'prometheus-1'

# 规则文件
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 15s

  # Spring Boot Actuator监控
  - job_name: 'teleshop-gateway'
    static_configs:
      - targets: ['gateway:8080']
    metrics_path: /actuator/prometheus
    scrape_interval: 15s
    scrape_timeout: 10s

  - job_name: 'teleshop-user-service'
    static_configs:
      - targets: ['user-service:8081']
    metrics_path: /actuator/prometheus
    scrape_interval: 15s
    scrape_timeout: 10s

  - job_name: 'teleshop-product-service'
    static_configs:
      - targets: ['product-service:8082']
    metrics_path: /actuator/prometheus
    scrape_interval: 15s
    scrape_timeout: 10s

  - job_name: 'teleshop-order-service'
    static_configs:
      - targets: ['order-service:8083']
    metrics_path: /actuator/prometheus
    scrape_interval: 15s
    scrape_timeout: 10s

  - job_name: 'teleshop-wallet-service'
    static_configs:
      - targets: ['wallet-service:8084']
    metrics_path: /actuator/prometheus
    scrape_interval: 15s
    scrape_timeout: 10s

  - job_name: 'teleshop-message-service'
    static_configs:
      - targets: ['message-service:8085']
    metrics_path: /actuator/prometheus
    scrape_interval: 15s
    scrape_timeout: 10s

  - job_name: 'teleshop-merchant-service'
    static_configs:
      - targets: ['merchant-service:8086']
    metrics_path: /actuator/prometheus
    scrape_interval: 15s
    scrape_timeout: 10s

  # MySQL监控
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']
    scrape_interval: 30s

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # RabbitMQ监控
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']
    metrics_path: /metrics
    scrape_interval: 30s

  # Elasticsearch监控
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch-exporter:9114']
    scrape_interval: 30s

  # Node Exporter监控（系统指标）
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # cAdvisor监控（容器指标）
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Nacos监控
  - job_name: 'nacos'
    static_configs:
      - targets: ['nacos:8848']
    metrics_path: /nacos/actuator/prometheus
    scrape_interval: 30s

  # MinIO监控
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: /minio/v2/metrics/cluster
    scrape_interval: 30s

  # Jaeger监控
  - job_name: 'jaeger'
    static_configs:
      - targets: ['jaeger:14269']
    metrics_path: /metrics
    scrape_interval: 30s

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-storage:9201/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"
