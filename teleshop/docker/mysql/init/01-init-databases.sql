-- TeleShop数据库初始化脚本
-- 创建数据库和用户

-- 创建TeleShop主数据库
CREATE DATABASE IF NOT EXISTS `teleshop` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建Nacos配置中心数据库
CREATE DATABASE IF NOT EXISTS `nacos` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER IF NOT EXISTS 'teleshop'@'%' IDENTIFIED BY 'teleshop123';
GRANT ALL PRIVILEGES ON `teleshop`.* TO 'teleshop'@'%';
GRANT ALL PRIVILEGES ON `nacos`.* TO 'teleshop'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 使用TeleShop数据库
USE `teleshop`;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `t_user` (
    `user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码',
    `nickname` VARCHAR(50) COMMENT '昵称',
    `real_name` VARCHAR(50) COMMENT '真实姓名',
    `avatar` VARCHAR(500) COMMENT '头像URL',
    `gender` TINYINT COMMENT '性别：1-男，2-女，0-未知',
    `birthday` DATE COMMENT '生日',
    `phone` VARCHAR(20) COMMENT '手机号',
    `email` VARCHAR(100) COMMENT '邮箱',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-禁用',
    `level` INT NOT NULL DEFAULT 1 COMMENT '用户等级',
    `experience` INT NOT NULL DEFAULT 0 COMMENT '经验值',
    `points` INT NOT NULL DEFAULT 0 COMMENT '积分',
    `last_login_time` DATETIME COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(50) COMMENT '最后登录IP',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`user_id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_phone` (`phone`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建商品分类表
CREATE TABLE IF NOT EXISTS `t_category` (
    `category_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    `category_name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    `parent_id` BIGINT COMMENT '父分类ID',
    `level` TINYINT NOT NULL DEFAULT 1 COMMENT '分类层级',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序',
    `icon` VARCHAR(500) COMMENT '图标',
    `image` VARCHAR(500) COMMENT '图片',
    `description` TEXT COMMENT '描述',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，2-禁用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`category_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_level` (`level`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 创建商品表
CREATE TABLE IF NOT EXISTS `t_product` (
    `product_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '商品ID',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `sub_title` VARCHAR(500) COMMENT '副标题',
    `description` TEXT COMMENT '商品描述',
    `category_id` BIGINT NOT NULL COMMENT '分类ID',
    `brand_id` BIGINT COMMENT '品牌ID',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `product_code` VARCHAR(100) COMMENT '商品编码',
    `main_image` VARCHAR(500) COMMENT '主图',
    `images` TEXT COMMENT '商品图片（JSON数组）',
    `video_url` VARCHAR(500) COMMENT '视频URL',
    `original_price` DECIMAL(10,2) COMMENT '原价',
    `current_price` DECIMAL(10,2) NOT NULL COMMENT '现价',
    `stock_quantity` INT NOT NULL DEFAULT 0 COMMENT '库存数量',
    `sales_count` INT NOT NULL DEFAULT 0 COMMENT '销量',
    `view_count` INT NOT NULL DEFAULT 0 COMMENT '浏览量',
    `favorite_count` INT NOT NULL DEFAULT 0 COMMENT '收藏数',
    `comment_count` INT NOT NULL DEFAULT 0 COMMENT '评论数',
    `rating` DECIMAL(3,2) NOT NULL DEFAULT 5.00 COMMENT '评分',
    `weight` INT COMMENT '重量（克）',
    `dimensions` VARCHAR(100) COMMENT '尺寸',
    `tags` TEXT COMMENT '标签（JSON数组）',
    `is_recommended` TINYINT NOT NULL DEFAULT 0 COMMENT '是否推荐：0-否，1-是',
    `is_new` TINYINT NOT NULL DEFAULT 0 COMMENT '是否新品：0-否，1-是',
    `is_hot` TINYINT NOT NULL DEFAULT 0 COMMENT '是否热销：0-否，1-是',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-上架，2-下架，3-删除',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`product_id`),
    UNIQUE KEY `uk_product_code` (`product_code`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_brand_id` (`brand_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_status` (`status`),
    KEY `idx_is_recommended` (`is_recommended`),
    KEY `idx_is_new` (`is_new`),
    KEY `idx_is_hot` (`is_hot`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 创建订单表
CREATE TABLE IF NOT EXISTS `t_order` (
    `order_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '订单ID',
    `order_no` VARCHAR(64) NOT NULL COMMENT '订单号',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `merchant_id` BIGINT NOT NULL COMMENT '商家ID',
    `order_type` TINYINT NOT NULL DEFAULT 1 COMMENT '订单类型：1-普通订单，2-拼团订单，3-秒杀订单',
    `order_source` TINYINT NOT NULL DEFAULT 1 COMMENT '订单来源：1-APP，2-H5，3-小程序，4-PC',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '订单状态：1-待付款，2-待发货，3-待收货，4-待评价，5-已完成，6-已取消',
    `product_amount` DECIMAL(10,2) NOT NULL COMMENT '商品总金额',
    `shipping_fee` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '运费',
    `discount_amount` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '优惠金额',
    `actual_amount` DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    `payment_method` TINYINT NOT NULL DEFAULT 1 COMMENT '支付方式：1-余额支付，2-微信支付，3-支付宝',
    `payment_status` TINYINT NOT NULL DEFAULT 1 COMMENT '支付状态：1-未支付，2-已支付，3-支付失败',
    `payment_time` DATETIME COMMENT '支付时间',
    `receiver_name` VARCHAR(50) NOT NULL COMMENT '收货人姓名',
    `receiver_phone` VARCHAR(20) NOT NULL COMMENT '收货人手机号',
    `receiver_address` VARCHAR(500) NOT NULL COMMENT '收货地址',
    `logistics_company` VARCHAR(100) COMMENT '物流公司',
    `logistics_no` VARCHAR(100) COMMENT '物流单号',
    `ship_time` DATETIME COMMENT '发货时间',
    `receive_time` DATETIME COMMENT '收货时间',
    `complete_time` DATETIME COMMENT '完成时间',
    `cancel_time` DATETIME COMMENT '取消时间',
    `cancel_reason` VARCHAR(500) COMMENT '取消原因',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`order_id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 创建钱包表
CREATE TABLE IF NOT EXISTS `t_wallet` (
    `wallet_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '钱包ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `wallet_type` TINYINT NOT NULL COMMENT '钱包类型：1-余额钱包，2-积分钱包，3-U币钱包',
    `wallet_name` VARCHAR(100) COMMENT '钱包名称',
    `available_balance` DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '可用余额',
    `frozen_balance` DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '冻结余额',
    `total_balance` DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '总余额',
    `wallet_address` VARCHAR(200) COMMENT '钱包地址',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-冻结，3-禁用',
    `is_default` TINYINT NOT NULL DEFAULT 0 COMMENT '是否默认钱包：0-否，1-是',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`wallet_id`),
    UNIQUE KEY `uk_user_wallet_type` (`user_id`, `wallet_type`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_wallet_type` (`wallet_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='钱包表';

-- 插入初始数据
INSERT INTO `t_user` (`username`, `password`, `nickname`, `phone`, `email`, `status`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '管理员', '13800138000', '<EMAIL>', 1),
('test', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '测试用户', '13800138001', '<EMAIL>', 1);

INSERT INTO `t_category` (`category_name`, `parent_id`, `level`, `sort_order`, `status`) VALUES
('电子产品', NULL, 1, 1, 1),
('服装鞋帽', NULL, 1, 2, 1),
('家居用品', NULL, 1, 3, 1),
('食品饮料', NULL, 1, 4, 1),
('图书音像', NULL, 1, 5, 1);

-- 使用Nacos数据库并创建表
USE `nacos`;

-- Nacos配置表
CREATE TABLE IF NOT EXISTS `config_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) DEFAULT NULL,
  `content` longtext NOT NULL COMMENT 'content',
  `md5` varchar(32) DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text COMMENT 'source user',
  `src_ip` varchar(50) DEFAULT NULL COMMENT 'source ip',
  `app_name` varchar(128) DEFAULT NULL,
  `tenant_id` varchar(128) DEFAULT '' COMMENT '租户字段',
  `c_desc` varchar(256) DEFAULT NULL,
  `c_use` varchar(64) DEFAULT NULL,
  `effect` varchar(64) DEFAULT NULL,
  `type` varchar(64) DEFAULT NULL,
  `c_schema` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfo_datagrouptenant` (`data_id`,`group_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='config_info';

-- 其他Nacos必要的表（简化版）
CREATE TABLE IF NOT EXISTS `config_info_aggr` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) NOT NULL COMMENT 'group_id',
  `datum_id` varchar(255) NOT NULL COMMENT 'datum_id',
  `content` longtext NOT NULL COMMENT '内容',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `app_name` varchar(128) DEFAULT NULL,
  `tenant_id` varchar(128) DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfoaggr_datagrouptenantdatum` (`data_id`,`group_id`,`tenant_id`,`datum_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='增加租户字段';

CREATE TABLE IF NOT EXISTS `config_info_beta` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) DEFAULT NULL COMMENT 'app_name',
  `content` longtext NOT NULL COMMENT 'content',
  `beta_ips` varchar(1024) DEFAULT NULL COMMENT 'betaIps',
  `md5` varchar(32) DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text COMMENT 'source user',
  `src_ip` varchar(50) DEFAULT NULL COMMENT 'source ip',
  `tenant_id` varchar(128) DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfobeta_datagrouptenant` (`data_id`,`group_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='config_info_beta';

-- 完成初始化
SELECT 'TeleShop数据库初始化完成' AS message;
