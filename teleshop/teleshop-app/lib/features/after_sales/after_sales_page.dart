import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:teleshop_app/features/after_sales/after_sales_provider.dart';
import 'package:teleshop_app/features/after_sales/models/after_sales_models.dart';
import 'package:teleshop_app/widgets/common/loading_widget.dart';
import 'package:teleshop_app/widgets/common/error_widget.dart';

/// 售后服务页面
/// 支持退货申请、换货申请、退款跟踪、售后评价、维权投诉等完整售后服务功能
class AfterSalesPage extends ConsumerStatefulWidget {
  final String? orderId;

  const AfterSalesPage({super.key, this.orderId});

  @override
  ConsumerState<AfterSalesPage> createState() => _AfterSalesPageState();
}

class _AfterSalesPageState extends ConsumerState<AfterSalesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // 加载售后数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(afterSalesProvider.notifier).loadAfterSalesData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final afterSalesState = ref.watch(afterSalesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('售后服务'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: '申请售后'),
            Tab(text: '售后记录'),
            Tab(text: '帮助中心'),
          ],
        ),
      ),
      body: afterSalesState.isLoading
          ? const LoadingWidget()
          : afterSalesState.error != null
              ? ErrorWidget(
                  message: afterSalesState.error!,
                  onRetry: () => ref.read(afterSalesProvider.notifier).loadAfterSalesData(),
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildApplyTab(afterSalesState),
                    _buildRecordsTab(afterSalesState),
                    _buildHelpTab(),
                  ],
                ),
    );
  }

  /// 构建申请售后标签页
  Widget _buildApplyTab(AfterSalesState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 快速申请入口
          _buildQuickApplySection(),

          const SizedBox(height: 24),

          // 可申请售后的订单
          _buildEligibleOrdersSection(state.eligibleOrders),

          const SizedBox(height: 24),

          // 售后政策说明
          _buildPolicySection(),
        ],
      ),
    );
  }

  /// 构建快速申请区域
  Widget _buildQuickApplySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '快速申请',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickApplyButton(
                    icon: Icons.keyboard_return,
                    title: '申请退货',
                    subtitle: '商品有质量问题',
                    color: Colors.red,
                    onTap: () => _showReturnApplication(),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickApplyButton(
                    icon: Icons.swap_horiz,
                    title: '申请换货',
                    subtitle: '商品规格不符',
                    color: Colors.orange,
                    onTap: () => _showExchangeApplication(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildQuickApplyButton(
                    icon: Icons.account_balance_wallet,
                    title: '申请退款',
                    subtitle: '仅退款不退货',
                    color: Colors.blue,
                    onTap: () => _showRefundApplication(),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickApplyButton(
                    icon: Icons.report_problem,
                    title: '投诉维权',
                    subtitle: '商家服务问题',
                    color: Colors.purple,
                    onTap: () => _showComplaintApplication(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建快速申请按钮
  Widget _buildQuickApplyButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建可申请售后的订单区域
  Widget _buildEligibleOrdersSection(List<OrderInfo> eligibleOrders) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '可申请售后的订单',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        if (eligibleOrders.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.inbox_outlined,
                      size: 48,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '暂无可申请售后的订单',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
        else
          ...eligibleOrders.map((order) => _buildEligibleOrderCard(order)),
      ],
    );
  }

  /// 构建可申请售后的订单卡片
  Widget _buildEligibleOrderCard(OrderInfo order) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '订单号：${order.orderNumber}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getOrderStatusColor(order.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getOrderStatusText(order.status),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getOrderStatusColor(order.status),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...order.items.map((item) => _buildOrderItemRow(order, item)),
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  '订单金额：¥${order.totalAmount.toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 14),
                ),
                const Spacer(),
                Text(
                  '下单时间：${_formatDateTime(order.createTime)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建订单商品行
  Widget _buildOrderItemRow(OrderInfo order, OrderItem item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.network(
              item.product.imageUrl,
              width: 40,
              height: 40,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.name,
                  style: const TextStyle(fontSize: 14),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '¥${item.price.toStringAsFixed(2)} × ${item.quantity}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () => _showAfterSalesApplication(order, item),
            child: const Text('申请售后', style: TextStyle(fontSize: 12)),
          ),
        ],
      ),
    );
  }

  /// 构建售后记录标签页
  Widget _buildRecordsTab(AfterSalesState state) {
    return state.afterSalesRecords.isEmpty
        ? _buildEmptyRecords()
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: state.afterSalesRecords.length,
            itemBuilder: (context, index) {
              final record = state.afterSalesRecords[index];
              return _buildAfterSalesRecordCard(record);
            },
          );
  }

  /// 构建空记录状态
  Widget _buildEmptyRecords() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '暂无售后记录',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '您还没有申请过售后服务',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建售后记录卡片
  Widget _buildAfterSalesRecordCard(AfterSalesRecord record) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '售后单号：${record.afterSalesNumber}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getAfterSalesStatusColor(record.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getAfterSalesStatusText(record.status),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getAfterSalesStatusColor(record.status),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Image.network(
                    record.product.imageUrl,
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        record.product.name,
                        style: const TextStyle(fontSize: 14),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '售后类型：${_getAfterSalesTypeText(record.type)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      Text(
                        '申请时间：${_formatDateTime(record.createTime)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                if (record.refundAmount > 0)
                  Text(
                    '退款金额：¥${record.refundAmount.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showAfterSalesDetail(record),
                  child: const Text('查看详情'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }