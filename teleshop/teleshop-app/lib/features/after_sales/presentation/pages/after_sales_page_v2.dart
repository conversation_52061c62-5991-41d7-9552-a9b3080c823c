import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:teleshop_app/features/after_sales/presentation/providers/after_sales_provider.dart';
import 'package:teleshop_app/features/after_sales/presentation/widgets/after_sales_widgets.dart';
import 'package:teleshop_app/features/after_sales/domain/entities/after_sales.dart';
import 'package:teleshop_app/widgets/common/loading_widget.dart';
import 'package:teleshop_app/widgets/common/error_widget.dart';

/// 售后服务页面 V2
/// 支持完整的售后服务功能：退货申请、换货申请、退款跟踪、售后评价、维权投诉等
class AfterSalesPageV2 extends ConsumerStatefulWidget {
  final String? orderId;

  const AfterSalesPageV2({super.key, this.orderId});

  @override
  ConsumerState<AfterSalesPageV2> createState() => _AfterSalesPageV2State();
}

class _AfterSalesPageV2State extends ConsumerState<AfterSalesPageV2>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _scrollController.addListener(_onScroll);
    
    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAfterSalesData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreAfterSalesRecords();
    }
  }

  void _loadAfterSalesData() {
    final notifier = ref.read(afterSalesProvider.notifier);
    notifier.loadEligibleOrders();
    notifier.loadAfterSalesRecords();
    notifier.loadAfterSalesPolicy();
    notifier.loadAfterSalesHelp();
  }

  void _loadMoreAfterSalesRecords() {
    final notifier = ref.read(afterSalesProvider.notifier);
    notifier.loadMoreAfterSalesRecords();
  }

  @override
  Widget build(BuildContext context) {
    final afterSalesState = ref.watch(afterSalesProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: afterSalesState.isLoading && afterSalesState.isEmpty
          ? const LoadingWidget()
          : afterSalesState.error != null && afterSalesState.isEmpty
              ? ErrorWidget(
                  message: afterSalesState.error!,
                  onRetry: _loadAfterSalesData,
                )
              : _buildContent(afterSalesState),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('售后服务'),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      bottom: TabBar(
        controller: _tabController,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        indicatorColor: Colors.white,
        indicatorWeight: 3,
        tabs: const [
          Tab(text: '申请售后'),
          Tab(text: '售后记录'),
          Tab(text: '进度跟踪'),
          Tab(text: '帮助中心'),
        ],
      ),
    );
  }

  Widget _buildContent(AfterSalesState state) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildApplyTab(state),
        _buildRecordsTab(state),
        _buildProgressTab(state),
        _buildHelpTab(state),
      ],
    );
  }

  Widget _buildApplyTab(AfterSalesState state) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.read(afterSalesProvider.notifier).loadEligibleOrders();
      },
      child: CustomScrollView(
        slivers: [
          // 快速申请入口
          SliverToBoxAdapter(child: _buildQuickApplySection()),

          // 可申请售后的订单
          SliverToBoxAdapter(child: _buildEligibleOrdersSection(state.eligibleOrders)),

          // 售后政策说明
          SliverToBoxAdapter(child: _buildPolicySection(state.policy)),

          SliverToBoxAdapter(child: SizedBox(height: 20.h)),
        ],
      ),
    );
  }

  Widget _buildQuickApplySection() {
    return Container(
      margin: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快速申请',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildQuickApplyButton(
                  icon: Icons.keyboard_return,
                  title: '申请退货',
                  subtitle: '商品有质量问题',
                  color: Colors.blue,
                  onTap: () => _showReturnApplication(),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildQuickApplyButton(
                  icon: Icons.swap_horiz,
                  title: '申请换货',
                  subtitle: '商品规格不符',
                  color: Colors.green,
                  onTap: () => _showExchangeApplication(),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildQuickApplyButton(
                  icon: Icons.account_balance_wallet,
                  title: '申请退款',
                  subtitle: '仅退款不退货',
                  color: Colors.orange,
                  onTap: () => _showRefundApplication(),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildQuickApplyButton(
                  icon: Icons.report_problem,
                  title: '投诉维权',
                  subtitle: '商家服务问题',
                  color: Colors.red,
                  onTap: () => _showComplaintApplication(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickApplyButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: color.withOpacity(0.3)),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32.sp),
            SizedBox(height: 8.h),
            Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEligibleOrdersSection(List<EligibleOrder> orders) {
    return Container(
      margin: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '可申请售后的订单',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          if (orders.isEmpty)
            Container(
              padding: EdgeInsets.all(24.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.inbox_outlined,
                      size: 48.sp,
                      color: Colors.grey[400],
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      '暂无可申请售后的订单',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ...orders.map((order) => Container(
              margin: EdgeInsets.only(bottom: 12.h),
              child: EligibleOrderCard(
                order: order,
                onApplyAfterSales: (type) => _applyAfterSales(order, type),
                onViewDetail: () => _viewOrderDetail(order),
              ),
            )),
        ],
      ),
    );
  }

  Widget _buildPolicySection(AfterSalesPolicy? policy) {
    if (policy == null) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.policy, color: Colors.blue.shade600),
              SizedBox(width: 8.w),
              Text(
                '售后政策',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            policy.description,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.blue.shade700,
              height: 1.5,
            ),
          ),
          SizedBox(height: 12.h),
          TextButton(
            onPressed: () => _viewFullPolicy(policy),
            child: Text(
              '查看完整政策',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.blue.shade600,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordsTab(AfterSalesState state) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.read(afterSalesProvider.notifier).loadAfterSalesRecords();
      },
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 统计信息
          SliverToBoxAdapter(child: _buildStatsSection(state.stats)),

          // 售后记录列表
          if (state.records.isEmpty)
            SliverFillRemaining(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.history,
                      size: 64.sp,
                      color: Colors.grey[400],
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      '暂无售后记录',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < state.records.length) {
                    return Container(
                      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      child: AfterSalesRecordCard(
                        record: state.records[index],
                        onViewDetail: () => _viewRecordDetail(state.records[index]),
                        onTrackProgress: () => _trackProgress(state.records[index]),
                        onContactService: () => _contactService(state.records[index]),
                        onSubmitReview: () => _submitReview(state.records[index]),
                      ),
                    );
                  } else {
                    return const LoadingWidget();
                  }
                },
                childCount: state.records.length + (state.isLoadingMore ? 1 : 0),
              ),
            ),

          SliverToBoxAdapter(child: SizedBox(height: 20.h)),
        ],
      ),
    );
  }

  Widget _buildStatsSection(Map<String, dynamic> stats) {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              '总申请',
              '${stats['totalApplications'] ?? 0}',
              Colors.blue,
            ),
          ),
          Expanded(
            child: _buildStatItem(
              '处理中',
              '${stats['processingApplications'] ?? 0}',
              Colors.orange,
            ),
          ),
          Expanded(
            child: _buildStatItem(
              '已完成',
              '${stats['completedApplications'] ?? 0}',
              Colors.green,
            ),
          ),
          Expanded(
            child: _buildStatItem(
              '满意度',
              '${stats['satisfactionRate'] ?? 0}%',
              Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressTab(AfterSalesState state) {
    // 进度跟踪标签页实现
    return const Center(
      child: Text('进度跟踪功能开发中'),
    );
  }

  Widget _buildHelpTab(AfterSalesState state) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.read(afterSalesProvider.notifier).loadAfterSalesHelp();
      },
      child: ListView(
        padding: EdgeInsets.all(16.w),
        children: [
          // 常见问题
          _buildHelpSection(
            '常见问题',
            Icons.help_outline,
            state.helpItems.where((item) => item.category == 'faq').toList(),
          ),
          
          SizedBox(height: 20.h),
          
          // 操作指南
          _buildHelpSection(
            '操作指南',
            Icons.menu_book,
            state.helpItems.where((item) => item.category == 'guide').toList(),
          ),
          
          SizedBox(height: 20.h),
          
          // 联系客服
          _buildContactSection(),
        ],
      ),
    );
  }

  Widget _buildHelpSection(String title, IconData icon, List<AfterSalesHelp> items) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Icon(icon, color: Theme.of(context).primaryColor),
                SizedBox(width: 8.w),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          ...items.map((item) => ListTile(
            title: Text(item.title),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _viewHelpDetail(item),
          )),
        ],
      ),
    );
  }

  Widget _buildContactSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '联系客服',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _startOnlineChat(),
                  icon: const Icon(Icons.chat),
                  label: const Text('在线客服'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _callService(),
                  icon: const Icon(Icons.phone),
                  label: const Text('电话客服'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 事件处理方法
  void _showReturnApplication() {
    Navigator.of(context).pushNamed('/after-sales/return-apply');
  }

  void _showExchangeApplication() {
    Navigator.of(context).pushNamed('/after-sales/exchange-apply');
  }

  void _showRefundApplication() {
    Navigator.of(context).pushNamed('/after-sales/refund-apply');
  }

  void _showComplaintApplication() {
    Navigator.of(context).pushNamed('/after-sales/complaint-apply');
  }

  void _applyAfterSales(EligibleOrder order, String type) {
    Navigator.of(context).pushNamed(
      '/after-sales/$type-apply',
      arguments: order,
    );
  }

  void _viewOrderDetail(EligibleOrder order) {
    Navigator.of(context).pushNamed('/order-detail', arguments: order.orderId);
  }

  void _viewFullPolicy(AfterSalesPolicy policy) {
    Navigator.of(context).pushNamed('/after-sales/policy', arguments: policy);
  }

  void _viewRecordDetail(AfterSalesRecord record) {
    Navigator.of(context).pushNamed('/after-sales/record-detail', arguments: record);
  }

  void _trackProgress(AfterSalesRecord record) {
    Navigator.of(context).pushNamed('/after-sales/progress', arguments: record);
  }

  void _contactService(AfterSalesRecord record) {
    Navigator.of(context).pushNamed('/customer-service', arguments: record);
  }

  void _submitReview(AfterSalesRecord record) {
    Navigator.of(context).pushNamed('/after-sales/review', arguments: record);
  }

  void _viewHelpDetail(AfterSalesHelp item) {
    Navigator.of(context).pushNamed('/after-sales/help-detail', arguments: item);
  }

  void _startOnlineChat() {
    Navigator.of(context).pushNamed('/customer-service');
  }

  void _callService() {
    // 拨打客服电话
  }
}
