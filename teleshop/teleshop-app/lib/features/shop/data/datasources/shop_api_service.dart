import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/shop_models.dart';

part 'shop_api_service.g.dart';

/// 商城API服务
@RestApi()
abstract class ShopApiService {
  factory ShopApiService(Dio dio, {String baseUrl}) = _ShopApiService;

  /// 获取商品列表
  @GET('/api/v1/products')
  Future<PageResponse<ProductResponse>> getProducts(
    @Query('page') int page,
    @Query('size') int size,
    @Query('categoryId') int? categoryId,
    @Query('brandId') int? brandId,
    @Query('keyword') String? keyword,
    @Query('minPrice') double? minPrice,
    @Query('maxPrice') double? maxPrice,
    @Query('sortBy') String? sortBy,
    @Query('sortOrder') String? sortOrder,
  );

  /// 获取商品详情
  @GET('/api/v1/products/{productId}')
  Future<ProductDetailResponse> getProductDetail(
    @Path('productId') int productId,
  );

  /// 搜索商品
  @GET('/api/v1/products/search')
  Future<PageResponse<ProductResponse>> searchProducts(
    @Query('keyword') String keyword,
    @Query('page') int page,
    @Query('size') int size,
    @Query('categoryId') int? categoryId,
    @Query('brandId') int? brandId,
    @Query('minPrice') double? minPrice,
    @Query('maxPrice') double? maxPrice,
    @Query('sortBy') String? sortBy,
    @Query('sortOrder') String? sortOrder,
  );

  /// 获取推荐商品
  @GET('/api/v1/products/recommended')
  Future<List<ProductResponse>> getRecommendedProducts(
    @Query('limit') int limit,
  );

  /// 获取新品列表
  @GET('/api/v1/products/new')
  Future<List<ProductResponse>> getNewProducts(
    @Query('limit') int limit,
  );

  /// 获取热销商品
  @GET('/api/v1/products/hot')
  Future<List<ProductResponse>> getHotProducts(
    @Query('limit') int limit,
  );

  /// 获取分类列表
  @GET('/api/v1/categories')
  Future<List<CategoryResponse>> getCategories();

  /// 获取分类下的商品
  @GET('/api/v1/categories/{categoryId}/products')
  Future<PageResponse<ProductResponse>> getCategoryProducts(
    @Path('categoryId') int categoryId,
    @Query('page') int page,
    @Query('size') int size,
    @Query('sortBy') String? sortBy,
    @Query('sortOrder') String? sortOrder,
  );

  /// 获取品牌列表
  @GET('/api/v1/brands')
  Future<List<BrandResponse>> getBrands();

  /// 获取购物车
  @GET('/api/v1/cart')
  Future<CartResponse> getCart(
    @Header('Authorization') String accessToken,
  );

  /// 添加到购物车
  @POST('/api/v1/cart/items')
  Future<CartItemResponse> addToCart(
    @Header('Authorization') String accessToken,
    @Body() AddToCartRequest request,
  );

  /// 更新购物车商品数量
  @PUT('/api/v1/cart/items/{itemId}')
  Future<CartItemResponse> updateCartItem(
    @Path('itemId') int itemId,
    @Header('Authorization') String accessToken,
    @Body() UpdateCartItemRequest request,
  );

  /// 删除购物车商品
  @DELETE('/api/v1/cart/items/{itemId}')
  Future<void> removeFromCart(
    @Path('itemId') int itemId,
    @Header('Authorization') String accessToken,
  );

  /// 清空购物车
  @DELETE('/api/v1/cart')
  Future<void> clearCart(
    @Header('Authorization') String accessToken,
  );

  /// 创建订单
  @POST('/api/v1/orders')
  Future<OrderResponse> createOrder(
    @Header('Authorization') String accessToken,
    @Body() CreateOrderRequest request,
  );

  /// 获取订单列表
  @GET('/api/v1/orders')
  Future<PageResponse<OrderResponse>> getOrders(
    @Header('Authorization') String accessToken,
    @Query('page') int page,
    @Query('size') int size,
    @Query('status') int? status,
    @Query('startTime') String? startTime,
    @Query('endTime') String? endTime,
  );

  /// 获取订单详情
  @GET('/api/v1/orders/{orderId}')
  Future<OrderDetailResponse> getOrderDetail(
    @Path('orderId') int orderId,
    @Header('Authorization') String accessToken,
  );

  /// 取消订单
  @PUT('/api/v1/orders/{orderId}/cancel')
  Future<void> cancelOrder(
    @Path('orderId') int orderId,
    @Header('Authorization') String accessToken,
    @Body() CancelOrderRequest request,
  );

  /// 确认收货
  @PUT('/api/v1/orders/{orderId}/confirm')
  Future<void> confirmOrder(
    @Path('orderId') int orderId,
    @Header('Authorization') String accessToken,
  );

  /// 申请退款
  @POST('/api/v1/orders/{orderId}/refund')
  Future<RefundResponse> requestRefund(
    @Path('orderId') int orderId,
    @Header('Authorization') String accessToken,
    @Body() RefundRequest request,
  );

  /// 获取收货地址列表
  @GET('/api/v1/addresses')
  Future<List<AddressResponse>> getAddresses(
    @Header('Authorization') String accessToken,
  );

  /// 添加收货地址
  @POST('/api/v1/addresses')
  Future<AddressResponse> addAddress(
    @Header('Authorization') String accessToken,
    @Body() AddAddressRequest request,
  );

  /// 更新收货地址
  @PUT('/api/v1/addresses/{addressId}')
  Future<AddressResponse> updateAddress(
    @Path('addressId') int addressId,
    @Header('Authorization') String accessToken,
    @Body() UpdateAddressRequest request,
  );

  /// 删除收货地址
  @DELETE('/api/v1/addresses/{addressId}')
  Future<void> deleteAddress(
    @Path('addressId') int addressId,
    @Header('Authorization') String accessToken,
  );

  /// 设置默认地址
  @PUT('/api/v1/addresses/{addressId}/default')
  Future<void> setDefaultAddress(
    @Path('addressId') int addressId,
    @Header('Authorization') String accessToken,
  );

  /// 获取优惠券列表
  @GET('/api/v1/coupons')
  Future<List<CouponResponse>> getCoupons(
    @Header('Authorization') String accessToken,
    @Query('status') int? status,
  );

  /// 获取可用优惠券
  @GET('/api/v1/coupons/available')
  Future<List<CouponResponse>> getAvailableCoupons(
    @Header('Authorization') String accessToken,
    @Query('totalAmount') double totalAmount,
  );

  /// 使用优惠券
  @POST('/api/v1/coupons/{couponId}/use')
  Future<void> useCoupon(
    @Path('couponId') int couponId,
    @Header('Authorization') String accessToken,
    @Body() UseCouponRequest request,
  );

  /// 收藏商品
  @POST('/api/v1/favorites')
  Future<void> addToFavorites(
    @Header('Authorization') String accessToken,
    @Body() AddToFavoritesRequest request,
  );

  /// 取消收藏
  @DELETE('/api/v1/favorites/{productId}')
  Future<void> removeFromFavorites(
    @Path('productId') int productId,
    @Header('Authorization') String accessToken,
  );

  /// 获取收藏列表
  @GET('/api/v1/favorites')
  Future<PageResponse<ProductResponse>> getFavorites(
    @Header('Authorization') String accessToken,
    @Query('page') int page,
    @Query('size') int size,
  );

  /// 检查商品是否已收藏
  @GET('/api/v1/favorites/{productId}/check')
  Future<bool> isFavorite(
    @Path('productId') int productId,
    @Header('Authorization') String accessToken,
  );

  /// 获取商品评价
  @GET('/api/v1/products/{productId}/reviews')
  Future<PageResponse<ReviewResponse>> getProductReviews(
    @Path('productId') int productId,
    @Query('page') int page,
    @Query('size') int size,
    @Query('rating') int? rating,
  );

  /// 添加商品评价
  @POST('/api/v1/products/{productId}/reviews')
  Future<ReviewResponse> addProductReview(
    @Path('productId') int productId,
    @Header('Authorization') String accessToken,
    @Body() AddReviewRequest request,
  );
}
