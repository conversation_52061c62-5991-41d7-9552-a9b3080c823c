import 'package:json_annotation/json_annotation.dart';

part 'shop_models.g.dart';

/// 商品响应模型
@JsonSerializable()
class ProductResponse {
  final int productId;
  final String productName;
  final String? subTitle;
  final String? description;
  final int categoryId;
  final int? brandId;
  final int merchantId;
  final String? productCode;
  final String? mainImage;
  final List<String> images;
  final String? videoUrl;
  final double? originalPrice;
  final double currentPrice;
  final int stockQuantity;
  final int salesCount;
  final int viewCount;
  final int favoriteCount;
  final int commentCount;
  final double rating;
  final int? weight;
  final String? dimensions;
  final List<String> tags;
  final bool isRecommended;
  final bool isNew;
  final bool isHot;
  final int status;
  final String createTime;
  final String updateTime;

  ProductResponse({
    required this.productId,
    required this.productName,
    this.subTitle,
    this.description,
    required this.categoryId,
    this.brandId,
    required this.merchantId,
    this.productCode,
    this.mainImage,
    this.images = const [],
    this.videoUrl,
    this.originalPrice,
    required this.currentPrice,
    required this.stockQuantity,
    required this.salesCount,
    required this.viewCount,
    required this.favoriteCount,
    required this.commentCount,
    required this.rating,
    this.weight,
    this.dimensions,
    this.tags = const [],
    required this.isRecommended,
    required this.isNew,
    required this.isHot,
    required this.status,
    required this.createTime,
    required this.updateTime,
  });

  factory ProductResponse.fromJson(Map<String, dynamic> json) =>
      _$ProductResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ProductResponseToJson(this);
}

/// 商品详情响应模型
@JsonSerializable()
class ProductDetailResponse {
  final ProductResponse product;
  final List<ProductSpecification> specifications;
  final List<ProductAttribute> attributes;
  final List<ProductSku> skus;
  final MerchantInfo merchant;
  final bool isFavorite;

  ProductDetailResponse({
    required this.product,
    required this.specifications,
    required this.attributes,
    required this.skus,
    required this.merchant,
    required this.isFavorite,
  });

  factory ProductDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$ProductDetailResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ProductDetailResponseToJson(this);
}

/// 商品规格
@JsonSerializable()
class ProductSpecification {
  final String name;
  final String value;

  ProductSpecification({
    required this.name,
    required this.value,
  });

  factory ProductSpecification.fromJson(Map<String, dynamic> json) =>
      _$ProductSpecificationFromJson(json);

  Map<String, dynamic> toJson() => _$ProductSpecificationToJson(this);
}

/// 商品属性
@JsonSerializable()
class ProductAttribute {
  final String name;
  final List<String> values;

  ProductAttribute({
    required this.name,
    required this.values,
  });

  factory ProductAttribute.fromJson(Map<String, dynamic> json) =>
      _$ProductAttributeFromJson(json);

  Map<String, dynamic> toJson() => _$ProductAttributeToJson(this);
}

/// 商品SKU
@JsonSerializable()
class ProductSku {
  final int skuId;
  final String skuCode;
  final Map<String, String> attributes;
  final double price;
  final int stock;
  final String? image;

  ProductSku({
    required this.skuId,
    required this.skuCode,
    required this.attributes,
    required this.price,
    required this.stock,
    this.image,
  });

  factory ProductSku.fromJson(Map<String, dynamic> json) =>
      _$ProductSkuFromJson(json);

  Map<String, dynamic> toJson() => _$ProductSkuToJson(this);
}

/// 商家信息
@JsonSerializable()
class MerchantInfo {
  final int merchantId;
  final String merchantName;
  final String? logo;
  final double rating;
  final int productCount;
  final int followerCount;

  MerchantInfo({
    required this.merchantId,
    required this.merchantName,
    this.logo,
    required this.rating,
    required this.productCount,
    required this.followerCount,
  });

  factory MerchantInfo.fromJson(Map<String, dynamic> json) =>
      _$MerchantInfoFromJson(json);

  Map<String, dynamic> toJson() => _$MerchantInfoToJson(this);
}

/// 分类响应模型
@JsonSerializable()
class CategoryResponse {
  final int categoryId;
  final String categoryName;
  final String? icon;
  final String? image;
  final int? parentId;
  final int level;
  final int sortOrder;
  final List<CategoryResponse> children;

  CategoryResponse({
    required this.categoryId,
    required this.categoryName,
    this.icon,
    this.image,
    this.parentId,
    required this.level,
    required this.sortOrder,
    this.children = const [],
  });

  factory CategoryResponse.fromJson(Map<String, dynamic> json) =>
      _$CategoryResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryResponseToJson(this);
}

/// 品牌响应模型
@JsonSerializable()
class BrandResponse {
  final int brandId;
  final String brandName;
  final String? logo;
  final String? description;

  BrandResponse({
    required this.brandId,
    required this.brandName,
    this.logo,
    this.description,
  });

  factory BrandResponse.fromJson(Map<String, dynamic> json) =>
      _$BrandResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BrandResponseToJson(this);
}

/// 购物车响应模型
@JsonSerializable()
class CartResponse {
  final int cartId;
  final int userId;
  final List<CartItemResponse> items;
  final double totalAmount;
  final int totalQuantity;
  final String updateTime;

  CartResponse({
    required this.cartId,
    required this.userId,
    required this.items,
    required this.totalAmount,
    required this.totalQuantity,
    required this.updateTime,
  });

  factory CartResponse.fromJson(Map<String, dynamic> json) =>
      _$CartResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CartResponseToJson(this);
}

/// 购物车商品响应模型
@JsonSerializable()
class CartItemResponse {
  final int itemId;
  final int productId;
  final int? skuId;
  final String productName;
  final String? productImage;
  final Map<String, String>? skuAttributes;
  final double price;
  final int quantity;
  final double totalPrice;
  final bool selected;
  final String addTime;

  CartItemResponse({
    required this.itemId,
    required this.productId,
    this.skuId,
    required this.productName,
    this.productImage,
    this.skuAttributes,
    required this.price,
    required this.quantity,
    required this.totalPrice,
    required this.selected,
    required this.addTime,
  });

  factory CartItemResponse.fromJson(Map<String, dynamic> json) =>
      _$CartItemResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CartItemResponseToJson(this);
}

/// 添加到购物车请求模型
@JsonSerializable()
class AddToCartRequest {
  final int productId;
  final int? skuId;
  final int quantity;

  AddToCartRequest({
    required this.productId,
    this.skuId,
    required this.quantity,
  });

  factory AddToCartRequest.fromJson(Map<String, dynamic> json) =>
      _$AddToCartRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddToCartRequestToJson(this);
}

/// 更新购物车商品请求模型
@JsonSerializable()
class UpdateCartItemRequest {
  final int quantity;
  final bool? selected;

  UpdateCartItemRequest({
    required this.quantity,
    this.selected,
  });

  factory UpdateCartItemRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateCartItemRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateCartItemRequestToJson(this);
}

/// 订单响应模型
@JsonSerializable()
class OrderResponse {
  final int orderId;
  final String orderNo;
  final int userId;
  final int merchantId;
  final int orderType;
  final int orderSource;
  final int status;
  final double productAmount;
  final double shippingFee;
  final double discountAmount;
  final double actualAmount;
  final int paymentMethod;
  final int paymentStatus;
  final String? paymentTime;
  final String receiverName;
  final String receiverPhone;
  final String receiverAddress;
  final String? logisticsCompany;
  final String? logisticsNo;
  final String? shipTime;
  final String? receiveTime;
  final String? completeTime;
  final String? cancelTime;
  final String? cancelReason;
  final String? remark;
  final String createTime;

  OrderResponse({
    required this.orderId,
    required this.orderNo,
    required this.userId,
    required this.merchantId,
    required this.orderType,
    required this.orderSource,
    required this.status,
    required this.productAmount,
    required this.shippingFee,
    required this.discountAmount,
    required this.actualAmount,
    required this.paymentMethod,
    required this.paymentStatus,
    this.paymentTime,
    required this.receiverName,
    required this.receiverPhone,
    required this.receiverAddress,
    this.logisticsCompany,
    this.logisticsNo,
    this.shipTime,
    this.receiveTime,
    this.completeTime,
    this.cancelTime,
    this.cancelReason,
    this.remark,
    required this.createTime,
  });

  factory OrderResponse.fromJson(Map<String, dynamic> json) =>
      _$OrderResponseFromJson(json);

  Map<String, dynamic> toJson() => _$OrderResponseToJson(this);
}

/// 订单详情响应模型
@JsonSerializable()
class OrderDetailResponse {
  final OrderResponse order;
  final List<OrderItemResponse> items;
  final List<OrderLogResponse> logs;

  OrderDetailResponse({
    required this.order,
    required this.items,
    required this.logs,
  });

  factory OrderDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailResponseFromJson(json);

  Map<String, dynamic> toJson() => _$OrderDetailResponseToJson(this);
}

/// 订单商品响应模型
@JsonSerializable()
class OrderItemResponse {
  final int itemId;
  final int productId;
  final int? skuId;
  final String productName;
  final String? productImage;
  final Map<String, String>? skuAttributes;
  final double price;
  final int quantity;
  final double totalPrice;

  OrderItemResponse({
    required this.itemId,
    required this.productId,
    this.skuId,
    required this.productName,
    this.productImage,
    this.skuAttributes,
    required this.price,
    required this.quantity,
    required this.totalPrice,
  });

  factory OrderItemResponse.fromJson(Map<String, dynamic> json) =>
      _$OrderItemResponseFromJson(json);

  Map<String, dynamic> toJson() => _$OrderItemResponseToJson(this);
}

/// 订单日志响应模型
@JsonSerializable()
class OrderLogResponse {
  final int logId;
  final String action;
  final String description;
  final String createTime;

  OrderLogResponse({
    required this.logId,
    required this.action,
    required this.description,
    required this.createTime,
  });

  factory OrderLogResponse.fromJson(Map<String, dynamic> json) =>
      _$OrderLogResponseFromJson(json);

  Map<String, dynamic> toJson() => _$OrderLogResponseToJson(this);
}

/// 创建订单请求模型
@JsonSerializable()
class CreateOrderRequest {
  final List<int> cartItemIds;
  final int addressId;
  final int paymentMethod;
  final int? couponId;
  final int pointsUsed;
  final String? remark;

  CreateOrderRequest({
    required this.cartItemIds,
    required this.addressId,
    required this.paymentMethod,
    this.couponId,
    required this.pointsUsed,
    this.remark,
  });

  factory CreateOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateOrderRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreateOrderRequestToJson(this);
}

/// 取消订单请求模型
@JsonSerializable()
class CancelOrderRequest {
  final String reason;

  CancelOrderRequest({
    required this.reason,
  });

  factory CancelOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$CancelOrderRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CancelOrderRequestToJson(this);
}

/// 退款请求模型
@JsonSerializable()
class RefundRequest {
  final String reason;
  final double amount;
  final List<String>? images;

  RefundRequest({
    required this.reason,
    required this.amount,
    this.images,
  });

  factory RefundRequest.fromJson(Map<String, dynamic> json) =>
      _$RefundRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RefundRequestToJson(this);
}

/// 退款响应模型
@JsonSerializable()
class RefundResponse {
  final int refundId;
  final String refundNo;
  final double amount;
  final int status;
  final String createTime;

  RefundResponse({
    required this.refundId,
    required this.refundNo,
    required this.amount,
    required this.status,
    required this.createTime,
  });

  factory RefundResponse.fromJson(Map<String, dynamic> json) =>
      _$RefundResponseFromJson(json);

  Map<String, dynamic> toJson() => _$RefundResponseToJson(this);
}

/// 收货地址响应模型
@JsonSerializable()
class AddressResponse {
  final int addressId;
  final String receiverName;
  final String receiverPhone;
  final String province;
  final String city;
  final String district;
  final String detailAddress;
  final String? zipCode;
  final bool isDefault;

  AddressResponse({
    required this.addressId,
    required this.receiverName,
    required this.receiverPhone,
    required this.province,
    required this.city,
    required this.district,
    required this.detailAddress,
    this.zipCode,
    required this.isDefault,
  });

  factory AddressResponse.fromJson(Map<String, dynamic> json) =>
      _$AddressResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AddressResponseToJson(this);
}

/// 添加地址请求模型
@JsonSerializable()
class AddAddressRequest {
  final String receiverName;
  final String receiverPhone;
  final String province;
  final String city;
  final String district;
  final String detailAddress;
  final String? zipCode;
  final bool isDefault;

  AddAddressRequest({
    required this.receiverName,
    required this.receiverPhone,
    required this.province,
    required this.city,
    required this.district,
    required this.detailAddress,
    this.zipCode,
    required this.isDefault,
  });

  factory AddAddressRequest.fromJson(Map<String, dynamic> json) =>
      _$AddAddressRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddAddressRequestToJson(this);
}

/// 更新地址请求模型
@JsonSerializable()
class UpdateAddressRequest {
  final String receiverName;
  final String receiverPhone;
  final String province;
  final String city;
  final String district;
  final String detailAddress;
  final String? zipCode;
  final bool isDefault;

  UpdateAddressRequest({
    required this.receiverName,
    required this.receiverPhone,
    required this.province,
    required this.city,
    required this.district,
    required this.detailAddress,
    this.zipCode,
    required this.isDefault,
  });

  factory UpdateAddressRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateAddressRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateAddressRequestToJson(this);
}

/// 优惠券响应模型
@JsonSerializable()
class CouponResponse {
  final int couponId;
  final String couponCode;
  final String couponName;
  final int couponType;
  final double discountValue;
  final double minAmount;
  final String startTime;
  final String endTime;
  final int status;

  CouponResponse({
    required this.couponId,
    required this.couponCode,
    required this.couponName,
    required this.couponType,
    required this.discountValue,
    required this.minAmount,
    required this.startTime,
    required this.endTime,
    required this.status,
  });

  factory CouponResponse.fromJson(Map<String, dynamic> json) =>
      _$CouponResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CouponResponseToJson(this);
}

/// 使用优惠券请求模型
@JsonSerializable()
class UseCouponRequest {
  final String orderNo;

  UseCouponRequest({
    required this.orderNo,
  });

  factory UseCouponRequest.fromJson(Map<String, dynamic> json) =>
      _$UseCouponRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UseCouponRequestToJson(this);
}

/// 添加收藏请求模型
@JsonSerializable()
class AddToFavoritesRequest {
  final int productId;

  AddToFavoritesRequest({
    required this.productId,
  });

  factory AddToFavoritesRequest.fromJson(Map<String, dynamic> json) =>
      _$AddToFavoritesRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddToFavoritesRequestToJson(this);
}

/// 商品评价响应模型
@JsonSerializable()
class ReviewResponse {
  final int reviewId;
  final int userId;
  final String userName;
  final String? userAvatar;
  final int rating;
  final String content;
  final List<String> images;
  final String createTime;

  ReviewResponse({
    required this.reviewId,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.rating,
    required this.content,
    required this.images,
    required this.createTime,
  });

  factory ReviewResponse.fromJson(Map<String, dynamic> json) =>
      _$ReviewResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ReviewResponseToJson(this);
}

/// 添加评价请求模型
@JsonSerializable()
class AddReviewRequest {
  final int orderId;
  final int rating;
  final String content;
  final List<String>? images;

  AddReviewRequest({
    required this.orderId,
    required this.rating,
    required this.content,
    this.images,
  });

  factory AddReviewRequest.fromJson(Map<String, dynamic> json) =>
      _$AddReviewRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddReviewRequestToJson(this);
}

/// 分页响应模型
@JsonSerializable()
class PageResponse<T> {
  final List<T> content;
  final int totalElements;
  final int totalPages;
  final int number;
  final int size;
  final bool first;
  final bool last;

  PageResponse({
    required this.content,
    required this.totalElements,
    required this.totalPages,
    required this.number,
    required this.size,
    required this.first,
    required this.last,
  });

  factory PageResponse.fromJson(Map<String, dynamic> json) =>
      _$PageResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PageResponseToJson(this);
}
