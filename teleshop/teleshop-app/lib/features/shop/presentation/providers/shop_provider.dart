import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/datasources/shop_api_service.dart';
import '../../data/models/shop_models.dart';
import '../../../auth/presentation/providers/enhanced_auth_provider.dart';
import '../../../../core/network/dio_client.dart';

/// 商城API服务提供者
final shopApiServiceProvider = Provider<ShopApiService>((ref) {
  final dio = ref.watch(dioProvider);
  return ShopApiService(dio);
});

/// 商城状态
class ShopState {
  final bool isLoading;
  final String? error;
  final List<ProductResponse> products;
  final List<ProductResponse> recommendedProducts;
  final List<ProductResponse> newProducts;
  final List<ProductResponse> hotProducts;
  final List<CategoryResponse> categories;
  final List<BrandResponse> brands;
  final ProductDetailResponse? selectedProduct;
  final CartResponse? cart;
  final List<OrderResponse> orders;
  final List<AddressResponse> addresses;
  final List<CouponResponse> coupons;
  final List<ProductResponse> favorites;
  final int currentPage;
  final bool hasMore;

  const ShopState({
    this.isLoading = false,
    this.error,
    this.products = const [],
    this.recommendedProducts = const [],
    this.newProducts = const [],
    this.hotProducts = const [],
    this.categories = const [],
    this.brands = const [],
    this.selectedProduct,
    this.cart,
    this.orders = const [],
    this.addresses = const [],
    this.coupons = const [],
    this.favorites = const [],
    this.currentPage = 0,
    this.hasMore = true,
  });

  ShopState copyWith({
    bool? isLoading,
    String? error,
    List<ProductResponse>? products,
    List<ProductResponse>? recommendedProducts,
    List<ProductResponse>? newProducts,
    List<ProductResponse>? hotProducts,
    List<CategoryResponse>? categories,
    List<BrandResponse>? brands,
    ProductDetailResponse? selectedProduct,
    CartResponse? cart,
    List<OrderResponse>? orders,
    List<AddressResponse>? addresses,
    List<CouponResponse>? coupons,
    List<ProductResponse>? favorites,
    int? currentPage,
    bool? hasMore,
  }) {
    return ShopState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      products: products ?? this.products,
      recommendedProducts: recommendedProducts ?? this.recommendedProducts,
      newProducts: newProducts ?? this.newProducts,
      hotProducts: hotProducts ?? this.hotProducts,
      categories: categories ?? this.categories,
      brands: brands ?? this.brands,
      selectedProduct: selectedProduct ?? this.selectedProduct,
      cart: cart ?? this.cart,
      orders: orders ?? this.orders,
      addresses: addresses ?? this.addresses,
      coupons: coupons ?? this.coupons,
      favorites: favorites ?? this.favorites,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

/// 商城状态管理
class ShopNotifier extends StateNotifier<ShopState> {
  ShopNotifier(this._ref) : super(const ShopState()) {
    _initialize();
  }

  final Ref _ref;
  Timer? _refreshTimer;

  ShopApiService get _shopApi => _ref.read(shopApiServiceProvider);

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  /// 初始化
  Future<void> _initialize() async {
    await Future.wait([
      loadCategories(),
      loadBrands(),
      loadRecommendedProducts(),
      loadNewProducts(),
      loadHotProducts(),
      loadCart(),
    ]);
  }

  /// 加载商品列表
  Future<void> loadProducts({
    int page = 0,
    int size = 20,
    int? categoryId,
    int? brandId,
    String? keyword,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    String? sortOrder,
    bool refresh = false,
  }) async {
    try {
      if (refresh) {
        state = state.copyWith(isLoading: true, error: null, currentPage: 0);
      } else if (page == 0) {
        state = state.copyWith(isLoading: true, error: null);
      }

      final response = await _shopApi.getProducts(
        page,
        size,
        categoryId,
        brandId,
        keyword,
        minPrice,
        maxPrice,
        sortBy,
        sortOrder,
      );

      List<ProductResponse> newProducts;
      if (page == 0 || refresh) {
        newProducts = response.content;
      } else {
        newProducts = [...state.products, ...response.content];
      }

      state = state.copyWith(
        isLoading: false,
        products: newProducts,
        currentPage: page,
        hasMore: !response.last,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '加载商品列表失败: $e',
      );
    }
  }

  /// 搜索商品
  Future<void> searchProducts({
    required String keyword,
    int page = 0,
    int size = 20,
    int? categoryId,
    int? brandId,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    String? sortOrder,
    bool refresh = false,
  }) async {
    try {
      if (refresh) {
        state = state.copyWith(isLoading: true, error: null, currentPage: 0);
      } else if (page == 0) {
        state = state.copyWith(isLoading: true, error: null);
      }

      final response = await _shopApi.searchProducts(
        keyword,
        page,
        size,
        categoryId,
        brandId,
        minPrice,
        maxPrice,
        sortBy,
        sortOrder,
      );

      List<ProductResponse> newProducts;
      if (page == 0 || refresh) {
        newProducts = response.content;
      } else {
        newProducts = [...state.products, ...response.content];
      }

      state = state.copyWith(
        isLoading: false,
        products: newProducts,
        currentPage: page,
        hasMore: !response.last,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '搜索商品失败: $e',
      );
    }
  }

  /// 获取商品详情
  Future<void> getProductDetail(int productId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _shopApi.getProductDetail(productId);

      state = state.copyWith(
        isLoading: false,
        selectedProduct: response,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '获取商品详情失败: $e',
      );
    }
  }

  /// 加载推荐商品
  Future<void> loadRecommendedProducts({int limit = 10}) async {
    try {
      final products = await _shopApi.getRecommendedProducts(limit);
      state = state.copyWith(recommendedProducts: products);
    } catch (e) {
      // 静默处理推荐商品加载失败
    }
  }

  /// 加载新品
  Future<void> loadNewProducts({int limit = 10}) async {
    try {
      final products = await _shopApi.getNewProducts(limit);
      state = state.copyWith(newProducts: products);
    } catch (e) {
      // 静默处理新品加载失败
    }
  }

  /// 加载热销商品
  Future<void> loadHotProducts({int limit = 10}) async {
    try {
      final products = await _shopApi.getHotProducts(limit);
      state = state.copyWith(hotProducts: products);
    } catch (e) {
      // 静默处理热销商品加载失败
    }
  }

  /// 加载分类列表
  Future<void> loadCategories() async {
    try {
      final categories = await _shopApi.getCategories();
      state = state.copyWith(categories: categories);
    } catch (e) {
      // 静默处理分类加载失败
    }
  }

  /// 加载品牌列表
  Future<void> loadBrands() async {
    try {
      final brands = await _shopApi.getBrands();
      state = state.copyWith(brands: brands);
    } catch (e) {
      // 静默处理品牌加载失败
    }
  }

  /// 加载购物车
  Future<void> loadCart() async {
    try {
      final accessToken = await _getAccessToken();
      if (accessToken == null) return;

      final cart = await _shopApi.getCart('Bearer $accessToken');
      state = state.copyWith(cart: cart);
    } catch (e) {
      // 静默处理购物车加载失败
    }
  }

  /// 添加到购物车
  Future<bool> addToCart({
    required int productId,
    int? skuId,
    required int quantity,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final accessToken = await _getAccessToken();
      if (accessToken == null) {
        state = state.copyWith(
          isLoading: false,
          error: '请先登录',
        );
        return false;
      }

      final request = AddToCartRequest(
        productId: productId,
        skuId: skuId,
        quantity: quantity,
      );

      await _shopApi.addToCart('Bearer $accessToken', request);

      // 重新加载购物车
      await loadCart();

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '添加到购物车失败: $e',
      );
      return false;
    }
  }

  /// 更新购物车商品数量
  Future<bool> updateCartItem({
    required int itemId,
    required int quantity,
    bool? selected,
  }) async {
    try {
      final accessToken = await _getAccessToken();
      if (accessToken == null) return false;

      final request = UpdateCartItemRequest(
        quantity: quantity,
        selected: selected,
      );

      await _shopApi.updateCartItem(itemId, 'Bearer $accessToken', request);

      // 重新加载购物车
      await loadCart();

      return true;
    } catch (e) {
      state = state.copyWith(error: '更新购物车失败: $e');
      return false;
    }
  }

  /// 删除购物车商品
  Future<bool> removeFromCart(int itemId) async {
    try {
      final accessToken = await _getAccessToken();
      if (accessToken == null) return false;

      await _shopApi.removeFromCart(itemId, 'Bearer $accessToken');

      // 重新加载购物车
      await loadCart();

      return true;
    } catch (e) {
      state = state.copyWith(error: '删除购物车商品失败: $e');
      return false;
    }
  }

  /// 获取访问令牌
  Future<String?> _getAccessToken() async {
    return await _ref.read(enhancedAuthProvider.notifier)._authRepository.getAccessToken();
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// 清除选中商品
  void clearSelectedProduct() {
    state = state.copyWith(selectedProduct: null);
  }
}

/// 商城状态提供者
final shopProvider = StateNotifierProvider<ShopNotifier, ShopState>((ref) {
  return ShopNotifier(ref);
});
