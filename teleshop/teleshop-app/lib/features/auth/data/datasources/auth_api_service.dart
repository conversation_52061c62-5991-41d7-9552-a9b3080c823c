import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../../models/auth_request.dart';
import '../../models/auth_response.dart';

part 'auth_api_service.g.dart';

/// 认证API服务
@RestApi()
abstract class AuthApiService {
  factory AuthApiService(Dio dio, {String baseUrl}) = _AuthApiService;

  /// 用户注册
  @POST('/api/v1/users/register')
  Future<AuthResponse> register(@Body() RegisterRequest request);

  /// 用户登录
  @POST('/api/v1/users/login')
  Future<AuthResponse> login(@Body() LoginRequest request);

  /// 刷新令牌
  @POST('/api/v1/auth/refresh')
  Future<AuthResponse> refreshToken(@Header('Authorization') String refreshToken);

  /// 用户登出
  @POST('/api/v1/auth/logout')
  Future<void> logout(@Header('Authorization') String accessToken);

  /// 发送验证码
  @POST('/api/v1/auth/send-verify-code')
  Future<void> sendVerifyCode(@Body() SendVerifyCodeRequest request);

  /// 验证验证码
  @POST('/api/v1/auth/verify-code')
  Future<bool> verifyCode(@Body() VerifyCodeRequest request);

  /// 重置密码
  @POST('/api/v1/auth/reset-password')
  Future<void> resetPassword(@Body() ResetPasswordRequest request);

  /// 修改密码
  @PUT('/api/v1/auth/change-password')
  Future<void> changePassword(
    @Header('Authorization') String accessToken,
    @Body() ChangePasswordRequest request,
  );

  /// 绑定手机号
  @POST('/api/v1/auth/bind-phone')
  Future<void> bindPhone(
    @Header('Authorization') String accessToken,
    @Body() BindPhoneRequest request,
  );

  /// 绑定邮箱
  @POST('/api/v1/auth/bind-email')
  Future<void> bindEmail(
    @Header('Authorization') String accessToken,
    @Body() BindEmailRequest request,
  );

  /// 检查用户名是否存在
  @GET('/api/v1/users/check/username')
  Future<bool> checkUsername(@Query('username') String username);

  /// 检查手机号是否存在
  @GET('/api/v1/users/check/phone')
  Future<bool> checkPhone(@Query('phone') String phone);

  /// 检查邮箱是否存在
  @GET('/api/v1/users/check/email')
  Future<bool> checkEmail(@Query('email') String email);

  /// 获取用户信息
  @GET('/api/v1/users/{userId}')
  Future<UserResponse> getUserInfo(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 更新用户信息
  @PUT('/api/v1/users/{userId}')
  Future<UserResponse> updateUserInfo(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() UpdateUserRequest request,
  );
}
