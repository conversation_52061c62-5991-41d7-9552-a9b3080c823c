import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/auth_response.dart';
import '../../models/user.dart';

/// 认证本地数据源接口
abstract class AuthLocalDataSource {
  Future<void> saveAuthData(AuthResponse authResponse);
  Future<AuthResponse?> getAuthData();
  Future<void> saveUser(User user);
  Future<User?> getUser();
  Future<void> saveAccessToken(String token);
  Future<String?> getAccessToken();
  Future<void> saveRefreshToken(String token);
  Future<String?> getRefreshToken();
  Future<void> saveBiometricEnabled(bool enabled);
  Future<bool> getBiometricEnabled();
  Future<void> saveRememberMe(bool remember);
  Future<bool> getRememberMe();
  Future<void> saveLastLoginUsername(String username);
  Future<String?> getLastLoginUsername();
  Future<void> clearAuthData();
  Future<bool> isLoggedIn();
}

/// 认证本地数据源实现
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SharedPreferences _prefs;

  AuthLocalDataSourceImpl(this._prefs);

  static const String _keyAuthData = 'auth_data';
  static const String _keyUser = 'user_data';
  static const String _keyAccessToken = 'access_token';
  static const String _keyRefreshToken = 'refresh_token';
  static const String _keyBiometricEnabled = 'biometric_enabled';
  static const String _keyRememberMe = 'remember_me';
  static const String _keyLastLoginUsername = 'last_login_username';

  @override
  Future<void> saveAuthData(AuthResponse authResponse) async {
    final jsonString = jsonEncode(authResponse.toJson());
    await _prefs.setString(_keyAuthData, jsonString);
    
    // 同时保存token
    if (authResponse.accessToken != null) {
      await saveAccessToken(authResponse.accessToken!);
    }
    if (authResponse.refreshToken != null) {
      await saveRefreshToken(authResponse.refreshToken!);
    }
    
    // 保存用户信息
    if (authResponse.user != null) {
      await saveUser(authResponse.user!);
    }
  }

  @override
  Future<AuthResponse?> getAuthData() async {
    final jsonString = _prefs.getString(_keyAuthData);
    if (jsonString != null) {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return AuthResponse.fromJson(json);
    }
    return null;
  }

  @override
  Future<void> saveUser(User user) async {
    final jsonString = jsonEncode(user.toJson());
    await _prefs.setString(_keyUser, jsonString);
  }

  @override
  Future<User?> getUser() async {
    final jsonString = _prefs.getString(_keyUser);
    if (jsonString != null) {
      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return User.fromJson(json);
    }
    return null;
  }

  @override
  Future<void> saveAccessToken(String token) async {
    await _prefs.setString(_keyAccessToken, token);
  }

  @override
  Future<String?> getAccessToken() async {
    return _prefs.getString(_keyAccessToken);
  }

  @override
  Future<void> saveRefreshToken(String token) async {
    await _prefs.setString(_keyRefreshToken, token);
  }

  @override
  Future<String?> getRefreshToken() async {
    return _prefs.getString(_keyRefreshToken);
  }

  @override
  Future<void> saveBiometricEnabled(bool enabled) async {
    await _prefs.setBool(_keyBiometricEnabled, enabled);
  }

  @override
  Future<bool> getBiometricEnabled() async {
    return _prefs.getBool(_keyBiometricEnabled) ?? false;
  }

  @override
  Future<void> saveRememberMe(bool remember) async {
    await _prefs.setBool(_keyRememberMe, remember);
  }

  @override
  Future<bool> getRememberMe() async {
    return _prefs.getBool(_keyRememberMe) ?? false;
  }

  @override
  Future<void> saveLastLoginUsername(String username) async {
    await _prefs.setString(_keyLastLoginUsername, username);
  }

  @override
  Future<String?> getLastLoginUsername() async {
    return _prefs.getString(_keyLastLoginUsername);
  }

  @override
  Future<void> clearAuthData() async {
    await Future.wait([
      _prefs.remove(_keyAuthData),
      _prefs.remove(_keyUser),
      _prefs.remove(_keyAccessToken),
      _prefs.remove(_keyRefreshToken),
    ]);
  }

  @override
  Future<bool> isLoggedIn() async {
    final accessToken = await getAccessToken();
    return accessToken != null && accessToken.isNotEmpty;
  }
}
