import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/api_client.dart';
import '../../models/auth_request.dart';
import '../../models/auth_response.dart';
import '../../models/user.dart';
import 'auth_api_service.dart';

/// 认证远程数据源接口
abstract class AuthRemoteDataSource {
  Future<Either<Failure, AuthResponse>> register(RegisterRequest request);
  Future<Either<Failure, AuthResponse>> login(LoginRequest request);
  Future<Either<Failure, AuthResponse>> refreshToken(String refreshToken);
  Future<Either<Failure, void>> logout(String accessToken);
  Future<Either<Failure, void>> sendVerifyCode(SendVerifyCodeRequest request);
  Future<Either<Failure, bool>> verifyCode(VerifyCodeRequest request);
  Future<Either<Failure, void>> resetPassword(ResetPasswordRequest request);
  Future<Either<Failure, void>> changePassword(String accessToken, ChangePasswordRequest request);
  Future<Either<Failure, void>> bindPhone(String accessToken, BindPhoneRequest request);
  Future<Either<Failure, void>> bindEmail(String accessToken, BindEmailRequest request);
  Future<Either<Failure, bool>> checkUsername(String username);
  Future<Either<Failure, bool>> checkPhone(String phone);
  Future<Either<Failure, bool>> checkEmail(String email);
  Future<Either<Failure, User>> getUserInfo(int userId, String accessToken);
  Future<Either<Failure, User>> updateUserInfo(int userId, String accessToken, UpdateUserRequest request);
}

/// 认证远程数据源实现
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final AuthApiService _apiService;

  AuthRemoteDataSourceImpl(this._apiService);

  @override
  Future<Either<Failure, AuthResponse>> register(RegisterRequest request) async {
    try {
      final response = await _apiService.register(request);
      return Right(response);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, AuthResponse>> login(LoginRequest request) async {
    try {
      final response = await _apiService.login(request);
      return Right(response);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, AuthResponse>> refreshToken(String refreshToken) async {
    try {
      final response = await _apiService.refreshToken('Bearer $refreshToken');
      return Right(response);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, void>> logout(String accessToken) async {
    try {
      await _apiService.logout('Bearer $accessToken');
      return const Right(null);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, void>> sendVerifyCode(SendVerifyCodeRequest request) async {
    try {
      await _apiService.sendVerifyCode(request);
      return const Right(null);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, bool>> verifyCode(VerifyCodeRequest request) async {
    try {
      final result = await _apiService.verifyCode(request);
      return Right(result);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword(ResetPasswordRequest request) async {
    try {
      await _apiService.resetPassword(request);
      return const Right(null);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, void>> changePassword(String accessToken, ChangePasswordRequest request) async {
    try {
      await _apiService.changePassword('Bearer $accessToken', request);
      return const Right(null);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, void>> bindPhone(String accessToken, BindPhoneRequest request) async {
    try {
      await _apiService.bindPhone('Bearer $accessToken', request);
      return const Right(null);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, void>> bindEmail(String accessToken, BindEmailRequest request) async {
    try {
      await _apiService.bindEmail('Bearer $accessToken', request);
      return const Right(null);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, bool>> checkUsername(String username) async {
    try {
      final exists = await _apiService.checkUsername(username);
      return Right(exists);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, bool>> checkPhone(String phone) async {
    try {
      final exists = await _apiService.checkPhone(phone);
      return Right(exists);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, bool>> checkEmail(String email) async {
    try {
      final exists = await _apiService.checkEmail(email);
      return Right(exists);
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, User>> getUserInfo(int userId, String accessToken) async {
    try {
      final response = await _apiService.getUserInfo(userId, 'Bearer $accessToken');
      return Right(response.toUser());
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  @override
  Future<Either<Failure, User>> updateUserInfo(int userId, String accessToken, UpdateUserRequest request) async {
    try {
      final response = await _apiService.updateUserInfo(userId, 'Bearer $accessToken', request);
      return Right(response.toUser());
    } catch (e) {
      return Left(_handleError(e));
    }
  }

  /// 处理错误
  Failure _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return const NetworkFailure('网络连接超时');
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['message'] ?? '请求失败';
          
          switch (statusCode) {
            case 400:
              return ServerFailure('参数错误: $message');
            case 401:
              return const AuthFailure('认证失败，请重新登录');
            case 403:
              return const AuthFailure('权限不足');
            case 404:
              return const ServerFailure('请求的资源不存在');
            case 409:
              return ServerFailure('资源冲突: $message');
            case 422:
              return ServerFailure('参数验证失败: $message');
            case 500:
              return const ServerFailure('服务器内部错误');
            default:
              return ServerFailure('服务器错误: $message');
          }
        case DioExceptionType.cancel:
          return const NetworkFailure('请求已取消');
        case DioExceptionType.unknown:
          return const NetworkFailure('网络连接失败');
        default:
          return NetworkFailure('网络错误: ${error.message}');
      }
    }
    
    return ServerFailure('未知错误: ${error.toString()}');
  }
}
