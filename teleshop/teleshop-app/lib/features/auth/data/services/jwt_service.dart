import 'dart:convert';
import 'package:jwt_decoder/jwt_decoder.dart';

/// JWT令牌管理服务
class JwtService {
  static final JwtService _instance = JwtService._internal();
  factory JwtService() => _instance;
  JwtService._internal();

  /// 解码JWT令牌
  Map<String, dynamic>? decodeToken(String token) {
    try {
      return JwtDecoder.decode(token);
    } catch (e) {
      return null;
    }
  }

  /// 检查JWT令牌是否过期
  bool isTokenExpired(String token) {
    try {
      return JwtDecoder.isExpired(token);
    } catch (e) {
      return true;
    }
  }

  /// 获取JWT令牌的过期时间
  DateTime? getTokenExpirationDate(String token) {
    try {
      return JwtDecoder.getExpirationDate(token);
    } catch (e) {
      return null;
    }
  }

  /// 获取JWT令牌的剩余有效时间（秒）
  Duration? getTokenRemainingTime(String token) {
    try {
      final expirationDate = getTokenExpirationDate(token);
      if (expirationDate == null) return null;
      
      final now = DateTime.now();
      if (expirationDate.isBefore(now)) {
        return Duration.zero;
      }
      
      return expirationDate.difference(now);
    } catch (e) {
      return null;
    }
  }

  /// 检查JWT令牌是否即将过期（默认5分钟内）
  bool isTokenExpiringSoon(String token, {Duration threshold = const Duration(minutes: 5)}) {
    try {
      final remainingTime = getTokenRemainingTime(token);
      if (remainingTime == null) return true;
      
      return remainingTime <= threshold;
    } catch (e) {
      return true;
    }
  }

  /// 从JWT令牌中获取用户ID
  int? getUserIdFromToken(String token) {
    try {
      final payload = decodeToken(token);
      if (payload == null) return null;
      
      // 尝试不同的字段名
      final userId = payload['userId'] ?? 
                    payload['user_id'] ?? 
                    payload['sub'] ?? 
                    payload['id'];
      
      if (userId is int) {
        return userId;
      } else if (userId is String) {
        return int.tryParse(userId);
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 从JWT令牌中获取用户名
  String? getUsernameFromToken(String token) {
    try {
      final payload = decodeToken(token);
      if (payload == null) return null;
      
      return payload['username'] ?? 
             payload['user_name'] ?? 
             payload['name'];
    } catch (e) {
      return null;
    }
  }

  /// 从JWT令牌中获取用户角色
  List<String> getRolesFromToken(String token) {
    try {
      final payload = decodeToken(token);
      if (payload == null) return [];
      
      final roles = payload['roles'] ?? 
                   payload['authorities'] ?? 
                   payload['permissions'] ?? 
                   [];
      
      if (roles is List) {
        return roles.map((role) => role.toString()).toList();
      } else if (roles is String) {
        // 如果是逗号分隔的字符串
        return roles.split(',').map((role) => role.trim()).toList();
      }
      
      return [];
    } catch (e) {
      return [];
    }
  }

  /// 从JWT令牌中获取权限列表
  List<String> getPermissionsFromToken(String token) {
    try {
      final payload = decodeToken(token);
      if (payload == null) return [];
      
      final permissions = payload['permissions'] ?? 
                         payload['authorities'] ?? 
                         payload['scopes'] ?? 
                         [];
      
      if (permissions is List) {
        return permissions.map((permission) => permission.toString()).toList();
      } else if (permissions is String) {
        // 如果是逗号分隔的字符串
        return permissions.split(',').map((permission) => permission.trim()).toList();
      }
      
      return [];
    } catch (e) {
      return [];
    }
  }

  /// 检查用户是否有指定角色
  bool hasRole(String token, String role) {
    final roles = getRolesFromToken(token);
    return roles.contains(role);
  }

  /// 检查用户是否有指定权限
  bool hasPermission(String token, String permission) {
    final permissions = getPermissionsFromToken(token);
    return permissions.contains(permission);
  }

  /// 检查用户是否有任意一个指定角色
  bool hasAnyRole(String token, List<String> roles) {
    final userRoles = getRolesFromToken(token);
    return roles.any((role) => userRoles.contains(role));
  }

  /// 检查用户是否有任意一个指定权限
  bool hasAnyPermission(String token, List<String> permissions) {
    final userPermissions = getPermissionsFromToken(token);
    return permissions.any((permission) => userPermissions.contains(permission));
  }

  /// 获取JWT令牌的签发时间
  DateTime? getTokenIssuedAt(String token) {
    try {
      final payload = decodeToken(token);
      if (payload == null) return null;
      
      final iat = payload['iat'];
      if (iat is int) {
        return DateTime.fromMillisecondsSinceEpoch(iat * 1000);
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 获取JWT令牌的签发者
  String? getTokenIssuer(String token) {
    try {
      final payload = decodeToken(token);
      if (payload == null) return null;
      
      return payload['iss'];
    } catch (e) {
      return null;
    }
  }

  /// 获取JWT令牌的受众
  String? getTokenAudience(String token) {
    try {
      final payload = decodeToken(token);
      if (payload == null) return null;
      
      return payload['aud'];
    } catch (e) {
      return null;
    }
  }

  /// 验证JWT令牌的基本格式
  bool isValidTokenFormat(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return false;
      
      // 尝试解码每个部分
      for (final part in parts) {
        base64Url.decode(base64Url.normalize(part));
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取JWT令牌的完整信息
  JwtTokenInfo? getTokenInfo(String token) {
    try {
      final payload = decodeToken(token);
      if (payload == null) return null;
      
      return JwtTokenInfo(
        userId: getUserIdFromToken(token),
        username: getUsernameFromToken(token),
        roles: getRolesFromToken(token),
        permissions: getPermissionsFromToken(token),
        issuedAt: getTokenIssuedAt(token),
        expirationDate: getTokenExpirationDate(token),
        issuer: getTokenIssuer(token),
        audience: getTokenAudience(token),
        isExpired: isTokenExpired(token),
        remainingTime: getTokenRemainingTime(token),
      );
    } catch (e) {
      return null;
    }
  }
}

/// JWT令牌信息
class JwtTokenInfo {
  final int? userId;
  final String? username;
  final List<String> roles;
  final List<String> permissions;
  final DateTime? issuedAt;
  final DateTime? expirationDate;
  final String? issuer;
  final String? audience;
  final bool isExpired;
  final Duration? remainingTime;

  const JwtTokenInfo({
    this.userId,
    this.username,
    this.roles = const [],
    this.permissions = const [],
    this.issuedAt,
    this.expirationDate,
    this.issuer,
    this.audience,
    this.isExpired = false,
    this.remainingTime,
  });

  @override
  String toString() {
    return 'JwtTokenInfo(userId: $userId, username: $username, '
           'roles: $roles, permissions: $permissions, '
           'isExpired: $isExpired, remainingTime: $remainingTime)';
  }
}
