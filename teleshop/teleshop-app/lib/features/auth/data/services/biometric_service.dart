import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_ios/local_auth_ios.dart';

/// 生物识别认证服务
class BiometricService {
  static final BiometricService _instance = BiometricService._internal();
  factory BiometricService() => _instance;
  BiometricService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();

  /// 检查设备是否支持生物识别
  Future<bool> isDeviceSupported() async {
    try {
      return await _localAuth.isDeviceSupported();
    } catch (e) {
      return false;
    }
  }

  /// 检查是否有可用的生物识别方法
  Future<bool> canCheckBiometrics() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      return false;
    }
  }

  /// 获取可用的生物识别类型
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  /// 检查是否支持指纹识别
  Future<bool> isFingerprintAvailable() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();
      return availableBiometrics.contains(BiometricType.fingerprint);
    } catch (e) {
      return false;
    }
  }

  /// 检查是否支持面容识别
  Future<bool> isFaceIdAvailable() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();
      return availableBiometrics.contains(BiometricType.face);
    } catch (e) {
      return false;
    }
  }

  /// 执行生物识别认证
  Future<BiometricAuthResult> authenticate({
    String localizedReason = '请验证您的身份',
    bool useErrorDialogs = true,
    bool stickyAuth = false,
    bool sensitiveTransaction = true,
    bool biometricOnly = false,
  }) async {
    try {
      // 检查设备支持
      if (!await isDeviceSupported()) {
        return BiometricAuthResult.notSupported('设备不支持生物识别');
      }

      // 检查是否可用
      if (!await canCheckBiometrics()) {
        return BiometricAuthResult.notAvailable('生物识别不可用');
      }

      // 检查是否有可用的生物识别方法
      final availableBiometrics = await getAvailableBiometrics();
      if (availableBiometrics.isEmpty) {
        return BiometricAuthResult.notEnrolled('未设置生物识别');
      }

      // 执行认证
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: localizedReason,
        authMessages: const [
          AndroidAuthMessages(
            signInTitle: '生物识别认证',
            cancelButton: '取消',
            deviceCredentialsRequiredTitle: '需要设备凭据',
            deviceCredentialsSetupDescription: '请在设备设置中设置生物识别',
            goToSettingsButton: '去设置',
            goToSettingsDescription: '请在设置中启用生物识别',
          ),
          IOSAuthMessages(
            cancelButton: '取消',
            goToSettingsButton: '去设置',
            goToSettingsDescription: '请在设置中启用生物识别',
            lockOut: '生物识别已锁定，请使用密码解锁',
          ),
        ],
        options: AuthenticationOptions(
          useErrorDialogs: useErrorDialogs,
          stickyAuth: stickyAuth,
          sensitiveTransaction: sensitiveTransaction,
          biometricOnly: biometricOnly,
        ),
      );

      if (didAuthenticate) {
        return BiometricAuthResult.success();
      } else {
        return BiometricAuthResult.failed('认证失败');
      }
    } catch (e) {
      return BiometricAuthResult.error('认证过程中发生错误: $e');
    }
  }

  /// 停止认证
  Future<bool> stopAuthentication() async {
    try {
      return await _localAuth.stopAuthentication();
    } catch (e) {
      return false;
    }
  }

  /// 获取生物识别类型的显示名称
  String getBiometricTypeName(BiometricType type) {
    switch (type) {
      case BiometricType.face:
        return '面容识别';
      case BiometricType.fingerprint:
        return '指纹识别';
      case BiometricType.iris:
        return '虹膜识别';
      case BiometricType.weak:
        return '弱生物识别';
      case BiometricType.strong:
        return '强生物识别';
      default:
        return '生物识别';
    }
  }

  /// 获取可用生物识别方法的描述
  Future<String> getAvailableBiometricsDescription() async {
    final availableBiometrics = await getAvailableBiometrics();
    if (availableBiometrics.isEmpty) {
      return '无可用的生物识别方法';
    }

    final names = availableBiometrics
        .map((type) => getBiometricTypeName(type))
        .toList();
    
    if (names.length == 1) {
      return names.first;
    } else {
      return names.join('、');
    }
  }
}

/// 生物识别认证结果
class BiometricAuthResult {
  final bool isSuccess;
  final String? message;
  final BiometricAuthResultType type;

  const BiometricAuthResult._({
    required this.isSuccess,
    this.message,
    required this.type,
  });

  factory BiometricAuthResult.success() {
    return const BiometricAuthResult._(
      isSuccess: true,
      type: BiometricAuthResultType.success,
    );
  }

  factory BiometricAuthResult.failed(String message) {
    return BiometricAuthResult._(
      isSuccess: false,
      message: message,
      type: BiometricAuthResultType.failed,
    );
  }

  factory BiometricAuthResult.error(String message) {
    return BiometricAuthResult._(
      isSuccess: false,
      message: message,
      type: BiometricAuthResultType.error,
    );
  }

  factory BiometricAuthResult.notSupported(String message) {
    return BiometricAuthResult._(
      isSuccess: false,
      message: message,
      type: BiometricAuthResultType.notSupported,
    );
  }

  factory BiometricAuthResult.notAvailable(String message) {
    return BiometricAuthResult._(
      isSuccess: false,
      message: message,
      type: BiometricAuthResultType.notAvailable,
    );
  }

  factory BiometricAuthResult.notEnrolled(String message) {
    return BiometricAuthResult._(
      isSuccess: false,
      message: message,
      type: BiometricAuthResultType.notEnrolled,
    );
  }
}

/// 生物识别认证结果类型
enum BiometricAuthResultType {
  success,
  failed,
  error,
  notSupported,
  notAvailable,
  notEnrolled,
}
