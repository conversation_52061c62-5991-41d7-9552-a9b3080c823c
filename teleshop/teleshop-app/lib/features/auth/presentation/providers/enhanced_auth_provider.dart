import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/services/biometric_service.dart';
import '../../data/services/jwt_service.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../models/auth_request.dart';
import '../../models/auth_response.dart';
import '../../models/user.dart';
import 'auth_provider.dart';
import 'auth_state.dart';

/// 增强版认证服务提供者
final biometricServiceProvider = Provider<BiometricService>((ref) {
  return BiometricService();
});

final jwtServiceProvider = Provider<JwtService>((ref) {
  return JwtService();
});

/// 增强版认证状态提供者
class EnhancedAuthNotifier extends StateNotifier<AuthState> {
  EnhancedAuthNotifier(this._ref) : super(const AuthState.initial()) {
    _initialize();
  }

  final Ref _ref;
  Timer? _tokenRefreshTimer;

  AuthRepository get _authRepository => _ref.read(authRepositoryProvider);
  BiometricService get _biometricService => _ref.read(biometricServiceProvider);
  JwtService get _jwtService => _ref.read(jwtServiceProvider);

  @override
  void dispose() {
    _tokenRefreshTimer?.cancel();
    super.dispose();
  }

  /// 初始化认证状态
  Future<void> _initialize() async {
    state = const AuthState.loading();

    try {
      final isLoggedIn = await _authRepository.isLoggedIn();
      if (!isLoggedIn) {
        state = const AuthState.unauthenticated();
        return;
      }

      final accessToken = await _authRepository.getAccessToken();
      if (accessToken == null) {
        state = const AuthState.unauthenticated();
        return;
      }

      // 检查token是否过期
      if (_jwtService.isTokenExpired(accessToken)) {
        // 尝试刷新token
        final refreshResult = await _authRepository.refreshToken();
        await refreshResult.fold(
          (failure) async {
            state = const AuthState.unauthenticated();
          },
          (authResponse) async {
            await _handleSuccessfulAuth(authResponse);
          },
        );
        return;
      }

      // 获取用户信息
      final userResult = await _authRepository.getCurrentUser();
      await userResult.fold(
        (failure) async {
          state = const AuthState.unauthenticated();
        },
        (user) async {
          state = AuthState.authenticated(user);
          _startTokenRefreshTimer(accessToken);
        },
      );
    } catch (e) {
      state = const AuthState.unauthenticated();
    }
  }

  /// 用户名密码登录
  Future<void> login(LoginRequest request) async {
    state = const AuthState.loading();

    try {
      final result = await _authRepository.login(request);
      await result.fold(
        (failure) async {
          state = AuthState.error(failure.message);
        },
        (authResponse) async {
          await _handleSuccessfulAuth(authResponse);
        },
      );
    } catch (e) {
      state = AuthState.error('登录失败: $e');
    }
  }

  /// 生物识别登录
  Future<void> biometricLogin() async {
    try {
      // 检查是否启用生物识别
      final biometricEnabled = await _authRepository.getBiometricEnabled();
      if (!biometricEnabled) {
        state = const AuthState.error('未启用生物识别登录');
        return;
      }

      // 检查设备支持
      if (!await _biometricService.isDeviceSupported()) {
        state = const AuthState.error('设备不支持生物识别');
        return;
      }

      state = const AuthState.loading();

      // 执行生物识别认证
      final biometricResult = await _biometricService.authenticate(
        localizedReason: '请验证您的身份以登录TeleShop',
        sensitiveTransaction: true,
      );

      if (!biometricResult.isSuccess) {
        state = AuthState.error(biometricResult.message ?? '生物识别认证失败');
        return;
      }

      // 生物识别成功，获取保存的用户信息
      final userResult = await _authRepository.getCurrentUser();
      await userResult.fold(
        (failure) async {
          state = const AuthState.error('获取用户信息失败');
        },
        (user) async {
          state = AuthState.authenticated(user);
          
          // 启动token刷新定时器
          final accessToken = await _authRepository.getAccessToken();
          if (accessToken != null) {
            _startTokenRefreshTimer(accessToken);
          }
        },
      );
    } catch (e) {
      state = AuthState.error('生物识别登录失败: $e');
    }
  }

  /// 注册
  Future<void> register(RegisterRequest request) async {
    state = const AuthState.loading();

    try {
      final result = await _authRepository.register(request);
      await result.fold(
        (failure) async {
          state = AuthState.error(failure.message);
        },
        (authResponse) async {
          await _handleSuccessfulAuth(authResponse);
        },
      );
    } catch (e) {
      state = AuthState.error('注册失败: $e');
    }
  }

  /// 发送验证码
  Future<void> sendVerifyCode(SendVerifyCodeRequest request) async {
    try {
      final result = await _authRepository.sendVerifyCode(request);
      result.fold(
        (failure) {
          state = AuthState.error(failure.message);
        },
        (_) {
          // 验证码发送成功，不改变当前状态
        },
      );
    } catch (e) {
      state = AuthState.error('发送验证码失败: $e');
    }
  }

  /// 退出登录
  Future<void> logout() async {
    try {
      _tokenRefreshTimer?.cancel();
      await _authRepository.logout();
      state = const AuthState.unauthenticated();
    } catch (e) {
      // 即使退出失败也要清除状态
      state = const AuthState.unauthenticated();
    }
  }

  /// 启用/禁用生物识别
  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      if (enabled) {
        // 检查设备支持
        if (!await _biometricService.isDeviceSupported()) {
          state = const AuthState.error('设备不支持生物识别');
          return;
        }

        // 执行一次生物识别验证
        final biometricResult = await _biometricService.authenticate(
          localizedReason: '请验证您的身份以启用生物识别登录',
        );

        if (!biometricResult.isSuccess) {
          state = AuthState.error(biometricResult.message ?? '生物识别验证失败');
          return;
        }
      }

      await _authRepository.setBiometricEnabled(enabled);
    } catch (e) {
      state = AuthState.error('设置生物识别失败: $e');
    }
  }

  /// 检查生物识别是否可用
  Future<bool> isBiometricAvailable() async {
    try {
      return await _biometricService.canCheckBiometrics() &&
             await _biometricService.isDeviceSupported();
    } catch (e) {
      return false;
    }
  }

  /// 获取可用的生物识别类型描述
  Future<String> getBiometricDescription() async {
    try {
      return await _biometricService.getAvailableBiometricsDescription();
    } catch (e) {
      return '生物识别';
    }
  }

  /// 处理成功认证
  Future<void> _handleSuccessfulAuth(AuthResponse authResponse) async {
    if (authResponse.user != null) {
      state = AuthState.authenticated(authResponse.user!);
      
      // 启动token刷新定时器
      if (authResponse.accessToken != null) {
        _startTokenRefreshTimer(authResponse.accessToken!);
      }
    } else {
      state = const AuthState.error('用户信息获取失败');
    }
  }

  /// 启动token刷新定时器
  void _startTokenRefreshTimer(String accessToken) {
    _tokenRefreshTimer?.cancel();

    final remainingTime = _jwtService.getTokenRemainingTime(accessToken);
    if (remainingTime == null) return;

    // 在token过期前5分钟刷新
    final refreshTime = remainingTime - const Duration(minutes: 5);
    if (refreshTime.isNegative) {
      // 如果剩余时间不足5分钟，立即刷新
      _refreshToken();
      return;
    }

    _tokenRefreshTimer = Timer(refreshTime, () {
      _refreshToken();
    });
  }

  /// 刷新token
  Future<void> _refreshToken() async {
    try {
      final result = await _authRepository.refreshToken();
      result.fold(
        (failure) {
          // token刷新失败，需要重新登录
          state = const AuthState.unauthenticated();
        },
        (authResponse) {
          // token刷新成功，重新启动定时器
          if (authResponse.accessToken != null) {
            _startTokenRefreshTimer(authResponse.accessToken!);
          }
        },
      );
    } catch (e) {
      state = const AuthState.unauthenticated();
    }
  }

  /// 获取当前用户
  User? get currentUser {
    return state.maybeWhen(
      authenticated: (user) => user,
      orElse: () => null,
    );
  }

  /// 检查是否已登录
  bool get isAuthenticated {
    return state.maybeWhen(
      authenticated: (_) => true,
      orElse: () => false,
    );
  }

  /// 检查是否正在加载
  bool get isLoading {
    return state.maybeWhen(
      loading: () => true,
      orElse: () => false,
    );
  }
}

/// 增强版认证状态提供者
final enhancedAuthProvider = StateNotifierProvider<EnhancedAuthNotifier, AuthState>((ref) {
  return EnhancedAuthNotifier(ref);
});
