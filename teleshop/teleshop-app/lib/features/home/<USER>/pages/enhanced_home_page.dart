import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:teleshop_app/features/recommendation/presentation/providers/recommendation_provider.dart';
import 'package:teleshop_app/features/marketing/presentation/providers/marketing_provider.dart';
import 'package:teleshop_app/features/user_growth/presentation/providers/user_growth_provider.dart';
import 'package:teleshop_app/features/home/<USER>/widgets/home_widgets.dart';
import 'package:teleshop_app/widgets/common/loading_widget.dart';

/// 增强版主页
/// 集成了商品推荐、营销活动、用户成长等所有功能模块
class EnhancedHomePage extends ConsumerStatefulWidget {
  const EnhancedHomePage({super.key});

  @override
  ConsumerState<EnhancedHomePage> createState() => _EnhancedHomePageState();
}

class _EnhancedHomePageState extends ConsumerState<EnhancedHomePage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadHomeData();
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadHomeData() {
    // 加载推荐数据
    ref.read(recommendationProvider.notifier).loadPersonalizedRecommendations();
    ref.read(recommendationProvider.notifier).loadHotRecommendations();
    ref.read(recommendationProvider.notifier).loadNewArrivals();
    
    // 加载营销活动数据
    ref.read(marketingProvider.notifier).loadFlashSales();
    ref.read(marketingProvider.notifier).loadGroupBuys();
    
    // 加载用户成长数据
    ref.read(userGrowthProvider.notifier).loadUserLevel();
    ref.read(userGrowthProvider.notifier).loadUserPoints();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: RefreshIndicator(
          onRefresh: () async {
            _loadHomeData();
          },
          child: CustomScrollView(
            controller: _scrollController,
            slivers: [
              _buildAppBar(),
              _buildSearchBar(),
              _buildUserGrowthCard(),
              _buildQuickActions(),
              _buildFlashSaleSection(),
              _buildGroupBuySection(),
              _buildRecommendationSection(),
              _buildHotProductsSection(),
              _buildNewArrivalsSection(),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120.h,
      floating: false,
      pinned: true,
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        title: Row(
          children: [
            Icon(Icons.shopping_bag, size: 24.sp),
            SizedBox(width: 8.w),
            const Text('TeleShop'),
          ],
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () => Navigator.of(context).pushNamed('/notifications'),
        ),
        IconButton(
          icon: const Icon(Icons.shopping_cart_outlined),
          onPressed: () => Navigator.of(context).pushNamed('/cart'),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.all(16.w),
        child: GestureDetector(
          onTap: () => Navigator.of(context).pushNamed('/search'),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(Icons.search, color: Colors.grey[400], size: 20.sp),
                SizedBox(width: 12.w),
                Text(
                  '搜索商品、品牌、店铺',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserGrowthCard() {
    return SliverToBoxAdapter(
      child: Consumer(
        builder: (context, ref, child) {
          final growthState = ref.watch(userGrowthProvider);
          final userLevel = growthState.userLevel;
          final userPoints = growthState.userPoints;

          if (userLevel == null && userPoints == null) {
            return const SizedBox.shrink();
          }

          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.purple.shade400, Colors.purple.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userLevel?.currentLevelName ?? '新用户',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '积分：${userPoints?.currentPoints ?? 0}',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14.sp,
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pushNamed('/user-growth'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.purple,
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  ),
                  child: const Text('成长中心'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildQuickActions() {
    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '快速入口',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionItem(
                    '限时抢购',
                    Icons.flash_on,
                    Colors.red,
                    () => Navigator.of(context).pushNamed('/flash-sale'),
                  ),
                ),
                Expanded(
                  child: _buildQuickActionItem(
                    '团购',
                    Icons.group,
                    Colors.purple,
                    () => Navigator.of(context).pushNamed('/group-buy'),
                  ),
                ),
                Expanded(
                  child: _buildQuickActionItem(
                    '积分商城',
                    Icons.store,
                    Colors.orange,
                    () => Navigator.of(context).pushNamed('/points-mall'),
                  ),
                ),
                Expanded(
                  child: _buildQuickActionItem(
                    '售后服务',
                    Icons.support_agent,
                    Colors.blue,
                    () => Navigator.of(context).pushNamed('/after-sales'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionItem(String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48.w,
            height: 48.h,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(24.r),
            ),
            child: Icon(icon, color: color, size: 24.sp),
          ),
          SizedBox(height: 8.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlashSaleSection() {
    return SliverToBoxAdapter(
      child: Consumer(
        builder: (context, ref, child) {
          final marketingState = ref.watch(marketingProvider);
          final flashSales = marketingState.activeFlashSales;

          if (flashSales.isEmpty) return const SizedBox.shrink();

          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.flash_on, color: Colors.red, size: 20.sp),
                    SizedBox(width: 8.w),
                    Text(
                      '限时抢购',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () => Navigator.of(context).pushNamed('/flash-sale'),
                      child: const Text('查看更多'),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                SizedBox(
                  height: 200.h,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: flashSales.length,
                    itemBuilder: (context, index) {
                      final flashSale = flashSales[index];
                      return Container(
                        width: 160.w,
                        margin: EdgeInsets.only(right: 12.w),
                        child: FlashSaleCard(
                          flashSale: flashSale,
                          onTap: () => Navigator.of(context).pushNamed(
                            '/flash-sale-detail',
                            arguments: flashSale,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildGroupBuySection() {
    return SliverToBoxAdapter(
      child: Consumer(
        builder: (context, ref, child) {
          final marketingState = ref.watch(marketingProvider);
          final groupBuys = marketingState.groupBuys;

          if (groupBuys.isEmpty) return const SizedBox.shrink();

          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.group, color: Colors.purple, size: 20.sp),
                    SizedBox(width: 8.w),
                    Text(
                      '团购活动',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () => Navigator.of(context).pushNamed('/group-buy'),
                      child: const Text('查看更多'),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                SizedBox(
                  height: 200.h,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: groupBuys.length,
                    itemBuilder: (context, index) {
                      final groupBuy = groupBuys[index];
                      return Container(
                        width: 160.w,
                        margin: EdgeInsets.only(right: 12.w),
                        child: GroupBuyCard(
                          groupBuy: groupBuy,
                          onTap: () => Navigator.of(context).pushNamed(
                            '/group-buy-detail',
                            arguments: groupBuy,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildRecommendationSection() {
    return SliverToBoxAdapter(
      child: Consumer(
        builder: (context, ref, child) {
          final recommendationState = ref.watch(recommendationProvider);
          final recommendations = recommendationState.personalizedRecommendations;

          if (recommendations.isEmpty) return const SizedBox.shrink();

          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.recommend, color: Colors.blue, size: 20.sp),
                    SizedBox(width: 8.w),
                    Text(
                      '为你推荐',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () => Navigator.of(context).pushNamed('/recommendation'),
                      child: const Text('查看更多'),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.75,
                    crossAxisSpacing: 12.w,
                    mainAxisSpacing: 12.h,
                  ),
                  itemCount: recommendations.length > 4 ? 4 : recommendations.length,
                  itemBuilder: (context, index) {
                    final product = recommendations[index];
                    return ProductCard(
                      product: product,
                      onTap: () => Navigator.of(context).pushNamed(
                        '/product-detail',
                        arguments: product,
                      ),
                    );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHotProductsSection() {
    return SliverToBoxAdapter(
      child: Consumer(
        builder: (context, ref, child) {
          final recommendationState = ref.watch(recommendationProvider);
          final hotProducts = recommendationState.hotRecommendations;

          if (hotProducts.isEmpty) return const SizedBox.shrink();

          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.local_fire_department, color: Colors.orange, size: 20.sp),
                    SizedBox(width: 8.w),
                    Text(
                      '热门商品',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () => Navigator.of(context).pushNamed('/recommendation'),
                      child: const Text('查看更多'),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                SizedBox(
                  height: 200.h,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: hotProducts.length,
                    itemBuilder: (context, index) {
                      final product = hotProducts[index];
                      return Container(
                        width: 140.w,
                        margin: EdgeInsets.only(right: 12.w),
                        child: ProductCard(
                          product: product,
                          onTap: () => Navigator.of(context).pushNamed(
                            '/product-detail',
                            arguments: product,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildNewArrivalsSection() {
    return SliverToBoxAdapter(
      child: Consumer(
        builder: (context, ref, child) {
          final recommendationState = ref.watch(recommendationProvider);
          final newArrivals = recommendationState.newArrivals;

          if (newArrivals.isEmpty) return const SizedBox.shrink();

          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.new_releases, color: Colors.green, size: 20.sp),
                    SizedBox(width: 8.w),
                    Text(
                      '新品上架',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () => Navigator.of(context).pushNamed('/recommendation'),
                      child: const Text('查看更多'),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                SizedBox(
                  height: 200.h,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: newArrivals.length,
                    itemBuilder: (context, index) {
                      final product = newArrivals[index];
                      return Container(
                        width: 140.w,
                        margin: EdgeInsets.only(right: 12.w),
                        child: ProductCard(
                          product: product,
                          onTap: () => Navigator.of(context).pushNamed(
                            '/product-detail',
                            arguments: product,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFooter() {
    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.all(16.w),
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          children: [
            Text(
              '已经到底了～',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '更多精彩内容，敬请期待',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
