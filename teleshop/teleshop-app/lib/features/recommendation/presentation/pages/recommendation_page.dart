import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:teleshop_app/features/recommendation/presentation/providers/recommendation_provider.dart';
import 'package:teleshop_app/features/recommendation/presentation/widgets/recommendation_widgets.dart';
import 'package:teleshop_app/features/shop/domain/entities/product.dart';
import 'package:teleshop_app/widgets/common/loading_widget.dart';
import 'package:teleshop_app/widgets/common/error_widget.dart';

/// 商品推荐页面
/// 支持个性化推荐、热门商品、新品推荐、相似商品等多种推荐场景
class RecommendationPage extends ConsumerStatefulWidget {
  final String? scene;
  final String? productId;
  final String? categoryId;

  const RecommendationPage({
    super.key,
    this.scene,
    this.productId,
    this.categoryId,
  });

  @override
  ConsumerState<RecommendationPage> createState() => _RecommendationPageState();
}

class _RecommendationPageState extends ConsumerState<RecommendationPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _scrollController.addListener(_onScroll);
    
    // 初始化推荐数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadRecommendations();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      // 触发加载更多
      _loadMoreRecommendations();
    }
  }

  void _loadRecommendations() {
    final notifier = ref.read(recommendationProvider.notifier);
    notifier.loadPersonalizedRecommendations();
    notifier.loadHotRecommendations();
    notifier.loadNewArrivals();
    notifier.loadGuessYouLike();
    
    if (widget.productId != null) {
      notifier.loadSimilarRecommendations(widget.productId!);
    }
  }

  void _loadMoreRecommendations() {
    final currentTab = _tabController.index;
    final notifier = ref.read(recommendationProvider.notifier);
    
    switch (currentTab) {
      case 0:
        notifier.loadMorePersonalizedRecommendations();
        break;
      case 1:
        notifier.loadMoreHotRecommendations();
        break;
      case 2:
        notifier.loadMoreNewArrivals();
        break;
      case 3:
        notifier.loadMoreGuessYouLike();
        break;
      case 4:
        if (widget.productId != null) {
          notifier.loadMoreSimilarRecommendations(widget.productId!);
        }
        break;
      case 5:
        notifier.loadMoreHistoryBasedRecommendations();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final recommendationState = ref.watch(recommendationProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: recommendationState.isLoading && recommendationState.isEmpty
          ? const LoadingWidget()
          : recommendationState.error != null && recommendationState.isEmpty
              ? ErrorWidget(
                  message: recommendationState.error!,
                  onRetry: _loadRecommendations,
                )
              : _buildContent(recommendationState),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('商品推荐'),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        indicatorColor: Colors.white,
        indicatorWeight: 3,
        tabs: const [
          Tab(text: '为你推荐'),
          Tab(text: '热门商品'),
          Tab(text: '新品上架'),
          Tab(text: '猜你喜欢'),
          Tab(text: '相似商品'),
          Tab(text: '浏览历史'),
        ],
      ),
    );
  }

  Widget _buildContent(RecommendationState state) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildPersonalizedTab(state.personalizedRecommendations),
        _buildHotTab(state.hotRecommendations),
        _buildNewArrivalsTab(state.newArrivals),
        _buildGuessYouLikeTab(state.guessYouLike),
        _buildSimilarTab(state.similarRecommendations),
        _buildHistoryBasedTab(state.historyBasedRecommendations),
      ],
    );
  }

  Widget _buildPersonalizedTab(List<Product> products) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.read(recommendationProvider.notifier).loadPersonalizedRecommendations();
      },
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 推荐理由说明
          SliverToBoxAdapter(
            child: Container(
              margin: EdgeInsets.all(16.w),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.lightbulb_outline, color: Colors.blue.shade600),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      '基于您的浏览历史和购买偏好为您精心推荐',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 商品网格
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            sliver: SliverGrid(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < products.length) {
                    return RecommendationProductCard(
                      product: products[index],
                      recommendationType: 'personalized',
                      onTap: () => _onProductTap(products[index]),
                      onAddToCart: () => _onAddToCart(products[index]),
                    );
                  } else {
                    return const LoadingProductCard();
                  }
                },
                childCount: products.length + (ref.watch(recommendationProvider).isLoadingMore ? 2 : 0),
              ),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 12.w,
                mainAxisSpacing: 12.h,
              ),
            ),
          ),
          
          SliverToBoxAdapter(child: SizedBox(height: 20.h)),
        ],
      ),
    );
  }

  Widget _buildHotTab(List<Product> products) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.read(recommendationProvider.notifier).loadHotRecommendations();
      },
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 热门商品排行榜
          SliverToBoxAdapter(
            child: Container(
              margin: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.local_fire_department, color: Colors.red),
                      SizedBox(width: 8.w),
                      Text(
                        '热门排行榜',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '基于销量、浏览量、收藏量综合排名',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 热门商品列表
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < products.length) {
                    return HotProductRankCard(
                      product: products[index],
                      rank: index + 1,
                      onTap: () => _onProductTap(products[index]),
                    );
                  } else {
                    return const LoadingProductCard();
                  }
                },
                childCount: products.length + (ref.watch(recommendationProvider).isLoadingMore ? 2 : 0),
              ),
            ),
          ),
          
          SliverToBoxAdapter(child: SizedBox(height: 20.h)),
        ],
      ),
    );
  }

  Widget _buildNewArrivalsTab(List<Product> products) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.read(recommendationProvider.notifier).loadNewArrivals();
      },
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 新品标题
          SliverToBoxAdapter(
            child: Container(
              margin: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  Icon(Icons.new_releases, color: Colors.green),
                  SizedBox(width: 8.w),
                  Text(
                    '新品上架',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '最新上架商品',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 新品网格
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            sliver: SliverGrid(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < products.length) {
                    return NewArrivalProductCard(
                      product: products[index],
                      onTap: () => _onProductTap(products[index]),
                      onAddToCart: () => _onAddToCart(products[index]),
                    );
                  } else {
                    return const LoadingProductCard();
                  }
                },
                childCount: products.length + (ref.watch(recommendationProvider).isLoadingMore ? 2 : 0),
              ),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 12.w,
                mainAxisSpacing: 12.h,
              ),
            ),
          ),
          
          SliverToBoxAdapter(child: SizedBox(height: 20.h)),
        ],
      ),
    );
  }

  Widget _buildGuessYouLikeTab(List<Product> products) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.read(recommendationProvider.notifier).loadGuessYouLike();
      },
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 猜你喜欢说明
          SliverToBoxAdapter(
            child: Container(
              margin: EdgeInsets.all(16.w),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.purple.shade50,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: Colors.purple.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.favorite_outline, color: Colors.purple.shade600),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      '根据您的兴趣偏好智能推荐',
                      style: TextStyle(
                        color: Colors.purple.shade700,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 商品网格
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            sliver: SliverGrid(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < products.length) {
                    return GuessYouLikeProductCard(
                      product: products[index],
                      onTap: () => _onProductTap(products[index]),
                      onAddToCart: () => _onAddToCart(products[index]),
                    );
                  } else {
                    return const LoadingProductCard();
                  }
                },
                childCount: products.length + (ref.watch(recommendationProvider).isLoadingMore ? 2 : 0),
              ),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 12.w,
                mainAxisSpacing: 12.h,
              ),
            ),
          ),
          
          SliverToBoxAdapter(child: SizedBox(height: 20.h)),
        ],
      ),
    );
  }

  Widget _buildSimilarTab(List<Product> products) {
    if (widget.productId == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info_outline, size: 64.sp, color: Colors.grey),
            SizedBox(height: 16.h),
            Text(
              '请先选择一个商品查看相似推荐',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.read(recommendationProvider.notifier).loadSimilarRecommendations(widget.productId!);
      },
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 相似商品说明
          SliverToBoxAdapter(
            child: Container(
              margin: EdgeInsets.all(16.w),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.compare_arrows, color: Colors.orange.shade600),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      '与您浏览的商品相似的其他商品',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 相似商品网格
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            sliver: SliverGrid(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < products.length) {
                    return SimilarProductCard(
                      product: products[index],
                      onTap: () => _onProductTap(products[index]),
                      onAddToCart: () => _onAddToCart(products[index]),
                    );
                  } else {
                    return const LoadingProductCard();
                  }
                },
                childCount: products.length + (ref.watch(recommendationProvider).isLoadingMore ? 2 : 0),
              ),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 12.w,
                mainAxisSpacing: 12.h,
              ),
            ),
          ),
          
          SliverToBoxAdapter(child: SizedBox(height: 20.h)),
        ],
      ),
    );
  }

  Widget _buildHistoryBasedTab(List<Product> products) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.read(recommendationProvider.notifier).loadHistoryBasedRecommendations();
      },
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 历史推荐说明
          SliverToBoxAdapter(
            child: Container(
              margin: EdgeInsets.all(16.w),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.teal.shade50,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: Colors.teal.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.history, color: Colors.teal.shade600),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      '基于您的浏览历史推荐相关商品',
                      style: TextStyle(
                        color: Colors.teal.shade700,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 历史推荐网格
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            sliver: SliverGrid(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index < products.length) {
                    return HistoryBasedProductCard(
                      product: products[index],
                      onTap: () => _onProductTap(products[index]),
                      onAddToCart: () => _onAddToCart(products[index]),
                    );
                  } else {
                    return const LoadingProductCard();
                  }
                },
                childCount: products.length + (ref.watch(recommendationProvider).isLoadingMore ? 2 : 0),
              ),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 12.w,
                mainAxisSpacing: 12.h,
              ),
            ),
          ),
          
          SliverToBoxAdapter(child: SizedBox(height: 20.h)),
        ],
      ),
    );
  }

  void _onProductTap(Product product) {
    // 记录用户行为
    ref.read(recommendationProvider.notifier).recordUserBehavior(
      productId: product.id,
      behaviorType: 'CLICK',
      pageSource: 'recommendation_page',
    );
    
    // 导航到商品详情页
    Navigator.of(context).pushNamed('/product-detail', arguments: product);
  }

  void _onAddToCart(Product product) {
    // 记录用户行为
    ref.read(recommendationProvider.notifier).recordUserBehavior(
      productId: product.id,
      behaviorType: 'ADD_TO_CART',
      pageSource: 'recommendation_page',
    );
    
    // 添加到购物车
    // TODO: 实现添加到购物车逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${product.name} 已添加到购物车')),
    );
  }
}
