import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:teleshop_app/features/recommendation/domain/usecases/recommendation_usecases.dart';
import 'package:teleshop_app/features/recommendation/data/repositories/recommendation_repository_impl.dart';
import 'package:teleshop_app/features/recommendation/data/datasources/recommendation_remote_data_source.dart';
import 'package:teleshop_app/features/shop/domain/entities/product.dart';
import 'package:teleshop_app/core/network/dio_client.dart';

/// 推荐状态
class RecommendationState {
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;
  final List<Product> personalizedRecommendations;
  final List<Product> hotRecommendations;
  final List<Product> newArrivals;
  final List<Product> guessYouLike;
  final List<Product> similarRecommendations;
  final List<Product> historyBasedRecommendations;
  final List<Product> cartBasedRecommendations;
  final Map<String, Object> stats;
  final int currentPage;
  final bool hasMore;

  const RecommendationState({
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
    this.personalizedRecommendations = const [],
    this.hotRecommendations = const [],
    this.newArrivals = const [],
    this.guessYouLike = const [],
    this.similarRecommendations = const [],
    this.historyBasedRecommendations = const [],
    this.cartBasedRecommendations = const [],
    this.stats = const {},
    this.currentPage = 1,
    this.hasMore = true,
  });

  RecommendationState copyWith({
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
    List<Product>? personalizedRecommendations,
    List<Product>? hotRecommendations,
    List<Product>? newArrivals,
    List<Product>? guessYouLike,
    List<Product>? similarRecommendations,
    List<Product>? historyBasedRecommendations,
    List<Product>? cartBasedRecommendations,
    Map<String, Object>? stats,
    int? currentPage,
    bool? hasMore,
  }) {
    return RecommendationState(
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error,
      personalizedRecommendations: personalizedRecommendations ?? this.personalizedRecommendations,
      hotRecommendations: hotRecommendations ?? this.hotRecommendations,
      newArrivals: newArrivals ?? this.newArrivals,
      guessYouLike: guessYouLike ?? this.guessYouLike,
      similarRecommendations: similarRecommendations ?? this.similarRecommendations,
      historyBasedRecommendations: historyBasedRecommendations ?? this.historyBasedRecommendations,
      cartBasedRecommendations: cartBasedRecommendations ?? this.cartBasedRecommendations,
      stats: stats ?? this.stats,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
    );
  }

  bool get isEmpty => 
      personalizedRecommendations.isEmpty &&
      hotRecommendations.isEmpty &&
      newArrivals.isEmpty &&
      guessYouLike.isEmpty &&
      similarRecommendations.isEmpty &&
      historyBasedRecommendations.isEmpty &&
      cartBasedRecommendations.isEmpty;
}

/// 推荐状态管理器
class RecommendationNotifier extends StateNotifier<RecommendationState> {
  final RecommendationUseCases _useCases;

  RecommendationNotifier(this._useCases) : super(const RecommendationState());

  /// 加载个性化推荐
  Future<void> loadPersonalizedRecommendations({
    String? userId,
    String? scene,
    int page = 1,
    int size = 20,
  }) async {
    if (page == 1) {
      state = state.copyWith(isLoading: true, error: null);
    } else {
      state = state.copyWith(isLoadingMore: true);
    }

    try {
      final recommendations = await _useCases.getPersonalizedRecommendations(
        userId: userId,
        scene: scene,
        page: page,
        size: size,
      );

      if (page == 1) {
        state = state.copyWith(
          isLoading: false,
          personalizedRecommendations: recommendations,
          hasMore: recommendations.length >= size,
        );
      } else {
        state = state.copyWith(
          isLoadingMore: false,
          personalizedRecommendations: [...state.personalizedRecommendations, ...recommendations],
          hasMore: recommendations.length >= size,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  /// 加载更多个性化推荐
  Future<void> loadMorePersonalizedRecommendations() async {
    if (!state.hasMore || state.isLoadingMore) return;
    await loadPersonalizedRecommendations(page: state.currentPage + 1);
  }

  /// 加载热门商品推荐
  Future<void> loadHotRecommendations({
    String? categoryId,
    String? timeRange,
    int page = 1,
    int size = 20,
  }) async {
    if (page == 1) {
      state = state.copyWith(isLoading: true, error: null);
    } else {
      state = state.copyWith(isLoadingMore: true);
    }

    try {
      final recommendations = await _useCases.getHotRecommendations(
        categoryId: categoryId,
        timeRange: timeRange,
        page: page,
        size: size,
      );

      if (page == 1) {
        state = state.copyWith(
          isLoading: false,
          hotRecommendations: recommendations,
          hasMore: recommendations.length >= size,
        );
      } else {
        state = state.copyWith(
          isLoadingMore: false,
          hotRecommendations: [...state.hotRecommendations, ...recommendations],
          hasMore: recommendations.length >= size,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  /// 加载更多热门推荐
  Future<void> loadMoreHotRecommendations() async {
    if (!state.hasMore || state.isLoadingMore) return;
    await loadHotRecommendations(page: state.currentPage + 1);
  }

  /// 加载新品推荐
  Future<void> loadNewArrivals({
    String? categoryId,
    int page = 1,
    int size = 20,
  }) async {
    if (page == 1) {
      state = state.copyWith(isLoading: true, error: null);
    } else {
      state = state.copyWith(isLoadingMore: true);
    }

    try {
      final recommendations = await _useCases.getNewArrivals(
        categoryId: categoryId,
        page: page,
        size: size,
      );

      if (page == 1) {
        state = state.copyWith(
          isLoading: false,
          newArrivals: recommendations,
          hasMore: recommendations.length >= size,
        );
      } else {
        state = state.copyWith(
          isLoadingMore: false,
          newArrivals: [...state.newArrivals, ...recommendations],
          hasMore: recommendations.length >= size,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  /// 加载更多新品推荐
  Future<void> loadMoreNewArrivals() async {
    if (!state.hasMore || state.isLoadingMore) return;
    await loadNewArrivals(page: state.currentPage + 1);
  }

  /// 加载猜你喜欢推荐
  Future<void> loadGuessYouLike({
    String? userId,
    int limit = 20,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final recommendations = await _useCases.getGuessYouLike(
        userId: userId,
        limit: limit,
      );

      state = state.copyWith(
        isLoading: false,
        guessYouLike: recommendations,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载更多猜你喜欢推荐
  Future<void> loadMoreGuessYouLike() async {
    if (state.isLoadingMore) return;
    
    state = state.copyWith(isLoadingMore: true);

    try {
      final recommendations = await _useCases.getGuessYouLike(
        limit: 20,
      );

      state = state.copyWith(
        isLoadingMore: false,
        guessYouLike: [...state.guessYouLike, ...recommendations],
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  /// 加载相似商品推荐
  Future<void> loadSimilarRecommendations(
    String productId, {
    String? algorithm,
    int limit = 20,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final recommendations = await _useCases.getSimilarRecommendations(
        productId: productId,
        algorithm: algorithm,
        limit: limit,
      );

      state = state.copyWith(
        isLoading: false,
        similarRecommendations: recommendations,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载更多相似商品推荐
  Future<void> loadMoreSimilarRecommendations(String productId) async {
    if (state.isLoadingMore) return;
    
    state = state.copyWith(isLoadingMore: true);

    try {
      final recommendations = await _useCases.getSimilarRecommendations(
        productId: productId,
        limit: 20,
      );

      state = state.copyWith(
        isLoadingMore: false,
        similarRecommendations: [...state.similarRecommendations, ...recommendations],
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  /// 加载基于历史的推荐
  Future<void> loadHistoryBasedRecommendations({
    String? userId,
    int historyDays = 7,
    int limit = 20,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final recommendations = await _useCases.getHistoryBasedRecommendations(
        userId: userId,
        historyDays: historyDays,
        limit: limit,
      );

      state = state.copyWith(
        isLoading: false,
        historyBasedRecommendations: recommendations,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载更多基于历史的推荐
  Future<void> loadMoreHistoryBasedRecommendations() async {
    if (state.isLoadingMore) return;
    
    state = state.copyWith(isLoadingMore: true);

    try {
      final recommendations = await _useCases.getHistoryBasedRecommendations(
        historyDays: 7,
        limit: 20,
      );

      state = state.copyWith(
        isLoadingMore: false,
        historyBasedRecommendations: [...state.historyBasedRecommendations, ...recommendations],
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  /// 加载购物车推荐
  Future<void> loadCartBasedRecommendations({
    String? userId,
    int limit = 10,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final recommendations = await _useCases.getCartBasedRecommendations(
        userId: userId,
        limit: limit,
      );

      state = state.copyWith(
        isLoading: false,
        cartBasedRecommendations: recommendations,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 记录用户行为
  Future<void> recordUserBehavior({
    required String productId,
    required String behaviorType,
    String? categoryId,
    String? brandId,
    String? sessionId,
    String? pageSource,
    int? duration,
    Map<String, Object>? properties,
  }) async {
    try {
      await _useCases.recordUserBehavior(
        productId: productId,
        behaviorType: behaviorType,
        categoryId: categoryId,
        brandId: brandId,
        sessionId: sessionId,
        pageSource: pageSource,
        duration: duration,
        properties: properties,
      );
    } catch (e) {
      // 静默处理行为记录失败
      print('记录用户行为失败: $e');
    }
  }

  /// 获取推荐统计信息
  Future<void> loadRecommendationStats({
    String? userId,
    int days = 7,
  }) async {
    try {
      final stats = await _useCases.getRecommendationStats(
        userId: userId,
        days: days,
      );

      state = state.copyWith(stats: stats);
    } catch (e) {
      print('获取推荐统计失败: $e');
    }
  }

  /// 刷新所有推荐
  Future<void> refreshAllRecommendations() async {
    await Future.wait([
      loadPersonalizedRecommendations(),
      loadHotRecommendations(),
      loadNewArrivals(),
      loadGuessYouLike(),
    ]);
  }

  /// 清除错误状态
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider定义
final recommendationRemoteDataSourceProvider = Provider<RecommendationRemoteDataSource>((ref) {
  final dioClient = ref.watch(dioClientProvider);
  return RecommendationRemoteDataSource(dioClient);
});

final recommendationRepositoryProvider = Provider<RecommendationRepositoryImpl>((ref) {
  final remoteDataSource = ref.watch(recommendationRemoteDataSourceProvider);
  return RecommendationRepositoryImpl(remoteDataSource);
});

final recommendationUseCasesProvider = Provider<RecommendationUseCases>((ref) {
  final repository = ref.watch(recommendationRepositoryProvider);
  return RecommendationUseCases(repository);
});

final recommendationProvider = StateNotifierProvider<RecommendationNotifier, RecommendationState>((ref) {
  final useCases = ref.watch(recommendationUseCasesProvider);
  return RecommendationNotifier(useCases);
});
