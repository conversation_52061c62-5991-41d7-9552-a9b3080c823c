import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/profile_models.dart';

part 'profile_api_service.g.dart';

/// 个人资料API服务
@RestApi()
abstract class ProfileApiService {
  factory ProfileApiService(Dio dio, {String baseUrl}) = _ProfileApiService;

  /// 获取用户资料
  @GET('/api/v1/users/{userId}/profile')
  Future<UserProfileResponse> getUserProfile(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 更新用户资料
  @PUT('/api/v1/users/{userId}/profile')
  Future<UserProfileResponse> updateUserProfile(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() UpdateUserProfileRequest request,
  );

  /// 上传头像
  @POST('/api/v1/users/{userId}/avatar')
  @MultiPart()
  Future<AvatarUploadResponse> uploadAvatar(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Part() MultipartFile avatar,
  );

  /// 获取用户设置
  @GET('/api/v1/users/{userId}/settings')
  Future<UserSettingsResponse> getUserSettings(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 更新用户设置
  @PUT('/api/v1/users/{userId}/settings')
  Future<UserSettingsResponse> updateUserSettings(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() UpdateUserSettingsRequest request,
  );

  /// 获取隐私设置
  @GET('/api/v1/users/{userId}/privacy')
  Future<PrivacySettingsResponse> getPrivacySettings(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 更新隐私设置
  @PUT('/api/v1/users/{userId}/privacy')
  Future<PrivacySettingsResponse> updatePrivacySettings(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() UpdatePrivacySettingsRequest request,
  );

  /// 获取通知设置
  @GET('/api/v1/users/{userId}/notifications')
  Future<NotificationSettingsResponse> getNotificationSettings(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 更新通知设置
  @PUT('/api/v1/users/{userId}/notifications')
  Future<NotificationSettingsResponse> updateNotificationSettings(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() UpdateNotificationSettingsRequest request,
  );

  /// 获取安全设置
  @GET('/api/v1/users/{userId}/security')
  Future<SecuritySettingsResponse> getSecuritySettings(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 更新安全设置
  @PUT('/api/v1/users/{userId}/security')
  Future<SecuritySettingsResponse> updateSecuritySettings(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() UpdateSecuritySettingsRequest request,
  );

  /// 修改密码
  @PUT('/api/v1/users/{userId}/password')
  Future<void> changePassword(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() ChangePasswordRequest request,
  );

  /// 绑定手机号
  @POST('/api/v1/users/{userId}/bind-phone')
  Future<void> bindPhone(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() BindPhoneRequest request,
  );

  /// 绑定邮箱
  @POST('/api/v1/users/{userId}/bind-email')
  Future<void> bindEmail(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() BindEmailRequest request,
  );

  /// 实名认证
  @POST('/api/v1/users/{userId}/kyc')
  Future<KycResponse> submitKyc(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() KycRequest request,
  );

  /// 获取实名认证状态
  @GET('/api/v1/users/{userId}/kyc')
  Future<KycStatusResponse> getKycStatus(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 获取用户统计信息
  @GET('/api/v1/users/{userId}/stats')
  Future<UserStatsResponse> getUserStats(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 获取关注列表
  @GET('/api/v1/users/{userId}/following')
  Future<PageResponse<UserFollowResponse>> getFollowing(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Query('page') int page,
    @Query('size') int size,
  );

  /// 获取粉丝列表
  @GET('/api/v1/users/{userId}/followers')
  Future<PageResponse<UserFollowResponse>> getFollowers(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Query('page') int page,
    @Query('size') int size,
  );

  /// 关注用户
  @POST('/api/v1/users/{userId}/follow')
  Future<void> followUser(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() FollowUserRequest request,
  );

  /// 取消关注
  @DELETE('/api/v1/users/{userId}/follow/{targetUserId}')
  Future<void> unfollowUser(
    @Path('userId') int userId,
    @Path('targetUserId') int targetUserId,
    @Header('Authorization') String accessToken,
  );

  /// 检查是否关注
  @GET('/api/v1/users/{userId}/follow/{targetUserId}/check')
  Future<bool> isFollowing(
    @Path('userId') int userId,
    @Path('targetUserId') int targetUserId,
    @Header('Authorization') String accessToken,
  );

  /// 获取用户动态
  @GET('/api/v1/users/{userId}/moments')
  Future<PageResponse<UserMomentResponse>> getUserMoments(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Query('page') int page,
    @Query('size') int size,
  );

  /// 发布动态
  @POST('/api/v1/users/{userId}/moments')
  Future<UserMomentResponse> publishMoment(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() PublishMomentRequest request,
  );

  /// 删除动态
  @DELETE('/api/v1/users/{userId}/moments/{momentId}')
  Future<void> deleteMoment(
    @Path('userId') int userId,
    @Path('momentId') int momentId,
    @Header('Authorization') String accessToken,
  );

  /// 获取系统消息
  @GET('/api/v1/users/{userId}/system-messages')
  Future<PageResponse<SystemMessageResponse>> getSystemMessages(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Query('page') int page,
    @Query('size') int size,
    @Query('type') int? type,
    @Query('read') bool? read,
  );

  /// 标记消息已读
  @PUT('/api/v1/users/{userId}/system-messages/{messageId}/read')
  Future<void> markMessageRead(
    @Path('userId') int userId,
    @Path('messageId') int messageId,
    @Header('Authorization') String accessToken,
  );

  /// 删除系统消息
  @DELETE('/api/v1/users/{userId}/system-messages/{messageId}')
  Future<void> deleteSystemMessage(
    @Path('userId') int userId,
    @Path('messageId') int messageId,
    @Header('Authorization') String accessToken,
  );

  /// 获取应用设置
  @GET('/api/v1/app/settings')
  Future<AppSettingsResponse> getAppSettings();

  /// 获取帮助文档
  @GET('/api/v1/app/help')
  Future<List<HelpDocResponse>> getHelpDocs();

  /// 获取关于我们
  @GET('/api/v1/app/about')
  Future<AboutResponse> getAbout();

  /// 意见反馈
  @POST('/api/v1/users/{userId}/feedback')
  Future<void> submitFeedback(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() FeedbackRequest request,
  );

  /// 注销账户
  @DELETE('/api/v1/users/{userId}/account')
  Future<void> deleteAccount(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
    @Body() DeleteAccountRequest request,
  );
}
