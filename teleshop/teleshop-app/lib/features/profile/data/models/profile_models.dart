import 'package:json_annotation/json_annotation.dart';

part 'profile_models.g.dart';

/// 用户资料响应模型
@JsonSerializable()
class UserProfileResponse {
  final int userId;
  final String username;
  final String? nickname;
  final String? realName;
  final String? avatar;
  final int? gender;
  final String? birthday;
  final String? bio;
  final String? location;
  final String? phone;
  final String? email;
  final bool phoneVerified;
  final bool emailVerified;
  final int level;
  final int experience;
  final int points;
  final int followingCount;
  final int followersCount;
  final int kycStatus;
  final String? kycTime;
  final String createTime;
  final String updateTime;

  UserProfileResponse({
    required this.userId,
    required this.username,
    this.nickname,
    this.realName,
    this.avatar,
    this.gender,
    this.birthday,
    this.bio,
    this.location,
    this.phone,
    this.email,
    required this.phoneVerified,
    required this.emailVerified,
    required this.level,
    required this.experience,
    required this.points,
    required this.followingCount,
    required this.followersCount,
    required this.kycStatus,
    this.kycTime,
    required this.createTime,
    required this.updateTime,
  });

  factory UserProfileResponse.fromJson(Map<String, dynamic> json) =>
      _$UserProfileResponseFromJson(json);

  Map<String, dynamic> toJson() => _$UserProfileResponseToJson(this);
}

/// 更新用户资料请求模型
@JsonSerializable()
class UpdateUserProfileRequest {
  final String? nickname;
  final String? realName;
  final int? gender;
  final String? birthday;
  final String? bio;
  final String? location;

  UpdateUserProfileRequest({
    this.nickname,
    this.realName,
    this.gender,
    this.birthday,
    this.bio,
    this.location,
  });

  factory UpdateUserProfileRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateUserProfileRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateUserProfileRequestToJson(this);
}

/// 头像上传响应模型
@JsonSerializable()
class AvatarUploadResponse {
  final String avatarUrl;
  final String uploadTime;

  AvatarUploadResponse({
    required this.avatarUrl,
    required this.uploadTime,
  });

  factory AvatarUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$AvatarUploadResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AvatarUploadResponseToJson(this);
}

/// 用户设置响应模型
@JsonSerializable()
class UserSettingsResponse {
  final String language;
  final String theme;
  final String currency;
  final String timezone;
  final bool autoLogin;
  final bool biometricLogin;
  final bool soundEnabled;
  final bool vibrationEnabled;

  UserSettingsResponse({
    required this.language,
    required this.theme,
    required this.currency,
    required this.timezone,
    required this.autoLogin,
    required this.biometricLogin,
    required this.soundEnabled,
    required this.vibrationEnabled,
  });

  factory UserSettingsResponse.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$UserSettingsResponseToJson(this);
}

/// 更新用户设置请求模型
@JsonSerializable()
class UpdateUserSettingsRequest {
  final String? language;
  final String? theme;
  final String? currency;
  final String? timezone;
  final bool? autoLogin;
  final bool? biometricLogin;
  final bool? soundEnabled;
  final bool? vibrationEnabled;

  UpdateUserSettingsRequest({
    this.language,
    this.theme,
    this.currency,
    this.timezone,
    this.autoLogin,
    this.biometricLogin,
    this.soundEnabled,
    this.vibrationEnabled,
  });

  factory UpdateUserSettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateUserSettingsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateUserSettingsRequestToJson(this);
}

/// 隐私设置响应模型
@JsonSerializable()
class PrivacySettingsResponse {
  final bool profileVisible;
  final bool phoneVisible;
  final bool emailVisible;
  final bool locationVisible;
  final bool onlineStatusVisible;
  final bool allowFriendRequests;
  final bool allowGroupInvites;
  final bool allowStrangerMessages;

  PrivacySettingsResponse({
    required this.profileVisible,
    required this.phoneVisible,
    required this.emailVisible,
    required this.locationVisible,
    required this.onlineStatusVisible,
    required this.allowFriendRequests,
    required this.allowGroupInvites,
    required this.allowStrangerMessages,
  });

  factory PrivacySettingsResponse.fromJson(Map<String, dynamic> json) =>
      _$PrivacySettingsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PrivacySettingsResponseToJson(this);
}

/// 更新隐私设置请求模型
@JsonSerializable()
class UpdatePrivacySettingsRequest {
  final bool? profileVisible;
  final bool? phoneVisible;
  final bool? emailVisible;
  final bool? locationVisible;
  final bool? onlineStatusVisible;
  final bool? allowFriendRequests;
  final bool? allowGroupInvites;
  final bool? allowStrangerMessages;

  UpdatePrivacySettingsRequest({
    this.profileVisible,
    this.phoneVisible,
    this.emailVisible,
    this.locationVisible,
    this.onlineStatusVisible,
    this.allowFriendRequests,
    this.allowGroupInvites,
    this.allowStrangerMessages,
  });

  factory UpdatePrivacySettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePrivacySettingsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdatePrivacySettingsRequestToJson(this);
}

/// 通知设置响应模型
@JsonSerializable()
class NotificationSettingsResponse {
  final bool pushEnabled;
  final bool messageNotification;
  final bool orderNotification;
  final bool promotionNotification;
  final bool systemNotification;
  final String quietStartTime;
  final String quietEndTime;

  NotificationSettingsResponse({
    required this.pushEnabled,
    required this.messageNotification,
    required this.orderNotification,
    required this.promotionNotification,
    required this.systemNotification,
    required this.quietStartTime,
    required this.quietEndTime,
  });

  factory NotificationSettingsResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationSettingsResponseToJson(this);
}

/// 更新通知设置请求模型
@JsonSerializable()
class UpdateNotificationSettingsRequest {
  final bool? pushEnabled;
  final bool? messageNotification;
  final bool? orderNotification;
  final bool? promotionNotification;
  final bool? systemNotification;
  final String? quietStartTime;
  final String? quietEndTime;

  UpdateNotificationSettingsRequest({
    this.pushEnabled,
    this.messageNotification,
    this.orderNotification,
    this.promotionNotification,
    this.systemNotification,
    this.quietStartTime,
    this.quietEndTime,
  });

  factory UpdateNotificationSettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateNotificationSettingsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateNotificationSettingsRequestToJson(this);
}

/// 安全设置响应模型
@JsonSerializable()
class SecuritySettingsResponse {
  final bool twoFactorEnabled;
  final bool loginNotification;
  final bool deviceManagement;
  final int sessionTimeout;
  final List<String> trustedDevices;

  SecuritySettingsResponse({
    required this.twoFactorEnabled,
    required this.loginNotification,
    required this.deviceManagement,
    required this.sessionTimeout,
    required this.trustedDevices,
  });

  factory SecuritySettingsResponse.fromJson(Map<String, dynamic> json) =>
      _$SecuritySettingsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SecuritySettingsResponseToJson(this);
}

/// 更新安全设置请求模型
@JsonSerializable()
class UpdateSecuritySettingsRequest {
  final bool? twoFactorEnabled;
  final bool? loginNotification;
  final bool? deviceManagement;
  final int? sessionTimeout;

  UpdateSecuritySettingsRequest({
    this.twoFactorEnabled,
    this.loginNotification,
    this.deviceManagement,
    this.sessionTimeout,
  });

  factory UpdateSecuritySettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateSecuritySettingsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateSecuritySettingsRequestToJson(this);
}

/// 修改密码请求模型
@JsonSerializable()
class ChangePasswordRequest {
  final String oldPassword;
  final String newPassword;
  final String confirmPassword;

  ChangePasswordRequest({
    required this.oldPassword,
    required this.newPassword,
    required this.confirmPassword,
  });

  factory ChangePasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$ChangePasswordRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ChangePasswordRequestToJson(this);
}

/// 绑定手机号请求模型
@JsonSerializable()
class BindPhoneRequest {
  final String phone;
  final String verifyCode;

  BindPhoneRequest({
    required this.phone,
    required this.verifyCode,
  });

  factory BindPhoneRequest.fromJson(Map<String, dynamic> json) =>
      _$BindPhoneRequestFromJson(json);

  Map<String, dynamic> toJson() => _$BindPhoneRequestToJson(this);
}

/// 绑定邮箱请求模型
@JsonSerializable()
class BindEmailRequest {
  final String email;
  final String verifyCode;

  BindEmailRequest({
    required this.email,
    required this.verifyCode,
  });

  factory BindEmailRequest.fromJson(Map<String, dynamic> json) =>
      _$BindEmailRequestFromJson(json);

  Map<String, dynamic> toJson() => _$BindEmailRequestToJson(this);
}

/// 实名认证请求模型
@JsonSerializable()
class KycRequest {
  final String realName;
  final String idCard;
  final String idCardFront;
  final String idCardBack;

  KycRequest({
    required this.realName,
    required this.idCard,
    required this.idCardFront,
    required this.idCardBack,
  });

  factory KycRequest.fromJson(Map<String, dynamic> json) =>
      _$KycRequestFromJson(json);

  Map<String, dynamic> toJson() => _$KycRequestToJson(this);
}

/// 实名认证响应模型
@JsonSerializable()
class KycResponse {
  final int kycId;
  final int status;
  final String submitTime;
  final String? reviewTime;
  final String? failReason;

  KycResponse({
    required this.kycId,
    required this.status,
    required this.submitTime,
    this.reviewTime,
    this.failReason,
  });

  factory KycResponse.fromJson(Map<String, dynamic> json) =>
      _$KycResponseFromJson(json);

  Map<String, dynamic> toJson() => _$KycResponseToJson(this);
}

/// 实名认证状态响应模型
@JsonSerializable()
class KycStatusResponse {
  final int status;
  final String? realName;
  final String? idCard;
  final String? submitTime;
  final String? reviewTime;
  final String? failReason;

  KycStatusResponse({
    required this.status,
    this.realName,
    this.idCard,
    this.submitTime,
    this.reviewTime,
    this.failReason,
  });

  factory KycStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$KycStatusResponseFromJson(json);

  Map<String, dynamic> toJson() => _$KycStatusResponseToJson(this);
}

/// 用户统计响应模型
@JsonSerializable()
class UserStatsResponse {
  final int totalOrders;
  final double totalSpent;
  final int totalPoints;
  final int totalCoupons;
  final int totalFavorites;
  final int totalReviews;
  final int loginDays;
  final String lastLoginTime;

  UserStatsResponse({
    required this.totalOrders,
    required this.totalSpent,
    required this.totalPoints,
    required this.totalCoupons,
    required this.totalFavorites,
    required this.totalReviews,
    required this.loginDays,
    required this.lastLoginTime,
  });

  factory UserStatsResponse.fromJson(Map<String, dynamic> json) =>
      _$UserStatsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$UserStatsResponseToJson(this);
}

/// 用户关注响应模型
@JsonSerializable()
class UserFollowResponse {
  final int userId;
  final String username;
  final String? nickname;
  final String? avatar;
  final int level;
  final bool isFollowing;
  final String followTime;

  UserFollowResponse({
    required this.userId,
    required this.username,
    this.nickname,
    this.avatar,
    required this.level,
    required this.isFollowing,
    required this.followTime,
  });

  factory UserFollowResponse.fromJson(Map<String, dynamic> json) =>
      _$UserFollowResponseFromJson(json);

  Map<String, dynamic> toJson() => _$UserFollowResponseToJson(this);
}

/// 关注用户请求模型
@JsonSerializable()
class FollowUserRequest {
  final int targetUserId;

  FollowUserRequest({
    required this.targetUserId,
  });

  factory FollowUserRequest.fromJson(Map<String, dynamic> json) =>
      _$FollowUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$FollowUserRequestToJson(this);
}

/// 用户动态响应模型
@JsonSerializable()
class UserMomentResponse {
  final int momentId;
  final int userId;
  final String content;
  final List<String> images;
  final int likeCount;
  final int commentCount;
  final bool isLiked;
  final String createTime;

  UserMomentResponse({
    required this.momentId,
    required this.userId,
    required this.content,
    required this.images,
    required this.likeCount,
    required this.commentCount,
    required this.isLiked,
    required this.createTime,
  });

  factory UserMomentResponse.fromJson(Map<String, dynamic> json) =>
      _$UserMomentResponseFromJson(json);

  Map<String, dynamic> toJson() => _$UserMomentResponseToJson(this);
}

/// 发布动态请求模型
@JsonSerializable()
class PublishMomentRequest {
  final String content;
  final List<String>? images;

  PublishMomentRequest({
    required this.content,
    this.images,
  });

  factory PublishMomentRequest.fromJson(Map<String, dynamic> json) =>
      _$PublishMomentRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PublishMomentRequestToJson(this);
}

/// 系统消息响应模型
@JsonSerializable()
class SystemMessageResponse {
  final int messageId;
  final int type;
  final String title;
  final String content;
  final bool isRead;
  final String createTime;

  SystemMessageResponse({
    required this.messageId,
    required this.type,
    required this.title,
    required this.content,
    required this.isRead,
    required this.createTime,
  });

  factory SystemMessageResponse.fromJson(Map<String, dynamic> json) =>
      _$SystemMessageResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SystemMessageResponseToJson(this);
}

/// 应用设置响应模型
@JsonSerializable()
class AppSettingsResponse {
  final String appVersion;
  final String apiVersion;
  final bool maintenanceMode;
  final String? maintenanceMessage;
  final List<String> supportedLanguages;
  final List<String> supportedCurrencies;

  AppSettingsResponse({
    required this.appVersion,
    required this.apiVersion,
    required this.maintenanceMode,
    this.maintenanceMessage,
    required this.supportedLanguages,
    required this.supportedCurrencies,
  });

  factory AppSettingsResponse.fromJson(Map<String, dynamic> json) =>
      _$AppSettingsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AppSettingsResponseToJson(this);
}

/// 帮助文档响应模型
@JsonSerializable()
class HelpDocResponse {
  final int docId;
  final String title;
  final String content;
  final int category;
  final int sortOrder;

  HelpDocResponse({
    required this.docId,
    required this.title,
    required this.content,
    required this.category,
    required this.sortOrder,
  });

  factory HelpDocResponse.fromJson(Map<String, dynamic> json) =>
      _$HelpDocResponseFromJson(json);

  Map<String, dynamic> toJson() => _$HelpDocResponseToJson(this);
}

/// 关于我们响应模型
@JsonSerializable()
class AboutResponse {
  final String appName;
  final String version;
  final String company;
  final String description;
  final String website;
  final String email;
  final String phone;
  final String address;

  AboutResponse({
    required this.appName,
    required this.version,
    required this.company,
    required this.description,
    required this.website,
    required this.email,
    required this.phone,
    required this.address,
  });

  factory AboutResponse.fromJson(Map<String, dynamic> json) =>
      _$AboutResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AboutResponseToJson(this);
}

/// 意见反馈请求模型
@JsonSerializable()
class FeedbackRequest {
  final String type;
  final String content;
  final String? contact;
  final List<String>? images;

  FeedbackRequest({
    required this.type,
    required this.content,
    this.contact,
    this.images,
  });

  factory FeedbackRequest.fromJson(Map<String, dynamic> json) =>
      _$FeedbackRequestFromJson(json);

  Map<String, dynamic> toJson() => _$FeedbackRequestToJson(this);
}

/// 注销账户请求模型
@JsonSerializable()
class DeleteAccountRequest {
  final String reason;
  final String password;

  DeleteAccountRequest({
    required this.reason,
    required this.password,
  });

  factory DeleteAccountRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteAccountRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteAccountRequestToJson(this);
}

/// 分页响应模型
@JsonSerializable()
class PageResponse<T> {
  final List<T> content;
  final int totalElements;
  final int totalPages;
  final int number;
  final int size;
  final bool first;
  final bool last;

  PageResponse({
    required this.content,
    required this.totalElements,
    required this.totalPages,
    required this.number,
    required this.size,
    required this.first,
    required this.last,
  });

  factory PageResponse.fromJson(Map<String, dynamic> json) =>
      _$PageResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PageResponseToJson(this);
}
