import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/datasources/profile_api_service.dart';
import '../../data/models/profile_models.dart';
import '../../../auth/presentation/providers/enhanced_auth_provider.dart';
import '../../../../core/network/dio_client.dart';

/// 个人资料API服务提供者
final profileApiServiceProvider = Provider<ProfileApiService>((ref) {
  final dio = ref.watch(dioProvider);
  return ProfileApiService(dio);
});

/// 个人资料状态
class ProfileState {
  final bool isLoading;
  final String? error;
  final UserProfileResponse? userProfile;
  final UserSettingsResponse? userSettings;
  final PrivacySettingsResponse? privacySettings;
  final NotificationSettingsResponse? notificationSettings;
  final SecuritySettingsResponse? securitySettings;
  final KycStatusResponse? kycStatus;
  final UserStatsResponse? userStats;
  final List<UserFollowResponse> following;
  final List<UserFollowResponse> followers;
  final List<UserMomentResponse> moments;
  final List<SystemMessageResponse> systemMessages;
  final AppSettingsResponse? appSettings;
  final List<HelpDocResponse> helpDocs;
  final AboutResponse? about;

  const ProfileState({
    this.isLoading = false,
    this.error,
    this.userProfile,
    this.userSettings,
    this.privacySettings,
    this.notificationSettings,
    this.securitySettings,
    this.kycStatus,
    this.userStats,
    this.following = const [],
    this.followers = const [],
    this.moments = const [],
    this.systemMessages = const [],
    this.appSettings,
    this.helpDocs = const [],
    this.about,
  });

  ProfileState copyWith({
    bool? isLoading,
    String? error,
    UserProfileResponse? userProfile,
    UserSettingsResponse? userSettings,
    PrivacySettingsResponse? privacySettings,
    NotificationSettingsResponse? notificationSettings,
    SecuritySettingsResponse? securitySettings,
    KycStatusResponse? kycStatus,
    UserStatsResponse? userStats,
    List<UserFollowResponse>? following,
    List<UserFollowResponse>? followers,
    List<UserMomentResponse>? moments,
    List<SystemMessageResponse>? systemMessages,
    AppSettingsResponse? appSettings,
    List<HelpDocResponse>? helpDocs,
    AboutResponse? about,
  }) {
    return ProfileState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      userProfile: userProfile ?? this.userProfile,
      userSettings: userSettings ?? this.userSettings,
      privacySettings: privacySettings ?? this.privacySettings,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      securitySettings: securitySettings ?? this.securitySettings,
      kycStatus: kycStatus ?? this.kycStatus,
      userStats: userStats ?? this.userStats,
      following: following ?? this.following,
      followers: followers ?? this.followers,
      moments: moments ?? this.moments,
      systemMessages: systemMessages ?? this.systemMessages,
      appSettings: appSettings ?? this.appSettings,
      helpDocs: helpDocs ?? this.helpDocs,
      about: about ?? this.about,
    );
  }
}

/// 个人资料状态管理
class ProfileNotifier extends StateNotifier<ProfileState> {
  ProfileNotifier(this._ref) : super(const ProfileState()) {
    _initialize();
  }

  final Ref _ref;
  Timer? _refreshTimer;

  ProfileApiService get _profileApi => _ref.read(profileApiServiceProvider);

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  /// 初始化
  Future<void> _initialize() async {
    await Future.wait([
      loadUserProfile(),
      loadUserSettings(),
      loadAppSettings(),
    ]);
  }

  /// 加载用户资料
  Future<void> loadUserProfile() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final user = await _getCurrentUser();
      if (user == null) {
        state = state.copyWith(
          isLoading: false,
          error: '用户未登录',
        );
        return;
      }

      final accessToken = await _getAccessToken();
      if (accessToken == null) {
        state = state.copyWith(
          isLoading: false,
          error: '访问令牌不存在',
        );
        return;
      }

      final profile = await _profileApi.getUserProfile(user.id, 'Bearer $accessToken');

      state = state.copyWith(
        isLoading: false,
        userProfile: profile,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '加载用户资料失败: $e',
      );
    }
  }

  /// 更新用户资料
  Future<bool> updateUserProfile(UpdateUserProfileRequest request) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final user = await _getCurrentUser();
      if (user == null) {
        state = state.copyWith(
          isLoading: false,
          error: '用户未登录',
        );
        return false;
      }

      final accessToken = await _getAccessToken();
      if (accessToken == null) {
        state = state.copyWith(
          isLoading: false,
          error: '访问令牌不存在',
        );
        return false;
      }

      final updatedProfile = await _profileApi.updateUserProfile(
        user.id,
        'Bearer $accessToken',
        request,
      );

      state = state.copyWith(
        isLoading: false,
        userProfile: updatedProfile,
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '更新用户资料失败: $e',
      );
      return false;
    }
  }

  /// 上传头像
  Future<bool> uploadAvatar(File avatarFile) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final user = await _getCurrentUser();
      if (user == null) {
        state = state.copyWith(
          isLoading: false,
          error: '用户未登录',
        );
        return false;
      }

      final accessToken = await _getAccessToken();
      if (accessToken == null) {
        state = state.copyWith(
          isLoading: false,
          error: '访问令牌不存在',
        );
        return false;
      }

      final multipartFile = await MultipartFile.fromFile(
        avatarFile.path,
        filename: 'avatar.jpg',
      );

      final response = await _profileApi.uploadAvatar(
        user.id,
        'Bearer $accessToken',
        multipartFile,
      );

      // 更新用户资料中的头像URL
      if (state.userProfile != null) {
        final updatedProfile = UserProfileResponse(
          userId: state.userProfile!.userId,
          username: state.userProfile!.username,
          nickname: state.userProfile!.nickname,
          realName: state.userProfile!.realName,
          avatar: response.avatarUrl,
          gender: state.userProfile!.gender,
          birthday: state.userProfile!.birthday,
          bio: state.userProfile!.bio,
          location: state.userProfile!.location,
          phone: state.userProfile!.phone,
          email: state.userProfile!.email,
          phoneVerified: state.userProfile!.phoneVerified,
          emailVerified: state.userProfile!.emailVerified,
          level: state.userProfile!.level,
          experience: state.userProfile!.experience,
          points: state.userProfile!.points,
          followingCount: state.userProfile!.followingCount,
          followersCount: state.userProfile!.followersCount,
          kycStatus: state.userProfile!.kycStatus,
          kycTime: state.userProfile!.kycTime,
          createTime: state.userProfile!.createTime,
          updateTime: response.uploadTime,
        );

        state = state.copyWith(
          isLoading: false,
          userProfile: updatedProfile,
        );
      } else {
        state = state.copyWith(isLoading: false);
      }

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '上传头像失败: $e',
      );
      return false;
    }
  }

  /// 加载用户设置
  Future<void> loadUserSettings() async {
    try {
      final user = await _getCurrentUser();
      if (user == null) return;

      final accessToken = await _getAccessToken();
      if (accessToken == null) return;

      final settings = await _profileApi.getUserSettings(user.id, 'Bearer $accessToken');
      state = state.copyWith(userSettings: settings);
    } catch (e) {
      // 静默处理设置加载失败
    }
  }

  /// 更新用户设置
  Future<bool> updateUserSettings(UpdateUserSettingsRequest request) async {
    try {
      final user = await _getCurrentUser();
      if (user == null) return false;

      final accessToken = await _getAccessToken();
      if (accessToken == null) return false;

      final updatedSettings = await _profileApi.updateUserSettings(
        user.id,
        'Bearer $accessToken',
        request,
      );

      state = state.copyWith(userSettings: updatedSettings);
      return true;
    } catch (e) {
      state = state.copyWith(error: '更新用户设置失败: $e');
      return false;
    }
  }

  /// 提交实名认证
  Future<bool> submitKyc(KycRequest request) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final user = await _getCurrentUser();
      if (user == null) {
        state = state.copyWith(
          isLoading: false,
          error: '用户未登录',
        );
        return false;
      }

      final accessToken = await _getAccessToken();
      if (accessToken == null) {
        state = state.copyWith(
          isLoading: false,
          error: '访问令牌不存在',
        );
        return false;
      }

      await _profileApi.submitKyc(user.id, 'Bearer $accessToken', request);

      // 重新加载KYC状态
      await loadKycStatus();

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '提交实名认证失败: $e',
      );
      return false;
    }
  }

  /// 加载KYC状态
  Future<void> loadKycStatus() async {
    try {
      final user = await _getCurrentUser();
      if (user == null) return;

      final accessToken = await _getAccessToken();
      if (accessToken == null) return;

      final kycStatus = await _profileApi.getKycStatus(user.id, 'Bearer $accessToken');
      state = state.copyWith(kycStatus: kycStatus);
    } catch (e) {
      // 静默处理KYC状态加载失败
    }
  }

  /// 加载应用设置
  Future<void> loadAppSettings() async {
    try {
      final appSettings = await _profileApi.getAppSettings();
      state = state.copyWith(appSettings: appSettings);
    } catch (e) {
      // 静默处理应用设置加载失败
    }
  }

  /// 提交意见反馈
  Future<bool> submitFeedback(FeedbackRequest request) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final user = await _getCurrentUser();
      if (user == null) {
        state = state.copyWith(
          isLoading: false,
          error: '用户未登录',
        );
        return false;
      }

      final accessToken = await _getAccessToken();
      if (accessToken == null) {
        state = state.copyWith(
          isLoading: false,
          error: '访问令牌不存在',
        );
        return false;
      }

      await _profileApi.submitFeedback(user.id, 'Bearer $accessToken', request);

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '提交意见反馈失败: $e',
      );
      return false;
    }
  }

  /// 获取当前用户
  Future<dynamic> _getCurrentUser() async {
    final authState = _ref.read(enhancedAuthProvider);
    return authState.maybeWhen(
      authenticated: (user) => user,
      orElse: () => null,
    );
  }

  /// 获取访问令牌
  Future<String?> _getAccessToken() async {
    return await _ref.read(enhancedAuthProvider.notifier)._authRepository.getAccessToken();
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// 个人资料状态提供者
final profileProvider = StateNotifierProvider<ProfileNotifier, ProfileState>((ref) {
  return ProfileNotifier(ref);
});
