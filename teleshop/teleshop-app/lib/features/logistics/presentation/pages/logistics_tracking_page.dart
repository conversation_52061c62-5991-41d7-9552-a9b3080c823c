import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:teleshop_app/features/logistics/presentation/providers/logistics_provider.dart';
import 'package:teleshop_app/features/logistics/presentation/widgets/logistics_widgets.dart';
import 'package:teleshop_app/features/logistics/domain/entities/logistics_tracking.dart';
import 'package:teleshop_app/widgets/common/loading_widget.dart';
import 'package:teleshop_app/widgets/common/error_widget.dart';

/// 物流跟踪页面
/// 支持实时物流跟踪、配送员联系、配送时间预约、签收确认、物流异常处理等功能
class LogisticsTrackingPage extends ConsumerStatefulWidget {
  final String orderId;
  final String? trackingNumber;

  const LogisticsTrackingPage({
    super.key,
    required this.orderId,
    this.trackingNumber,
  });

  @override
  ConsumerState<LogisticsTrackingPage> createState() => _LogisticsTrackingPageState();
}

class _LogisticsTrackingPageState extends ConsumerState<LogisticsTrackingPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadLogisticsData();
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadLogisticsData() {
    final notifier = ref.read(logisticsProvider.notifier);
    notifier.loadOrderLogisticsTracking(widget.orderId);
    notifier.loadDeliveryPerson(widget.orderId);
    notifier.loadAvailableDeliveryTimes(widget.orderId);
  }

  @override
  Widget build(BuildContext context) {
    final logisticsState = ref.watch(logisticsProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(logisticsState.tracking),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: logisticsState.isLoading && logisticsState.tracking == null
            ? const LoadingWidget()
            : logisticsState.error != null && logisticsState.tracking == null
                ? ErrorWidget(
                    message: logisticsState.error!,
                    onRetry: _loadLogisticsData,
                  )
                : _buildContent(logisticsState),
      ),
      bottomNavigationBar: logisticsState.tracking != null
          ? _buildBottomActions(logisticsState.tracking!)
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar(LogisticsTracking? tracking) {
    return AppBar(
      title: Text(tracking?.trackingNumber ?? '物流跟踪'),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => _refreshTracking(),
        ),
        IconButton(
          icon: const Icon(Icons.share),
          onPressed: () => _shareTracking(tracking),
        ),
      ],
    );
  }

  Widget _buildContent(LogisticsState state) {
    final tracking = state.tracking!;
    
    return RefreshIndicator(
      onRefresh: () async {
        _loadLogisticsData();
      },
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 物流状态卡片
          SliverToBoxAdapter(child: _buildStatusCard(tracking)),

          // 配送员信息
          if (tracking.deliveryPerson != null)
            SliverToBoxAdapter(child: _buildDeliveryPersonCard(tracking.deliveryPerson!)),

          // 预计送达时间
          SliverToBoxAdapter(child: _buildDeliveryTimeCard(tracking)),

          // 物流进度
          SliverToBoxAdapter(child: _buildProgressCard(tracking)),

          // 物流轨迹
          SliverToBoxAdapter(child: _buildTrackingHistory(tracking.traces)),

          // 异常处理
          if (tracking.hasException)
            SliverToBoxAdapter(child: _buildExceptionCard(tracking.exceptionInfo!)),

          // 服务评价
          if (tracking.isDelivered)
            SliverToBoxAdapter(child: _buildReviewCard(tracking)),

          SliverToBoxAdapter(child: SizedBox(height: 100.h)),
        ],
      ),
    );
  }

  Widget _buildStatusCard(LogisticsTracking tracking) {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _getStatusGradientColors(tracking.currentStatus),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  _getStatusIcon(tracking.currentStatus),
                  color: Colors.white,
                  size: 24.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tracking.statusDescription,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '${tracking.logisticsCompanyName} · ${tracking.trackingNumber}',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          if (tracking.estimatedDeliveryTime != null)
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: Colors.white.withOpacity(0.9),
                  size: 16.sp,
                ),
                SizedBox(width: 4.w),
                Text(
                  '预计 ${_formatDateTime(tracking.estimatedDeliveryTime!)} 送达',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildDeliveryPersonCard(DeliveryPerson deliveryPerson) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.delivery_dining, color: Theme.of(context).primaryColor),
              SizedBox(width: 8.w),
              Text(
                '配送员信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              CircleAvatar(
                radius: 24.r,
                backgroundImage: deliveryPerson.avatar != null
                    ? NetworkImage(deliveryPerson.avatar!)
                    : null,
                child: deliveryPerson.avatar == null
                    ? Icon(Icons.person, size: 24.sp)
                    : null,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          deliveryPerson.name,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        if (deliveryPerson.isOnline)
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                            child: Text(
                              '在线',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10.sp,
                              ),
                            ),
                          ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        Icon(Icons.star, color: Colors.orange, size: 16.sp),
                        SizedBox(width: 4.w),
                        Text(
                          '${deliveryPerson.rating?.toStringAsFixed(1) ?? "暂无评分"}',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Text(
                          '${deliveryPerson.vehicleType} ${deliveryPerson.vehicleNumber}',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  IconButton(
                    onPressed: () => _callDeliveryPerson(deliveryPerson),
                    icon: Icon(Icons.phone, color: Theme.of(context).primaryColor),
                  ),
                  IconButton(
                    onPressed: () => _chatWithDeliveryPerson(deliveryPerson),
                    icon: Icon(Icons.chat, color: Theme.of(context).primaryColor),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryTimeCard(LogisticsTracking tracking) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.access_time, color: Theme.of(context).primaryColor),
              SizedBox(width: 8.w),
              Text(
                '配送时间',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (tracking.canScheduleDelivery && !tracking.isDelivered)
                TextButton(
                  onPressed: () => _scheduleDelivery(),
                  child: const Text('预约配送'),
                ),
            ],
          ),
          SizedBox(height: 12.h),
          if (tracking.estimatedDeliveryTime != null)
            _buildTimeItem(
              '预计送达',
              _formatDateTime(tracking.estimatedDeliveryTime!),
              Icons.schedule,
              Colors.blue,
            ),
          if (tracking.actualDeliveryTime != null)
            _buildTimeItem(
              '实际送达',
              _formatDateTime(tracking.actualDeliveryTime!),
              Icons.check_circle,
              Colors.green,
            ),
          if (tracking.shipTime != null)
            _buildTimeItem(
              '发货时间',
              _formatDateTime(tracking.shipTime!),
              Icons.local_shipping,
              Colors.orange,
            ),
        ],
      ),
    );
  }

  Widget _buildTimeItem(String label, String time, IconData icon, Color color) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16.sp),
          SizedBox(width: 8.w),
          Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
          const Spacer(),
          Text(
            time,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCard(LogisticsTracking tracking) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.timeline, color: Theme.of(context).primaryColor),
              SizedBox(width: 8.w),
              Text(
                '配送进度',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '${tracking.getDeliveryProgress()}%',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          LogisticsProgressIndicator(
            progress: tracking.getDeliveryProgress() / 100,
            currentStatus: tracking.currentStatus,
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingHistory(List<LogisticsTrace> traces) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.history, color: Theme.of(context).primaryColor),
              SizedBox(width: 8.w),
              Text(
                '物流轨迹',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          LogisticsTraceTimeline(traces: traces),
        ],
      ),
    );
  }

  Widget _buildExceptionCard(LogisticsExceptionInfo exceptionInfo) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              SizedBox(width: 8.w),
              Text(
                '物流异常',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.red.shade700,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            exceptionInfo.description,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.red.shade700,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => _reportException(),
                  child: const Text('报告问题'),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _contactCustomerService(),
                  child: const Text('联系客服'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReviewCard(LogisticsTracking tracking) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.rate_review, color: Theme.of(context).primaryColor),
              SizedBox(width: 8.w),
              Text(
                '服务评价',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            '您对本次配送服务满意吗？',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 12.h),
          ElevatedButton(
            onPressed: () => _submitLogisticsReview(tracking),
            child: const Text('评价服务'),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions(LogisticsTracking tracking) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            if (tracking.isOutForDelivery && !tracking.isDelivered)
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _confirmReceipt(tracking),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('确认收货'),
                ),
              ),
            if (tracking.canContactDeliveryPerson && tracking.deliveryPerson != null)
              Expanded(
                child: OutlinedButton(
                  onPressed: () => _callDeliveryPerson(tracking.deliveryPerson!),
                  child: const Text('联系配送员'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 辅助方法
  List<Color> _getStatusGradientColors(String status) {
    switch (status) {
      case 'DELIVERED':
        return [Colors.green.shade400, Colors.green.shade600];
      case 'OUT_FOR_DELIVERY':
        return [Colors.blue.shade400, Colors.blue.shade600];
      case 'IN_TRANSIT':
        return [Colors.orange.shade400, Colors.orange.shade600];
      default:
        return [Colors.grey.shade400, Colors.grey.shade600];
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'DELIVERED':
        return Icons.check_circle;
      case 'OUT_FOR_DELIVERY':
        return Icons.local_shipping;
      case 'IN_TRANSIT':
        return Icons.timeline;
      default:
        return Icons.info;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month}月${dateTime.day}日 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 事件处理方法
  void _refreshTracking() {
    final notifier = ref.read(logisticsProvider.notifier);
    notifier.syncLogisticsTracking(widget.orderId);
  }

  void _shareTracking(LogisticsTracking? tracking) {
    if (tracking != null) {
      ref.read(logisticsProvider.notifier).shareLogisticsTracking(tracking);
    }
  }

  void _scheduleDelivery() {
    Navigator.of(context).pushNamed('/logistics/schedule-delivery', arguments: widget.orderId);
  }

  void _callDeliveryPerson(DeliveryPerson deliveryPerson) {
    ref.read(logisticsProvider.notifier).contactDeliveryPerson(
      widget.orderId,
      'CALL',
      deliveryPerson.phone,
    );
  }

  void _chatWithDeliveryPerson(DeliveryPerson deliveryPerson) {
    Navigator.of(context).pushNamed('/logistics/chat-delivery-person', arguments: {
      'orderId': widget.orderId,
      'deliveryPerson': deliveryPerson,
    });
  }

  void _confirmReceipt(LogisticsTracking tracking) {
    showDialog(
      context: context,
      builder: (context) => ConfirmReceiptDialog(
        tracking: tracking,
        onConfirm: (receiptInfo) {
          ref.read(logisticsProvider.notifier).confirmReceipt(widget.orderId, receiptInfo);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _reportException() {
    Navigator.of(context).pushNamed('/logistics/report-exception', arguments: widget.orderId);
  }

  void _contactCustomerService() {
    Navigator.of(context).pushNamed('/customer-service', arguments: {
      'type': 'logistics',
      'orderId': widget.orderId,
    });
  }

  void _submitLogisticsReview(LogisticsTracking tracking) {
    Navigator.of(context).pushNamed('/logistics/review', arguments: tracking);
  }
}
