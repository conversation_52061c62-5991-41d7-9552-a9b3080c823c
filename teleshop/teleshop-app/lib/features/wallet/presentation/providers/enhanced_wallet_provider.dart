import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/datasources/wallet_api_service.dart';
import '../../data/models/wallet_models.dart';
import '../../data/services/qr_code_service.dart';
import '../../data/services/payment_security_service.dart';
import '../../../auth/presentation/providers/enhanced_auth_provider.dart';
import '../../../../core/network/dio_client.dart';

/// 增强版钱包服务提供者
final enhancedWalletApiServiceProvider = Provider<WalletApiService>((ref) {
  final dio = ref.watch(dioProvider);
  return WalletApiService(dio);
});

final enhancedQrCodeServiceProvider = Provider<QrCodeService>((ref) {
  return QrCodeService();
});

final enhancedPaymentSecurityServiceProvider = Provider<PaymentSecurityService>((ref) {
  return PaymentSecurityService();
});

/// 增强版钱包状态
class EnhancedWalletState {
  final bool isLoading;
  final List<WalletResponse> wallets;
  final UserAssetResponse? userAssets;
  final String? error;
  final WalletResponse? selectedWallet;
  final List<TransactionResponse> recentTransactions;
  final bool hasPayPassword;
  final bool biometricPayEnabled;
  final int payPasswordFailedAttempts;
  final int payPasswordLockTime;

  const EnhancedWalletState({
    this.isLoading = false,
    this.wallets = const [],
    this.userAssets,
    this.error,
    this.selectedWallet,
    this.recentTransactions = const [],
    this.hasPayPassword = false,
    this.biometricPayEnabled = false,
    this.payPasswordFailedAttempts = 0,
    this.payPasswordLockTime = 0,
  });

  EnhancedWalletState copyWith({
    bool? isLoading,
    List<WalletResponse>? wallets,
    UserAssetResponse? userAssets,
    String? error,
    WalletResponse? selectedWallet,
    List<TransactionResponse>? recentTransactions,
    bool? hasPayPassword,
    bool? biometricPayEnabled,
    int? payPasswordFailedAttempts,
    int? payPasswordLockTime,
  }) {
    return EnhancedWalletState(
      isLoading: isLoading ?? this.isLoading,
      wallets: wallets ?? this.wallets,
      userAssets: userAssets ?? this.userAssets,
      error: error,
      selectedWallet: selectedWallet ?? this.selectedWallet,
      recentTransactions: recentTransactions ?? this.recentTransactions,
      hasPayPassword: hasPayPassword ?? this.hasPayPassword,
      biometricPayEnabled: biometricPayEnabled ?? this.biometricPayEnabled,
      payPasswordFailedAttempts: payPasswordFailedAttempts ?? this.payPasswordFailedAttempts,
      payPasswordLockTime: payPasswordLockTime ?? this.payPasswordLockTime,
    );
  }

  bool get isPayPasswordLocked => payPasswordLockTime > 0;
}

/// 增强版钱包状态管理
class EnhancedWalletNotifier extends StateNotifier<EnhancedWalletState> {
  EnhancedWalletNotifier(this._ref) : super(const EnhancedWalletState()) {
    _initialize();
  }

  final Ref _ref;
  Timer? _refreshTimer;
  Timer? _securityTimer;

  WalletApiService get _walletApi => _ref.read(enhancedWalletApiServiceProvider);
  QrCodeService get _qrCodeService => _ref.read(enhancedQrCodeServiceProvider);
  PaymentSecurityService get _paymentSecurity => _ref.read(enhancedPaymentSecurityServiceProvider);

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _securityTimer?.cancel();
    super.dispose();
  }

  /// 初始化
  Future<void> _initialize() async {
    await Future.wait([
      loadUserWallets(),
      _loadSecurityInfo(),
    ]);
    _startTimers();
  }

  /// 加载用户钱包列表
  Future<void> loadUserWallets() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final authState = _ref.read(enhancedAuthProvider);
      final user = authState.maybeWhen(
        authenticated: (user) => user,
        orElse: () => null,
      );

      if (user == null) {
        state = state.copyWith(
          isLoading: false,
          error: '用户未登录',
        );
        return;
      }

      final accessToken = await _ref.read(enhancedAuthProvider.notifier)._authRepository.getAccessToken();
      if (accessToken == null) {
        state = state.copyWith(
          isLoading: false,
          error: '访问令牌不存在',
        );
        return;
      }

      // 并行加载钱包列表和用户资产
      final results = await Future.wait([
        _walletApi.getUserWallets(user.id, 'Bearer $accessToken'),
        _walletApi.getUserAssets(user.id, 'Bearer $accessToken'),
      ]);

      final wallets = results[0] as List<WalletResponse>;
      final userAssets = results[1] as UserAssetResponse;

      // 设置默认选中钱包
      WalletResponse? selectedWallet;
      if (wallets.isNotEmpty) {
        selectedWallet = wallets.firstWhere(
          (wallet) => wallet.isDefault,
          orElse: () => wallets.first,
        );
      }

      // 加载最近交易记录
      List<TransactionResponse> recentTransactions = [];
      if (selectedWallet != null) {
        try {
          final transactionPage = await _walletApi.getTransactions(
            selectedWallet.walletId,
            'Bearer $accessToken',
            0, // page
            10, // size
            null, // type
            null, // status
            null, // startTime
            null, // endTime
          );
          recentTransactions = transactionPage.content;
        } catch (e) {
          // 忽略交易记录加载失败
        }
      }

      state = state.copyWith(
        isLoading: false,
        wallets: wallets,
        userAssets: userAssets,
        selectedWallet: selectedWallet,
        recentTransactions: recentTransactions,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '加载钱包信息失败: $e',
      );
    }
  }

  /// 加载安全信息
  Future<void> _loadSecurityInfo() async {
    try {
      final results = await Future.wait([
        _paymentSecurity.hasPayPassword(),
        _paymentSecurity.isBiometricPayEnabled(),
        _paymentSecurity.getFailedAttempts(),
        _paymentSecurity.getLockRemainingTime(),
      ]);

      state = state.copyWith(
        hasPayPassword: results[0] as bool,
        biometricPayEnabled: results[1] as bool,
        payPasswordFailedAttempts: results[2] as int,
        payPasswordLockTime: results[3] as int,
      );
    } catch (e) {
      // 忽略安全信息加载失败
    }
  }

  /// 选择钱包
  Future<void> selectWallet(WalletResponse wallet) async {
    state = state.copyWith(selectedWallet: wallet);
    
    // 加载该钱包的最近交易记录
    await _loadRecentTransactions(wallet.walletId);
  }

  /// 加载最近交易记录
  Future<void> _loadRecentTransactions(int walletId) async {
    try {
      final accessToken = await _ref.read(enhancedAuthProvider.notifier)._authRepository.getAccessToken();
      if (accessToken == null) return;

      final transactionPage = await _walletApi.getTransactions(
        walletId,
        'Bearer $accessToken',
        0, // page
        10, // size
        null, // type
        null, // status
        null, // startTime
        null, // endTime
      );

      state = state.copyWith(recentTransactions: transactionPage.content);
    } catch (e) {
      // 忽略交易记录加载失败
    }
  }

  /// 扫码支付
  Future<bool> qrCodePayment({
    required QrCodeData qrData,
    required String payPassword,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // 验证二维码
      if (_qrCodeService.isQrCodeExpired(qrData)) {
        state = state.copyWith(
          isLoading: false,
          error: '二维码已过期',
        );
        return false;
      }

      if (!qrData.isPaymentCode) {
        state = state.copyWith(
          isLoading: false,
          error: '无效的支付二维码',
        );
        return false;
      }

      // 验证支付密码
      final verifyResult = await _paymentSecurity.verifyPayPassword(payPassword);
      if (!verifyResult.success) {
        await _loadSecurityInfo(); // 更新安全信息
        state = state.copyWith(
          isLoading: false,
          error: verifyResult.message ?? '支付密码验证失败',
        );
        return false;
      }

      final accessToken = await _ref.read(enhancedAuthProvider.notifier)._authRepository.getAccessToken();
      if (accessToken == null) {
        state = state.copyWith(
          isLoading: false,
          error: '访问令牌不存在',
        );
        return false;
      }

      if (state.selectedWallet == null) {
        state = state.copyWith(
          isLoading: false,
          error: '请选择支付钱包',
        );
        return false;
      }

      final request = QrCodePaymentRequest(
        walletId: state.selectedWallet!.walletId,
        qrCodeData: qrData.toJson(),
        payPassword: payPassword,
      );

      await _walletApi.qrCodePayment('Bearer $accessToken', request);

      // 刷新钱包信息
      await loadUserWallets();
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '扫码支付失败: $e',
      );
      return false;
    }
  }

  /// 生物识别支付
  Future<bool> biometricPayment({
    required QrCodeData qrData,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // 验证生物识别
      final biometricResult = await _paymentSecurity.verifyBiometricPay();
      if (!biometricResult.success) {
        state = state.copyWith(
          isLoading: false,
          error: biometricResult.message ?? '生物识别验证失败',
        );
        return false;
      }

      // 执行支付（不需要支付密码）
      final accessToken = await _ref.read(enhancedAuthProvider.notifier)._authRepository.getAccessToken();
      if (accessToken == null) {
        state = state.copyWith(
          isLoading: false,
          error: '访问令牌不存在',
        );
        return false;
      }

      if (state.selectedWallet == null) {
        state = state.copyWith(
          isLoading: false,
          error: '请选择支付钱包',
        );
        return false;
      }

      final request = QrCodePaymentRequest(
        walletId: state.selectedWallet!.walletId,
        qrCodeData: qrData.toJson(),
        payPassword: '', // 生物识别支付不需要密码
      );

      await _walletApi.qrCodePayment('Bearer $accessToken', request);

      // 刷新钱包信息
      await loadUserWallets();
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '生物识别支付失败: $e',
      );
      return false;
    }
  }

  /// 设置支付密码
  Future<bool> setPayPassword(String password, String confirmPassword, String verifyCode) async {
    try {
      if (password != confirmPassword) {
        state = state.copyWith(error: '两次输入的密码不一致');
        return false;
      }

      final result = await _paymentSecurity.setPayPassword(password);
      if (result.success) {
        await _loadSecurityInfo();
        return true;
      } else {
        state = state.copyWith(error: result.message);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: '设置支付密码失败: $e');
      return false;
    }
  }

  /// 启用/禁用生物识别支付
  Future<bool> setBiometricPayEnabled(bool enabled) async {
    try {
      final result = await _paymentSecurity.setBiometricPayEnabled(enabled);
      if (result.success) {
        await _loadSecurityInfo();
        return true;
      } else {
        state = state.copyWith(error: result.message);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: '设置生物识别支付失败: $e');
      return false;
    }
  }

  /// 生成收款二维码
  String generateReceiveQrCode({
    required double amount,
    String? remark,
    String? merchantName,
  }) {
    if (state.selectedWallet?.walletAddress == null) {
      throw Exception('钱包地址不存在');
    }

    return _qrCodeService.generateReceiveQrCodeData(
      walletAddress: state.selectedWallet!.walletAddress!,
      amount: amount,
      remark: remark,
      merchantName: merchantName,
      expireTime: DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch,
    );
  }

  /// 解析二维码
  QrCodeData? parseQrCode(String data) {
    return _qrCodeService.parseQrCodeData(data);
  }

  /// 启动定时器
  void _startTimers() {
    // 钱包余额刷新定时器
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (state.selectedWallet != null) {
        _refreshWalletBalance(state.selectedWallet!.walletId);
      }
    });

    // 安全信息刷新定时器
    _securityTimer?.cancel();
    _securityTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _loadSecurityInfo();
    });
  }

  /// 刷新钱包余额
  Future<void> _refreshWalletBalance(int walletId) async {
    try {
      final accessToken = await _ref.read(enhancedAuthProvider.notifier)._authRepository.getAccessToken();
      if (accessToken == null) return;

      final balance = await _walletApi.getWalletBalance(walletId, 'Bearer $accessToken');
      
      // 更新对应钱包的余额
      final updatedWallets = state.wallets.map((wallet) {
        if (wallet.walletId == walletId) {
          return WalletResponse(
            walletId: wallet.walletId,
            userId: wallet.userId,
            walletType: wallet.walletType,
            walletName: wallet.walletName,
            availableBalance: balance.availableBalance,
            frozenBalance: balance.frozenBalance,
            totalBalance: balance.totalBalance,
            walletAddress: wallet.walletAddress,
            status: wallet.status,
            isDefault: wallet.isDefault,
            securityLevel: wallet.securityLevel,
            dailyTransferLimit: wallet.dailyTransferLimit,
            singleTransferLimit: wallet.singleTransferLimit,
            todayTransferAmount: wallet.todayTransferAmount,
            lastTransactionTime: wallet.lastTransactionTime,
            createTime: wallet.createTime,
            updateTime: balance.updateTime,
          );
        }
        return wallet;
      }).toList();

      // 更新选中钱包
      WalletResponse? updatedSelectedWallet;
      if (state.selectedWallet?.walletId == walletId) {
        updatedSelectedWallet = updatedWallets.firstWhere(
          (wallet) => wallet.walletId == walletId,
        );
      }

      state = state.copyWith(
        wallets: updatedWallets,
        selectedWallet: updatedSelectedWallet,
      );
    } catch (e) {
      // 静默处理刷新失败
    }
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// 增强版钱包状态提供者
final enhancedWalletProvider = StateNotifierProvider<EnhancedWalletNotifier, EnhancedWalletState>((ref) {
  return EnhancedWalletNotifier(ref);
});

/// 二维码支付请求模型
class QrCodePaymentRequest {
  final int walletId;
  final Map<String, dynamic> qrCodeData;
  final String payPassword;

  QrCodePaymentRequest({
    required this.walletId,
    required this.qrCodeData,
    required this.payPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'walletId': walletId,
      'qrCodeData': qrCodeData,
      'payPassword': payPassword,
    };
  }
}
