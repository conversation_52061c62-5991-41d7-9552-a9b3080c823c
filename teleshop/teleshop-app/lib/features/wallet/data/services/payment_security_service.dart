import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 支付安全服务
class PaymentSecurityService {
  static final PaymentSecurityService _instance = PaymentSecurityService._internal();
  factory PaymentSecurityService() => _instance;
  PaymentSecurityService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();
  static const String _keyPayPasswordHash = 'pay_password_hash';
  static const String _keyPayPasswordSalt = 'pay_password_salt';
  static const String _keyFailedAttempts = 'pay_password_failed_attempts';
  static const String _keyLockTime = 'pay_password_lock_time';
  static const String _keyBiometricPayEnabled = 'biometric_pay_enabled';
  static const int _maxFailedAttempts = 5;
  static const int _lockDurationMinutes = 30;

  /// 设置支付密码
  Future<PaymentSecurityResult> setPayPassword(String password) async {
    try {
      if (!_isValidPassword(password)) {
        return PaymentSecurityResult.error('支付密码必须是6位数字');
      }

      final prefs = await SharedPreferences.getInstance();
      final salt = _generateSalt();
      final hash = _hashPassword(password, salt);

      await Future.wait([
        prefs.setString(_keyPayPasswordHash, hash),
        prefs.setString(_keyPayPasswordSalt, salt),
        prefs.remove(_keyFailedAttempts),
        prefs.remove(_keyLockTime),
      ]);

      return PaymentSecurityResult.success();
    } catch (e) {
      return PaymentSecurityResult.error('设置支付密码失败: $e');
    }
  }

  /// 验证支付密码
  Future<PaymentSecurityResult> verifyPayPassword(String password) async {
    try {
      // 检查是否被锁定
      final lockResult = await _checkLockStatus();
      if (!lockResult.success) {
        return lockResult;
      }

      final prefs = await SharedPreferences.getInstance();
      final storedHash = prefs.getString(_keyPayPasswordHash);
      final salt = prefs.getString(_keyPayPasswordSalt);

      if (storedHash == null || salt == null) {
        return PaymentSecurityResult.error('未设置支付密码');
      }

      final inputHash = _hashPassword(password, salt);
      if (inputHash == storedHash) {
        // 验证成功，清除失败次数
        await prefs.remove(_keyFailedAttempts);
        return PaymentSecurityResult.success();
      } else {
        // 验证失败，增加失败次数
        await _incrementFailedAttempts();
        return PaymentSecurityResult.error('支付密码错误');
      }
    } catch (e) {
      return PaymentSecurityResult.error('验证支付密码失败: $e');
    }
  }

  /// 修改支付密码
  Future<PaymentSecurityResult> changePayPassword(String oldPassword, String newPassword) async {
    try {
      // 先验证旧密码
      final verifyResult = await verifyPayPassword(oldPassword);
      if (!verifyResult.success) {
        return verifyResult;
      }

      // 设置新密码
      return await setPayPassword(newPassword);
    } catch (e) {
      return PaymentSecurityResult.error('修改支付密码失败: $e');
    }
  }

  /// 检查是否设置了支付密码
  Future<bool> hasPayPassword() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hash = prefs.getString(_keyPayPasswordHash);
      return hash != null && hash.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 清除支付密码
  Future<PaymentSecurityResult> clearPayPassword() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await Future.wait([
        prefs.remove(_keyPayPasswordHash),
        prefs.remove(_keyPayPasswordSalt),
        prefs.remove(_keyFailedAttempts),
        prefs.remove(_keyLockTime),
      ]);
      return PaymentSecurityResult.success();
    } catch (e) {
      return PaymentSecurityResult.error('清除支付密码失败: $e');
    }
  }

  /// 启用/禁用生物识别支付
  Future<PaymentSecurityResult> setBiometricPayEnabled(bool enabled) async {
    try {
      if (enabled) {
        // 检查设备支持
        if (!await _localAuth.isDeviceSupported()) {
          return PaymentSecurityResult.error('设备不支持生物识别');
        }

        if (!await _localAuth.canCheckBiometrics) {
          return PaymentSecurityResult.error('生物识别不可用');
        }

        // 执行一次生物识别验证
        final didAuthenticate = await _localAuth.authenticate(
          localizedReason: '请验证您的身份以启用生物识别支付',
          options: const AuthenticationOptions(
            sensitiveTransaction: true,
            biometricOnly: true,
          ),
        );

        if (!didAuthenticate) {
          return PaymentSecurityResult.error('生物识别验证失败');
        }
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyBiometricPayEnabled, enabled);
      return PaymentSecurityResult.success();
    } catch (e) {
      return PaymentSecurityResult.error('设置生物识别支付失败: $e');
    }
  }

  /// 检查是否启用了生物识别支付
  Future<bool> isBiometricPayEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyBiometricPayEnabled) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// 生物识别支付验证
  Future<PaymentSecurityResult> verifyBiometricPay() async {
    try {
      // 检查是否启用
      if (!await isBiometricPayEnabled()) {
        return PaymentSecurityResult.error('未启用生物识别支付');
      }

      // 检查设备支持
      if (!await _localAuth.canCheckBiometrics) {
        return PaymentSecurityResult.error('生物识别不可用');
      }

      // 执行生物识别验证
      final didAuthenticate = await _localAuth.authenticate(
        localizedReason: '请验证您的身份以完成支付',
        options: const AuthenticationOptions(
          sensitiveTransaction: true,
          biometricOnly: true,
        ),
      );

      if (didAuthenticate) {
        return PaymentSecurityResult.success();
      } else {
        return PaymentSecurityResult.error('生物识别验证失败');
      }
    } catch (e) {
      return PaymentSecurityResult.error('生物识别支付验证失败: $e');
    }
  }

  /// 获取锁定剩余时间（分钟）
  Future<int> getLockRemainingTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lockTime = prefs.getInt(_keyLockTime);
      if (lockTime == null) return 0;

      final now = DateTime.now().millisecondsSinceEpoch;
      final unlockTime = lockTime + (_lockDurationMinutes * 60 * 1000);
      
      if (now >= unlockTime) {
        // 锁定时间已过，清除锁定状态
        await Future.wait([
          prefs.remove(_keyLockTime),
          prefs.remove(_keyFailedAttempts),
        ]);
        return 0;
      }

      return ((unlockTime - now) / (60 * 1000)).ceil();
    } catch (e) {
      return 0;
    }
  }

  /// 获取失败尝试次数
  Future<int> getFailedAttempts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_keyFailedAttempts) ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// 检查锁定状态
  Future<PaymentSecurityResult> _checkLockStatus() async {
    final remainingTime = await getLockRemainingTime();
    if (remainingTime > 0) {
      return PaymentSecurityResult.error('支付密码已锁定，请${remainingTime}分钟后再试');
    }
    return PaymentSecurityResult.success();
  }

  /// 增加失败尝试次数
  Future<void> _incrementFailedAttempts() async {
    final prefs = await SharedPreferences.getInstance();
    final currentAttempts = await getFailedAttempts();
    final newAttempts = currentAttempts + 1;

    await prefs.setInt(_keyFailedAttempts, newAttempts);

    if (newAttempts >= _maxFailedAttempts) {
      // 达到最大失败次数，锁定账户
      final lockTime = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt(_keyLockTime, lockTime);
    }
  }

  /// 验证密码格式
  bool _isValidPassword(String password) {
    if (password.length != 6) return false;
    return RegExp(r'^\d{6}$').hasMatch(password);
  }

  /// 生成盐值
  String _generateSalt() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// 哈希密码
  String _hashPassword(String password, String salt) {
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}

/// 支付安全结果
class PaymentSecurityResult {
  final bool success;
  final String? message;

  const PaymentSecurityResult({
    required this.success,
    this.message,
  });

  factory PaymentSecurityResult.success([String? message]) {
    return PaymentSecurityResult(success: true, message: message);
  }

  factory PaymentSecurityResult.error(String message) {
    return PaymentSecurityResult(success: false, message: message);
  }
}
