import 'package:json_annotation/json_annotation.dart';

part 'wallet_models.g.dart';

/// 钱包响应模型
@JsonSerializable()
class WalletResponse {
  final int walletId;
  final int userId;
  final int walletType;
  final String walletName;
  final double availableBalance;
  final double frozenBalance;
  final double totalBalance;
  final String? walletAddress;
  final int status;
  final bool isDefault;
  final int securityLevel;
  final double? dailyTransferLimit;
  final double? singleTransferLimit;
  final double todayTransferAmount;
  final String? lastTransactionTime;
  final String createTime;
  final String updateTime;

  WalletResponse({
    required this.walletId,
    required this.userId,
    required this.walletType,
    required this.walletName,
    required this.availableBalance,
    required this.frozenBalance,
    required this.totalBalance,
    this.walletAddress,
    required this.status,
    required this.isDefault,
    required this.securityLevel,
    this.dailyTransferLimit,
    this.singleTransferLimit,
    required this.todayTransferAmount,
    this.lastTransactionTime,
    required this.createTime,
    required this.updateTime,
  });

  factory WalletResponse.fromJson(Map<String, dynamic> json) =>
      _$WalletResponseFromJson(json);

  Map<String, dynamic> toJson() => _$WalletResponseToJson(this);
}

/// 钱包余额响应模型
@JsonSerializable()
class WalletBalanceResponse {
  final int walletId;
  final double availableBalance;
  final double frozenBalance;
  final double totalBalance;
  final String updateTime;

  WalletBalanceResponse({
    required this.walletId,
    required this.availableBalance,
    required this.frozenBalance,
    required this.totalBalance,
    required this.updateTime,
  });

  factory WalletBalanceResponse.fromJson(Map<String, dynamic> json) =>
      _$WalletBalanceResponseFromJson(json);

  Map<String, dynamic> toJson() => _$WalletBalanceResponseToJson(this);
}

/// 用户资产响应模型
@JsonSerializable()
class UserAssetResponse {
  final int userId;
  final double totalAssets;
  final double totalBalance;
  final double totalPoints;
  final double totalUCoin;
  final List<WalletResponse> wallets;
  final String updateTime;

  UserAssetResponse({
    required this.userId,
    required this.totalAssets,
    required this.totalBalance,
    required this.totalPoints,
    required this.totalUCoin,
    required this.wallets,
    required this.updateTime,
  });

  factory UserAssetResponse.fromJson(Map<String, dynamic> json) =>
      _$UserAssetResponseFromJson(json);

  Map<String, dynamic> toJson() => _$UserAssetResponseToJson(this);
}

/// 交易响应模型
@JsonSerializable()
class TransactionResponse {
  final int transactionId;
  final int walletId;
  final int userId;
  final String transactionNo;
  final String? externalTransactionNo;
  final int transactionType;
  final int direction;
  final double amount;
  final double fee;
  final double actualAmount;
  final double balanceBefore;
  final double balanceAfter;
  final int? targetWalletId;
  final int? targetUserId;
  final String? targetWalletAddress;
  final int status;
  final String? description;
  final String? remark;
  final String? businessOrderNo;
  final String? businessType;
  final String? paymentMethod;
  final String? paymentChannel;
  final String? blockchainTxHash;
  final int confirmations;
  final String? failureReason;
  final String? processedTime;
  final String? completedTime;
  final String createTime;

  TransactionResponse({
    required this.transactionId,
    required this.walletId,
    required this.userId,
    required this.transactionNo,
    this.externalTransactionNo,
    required this.transactionType,
    required this.direction,
    required this.amount,
    required this.fee,
    required this.actualAmount,
    required this.balanceBefore,
    required this.balanceAfter,
    this.targetWalletId,
    this.targetUserId,
    this.targetWalletAddress,
    required this.status,
    this.description,
    this.remark,
    this.businessOrderNo,
    this.businessType,
    this.paymentMethod,
    this.paymentChannel,
    this.blockchainTxHash,
    required this.confirmations,
    this.failureReason,
    this.processedTime,
    this.completedTime,
    required this.createTime,
  });

  factory TransactionResponse.fromJson(Map<String, dynamic> json) =>
      _$TransactionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TransactionResponseToJson(this);
}

/// 充值请求模型
@JsonSerializable()
class RechargeRequest {
  final int walletId;
  final double amount;
  final String paymentMethod;
  final String? paymentChannel;
  final String? remark;

  RechargeRequest({
    required this.walletId,
    required this.amount,
    required this.paymentMethod,
    this.paymentChannel,
    this.remark,
  });

  factory RechargeRequest.fromJson(Map<String, dynamic> json) =>
      _$RechargeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RechargeRequestToJson(this);
}

/// 提现请求模型
@JsonSerializable()
class WithdrawRequest {
  final int walletId;
  final double amount;
  final String targetAddress;
  final String payPassword;
  final String? remark;

  WithdrawRequest({
    required this.walletId,
    required this.amount,
    required this.targetAddress,
    required this.payPassword,
    this.remark,
  });

  factory WithdrawRequest.fromJson(Map<String, dynamic> json) =>
      _$WithdrawRequestFromJson(json);

  Map<String, dynamic> toJson() => _$WithdrawRequestToJson(this);
}

/// 转账请求模型
@JsonSerializable()
class TransferRequest {
  final int fromWalletId;
  final String toWalletAddress;
  final double amount;
  final String payPassword;
  final String? remark;

  TransferRequest({
    required this.fromWalletId,
    required this.toWalletAddress,
    required this.amount,
    required this.payPassword,
    this.remark,
  });

  factory TransferRequest.fromJson(Map<String, dynamic> json) =>
      _$TransferRequestFromJson(json);

  Map<String, dynamic> toJson() => _$TransferRequestToJson(this);
}

/// 支付请求模型
@JsonSerializable()
class PaymentRequest {
  final int walletId;
  final double amount;
  final String businessOrderNo;
  final String businessType;
  final String payPassword;
  final String? remark;

  PaymentRequest({
    required this.walletId,
    required this.amount,
    required this.businessOrderNo,
    required this.businessType,
    required this.payPassword,
    this.remark,
  });

  factory PaymentRequest.fromJson(Map<String, dynamic> json) =>
      _$PaymentRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentRequestToJson(this);
}

/// 设置支付密码请求模型
@JsonSerializable()
class SetPayPasswordRequest {
  final String payPassword;
  final String confirmPassword;
  final String verifyCode;

  SetPayPasswordRequest({
    required this.payPassword,
    required this.confirmPassword,
    required this.verifyCode,
  });

  factory SetPayPasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$SetPayPasswordRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SetPayPasswordRequestToJson(this);
}

/// 修改支付密码请求模型
@JsonSerializable()
class ChangePayPasswordRequest {
  final String oldPassword;
  final String newPassword;
  final String confirmPassword;

  ChangePayPasswordRequest({
    required this.oldPassword,
    required this.newPassword,
    required this.confirmPassword,
  });

  factory ChangePayPasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$ChangePayPasswordRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ChangePayPasswordRequestToJson(this);
}

/// 验证支付密码请求模型
@JsonSerializable()
class VerifyPayPasswordRequest {
  final String payPassword;

  VerifyPayPasswordRequest({
    required this.payPassword,
  });

  factory VerifyPayPasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifyPayPasswordRequestFromJson(json);

  Map<String, dynamic> toJson() => _$VerifyPayPasswordRequestToJson(this);
}

/// 重置支付密码请求模型
@JsonSerializable()
class ResetPayPasswordRequest {
  final String newPassword;
  final String confirmPassword;
  final String verifyCode;

  ResetPayPasswordRequest({
    required this.newPassword,
    required this.confirmPassword,
    required this.verifyCode,
  });

  factory ResetPayPasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$ResetPayPasswordRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ResetPayPasswordRequestToJson(this);
}

/// 分页响应模型
@JsonSerializable()
class PageResponse<T> {
  final List<T> content;
  final int totalElements;
  final int totalPages;
  final int number;
  final int size;
  final bool first;
  final bool last;

  PageResponse({
    required this.content,
    required this.totalElements,
    required this.totalPages,
    required this.number,
    required this.size,
    required this.first,
    required this.last,
  });

  factory PageResponse.fromJson(Map<String, dynamic> json) =>
      _$PageResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PageResponseToJson(this);
}

/// 二维码相关请求模型
@JsonSerializable()
class GenerateQrCodeRequest {
  final String walletAddress;
  final double amount;
  final String? remark;
  final String? merchantName;
  final int? expireTime;

  GenerateQrCodeRequest({
    required this.walletAddress,
    required this.amount,
    this.remark,
    this.merchantName,
    this.expireTime,
  });

  factory GenerateQrCodeRequest.fromJson(Map<String, dynamic> json) =>
      _$GenerateQrCodeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$GenerateQrCodeRequestToJson(this);
}

@JsonSerializable()
class ParseQrCodeRequest {
  final String qrCodeData;

  ParseQrCodeRequest({
    required this.qrCodeData,
  });

  factory ParseQrCodeRequest.fromJson(Map<String, dynamic> json) =>
      _$ParseQrCodeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ParseQrCodeRequestToJson(this);
}

@JsonSerializable()
class QrCodeResponse {
  final String qrCodeData;
  final String qrCodeImage;
  final String expireTime;

  QrCodeResponse({
    required this.qrCodeData,
    required this.qrCodeImage,
    required this.expireTime,
  });

  factory QrCodeResponse.fromJson(Map<String, dynamic> json) =>
      _$QrCodeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$QrCodeResponseToJson(this);
}

@JsonSerializable()
class ParseQrCodeResponse {
  final String type;
  final String walletAddress;
  final double? amount;
  final String? remark;
  final String? merchantName;
  final bool isValid;
  final bool isExpired;

  ParseQrCodeResponse({
    required this.type,
    required this.walletAddress,
    this.amount,
    this.remark,
    this.merchantName,
    required this.isValid,
    required this.isExpired,
  });

  factory ParseQrCodeResponse.fromJson(Map<String, dynamic> json) =>
      _$ParseQrCodeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ParseQrCodeResponseToJson(this);
}

/// 其他钱包相关模型
@JsonSerializable()
class SetTransferLimitRequest {
  final double? dailyLimit;
  final double? singleLimit;

  SetTransferLimitRequest({
    this.dailyLimit,
    this.singleLimit,
  });

  factory SetTransferLimitRequest.fromJson(Map<String, dynamic> json) =>
      _$SetTransferLimitRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SetTransferLimitRequestToJson(this);
}

@JsonSerializable()
class FreezeWalletRequest {
  final String reason;

  FreezeWalletRequest({
    required this.reason,
  });

  factory FreezeWalletRequest.fromJson(Map<String, dynamic> json) =>
      _$FreezeWalletRequestFromJson(json);

  Map<String, dynamic> toJson() => _$FreezeWalletRequestToJson(this);
}

@JsonSerializable()
class UnfreezeWalletRequest {
  final String reason;

  UnfreezeWalletRequest({
    required this.reason,
  });

  factory UnfreezeWalletRequest.fromJson(Map<String, dynamic> json) =>
      _$UnfreezeWalletRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UnfreezeWalletRequestToJson(this);
}

@JsonSerializable()
class WalletStatsResponse {
  final int totalWallets;
  final double totalAssets;
  final double totalIncome;
  final double totalExpense;
  final int totalTransactions;

  WalletStatsResponse({
    required this.totalWallets,
    required this.totalAssets,
    required this.totalIncome,
    required this.totalExpense,
    required this.totalTransactions,
  });

  factory WalletStatsResponse.fromJson(Map<String, dynamic> json) =>
      _$WalletStatsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$WalletStatsResponseToJson(this);
}

@JsonSerializable()
class DailyTransactionStatsResponse {
  final String date;
  final double totalIncome;
  final double totalExpense;
  final int transactionCount;

  DailyTransactionStatsResponse({
    required this.date,
    required this.totalIncome,
    required this.totalExpense,
    required this.transactionCount,
  });

  factory DailyTransactionStatsResponse.fromJson(Map<String, dynamic> json) =>
      _$DailyTransactionStatsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DailyTransactionStatsResponseToJson(this);
}

@JsonSerializable()
class PaymentMethodResponse {
  final int id;
  final String name;
  final String type;
  final String icon;
  final bool enabled;

  PaymentMethodResponse({
    required this.id,
    required this.name,
    required this.type,
    required this.icon,
    required this.enabled,
  });

  factory PaymentMethodResponse.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentMethodResponseToJson(this);
}

@JsonSerializable()
class BankCardResponse {
  final int cardId;
  final String cardNumber;
  final String cardType;
  final String bankName;
  final String holderName;
  final bool isDefault;

  BankCardResponse({
    required this.cardId,
    required this.cardNumber,
    required this.cardType,
    required this.bankName,
    required this.holderName,
    required this.isDefault,
  });

  factory BankCardResponse.fromJson(Map<String, dynamic> json) =>
      _$BankCardResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BankCardResponseToJson(this);
}

@JsonSerializable()
class BindBankCardRequest {
  final String cardNumber;
  final String cardType;
  final String bankName;
  final String holderName;
  final String verifyCode;

  BindBankCardRequest({
    required this.cardNumber,
    required this.cardType,
    required this.bankName,
    required this.holderName,
    required this.verifyCode,
  });

  factory BindBankCardRequest.fromJson(Map<String, dynamic> json) =>
      _$BindBankCardRequestFromJson(json);

  Map<String, dynamic> toJson() => _$BindBankCardRequestToJson(this);
}

@JsonSerializable()
class ExchangeRateResponse {
  final String fromCurrency;
  final String toCurrency;
  final double rate;
  final String updateTime;

  ExchangeRateResponse({
    required this.fromCurrency,
    required this.toCurrency,
    required this.rate,
    required this.updateTime,
  });

  factory ExchangeRateResponse.fromJson(Map<String, dynamic> json) =>
      _$ExchangeRateResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ExchangeRateResponseToJson(this);
}

@JsonSerializable()
class ExchangeRequest {
  final int fromWalletId;
  final int toWalletId;
  final double amount;
  final String payPassword;

  ExchangeRequest({
    required this.fromWalletId,
    required this.toWalletId,
    required this.amount,
    required this.payPassword,
  });

  factory ExchangeRequest.fromJson(Map<String, dynamic> json) =>
      _$ExchangeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ExchangeRequestToJson(this);
}
