import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/wallet_models.dart';

part 'wallet_api_service.g.dart';

/// 钱包API服务
@RestApi()
abstract class WalletApiService {
  factory WalletApiService(Dio dio, {String baseUrl}) = _WalletApiService;

  /// 获取用户钱包列表
  @GET('/api/v1/wallets/user/{userId}')
  Future<List<WalletResponse>> getUserWallets(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 获取钱包详情
  @GET('/api/v1/wallets/{walletId}')
  Future<WalletResponse> getWalletById(
    @Path('walletId') int walletId,
    @Header('Authorization') String accessToken,
  );

  /// 获取钱包余额
  @GET('/api/v1/wallets/{walletId}/balance')
  Future<WalletBalanceResponse> getWalletBalance(
    @Path('walletId') int walletId,
    @Header('Authorization') String accessToken,
  );

  /// 获取用户总资产
  @GET('/api/v1/wallets/user/{userId}/assets')
  Future<UserAssetResponse> getUserAssets(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 充值
  @POST('/api/v1/wallets/recharge')
  Future<TransactionResponse> recharge(
    @Header('Authorization') String accessToken,
    @Body() RechargeRequest request,
  );

  /// 提现
  @POST('/api/v1/wallets/withdraw')
  Future<TransactionResponse> withdraw(
    @Header('Authorization') String accessToken,
    @Body() WithdrawRequest request,
  );

  /// 转账
  @POST('/api/v1/wallets/transfer')
  Future<TransactionResponse> transfer(
    @Header('Authorization') String accessToken,
    @Body() TransferRequest request,
  );

  /// 支付
  @POST('/api/v1/wallets/payment')
  Future<TransactionResponse> payment(
    @Header('Authorization') String accessToken,
    @Body() PaymentRequest request,
  );

  /// 获取交易记录
  @GET('/api/v1/wallets/{walletId}/transactions')
  Future<PageResponse<TransactionResponse>> getTransactions(
    @Path('walletId') int walletId,
    @Header('Authorization') String accessToken,
    @Query('page') int page,
    @Query('size') int size,
    @Query('type') int? type,
    @Query('status') int? status,
    @Query('startTime') String? startTime,
    @Query('endTime') String? endTime,
  );

  /// 获取交易详情
  @GET('/api/v1/wallets/transactions/{transactionId}')
  Future<TransactionResponse> getTransactionById(
    @Path('transactionId') int transactionId,
    @Header('Authorization') String accessToken,
  );

  /// 设置支付密码
  @POST('/api/v1/wallets/pay-password')
  Future<void> setPayPassword(
    @Header('Authorization') String accessToken,
    @Body() SetPayPasswordRequest request,
  );

  /// 修改支付密码
  @PUT('/api/v1/wallets/pay-password')
  Future<void> changePayPassword(
    @Header('Authorization') String accessToken,
    @Body() ChangePayPasswordRequest request,
  );

  /// 验证支付密码
  @POST('/api/v1/wallets/pay-password/verify')
  Future<bool> verifyPayPassword(
    @Header('Authorization') String accessToken,
    @Body() VerifyPayPasswordRequest request,
  );

  /// 重置支付密码
  @POST('/api/v1/wallets/pay-password/reset')
  Future<void> resetPayPassword(
    @Header('Authorization') String accessToken,
    @Body() ResetPayPasswordRequest request,
  );

  /// 设置转账限额
  @PUT('/api/v1/wallets/{walletId}/transfer-limit')
  Future<void> setTransferLimit(
    @Path('walletId') int walletId,
    @Header('Authorization') String accessToken,
    @Body() SetTransferLimitRequest request,
  );

  /// 冻结钱包
  @POST('/api/v1/wallets/{walletId}/freeze')
  Future<void> freezeWallet(
    @Path('walletId') int walletId,
    @Header('Authorization') String accessToken,
    @Body() FreezeWalletRequest request,
  );

  /// 解冻钱包
  @POST('/api/v1/wallets/{walletId}/unfreeze')
  Future<void> unfreezeWallet(
    @Path('walletId') int walletId,
    @Header('Authorization') String accessToken,
    @Body() UnfreezeWalletRequest request,
  );

  /// 获取钱包统计信息
  @GET('/api/v1/wallets/user/{userId}/stats')
  Future<WalletStatsResponse> getWalletStats(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 获取今日交易统计
  @GET('/api/v1/wallets/user/{userId}/daily-stats')
  Future<DailyTransactionStatsResponse> getDailyTransactionStats(
    @Path('userId') int userId,
    @Header('Authorization') String accessToken,
  );

  /// 生成收款二维码
  @POST('/api/v1/wallets/qrcode/receive')
  Future<QrCodeResponse> generateReceiveQrCode(
    @Header('Authorization') String accessToken,
    @Body() GenerateQrCodeRequest request,
  );

  /// 解析支付二维码
  @POST('/api/v1/wallets/qrcode/parse')
  Future<ParseQrCodeResponse> parsePaymentQrCode(
    @Header('Authorization') String accessToken,
    @Body() ParseQrCodeRequest request,
  );

  /// 扫码支付
  @POST('/api/v1/wallets/qrcode/pay')
  Future<TransactionResponse> qrCodePayment(
    @Header('Authorization') String accessToken,
    @Body() QrCodePaymentRequest request,
  );

  /// 获取支付方式列表
  @GET('/api/v1/wallets/payment-methods')
  Future<List<PaymentMethodResponse>> getPaymentMethods(
    @Header('Authorization') String accessToken,
  );

  /// 绑定银行卡
  @POST('/api/v1/wallets/bank-cards')
  Future<BankCardResponse> bindBankCard(
    @Header('Authorization') String accessToken,
    @Body() BindBankCardRequest request,
  );

  /// 获取银行卡列表
  @GET('/api/v1/wallets/bank-cards')
  Future<List<BankCardResponse>> getBankCards(
    @Header('Authorization') String accessToken,
  );

  /// 解绑银行卡
  @DELETE('/api/v1/wallets/bank-cards/{cardId}')
  Future<void> unbindBankCard(
    @Path('cardId') int cardId,
    @Header('Authorization') String accessToken,
  );

  /// 获取汇率信息
  @GET('/api/v1/wallets/exchange-rates')
  Future<List<ExchangeRateResponse>> getExchangeRates();

  /// 货币兑换
  @POST('/api/v1/wallets/exchange')
  Future<TransactionResponse> exchangeCurrency(
    @Header('Authorization') String accessToken,
    @Body() ExchangeRequest request,
  );
}
