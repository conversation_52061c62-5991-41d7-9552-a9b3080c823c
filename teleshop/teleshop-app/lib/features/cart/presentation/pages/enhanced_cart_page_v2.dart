import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:teleshop_app/features/cart/presentation/providers/cart_provider.dart';
import 'package:teleshop_app/features/cart/presentation/widgets/cart_widgets.dart';
import 'package:teleshop_app/features/cart/domain/entities/cart.dart';
import 'package:teleshop_app/features/cart/domain/entities/cart_item.dart';
import 'package:teleshop_app/features/coupon/presentation/pages/coupon_selector_page.dart';
import 'package:teleshop_app/widgets/common/loading_widget.dart';
import 'package:teleshop_app/widgets/common/error_widget.dart';

/// 增强版购物车页面 V2
/// 支持完整的购物车功能：规格变更、优惠券应用、库存检查、价格变动提醒、分享等
class EnhancedCartPageV2 extends ConsumerStatefulWidget {
  const EnhancedCartPageV2({super.key});

  @override
  ConsumerState<EnhancedCartPageV2> createState() => _EnhancedCartPageV2State();
}

class _EnhancedCartPageV2State extends ConsumerState<EnhancedCartPageV2>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final Set<String> _selectedItems = <String>{};
  bool _selectAll = false;
  bool _isEditMode = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // 初始化购物车数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCartData();
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _loadCartData() {
    final notifier = ref.read(cartProvider.notifier);
    notifier.loadCart();
    notifier.checkStockStatus();
    notifier.checkPriceChanges();
    notifier.loadRecommendations();
  }

  @override
  Widget build(BuildContext context) {
    final cartState = ref.watch(cartProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(cartState),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: cartState.isLoading
            ? const LoadingWidget()
            : cartState.error != null
                ? ErrorWidget(
                    message: cartState.error!,
                    onRetry: _loadCartData,
                  )
                : cartState.cart?.items.isEmpty ?? true
                    ? _buildEmptyCart()
                    : _buildCartContent(cartState),
      ),
      bottomNavigationBar: cartState.cart?.items.isNotEmpty ?? false
          ? _buildBottomBar(cartState)
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar(CartState cartState) {
    final itemCount = cartState.cart?.items.length ?? 0;
    final selectedCount = _selectedItems.length;

    return AppBar(
      title: Text(
        _isEditMode 
            ? '已选择 $selectedCount 件商品'
            : '购物车 ($itemCount)',
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (itemCount > 0)
          TextButton(
            onPressed: () {
              setState(() {
                _isEditMode = !_isEditMode;
                if (!_isEditMode) {
                  _selectedItems.clear();
                  _selectAll = false;
                }
              });
            },
            child: Text(
              _isEditMode ? '完成' : '编辑',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        if (!_isEditMode && itemCount > 0)
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareCart(cartState.cart!),
          ),
      ],
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 120.sp,
            color: Colors.grey.shade300,
          ),
          SizedBox(height: 24.h),
          Text(
            '购物车空空如也',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '快去挑选心仪的商品吧～',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey.shade500,
            ),
          ),
          SizedBox(height: 32.h),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pushReplacementNamed('/home'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24.r),
              ),
            ),
            child: Text(
              '去逛逛',
              style: TextStyle(fontSize: 16.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCartContent(CartState cartState) {
    final cart = cartState.cart!;
    
    return RefreshIndicator(
      onRefresh: () async {
        _loadCartData();
      },
      child: CustomScrollView(
        slivers: [
          // 购物车状态提醒
          if (cart.hasInvalidItems || cart.hasOutOfStockItems || cart.hasPriceChangedItems)
            SliverToBoxAdapter(child: _buildStatusAlerts(cart)),

          // 全选和优惠券区域
          SliverToBoxAdapter(child: _buildTopActions(cart)),

          // 购物车商品列表
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final item = cart.items[index];
                return _buildCartItem(item);
              },
              childCount: cart.items.length,
            ),
          ),

          // 推荐商品区域
          if (cart.recommendedItems.isNotEmpty)
            SliverToBoxAdapter(child: _buildRecommendedItems(cart.recommendedItems)),

          // 底部间距
          SliverToBoxAdapter(child: SizedBox(height: 100.h)),
        ],
      ),
    );
  }

  Widget _buildStatusAlerts(Cart cart) {
    return Container(
      margin: EdgeInsets.all(16.w),
      child: Column(
        children: [
          if (cart.hasInvalidItems)
            _buildAlert(
              icon: Icons.error_outline,
              color: Colors.red,
              title: '商品失效提醒',
              message: '购物车中有 ${cart.invalidItemCount} 件商品已失效',
              action: '清理失效商品',
              onActionTap: () => _clearInvalidItems(),
            ),
          if (cart.hasOutOfStockItems)
            _buildAlert(
              icon: Icons.inventory_2_outlined,
              color: Colors.orange,
              title: '库存不足提醒',
              message: '购物车中有 ${cart.outOfStockItemCount} 件商品库存不足',
              action: '查看详情',
              onActionTap: () => _showStockDetails(),
            ),
          if (cart.hasPriceChangedItems)
            _buildAlert(
              icon: Icons.trending_up,
              color: Colors.blue,
              title: '价格变动提醒',
              message: '购物车中有 ${cart.priceChangedItemCount} 件商品价格发生变动',
              action: '查看变动',
              onActionTap: () => _showPriceChanges(),
            ),
        ],
      ),
    );
  }

  Widget _buildAlert({
    required IconData icon,
    required Color color,
    required String title,
    required String message,
    required String action,
    required VoidCallback onActionTap,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20.sp),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: color.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: onActionTap,
            child: Text(
              action,
              style: TextStyle(
                fontSize: 12.sp,
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopActions(Cart cart) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Column(
        children: [
          // 全选行
          Row(
            children: [
              Checkbox(
                value: _selectAll,
                onChanged: _isEditMode ? (value) => _toggleSelectAll(value ?? false) : null,
                activeColor: Theme.of(context).primaryColor,
              ),
              Text(
                '全选',
                style: TextStyle(fontSize: 16.sp),
              ),
              const Spacer(),
              if (!_isEditMode && cart.appliedCoupon == null)
                TextButton.icon(
                  onPressed: () => _showCouponSelector(),
                  icon: const Icon(Icons.local_offer_outlined),
                  label: const Text('选择优惠券'),
                ),
            ],
          ),

          // 优惠券信息
          if (cart.appliedCoupon != null)
            Container(
              margin: EdgeInsets.only(top: 8.h),
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.local_offer, color: Colors.red.shade600, size: 20.sp),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cart.appliedCoupon!.couponName,
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.red.shade700,
                          ),
                        ),
                        Text(
                          '已优惠 ¥${cart.discountAmount.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.red.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  TextButton(
                    onPressed: () => _removeCoupon(),
                    child: Text(
                      '更换',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.red.shade600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCartItem(CartItem item) {
    final isSelected = _selectedItems.contains(item.id);
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: CartItemCard(
        item: item,
        isSelected: isSelected,
        isEditMode: _isEditMode,
        onSelectionChanged: (selected) => _toggleItemSelection(item.id, selected),
        onQuantityChanged: (quantity) => _updateQuantity(item.id, quantity),
        onSpecsChanged: (specs) => _updateSpecs(item.id, specs),
        onRemove: () => _removeItem(item.id),
        onFavorite: () => _moveToFavorites(item.id),
        onNoteChanged: (note) => _updateNote(item.id, note),
      ),
    );
  }

  Widget _buildRecommendedItems(List<CartItem> recommendedItems) {
    return Container(
      margin: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '为你推荐',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          SizedBox(
            height: 200.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: recommendedItems.length,
              itemBuilder: (context, index) {
                final item = recommendedItems[index];
                return Container(
                  width: 150.w,
                  margin: EdgeInsets.only(right: 12.w),
                  child: RecommendedItemCard(
                    item: item,
                    onAddToCart: () => _addRecommendedToCart(item),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar(CartState cartState) {
    final cart = cartState.cart!;
    final selectedItems = cart.items.where((item) => _selectedItems.contains(item.id)).toList();
    final selectedTotal = selectedItems.fold<double>(
      0.0,
      (sum, item) => sum + (item.currentPrice * item.quantity),
    );

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: _isEditMode
            ? _buildEditModeBottomBar()
            : _buildNormalBottomBar(cart, selectedTotal),
      ),
    );
  }

  Widget _buildEditModeBottomBar() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _selectedItems.isNotEmpty ? () => _batchRemoveItems() : null,
            child: Text('删除 (${_selectedItems.length})'),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: OutlinedButton(
            onPressed: _selectedItems.isNotEmpty ? () => _batchMoveToFavorites() : null,
            child: Text('移入收藏 (${_selectedItems.length})'),
          ),
        ),
      ],
    );
  }

  Widget _buildNormalBottomBar(Cart cart, double selectedTotal) {
    return Row(
      children: [
        Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (cart.appliedCoupon != null)
                Text(
                  '已优惠 ¥${cart.discountAmount.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.red,
                  ),
                ),
              Row(
                children: [
                  Text(
                    '合计：',
                    style: TextStyle(fontSize: 16.sp),
                  ),
                  Text(
                    '¥${selectedTotal.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        SizedBox(width: 16.w),
        ElevatedButton(
          onPressed: _selectedItems.isNotEmpty ? () => _proceedToCheckout() : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24.r),
            ),
          ),
          child: Text(
            '结算 (${_selectedItems.length})',
            style: TextStyle(fontSize: 16.sp),
          ),
        ),
      ],
    );
  }

  // 事件处理方法
  void _toggleSelectAll(bool selectAll) {
    setState(() {
      _selectAll = selectAll;
      final cart = ref.read(cartProvider).cart;
      if (cart != null) {
        if (selectAll) {
          _selectedItems.addAll(cart.items.map((item) => item.id));
        } else {
          _selectedItems.clear();
        }
      }
    });
  }

  void _toggleItemSelection(String itemId, bool selected) {
    setState(() {
      if (selected) {
        _selectedItems.add(itemId);
      } else {
        _selectedItems.remove(itemId);
      }
      
      final cart = ref.read(cartProvider).cart;
      if (cart != null) {
        _selectAll = _selectedItems.length == cart.items.length;
      }
    });
  }

  void _updateQuantity(String itemId, int quantity) {
    ref.read(cartProvider.notifier).updateQuantity(itemId, quantity);
  }

  void _updateSpecs(String itemId, Map<String, String> specs) {
    ref.read(cartProvider.notifier).updateSpecs(itemId, specs);
  }

  void _updateNote(String itemId, String note) {
    ref.read(cartProvider.notifier).updateNote(itemId, note);
  }

  void _removeItem(String itemId) {
    ref.read(cartProvider.notifier).removeItem(itemId);
    _selectedItems.remove(itemId);
  }

  void _moveToFavorites(String itemId) {
    ref.read(cartProvider.notifier).moveToFavorites(itemId);
    _selectedItems.remove(itemId);
  }

  void _batchRemoveItems() {
    ref.read(cartProvider.notifier).batchRemoveItems(_selectedItems.toList());
    _selectedItems.clear();
    _selectAll = false;
  }

  void _batchMoveToFavorites() {
    ref.read(cartProvider.notifier).batchMoveToFavorites(_selectedItems.toList());
    _selectedItems.clear();
    _selectAll = false;
  }

  void _showCouponSelector() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CouponSelectorPage(),
      ),
    );
  }

  void _removeCoupon() {
    ref.read(cartProvider.notifier).removeCoupon();
  }

  void _clearInvalidItems() {
    ref.read(cartProvider.notifier).clearInvalidItems();
  }

  void _showStockDetails() {
    // 显示库存详情
  }

  void _showPriceChanges() {
    // 显示价格变动详情
  }

  void _addRecommendedToCart(CartItem item) {
    ref.read(cartProvider.notifier).addToCart(
      productId: item.productId,
      quantity: 1,
      specs: item.specs,
    );
  }

  void _shareCart(Cart cart) {
    ref.read(cartProvider.notifier).shareCart();
  }

  void _proceedToCheckout() {
    final selectedItems = ref.read(cartProvider).cart?.items
        .where((item) => _selectedItems.contains(item.id))
        .toList() ?? [];
    
    Navigator.of(context).pushNamed('/checkout', arguments: {
      'items': selectedItems,
      'coupon': ref.read(cartProvider).cart?.appliedCoupon,
    });
  }
}
