import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:teleshop_app/features/cart/cart_provider.dart';
import 'package:teleshop_app/features/cart/models/cart_models.dart';
import 'package:teleshop_app/features/coupon/coupon_selector.dart';
import 'package:teleshop_app/widgets/common/loading_widget.dart';
import 'package:teleshop_app/widgets/common/error_widget.dart';

/// 增强版购物车页面
/// 支持规格变更、优惠券应用、库存检查、价格变动提醒等完整功能
class EnhancedCartPage extends ConsumerStatefulWidget {
  const EnhancedCartPage({super.key});

  @override
  ConsumerState<EnhancedCartPage> createState() => _EnhancedCartPageState();
}

class _EnhancedCartPageState extends ConsumerState<EnhancedCartPage> {
  final Set<String> _selectedItems = <String>{};
  bool _selectAll = false;

  @override
  void initState() {
    super.initState();
    // 初始化购物车数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cartProvider.notifier).loadCartItems();
      ref.read(cartProvider.notifier).startRealTimeSync();
    });
  }

  @override
  Widget build(BuildContext context) {
    final cartState = ref.watch(cartProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('购物车 (${cartState.items.length})'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (cartState.items.isNotEmpty)
            TextButton(
              onPressed: () => _showCartManagement(),
              child: const Text('管理', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
      body: cartState.isLoading
          ? const LoadingWidget()
          : cartState.error != null
              ? ErrorWidget(
                  message: cartState.error!,
                  onRetry: () => ref.read(cartProvider.notifier).loadCartItems(),
                )
              : cartState.items.isEmpty
                  ? _buildEmptyCart()
                  : _buildCartContent(cartState),
      bottomNavigationBar: cartState.items.isNotEmpty
          ? _buildBottomBar(cartState)
          : null,
    );
  }

  /// 构建空购物车状态
  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '购物车是空的',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '快去挑选心仪的商品吧～',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pushReplacementNamed('/home'),
            child: const Text('去逛逛'),
          ),
        ],
      ),
    );
  }

  /// 构建购物车内容
  Widget _buildCartContent(CartState cartState) {
    return Column(
      children: [
        // 全选和优惠券区域
        _buildTopActions(cartState),
        
        // 购物车商品列表
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: cartState.items.length,
            itemBuilder: (context, index) {
              final item = cartState.items[index];
              return _buildCartItem(item);
            },
          ),
        ),
        
        // 推荐商品区域
        if (cartState.recommendedItems.isNotEmpty)
          _buildRecommendedItems(cartState.recommendedItems),
      ],
    );
  }

  /// 构建顶部操作区域
  Widget _buildTopActions(CartState cartState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Column(
        children: [
          // 全选行
          Row(
            children: [
              Checkbox(
                value: _selectAll,
                onChanged: (value) => _toggleSelectAll(value ?? false),
              ),
              const Text('全选'),
              const Spacer(),
              TextButton(
                onPressed: () => _clearInvalidItems(),
                child: const Text('清理失效商品'),
              ),
            ],
          ),
          
          // 优惠券选择
          if (cartState.availableCoupons.isNotEmpty)
            GestureDetector(
              onTap: () => _showCouponSelector(cartState.availableCoupons),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.local_offer, color: Colors.orange.shade600, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        cartState.selectedCoupon != null
                            ? '已选择：${cartState.selectedCoupon!.name}'
                            : '选择优惠券 (${cartState.availableCoupons.length}张可用)',
                        style: TextStyle(
                          color: Colors.orange.shade700,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    Icon(Icons.chevron_right, color: Colors.orange.shade600),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建购物车商品项
  Widget _buildCartItem(CartItem item) {
    final isSelected = _selectedItems.contains(item.id);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 商品主体信息
          Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 选择框
                Checkbox(
                  value: isSelected,
                  onChanged: (value) => _toggleItemSelection(item.id, value ?? false),
                ),
                
                // 商品图片
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    item.product.imageUrl,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: 80,
                      height: 80,
                      color: Colors.grey.shade200,
                      child: const Icon(Icons.image_not_supported),
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // 商品信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 商品名称
                      Text(
                        item.product.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // 规格信息
                      if (item.selectedSpecs.isNotEmpty)
                        GestureDetector(
                          onTap: () => _showSpecSelector(item),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  _formatSpecs(item.selectedSpecs),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.keyboard_arrow_down,
                                  size: 16,
                                  color: Colors.grey.shade600,
                                ),
                              ],
                            ),
                          ),
                        ),
                      
                      const SizedBox(height: 8),
                      
                      // 价格和数量
                      Row(
                        children: [
                          // 价格
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '¥${item.currentPrice.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              if (item.originalPrice > item.currentPrice)
                                Text(
                                  '¥${item.originalPrice.toStringAsFixed(2)}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade500,
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                            ],
                          ),
                          
                          const Spacer(),
                          
                          // 数量控制
                          _buildQuantityControl(item),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // 状态提示
          if (item.status != CartItemStatus.normal)
            _buildItemStatusBar(item),
        ],
      ),
    );
  }

  /// 构建数量控制器
  Widget _buildQuantityControl(CartItem item) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: item.quantity > 1
                ? () => _updateQuantity(item.id, item.quantity - 1)
                : null,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Icon(
                Icons.remove,
                size: 16,
                color: item.quantity > 1 ? Colors.black : Colors.grey,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              item.quantity.toString(),
              style: const TextStyle(fontSize: 16),
            ),
          ),
          GestureDetector(
            onTap: item.quantity < item.maxQuantity
                ? () => _updateQuantity(item.id, item.quantity + 1)
                : null,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Icon(
                Icons.add,
                size: 16,
                color: item.quantity < item.maxQuantity ? Colors.black : Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建商品状态栏
  Widget _buildItemStatusBar(CartItem item) {
    Color backgroundColor;
    String message;
    IconData icon;
    
    switch (item.status) {
      case CartItemStatus.outOfStock:
        backgroundColor = Colors.red.shade50;
        message = '商品已售罄';
        icon = Icons.error_outline;
        break;
      case CartItemStatus.priceChanged:
        backgroundColor = Colors.orange.shade50;
        message = '价格已变动，请确认';
        icon = Icons.warning_amber;
        break;
      case CartItemStatus.limitExceeded:
        backgroundColor = Colors.blue.shade50;
        message = '超出限购数量';
        icon = Icons.info_outline;
        break;
      case CartItemStatus.offline:
        backgroundColor = Colors.grey.shade50;
        message = '商品已下架';
        icon = Icons.visibility_off;
        break;
      default:
        return const SizedBox.shrink();
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.orange.shade700),
          const SizedBox(width: 8),
          Text(
            message,
            style: TextStyle(
              fontSize: 12,
              color: Colors.orange.shade700,
            ),
          ),
          const Spacer(),
          if (item.status == CartItemStatus.priceChanged)
            TextButton(
              onPressed: () => _confirmPriceChange(item),
              child: const Text('确认', style: TextStyle(fontSize: 12)),
            ),
        ],
      ),
    );
  }

  /// 构建推荐商品
  Widget _buildRecommendedItems(List<Product> recommendedItems) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '为你推荐',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: recommendedItems.length,
              itemBuilder: (context, index) {
                final product = recommendedItems[index];
                return _buildRecommendedItem(product);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建推荐商品项
  Widget _buildRecommendedItem(Product product) {
    return Container(
      width: 100,
      margin: const EdgeInsets.only(right: 12),
      child: Column(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              product.imageUrl,
              width: 80,
              height: 80,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            product.name,
            style: const TextStyle(fontSize: 12),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            '¥${product.price.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomBar(CartState cartState) {
    final selectedItems = cartState.items.where((item) => _selectedItems.contains(item.id)).toList();
    final totalAmount = selectedItems.fold<double>(0, (sum, item) => sum + (item.currentPrice * item.quantity));
    final discountAmount = cartState.selectedCoupon?.calculateDiscount(totalAmount) ?? 0;
    final finalAmount = totalAmount - discountAmount;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (discountAmount > 0) ...[
                    Text(
                      '原价：¥${totalAmount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        decoration: TextDecoration.lineThrough,
                      ),
                    ),
                    Text(
                      '优惠：-¥${discountAmount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.green,
                      ),
                    ),
                  ],
                  Row(
                    children: [
                      const Text('合计：'),
                      Text(
                        '¥${finalAmount.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: selectedItems.isNotEmpty ? () => _proceedToCheckout(selectedItems) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: Text('结算 (${selectedItems.length})'),
            ),
          ],
        ),
      ),
    );
  }

  // 事件处理方法
  void _toggleSelectAll(bool selectAll) {
    setState(() {
      _selectAll = selectAll;
      if (selectAll) {
        _selectedItems.addAll(ref.read(cartProvider).items.map((item) => item.id));
      } else {
        _selectedItems.clear();
      }
    });
  }

  void _toggleItemSelection(String itemId, bool selected) {
    setState(() {
      if (selected) {
        _selectedItems.add(itemId);
      } else {
        _selectedItems.remove(itemId);
      }
      _selectAll = _selectedItems.length == ref.read(cartProvider).items.length;
    });
  }

  void _updateQuantity(String itemId, int newQuantity) {
    ref.read(cartProvider.notifier).updateQuantity(itemId, newQuantity);
  }

  void _showSpecSelector(CartItem item) {
    // 显示规格选择器
    showModalBottomSheet(
      context: context,
      builder: (context) => SpecSelectorSheet(
        product: item.product,
        selectedSpecs: item.selectedSpecs,
        onSpecChanged: (newSpecs) {
          ref.read(cartProvider.notifier).updateSpecs(item.id, newSpecs);
        },
      ),
    );
  }

  void _showCouponSelector(List<Coupon> availableCoupons) {
    showModalBottomSheet(
      context: context,
      builder: (context) => CouponSelectorSheet(
        availableCoupons: availableCoupons,
        onCouponSelected: (coupon) {
          ref.read(cartProvider.notifier).selectCoupon(coupon);
        },
      ),
    );
  }

  void _clearInvalidItems() {
    ref.read(cartProvider.notifier).clearInvalidItems();
  }

  void _confirmPriceChange(CartItem item) {
    ref.read(cartProvider.notifier).confirmPriceChange(item.id);
  }

  void _showCartManagement() {
    // 显示购物车管理选项
  }

  void _proceedToCheckout(List<CartItem> selectedItems) {
    Navigator.of(context).pushNamed('/checkout', arguments: {
      'items': selectedItems,
      'coupon': ref.read(cartProvider).selectedCoupon,
    });
  }

  String _formatSpecs(Map<String, String> specs) {
    return specs.values.join(' ');
  }
}
