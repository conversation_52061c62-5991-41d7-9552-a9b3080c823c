import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:teleshop_app/features/user_growth/presentation/providers/user_growth_provider.dart';
import 'package:teleshop_app/features/user_growth/presentation/widgets/user_growth_widgets.dart';
import 'package:teleshop_app/features/user_growth/domain/entities/user_growth.dart';
import 'package:teleshop_app/widgets/common/loading_widget.dart';
import 'package:teleshop_app/widgets/common/error_widget.dart';

/// 用户成长体系页面
/// 支持用户等级系统、积分获取消费、成就系统、任务系统、邀请奖励等功能
class UserGrowthPage extends ConsumerStatefulWidget {
  const UserGrowthPage({super.key});

  @override
  ConsumerState<UserGrowthPage> createState() => _UserGrowthPageState();
}

class _UserGrowthPageState extends ConsumerState<UserGrowthPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUserGrowthData();
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadUserGrowthData() {
    final notifier = ref.read(userGrowthProvider.notifier);
    notifier.loadUserLevel();
    notifier.loadUserPoints();
    notifier.loadUserAchievements();
    notifier.loadUserTasks();
    notifier.loadUserInvitation();
    notifier.loadGrowthStats();
  }

  @override
  Widget build(BuildContext context) {
    final growthState = ref.watch(userGrowthProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: growthState.isLoading && growthState.isEmpty
            ? const LoadingWidget()
            : growthState.error != null && growthState.isEmpty
                ? ErrorWidget(
                    message: growthState.error!,
                    onRetry: _loadUserGrowthData,
                  )
                : _buildContent(growthState),
      ),
    );
  }

  Widget _buildContent(UserGrowthState state) {
    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          _buildAppBar(state),
          _buildUserLevelHeader(state.userLevel),
        ];
      },
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(state),
          _buildPointsTab(state),
          _buildAchievementsTab(state),
          _buildTasksTab(state),
          _buildInvitationTab(state),
        ],
      ),
    );
  }

  Widget _buildAppBar(UserGrowthState state) {
    return SliverAppBar(
      expandedHeight: 200.h,
      floating: false,
      pinned: true,
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text('成长中心'),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: 20.w,
                top: 60.h,
                child: Icon(
                  Icons.auto_awesome,
                  size: 80.sp,
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
            ],
          ),
        ),
      ),
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        indicatorColor: Colors.white,
        indicatorWeight: 3,
        tabs: const [
          Tab(text: '总览'),
          Tab(text: '积分'),
          Tab(text: '成就'),
          Tab(text: '任务'),
          Tab(text: '邀请'),
        ],
      ),
    );
  }

  Widget _buildUserLevelHeader(UserLevel? userLevel) {
    if (userLevel == null) return const SliverToBoxAdapter(child: SizedBox.shrink());

    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.all(16.w),
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(int.parse('0xFF${userLevel.currentLevelColor.substring(1)}')),
              Color(int.parse('0xFF${userLevel.currentLevelColor.substring(1)}')).withOpacity(0.7),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 60.w,
                  height: 60.h,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(30.r),
                  ),
                  child: Icon(
                    Icons.star,
                    color: Colors.white,
                    size: 32.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userLevel.currentLevelName,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '当前经验值：${userLevel.currentExp}',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14.sp,
                        ),
                      ),
                    ],
                  ),
                ),
                if (userLevel.canUpgrade())
                  ElevatedButton(
                    onPressed: () => _upgradeLevel(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Theme.of(context).primaryColor,
                    ),
                    child: const Text('升级'),
                  ),
              ],
            ),
            SizedBox(height: 16.h),
            if (userLevel.nextLevelName != null) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    userLevel.currentLevelName,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 12.sp,
                    ),
                  ),
                  Text(
                    userLevel.nextLevelName!,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 12.sp,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              LinearProgressIndicator(
                value: userLevel.levelProgress / 100,
                backgroundColor: Colors.white.withOpacity(0.3),
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              SizedBox(height: 8.h),
              Text(
                '距离下一等级还需 ${userLevel.expToNextLevel} 经验值',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 12.sp,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab(UserGrowthState state) {
    return RefreshIndicator(
      onRefresh: () async {
        _loadUserGrowthData();
      },
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 成长统计卡片
            _buildGrowthStatsCard(state.stats),
            
            SizedBox(height: 16.h),
            
            // 快速入口
            _buildQuickActionsCard(),
            
            SizedBox(height: 16.h),
            
            // 最近成就
            _buildRecentAchievementsCard(state.achievements.take(3).toList()),
            
            SizedBox(height: 16.h),
            
            // 进行中的任务
            _buildActiveTasksCard(state.tasks.where((task) => task.status == 'IN_PROGRESS').take(3).toList()),
            
            SizedBox(height: 16.h),
            
            // 成长轨迹
            _buildGrowthTimelineCard(state.timeline),
          ],
        ),
      ),
    );
  }

  Widget _buildGrowthStatsCard(UserGrowthStats? stats) {
    if (stats == null) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '成长统计',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  '总积分',
                  '${stats.totalPoints}',
                  Icons.stars,
                  Colors.orange,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '成就数',
                  '${stats.achievementCount}',
                  Icons.emoji_events,
                  Colors.purple,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '完成任务',
                  '${stats.completedTasks}',
                  Icons.task_alt,
                  Colors.green,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '邀请好友',
                  '${stats.invitedFriends}',
                  Icons.people,
                  Colors.blue,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          width: 40.w,
          height: 40.h,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Icon(icon, color: color, size: 20.sp),
        ),
        SizedBox(height: 8.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionsCard() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快速操作',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  '签到',
                  Icons.today,
                  Colors.green,
                  () => _dailyCheckin(),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildQuickActionButton(
                  '做任务',
                  Icons.assignment,
                  Colors.blue,
                  () => _tabController.animateTo(3),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildQuickActionButton(
                  '邀请好友',
                  Icons.share,
                  Colors.purple,
                  () => _tabController.animateTo(4),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildQuickActionButton(
                  '积分商城',
                  Icons.store,
                  Colors.orange,
                  () => _goToPointsMall(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(String label, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24.sp),
            SizedBox(height: 4.h),
            Text(
              label,
              style: TextStyle(
                fontSize: 12.sp,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentAchievementsCard(List<UserAchievement> achievements) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '最近成就',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => _tabController.animateTo(2),
                child: const Text('查看全部'),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          if (achievements.isEmpty)
            Center(
              child: Text(
                '暂无成就，快去完成任务解锁吧！',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
              ),
            )
          else
            ...achievements.map((achievement) => AchievementListItem(
              achievement: achievement,
              onTap: () => _viewAchievementDetail(achievement),
            )),
        ],
      ),
    );
  }

  Widget _buildActiveTasksCard(List<UserTask> tasks) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '进行中的任务',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => _tabController.animateTo(3),
                child: const Text('查看全部'),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          if (tasks.isEmpty)
            Center(
              child: Text(
                '暂无进行中的任务',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
              ),
            )
          else
            ...tasks.map((task) => TaskListItem(
              task: task,
              onTap: () => _viewTaskDetail(task),
              onComplete: () => _completeTask(task),
            )),
        ],
      ),
    );
  }

  Widget _buildGrowthTimelineCard(List<GrowthTimeline> timeline) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '成长轨迹',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          if (timeline.isEmpty)
            Center(
              child: Text(
                '暂无成长记录',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
              ),
            )
          else
            GrowthTimelineWidget(timeline: timeline.take(5).toList()),
        ],
      ),
    );
  }

  Widget _buildPointsTab(UserGrowthState state) {
    return PointsTabWidget(
      userPoints: state.userPoints,
      pointsTransactions: state.pointsTransactions,
      pointsRules: state.pointsRules,
      onRefresh: () => ref.read(userGrowthProvider.notifier).loadUserPoints(),
      onExchangePoints: (productId) => _exchangePoints(productId),
    );
  }

  Widget _buildAchievementsTab(UserGrowthState state) {
    return AchievementsTabWidget(
      achievements: state.achievements,
      achievementConfigs: state.achievementConfigs,
      onRefresh: () => ref.read(userGrowthProvider.notifier).loadUserAchievements(),
      onClaimReward: (achievementId) => _claimAchievementReward(achievementId),
    );
  }

  Widget _buildTasksTab(UserGrowthState state) {
    return TasksTabWidget(
      tasks: state.tasks,
      taskConfigs: state.taskConfigs,
      onRefresh: () => ref.read(userGrowthProvider.notifier).loadUserTasks(),
      onCompleteTask: (taskId) => _completeTask(taskId),
      onClaimReward: (taskId) => _claimTaskReward(taskId),
    );
  }

  Widget _buildInvitationTab(UserGrowthState state) {
    return InvitationTabWidget(
      invitation: state.invitation,
      invitationRecords: state.invitationRecords,
      onRefresh: () => ref.read(userGrowthProvider.notifier).loadUserInvitation(),
      onGenerateCode: () => _generateInvitationCode(),
      onShareInvitation: () => _shareInvitation(),
    );
  }

  // 事件处理方法
  void _upgradeLevel() {
    ref.read(userGrowthProvider.notifier).upgradeUserLevel();
  }

  void _dailyCheckin() {
    ref.read(userGrowthProvider.notifier).dailyCheckin();
  }

  void _goToPointsMall() {
    Navigator.of(context).pushNamed('/points-mall');
  }

  void _viewAchievementDetail(UserAchievement achievement) {
    Navigator.of(context).pushNamed('/achievement-detail', arguments: achievement);
  }

  void _viewTaskDetail(UserTask task) {
    Navigator.of(context).pushNamed('/task-detail', arguments: task);
  }

  void _completeTask(dynamic task) {
    String taskId = task is UserTask ? task.taskId : task.toString();
    ref.read(userGrowthProvider.notifier).completeTask(taskId);
  }

  void _exchangePoints(String productId) {
    ref.read(userGrowthProvider.notifier).exchangePoints(productId);
  }

  void _claimAchievementReward(String achievementId) {
    ref.read(userGrowthProvider.notifier).claimAchievementReward(achievementId);
  }

  void _claimTaskReward(String taskId) {
    ref.read(userGrowthProvider.notifier).claimTaskReward(taskId);
  }

  void _generateInvitationCode() {
    ref.read(userGrowthProvider.notifier).generateInvitationCode();
  }

  void _shareInvitation() {
    ref.read(userGrowthProvider.notifier).shareInvitation();
  }
}
