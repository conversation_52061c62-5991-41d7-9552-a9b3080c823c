import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:teleshop_app/core/websocket/websocket_service.dart';
import 'package:teleshop_app/core/websocket/websocket_models.dart';
import 'package:teleshop_app/features/customer_service/customer_service_provider.dart';
import 'package:teleshop_app/features/customer_service/models/customer_service_models.dart';
import 'package:teleshop_app/widgets/common/loading_widget.dart';
import 'package:teleshop_app/widgets/common/error_widget.dart';

/// 智能客服页面
/// 提供AI客服聊天功能，支持文本、图片、语音等多种交互方式
class CustomerServicePage extends ConsumerStatefulWidget {
  const CustomerServicePage({super.key});

  @override
  ConsumerState<CustomerServicePage> createState() => _CustomerServicePageState();
}

class _CustomerServicePageState extends ConsumerState<CustomerServicePage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    // 初始化客服会话
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(customerServiceProvider.notifier).initializeSession();
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customerServiceState = ref.watch(customerServiceProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('智能客服'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // 客服状态指示器
          _buildServiceStatusIndicator(customerServiceState),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        children: [
          // 客服信息栏
          _buildServiceInfoBar(customerServiceState),
          
          // 消息列表
          Expanded(
            child: _buildMessageList(customerServiceState),
          ),
          
          // 输入栏
          _buildInputArea(customerServiceState),
        ],
      ),
    );
  }

  /// 构建客服状态指示器
  Widget _buildServiceStatusIndicator(CustomerServiceState state) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(state.serviceStatus),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getStatusIcon(state.serviceStatus),
            size: 12,
            color: Colors.white,
          ),
          const SizedBox(width: 4),
          Text(
            _getStatusText(state.serviceStatus),
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建客服信息栏
  Widget _buildServiceInfoBar(CustomerServiceState state) {
    if (state.currentAgent != null) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blue.shade50,
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade200),
          ),
        ),
        child: Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundImage: state.currentAgent!.avatar != null
                  ? NetworkImage(state.currentAgent!.avatar!)
                  : null,
              child: state.currentAgent!.avatar == null
                  ? const Icon(Icons.person, size: 16)
                  : null,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    state.currentAgent!.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    '${state.currentAgent!.title} • 在线',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            if (state.serviceType == ServiceType.human)
              IconButton(
                icon: const Icon(Icons.phone, size: 20),
                onPressed: () => _makePhoneCall(state.currentAgent!.phone),
              ),
          ],
        ),
      );
    }
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.smart_toy, color: Colors.green.shade600),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'TeleShop智能助手',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  '我是您的专属购物助手，有什么可以帮您的吗？',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () => _requestHumanService(),
            child: const Text('转人工', style: TextStyle(fontSize: 12)),
          ),
        ],
      ),
    );
  }

  /// 构建消息列表
  Widget _buildMessageList(CustomerServiceState state) {
    if (state.isLoading) {
      return const LoadingWidget();
    }
    
    if (state.error != null) {
      return ErrorWidget(
        message: state.error!,
        onRetry: () => ref.read(customerServiceProvider.notifier).retryConnection(),
      );
    }
    
    if (state.messages.isEmpty) {
      return _buildEmptyState();
    }
    
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: state.messages.length,
      itemBuilder: (context, index) {
        final message = state.messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '开始对话',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '有任何问题都可以问我哦～',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildQuickReplyChip('查看订单状态'),
              _buildQuickReplyChip('退换货政策'),
              _buildQuickReplyChip('支付问题'),
              _buildQuickReplyChip('商品咨询'),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建快速回复按钮
  Widget _buildQuickReplyChip(String text) {
    return ActionChip(
      label: Text(text),
      onPressed: () => _sendQuickReply(text),
      backgroundColor: Colors.blue.shade50,
      labelStyle: TextStyle(color: Colors.blue.shade700),
    );
  }

  /// 构建消息气泡
  Widget _buildMessageBubble(CustomerServiceMessage message) {
    final isUser = message.senderType == MessageSenderType.user;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            _buildAvatar(message.senderType),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Column(
              crossAxisAlignment: isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: isUser ? Theme.of(context).primaryColor : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(20).copyWith(
                      bottomRight: isUser ? const Radius.circular(4) : null,
                      bottomLeft: !isUser ? const Radius.circular(4) : null,
                    ),
                  ),
                  child: _buildMessageContent(message, isUser),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _formatTime(message.timestamp),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                    if (isUser && message.status != null) ...[
                      const SizedBox(width: 4),
                      Icon(
                        _getMessageStatusIcon(message.status!),
                        size: 12,
                        color: Colors.grey.shade500,
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            _buildAvatar(message.senderType),
          ],
        ],
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar(MessageSenderType senderType) {
    return CircleAvatar(
      radius: 16,
      backgroundColor: senderType == MessageSenderType.user
          ? Theme.of(context).primaryColor
          : Colors.green.shade400,
      child: Icon(
        senderType == MessageSenderType.user ? Icons.person : Icons.smart_toy,
        size: 16,
        color: Colors.white,
      ),
    );
  }

  /// 构建消息内容
  Widget _buildMessageContent(CustomerServiceMessage message, bool isUser) {
    switch (message.messageType) {
      case CustomerServiceMessageType.text:
        return Text(
          message.content,
          style: TextStyle(
            color: isUser ? Colors.white : Colors.black87,
            fontSize: 16,
          ),
        );
      case CustomerServiceMessageType.image:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                message.content,
                width: 200,
                fit: BoxFit.cover,
              ),
            ),
            if (message.caption != null) ...[
              const SizedBox(height: 8),
              Text(
                message.caption!,
                style: TextStyle(
                  color: isUser ? Colors.white : Colors.black87,
                  fontSize: 14,
                ),
              ),
            ],
          ],
        );
      case CustomerServiceMessageType.quickReplies:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.content,
              style: TextStyle(
                color: isUser ? Colors.white : Colors.black87,
                fontSize: 16,
              ),
            ),
            if (message.quickReplies != null) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: message.quickReplies!.map((reply) {
                  return ActionChip(
                    label: Text(reply),
                    onPressed: () => _sendQuickReply(reply),
                    backgroundColor: Colors.white,
                    labelStyle: TextStyle(color: Theme.of(context).primaryColor),
                  );
                }).toList(),
              ),
            ],
          ],
        );
      default:
        return Text(
          message.content,
          style: TextStyle(
            color: isUser ? Colors.white : Colors.black87,
            fontSize: 16,
          ),
        );
    }
  }

  /// 构建输入区域
  Widget _buildInputArea(CustomerServiceState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          // 附件按钮
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAttachmentOptions(),
          ),
          
          // 输入框
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: '输入消息...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // 发送按钮
          IconButton(
            icon: Icon(
              Icons.send,
              color: Theme.of(context).primaryColor,
            ),
            onPressed: state.isSending ? null : _sendMessage,
          ),
        ],
      ),
    );
  }

  /// 发送消息
  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;
    
    ref.read(customerServiceProvider.notifier).sendMessage(text);
    _messageController.clear();
    
    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  /// 发送快速回复
  void _sendQuickReply(String text) {
    ref.read(customerServiceProvider.notifier).sendMessage(text);
    
    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  /// 滚动到底部
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  /// 显示附件选项
  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo),
              title: const Text('相册'),
              onTap: () {
                Navigator.pop(context);
                _pickImage();
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('拍照'),
              onTap: () {
                Navigator.pop(context);
                _takePhoto();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 选择图片
  void _pickImage() {
    // 实现图片选择逻辑
  }

  /// 拍照
  void _takePhoto() {
    // 实现拍照逻辑
  }

  /// 请求人工客服
  void _requestHumanService() {
    ref.read(customerServiceProvider.notifier).requestHumanService();
  }

  /// 拨打电话
  void _makePhoneCall(String? phone) {
    if (phone != null) {
      // 实现拨打电话逻辑
    }
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else {
      return '${time.month}/${time.day} ${time.hour}:${time.minute.toString().padLeft(2, '0')}';
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(ServiceStatus status) {
    switch (status) {
      case ServiceStatus.online:
        return Colors.green;
      case ServiceStatus.busy:
        return Colors.orange;
      case ServiceStatus.offline:
        return Colors.grey;
    }
  }

  /// 获取状态图标
  IconData _getStatusIcon(ServiceStatus status) {
    switch (status) {
      case ServiceStatus.online:
        return Icons.circle;
      case ServiceStatus.busy:
        return Icons.schedule;
      case ServiceStatus.offline:
        return Icons.circle_outlined;
    }
  }

  /// 获取状态文本
  String _getStatusText(ServiceStatus status) {
    switch (status) {
      case ServiceStatus.online:
        return '在线';
      case ServiceStatus.busy:
        return '繁忙';
      case ServiceStatus.offline:
        return '离线';
    }
  }

  /// 获取消息状态图标
  IconData _getMessageStatusIcon(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return Icons.schedule;
      case MessageStatus.sent:
        return Icons.check;
      case MessageStatus.delivered:
        return Icons.done_all;
      case MessageStatus.read:
        return Icons.done_all;
      case MessageStatus.failed:
        return Icons.error_outline;
    }
  }
}
