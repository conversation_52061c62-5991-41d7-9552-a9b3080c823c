import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../auth/auth_models.dart';

part 'secure_storage.g.dart';

/// 安全存储服务
/// 使用Flutter Secure Storage存储敏感信息
@riverpod
SecureStorage secureStorage(SecureStorageRef ref) {
  return SecureStorage();
}

class SecureStorage {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'teleshop_secure_prefs',
      preferencesKeyPrefix: 'teleshop_',
    ),
    iOptions: IOSOptions(
      groupId: 'group.com.teleshop.app',
      accountName: 'TeleShop',
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  
  // 存储键常量
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userInfoKey = 'user_info';
  static const String _deviceIdKey = 'device_id';
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _autoLoginKey = 'auto_login';
  static const String _languageKey = 'language';
  static const String _themeKey = 'theme';
  
  /// 保存访问令牌
  Future<void> saveAccessToken(String token) async {
    await _storage.write(key: _accessTokenKey, value: token);
  }
  
  /// 获取访问令牌
  Future<String?> getAccessToken() async {
    return await _storage.read(key: _accessTokenKey);
  }
  
  /// 保存刷新令牌
  Future<void> saveRefreshToken(String token) async {
    await _storage.write(key: _refreshTokenKey, value: token);
  }
  
  /// 获取刷新令牌
  Future<String?> getRefreshToken() async {
    return await _storage.read(key: _refreshTokenKey);
  }
  
  /// 保存用户信息
  Future<void> saveUserInfo(UserInfo userInfo) async {
    final jsonString = jsonEncode(userInfo.toJson());
    await _storage.write(key: _userInfoKey, value: jsonString);
  }
  
  /// 获取用户信息
  Future<UserInfo?> getUserInfo() async {
    final jsonString = await _storage.read(key: _userInfoKey);
    if (jsonString != null) {
      try {
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        return UserInfo.fromJson(json);
      } catch (e) {
        // 解析失败，删除无效数据
        await _storage.delete(key: _userInfoKey);
        return null;
      }
    }
    return null;
  }
  
  /// 保存设备ID
  Future<void> saveDeviceId(String deviceId) async {
    await _storage.write(key: _deviceIdKey, value: deviceId);
  }
  
  /// 获取设备ID
  Future<String?> getDeviceId() async {
    return await _storage.read(key: _deviceIdKey);
  }
  
  /// 设置生物识别启用状态
  Future<void> setBiometricEnabled(bool enabled) async {
    await _storage.write(key: _biometricEnabledKey, value: enabled.toString());
  }
  
  /// 获取生物识别启用状态
  Future<bool> isBiometricEnabled() async {
    final value = await _storage.read(key: _biometricEnabledKey);
    return value == 'true';
  }
  
  /// 设置自动登录状态
  Future<void> setAutoLogin(bool enabled) async {
    await _storage.write(key: _autoLoginKey, value: enabled.toString());
  }
  
  /// 获取自动登录状态
  Future<bool> isAutoLoginEnabled() async {
    final value = await _storage.read(key: _autoLoginKey);
    return value == 'true';
  }
  
  /// 保存语言设置
  Future<void> saveLanguage(String language) async {
    await _storage.write(key: _languageKey, value: language);
  }
  
  /// 获取语言设置
  Future<String?> getLanguage() async {
    return await _storage.read(key: _languageKey);
  }
  
  /// 保存主题设置
  Future<void> saveTheme(String theme) async {
    await _storage.write(key: _themeKey, value: theme);
  }
  
  /// 获取主题设置
  Future<String?> getTheme() async {
    return await _storage.read(key: _themeKey);
  }
  
  /// 检查是否有有效的认证令牌
  Future<bool> hasValidTokens() async {
    final accessToken = await getAccessToken();
    final refreshToken = await getRefreshToken();
    return accessToken != null && refreshToken != null;
  }
  
  /// 清除认证相关数据
  Future<void> clearAuthData() async {
    await Future.wait([
      _storage.delete(key: _accessTokenKey),
      _storage.delete(key: _refreshTokenKey),
      _storage.delete(key: _userInfoKey),
    ]);
  }
  
  /// 清除所有数据
  Future<void> clearAll() async {
    await _storage.deleteAll();
  }
  
  /// 获取所有存储的键
  Future<Map<String, String>> getAllData() async {
    return await _storage.readAll();
  }
  
  /// 检查特定键是否存在
  Future<bool> containsKey(String key) async {
    return await _storage.containsKey(key: key);
  }
  
  /// 批量保存数据
  Future<void> saveMultiple(Map<String, String> data) async {
    for (final entry in data.entries) {
      await _storage.write(key: entry.key, value: entry.value);
    }
  }
  
  /// 批量删除数据
  Future<void> deleteMultiple(List<String> keys) async {
    for (final key in keys) {
      await _storage.delete(key: key);
    }
  }
}

/// 存储异常类
class StorageException implements Exception {
  final String message;
  final dynamic originalError;
  
  const StorageException(this.message, [this.originalError]);
  
  @override
  String toString() {
    if (originalError != null) {
      return 'StorageException: $message (原因: $originalError)';
    }
    return 'StorageException: $message';
  }
}

/// 存储工具类
class StorageUtils {
  /// 加密字符串
  static String encryptString(String value, String key) {
    // 这里可以实现自定义加密逻辑
    // 目前Flutter Secure Storage已经提供了加密功能
    return value;
  }
  
  /// 解密字符串
  static String decryptString(String encryptedValue, String key) {
    // 这里可以实现自定义解密逻辑
    return encryptedValue;
  }
  
  /// 验证JSON格式
  static bool isValidJson(String jsonString) {
    try {
      jsonDecode(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// 安全的JSON解析
  static Map<String, dynamic>? safeJsonDecode(String jsonString) {
    try {
      final decoded = jsonDecode(jsonString);
      if (decoded is Map<String, dynamic>) {
        return decoded;
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  
  /// 生成存储键
  static String generateKey(String prefix, String suffix) {
    return '${prefix}_$suffix';
  }
  
  /// 验证存储值
  static bool validateStorageValue(String? value, {int? maxLength}) {
    if (value == null || value.isEmpty) {
      return false;
    }
    
    if (maxLength != null && value.length > maxLength) {
      return false;
    }
    
    return true;
  }
}
