import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_models.freezed.dart';
part 'auth_models.g.dart';

/// 登录请求模型
@freezed
class LoginRequest with _$LoginRequest {
  const factory LoginRequest({
    required String username,
    required String password,
    String? captcha,
    String? captchaId,
    @Default(LoginType.username) LoginType loginType,
    @Default(false) bool rememberMe,
    String? clientId,
  }) = _LoginRequest;
  
  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);
}

/// 登录类型枚举
enum LoginType {
  @JsonValue('USERNAME')
  username,
  @JsonValue('EMAIL')
  email,
  @JsonValue('PHONE')
  phone,
}

/// 令牌信息模型
@freezed
class TokenInfo with _$TokenInfo {
  const factory TokenInfo({
    required String accessToken,
    required String refreshToken,
    @Default('Bearer') String tokenType,
    required int expiresIn,
    String? scope,
    UserInfo? userInfo,
    DateTime? issuedAt,
  }) = _TokenInfo;
  
  factory TokenInfo.fromJson(Map<String, dynamic> json) =>
      _$TokenInfoFromJson(json);
}

/// 用户信息模型
@freezed
class UserInfo with _$UserInfo {
  const factory UserInfo({
    required int userId,
    required String username,
    String? email,
    String? phone,
    required String userType,
    required String status,
    @Default([]) List<String> roles,
    @Default([]) List<String> permissions,
    @Default(false) bool emailVerified,
    @Default(false) bool phoneVerified,
    @Default(false) bool twoFactorEnabled,
    DateTime? lastLoginTime,
  }) = _UserInfo;
  
  factory UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);
}

/// 用户注册请求模型
@freezed
class RegisterRequest with _$RegisterRequest {
  const factory RegisterRequest({
    required String username,
    required String password,
    required String confirmPassword,
    String? email,
    String? phone,
    String? emailCode,
    String? phoneCode,
    required bool agreeTerms,
    String? inviteCode,
  }) = _RegisterRequest;
  
  factory RegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestFromJson(json);
}

/// 密码重置请求模型
@freezed
class ResetPasswordRequest with _$ResetPasswordRequest {
  const factory ResetPasswordRequest({
    String? email,
    String? phone,
    required String verificationCode,
    required String newPassword,
    required String confirmPassword,
  }) = _ResetPasswordRequest;
  
  factory ResetPasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$ResetPasswordRequestFromJson(json);
}

/// 修改密码请求模型
@freezed
class ChangePasswordRequest with _$ChangePasswordRequest {
  const factory ChangePasswordRequest({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) = _ChangePasswordRequest;
  
  factory ChangePasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$ChangePasswordRequestFromJson(json);
}

/// 验证码请求模型
@freezed
class VerificationCodeRequest with _$VerificationCodeRequest {
  const factory VerificationCodeRequest({
    String? email,
    String? phone,
    required VerificationCodeType type,
  }) = _VerificationCodeRequest;
  
  factory VerificationCodeRequest.fromJson(Map<String, dynamic> json) =>
      _$VerificationCodeRequestFromJson(json);
}

/// 验证码类型枚举
enum VerificationCodeType {
  @JsonValue('REGISTER')
  register,
  @JsonValue('RESET_PASSWORD')
  resetPassword,
  @JsonValue('VERIFY_EMAIL')
  verifyEmail,
  @JsonValue('VERIFY_PHONE')
  verifyPhone,
  @JsonValue('LOGIN')
  login,
}

/// 用户状态枚举
enum UserStatus {
  @JsonValue('ACTIVE')
  active,
  @JsonValue('DISABLED')
  disabled,
  @JsonValue('DELETED')
  deleted,
  @JsonValue('PENDING_ACTIVATION')
  pendingActivation,
}

/// 用户类型枚举
enum UserType {
  @JsonValue('REGULAR_USER')
  regularUser,
  @JsonValue('MERCHANT_USER')
  merchantUser,
  @JsonValue('ADMIN_USER')
  adminUser,
}

/// 令牌验证响应模型
@freezed
class TokenValidationResponse with _$TokenValidationResponse {
  const factory TokenValidationResponse({
    required bool valid,
    int? userId,
    String? username,
    String? userType,
    @Default([]) List<String> roles,
    @Default([]) List<String> permissions,
    DateTime? expiresAt,
    @Default(false) bool expiringSoon,
    String? errorMessage,
  }) = _TokenValidationResponse;
  
  factory TokenValidationResponse.fromJson(Map<String, dynamic> json) =>
      _$TokenValidationResponseFromJson(json);
}

/// API响应基础模型
@freezed
class ApiResponse<T> with _$ApiResponse<T> {
  const factory ApiResponse({
    required int code,
    required String message,
    T? data,
    DateTime? timestamp,
    String? traceId,
  }) = _ApiResponse<T>;
  
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) => _$ApiResponseFromJson(json, fromJsonT);
}

/// 分页响应模型
@freezed
class PageResponse<T> with _$PageResponse<T> {
  const factory PageResponse({
    @Default([]) List<T> content,
    @Default(0) int page,
    @Default(20) int size,
    @Default(0) int totalElements,
    @Default(0) int totalPages,
    @Default(true) bool first,
    @Default(true) bool last,
  }) = _PageResponse<T>;
  
  factory PageResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) => _$PageResponseFromJson(json, fromJsonT);
}

/// 认证配置模型
@freezed
class AuthConfig with _$AuthConfig {
  const factory AuthConfig({
    @Default('https://api.teleshop.com') String baseUrl,
    @Default('/api/v1') String apiVersion,
    @Default(30000) int connectTimeout,
    @Default(30000) int receiveTimeout,
    @Default(30000) int sendTimeout,
    @Default(true) bool enableLogging,
    @Default('teleshop-app') String clientId,
    String? clientSecret,
  }) = _AuthConfig;
  
  factory AuthConfig.fromJson(Map<String, dynamic> json) =>
      _$AuthConfigFromJson(json);
}
