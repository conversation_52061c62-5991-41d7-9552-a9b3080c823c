import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../network/api_client.dart';
import '../storage/secure_storage.dart';
import 'auth_models.dart';

part 'auth_service.g.dart';

/// 统一身份认证服务
/// 负责与认证服务器的交互，包括登录、令牌刷新、登出等
@riverpod
class AuthService extends _$AuthService {
  late final ApiClient _apiClient;
  late final SecureStorage _secureStorage;
  
  @override
  Future<AuthState> build() async {
    _apiClient = ref.read(apiClientProvider);
    _secureStorage = ref.read(secureStorageProvider);
    
    // 启动时检查本地存储的令牌
    return await _checkStoredToken();
  }
  
  /// 用户登录
  Future<void> login(LoginRequest request) async {
    try {
      state = const AsyncValue.loading();
      
      final response = await _apiClient.post(
        '/auth/login',
        data: request.toJson(),
      );
      
      if (response.data['code'] == 200) {
        final tokenInfo = TokenInfo.fromJson(response.data['data']);
        await _saveTokens(tokenInfo);
        
        state = AsyncValue.data(AuthState.authenticated(
          user: tokenInfo.userInfo!,
          tokenInfo: tokenInfo,
        ));
      } else {
        throw AuthException(response.data['message'] ?? '登录失败');
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }
  
  /// 刷新令牌
  Future<void> refreshToken() async {
    try {
      final refreshToken = await _secureStorage.getRefreshToken();
      if (refreshToken == null) {
        throw AuthException('刷新令牌不存在');
      }
      
      final response = await _apiClient.post(
        '/auth/refresh',
        data: {'refreshToken': refreshToken},
      );
      
      if (response.data['code'] == 200) {
        final tokenInfo = TokenInfo.fromJson(response.data['data']);
        await _saveTokens(tokenInfo);
        
        // 更新当前状态
        final currentState = state.value;
        if (currentState != null && currentState.isAuthenticated) {
          state = AsyncValue.data(currentState.copyWith(
            tokenInfo: tokenInfo,
          ));
        }
      } else {
        throw AuthException('令牌刷新失败');
      }
    } catch (e) {
      // 刷新失败，清除本地令牌并跳转到登录页
      await logout();
      rethrow;
    }
  }
  
  /// 用户登出
  Future<void> logout() async {
    try {
      final accessToken = await _secureStorage.getAccessToken();
      if (accessToken != null) {
        // 尝试撤销令牌（即使失败也继续登出流程）
        try {
          await _apiClient.post(
            '/auth/logout',
            options: Options(
              headers: {'Authorization': 'Bearer $accessToken'},
            ),
          );
        } catch (e) {
          // 忽略撤销令牌的错误
        }
      }
    } finally {
      // 清除本地存储的令牌
      await _clearTokens();
      state = const AsyncValue.data(AuthState.unauthenticated());
    }
  }
  
  /// 检查用户名是否可用
  Future<bool> checkUsernameAvailable(String username) async {
    try {
      final response = await _apiClient.get(
        '/auth/check-username',
        queryParameters: {'username': username},
      );
      
      return response.data['data'] == true;
    } catch (e) {
      return false;
    }
  }
  
  /// 检查邮箱是否可用
  Future<bool> checkEmailAvailable(String email) async {
    try {
      final response = await _apiClient.get(
        '/auth/check-email',
        queryParameters: {'email': email},
      );
      
      return response.data['data'] == true;
    } catch (e) {
      return false;
    }
  }
  
  /// 检查手机号是否可用
  Future<bool> checkPhoneAvailable(String phone) async {
    try {
      final response = await _apiClient.get(
        '/auth/check-phone',
        queryParameters: {'phone': phone},
      );
      
      return response.data['data'] == true;
    } catch (e) {
      return false;
    }
  }
  
  /// 获取当前用户信息
  Future<UserInfo> getCurrentUser() async {
    try {
      final accessToken = await _secureStorage.getAccessToken();
      if (accessToken == null) {
        throw AuthException('访问令牌不存在');
      }
      
      final response = await _apiClient.get(
        '/auth/me',
        options: Options(
          headers: {'Authorization': 'Bearer $accessToken'},
        ),
      );
      
      if (response.data['code'] == 200) {
        return UserInfo.fromJson(response.data['data']);
      } else {
        throw AuthException(response.data['message'] ?? '获取用户信息失败');
      }
    } catch (e) {
      rethrow;
    }
  }
  
  /// 检查本地存储的令牌
  Future<AuthState> _checkStoredToken() async {
    try {
      final accessToken = await _secureStorage.getAccessToken();
      final refreshToken = await _secureStorage.getRefreshToken();
      
      if (accessToken == null || refreshToken == null) {
        return const AuthState.unauthenticated();
      }
      
      // 验证令牌有效性
      final response = await _apiClient.post(
        '/auth/validate',
        queryParameters: {'token': accessToken},
      );
      
      if (response.data['data']['valid'] == true) {
        // 令牌有效，获取用户信息
        final userInfo = await getCurrentUser();
        final tokenInfo = TokenInfo(
          accessToken: accessToken,
          refreshToken: refreshToken,
          tokenType: 'Bearer',
          expiresIn: 3600,
          userInfo: userInfo,
        );
        
        return AuthState.authenticated(
          user: userInfo,
          tokenInfo: tokenInfo,
        );
      } else {
        // 令牌无效，尝试刷新
        try {
          await refreshToken();
          final userInfo = await getCurrentUser();
          final newAccessToken = await _secureStorage.getAccessToken();
          final tokenInfo = TokenInfo(
            accessToken: newAccessToken!,
            refreshToken: refreshToken,
            tokenType: 'Bearer',
            expiresIn: 3600,
            userInfo: userInfo,
          );
          
          return AuthState.authenticated(
            user: userInfo,
            tokenInfo: tokenInfo,
          );
        } catch (e) {
          // 刷新失败，清除令牌
          await _clearTokens();
          return const AuthState.unauthenticated();
        }
      }
    } catch (e) {
      // 检查失败，清除令牌
      await _clearTokens();
      return const AuthState.unauthenticated();
    }
  }
  
  /// 保存令牌到安全存储
  Future<void> _saveTokens(TokenInfo tokenInfo) async {
    await _secureStorage.saveAccessToken(tokenInfo.accessToken);
    await _secureStorage.saveRefreshToken(tokenInfo.refreshToken);
    
    // 保存用户信息
    if (tokenInfo.userInfo != null) {
      await _secureStorage.saveUserInfo(tokenInfo.userInfo!);
    }
  }
  
  /// 清除本地存储的令牌
  Future<void> _clearTokens() async {
    await _secureStorage.clearAll();
  }
}

/// 认证异常类
class AuthException implements Exception {
  final String message;
  
  const AuthException(this.message);
  
  @override
  String toString() => 'AuthException: $message';
}

/// 认证状态
class AuthState {
  final bool isAuthenticated;
  final UserInfo? user;
  final TokenInfo? tokenInfo;
  final String? error;
  
  const AuthState._({
    required this.isAuthenticated,
    this.user,
    this.tokenInfo,
    this.error,
  });
  
  const AuthState.authenticated({
    required UserInfo user,
    required TokenInfo tokenInfo,
  }) : this._(
    isAuthenticated: true,
    user: user,
    tokenInfo: tokenInfo,
  );
  
  const AuthState.unauthenticated({String? error}) : this._(
    isAuthenticated: false,
    error: error,
  );
  
  AuthState copyWith({
    bool? isAuthenticated,
    UserInfo? user,
    TokenInfo? tokenInfo,
    String? error,
  }) {
    return AuthState._(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: user ?? this.user,
      tokenInfo: tokenInfo ?? this.tokenInfo,
      error: error ?? this.error,
    );
  }
}
