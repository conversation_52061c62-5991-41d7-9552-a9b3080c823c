import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../auth/auth_service.dart';
import '../storage/secure_storage.dart';
import 'websocket_models.dart';

part 'websocket_service.g.dart';

/// WebSocket服务
/// 负责与消息服务器的WebSocket连接管理
@riverpod
class WebSocketService extends _$WebSocketService {
  WebSocketChannel? _channel;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  StreamController<WebSocketMessage>? _messageController;
  
  // 连接状态
  bool _isConnected = false;
  bool _isConnecting = false;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  
  // 配置参数
  static const int _maxReconnectAttempts = 5;
  static const int _heartbeatInterval = 30; // 秒
  static const int _reconnectDelay = 5; // 秒
  
  @override
  WebSocketConnectionState build() {
    return const WebSocketConnectionState.disconnected();
  }
  
  /// 连接WebSocket
  Future<void> connect() async {
    if (_isConnected || _isConnecting) {
      return;
    }
    
    try {
      _isConnecting = true;
      state = const WebSocketConnectionState.connecting();
      
      // 获取用户认证信息
      final authState = await ref.read(authServiceProvider.future);
      if (!authState.isAuthenticated || authState.user == null) {
        throw WebSocketException('用户未认证');
      }
      
      final userId = authState.user!.userId;
      final accessToken = authState.tokenInfo?.accessToken;
      
      if (accessToken == null) {
        throw WebSocketException('访问令牌不存在');
      }
      
      // 构建WebSocket URL
      final wsUrl = _buildWebSocketUrl(userId, accessToken);
      
      // 建立WebSocket连接
      _channel = WebSocketChannel.connect(
        Uri.parse(wsUrl),
        protocols: ['teleshop-protocol'],
      );
      
      // 初始化消息流控制器
      _messageController = StreamController<WebSocketMessage>.broadcast();
      
      // 监听消息
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
        cancelOnError: false,
      );
      
      // 标记为已连接
      _isConnected = true;
      _isConnecting = false;
      _reconnectAttempts = 0;
      
      // 启动心跳
      _startHeartbeat();
      
      // 更新状态
      state = WebSocketConnectionState.connected(
        userId: userId,
        connectedAt: DateTime.now(),
      );
      
      print('WebSocket连接成功: userId=$userId');
      
    } catch (e) {
      _isConnecting = false;
      state = WebSocketConnectionState.error(e.toString());
      
      print('WebSocket连接失败: $e');
      
      // 自动重连
      if (_shouldReconnect) {
        _scheduleReconnect();
      }
      
      rethrow;
    }
  }
  
  /// 断开WebSocket连接
  Future<void> disconnect() async {
    _shouldReconnect = false;
    
    // 停止心跳和重连定时器
    _heartbeatTimer?.cancel();
    _reconnectTimer?.cancel();
    
    // 关闭WebSocket连接
    if (_channel != null) {
      await _channel!.sink.close(status.normalClosure);
      _channel = null;
    }
    
    // 关闭消息流
    await _messageController?.close();
    _messageController = null;
    
    // 重置状态
    _isConnected = false;
    _isConnecting = false;
    _reconnectAttempts = 0;
    
    state = const WebSocketConnectionState.disconnected();
    
    print('WebSocket连接已断开');
  }
  
  /// 发送消息
  Future<void> sendMessage(WebSocketMessage message) async {
    if (!_isConnected || _channel == null) {
      throw WebSocketException('WebSocket未连接');
    }
    
    try {
      final messageJson = jsonEncode(message.toJson());
      _channel!.sink.add(messageJson);
      
      print('WebSocket消息发送成功: ${message.messageId}');
      
    } catch (e) {
      print('WebSocket消息发送失败: $e');
      throw WebSocketException('消息发送失败: $e');
    }
  }
  
  /// 发送文本消息
  Future<void> sendTextMessage(String content, {
    int? receiverId,
    String? conversationId,
  }) async {
    final message = WebSocketMessage(
      messageId: _generateMessageId(),
      messageType: MessageType.text,
      content: content,
      receiverId: receiverId,
      conversationId: conversationId,
      timestamp: DateTime.now(),
    );
    
    await sendMessage(message);
  }
  
  /// 发送心跳消息
  Future<void> sendHeartbeat() async {
    if (!_isConnected) return;
    
    try {
      final heartbeat = WebSocketMessage(
        messageId: _generateMessageId(),
        messageType: MessageType.heartbeat,
        content: 'ping',
        timestamp: DateTime.now(),
      );
      
      await sendMessage(heartbeat);
      
    } catch (e) {
      print('发送心跳失败: $e');
    }
  }
  
  /// 获取消息流
  Stream<WebSocketMessage>? get messageStream => _messageController?.stream;
  
  /// 检查连接状态
  bool get isConnected => _isConnected;
  
  /// 处理接收到的消息
  void _onMessage(dynamic data) {
    try {
      final messageJson = jsonDecode(data.toString());
      final message = WebSocketMessage.fromJson(messageJson);
      
      // 处理特殊消息类型
      switch (message.messageType) {
        case MessageType.heartbeat:
          _handleHeartbeat(message);
          break;
        case MessageType.ack:
          _handleAck(message);
          break;
        case MessageType.error:
          _handleError(message);
          break;
        default:
          // 普通消息，发送到消息流
          _messageController?.add(message);
          break;
      }
      
      print('WebSocket消息接收: ${message.messageType} - ${message.content}');
      
    } catch (e) {
      print('解析WebSocket消息失败: $e');
    }
  }
  
  /// 处理连接错误
  void _onError(error) {
    print('WebSocket连接错误: $error');
    
    _isConnected = false;
    state = WebSocketConnectionState.error(error.toString());
    
    // 自动重连
    if (_shouldReconnect) {
      _scheduleReconnect();
    }
  }
  
  /// 处理连接断开
  void _onDisconnected() {
    print('WebSocket连接断开');
    
    _isConnected = false;
    _heartbeatTimer?.cancel();
    
    state = const WebSocketConnectionState.disconnected();
    
    // 自动重连
    if (_shouldReconnect) {
      _scheduleReconnect();
    }
  }
  
  /// 处理心跳消息
  void _handleHeartbeat(WebSocketMessage message) {
    if (message.content == 'ping') {
      // 回复pong
      final pong = WebSocketMessage(
        messageId: _generateMessageId(),
        messageType: MessageType.heartbeat,
        content: 'pong',
        relatedMessageId: message.messageId,
        timestamp: DateTime.now(),
      );
      
      sendMessage(pong);
    }
  }
  
  /// 处理确认消息
  void _handleAck(WebSocketMessage message) {
    // 可以在这里处理消息确认逻辑
    print('收到消息确认: ${message.relatedMessageId}');
  }
  
  /// 处理错误消息
  void _handleError(WebSocketMessage message) {
    print('收到错误消息: ${message.content}');
    state = WebSocketConnectionState.error(message.content);
  }
  
  /// 启动心跳
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(
      Duration(seconds: _heartbeatInterval),
      (_) => sendHeartbeat(),
    );
  }
  
  /// 安排重连
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      print('WebSocket重连次数超过限制，停止重连');
      state = const WebSocketConnectionState.error('连接失败，请检查网络');
      return;
    }
    
    _reconnectAttempts++;
    
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(
      Duration(seconds: _reconnectDelay * _reconnectAttempts),
      () {
        print('WebSocket开始第 $_reconnectAttempts 次重连');
        connect();
      },
    );
  }
  
  /// 构建WebSocket URL
  String _buildWebSocketUrl(int userId, String accessToken) {
    const baseUrl = String.fromEnvironment(
      'WEBSOCKET_BASE_URL',
      defaultValue: 'ws://localhost:8082/message-service',
    );
    
    return '$baseUrl/websocket/$userId?token=$accessToken';
  }
  
  /// 生成消息ID
  String _generateMessageId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${_generateRandomString(8)}';
  }
  
  /// 生成随机字符串
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(length, (index) => chars[random % chars.length]).join();
  }
}

/// WebSocket异常类
class WebSocketException implements Exception {
  final String message;
  
  const WebSocketException(this.message);
  
  @override
  String toString() => 'WebSocketException: $message';
}

/// WebSocket连接状态
sealed class WebSocketConnectionState {
  const WebSocketConnectionState();
  
  const factory WebSocketConnectionState.disconnected() = _Disconnected;
  const factory WebSocketConnectionState.connecting() = _Connecting;
  const factory WebSocketConnectionState.connected({
    required int userId,
    required DateTime connectedAt,
  }) = _Connected;
  const factory WebSocketConnectionState.error(String message) = _Error;
}

class _Disconnected extends WebSocketConnectionState {
  const _Disconnected();
}

class _Connecting extends WebSocketConnectionState {
  const _Connecting();
}

class _Connected extends WebSocketConnectionState {
  final int userId;
  final DateTime connectedAt;
  
  const _Connected({
    required this.userId,
    required this.connectedAt,
  });
}

class _Error extends WebSocketConnectionState {
  final String message;
  
  const _Error(this.message);
}
