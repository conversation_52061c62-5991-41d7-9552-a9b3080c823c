import 'package:freezed_annotation/freezed_annotation.dart';

part 'websocket_models.freezed.dart';
part 'websocket_models.g.dart';

/// WebSocket消息模型
@freezed
class WebSocketMessage with _$WebSocketMessage {
  const factory WebSocketMessage({
    required String messageId,
    required MessageType messageType,
    required String content,
    int? senderId,
    int? receiverId,
    String? conversationId,
    String? title,
    String? summary,
    @Default(MessagePriority.normal) MessagePriority priority,
    @Default(MessageStatus.pending) MessageStatus status,
    @Default(false) bool requireAck,
    DateTime? expireTime,
    required DateTime timestamp,
    String? relatedMessageId,
    Map<String, dynamic>? extraData,
    List<MessageAttachment>? attachments,
  }) = _WebSocketMessage;
  
  factory WebSocketMessage.fromJson(Map<String, dynamic> json) =>
      _$WebSocketMessageFromJson(json);
}

/// 消息类型枚举
enum MessageType {
  @JsonValue('TEXT')
  text,
  @JsonValue('IMAGE')
  image,
  @JsonValue('AUDIO')
  audio,
  @JsonValue('VIDEO')
  video,
  @JsonValue('FILE')
  file,
  @JsonValue('LOCATION')
  location,
  @JsonValue('LINK')
  link,
  @JsonValue('SYSTEM')
  system,
  @JsonValue('NOTIFICATION')
  notification,
  @JsonValue('ORDER')
  order,
  @JsonValue('PAYMENT')
  payment,
  @JsonValue('MARKETING')
  marketing,
  @JsonValue('CUSTOMER_SERVICE')
  customerService,
  @JsonValue('ACK')
  ack,
  @JsonValue('ERROR')
  error,
  @JsonValue('HEARTBEAT')
  heartbeat,
}

/// 消息优先级枚举
enum MessagePriority {
  @JsonValue('LOW')
  low,
  @JsonValue('NORMAL')
  normal,
  @JsonValue('HIGH')
  high,
  @JsonValue('URGENT')
  urgent,
}

/// 消息状态枚举
enum MessageStatus {
  @JsonValue('PENDING')
  pending,
  @JsonValue('SENT')
  sent,
  @JsonValue('DELIVERED')
  delivered,
  @JsonValue('READ')
  read,
  @JsonValue('FAILED')
  failed,
  @JsonValue('EXPIRED')
  expired,
  @JsonValue('RECALLED')
  recalled,
}

/// 消息附件模型
@freezed
class MessageAttachment with _$MessageAttachment {
  const factory MessageAttachment({
    required String id,
    required String fileName,
    required String fileType,
    required int fileSize,
    required String fileUrl,
    String? thumbnailUrl,
    int? width,
    int? height,
    int? duration,
    Map<String, dynamic>? metadata,
  }) = _MessageAttachment;
  
  factory MessageAttachment.fromJson(Map<String, dynamic> json) =>
      _$MessageAttachmentFromJson(json);
}

/// 推送通知模型
@freezed
class PushNotification with _$PushNotification {
  const factory PushNotification({
    required String id,
    required String title,
    required String body,
    String? imageUrl,
    String? clickAction,
    Map<String, String>? data,
    @Default(false) bool silent,
    String? sound,
    String? badge,
    String? tag,
    String? color,
    DateTime? scheduledTime,
    DateTime? expiryTime,
  }) = _PushNotification;
  
  factory PushNotification.fromJson(Map<String, dynamic> json) =>
      _$PushNotificationFromJson(json);
}

/// 消息会话模型
@freezed
class MessageConversation with _$MessageConversation {
  const factory MessageConversation({
    required String conversationId,
    required ConversationType type,
    required String title,
    String? avatar,
    String? description,
    required List<int> participants,
    WebSocketMessage? lastMessage,
    @Default(0) int unreadCount,
    @Default(false) bool muted,
    @Default(false) bool pinned,
    DateTime? lastReadTime,
    DateTime? createdTime,
    DateTime? updatedTime,
    Map<String, dynamic>? settings,
  }) = _MessageConversation;
  
  factory MessageConversation.fromJson(Map<String, dynamic> json) =>
      _$MessageConversationFromJson(json);
}

/// 会话类型枚举
enum ConversationType {
  @JsonValue('PRIVATE')
  private,
  @JsonValue('GROUP')
  group,
  @JsonValue('CHANNEL')
  channel,
  @JsonValue('CUSTOMER_SERVICE')
  customerService,
}

/// 消息发送请求模型
@freezed
class SendMessageRequest with _$SendMessageRequest {
  const factory SendMessageRequest({
    required MessageType messageType,
    required String content,
    String? title,
    int? receiverId,
    String? conversationId,
    @Default(MessagePriority.normal) MessagePriority priority,
    @Default(false) bool requireAck,
    DateTime? expireTime,
    List<MessageAttachment>? attachments,
    Map<String, dynamic>? extraData,
  }) = _SendMessageRequest;
  
  factory SendMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$SendMessageRequestFromJson(json);
}

/// 消息查询请求模型
@freezed
class MessageQueryRequest with _$MessageQueryRequest {
  const factory MessageQueryRequest({
    String? conversationId,
    int? senderId,
    int? receiverId,
    MessageType? messageType,
    DateTime? startTime,
    DateTime? endTime,
    @Default(0) int page,
    @Default(20) int size,
    @Default('timestamp') String sortBy,
    @Default('desc') String sortOrder,
  }) = _MessageQueryRequest;
  
  factory MessageQueryRequest.fromJson(Map<String, dynamic> json) =>
      _$MessageQueryRequestFromJson(json);
}

/// 消息查询响应模型
@freezed
class MessageQueryResponse with _$MessageQueryResponse {
  const factory MessageQueryResponse({
    @Default([]) List<WebSocketMessage> messages,
    @Default(0) int totalCount,
    @Default(0) int page,
    @Default(20) int size,
    @Default(false) bool hasMore,
  }) = _MessageQueryResponse;
  
  factory MessageQueryResponse.fromJson(Map<String, dynamic> json) =>
      _$MessageQueryResponseFromJson(json);
}

/// WebSocket事件模型
@freezed
class WebSocketEvent with _$WebSocketEvent {
  const factory WebSocketEvent({
    required String eventId,
    required WebSocketEventType eventType,
    required DateTime timestamp,
    Map<String, dynamic>? data,
  }) = _WebSocketEvent;
  
  factory WebSocketEvent.fromJson(Map<String, dynamic> json) =>
      _$WebSocketEventFromJson(json);
}

/// WebSocket事件类型枚举
enum WebSocketEventType {
  @JsonValue('CONNECTED')
  connected,
  @JsonValue('DISCONNECTED')
  disconnected,
  @JsonValue('MESSAGE_RECEIVED')
  messageReceived,
  @JsonValue('MESSAGE_SENT')
  messageSent,
  @JsonValue('MESSAGE_DELIVERED')
  messageDelivered,
  @JsonValue('MESSAGE_READ')
  messageRead,
  @JsonValue('TYPING_START')
  typingStart,
  @JsonValue('TYPING_STOP')
  typingStop,
  @JsonValue('USER_ONLINE')
  userOnline,
  @JsonValue('USER_OFFLINE')
  userOffline,
  @JsonValue('CONVERSATION_UPDATED')
  conversationUpdated,
  @JsonValue('ERROR')
  error,
}

/// 用户在线状态模型
@freezed
class UserOnlineStatus with _$UserOnlineStatus {
  const factory UserOnlineStatus({
    required int userId,
    required bool isOnline,
    DateTime? lastSeenTime,
    String? status,
    Map<String, dynamic>? deviceInfo,
  }) = _UserOnlineStatus;
  
  factory UserOnlineStatus.fromJson(Map<String, dynamic> json) =>
      _$UserOnlineStatusFromJson(json);
}

/// 输入状态模型
@freezed
class TypingStatus with _$TypingStatus {
  const factory TypingStatus({
    required int userId,
    required String conversationId,
    required bool isTyping,
    DateTime? timestamp,
  }) = _TypingStatus;
  
  factory TypingStatus.fromJson(Map<String, dynamic> json) =>
      _$TypingStatusFromJson(json);
}
