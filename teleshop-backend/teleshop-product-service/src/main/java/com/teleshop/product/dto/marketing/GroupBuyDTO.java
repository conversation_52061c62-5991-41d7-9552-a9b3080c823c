package com.teleshop.product.dto.marketing;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 团购活动数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupBuyDTO {

    /**
     * 团购活动ID
     */
    private String id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片
     */
    private List<String> productImages;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 团购价
     */
    private BigDecimal groupBuyPrice;

    /**
     * 折扣率
     */
    private BigDecimal discountRate;

    /**
     * 成团人数
     */
    private Integer requiredParticipants;

    /**
     * 活动状态：ACTIVE(进行中), ENDED(已结束), CANCELLED(已取消)
     */
    private String status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 团购规则
     */
    private GroupBuyRuleDTO rules;

    /**
     * 活动统计
     */
    private GroupBuyStatsDTO stats;

    /**
     * 正在进行的团购列表
     */
    private List<GroupBuyInstanceDTO> activeGroups;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupBuyRuleDTO {
        private Integer maxGroupsPerUser;
        private Integer maxBuyQuantityPerUser;
        private Integer groupTimeLimit; // 成团时间限制（小时）
        private Boolean allowRefund;
        private Integer refundTimeLimit;
        private Boolean requirePaymentFirst;
        private String paymentTimeLimit;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupBuyStatsDTO {
        private Integer totalGroups;
        private Integer successfulGroups;
        private Integer totalParticipants;
        private BigDecimal totalSales;
        private Double successRate;
        private Integer pageViews;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupBuyInstanceDTO {
        private String groupId;
        private String initiatorId;
        private String initiatorName;
        private String initiatorAvatar;
        private Integer currentParticipants;
        private Integer requiredParticipants;
        private LocalDateTime createTime;
        private LocalDateTime expireTime;
        private String status; // RECRUITING(招募中), SUCCESS(成团), FAILED(失败)
        private List<GroupParticipantDTO> participants;
        private long remainingSeconds;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupParticipantDTO {
        private String userId;
        private String userName;
        private String userAvatar;
        private Integer quantity;
        private LocalDateTime joinTime;
        private String status;
    }

    /**
     * 检查活动是否正在进行
     */
    public boolean isActive() {
        return "ACTIVE".equals(status) && 
               LocalDateTime.now().isAfter(startTime) && 
               LocalDateTime.now().isBefore(endTime);
    }

    /**
     * 检查活动是否已结束
     */
    public boolean isEnded() {
        return "ENDED".equals(status) || LocalDateTime.now().isAfter(endTime);
    }

    /**
     * 获取活动剩余时间（秒）
     */
    public long getRemainingSeconds() {
        if (isActive()) {
            return java.time.Duration.between(LocalDateTime.now(), endTime).getSeconds();
        }
        return 0;
    }

    /**
     * 获取节省金额
     */
    public BigDecimal getSavedAmount() {
        if (originalPrice != null && groupBuyPrice != null) {
            return originalPrice.subtract(groupBuyPrice);
        }
        return BigDecimal.ZERO;
    }
}

/**
 * 发起团购请求数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class GroupBuyInitiateRequestDTO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 收货地址ID
     */
    private String addressId;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 团购留言
     */
    private String message;

    /**
     * 是否公开团购
     */
    private Boolean isPublic;
}

/**
 * 参与团购请求数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class GroupBuyJoinRequestDTO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 团购实例ID
     */
    private String groupInstanceId;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 收货地址ID
     */
    private String addressId;

    /**
     * 支付方式
     */
    private String paymentMethod;
}

/**
 * 团购参与结果数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class GroupBuyParticipationDTO {

    /**
     * 参与记录ID
     */
    private String id;

    /**
     * 团购活动ID
     */
    private String groupBuyId;

    /**
     * 团购实例ID
     */
    private String groupInstanceId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 团购价格
     */
    private BigDecimal groupBuyPrice;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 参与类型：INITIATOR(发起者), PARTICIPANT(参与者)
     */
    private String participationType;

    /**
     * 参与状态：PENDING(待成团), SUCCESS(成团成功), FAILED(成团失败), CANCELLED(已取消)
     */
    private String status;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 参与时间
     */
    private LocalDateTime participationTime;

    /**
     * 成团时间
     */
    private LocalDateTime successTime;

    /**
     * 团购实例信息
     */
    private GroupBuyDTO.GroupBuyInstanceDTO groupInstance;

    /**
     * 分享信息
     */
    private GroupBuyShareInfoDTO shareInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupBuyShareInfoDTO {
        private String shareUrl;
        private String shareTitle;
        private String shareDescription;
        private String shareImage;
        private String qrCode;
    }
}
