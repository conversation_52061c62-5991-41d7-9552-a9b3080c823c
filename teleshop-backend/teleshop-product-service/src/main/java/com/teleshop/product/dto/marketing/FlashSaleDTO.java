package com.teleshop.product.dto.marketing;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 限时抢购活动数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleDTO {

    /**
     * 抢购活动ID
     */
    private String id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 活动状态：UPCOMING(即将开始), ACTIVE(进行中), ENDED(已结束), CANCELLED(已取消)
     */
    private String status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动商品列表
     */
    private List<FlashSaleProductDTO> products;

    /**
     * 活动规则
     */
    private FlashSaleRuleDTO rules;

    /**
     * 活动统计
     */
    private FlashSaleStatsDTO stats;

    /**
     * 活动横幅图片
     */
    private String bannerImage;

    /**
     * 活动分享图片
     */
    private String shareImage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FlashSaleProductDTO {
        private String productId;
        private String productName;
        private String productImage;
        private BigDecimal originalPrice;
        private BigDecimal flashSalePrice;
        private BigDecimal discountRate;
        private Integer totalStock;
        private Integer remainingStock;
        private Integer soldCount;
        private Integer maxBuyQuantity;
        private String status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FlashSaleRuleDTO {
        private Integer maxParticipants;
        private Integer maxBuyQuantityPerUser;
        private Boolean requireMembership;
        private String membershipLevel;
        private Boolean allowRefund;
        private Integer refundTimeLimit;
        private String paymentTimeLimit;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FlashSaleStatsDTO {
        private Integer totalParticipants;
        private Integer totalOrders;
        private BigDecimal totalSales;
        private Double conversionRate;
        private Integer pageViews;
        private Integer uniqueVisitors;
    }

    /**
     * 检查活动是否即将开始
     */
    public boolean isUpcoming() {
        return "UPCOMING".equals(status) && startTime.isAfter(LocalDateTime.now());
    }

    /**
     * 检查活动是否正在进行
     */
    public boolean isActive() {
        return "ACTIVE".equals(status) && 
               LocalDateTime.now().isAfter(startTime) && 
               LocalDateTime.now().isBefore(endTime);
    }

    /**
     * 检查活动是否已结束
     */
    public boolean isEnded() {
        return "ENDED".equals(status) || LocalDateTime.now().isAfter(endTime);
    }

    /**
     * 获取活动剩余时间（秒）
     */
    public long getRemainingSeconds() {
        if (isUpcoming()) {
            return java.time.Duration.between(LocalDateTime.now(), startTime).getSeconds();
        } else if (isActive()) {
            return java.time.Duration.between(LocalDateTime.now(), endTime).getSeconds();
        }
        return 0;
    }

    /**
     * 获取活动进度百分比
     */
    public double getProgressPercentage() {
        if (isUpcoming()) {
            return 0.0;
        } else if (isEnded()) {
            return 100.0;
        } else {
            long totalDuration = java.time.Duration.between(startTime, endTime).getSeconds();
            long elapsed = java.time.Duration.between(startTime, LocalDateTime.now()).getSeconds();
            return (double) elapsed / totalDuration * 100;
        }
    }
}

/**
 * 限时抢购参与请求数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class FlashSaleParticipationRequestDTO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 收货地址ID
     */
    private String addressId;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 备注
     */
    private String note;
}

/**
 * 限时抢购参与结果数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class FlashSaleParticipationDTO {

    /**
     * 参与记录ID
     */
    private String id;

    /**
     * 抢购活动ID
     */
    private String flashSaleId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 抢购价格
     */
    private BigDecimal flashSalePrice;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 参与状态：SUCCESS(成功), FAILED(失败), PENDING(待支付), CANCELLED(已取消)
     */
    private String status;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 参与时间
     */
    private LocalDateTime participationTime;

    /**
     * 支付截止时间
     */
    private LocalDateTime paymentDeadline;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 排队位置
     */
    private Integer queuePosition;
}
