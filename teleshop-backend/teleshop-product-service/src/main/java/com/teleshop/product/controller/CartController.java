package com.teleshop.product.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.product.dto.CartDTO;
import com.teleshop.product.dto.CartItemDTO;
import com.teleshop.product.dto.CartOperationDTO;
import com.teleshop.product.dto.CartShareDTO;
import com.teleshop.product.service.CartService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "购物车管理", description = "购物车相关接口")
@RestController
@RequestMapping("/api/v1/cart")
@RequiredArgsConstructor
public class CartController {

    private final CartService cartService;

    @Operation(summary = "获取购物车信息")
    @GetMapping("/{userId}")
    public ApiResponse<CartDTO> getCart(@PathVariable String userId) {
        CartDTO cart = cartService.getCart(userId);
        return ApiResponse.success(cart);
    }

    @Operation(summary = "添加商品到购物车")
    @PostMapping("/items")
    public ApiResponse<CartItemDTO> addToCart(@Valid @RequestBody CartOperationDTO operationDTO) {
        CartItemDTO cartItem = cartService.addToCart(operationDTO);
        return ApiResponse.success(cartItem);
    }

    @Operation(summary = "更新购物车商品数量")
    @PutMapping("/items/{itemId}/quantity")
    public ApiResponse<CartItemDTO> updateQuantity(
            @PathVariable String itemId,
            @RequestParam int quantity) {
        CartItemDTO cartItem = cartService.updateQuantity(itemId, quantity);
        return ApiResponse.success(cartItem);
    }

    @Operation(summary = "更新购物车商品规格")
    @PutMapping("/items/{itemId}/specs")
    public ApiResponse<CartItemDTO> updateSpecs(
            @PathVariable String itemId,
            @RequestBody Map<String, String> specs) {
        CartItemDTO cartItem = cartService.updateSpecs(itemId, specs);
        return ApiResponse.success(cartItem);
    }

    @Operation(summary = "删除购物车商品")
    @DeleteMapping("/items/{itemId}")
    public ApiResponse<Void> removeFromCart(@PathVariable String itemId) {
        cartService.removeFromCart(itemId);
        return ApiResponse.success();
    }

    @Operation(summary = "批量删除购物车商品")
    @DeleteMapping("/items/batch")
    public ApiResponse<Void> batchRemoveFromCart(@RequestBody List<String> itemIds) {
        cartService.batchRemoveFromCart(itemIds);
        return ApiResponse.success();
    }

    @Operation(summary = "清空购物车")
    @DeleteMapping("/{userId}/clear")
    public ApiResponse<Void> clearCart(@PathVariable String userId) {
        cartService.clearCart(userId);
        return ApiResponse.success();
    }

    @Operation(summary = "应用优惠券")
    @PostMapping("/{userId}/coupon")
    public ApiResponse<CartDTO> applyCoupon(
            @PathVariable String userId,
            @RequestParam String couponCode) {
        CartDTO cart = cartService.applyCoupon(userId, couponCode);
        return ApiResponse.success(cart);
    }

    @Operation(summary = "移除优惠券")
    @DeleteMapping("/{userId}/coupon")
    public ApiResponse<CartDTO> removeCoupon(@PathVariable String userId) {
        CartDTO cart = cartService.removeCoupon(userId);
        return ApiResponse.success(cart);
    }

    @Operation(summary = "检查购物车商品库存")
    @PostMapping("/{userId}/check-stock")
    public ApiResponse<Map<String, Object>> checkStock(@PathVariable String userId) {
        Map<String, Object> stockInfo = cartService.checkStock(userId);
        return ApiResponse.success(stockInfo);
    }

    @Operation(summary = "检查购物车商品价格变动")
    @PostMapping("/{userId}/check-price")
    public ApiResponse<Map<String, Object>> checkPriceChanges(@PathVariable String userId) {
        Map<String, Object> priceInfo = cartService.checkPriceChanges(userId);
        return ApiResponse.success(priceInfo);
    }

    @Operation(summary = "获取购物车推荐商品")
    @GetMapping("/{userId}/recommendations")
    public ApiResponse<List<CartItemDTO>> getCartRecommendations(
            @PathVariable String userId,
            @RequestParam(defaultValue = "10") int limit) {
        List<CartItemDTO> recommendations = cartService.getCartRecommendations(userId, limit);
        return ApiResponse.success(recommendations);
    }

    @Operation(summary = "分享购物车")
    @PostMapping("/{userId}/share")
    public ApiResponse<CartShareDTO> shareCart(
            @PathVariable String userId,
            @RequestParam(required = false) String message) {
        CartShareDTO shareInfo = cartService.shareCart(userId, message);
        return ApiResponse.success(shareInfo);
    }

    @Operation(summary = "获取分享的购物车")
    @GetMapping("/shared/{shareId}")
    public ApiResponse<CartDTO> getSharedCart(@PathVariable String shareId) {
        CartDTO cart = cartService.getSharedCart(shareId);
        return ApiResponse.success(cart);
    }

    @Operation(summary = "复制分享的购物车")
    @PostMapping("/shared/{shareId}/copy")
    public ApiResponse<CartDTO> copySharedCart(
            @PathVariable String shareId,
            @RequestParam String userId) {
        CartDTO cart = cartService.copySharedCart(shareId, userId);
        return ApiResponse.success(cart);
    }

    @Operation(summary = "获取购物车统计信息")
    @GetMapping("/{userId}/stats")
    public ApiResponse<Map<String, Object>> getCartStats(@PathVariable String userId) {
        Map<String, Object> stats = cartService.getCartStats(userId);
        return ApiResponse.success(stats);
    }

    @Operation(summary = "同步购物车数据")
    @PostMapping("/{userId}/sync")
    public ApiResponse<CartDTO> syncCart(
            @PathVariable String userId,
            @RequestBody List<CartItemDTO> localCartItems) {
        CartDTO cart = cartService.syncCart(userId, localCartItems);
        return ApiResponse.success(cart);
    }

    @Operation(summary = "预计算购物车价格")
    @PostMapping("/{userId}/calculate")
    public ApiResponse<Map<String, Object>> calculateCart(
            @PathVariable String userId,
            @RequestParam(required = false) String couponCode,
            @RequestParam(required = false) String addressId) {
        Map<String, Object> calculation = cartService.calculateCart(userId, couponCode, addressId);
        return ApiResponse.success(calculation);
    }

    @Operation(summary = "设置购物车商品备注")
    @PutMapping("/items/{itemId}/note")
    public ApiResponse<CartItemDTO> setItemNote(
            @PathVariable String itemId,
            @RequestParam String note) {
        CartItemDTO cartItem = cartService.setItemNote(itemId, note);
        return ApiResponse.success(cartItem);
    }

    @Operation(summary = "收藏购物车商品")
    @PostMapping("/items/{itemId}/favorite")
    public ApiResponse<Void> favoriteItem(@PathVariable String itemId) {
        cartService.favoriteItem(itemId);
        return ApiResponse.success();
    }

    @Operation(summary = "移动商品到收藏夹")
    @PostMapping("/items/{itemId}/move-to-favorites")
    public ApiResponse<Void> moveToFavorites(@PathVariable String itemId) {
        cartService.moveToFavorites(itemId);
        return ApiResponse.success();
    }

    @Operation(summary = "获取购物车失效商品")
    @GetMapping("/{userId}/invalid-items")
    public ApiResponse<List<CartItemDTO>> getInvalidItems(@PathVariable String userId) {
        List<CartItemDTO> invalidItems = cartService.getInvalidItems(userId);
        return ApiResponse.success(invalidItems);
    }

    @Operation(summary = "清理购物车失效商品")
    @DeleteMapping("/{userId}/invalid-items")
    public ApiResponse<Void> clearInvalidItems(@PathVariable String userId) {
        cartService.clearInvalidItems(userId);
        return ApiResponse.success();
    }

    @Operation(summary = "恢复购物车商品")
    @PostMapping("/items/{itemId}/restore")
    public ApiResponse<CartItemDTO> restoreItem(@PathVariable String itemId) {
        CartItemDTO cartItem = cartService.restoreItem(itemId);
        return ApiResponse.success(cartItem);
    }

    @Operation(summary = "获取购物车历史记录")
    @GetMapping("/{userId}/history")
    public ApiResponse<List<Map<String, Object>>> getCartHistory(
            @PathVariable String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        List<Map<String, Object>> history = cartService.getCartHistory(userId, page, size);
        return ApiResponse.success(history);
    }
}
