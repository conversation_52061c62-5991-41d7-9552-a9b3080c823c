package com.teleshop.product.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 批量推荐请求数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendationRequestDTO {

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 推荐场景列表
     */
    @NotEmpty(message = "推荐场景不能为空")
    private List<RecommendationScene> scenes;

    /**
     * 全局过滤条件
     */
    private FilterCondition globalFilter;

    /**
     * 推荐配置
     */
    private RecommendationConfig config;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecommendationScene {
        /**
         * 场景名称：home_page, product_detail, cart_page, checkout_page
         */
        @NotBlank(message = "场景名称不能为空")
        private String sceneName;

        /**
         * 推荐类型：personalized, hot, similar, new_arrivals, guess_you_like
         */
        @NotBlank(message = "推荐类型不能为空")
        private String recommendationType;

        /**
         * 推荐数量
         */
        private Integer limit;

        /**
         * 场景特定参数
         */
        private Map<String, Object> sceneParams;

        /**
         * 场景过滤条件
         */
        private FilterCondition sceneFilter;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterCondition {
        /**
         * 包含的分类ID列表
         */
        private List<String> includeCategoryIds;

        /**
         * 排除的分类ID列表
         */
        private List<String> excludeCategoryIds;

        /**
         * 包含的品牌ID列表
         */
        private List<String> includeBrandIds;

        /**
         * 排除的品牌ID列表
         */
        private List<String> excludeBrandIds;

        /**
         * 价格范围
         */
        private PriceRange priceRange;

        /**
         * 是否只推荐有库存商品
         */
        private Boolean onlyInStock;

        /**
         * 是否只推荐上架商品
         */
        private Boolean onlyOnSale;

        /**
         * 排除的商品ID列表
         */
        private List<String> excludeProductIds;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceRange {
        private Double minPrice;
        private Double maxPrice;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecommendationConfig {
        /**
         * 是否启用多样性
         */
        private Boolean enableDiversity;

        /**
         * 多样性权重
         */
        private Double diversityWeight;

        /**
         * 是否启用实时性
         */
        private Boolean enableRealtime;

        /**
         * 缓存时间（分钟）
         */
        private Integer cacheMinutes;

        /**
         * 推荐算法版本
         */
        private String algorithmVersion;
    }
}
