package com.teleshop.product.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 购物车数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CartDTO {

    /**
     * 购物车ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 购物车商品列表
     */
    private List<CartItemDTO> items;

    /**
     * 商品总数量
     */
    private Integer totalQuantity;

    /**
     * 商品总价（原价）
     */
    private BigDecimal totalOriginalPrice;

    /**
     * 商品总价（现价）
     */
    private BigDecimal totalPrice;

    /**
     * 优惠券信息
     */
    private CouponInfo appliedCoupon;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 最终价格
     */
    private BigDecimal finalPrice;

    /**
     * 运费
     */
    private BigDecimal shippingFee;

    /**
     * 总计金额
     */
    private BigDecimal totalAmount;

    /**
     * 购物车状态
     */
    private String status;

    /**
     * 失效商品数量
     */
    private Integer invalidItemCount;

    /**
     * 库存不足商品数量
     */
    private Integer outOfStockItemCount;

    /**
     * 价格变动商品数量
     */
    private Integer priceChangedItemCount;

    /**
     * 推荐商品列表
     */
    private List<CartItemDTO> recommendedItems;

    /**
     * 购物车备注
     */
    private String note;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后访问时间
     */
    private LocalDateTime lastAccessTime;

    /**
     * 扩展属性
     */
    private Map<String, Object> properties;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CouponInfo {
        private String couponId;
        private String couponCode;
        private String couponName;
        private String couponType;
        private BigDecimal discountValue;
        private BigDecimal minOrderAmount;
        private LocalDateTime expireTime;
        private String description;
    }

    /**
     * 计算节省金额
     */
    public BigDecimal getSavedAmount() {
        if (totalOriginalPrice != null && finalPrice != null) {
            return totalOriginalPrice.subtract(finalPrice);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 检查是否有失效商品
     */
    public boolean hasInvalidItems() {
        return invalidItemCount != null && invalidItemCount > 0;
    }

    /**
     * 检查是否有库存不足商品
     */
    public boolean hasOutOfStockItems() {
        return outOfStockItemCount != null && outOfStockItemCount > 0;
    }

    /**
     * 检查是否有价格变动商品
     */
    public boolean hasPriceChangedItems() {
        return priceChangedItemCount != null && priceChangedItemCount > 0;
    }

    /**
     * 检查购物车是否为空
     */
    public boolean isEmpty() {
        return items == null || items.isEmpty();
    }

    /**
     * 获取有效商品数量
     */
    public int getValidItemCount() {
        if (items == null) return 0;
        return (int) items.stream()
                .filter(item -> "VALID".equals(item.getStatus()))
                .count();
    }

    /**
     * 获取选中商品数量
     */
    public int getSelectedItemCount() {
        if (items == null) return 0;
        return (int) items.stream()
                .filter(CartItemDTO::isSelected)
                .count();
    }

    /**
     * 计算选中商品总价
     */
    public BigDecimal getSelectedItemsTotalPrice() {
        if (items == null) return BigDecimal.ZERO;
        return items.stream()
                .filter(CartItemDTO::isSelected)
                .map(item -> item.getCurrentPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
