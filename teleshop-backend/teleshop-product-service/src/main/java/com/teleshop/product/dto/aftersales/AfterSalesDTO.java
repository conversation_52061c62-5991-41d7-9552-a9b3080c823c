package com.teleshop.product.dto.aftersales;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 可申请售后的订单数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EligibleOrderDTO {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 商品信息
     */
    private List<EligibleOrderItemDTO> items;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 收货时间
     */
    private LocalDateTime receiveTime;

    /**
     * 可申请的售后类型
     */
    private List<String> availableAfterSalesTypes;

    /**
     * 售后截止时间
     */
    private LocalDateTime afterSalesDeadline;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EligibleOrderItemDTO {
        private String orderItemId;
        private String productId;
        private String productName;
        private String productImage;
        private String skuInfo;
        private Integer quantity;
        private BigDecimal price;
        private String status;
        private List<String> availableAfterSalesTypes;
    }
}

/**
 * 售后资格检查数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AfterSalesEligibilityDTO {

    /**
     * 是否可申请售后
     */
    private Boolean eligible;

    /**
     * 可申请的售后类型
     */
    private List<String> availableTypes;

    /**
     * 不可申请的原因
     */
    private String ineligibleReason;

    /**
     * 售后截止时间
     */
    private LocalDateTime deadline;

    /**
     * 剩余天数
     */
    private Integer remainingDays;

    /**
     * 售后政策说明
     */
    private String policyDescription;
}

/**
 * 退货申请请求数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnApplicationRequestDTO {

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 订单ID
     */
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    /**
     * 订单商品ID列表
     */
    @NotNull(message = "商品列表不能为空")
    private List<String> orderItemIds;

    /**
     * 退货原因
     */
    @NotBlank(message = "退货原因不能为空")
    private String reason;

    /**
     * 退货原因分类
     */
    private String reasonCategory;

    /**
     * 详细描述
     */
    private String description;

    /**
     * 退货数量
     */
    private Map<String, Integer> quantities;

    /**
     * 凭证图片
     */
    private List<String> evidenceImages;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 是否需要上门取件
     */
    private Boolean needPickup;

    /**
     * 取件地址ID
     */
    private String pickupAddressId;
}

/**
 * 换货申请请求数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeApplicationRequestDTO {

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 订单ID
     */
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    /**
     * 订单商品ID
     */
    @NotBlank(message = "商品ID不能为空")
    private String orderItemId;

    /**
     * 换货原因
     */
    @NotBlank(message = "换货原因不能为空")
    private String reason;

    /**
     * 换货原因分类
     */
    private String reasonCategory;

    /**
     * 详细描述
     */
    private String description;

    /**
     * 换货数量
     */
    private Integer quantity;

    /**
     * 新的规格要求
     */
    private Map<String, String> newSpecs;

    /**
     * 凭证图片
     */
    private List<String> evidenceImages;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 收货地址ID
     */
    private String deliveryAddressId;

    /**
     * 是否需要上门取件
     */
    private Boolean needPickup;

    /**
     * 取件地址ID
     */
    private String pickupAddressId;
}

/**
 * 退款申请请求数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundApplicationRequestDTO {

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 订单ID
     */
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    /**
     * 订单商品ID列表
     */
    private List<String> orderItemIds;

    /**
     * 退款类型：FULL(全额退款), PARTIAL(部分退款)
     */
    @NotBlank(message = "退款类型不能为空")
    private String refundType;

    /**
     * 退款原因
     */
    @NotBlank(message = "退款原因不能为空")
    private String reason;

    /**
     * 退款原因分类
     */
    private String reasonCategory;

    /**
     * 详细描述
     */
    private String description;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款数量
     */
    private Map<String, Integer> quantities;

    /**
     * 凭证图片
     */
    private List<String> evidenceImages;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 退款账户信息
     */
    private RefundAccountInfo refundAccount;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefundAccountInfo {
        private String accountType; // ORIGINAL(原路退回), BANK(银行卡), ALIPAY(支付宝), WECHAT(微信)
        private String accountNumber;
        private String accountName;
        private String bankName;
    }
}

/**
 * 售后申请结果数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AfterSalesApplicationDTO {

    /**
     * 申请ID
     */
    private String applicationId;

    /**
     * 申请编号
     */
    private String applicationNumber;

    /**
     * 申请类型：RETURN(退货), EXCHANGE(换货), REFUND(退款)
     */
    private String applicationType;

    /**
     * 申请状态
     */
    private String status;

    /**
     * 预计处理时间
     */
    private String estimatedProcessTime;

    /**
     * 申请时间
     */
    private LocalDateTime applicationTime;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 备注信息
     */
    private String note;

    /**
     * 下一步操作指引
     */
    private String nextStepGuide;
}
