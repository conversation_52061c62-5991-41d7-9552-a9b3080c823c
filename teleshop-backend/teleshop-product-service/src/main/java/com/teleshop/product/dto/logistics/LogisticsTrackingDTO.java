package com.teleshop.product.dto.logistics;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流跟踪数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsTrackingDTO {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 物流公司代码
     */
    private String logisticsCompanyCode;

    /**
     * 物流公司名称
     */
    private String logisticsCompanyName;

    /**
     * 物流公司Logo
     */
    private String logisticsCompanyLogo;

    /**
     * 当前状态
     */
    private String currentStatus;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 预计送达时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 实际送达时间
     */
    private LocalDateTime actualDeliveryTime;

    /**
     * 发货时间
     */
    private LocalDateTime shipTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 配送员信息
     */
    private DeliveryPersonInfo deliveryPerson;

    /**
     * 收货地址
     */
    private DeliveryAddressInfo deliveryAddress;

    /**
     * 物流轨迹
     */
    private List<LogisticsTraceDTO> traces;

    /**
     * 是否支持实时跟踪
     */
    private Boolean supportRealTimeTracking;

    /**
     * 是否可以联系配送员
     */
    private Boolean canContactDeliveryPerson;

    /**
     * 是否可以预约配送时间
     */
    private Boolean canScheduleDelivery;

    /**
     * 异常信息
     */
    private LogisticsExceptionInfo exceptionInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeliveryPersonInfo {
        private String id;
        private String name;
        private String phone;
        private String avatar;
        private Double rating;
        private String vehicleType;
        private String vehicleNumber;
        private Boolean isOnline;
        private LocationInfo currentLocation;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeliveryAddressInfo {
        private String recipientName;
        private String recipientPhone;
        private String province;
        private String city;
        private String district;
        private String detailAddress;
        private String postalCode;
        private Double latitude;
        private Double longitude;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationInfo {
        private Double latitude;
        private Double longitude;
        private String address;
        private LocalDateTime updateTime;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LogisticsExceptionInfo {
        private String exceptionType;
        private String description;
        private LocalDateTime occurTime;
        private String status;
        private String handleResult;
    }

    /**
     * 检查是否已发货
     */
    public boolean isShipped() {
        return shipTime != null && 
               ("SHIPPED".equals(currentStatus) || 
                "IN_TRANSIT".equals(currentStatus) || 
                "OUT_FOR_DELIVERY".equals(currentStatus) || 
                "DELIVERED".equals(currentStatus));
    }

    /**
     * 检查是否正在配送
     */
    public boolean isOutForDelivery() {
        return "OUT_FOR_DELIVERY".equals(currentStatus);
    }

    /**
     * 检查是否已送达
     */
    public boolean isDelivered() {
        return "DELIVERED".equals(currentStatus) && actualDeliveryTime != null;
    }

    /**
     * 检查是否有异常
     */
    public boolean hasException() {
        return exceptionInfo != null && "ACTIVE".equals(exceptionInfo.getStatus());
    }

    /**
     * 获取配送进度百分比
     */
    public int getDeliveryProgress() {
        switch (currentStatus) {
            case "PENDING":
                return 0;
            case "PICKED_UP":
                return 20;
            case "SHIPPED":
                return 40;
            case "IN_TRANSIT":
                return 60;
            case "OUT_FOR_DELIVERY":
                return 80;
            case "DELIVERED":
                return 100;
            default:
                return 0;
        }
    }
}

/**
 * 物流轨迹数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class LogisticsTraceDTO {

    /**
     * 轨迹ID
     */
    private String id;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态描述
     */
    private String description;

    /**
     * 发生时间
     */
    private LocalDateTime occurTime;

    /**
     * 发生地点
     */
    private String location;

    /**
     * 操作人员
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否为关键节点
     */
    private Boolean isKeyNode;

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 地理位置
     */
    private LocationInfo locationInfo;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationInfo {
        private Double latitude;
        private Double longitude;
        private String province;
        private String city;
        private String district;
    }
}

/**
 * 物流跟踪详情数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class LogisticsTrackingDetailDTO {

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 物流公司信息
     */
    private LogisticsCompanyInfo company;

    /**
     * 当前状态
     */
    private String currentStatus;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 发货信息
     */
    private ShipmentInfo shipmentInfo;

    /**
     * 收货信息
     */
    private RecipientInfo recipientInfo;

    /**
     * 物流轨迹列表
     */
    private List<LogisticsTraceDTO> traces;

    /**
     * 预计送达时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 实际送达时间
     */
    private LocalDateTime actualDeliveryTime;

    /**
     * 配送时效
     */
    private DeliveryTimeInfo deliveryTimeInfo;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 数据来源
     */
    private String dataSource;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LogisticsCompanyInfo {
        private String code;
        private String name;
        private String logo;
        private String website;
        private String customerServicePhone;
        private Boolean supportRealTimeTracking;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShipmentInfo {
        private String senderName;
        private String senderPhone;
        private String senderAddress;
        private LocalDateTime shipTime;
        private String shipmentType;
        private Double weight;
        private String packageDescription;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecipientInfo {
        private String recipientName;
        private String recipientPhone;
        private String recipientAddress;
        private String postalCode;
        private String specialInstructions;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeliveryTimeInfo {
        private Integer standardDeliveryDays;
        private Integer actualDeliveryDays;
        private Boolean isDelayed;
        private String delayReason;
        private Integer delayHours;
    }
}
