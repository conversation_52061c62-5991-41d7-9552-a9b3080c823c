package com.teleshop.product.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.product.dto.growth.*;
import com.teleshop.product.service.UserGrowthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "用户成长体系", description = "用户成长体系相关接口")
@RestController
@RequestMapping("/api/v1/user-growth")
@RequiredArgsConstructor
public class UserGrowthController {

    private final UserGrowthService userGrowthService;

    // ==================== 用户等级系统 ====================

    @Operation(summary = "获取用户等级信息")
    @GetMapping("/level/user/{userId}")
    public ApiResponse<UserLevelDTO> getUserLevel(@PathVariable String userId) {
        UserLevelDTO userLevel = userGrowthService.getUserLevel(userId);
        return ApiResponse.success(userLevel);
    }

    @Operation(summary = "获取所有等级配置")
    @GetMapping("/level/configs")
    public ApiResponse<List<LevelConfigDTO>> getLevelConfigs() {
        List<LevelConfigDTO> configs = userGrowthService.getLevelConfigs();
        return ApiResponse.success(configs);
    }

    @Operation(summary = "获取等级权益")
    @GetMapping("/level/{levelId}/benefits")
    public ApiResponse<List<LevelBenefitDTO>> getLevelBenefits(@PathVariable String levelId) {
        List<LevelBenefitDTO> benefits = userGrowthService.getLevelBenefits(levelId);
        return ApiResponse.success(benefits);
    }

    @Operation(summary = "升级用户等级")
    @PostMapping("/level/user/{userId}/upgrade")
    public ApiResponse<UserLevelUpgradeResultDTO> upgradeUserLevel(@PathVariable String userId) {
        UserLevelUpgradeResultDTO result = userGrowthService.upgradeUserLevel(userId);
        return ApiResponse.success(result);
    }

    // ==================== 积分系统 ====================

    @Operation(summary = "获取用户积分信息")
    @GetMapping("/points/user/{userId}")
    public ApiResponse<UserPointsDTO> getUserPoints(@PathVariable String userId) {
        UserPointsDTO userPoints = userGrowthService.getUserPoints(userId);
        return ApiResponse.success(userPoints);
    }

    @Operation(summary = "增加用户积分")
    @PostMapping("/points/user/{userId}/add")
    public ApiResponse<PointsTransactionDTO> addUserPoints(
            @PathVariable String userId,
            @Valid @RequestBody AddPointsRequestDTO requestDTO) {
        
        PointsTransactionDTO transaction = userGrowthService.addUserPoints(userId, requestDTO);
        return ApiResponse.success(transaction);
    }

    @Operation(summary = "消费用户积分")
    @PostMapping("/points/user/{userId}/consume")
    public ApiResponse<PointsTransactionDTO> consumeUserPoints(
            @PathVariable String userId,
            @Valid @RequestBody ConsumePointsRequestDTO requestDTO) {
        
        PointsTransactionDTO transaction = userGrowthService.consumeUserPoints(userId, requestDTO);
        return ApiResponse.success(transaction);
    }

    @Operation(summary = "获取积分交易记录")
    @GetMapping("/points/user/{userId}/transactions")
    public ApiResponse<List<PointsTransactionDTO>> getPointsTransactions(
            @PathVariable String userId,
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<PointsTransactionDTO> transactions = userGrowthService.getPointsTransactions(
                userId, type, page, size);
        return ApiResponse.success(transactions);
    }

    @Operation(summary = "获取积分获取规则")
    @GetMapping("/points/rules")
    public ApiResponse<List<PointsRuleDTO>> getPointsRules() {
        List<PointsRuleDTO> rules = userGrowthService.getPointsRules();
        return ApiResponse.success(rules);
    }

    // ==================== 成就系统 ====================

    @Operation(summary = "获取用户成就")
    @GetMapping("/achievements/user/{userId}")
    public ApiResponse<List<UserAchievementDTO>> getUserAchievements(
            @PathVariable String userId,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String status) {
        
        List<UserAchievementDTO> achievements = userGrowthService.getUserAchievements(
                userId, category, status);
        return ApiResponse.success(achievements);
    }

    @Operation(summary = "获取所有成就配置")
    @GetMapping("/achievements/configs")
    public ApiResponse<List<AchievementConfigDTO>> getAchievementConfigs(
            @RequestParam(required = false) String category) {
        
        List<AchievementConfigDTO> configs = userGrowthService.getAchievementConfigs(category);
        return ApiResponse.success(configs);
    }

    @Operation(summary = "解锁成就")
    @PostMapping("/achievements/user/{userId}/unlock")
    public ApiResponse<AchievementUnlockResultDTO> unlockAchievement(
            @PathVariable String userId,
            @Valid @RequestBody UnlockAchievementRequestDTO requestDTO) {
        
        AchievementUnlockResultDTO result = userGrowthService.unlockAchievement(userId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "领取成就奖励")
    @PostMapping("/achievements/user/{userId}/claim-reward")
    public ApiResponse<AchievementRewardDTO> claimAchievementReward(
            @PathVariable String userId,
            @RequestParam String achievementId) {
        
        AchievementRewardDTO reward = userGrowthService.claimAchievementReward(userId, achievementId);
        return ApiResponse.success(reward);
    }

    // ==================== 任务系统 ====================

    @Operation(summary = "获取用户任务列表")
    @GetMapping("/tasks/user/{userId}")
    public ApiResponse<List<UserTaskDTO>> getUserTasks(
            @PathVariable String userId,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {
        
        List<UserTaskDTO> tasks = userGrowthService.getUserTasks(userId, type, status);
        return ApiResponse.success(tasks);
    }

    @Operation(summary = "获取任务配置")
    @GetMapping("/tasks/configs")
    public ApiResponse<List<TaskConfigDTO>> getTaskConfigs(
            @RequestParam(required = false) String type) {
        
        List<TaskConfigDTO> configs = userGrowthService.getTaskConfigs(type);
        return ApiResponse.success(configs);
    }

    @Operation(summary = "完成任务")
    @PostMapping("/tasks/user/{userId}/complete")
    public ApiResponse<TaskCompletionResultDTO> completeTask(
            @PathVariable String userId,
            @Valid @RequestBody CompleteTaskRequestDTO requestDTO) {
        
        TaskCompletionResultDTO result = userGrowthService.completeTask(userId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "领取任务奖励")
    @PostMapping("/tasks/user/{userId}/claim-reward")
    public ApiResponse<TaskRewardDTO> claimTaskReward(
            @PathVariable String userId,
            @RequestParam String taskId) {
        
        TaskRewardDTO reward = userGrowthService.claimTaskReward(userId, taskId);
        return ApiResponse.success(reward);
    }

    @Operation(summary = "刷新每日任务")
    @PostMapping("/tasks/user/{userId}/refresh-daily")
    public ApiResponse<List<UserTaskDTO>> refreshDailyTasks(@PathVariable String userId) {
        List<UserTaskDTO> tasks = userGrowthService.refreshDailyTasks(userId);
        return ApiResponse.success(tasks);
    }

    // ==================== 邀请奖励 ====================

    @Operation(summary = "获取用户邀请信息")
    @GetMapping("/invitation/user/{userId}")
    public ApiResponse<UserInvitationDTO> getUserInvitation(@PathVariable String userId) {
        UserInvitationDTO invitation = userGrowthService.getUserInvitation(userId);
        return ApiResponse.success(invitation);
    }

    @Operation(summary = "生成邀请码")
    @PostMapping("/invitation/user/{userId}/generate-code")
    public ApiResponse<InvitationCodeDTO> generateInvitationCode(@PathVariable String userId) {
        InvitationCodeDTO code = userGrowthService.generateInvitationCode(userId);
        return ApiResponse.success(code);
    }

    @Operation(summary = "使用邀请码")
    @PostMapping("/invitation/use-code")
    public ApiResponse<InvitationUseResultDTO> useInvitationCode(
            @Valid @RequestBody UseInvitationCodeRequestDTO requestDTO) {
        
        InvitationUseResultDTO result = userGrowthService.useInvitationCode(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取邀请记录")
    @GetMapping("/invitation/user/{userId}/records")
    public ApiResponse<List<InvitationRecordDTO>> getInvitationRecords(
            @PathVariable String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<InvitationRecordDTO> records = userGrowthService.getInvitationRecords(userId, page, size);
        return ApiResponse.success(records);
    }

    @Operation(summary = "获取邀请奖励")
    @GetMapping("/invitation/rewards")
    public ApiResponse<List<InvitationRewardConfigDTO>> getInvitationRewards() {
        List<InvitationRewardConfigDTO> rewards = userGrowthService.getInvitationRewards();
        return ApiResponse.success(rewards);
    }

    // ==================== 成长统计 ====================

    @Operation(summary = "获取用户成长统计")
    @GetMapping("/stats/user/{userId}")
    public ApiResponse<UserGrowthStatsDTO> getUserGrowthStats(@PathVariable String userId) {
        UserGrowthStatsDTO stats = userGrowthService.getUserGrowthStats(userId);
        return ApiResponse.success(stats);
    }

    @Operation(summary = "获取成长排行榜")
    @GetMapping("/leaderboard")
    public ApiResponse<List<GrowthLeaderboardDTO>> getGrowthLeaderboard(
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "50") int size) {
        
        List<GrowthLeaderboardDTO> leaderboard = userGrowthService.getGrowthLeaderboard(type, page, size);
        return ApiResponse.success(leaderboard);
    }

    @Operation(summary = "获取用户成长轨迹")
    @GetMapping("/timeline/user/{userId}")
    public ApiResponse<List<GrowthTimelineDTO>> getUserGrowthTimeline(
            @PathVariable String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<GrowthTimelineDTO> timeline = userGrowthService.getUserGrowthTimeline(userId, page, size);
        return ApiResponse.success(timeline);
    }

    // ==================== 特殊活动 ====================

    @Operation(summary = "获取成长活动列表")
    @GetMapping("/activities")
    public ApiResponse<List<GrowthActivityDTO>> getGrowthActivities(
            @RequestParam(required = false) String status) {
        
        List<GrowthActivityDTO> activities = userGrowthService.getGrowthActivities(status);
        return ApiResponse.success(activities);
    }

    @Operation(summary = "参与成长活动")
    @PostMapping("/activities/{activityId}/participate")
    public ApiResponse<GrowthActivityParticipationDTO> participateGrowthActivity(
            @PathVariable String activityId,
            @RequestParam String userId) {
        
        GrowthActivityParticipationDTO participation = userGrowthService.participateGrowthActivity(
                activityId, userId);
        return ApiResponse.success(participation);
    }

    // ==================== 系统管理 ====================

    @Operation(summary = "同步用户成长数据")
    @PostMapping("/sync/user/{userId}")
    public ApiResponse<UserGrowthSyncResultDTO> syncUserGrowthData(@PathVariable String userId) {
        UserGrowthSyncResultDTO result = userGrowthService.syncUserGrowthData(userId);
        return ApiResponse.success(result);
    }

    @Operation(summary = "重置用户成长数据")
    @PostMapping("/reset/user/{userId}")
    public ApiResponse<Void> resetUserGrowthData(
            @PathVariable String userId,
            @RequestParam String reason) {
        
        userGrowthService.resetUserGrowthData(userId, reason);
        return ApiResponse.success();
    }
}
