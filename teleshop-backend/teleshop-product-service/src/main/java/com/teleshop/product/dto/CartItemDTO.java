package com.teleshop.product.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 购物车商品数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CartItemDTO {

    /**
     * 购物车商品ID
     */
    private String id;

    /**
     * 购物车ID
     */
    private String cartId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品SKU ID
     */
    private String skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品副标题
     */
    private String productSubtitle;

    /**
     * 商品图片
     */
    private List<String> productImages;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 商品分类ID
     */
    private String categoryId;

    /**
     * 商品品牌ID
     */
    private String brandId;

    /**
     * 商家ID
     */
    private String merchantId;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 商品规格
     */
    private Map<String, String> specs;

    /**
     * 规格描述
     */
    private String specDescription;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 现价
     */
    private BigDecimal currentPrice;

    /**
     * 是否选中
     */
    private boolean selected;

    /**
     * 商品状态：VALID(有效), INVALID(失效), OUT_OF_STOCK(缺货), PRICE_CHANGED(价格变动)
     */
    private String status;

    /**
     * 库存数量
     */
    private Integer stockQuantity;

    /**
     * 最大购买数量
     */
    private Integer maxBuyQuantity;

    /**
     * 最小购买数量
     */
    private Integer minBuyQuantity;

    /**
     * 商品备注
     */
    private String note;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 价格变动信息
     */
    private PriceChangeInfo priceChangeInfo;

    /**
     * 促销信息
     */
    private PromotionInfo promotionInfo;

    /**
     * 配送信息
     */
    private ShippingInfo shippingInfo;

    /**
     * 扩展属性
     */
    private Map<String, Object> properties;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceChangeInfo {
        private BigDecimal oldPrice;
        private BigDecimal newPrice;
        private BigDecimal changeAmount;
        private String changeType; // INCREASE, DECREASE
        private LocalDateTime changeTime;
        private String reason;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PromotionInfo {
        private String promotionId;
        private String promotionName;
        private String promotionType;
        private BigDecimal discountAmount;
        private String description;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShippingInfo {
        private String shippingMethod;
        private BigDecimal shippingFee;
        private String estimatedDeliveryTime;
        private boolean freeShipping;
        private String shippingNote;
    }

    /**
     * 计算商品小计
     */
    public BigDecimal getSubtotal() {
        if (currentPrice != null && quantity != null) {
            return currentPrice.multiply(BigDecimal.valueOf(quantity));
        }
        return BigDecimal.ZERO;
    }

    /**
     * 计算节省金额
     */
    public BigDecimal getSavedAmount() {
        if (originalPrice != null && currentPrice != null && quantity != null) {
            BigDecimal savedPerItem = originalPrice.subtract(currentPrice);
            return savedPerItem.multiply(BigDecimal.valueOf(quantity));
        }
        return BigDecimal.ZERO;
    }

    /**
     * 检查是否有效
     */
    public boolean isValid() {
        return "VALID".equals(status);
    }

    /**
     * 检查是否缺货
     */
    public boolean isOutOfStock() {
        return "OUT_OF_STOCK".equals(status) || 
               (stockQuantity != null && stockQuantity < quantity);
    }

    /**
     * 检查是否价格变动
     */
    public boolean isPriceChanged() {
        return "PRICE_CHANGED".equals(status) || priceChangeInfo != null;
    }

    /**
     * 检查是否失效
     */
    public boolean isInvalid() {
        return "INVALID".equals(status);
    }

    /**
     * 检查数量是否超出限制
     */
    public boolean isQuantityExceeded() {
        if (maxBuyQuantity != null && quantity != null) {
            return quantity > maxBuyQuantity;
        }
        return false;
    }

    /**
     * 检查数量是否低于最小限制
     */
    public boolean isQuantityBelowMin() {
        if (minBuyQuantity != null && quantity != null) {
            return quantity < minBuyQuantity;
        }
        return false;
    }

    /**
     * 获取可用库存数量
     */
    public Integer getAvailableStock() {
        if (stockQuantity != null && maxBuyQuantity != null) {
            return Math.min(stockQuantity, maxBuyQuantity);
        }
        return stockQuantity;
    }

    /**
     * 检查是否有促销
     */
    public boolean hasPromotion() {
        return promotionInfo != null;
    }

    /**
     * 检查是否免运费
     */
    public boolean isFreeShipping() {
        return shippingInfo != null && shippingInfo.isFreeShipping();
    }
}
