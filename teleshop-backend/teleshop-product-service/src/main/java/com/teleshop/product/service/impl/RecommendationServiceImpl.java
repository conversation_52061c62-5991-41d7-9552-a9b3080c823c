package com.teleshop.product.service.impl;

import com.teleshop.product.dto.ProductDTO;
import com.teleshop.product.dto.RecommendationRequestDTO;
import com.teleshop.product.dto.UserBehaviorDTO;
import com.teleshop.product.entity.Product;
import com.teleshop.product.entity.UserBehavior;
import com.teleshop.product.repository.ProductRepository;
import com.teleshop.product.repository.UserBehaviorRepository;
import com.teleshop.product.service.RecommendationService;
import com.teleshop.product.service.ProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 商品推荐服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecommendationServiceImpl implements RecommendationService {

    private final ProductRepository productRepository;
    private final UserBehaviorRepository userBehaviorRepository;
    private final ProductService productService;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String RECOMMENDATION_CACHE_PREFIX = "recommendation:";
    private static final String USER_BEHAVIOR_CACHE_PREFIX = "user_behavior:";
    private static final int DEFAULT_CACHE_MINUTES = 30;

    @Override
    public List<ProductDTO> getPersonalizedRecommendations(String userId, String scene, int page, int size) {
        String cacheKey = RECOMMENDATION_CACHE_PREFIX + "personalized:" + userId + ":" + scene + ":" + page + ":" + size;
        
        // 尝试从缓存获取
        List<ProductDTO> cachedResult = getCachedRecommendations(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        List<ProductDTO> recommendations = new ArrayList<>();
        
        try {
            if (userId != null) {
                // 基于用户行为的个性化推荐
                recommendations = getPersonalizedByUserBehavior(userId, scene, page, size);
            }
            
            // 如果个性化推荐结果不足，补充热门商品
            if (recommendations.size() < size) {
                List<ProductDTO> hotProducts = getHotRecommendations(null, "week", 1, size - recommendations.size());
                recommendations.addAll(hotProducts);
            }
            
            // 去重并限制数量
            recommendations = deduplicateAndLimit(recommendations, size);
            
            // 缓存结果
            cacheRecommendations(cacheKey, recommendations, DEFAULT_CACHE_MINUTES);
            
        } catch (Exception e) {
            log.error("获取个性化推荐失败: userId={}, scene={}", userId, scene, e);
            // 降级到热门商品推荐
            recommendations = getHotRecommendations(null, "week", page, size);
        }
        
        return recommendations;
    }

    @Override
    public List<ProductDTO> getHotRecommendations(String categoryId, String timeRange, int page, int size) {
        String cacheKey = RECOMMENDATION_CACHE_PREFIX + "hot:" + categoryId + ":" + timeRange + ":" + page + ":" + size;
        
        List<ProductDTO> cachedResult = getCachedRecommendations(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        try {
            LocalDateTime startTime = getStartTimeByRange(timeRange);
            Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "salesCount", "viewCount"));
            
            List<Product> hotProducts;
            if (categoryId != null) {
                hotProducts = productRepository.findHotProductsByCategory(categoryId, startTime, pageable);
            } else {
                hotProducts = productRepository.findHotProducts(startTime, pageable);
            }
            
            List<ProductDTO> recommendations = hotProducts.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            cacheRecommendations(cacheKey, recommendations, DEFAULT_CACHE_MINUTES);
            return recommendations;
            
        } catch (Exception e) {
            log.error("获取热门商品推荐失败: categoryId={}, timeRange={}", categoryId, timeRange, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ProductDTO> getSimilarRecommendations(String productId, String algorithm, int limit) {
        String cacheKey = RECOMMENDATION_CACHE_PREFIX + "similar:" + productId + ":" + algorithm + ":" + limit;
        
        List<ProductDTO> cachedResult = getCachedRecommendations(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        try {
            Product targetProduct = productRepository.findById(productId).orElse(null);
            if (targetProduct == null) {
                return Collections.emptyList();
            }
            
            List<Product> similarProducts;
            if ("content_based".equals(algorithm)) {
                // 基于内容的相似推荐
                similarProducts = productRepository.findSimilarProductsByContent(
                        targetProduct.getCategoryId(), 
                        targetProduct.getBrandId(), 
                        targetProduct.getId(),
                        PageRequest.of(0, limit)
                );
            } else {
                // 基于协同过滤的相似推荐
                similarProducts = productRepository.findSimilarProductsByCollaborativeFiltering(
                        productId, 
                        PageRequest.of(0, limit)
                );
            }
            
            List<ProductDTO> recommendations = similarProducts.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            cacheRecommendations(cacheKey, recommendations, DEFAULT_CACHE_MINUTES);
            return recommendations;
            
        } catch (Exception e) {
            log.error("获取相似商品推荐失败: productId={}, algorithm={}", productId, algorithm, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ProductDTO> getHistoryBasedRecommendations(String userId, int historyDays, int limit) {
        String cacheKey = RECOMMENDATION_CACHE_PREFIX + "history:" + userId + ":" + historyDays + ":" + limit;
        
        List<ProductDTO> cachedResult = getCachedRecommendations(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        try {
            LocalDateTime startTime = LocalDateTime.now().minusDays(historyDays);
            
            // 获取用户浏览历史
            List<UserBehavior> userBehaviors = userBehaviorRepository.findByUserIdAndBehaviorTimeAfter(userId, startTime);
            
            // 分析用户偏好
            Map<String, Double> categoryPreferences = analyzeCategoryPreferences(userBehaviors);
            Map<String, Double> brandPreferences = analyzeBrandPreferences(userBehaviors);
            
            // 基于偏好推荐商品
            List<Product> recommendedProducts = productRepository.findProductsByPreferences(
                    categoryPreferences.keySet(),
                    brandPreferences.keySet(),
                    PageRequest.of(0, limit)
            );
            
            List<ProductDTO> recommendations = recommendedProducts.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            cacheRecommendations(cacheKey, recommendations, DEFAULT_CACHE_MINUTES);
            return recommendations;
            
        } catch (Exception e) {
            log.error("获取基于历史的推荐失败: userId={}, historyDays={}", userId, historyDays, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ProductDTO> getNewArrivals(String categoryId, int page, int size) {
        String cacheKey = RECOMMENDATION_CACHE_PREFIX + "new:" + categoryId + ":" + page + ":" + size;
        
        List<ProductDTO> cachedResult = getCachedRecommendations(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        try {
            Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createTime"));
            
            List<Product> newProducts;
            if (categoryId != null) {
                newProducts = productRepository.findByCategoryIdAndStatus(categoryId, "ACTIVE", pageable);
            } else {
                newProducts = productRepository.findByStatus("ACTIVE", pageable);
            }
            
            List<ProductDTO> recommendations = newProducts.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            cacheRecommendations(cacheKey, recommendations, DEFAULT_CACHE_MINUTES);
            return recommendations;
            
        } catch (Exception e) {
            log.error("获取新品推荐失败: categoryId={}", categoryId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ProductDTO> getGuessYouLike(String userId, int limit) {
        // 猜你喜欢是个性化推荐的一个特殊场景
        return getPersonalizedRecommendations(userId, "guess_you_like", 1, limit);
    }

    @Override
    public List<ProductDTO> getCartBasedRecommendations(String userId, int limit) {
        String cacheKey = RECOMMENDATION_CACHE_PREFIX + "cart:" + userId + ":" + limit;
        
        List<ProductDTO> cachedResult = getCachedRecommendations(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        try {
            // 获取用户购物车商品
            // 这里需要调用购物车服务获取购物车商品
            // List<CartItem> cartItems = cartService.getCartItems(userId);
            
            // 基于购物车商品推荐相关商品
            List<ProductDTO> recommendations = new ArrayList<>();
            
            // 暂时返回热门商品作为降级方案
            recommendations = getHotRecommendations(null, "week", 1, limit);
            
            cacheRecommendations(cacheKey, recommendations, DEFAULT_CACHE_MINUTES);
            return recommendations;
            
        } catch (Exception e) {
            log.error("获取购物车推荐失败: userId={}", userId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public void recordUserBehavior(UserBehaviorDTO behaviorDTO) {
        try {
            UserBehavior userBehavior = UserBehavior.builder()
                    .userId(behaviorDTO.getUserId())
                    .behaviorType(behaviorDTO.getBehaviorType())
                    .productId(behaviorDTO.getProductId())
                    .categoryId(behaviorDTO.getCategoryId())
                    .brandId(behaviorDTO.getBrandId())
                    .behaviorTime(behaviorDTO.getBehaviorTime())
                    .sessionId(behaviorDTO.getSessionId())
                    .deviceInfo(behaviorDTO.getDeviceInfo())
                    .pageSource(behaviorDTO.getPageSource())
                    .duration(behaviorDTO.getDuration())
                    .weight(behaviorDTO.getWeight())
                    .build();
            
            userBehaviorRepository.save(userBehavior);
            
            // 异步更新用户画像和推荐缓存
            refreshUserRecommendationCache(behaviorDTO.getUserId());
            
        } catch (Exception e) {
            log.error("记录用户行为失败: {}", behaviorDTO, e);
        }
    }

    @Override
    public Map<String, List<ProductDTO>> getBatchRecommendations(RecommendationRequestDTO requestDTO) {
        Map<String, List<ProductDTO>> result = new HashMap<>();
        
        for (RecommendationRequestDTO.RecommendationScene scene : requestDTO.getScenes()) {
            try {
                List<ProductDTO> recommendations = getRecommendationsByType(
                        requestDTO.getUserId(),
                        scene.getRecommendationType(),
                        scene.getSceneName(),
                        scene.getLimit() != null ? scene.getLimit() : 10,
                        scene.getSceneParams()
                );
                
                result.put(scene.getSceneName(), recommendations);
                
            } catch (Exception e) {
                log.error("批量获取推荐失败: scene={}", scene.getSceneName(), e);
                result.put(scene.getSceneName(), Collections.emptyList());
            }
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getRecommendationStats(String userId, int days) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            LocalDateTime startTime = LocalDateTime.now().minusDays(days);
            
            // 用户行为统计
            List<UserBehavior> behaviors = userBehaviorRepository.findByUserIdAndBehaviorTimeAfter(userId, startTime);
            
            stats.put("totalBehaviors", behaviors.size());
            stats.put("viewCount", behaviors.stream().filter(b -> "VIEW".equals(b.getBehaviorType())).count());
            stats.put("clickCount", behaviors.stream().filter(b -> "CLICK".equals(b.getBehaviorType())).count());
            stats.put("addToCartCount", behaviors.stream().filter(b -> "ADD_TO_CART".equals(b.getBehaviorType())).count());
            stats.put("purchaseCount", behaviors.stream().filter(b -> "PURCHASE".equals(b.getBehaviorType())).count());
            
            // 偏好分析
            Map<String, Double> categoryPreferences = analyzeCategoryPreferences(behaviors);
            Map<String, Double> brandPreferences = analyzeBrandPreferences(behaviors);
            
            stats.put("topCategories", categoryPreferences);
            stats.put("topBrands", brandPreferences);
            
        } catch (Exception e) {
            log.error("获取推荐统计失败: userId={}", userId, e);
        }
        
        return stats;
    }

    @Override
    public void refreshUserRecommendationCache(String userId) {
        try {
            // 删除用户相关的推荐缓存
            String pattern = RECOMMENDATION_CACHE_PREFIX + "*:" + userId + ":*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
            
        } catch (Exception e) {
            log.error("刷新用户推荐缓存失败: userId={}", userId, e);
        }
    }

    @Override
    public String getRecommendationExplanation(String userId, String productId, String recommendationType) {
        try {
            switch (recommendationType) {
                case "personalized":
                    return "根据您的浏览历史和购买偏好为您推荐";
                case "hot":
                    return "热门商品推荐";
                case "similar":
                    return "与您浏览的商品相似";
                case "history_based":
                    return "基于您的浏览历史推荐";
                case "new_arrivals":
                    return "最新上架商品";
                case "guess_you_like":
                    return "猜您喜欢";
                case "cart_based":
                    return "购买此商品的用户还买了";
                default:
                    return "为您推荐";
            }
        } catch (Exception e) {
            log.error("获取推荐解释失败: userId={}, productId={}, type={}", userId, productId, recommendationType, e);
            return "为您推荐";
        }
    }

    // 私有辅助方法
    private List<ProductDTO> getPersonalizedByUserBehavior(String userId, String scene, int page, int size) {
        // 实现基于用户行为的个性化推荐逻辑
        return Collections.emptyList();
    }

    private LocalDateTime getStartTimeByRange(String timeRange) {
        switch (timeRange) {
            case "day":
                return LocalDateTime.now().minusDays(1);
            case "week":
                return LocalDateTime.now().minusDays(7);
            case "month":
                return LocalDateTime.now().minusDays(30);
            default:
                return LocalDateTime.now().minusDays(7);
        }
    }

    private ProductDTO convertToDTO(Product product) {
        // 实现Product到ProductDTO的转换
        return ProductDTO.builder()
                .id(product.getId())
                .name(product.getName())
                .description(product.getDescription())
                .price(product.getPrice())
                .originalPrice(product.getOriginalPrice())
                .categoryId(product.getCategoryId())
                .brandId(product.getBrandId())
                .images(product.getImages())
                .status(product.getStatus())
                .salesCount(product.getSalesCount())
                .viewCount(product.getViewCount())
                .rating(product.getRating())
                .createTime(product.getCreateTime())
                .updateTime(product.getUpdateTime())
                .build();
    }

    private List<ProductDTO> deduplicateAndLimit(List<ProductDTO> products, int limit) {
        return products.stream()
                .distinct()
                .limit(limit)
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    private List<ProductDTO> getCachedRecommendations(String cacheKey) {
        try {
            return (List<ProductDTO>) redisTemplate.opsForValue().get(cacheKey);
        } catch (Exception e) {
            log.warn("获取推荐缓存失败: key={}", cacheKey, e);
            return null;
        }
    }

    private void cacheRecommendations(String cacheKey, List<ProductDTO> recommendations, int minutes) {
        try {
            redisTemplate.opsForValue().set(cacheKey, recommendations, minutes, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.warn("缓存推荐结果失败: key={}", cacheKey, e);
        }
    }

    private Map<String, Double> analyzeCategoryPreferences(List<UserBehavior> behaviors) {
        Map<String, Double> preferences = new HashMap<>();
        
        for (UserBehavior behavior : behaviors) {
            if (behavior.getCategoryId() != null) {
                double weight = getWeightByBehaviorType(behavior.getBehaviorType());
                preferences.merge(behavior.getCategoryId(), weight, Double::sum);
            }
        }
        
        return preferences.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .limit(10)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }

    private Map<String, Double> analyzeBrandPreferences(List<UserBehavior> behaviors) {
        Map<String, Double> preferences = new HashMap<>();
        
        for (UserBehavior behavior : behaviors) {
            if (behavior.getBrandId() != null) {
                double weight = getWeightByBehaviorType(behavior.getBehaviorType());
                preferences.merge(behavior.getBrandId(), weight, Double::sum);
            }
        }
        
        return preferences.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .limit(10)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }

    private double getWeightByBehaviorType(String behaviorType) {
        switch (behaviorType) {
            case "VIEW":
                return 1.0;
            case "CLICK":
                return 2.0;
            case "ADD_TO_CART":
                return 3.0;
            case "PURCHASE":
                return 5.0;
            case "SHARE":
                return 2.5;
            case "FAVORITE":
                return 3.5;
            default:
                return 1.0;
        }
    }

    private List<ProductDTO> getRecommendationsByType(String userId, String type, String scene, int limit, Map<String, Object> params) {
        switch (type) {
            case "personalized":
                return getPersonalizedRecommendations(userId, scene, 1, limit);
            case "hot":
                String categoryId = params != null ? (String) params.get("categoryId") : null;
                String timeRange = params != null ? (String) params.get("timeRange") : "week";
                return getHotRecommendations(categoryId, timeRange, 1, limit);
            case "similar":
                String productId = params != null ? (String) params.get("productId") : null;
                String algorithm = params != null ? (String) params.get("algorithm") : "content_based";
                return getSimilarRecommendations(productId, algorithm, limit);
            case "new_arrivals":
                String newCategoryId = params != null ? (String) params.get("categoryId") : null;
                return getNewArrivals(newCategoryId, 1, limit);
            case "guess_you_like":
                return getGuessYouLike(userId, limit);
            case "cart_based":
                return getCartBasedRecommendations(userId, limit);
            default:
                return getHotRecommendations(null, "week", 1, limit);
        }
    }
}
