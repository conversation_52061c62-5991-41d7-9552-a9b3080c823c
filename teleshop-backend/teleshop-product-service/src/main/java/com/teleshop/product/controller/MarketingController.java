package com.teleshop.product.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.product.dto.marketing.*;
import com.teleshop.product.service.MarketingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "营销活动", description = "营销活动相关接口")
@RestController
@RequestMapping("/api/v1/marketing")
@RequiredArgsConstructor
public class MarketingController {

    private final MarketingService marketingService;

    // ==================== 限时抢购 ====================

    @Operation(summary = "获取限时抢购活动列表")
    @GetMapping("/flash-sales")
    public ApiResponse<List<FlashSaleDTO>> getFlashSales(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status) {
        
        List<FlashSaleDTO> flashSales = marketingService.getFlashSales(page, size, status);
        return ApiResponse.success(flashSales);
    }

    @Operation(summary = "获取限时抢购详情")
    @GetMapping("/flash-sales/{saleId}")
    public ApiResponse<FlashSaleDTO> getFlashSaleDetail(@PathVariable String saleId) {
        FlashSaleDTO flashSale = marketingService.getFlashSaleDetail(saleId);
        return ApiResponse.success(flashSale);
    }

    @Operation(summary = "参与限时抢购")
    @PostMapping("/flash-sales/{saleId}/participate")
    public ApiResponse<FlashSaleParticipationDTO> participateFlashSale(
            @PathVariable String saleId,
            @Valid @RequestBody FlashSaleParticipationRequestDTO requestDTO) {
        
        FlashSaleParticipationDTO participation = marketingService.participateFlashSale(saleId, requestDTO);
        return ApiResponse.success(participation);
    }

    @Operation(summary = "获取用户抢购记录")
    @GetMapping("/flash-sales/user/{userId}/records")
    public ApiResponse<List<FlashSaleParticipationDTO>> getUserFlashSaleRecords(
            @PathVariable String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<FlashSaleParticipationDTO> records = marketingService.getUserFlashSaleRecords(userId, page, size);
        return ApiResponse.success(records);
    }

    // ==================== 团购活动 ====================

    @Operation(summary = "获取团购活动列表")
    @GetMapping("/group-buys")
    public ApiResponse<List<GroupBuyDTO>> getGroupBuys(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String status) {
        
        List<GroupBuyDTO> groupBuys = marketingService.getGroupBuys(page, size, categoryId, status);
        return ApiResponse.success(groupBuys);
    }

    @Operation(summary = "获取团购详情")
    @GetMapping("/group-buys/{groupBuyId}")
    public ApiResponse<GroupBuyDTO> getGroupBuyDetail(@PathVariable String groupBuyId) {
        GroupBuyDTO groupBuy = marketingService.getGroupBuyDetail(groupBuyId);
        return ApiResponse.success(groupBuy);
    }

    @Operation(summary = "发起团购")
    @PostMapping("/group-buys/{groupBuyId}/initiate")
    public ApiResponse<GroupBuyParticipationDTO> initiateGroupBuy(
            @PathVariable String groupBuyId,
            @Valid @RequestBody GroupBuyInitiateRequestDTO requestDTO) {
        
        GroupBuyParticipationDTO participation = marketingService.initiateGroupBuy(groupBuyId, requestDTO);
        return ApiResponse.success(participation);
    }

    @Operation(summary = "参与团购")
    @PostMapping("/group-buys/{groupBuyId}/join")
    public ApiResponse<GroupBuyParticipationDTO> joinGroupBuy(
            @PathVariable String groupBuyId,
            @Valid @RequestBody GroupBuyJoinRequestDTO requestDTO) {
        
        GroupBuyParticipationDTO participation = marketingService.joinGroupBuy(groupBuyId, requestDTO);
        return ApiResponse.success(participation);
    }

    @Operation(summary = "获取团购参与记录")
    @GetMapping("/group-buys/user/{userId}/records")
    public ApiResponse<List<GroupBuyParticipationDTO>> getUserGroupBuyRecords(
            @PathVariable String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<GroupBuyParticipationDTO> records = marketingService.getUserGroupBuyRecords(userId, page, size);
        return ApiResponse.success(records);
    }

    // ==================== 优惠券 ====================

    @Operation(summary = "获取可领取优惠券列表")
    @GetMapping("/coupons/available")
    public ApiResponse<List<CouponDTO>> getAvailableCoupons(
            @RequestParam(required = false) String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<CouponDTO> coupons = marketingService.getAvailableCoupons(userId, page, size);
        return ApiResponse.success(coupons);
    }

    @Operation(summary = "领取优惠券")
    @PostMapping("/coupons/{couponId}/claim")
    public ApiResponse<UserCouponDTO> claimCoupon(
            @PathVariable String couponId,
            @RequestParam String userId) {
        
        UserCouponDTO userCoupon = marketingService.claimCoupon(couponId, userId);
        return ApiResponse.success(userCoupon);
    }

    @Operation(summary = "获取用户优惠券列表")
    @GetMapping("/coupons/user/{userId}")
    public ApiResponse<List<UserCouponDTO>> getUserCoupons(
            @PathVariable String userId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<UserCouponDTO> userCoupons = marketingService.getUserCoupons(userId, status, page, size);
        return ApiResponse.success(userCoupons);
    }

    @Operation(summary = "使用优惠券")
    @PostMapping("/coupons/user/{userCouponId}/use")
    public ApiResponse<CouponUsageDTO> useCoupon(
            @PathVariable String userCouponId,
            @Valid @RequestBody CouponUsageRequestDTO requestDTO) {
        
        CouponUsageDTO usage = marketingService.useCoupon(userCouponId, requestDTO);
        return ApiResponse.success(usage);
    }

    // ==================== 积分系统 ====================

    @Operation(summary = "获取用户积分信息")
    @GetMapping("/points/user/{userId}")
    public ApiResponse<UserPointsDTO> getUserPoints(@PathVariable String userId) {
        UserPointsDTO userPoints = marketingService.getUserPoints(userId);
        return ApiResponse.success(userPoints);
    }

    @Operation(summary = "获取积分兑换商品列表")
    @GetMapping("/points/exchange/products")
    public ApiResponse<List<PointsExchangeProductDTO>> getPointsExchangeProducts(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String categoryId) {
        
        List<PointsExchangeProductDTO> products = marketingService.getPointsExchangeProducts(page, size, categoryId);
        return ApiResponse.success(products);
    }

    @Operation(summary = "积分兑换")
    @PostMapping("/points/exchange")
    public ApiResponse<PointsExchangeDTO> exchangePoints(
            @Valid @RequestBody PointsExchangeRequestDTO requestDTO) {
        
        PointsExchangeDTO exchange = marketingService.exchangePoints(requestDTO);
        return ApiResponse.success(exchange);
    }

    @Operation(summary = "获取积分记录")
    @GetMapping("/points/user/{userId}/records")
    public ApiResponse<List<PointsRecordDTO>> getPointsRecords(
            @PathVariable String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String type) {
        
        List<PointsRecordDTO> records = marketingService.getPointsRecords(userId, page, size, type);
        return ApiResponse.success(records);
    }

    // ==================== 会员等级体系 ====================

    @Operation(summary = "获取会员等级信息")
    @GetMapping("/membership/user/{userId}")
    public ApiResponse<UserMembershipDTO> getUserMembership(@PathVariable String userId) {
        UserMembershipDTO membership = marketingService.getUserMembership(userId);
        return ApiResponse.success(membership);
    }

    @Operation(summary = "获取会员等级列表")
    @GetMapping("/membership/levels")
    public ApiResponse<List<MembershipLevelDTO>> getMembershipLevels() {
        List<MembershipLevelDTO> levels = marketingService.getMembershipLevels();
        return ApiResponse.success(levels);
    }

    @Operation(summary = "获取会员权益")
    @GetMapping("/membership/benefits/{levelId}")
    public ApiResponse<List<MembershipBenefitDTO>> getMembershipBenefits(@PathVariable String levelId) {
        List<MembershipBenefitDTO> benefits = marketingService.getMembershipBenefits(levelId);
        return ApiResponse.success(benefits);
    }

    // ==================== 签到奖励 ====================

    @Operation(summary = "获取签到信息")
    @GetMapping("/checkin/user/{userId}")
    public ApiResponse<CheckinInfoDTO> getCheckinInfo(@PathVariable String userId) {
        CheckinInfoDTO checkinInfo = marketingService.getCheckinInfo(userId);
        return ApiResponse.success(checkinInfo);
    }

    @Operation(summary = "每日签到")
    @PostMapping("/checkin/user/{userId}/daily")
    public ApiResponse<CheckinRewardDTO> dailyCheckin(@PathVariable String userId) {
        CheckinRewardDTO reward = marketingService.dailyCheckin(userId);
        return ApiResponse.success(reward);
    }

    @Operation(summary = "获取签到记录")
    @GetMapping("/checkin/user/{userId}/records")
    public ApiResponse<List<CheckinRecordDTO>> getCheckinRecords(
            @PathVariable String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "30") int size) {
        
        List<CheckinRecordDTO> records = marketingService.getCheckinRecords(userId, page, size);
        return ApiResponse.success(records);
    }

    // ==================== 营销统计 ====================

    @Operation(summary = "获取营销活动统计")
    @GetMapping("/stats/activities")
    public ApiResponse<Map<String, Object>> getMarketingStats(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String activityType) {
        
        Map<String, Object> stats = marketingService.getMarketingStats(startDate, endDate, activityType);
        return ApiResponse.success(stats);
    }

    @Operation(summary = "获取用户营销参与统计")
    @GetMapping("/stats/user/{userId}")
    public ApiResponse<Map<String, Object>> getUserMarketingStats(@PathVariable String userId) {
        Map<String, Object> stats = marketingService.getUserMarketingStats(userId);
        return ApiResponse.success(stats);
    }
}
