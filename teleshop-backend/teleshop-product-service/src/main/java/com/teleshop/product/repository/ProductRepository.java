package com.teleshop.product.repository;

import com.teleshop.product.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品Repository
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long>, JpaSpecificationExecutor<Product> {

    /**
     * 根据商品编码查询商品
     */
    Product findByProductCode(String productCode);

    /**
     * 根据商家ID查询商品列表
     */
    Page<Product> findByMerchantIdOrderByCreatedDateDesc(Long merchantId, Pageable pageable);

    /**
     * 根据分类ID查询商品列表
     */
    Page<Product> findByCategoryIdAndStatusOrderBySortOrderDesc(Long categoryId, Integer status, Pageable pageable);

    /**
     * 根据品牌ID查询商品列表
     */
    Page<Product> findByBrandIdAndStatusOrderBySortOrderDesc(Long brandId, Integer status, Pageable pageable);

    /**
     * 根据商家ID和状态查询商品列表
     */
    Page<Product> findByMerchantIdAndStatusOrderByCreatedDateDesc(Long merchantId, Integer status, Pageable pageable);

    /**
     * 查询热销商品
     */
    @Query("SELECT p FROM Product p WHERE p.isHot = 1 AND p.status = :status ORDER BY p.salesCount DESC, p.sortOrder DESC")
    Page<Product> findHotProducts(@Param("status") Integer status, Pageable pageable);

    /**
     * 查询新品
     */
    @Query("SELECT p FROM Product p WHERE p.isNew = 1 AND p.status = :status ORDER BY p.createdDate DESC, p.sortOrder DESC")
    Page<Product> findNewProducts(@Param("status") Integer status, Pageable pageable);

    /**
     * 查询推荐商品
     */
    @Query("SELECT p FROM Product p WHERE p.isRecommended = 1 AND p.status = :status ORDER BY p.sortOrder DESC, p.salesCount DESC")
    Page<Product> findRecommendedProducts(@Param("status") Integer status, Pageable pageable);

    /**
     * 根据名称模糊查询商品
     */
    @Query("SELECT p FROM Product p WHERE p.name LIKE %:keyword% AND p.status = :status ORDER BY p.salesCount DESC")
    Page<Product> findByNameContaining(@Param("keyword") String keyword, @Param("status") Integer status, Pageable pageable);

    /**
     * 根据价格区间查询商品
     */
    @Query("SELECT p FROM Product p WHERE p.salePrice BETWEEN :minPrice AND :maxPrice AND p.status = :status ORDER BY p.salesCount DESC")
    Page<Product> findByPriceRange(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice, @Param("status") Integer status, Pageable pageable);

    /**
     * 查询分类下的商品（包含子分类）
     */
    @Query("SELECT p FROM Product p WHERE p.categoryId IN :categoryIds AND p.status = :status ORDER BY p.sortOrder DESC, p.salesCount DESC")
    Page<Product> findByCategoryIds(@Param("categoryIds") List<Long> categoryIds, @Param("status") Integer status, Pageable pageable);

    /**
     * 综合搜索商品
     */
    @Query("SELECT p FROM Product p WHERE " +
           "(:keyword IS NULL OR p.name LIKE %:keyword% OR p.subtitle LIKE %:keyword% OR p.seoKeywords LIKE %:keyword%) " +
           "AND (:categoryIds IS NULL OR p.categoryId IN :categoryIds) " +
           "AND (:brandIds IS NULL OR p.brandId IN :brandIds) " +
           "AND (:minPrice IS NULL OR p.salePrice >= :minPrice) " +
           "AND (:maxPrice IS NULL OR p.salePrice <= :maxPrice) " +
           "AND p.status = :status " +
           "ORDER BY p.sortOrder DESC, p.salesCount DESC")
    Page<Product> searchProducts(
            @Param("keyword") String keyword,
            @Param("categoryIds") List<Long> categoryIds,
            @Param("brandIds") List<Long> brandIds,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice,
            @Param("status") Integer status,
            Pageable pageable);

    /**
     * 更新商品浏览量
     */
    @Modifying
    @Query("UPDATE Product p SET p.viewCount = p.viewCount + 1 WHERE p.id = :productId")
    void incrementViewCount(@Param("productId") Long productId);

    /**
     * 更新商品收藏量
     */
    @Modifying
    @Query("UPDATE Product p SET p.favoriteCount = p.favoriteCount + :increment WHERE p.id = :productId")
    void updateFavoriteCount(@Param("productId") Long productId, @Param("increment") Integer increment);

    /**
     * 更新商品销量
     */
    @Modifying
    @Query("UPDATE Product p SET p.salesCount = p.salesCount + :increment WHERE p.id = :productId")
    void updateSalesCount(@Param("productId") Long productId, @Param("increment") Integer increment);

    /**
     * 更新库存数量
     */
    @Modifying
    @Query("UPDATE Product p SET p.stockQuantity = p.stockQuantity + :increment WHERE p.id = :productId")
    void updateStockQuantity(@Param("productId") Long productId, @Param("increment") Integer increment);

    /**
     * 查询库存不足的商品
     */
    @Query("SELECT p FROM Product p WHERE p.stockQuantity <= p.warningStock AND p.status = 1")
    List<Product> findLowStockProducts();

    /**
     * 查询商家的商品统计
     */
    @Query("SELECT " +
           "COUNT(p) as totalCount, " +
           "SUM(CASE WHEN p.status = 1 THEN 1 ELSE 0 END) as onSaleCount, " +
           "SUM(CASE WHEN p.status = 2 THEN 1 ELSE 0 END) as offSaleCount, " +
           "SUM(CASE WHEN p.auditStatus = 1 THEN 1 ELSE 0 END) as pendingAuditCount, " +
           "SUM(CASE WHEN p.stockQuantity <= p.warningStock THEN 1 ELSE 0 END) as lowStockCount " +
           "FROM Product p WHERE p.merchantId = :merchantId")
    Object[] getProductStatsByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 查询相似商品（同分类、同品牌）
     */
    @Query("SELECT p FROM Product p WHERE " +
           "(p.categoryId = :categoryId OR p.brandId = :brandId) " +
           "AND p.id != :productId AND p.status = :status " +
           "ORDER BY " +
           "CASE WHEN p.categoryId = :categoryId AND p.brandId = :brandId THEN 1 " +
           "     WHEN p.categoryId = :categoryId THEN 2 " +
           "     WHEN p.brandId = :brandId THEN 3 " +
           "     ELSE 4 END, " +
           "p.salesCount DESC")
    List<Product> findSimilarProducts(@Param("productId") Long productId, @Param("categoryId") Long categoryId, @Param("brandId") Long brandId, @Param("status") Integer status, Pageable pageable);

    /**
     * 根据商品ID列表查询商品
     */
    @Query("SELECT p FROM Product p WHERE p.id IN :productIds ORDER BY FIELD(p.id, :productIds)")
    List<Product> findByIdList(@Param("productIds") List<Long> productIds);

    // ==================== 推荐系统相关查询 ====================

    /**
     * 查询热门商品（基于时间范围）
     */
    @Query("SELECT p FROM Product p WHERE p.status = 1 " +
           "AND p.createdDate >= :startTime " +
           "AND (:categoryId IS NULL OR p.categoryId = :categoryId) " +
           "ORDER BY p.salesCount DESC, p.viewCount DESC, p.favoriteCount DESC")
    List<Product> findHotProducts(@Param("startTime") java.time.LocalDateTime startTime,
                                  @Param("categoryId") String categoryId,
                                  Pageable pageable);

    /**
     * 查询分类下的热门商品
     */
    @Query("SELECT p FROM Product p WHERE p.categoryId = :categoryId " +
           "AND p.status = 1 AND p.createdDate >= :startTime " +
           "ORDER BY p.salesCount DESC, p.viewCount DESC")
    List<Product> findHotProductsByCategory(@Param("categoryId") String categoryId,
                                           @Param("startTime") java.time.LocalDateTime startTime,
                                           Pageable pageable);

    /**
     * 基于内容的相似商品推荐
     */
    @Query("SELECT p FROM Product p WHERE " +
           "(p.categoryId = :categoryId OR p.brandId = :brandId) " +
           "AND p.id != :productId AND p.status = 1 " +
           "ORDER BY " +
           "CASE WHEN p.categoryId = :categoryId AND p.brandId = :brandId THEN 1 " +
           "     WHEN p.categoryId = :categoryId THEN 2 " +
           "     WHEN p.brandId = :brandId THEN 3 " +
           "     ELSE 4 END, " +
           "p.salesCount DESC, p.rating DESC")
    List<Product> findSimilarProductsByContent(@Param("categoryId") String categoryId,
                                              @Param("brandId") String brandId,
                                              @Param("productId") String productId,
                                              Pageable pageable);

    /**
     * 基于协同过滤的相似商品推荐（需要结合用户行为数据）
     */
    @Query("SELECT p FROM Product p WHERE p.id IN :relatedProductIds " +
           "AND p.status = 1 ORDER BY p.salesCount DESC, p.rating DESC")
    List<Product> findSimilarProductsByCollaborativeFiltering(@Param("relatedProductIds") List<String> relatedProductIds,
                                                              Pageable pageable);

    /**
     * 根据用户偏好查询商品
     */
    @Query("SELECT p FROM Product p WHERE " +
           "(:categoryIds IS NULL OR p.categoryId IN :categoryIds) " +
           "AND (:brandIds IS NULL OR p.brandId IN :brandIds) " +
           "AND p.status = 1 " +
           "ORDER BY p.salesCount DESC, p.rating DESC, p.viewCount DESC")
    List<Product> findProductsByPreferences(@Param("categoryIds") java.util.Set<String> categoryIds,
                                           @Param("brandIds") java.util.Set<String> brandIds,
                                           Pageable pageable);

    /**
     * 根据状态查询商品（支持分页）
     */
    @Query("SELECT p FROM Product p WHERE p.status = :status " +
           "AND (:categoryId IS NULL OR p.categoryId = :categoryId) " +
           "ORDER BY p.createdDate DESC")
    List<Product> findByStatus(@Param("status") String status,
                              @Param("categoryId") String categoryId,
                              Pageable pageable);

    /**
     * 根据分类ID和状态查询商品
     */
    List<Product> findByCategoryIdAndStatus(String categoryId, String status, Pageable pageable);

    /**
     * 根据状态查询商品
     */
    List<Product> findByStatus(String status, Pageable pageable);

    /**
     * 查询用户可能感兴趣的商品（基于价格区间和分类）
     */
    @Query("SELECT p FROM Product p WHERE " +
           "p.categoryId IN :categoryIds " +
           "AND p.salePrice BETWEEN :minPrice AND :maxPrice " +
           "AND p.status = 1 " +
           "ORDER BY p.rating DESC, p.salesCount DESC")
    List<Product> findProductsInPriceRangeAndCategories(@Param("categoryIds") List<String> categoryIds,
                                                        @Param("minPrice") java.math.BigDecimal minPrice,
                                                        @Param("maxPrice") java.math.BigDecimal maxPrice,
                                                        Pageable pageable);

    /**
     * 查询最近上架的新品
     */
    @Query("SELECT p FROM Product p WHERE p.status = 1 " +
           "AND (:categoryId IS NULL OR p.categoryId = :categoryId) " +
           "AND p.createdDate >= :recentDate " +
           "ORDER BY p.createdDate DESC, p.rating DESC")
    List<Product> findRecentNewProducts(@Param("categoryId") String categoryId,
                                       @Param("recentDate") java.time.LocalDateTime recentDate,
                                       Pageable pageable);

    /**
     * 查询高评分商品
     */
    @Query("SELECT p FROM Product p WHERE p.status = 1 " +
           "AND p.rating >= :minRating " +
           "AND (:categoryId IS NULL OR p.categoryId = :categoryId) " +
           "ORDER BY p.rating DESC, p.salesCount DESC")
    List<Product> findHighRatedProducts(@Param("minRating") Double minRating,
                                       @Param("categoryId") String categoryId,
                                       Pageable pageable);

    /**
     * 查询促销商品
     */
    @Query("SELECT p FROM Product p WHERE p.status = 1 " +
           "AND p.isPromotion = true " +
           "AND p.promotionStartTime <= :currentTime " +
           "AND p.promotionEndTime >= :currentTime " +
           "ORDER BY p.discountRate DESC, p.salesCount DESC")
    List<Product> findPromotionProducts(@Param("currentTime") java.time.LocalDateTime currentTime,
                                       Pageable pageable);
}