package com.teleshop.product.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import java.util.Map;

/**
 * 购物车操作数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CartOperationDTO {

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 商品ID
     */
    @NotBlank(message = "商品ID不能为空")
    private String productId;

    /**
     * 商品SKU ID
     */
    private String skuId;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于0")
    private Integer quantity;

    /**
     * 商品规格
     */
    private Map<String, String> specs;

    /**
     * 操作类型：ADD(添加), UPDATE(更新), REMOVE(删除)
     */
    private String operationType;

    /**
     * 是否立即选中
     */
    private Boolean selected;

    /**
     * 商品备注
     */
    private String note;

    /**
     * 来源页面
     */
    private String sourcePage;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
}

/**
 * 购物车分享数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class CartShareDTO {

    /**
     * 分享ID
     */
    private String shareId;

    /**
     * 分享链接
     */
    private String shareUrl;

    /**
     * 分享二维码
     */
    private String qrCode;

    /**
     * 分享标题
     */
    private String title;

    /**
     * 分享描述
     */
    private String description;

    /**
     * 分享图片
     */
    private String image;

    /**
     * 分享消息
     */
    private String message;

    /**
     * 分享用户ID
     */
    private String userId;

    /**
     * 分享用户名
     */
    private String userName;

    /**
     * 商品总数
     */
    private Integer totalItems;

    /**
     * 商品总价
     */
    private java.math.BigDecimal totalPrice;

    /**
     * 过期时间
     */
    private java.time.LocalDateTime expireTime;

    /**
     * 创建时间
     */
    private java.time.LocalDateTime createTime;

    /**
     * 访问次数
     */
    private Integer viewCount;

    /**
     * 复制次数
     */
    private Integer copyCount;

    /**
     * 分享状态
     */
    private String status;
}
