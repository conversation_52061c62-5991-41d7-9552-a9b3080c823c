package com.teleshop.product.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.product.dto.logistics.*;
import com.teleshop.product.service.LogisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "物流跟踪", description = "物流跟踪相关接口")
@RestController
@RequestMapping("/api/v1/logistics")
@RequiredArgsConstructor
public class LogisticsController {

    private final LogisticsService logisticsService;

    // ==================== 物流跟踪 ====================

    @Operation(summary = "获取订单物流信息")
    @GetMapping("/orders/{orderId}/tracking")
    public ApiResponse<LogisticsTrackingDTO> getOrderLogisticsTracking(@PathVariable String orderId) {
        LogisticsTrackingDTO tracking = logisticsService.getOrderLogisticsTracking(orderId);
        return ApiResponse.success(tracking);
    }

    @Operation(summary = "获取物流详细轨迹")
    @GetMapping("/tracking/{trackingNumber}")
    public ApiResponse<LogisticsTrackingDetailDTO> getLogisticsTrackingDetail(
            @PathVariable String trackingNumber,
            @RequestParam(required = false) String logisticsCompany) {
        
        LogisticsTrackingDetailDTO detail = logisticsService.getLogisticsTrackingDetail(
                trackingNumber, logisticsCompany);
        return ApiResponse.success(detail);
    }

    @Operation(summary = "实时同步物流信息")
    @PostMapping("/tracking/{trackingNumber}/sync")
    public ApiResponse<LogisticsTrackingDetailDTO> syncLogisticsTracking(
            @PathVariable String trackingNumber,
            @RequestParam String logisticsCompany) {
        
        LogisticsTrackingDetailDTO detail = logisticsService.syncLogisticsTracking(
                trackingNumber, logisticsCompany);
        return ApiResponse.success(detail);
    }

    @Operation(summary = "获取用户所有物流信息")
    @GetMapping("/user/{userId}/trackings")
    public ApiResponse<List<LogisticsTrackingDTO>> getUserLogisticsTrackings(
            @PathVariable String userId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<LogisticsTrackingDTO> trackings = logisticsService.getUserLogisticsTrackings(
                userId, status, page, size);
        return ApiResponse.success(trackings);
    }

    // ==================== 配送员联系 ====================

    @Operation(summary = "获取配送员信息")
    @GetMapping("/orders/{orderId}/delivery-person")
    public ApiResponse<DeliveryPersonDTO> getDeliveryPerson(@PathVariable String orderId) {
        DeliveryPersonDTO deliveryPerson = logisticsService.getDeliveryPerson(orderId);
        return ApiResponse.success(deliveryPerson);
    }

    @Operation(summary = "联系配送员")
    @PostMapping("/orders/{orderId}/contact-delivery-person")
    public ApiResponse<ContactResultDTO> contactDeliveryPerson(
            @PathVariable String orderId,
            @Valid @RequestBody ContactDeliveryPersonRequestDTO requestDTO) {
        
        ContactResultDTO result = logisticsService.contactDeliveryPerson(orderId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取配送员位置")
    @GetMapping("/orders/{orderId}/delivery-person/location")
    public ApiResponse<DeliveryPersonLocationDTO> getDeliveryPersonLocation(@PathVariable String orderId) {
        DeliveryPersonLocationDTO location = logisticsService.getDeliveryPersonLocation(orderId);
        return ApiResponse.success(location);
    }

    // ==================== 配送时间预约 ====================

    @Operation(summary = "获取可预约的配送时间")
    @GetMapping("/orders/{orderId}/available-delivery-times")
    public ApiResponse<List<DeliveryTimeSlotDTO>> getAvailableDeliveryTimes(@PathVariable String orderId) {
        List<DeliveryTimeSlotDTO> timeSlots = logisticsService.getAvailableDeliveryTimes(orderId);
        return ApiResponse.success(timeSlots);
    }

    @Operation(summary = "预约配送时间")
    @PostMapping("/orders/{orderId}/schedule-delivery")
    public ApiResponse<DeliveryScheduleDTO> scheduleDelivery(
            @PathVariable String orderId,
            @Valid @RequestBody DeliveryScheduleRequestDTO requestDTO) {
        
        DeliveryScheduleDTO schedule = logisticsService.scheduleDelivery(orderId, requestDTO);
        return ApiResponse.success(schedule);
    }

    @Operation(summary = "修改配送时间")
    @PutMapping("/orders/{orderId}/delivery-schedule")
    public ApiResponse<DeliveryScheduleDTO> updateDeliverySchedule(
            @PathVariable String orderId,
            @Valid @RequestBody DeliveryScheduleRequestDTO requestDTO) {
        
        DeliveryScheduleDTO schedule = logisticsService.updateDeliverySchedule(orderId, requestDTO);
        return ApiResponse.success(schedule);
    }

    @Operation(summary = "取消配送预约")
    @DeleteMapping("/orders/{orderId}/delivery-schedule")
    public ApiResponse<Void> cancelDeliverySchedule(
            @PathVariable String orderId,
            @RequestParam String reason) {
        
        logisticsService.cancelDeliverySchedule(orderId, reason);
        return ApiResponse.success();
    }

    // ==================== 签收确认 ====================

    @Operation(summary = "确认签收")
    @PostMapping("/orders/{orderId}/confirm-receipt")
    public ApiResponse<ReceiptConfirmationDTO> confirmReceipt(
            @PathVariable String orderId,
            @Valid @RequestBody ReceiptConfirmationRequestDTO requestDTO) {
        
        ReceiptConfirmationDTO confirmation = logisticsService.confirmReceipt(orderId, requestDTO);
        return ApiResponse.success(confirmation);
    }

    @Operation(summary = "代收签收")
    @PostMapping("/orders/{orderId}/proxy-receipt")
    public ApiResponse<ReceiptConfirmationDTO> proxyReceipt(
            @PathVariable String orderId,
            @Valid @RequestBody ProxyReceiptRequestDTO requestDTO) {
        
        ReceiptConfirmationDTO confirmation = logisticsService.proxyReceipt(orderId, requestDTO);
        return ApiResponse.success(confirmation);
    }

    @Operation(summary = "获取签收记录")
    @GetMapping("/orders/{orderId}/receipt-record")
    public ApiResponse<ReceiptRecordDTO> getReceiptRecord(@PathVariable String orderId) {
        ReceiptRecordDTO record = logisticsService.getReceiptRecord(orderId);
        return ApiResponse.success(record);
    }

    // ==================== 物流异常处理 ====================

    @Operation(summary = "报告物流异常")
    @PostMapping("/orders/{orderId}/report-exception")
    public ApiResponse<LogisticsExceptionDTO> reportLogisticsException(
            @PathVariable String orderId,
            @Valid @RequestBody LogisticsExceptionRequestDTO requestDTO) {
        
        LogisticsExceptionDTO exception = logisticsService.reportLogisticsException(orderId, requestDTO);
        return ApiResponse.success(exception);
    }

    @Operation(summary = "获取物流异常记录")
    @GetMapping("/orders/{orderId}/exceptions")
    public ApiResponse<List<LogisticsExceptionDTO>> getLogisticsExceptions(@PathVariable String orderId) {
        List<LogisticsExceptionDTO> exceptions = logisticsService.getLogisticsExceptions(orderId);
        return ApiResponse.success(exceptions);
    }

    @Operation(summary = "处理物流异常")
    @PostMapping("/exceptions/{exceptionId}/handle")
    public ApiResponse<LogisticsExceptionHandleResultDTO> handleLogisticsException(
            @PathVariable String exceptionId,
            @Valid @RequestBody LogisticsExceptionHandleRequestDTO requestDTO) {
        
        LogisticsExceptionHandleResultDTO result = logisticsService.handleLogisticsException(
                exceptionId, requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 物流评价 ====================

    @Operation(summary = "提交物流评价")
    @PostMapping("/orders/{orderId}/review")
    public ApiResponse<LogisticsReviewDTO> submitLogisticsReview(
            @PathVariable String orderId,
            @Valid @RequestBody LogisticsReviewRequestDTO requestDTO) {
        
        LogisticsReviewDTO review = logisticsService.submitLogisticsReview(orderId, requestDTO);
        return ApiResponse.success(review);
    }

    @Operation(summary = "获取物流评价")
    @GetMapping("/orders/{orderId}/review")
    public ApiResponse<LogisticsReviewDTO> getLogisticsReview(@PathVariable String orderId) {
        LogisticsReviewDTO review = logisticsService.getLogisticsReview(orderId);
        return ApiResponse.success(review);
    }

    // ==================== 物流公司管理 ====================

    @Operation(summary = "获取支持的物流公司列表")
    @GetMapping("/companies")
    public ApiResponse<List<LogisticsCompanyDTO>> getLogisticsCompanies() {
        List<LogisticsCompanyDTO> companies = logisticsService.getLogisticsCompanies();
        return ApiResponse.success(companies);
    }

    @Operation(summary = "获取物流公司详情")
    @GetMapping("/companies/{companyCode}")
    public ApiResponse<LogisticsCompanyDTO> getLogisticsCompany(@PathVariable String companyCode) {
        LogisticsCompanyDTO company = logisticsService.getLogisticsCompany(companyCode);
        return ApiResponse.success(company);
    }

    // ==================== 物流统计 ====================

    @Operation(summary = "获取用户物流统计")
    @GetMapping("/stats/user/{userId}")
    public ApiResponse<Map<String, Object>> getUserLogisticsStats(@PathVariable String userId) {
        Map<String, Object> stats = logisticsService.getUserLogisticsStats(userId);
        return ApiResponse.success(stats);
    }

    @Operation(summary = "获取物流服务质量统计")
    @GetMapping("/stats/quality")
    public ApiResponse<Map<String, Object>> getLogisticsQualityStats(
            @RequestParam(required = false) String companyCode,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        Map<String, Object> stats = logisticsService.getLogisticsQualityStats(
                companyCode, startDate, endDate);
        return ApiResponse.success(stats);
    }

    // ==================== 智能预测 ====================

    @Operation(summary = "预测配送时间")
    @GetMapping("/orders/{orderId}/delivery-prediction")
    public ApiResponse<DeliveryPredictionDTO> predictDeliveryTime(@PathVariable String orderId) {
        DeliveryPredictionDTO prediction = logisticsService.predictDeliveryTime(orderId);
        return ApiResponse.success(prediction);
    }

    @Operation(summary = "获取配送路径优化建议")
    @GetMapping("/orders/{orderId}/route-optimization")
    public ApiResponse<RouteOptimizationDTO> getRouteOptimization(@PathVariable String orderId) {
        RouteOptimizationDTO optimization = logisticsService.getRouteOptimization(orderId);
        return ApiResponse.success(optimization);
    }

    // ==================== 消息通知 ====================

    @Operation(summary = "设置物流通知偏好")
    @PostMapping("/user/{userId}/notification-preferences")
    public ApiResponse<Void> setLogisticsNotificationPreferences(
            @PathVariable String userId,
            @Valid @RequestBody LogisticsNotificationPreferencesDTO preferencesDTO) {
        
        logisticsService.setLogisticsNotificationPreferences(userId, preferencesDTO);
        return ApiResponse.success();
    }

    @Operation(summary = "获取物流通知偏好")
    @GetMapping("/user/{userId}/notification-preferences")
    public ApiResponse<LogisticsNotificationPreferencesDTO> getLogisticsNotificationPreferences(
            @PathVariable String userId) {
        
        LogisticsNotificationPreferencesDTO preferences = 
                logisticsService.getLogisticsNotificationPreferences(userId);
        return ApiResponse.success(preferences);
    }
}
