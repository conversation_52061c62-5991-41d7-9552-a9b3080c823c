package com.teleshop.product.repository;

import com.teleshop.product.entity.UserBehavior;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户行为数据仓库
 */
@Repository
public interface UserBehaviorRepository extends MongoRepository<UserBehavior, String> {

    /**
     * 根据用户ID和时间范围查询用户行为
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @return 用户行为列表
     */
    List<UserBehavior> findByUserIdAndBehaviorTimeAfter(String userId, LocalDateTime startTime);

    /**
     * 根据用户ID、行为类型和时间范围查询用户行为
     *
     * @param userId 用户ID
     * @param behaviorType 行为类型
     * @param startTime 开始时间
     * @return 用户行为列表
     */
    List<UserBehavior> findByUserIdAndBehaviorTypeAndBehaviorTimeAfter(String userId, String behaviorType, LocalDateTime startTime);

    /**
     * 根据商品ID查询用户行为
     *
     * @param productId 商品ID
     * @param startTime 开始时间
     * @param pageable 分页参数
     * @return 用户行为列表
     */
    List<UserBehavior> findByProductIdAndBehaviorTimeAfter(String productId, LocalDateTime startTime, Pageable pageable);

    /**
     * 根据分类ID查询用户行为
     *
     * @param categoryId 分类ID
     * @param startTime 开始时间
     * @param pageable 分页参数
     * @return 用户行为列表
     */
    List<UserBehavior> findByCategoryIdAndBehaviorTimeAfter(String categoryId, LocalDateTime startTime, Pageable pageable);

    /**
     * 根据品牌ID查询用户行为
     *
     * @param brandId 品牌ID
     * @param startTime 开始时间
     * @param pageable 分页参数
     * @return 用户行为列表
     */
    List<UserBehavior> findByBrandIdAndBehaviorTimeAfter(String brandId, LocalDateTime startTime, Pageable pageable);

    /**
     * 统计用户在指定时间范围内的行为数量
     *
     * @param userId 用户ID
     * @param behaviorType 行为类型
     * @param startTime 开始时间
     * @return 行为数量
     */
    long countByUserIdAndBehaviorTypeAndBehaviorTimeAfter(String userId, String behaviorType, LocalDateTime startTime);

    /**
     * 统计商品在指定时间范围内的行为数量
     *
     * @param productId 商品ID
     * @param behaviorType 行为类型
     * @param startTime 开始时间
     * @return 行为数量
     */
    long countByProductIdAndBehaviorTypeAndBehaviorTimeAfter(String productId, String behaviorType, LocalDateTime startTime);

    /**
     * 查询用户最近的行为记录
     *
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 用户行为列表
     */
    @Query(value = "{'userId': ?0}", sort = "{'behaviorTime': -1}")
    List<UserBehavior> findRecentBehaviorsByUserId(String userId, Pageable pageable);

    /**
     * 查询热门商品（基于用户行为）
     *
     * @param behaviorType 行为类型
     * @param startTime 开始时间
     * @param pageable 分页参数
     * @return 商品ID列表
     */
    @Query(value = "{'behaviorType': ?0, 'behaviorTime': {'$gte': ?1}}", 
           fields = "{'productId': 1}", 
           sort = "{'behaviorTime': -1}")
    List<String> findHotProductIds(String behaviorType, LocalDateTime startTime, Pageable pageable);

    /**
     * 查询用户偏好的分类
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @return 分类ID列表
     */
    @Query(value = "{'userId': ?0, 'behaviorTime': {'$gte': ?1}, 'categoryId': {'$ne': null}}", 
           fields = "{'categoryId': 1}")
    List<String> findUserPreferredCategories(String userId, LocalDateTime startTime);

    /**
     * 查询用户偏好的品牌
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @return 品牌ID列表
     */
    @Query(value = "{'userId': ?0, 'behaviorTime': {'$gte': ?1}, 'brandId': {'$ne': null}}", 
           fields = "{'brandId': 1}")
    List<String> findUserPreferredBrands(String userId, LocalDateTime startTime);

    /**
     * 删除过期的用户行为数据
     *
     * @param expireTime 过期时间
     */
    void deleteByBehaviorTimeBefore(LocalDateTime expireTime);

    /**
     * 查询会话内的用户行为
     *
     * @param sessionId 会话ID
     * @return 用户行为列表
     */
    List<UserBehavior> findBySessionIdOrderByBehaviorTimeAsc(String sessionId);

    /**
     * 查询同时浏览过两个商品的用户
     *
     * @param productId1 商品1ID
     * @param productId2 商品2ID
     * @param startTime 开始时间
     * @return 用户ID列表
     */
    @Query(value = "{'$and': [" +
           "{'productId': ?0, 'behaviorTime': {'$gte': ?2}}, " +
           "{'productId': ?1, 'behaviorTime': {'$gte': ?2}}" +
           "]}", 
           fields = "{'userId': 1}")
    List<String> findUsersViewedBothProducts(String productId1, String productId2, LocalDateTime startTime);
}
