package com.teleshop.product.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.product.dto.ProductDTO;
import com.teleshop.product.dto.RecommendationRequestDTO;
import com.teleshop.product.dto.UserBehaviorDTO;
import com.teleshop.product.service.RecommendationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "商品推荐", description = "商品推荐相关接口")
@RestController
@RequestMapping("/api/v1/products/recommendations")
@RequiredArgsConstructor
public class RecommendationController {

    private final RecommendationService recommendationService;

    @Operation(summary = "获取个性化推荐商品")
    @GetMapping("/personalized")
    public ApiResponse<List<ProductDTO>> getPersonalizedRecommendations(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String scene) {
        
        List<ProductDTO> recommendations = recommendationService.getPersonalizedRecommendations(userId, scene, page, size);
        return ApiResponse.success(recommendations);
    }

    @Operation(summary = "获取热门商品推荐")
    @GetMapping("/hot")
    public ApiResponse<List<ProductDTO>> getHotRecommendations(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String timeRange) {
        
        List<ProductDTO> recommendations = recommendationService.getHotRecommendations(categoryId, timeRange, page, size);
        return ApiResponse.success(recommendations);
    }

    @Operation(summary = "获取相似商品推荐")
    @GetMapping("/similar/{productId}")
    public ApiResponse<List<ProductDTO>> getSimilarRecommendations(
            @PathVariable String productId,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) String algorithm) {
        
        List<ProductDTO> recommendations = recommendationService.getSimilarRecommendations(productId, algorithm, limit);
        return ApiResponse.success(recommendations);
    }

    @Operation(summary = "基于浏览历史的推荐")
    @GetMapping("/history-based")
    public ApiResponse<List<ProductDTO>> getHistoryBasedRecommendations(
            @RequestParam String userId,
            @RequestParam(defaultValue = "20") int limit,
            @RequestParam(defaultValue = "7") int historyDays) {
        
        List<ProductDTO> recommendations = recommendationService.getHistoryBasedRecommendations(userId, historyDays, limit);
        return ApiResponse.success(recommendations);
    }

    @Operation(summary = "获取新品推荐")
    @GetMapping("/new-arrivals")
    public ApiResponse<List<ProductDTO>> getNewArrivals(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String categoryId) {
        
        List<ProductDTO> recommendations = recommendationService.getNewArrivals(categoryId, page, size);
        return ApiResponse.success(recommendations);
    }

    @Operation(summary = "获取猜你喜欢推荐")
    @GetMapping("/guess-you-like")
    public ApiResponse<List<ProductDTO>> getGuessYouLike(
            @RequestParam String userId,
            @RequestParam(defaultValue = "20") int limit) {
        
        List<ProductDTO> recommendations = recommendationService.getGuessYouLike(userId, limit);
        return ApiResponse.success(recommendations);
    }

    @Operation(summary = "获取购物车推荐")
    @GetMapping("/cart-based")
    public ApiResponse<List<ProductDTO>> getCartBasedRecommendations(
            @RequestParam String userId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<ProductDTO> recommendations = recommendationService.getCartBasedRecommendations(userId, limit);
        return ApiResponse.success(recommendations);
    }

    @Operation(summary = "记录用户行为")
    @PostMapping("/behavior")
    public ApiResponse<Void> recordUserBehavior(@Valid @RequestBody UserBehaviorDTO behaviorDTO) {
        recommendationService.recordUserBehavior(behaviorDTO);
        return ApiResponse.success();
    }

    @Operation(summary = "批量获取推荐")
    @PostMapping("/batch")
    public ApiResponse<Map<String, List<ProductDTO>>> getBatchRecommendations(
            @Valid @RequestBody RecommendationRequestDTO requestDTO) {
        
        Map<String, List<ProductDTO>> recommendations = recommendationService.getBatchRecommendations(requestDTO);
        return ApiResponse.success(recommendations);
    }

    @Operation(summary = "获取推荐统计信息")
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getRecommendationStats(
            @RequestParam String userId,
            @RequestParam(defaultValue = "7") int days) {
        
        Map<String, Object> stats = recommendationService.getRecommendationStats(userId, days);
        return ApiResponse.success(stats);
    }
}
