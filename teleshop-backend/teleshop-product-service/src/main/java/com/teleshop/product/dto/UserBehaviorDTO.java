package com.teleshop.product.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户行为数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserBehaviorDTO {

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 行为类型：VIEW(浏览), CLICK(点击), ADD_TO_CART(加购物车), PURCHASE(购买), SHARE(分享), FAVORITE(收藏)
     */
    @NotBlank(message = "行为类型不能为空")
    private String behaviorType;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品分类ID
     */
    private String categoryId;

    /**
     * 商品品牌ID
     */
    private String brandId;

    /**
     * 行为发生时间
     */
    @NotNull(message = "行为时间不能为空")
    private LocalDateTime behaviorTime;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 页面来源
     */
    private String pageSource;

    /**
     * 停留时长（秒）
     */
    private Long duration;

    /**
     * 行为权重（用于推荐算法）
     */
    private Double weight;

    /**
     * 扩展属性
     */
    private Map<String, Object> properties;

    /**
     * 地理位置信息
     */
    private LocationInfo location;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationInfo {
        private String country;
        private String province;
        private String city;
        private Double latitude;
        private Double longitude;
    }
}
