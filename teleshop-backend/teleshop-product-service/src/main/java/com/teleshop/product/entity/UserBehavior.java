package com.teleshop.product.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户行为实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "user_behaviors")
public class UserBehavior {

    @Id
    private String id;

    /**
     * 用户ID
     */
    @Indexed
    private String userId;

    /**
     * 行为类型：VIEW(浏览), CLICK(点击), ADD_TO_CART(加购物车), PURCHASE(购买), SHARE(分享), FAVORITE(收藏)
     */
    @Indexed
    private String behaviorType;

    /**
     * 商品ID
     */
    @Indexed
    private String productId;

    /**
     * 商品分类ID
     */
    @Indexed
    private String categoryId;

    /**
     * 商品品牌ID
     */
    @Indexed
    private String brandId;

    /**
     * 行为发生时间
     */
    @Indexed
    private LocalDateTime behaviorTime;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 页面来源
     */
    private String pageSource;

    /**
     * 停留时长（秒）
     */
    private Long duration;

    /**
     * 行为权重（用于推荐算法）
     */
    private Double weight;

    /**
     * 扩展属性
     */
    private Map<String, Object> properties;

    /**
     * 地理位置信息
     */
    private LocationInfo location;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationInfo {
        private String country;
        private String province;
        private String city;
        private Double latitude;
        private Double longitude;
    }
}
