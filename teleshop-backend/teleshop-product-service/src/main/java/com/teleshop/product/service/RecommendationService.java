package com.teleshop.product.service;

import com.teleshop.product.dto.ProductDTO;
import com.teleshop.product.dto.RecommendationRequestDTO;
import com.teleshop.product.dto.UserBehaviorDTO;

import java.util.List;
import java.util.Map;

/**
 * 商品推荐服务接口
 */
public interface RecommendationService {

    /**
     * 获取个性化推荐商品
     *
     * @param userId 用户ID
     * @param scene 推荐场景
     * @param page 页码
     * @param size 每页数量
     * @return 推荐商品列表
     */
    List<ProductDTO> getPersonalizedRecommendations(String userId, String scene, int page, int size);

    /**
     * 获取热门商品推荐
     *
     * @param categoryId 分类ID
     * @param timeRange 时间范围
     * @param page 页码
     * @param size 每页数量
     * @return 热门商品列表
     */
    List<ProductDTO> getHotRecommendations(String categoryId, String timeRange, int page, int size);

    /**
     * 获取相似商品推荐
     *
     * @param productId 商品ID
     * @param algorithm 推荐算法
     * @param limit 推荐数量
     * @return 相似商品列表
     */
    List<ProductDTO> getSimilarRecommendations(String productId, String algorithm, int limit);

    /**
     * 基于浏览历史的推荐
     *
     * @param userId 用户ID
     * @param historyDays 历史天数
     * @param limit 推荐数量
     * @return 推荐商品列表
     */
    List<ProductDTO> getHistoryBasedRecommendations(String userId, int historyDays, int limit);

    /**
     * 获取新品推荐
     *
     * @param categoryId 分类ID
     * @param page 页码
     * @param size 每页数量
     * @return 新品列表
     */
    List<ProductDTO> getNewArrivals(String categoryId, int page, int size);

    /**
     * 获取猜你喜欢推荐
     *
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐商品列表
     */
    List<ProductDTO> getGuessYouLike(String userId, int limit);

    /**
     * 获取购物车推荐
     *
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐商品列表
     */
    List<ProductDTO> getCartBasedRecommendations(String userId, int limit);

    /**
     * 记录用户行为
     *
     * @param behaviorDTO 用户行为数据
     */
    void recordUserBehavior(UserBehaviorDTO behaviorDTO);

    /**
     * 批量获取推荐
     *
     * @param requestDTO 批量推荐请求
     * @return 推荐结果映射
     */
    Map<String, List<ProductDTO>> getBatchRecommendations(RecommendationRequestDTO requestDTO);

    /**
     * 获取推荐统计信息
     *
     * @param userId 用户ID
     * @param days 统计天数
     * @return 统计信息
     */
    Map<String, Object> getRecommendationStats(String userId, int days);

    /**
     * 刷新用户推荐缓存
     *
     * @param userId 用户ID
     */
    void refreshUserRecommendationCache(String userId);

    /**
     * 获取推荐解释
     *
     * @param userId 用户ID
     * @param productId 商品ID
     * @param recommendationType 推荐类型
     * @return 推荐解释
     */
    String getRecommendationExplanation(String userId, String productId, String recommendationType);
}
