package com.teleshop.merchant.dto.marketing;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 商家优惠券DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantCouponDTO {

    /**
     * 优惠券ID
     */
    private String couponId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券类型：FIXED_AMOUNT(固定金额), PERCENTAGE(百分比), FREE_SHIPPING(免运费)
     */
    private String couponType;

    /**
     * 优惠券代码
     */
    private String couponCode;

    /**
     * 折扣值
     */
    private BigDecimal discountValue;

    /**
     * 最低消费金额
     */
    private BigDecimal minOrderAmount;

    /**
     * 最大折扣金额
     */
    private BigDecimal maxDiscountAmount;

    /**
     * 发行总量
     */
    private Integer totalQuantity;

    /**
     * 已领取数量
     */
    private Integer claimedQuantity;

    /**
     * 已使用数量
     */
    private Integer usedQuantity;

    /**
     * 每人限领数量
     */
    private Integer limitPerUser;

    /**
     * 有效期开始时间
     */
    private LocalDateTime validFrom;

    /**
     * 有效期结束时间
     */
    private LocalDateTime validTo;

    /**
     * 适用商品范围
     */
    private CouponScope scope;

    /**
     * 优惠券状态
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 使用统计
     */
    private CouponUsageStats usageStats;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CouponScope {
        private String scopeType; // ALL(全部商品), CATEGORY(指定分类), PRODUCT(指定商品), BRAND(指定品牌)
        private List<String> scopeValues;
        private List<String> excludeValues;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CouponUsageStats {
        private BigDecimal totalSavings;
        private Integer totalOrders;
        private BigDecimal averageOrderValue;
        private Double conversionRate;
    }
}

/**
 * 创建优惠券请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateCouponRequestDTO {

    /**
     * 优惠券名称
     */
    @NotBlank(message = "优惠券名称不能为空")
    private String couponName;

    /**
     * 优惠券类型
     */
    @NotBlank(message = "优惠券类型不能为空")
    private String couponType;

    /**
     * 折扣值
     */
    @NotNull(message = "折扣值不能为空")
    @DecimalMin(value = "0", message = "折扣值必须大于0")
    private BigDecimal discountValue;

    /**
     * 最低消费金额
     */
    private BigDecimal minOrderAmount;

    /**
     * 最大折扣金额
     */
    private BigDecimal maxDiscountAmount;

    /**
     * 发行总量
     */
    @NotNull(message = "发行总量不能为空")
    @Min(value = 1, message = "发行总量必须大于0")
    private Integer totalQuantity;

    /**
     * 每人限领数量
     */
    private Integer limitPerUser;

    /**
     * 有效期开始时间
     */
    @NotNull(message = "有效期开始时间不能为空")
    private LocalDateTime validFrom;

    /**
     * 有效期结束时间
     */
    @NotNull(message = "有效期结束时间不能为空")
    private LocalDateTime validTo;

    /**
     * 适用商品范围
     */
    private MerchantCouponDTO.CouponScope scope;

    /**
     * 优惠券描述
     */
    private String description;

    /**
     * 使用说明
     */
    private String usageInstructions;
}

/**
 * 限时折扣DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlashDiscountDTO {

    /**
     * 折扣ID
     */
    private String discountId;

    /**
     * 折扣名称
     */
    private String discountName;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 折扣价
     */
    private BigDecimal discountPrice;

    /**
     * 折扣类型：FIXED_AMOUNT(固定金额), PERCENTAGE(百分比)
     */
    private String discountType;

    /**
     * 折扣值
     */
    private BigDecimal discountValue;

    /**
     * 库存数量
     */
    private Integer stockQuantity;

    /**
     * 已售数量
     */
    private Integer soldQuantity;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 销售统计
     */
    private FlashDiscountStats stats;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FlashDiscountStats {
        private BigDecimal totalRevenue;
        private Integer totalOrders;
        private Double conversionRate;
        private Integer pageViews;
        private Integer uniqueVisitors;
    }
}

/**
 * 创建限时折扣请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateFlashDiscountRequestDTO {

    /**
     * 折扣名称
     */
    @NotBlank(message = "折扣名称不能为空")
    private String discountName;

    /**
     * 商品ID
     */
    @NotBlank(message = "商品ID不能为空")
    private String productId;

    /**
     * 折扣类型
     */
    @NotBlank(message = "折扣类型不能为空")
    private String discountType;

    /**
     * 折扣值
     */
    @NotNull(message = "折扣值不能为空")
    @DecimalMin(value = "0", message = "折扣值必须大于0")
    private BigDecimal discountValue;

    /**
     * 库存数量
     */
    @NotNull(message = "库存数量不能为空")
    @Min(value = 1, message = "库存数量必须大于0")
    private Integer stockQuantity;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 活动描述
     */
    private String description;
}

/**
 * 满减活动DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FullReductionActivityDTO {

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 满减规则
     */
    private List<FullReductionRule> rules;

    /**
     * 适用商品范围
     */
    private ActivityScope scope;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 参与订单数
     */
    private Integer participantOrders;

    /**
     * 优惠总金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FullReductionRule {
        private BigDecimal minAmount;
        private BigDecimal reductionAmount;
        private String description;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityScope {
        private String scopeType;
        private List<String> scopeValues;
        private List<String> excludeValues;
    }
}

/**
 * 团购活动DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantGroupBuyDTO {

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 团购价
     */
    private BigDecimal groupBuyPrice;

    /**
     * 成团人数
     */
    private Integer requiredParticipants;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 团购统计
     */
    private GroupBuyStats stats;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupBuyStats {
        private Integer totalGroups;
        private Integer successfulGroups;
        private Integer totalParticipants;
        private BigDecimal totalRevenue;
        private Double successRate;
    }
}

/**
 * 会员价格DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberPricingDTO {

    /**
     * 价格设置ID
     */
    private String pricingId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 会员等级价格
     */
    private List<MemberLevelPrice> memberPrices;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MemberLevelPrice {
        private String memberLevel;
        private String memberLevelName;
        private BigDecimal price;
        private BigDecimal discountAmount;
        private Double discountRate;
    }
}

/**
 * 营销概览DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketingOverviewDTO {

    /**
     * 活动总数
     */
    private Integer totalActivities;

    /**
     * 进行中活动数
     */
    private Integer activeActivities;

    /**
     * 总参与用户数
     */
    private Integer totalParticipants;

    /**
     * 总优惠金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 营销ROI
     */
    private Double marketingROI;

    /**
     * 转化率
     */
    private Double conversionRate;

    /**
     * 活动类型统计
     */
    private Map<String, ActivityTypeStats> activityTypeStats;

    /**
     * 趋势数据
     */
    private List<MarketingTrendData> trendData;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityTypeStats {
        private Integer count;
        private BigDecimal revenue;
        private Integer participants;
        private Double avgROI;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketingTrendData {
        private String date;
        private BigDecimal revenue;
        private Integer participants;
        private BigDecimal discountAmount;
        private Double roi;
    }
}
