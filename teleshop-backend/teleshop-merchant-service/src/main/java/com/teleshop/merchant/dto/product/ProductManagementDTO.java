package com.teleshop.merchant.dto.product;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 商品批量导入结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductBatchImportResultDTO {

    /**
     * 导入任务ID
     */
    private String taskId;

    /**
     * 总记录数
     */
    private Integer totalRecords;

    /**
     * 成功导入数
     */
    private Integer successCount;

    /**
     * 失败数
     */
    private Integer failureCount;

    /**
     * 跳过数
     */
    private Integer skipCount;

    /**
     * 导入状态
     */
    private String status;

    /**
     * 错误详情
     */
    private List<ImportErrorDetail> errors;

    /**
     * 导入开始时间
     */
    private LocalDateTime startTime;

    /**
     * 导入结束时间
     */
    private LocalDateTime endTime;

    /**
     * 错误报告下载链接
     */
    private String errorReportUrl;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImportErrorDetail {
        private Integer rowNumber;
        private String fieldName;
        private String errorMessage;
        private String originalValue;
    }
}

/**
 * 商品批量导出请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductBatchExportRequestDTO {

    /**
     * 导出类型：ALL(全部), SELECTED(选中), FILTERED(筛选)
     */
    @NotBlank(message = "导出类型不能为空")
    private String exportType;

    /**
     * 选中的商品ID列表
     */
    private List<String> selectedProductIds;

    /**
     * 筛选条件
     */
    private ProductFilterCondition filterCondition;

    /**
     * 导出字段
     */
    private List<String> exportFields;

    /**
     * 导出格式：EXCEL, CSV
     */
    private String exportFormat;

    /**
     * 是否包含图片
     */
    private Boolean includeImages;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductFilterCondition {
        private String categoryId;
        private String brandId;
        private String status;
        private BigDecimal minPrice;
        private BigDecimal maxPrice;
        private Integer minStock;
        private Integer maxStock;
        private LocalDateTime createTimeStart;
        private LocalDateTime createTimeEnd;
        private String keyword;
    }
}

/**
 * 商品批量操作结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductBatchOperationResultDTO {

    /**
     * 操作任务ID
     */
    private String taskId;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failureCount;

    /**
     * 操作状态
     */
    private String status;

    /**
     * 失败详情
     */
    private List<OperationFailureDetail> failures;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OperationFailureDetail {
        private String productId;
        private String productName;
        private String errorMessage;
    }
}

/**
 * 商品批量状态更新DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductBatchStatusUpdateDTO {

    /**
     * 商品ID列表
     */
    @NotNull(message = "商品ID列表不能为空")
    private List<String> productIds;

    /**
     * 目标状态
     */
    @NotBlank(message = "目标状态不能为空")
    private String targetStatus;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 是否发送通知
     */
    private Boolean sendNotification;
}

/**
 * 商品模板DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductTemplateDTO {

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 商家ID
     */
    private String merchantId;

    /**
     * 分类ID
     */
    private String categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 模板字段配置
     */
    private List<TemplateFieldConfig> fieldConfigs;

    /**
     * 默认值配置
     */
    private Map<String, Object> defaultValues;

    /**
     * 是否公共模板
     */
    private Boolean isPublic;

    /**
     * 使用次数
     */
    private Integer usageCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TemplateFieldConfig {
        private String fieldName;
        private String fieldLabel;
        private String fieldType;
        private Boolean required;
        private String defaultValue;
        private List<String> options;
        private String validation;
        private String description;
    }
}

/**
 * 创建商品模板DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateProductTemplateDTO {

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 分类ID
     */
    @NotBlank(message = "分类ID不能为空")
    private String categoryId;

    /**
     * 模板字段配置
     */
    @NotNull(message = "字段配置不能为空")
    private List<ProductTemplateDTO.TemplateFieldConfig> fieldConfigs;

    /**
     * 默认值配置
     */
    private Map<String, Object> defaultValues;

    /**
     * 是否公共模板
     */
    private Boolean isPublic;
}

/**
 * 更新商品模板DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateProductTemplateDTO {

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 模板字段配置
     */
    private List<ProductTemplateDTO.TemplateFieldConfig> fieldConfigs;

    /**
     * 默认值配置
     */
    private Map<String, Object> defaultValues;

    /**
     * 是否公共模板
     */
    private Boolean isPublic;
}

/**
 * 使用模板创建商品DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateProductFromTemplateDTO {

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String productName;

    /**
     * 字段值覆盖
     */
    private Map<String, Object> fieldValues;

    /**
     * 是否立即发布
     */
    private Boolean publishImmediately;
}

/**
 * 商品审核状态DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductAuditStatusDTO {

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 当前审核状态
     */
    private String currentStatus;

    /**
     * 审核阶段
     */
    private String auditStage;

    /**
     * 审核进度
     */
    private Integer auditProgress;

    /**
     * 审核员ID
     */
    private String auditorId;

    /**
     * 审核员姓名
     */
    private String auditorName;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 预计完成时间
     */
    private LocalDateTime estimatedCompleteTime;

    /**
     * 审核意见
     */
    private String auditComment;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 需要修改的字段
     */
    private List<String> fieldsToModify;

    /**
     * 审核检查项
     */
    private List<AuditCheckItem> checkItems;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuditCheckItem {
        private String itemName;
        private String itemDescription;
        private String status; // PENDING, PASSED, FAILED
        private String comment;
    }
}

/**
 * 商品审核历史DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductAuditHistoryDTO {

    /**
     * 审核记录ID
     */
    private String auditId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 审核类型
     */
    private String auditType;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 审核结果
     */
    private String auditResult;

    /**
     * 审核员信息
     */
    private AuditorInfo auditor;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核意见
     */
    private String auditComment;

    /**
     * 修改前数据
     */
    private Map<String, Object> beforeData;

    /**
     * 修改后数据
     */
    private Map<String, Object> afterData;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuditorInfo {
        private String auditorId;
        private String auditorName;
        private String auditorRole;
        private String department;
    }
}
