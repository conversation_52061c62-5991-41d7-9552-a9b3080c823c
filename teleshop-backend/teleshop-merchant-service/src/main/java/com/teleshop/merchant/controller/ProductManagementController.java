package com.teleshop.merchant.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.merchant.dto.product.*;
import com.teleshop.merchant.service.ProductManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "商品管理", description = "商家端商品管理相关接口")
@RestController
@RequestMapping("/api/v1/merchant/products")
@RequiredArgsConstructor
public class ProductManagementController {

    private final ProductManagementService productManagementService;

    // ==================== 商品批量操作 ====================

    @Operation(summary = "批量导入商品")
    @PostMapping("/batch-import")
    public ApiResponse<ProductBatchImportResultDTO> batchImportProducts(
            @RequestParam String merchantId,
            @RequestParam("file") MultipartFile file,
            @RequestParam(defaultValue = "false") boolean validateOnly) {
        
        ProductBatchImportResultDTO result = productManagementService.batchImportProducts(
                merchantId, file, validateOnly);
        return ApiResponse.success(result);
    }

    @Operation(summary = "批量导出商品")
    @PostMapping("/batch-export")
    public ApiResponse<String> batchExportProducts(
            @RequestParam String merchantId,
            @Valid @RequestBody ProductBatchExportRequestDTO requestDTO) {
        
        String downloadUrl = productManagementService.batchExportProducts(merchantId, requestDTO);
        return ApiResponse.success(downloadUrl);
    }

    @Operation(summary = "获取导入模板")
    @GetMapping("/import-template")
    public ApiResponse<String> getImportTemplate(@RequestParam String templateType) {
        String templateUrl = productManagementService.getImportTemplate(templateType);
        return ApiResponse.success(templateUrl);
    }

    @Operation(summary = "批量更新商品状态")
    @PutMapping("/batch-status")
    public ApiResponse<ProductBatchOperationResultDTO> batchUpdateProductStatus(
            @RequestParam String merchantId,
            @Valid @RequestBody ProductBatchStatusUpdateDTO requestDTO) {
        
        ProductBatchOperationResultDTO result = productManagementService.batchUpdateProductStatus(
                merchantId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "批量删除商品")
    @DeleteMapping("/batch-delete")
    public ApiResponse<ProductBatchOperationResultDTO> batchDeleteProducts(
            @RequestParam String merchantId,
            @RequestBody List<String> productIds) {
        
        ProductBatchOperationResultDTO result = productManagementService.batchDeleteProducts(
                merchantId, productIds);
        return ApiResponse.success(result);
    }

    // ==================== 商品模板管理 ====================

    @Operation(summary = "获取商品模板列表")
    @GetMapping("/templates")
    public ApiResponse<List<ProductTemplateDTO>> getProductTemplates(
            @RequestParam String merchantId,
            @RequestParam(required = false) String category) {
        
        List<ProductTemplateDTO> templates = productManagementService.getProductTemplates(merchantId, category);
        return ApiResponse.success(templates);
    }

    @Operation(summary = "创建商品模板")
    @PostMapping("/templates")
    public ApiResponse<ProductTemplateDTO> createProductTemplate(
            @RequestParam String merchantId,
            @Valid @RequestBody CreateProductTemplateDTO requestDTO) {
        
        ProductTemplateDTO template = productManagementService.createProductTemplate(merchantId, requestDTO);
        return ApiResponse.success(template);
    }

    @Operation(summary = "更新商品模板")
    @PutMapping("/templates/{templateId}")
    public ApiResponse<ProductTemplateDTO> updateProductTemplate(
            @PathVariable String templateId,
            @Valid @RequestBody UpdateProductTemplateDTO requestDTO) {
        
        ProductTemplateDTO template = productManagementService.updateProductTemplate(templateId, requestDTO);
        return ApiResponse.success(template);
    }

    @Operation(summary = "删除商品模板")
    @DeleteMapping("/templates/{templateId}")
    public ApiResponse<Void> deleteProductTemplate(@PathVariable String templateId) {
        productManagementService.deleteProductTemplate(templateId);
        return ApiResponse.success();
    }

    @Operation(summary = "使用模板创建商品")
    @PostMapping("/templates/{templateId}/create-product")
    public ApiResponse<ProductDTO> createProductFromTemplate(
            @PathVariable String templateId,
            @Valid @RequestBody CreateProductFromTemplateDTO requestDTO) {
        
        ProductDTO product = productManagementService.createProductFromTemplate(templateId, requestDTO);
        return ApiResponse.success(product);
    }

    // ==================== 商品审核管理 ====================

    @Operation(summary = "获取商品审核状态")
    @GetMapping("/{productId}/audit-status")
    public ApiResponse<ProductAuditStatusDTO> getProductAuditStatus(@PathVariable String productId) {
        ProductAuditStatusDTO auditStatus = productManagementService.getProductAuditStatus(productId);
        return ApiResponse.success(auditStatus);
    }

    @Operation(summary = "获取商品审核历史")
    @GetMapping("/{productId}/audit-history")
    public ApiResponse<List<ProductAuditHistoryDTO>> getProductAuditHistory(@PathVariable String productId) {
        List<ProductAuditHistoryDTO> auditHistory = productManagementService.getProductAuditHistory(productId);
        return ApiResponse.success(auditHistory);
    }

    @Operation(summary = "提交商品审核")
    @PostMapping("/{productId}/submit-audit")
    public ApiResponse<ProductAuditSubmissionDTO> submitProductAudit(
            @PathVariable String productId,
            @Valid @RequestBody ProductAuditSubmissionRequestDTO requestDTO) {
        
        ProductAuditSubmissionDTO submission = productManagementService.submitProductAudit(productId, requestDTO);
        return ApiResponse.success(submission);
    }

    @Operation(summary = "撤回商品审核")
    @PostMapping("/{productId}/withdraw-audit")
    public ApiResponse<Void> withdrawProductAudit(
            @PathVariable String productId,
            @RequestParam String reason) {
        
        productManagementService.withdrawProductAudit(productId, reason);
        return ApiResponse.success();
    }

    // ==================== 商品销售数据分析 ====================

    @Operation(summary = "获取商品销售统计")
    @GetMapping("/{productId}/sales-stats")
    public ApiResponse<ProductSalesStatsDTO> getProductSalesStats(
            @PathVariable String productId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        ProductSalesStatsDTO stats = productManagementService.getProductSalesStats(productId, startDate, endDate);
        return ApiResponse.success(stats);
    }

    @Operation(summary = "获取商品销售趋势")
    @GetMapping("/{productId}/sales-trend")
    public ApiResponse<List<ProductSalesTrendDTO>> getProductSalesTrend(
            @PathVariable String productId,
            @RequestParam String period,
            @RequestParam(defaultValue = "30") int days) {
        
        List<ProductSalesTrendDTO> trend = productManagementService.getProductSalesTrend(productId, period, days);
        return ApiResponse.success(trend);
    }

    @Operation(summary = "获取商品热度分析")
    @GetMapping("/{productId}/popularity-analysis")
    public ApiResponse<ProductPopularityAnalysisDTO> getProductPopularityAnalysis(@PathVariable String productId) {
        ProductPopularityAnalysisDTO analysis = productManagementService.getProductPopularityAnalysis(productId);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取商品转化率分析")
    @GetMapping("/{productId}/conversion-analysis")
    public ApiResponse<ProductConversionAnalysisDTO> getProductConversionAnalysis(
            @PathVariable String productId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        ProductConversionAnalysisDTO analysis = productManagementService.getProductConversionAnalysis(
                productId, startDate, endDate);
        return ApiResponse.success(analysis);
    }

    // ==================== 库存预警管理 ====================

    @Operation(summary = "获取库存预警设置")
    @GetMapping("/{productId}/stock-alert-settings")
    public ApiResponse<ProductStockAlertSettingsDTO> getStockAlertSettings(@PathVariable String productId) {
        ProductStockAlertSettingsDTO settings = productManagementService.getStockAlertSettings(productId);
        return ApiResponse.success(settings);
    }

    @Operation(summary = "更新库存预警设置")
    @PutMapping("/{productId}/stock-alert-settings")
    public ApiResponse<ProductStockAlertSettingsDTO> updateStockAlertSettings(
            @PathVariable String productId,
            @Valid @RequestBody UpdateStockAlertSettingsDTO requestDTO) {
        
        ProductStockAlertSettingsDTO settings = productManagementService.updateStockAlertSettings(
                productId, requestDTO);
        return ApiResponse.success(settings);
    }

    @Operation(summary = "获取库存预警列表")
    @GetMapping("/stock-alerts")
    public ApiResponse<List<ProductStockAlertDTO>> getStockAlerts(
            @RequestParam String merchantId,
            @RequestParam(required = false) String alertLevel,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<ProductStockAlertDTO> alerts = productManagementService.getStockAlerts(
                merchantId, alertLevel, page, size);
        return ApiResponse.success(alerts);
    }

    @Operation(summary = "处理库存预警")
    @PostMapping("/stock-alerts/{alertId}/handle")
    public ApiResponse<Void> handleStockAlert(
            @PathVariable String alertId,
            @Valid @RequestBody HandleStockAlertDTO requestDTO) {
        
        productManagementService.handleStockAlert(alertId, requestDTO);
        return ApiResponse.success();
    }

    // ==================== 价格策略管理 ====================

    @Operation(summary = "获取商品价格策略")
    @GetMapping("/{productId}/pricing-strategy")
    public ApiResponse<ProductPricingStrategyDTO> getProductPricingStrategy(@PathVariable String productId) {
        ProductPricingStrategyDTO strategy = productManagementService.getProductPricingStrategy(productId);
        return ApiResponse.success(strategy);
    }

    @Operation(summary = "更新商品价格策略")
    @PutMapping("/{productId}/pricing-strategy")
    public ApiResponse<ProductPricingStrategyDTO> updateProductPricingStrategy(
            @PathVariable String productId,
            @Valid @RequestBody UpdatePricingStrategyDTO requestDTO) {
        
        ProductPricingStrategyDTO strategy = productManagementService.updateProductPricingStrategy(
                productId, requestDTO);
        return ApiResponse.success(strategy);
    }

    @Operation(summary = "获取价格建议")
    @GetMapping("/{productId}/pricing-suggestion")
    public ApiResponse<ProductPricingSuggestionDTO> getProductPricingSuggestion(@PathVariable String productId) {
        ProductPricingSuggestionDTO suggestion = productManagementService.getProductPricingSuggestion(productId);
        return ApiResponse.success(suggestion);
    }

    @Operation(summary = "批量更新商品价格")
    @PutMapping("/batch-pricing")
    public ApiResponse<ProductBatchOperationResultDTO> batchUpdateProductPricing(
            @RequestParam String merchantId,
            @Valid @RequestBody ProductBatchPricingUpdateDTO requestDTO) {
        
        ProductBatchOperationResultDTO result = productManagementService.batchUpdateProductPricing(
                merchantId, requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 商品数据分析 ====================

    @Operation(summary = "获取商品综合分析报告")
    @GetMapping("/{productId}/comprehensive-analysis")
    public ApiResponse<ProductComprehensiveAnalysisDTO> getProductComprehensiveAnalysis(
            @PathVariable String productId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        ProductComprehensiveAnalysisDTO analysis = productManagementService.getProductComprehensiveAnalysis(
                productId, startDate, endDate);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取商品竞品分析")
    @GetMapping("/{productId}/competitor-analysis")
    public ApiResponse<ProductCompetitorAnalysisDTO> getProductCompetitorAnalysis(@PathVariable String productId) {
        ProductCompetitorAnalysisDTO analysis = productManagementService.getProductCompetitorAnalysis(productId);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取商品优化建议")
    @GetMapping("/{productId}/optimization-suggestions")
    public ApiResponse<List<ProductOptimizationSuggestionDTO>> getProductOptimizationSuggestions(
            @PathVariable String productId) {
        
        List<ProductOptimizationSuggestionDTO> suggestions = 
                productManagementService.getProductOptimizationSuggestions(productId);
        return ApiResponse.success(suggestions);
    }
}
