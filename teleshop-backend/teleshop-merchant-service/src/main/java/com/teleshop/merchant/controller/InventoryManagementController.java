package com.teleshop.merchant.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.merchant.dto.inventory.*;
import com.teleshop.merchant.service.InventoryManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "库存管理", description = "商家端库存管理相关接口")
@RestController
@RequestMapping("/api/v1/merchant/inventory")
@RequiredArgsConstructor
public class InventoryManagementController {

    private final InventoryManagementService inventoryManagementService;

    // ==================== 多仓库管理 ====================

    @Operation(summary = "获取仓库列表")
    @GetMapping("/warehouses")
    public ApiResponse<List<WarehouseDTO>> getWarehouses(
            @RequestParam String merchantId,
            @RequestParam(required = false) String status) {
        
        List<WarehouseDTO> warehouses = inventoryManagementService.getWarehouses(merchantId, status);
        return ApiResponse.success(warehouses);
    }

    @Operation(summary = "创建仓库")
    @PostMapping("/warehouses")
    public ApiResponse<WarehouseDTO> createWarehouse(
            @RequestParam String merchantId,
            @Valid @RequestBody CreateWarehouseRequestDTO requestDTO) {
        
        WarehouseDTO warehouse = inventoryManagementService.createWarehouse(merchantId, requestDTO);
        return ApiResponse.success(warehouse);
    }

    @Operation(summary = "更新仓库信息")
    @PutMapping("/warehouses/{warehouseId}")
    public ApiResponse<WarehouseDTO> updateWarehouse(
            @PathVariable String warehouseId,
            @Valid @RequestBody UpdateWarehouseRequestDTO requestDTO) {
        
        WarehouseDTO warehouse = inventoryManagementService.updateWarehouse(warehouseId, requestDTO);
        return ApiResponse.success(warehouse);
    }

    @Operation(summary = "删除仓库")
    @DeleteMapping("/warehouses/{warehouseId}")
    public ApiResponse<Void> deleteWarehouse(@PathVariable String warehouseId) {
        inventoryManagementService.deleteWarehouse(warehouseId);
        return ApiResponse.success();
    }

    @Operation(summary = "获取仓库库存概览")
    @GetMapping("/warehouses/{warehouseId}/overview")
    public ApiResponse<WarehouseInventoryOverviewDTO> getWarehouseInventoryOverview(
            @PathVariable String warehouseId) {
        
        WarehouseInventoryOverviewDTO overview = inventoryManagementService.getWarehouseInventoryOverview(warehouseId);
        return ApiResponse.success(overview);
    }

    // ==================== 库存查询 ====================

    @Operation(summary = "获取商品库存列表")
    @GetMapping("/stocks")
    public ApiResponse<List<ProductStockDTO>> getProductStocks(
            @RequestParam String merchantId,
            @RequestParam(required = false) String warehouseId,
            @RequestParam(required = false) String productId,
            @RequestParam(required = false) String alertLevel,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<ProductStockDTO> stocks = inventoryManagementService.getProductStocks(
                merchantId, warehouseId, productId, alertLevel, page, size);
        return ApiResponse.success(stocks);
    }

    @Operation(summary = "获取商品库存详情")
    @GetMapping("/stocks/{productId}")
    public ApiResponse<ProductStockDetailDTO> getProductStockDetail(
            @PathVariable String productId,
            @RequestParam(required = false) String warehouseId) {
        
        ProductStockDetailDTO detail = inventoryManagementService.getProductStockDetail(productId, warehouseId);
        return ApiResponse.success(detail);
    }

    @Operation(summary = "获取库存变动记录")
    @GetMapping("/stocks/{productId}/movements")
    public ApiResponse<List<StockMovementDTO>> getStockMovements(
            @PathVariable String productId,
            @RequestParam(required = false) String warehouseId,
            @RequestParam(required = false) String movementType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<StockMovementDTO> movements = inventoryManagementService.getStockMovements(
                productId, warehouseId, movementType, startDate, endDate, page, size);
        return ApiResponse.success(movements);
    }

    // ==================== 库存调整 ====================

    @Operation(summary = "调整库存")
    @PostMapping("/stocks/adjust")
    public ApiResponse<StockAdjustmentResultDTO> adjustStock(
            @Valid @RequestBody StockAdjustmentRequestDTO requestDTO) {
        
        StockAdjustmentResultDTO result = inventoryManagementService.adjustStock(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "批量调整库存")
    @PostMapping("/stocks/batch-adjust")
    public ApiResponse<BatchStockAdjustmentResultDTO> batchAdjustStock(
            @Valid @RequestBody BatchStockAdjustmentRequestDTO requestDTO) {
        
        BatchStockAdjustmentResultDTO result = inventoryManagementService.batchAdjustStock(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "库存入库")
    @PostMapping("/stocks/inbound")
    public ApiResponse<StockInboundResultDTO> stockInbound(
            @Valid @RequestBody StockInboundRequestDTO requestDTO) {
        
        StockInboundResultDTO result = inventoryManagementService.stockInbound(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "库存出库")
    @PostMapping("/stocks/outbound")
    public ApiResponse<StockOutboundResultDTO> stockOutbound(
            @Valid @RequestBody StockOutboundRequestDTO requestDTO) {
        
        StockOutboundResultDTO result = inventoryManagementService.stockOutbound(requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 库存预警 ====================

    @Operation(summary = "获取库存预警设置")
    @GetMapping("/alerts/settings")
    public ApiResponse<List<StockAlertSettingDTO>> getStockAlertSettings(
            @RequestParam String merchantId,
            @RequestParam(required = false) String warehouseId,
            @RequestParam(required = false) String productId) {
        
        List<StockAlertSettingDTO> settings = inventoryManagementService.getStockAlertSettings(
                merchantId, warehouseId, productId);
        return ApiResponse.success(settings);
    }

    @Operation(summary = "设置库存预警")
    @PostMapping("/alerts/settings")
    public ApiResponse<StockAlertSettingDTO> setStockAlertSetting(
            @Valid @RequestBody SetStockAlertSettingRequestDTO requestDTO) {
        
        StockAlertSettingDTO setting = inventoryManagementService.setStockAlertSetting(requestDTO);
        return ApiResponse.success(setting);
    }

    @Operation(summary = "批量设置库存预警")
    @PostMapping("/alerts/settings/batch")
    public ApiResponse<BatchStockAlertSettingResultDTO> batchSetStockAlertSettings(
            @Valid @RequestBody BatchStockAlertSettingRequestDTO requestDTO) {
        
        BatchStockAlertSettingResultDTO result = inventoryManagementService.batchSetStockAlertSettings(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取库存预警列表")
    @GetMapping("/alerts")
    public ApiResponse<List<StockAlertDTO>> getStockAlerts(
            @RequestParam String merchantId,
            @RequestParam(required = false) String warehouseId,
            @RequestParam(required = false) String alertLevel,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<StockAlertDTO> alerts = inventoryManagementService.getStockAlerts(
                merchantId, warehouseId, alertLevel, status, page, size);
        return ApiResponse.success(alerts);
    }

    @Operation(summary = "处理库存预警")
    @PostMapping("/alerts/{alertId}/handle")
    public ApiResponse<Void> handleStockAlert(
            @PathVariable String alertId,
            @Valid @RequestBody HandleStockAlertRequestDTO requestDTO) {
        
        inventoryManagementService.handleStockAlert(alertId, requestDTO);
        return ApiResponse.success();
    }

    // ==================== 库存盘点 ====================

    @Operation(summary = "创建盘点任务")
    @PostMapping("/stocktaking")
    public ApiResponse<StocktakingTaskDTO> createStocktakingTask(
            @Valid @RequestBody CreateStocktakingTaskRequestDTO requestDTO) {
        
        StocktakingTaskDTO task = inventoryManagementService.createStocktakingTask(requestDTO);
        return ApiResponse.success(task);
    }

    @Operation(summary = "获取盘点任务列表")
    @GetMapping("/stocktaking")
    public ApiResponse<List<StocktakingTaskDTO>> getStocktakingTasks(
            @RequestParam String merchantId,
            @RequestParam(required = false) String warehouseId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<StocktakingTaskDTO> tasks = inventoryManagementService.getStocktakingTasks(
                merchantId, warehouseId, status, page, size);
        return ApiResponse.success(tasks);
    }

    @Operation(summary = "获取盘点任务详情")
    @GetMapping("/stocktaking/{taskId}")
    public ApiResponse<StocktakingTaskDetailDTO> getStocktakingTaskDetail(@PathVariable String taskId) {
        StocktakingTaskDetailDTO detail = inventoryManagementService.getStocktakingTaskDetail(taskId);
        return ApiResponse.success(detail);
    }

    @Operation(summary = "开始盘点")
    @PostMapping("/stocktaking/{taskId}/start")
    public ApiResponse<Void> startStocktaking(@PathVariable String taskId) {
        inventoryManagementService.startStocktaking(taskId);
        return ApiResponse.success();
    }

    @Operation(summary = "提交盘点记录")
    @PostMapping("/stocktaking/{taskId}/records")
    public ApiResponse<StocktakingRecordDTO> submitStocktakingRecord(
            @PathVariable String taskId,
            @Valid @RequestBody SubmitStocktakingRecordRequestDTO requestDTO) {
        
        StocktakingRecordDTO record = inventoryManagementService.submitStocktakingRecord(taskId, requestDTO);
        return ApiResponse.success(record);
    }

    @Operation(summary = "批量导入盘点记录")
    @PostMapping("/stocktaking/{taskId}/batch-import")
    public ApiResponse<BatchStocktakingImportResultDTO> batchImportStocktakingRecords(
            @PathVariable String taskId,
            @RequestParam("file") MultipartFile file) {
        
        BatchStocktakingImportResultDTO result = inventoryManagementService.batchImportStocktakingRecords(taskId, file);
        return ApiResponse.success(result);
    }

    @Operation(summary = "完成盘点")
    @PostMapping("/stocktaking/{taskId}/complete")
    public ApiResponse<StocktakingCompletionResultDTO> completeStocktaking(
            @PathVariable String taskId,
            @Valid @RequestBody CompleteStocktakingRequestDTO requestDTO) {
        
        StocktakingCompletionResultDTO result = inventoryManagementService.completeStocktaking(taskId, requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 库存调拨 ====================

    @Operation(summary = "创建调拨单")
    @PostMapping("/transfers")
    public ApiResponse<StockTransferDTO> createStockTransfer(
            @Valid @RequestBody CreateStockTransferRequestDTO requestDTO) {
        
        StockTransferDTO transfer = inventoryManagementService.createStockTransfer(requestDTO);
        return ApiResponse.success(transfer);
    }

    @Operation(summary = "获取调拨单列表")
    @GetMapping("/transfers")
    public ApiResponse<List<StockTransferDTO>> getStockTransfers(
            @RequestParam String merchantId,
            @RequestParam(required = false) String fromWarehouseId,
            @RequestParam(required = false) String toWarehouseId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<StockTransferDTO> transfers = inventoryManagementService.getStockTransfers(
                merchantId, fromWarehouseId, toWarehouseId, status, page, size);
        return ApiResponse.success(transfers);
    }

    @Operation(summary = "获取调拨单详情")
    @GetMapping("/transfers/{transferId}")
    public ApiResponse<StockTransferDetailDTO> getStockTransferDetail(@PathVariable String transferId) {
        StockTransferDetailDTO detail = inventoryManagementService.getStockTransferDetail(transferId);
        return ApiResponse.success(detail);
    }

    @Operation(summary = "审核调拨单")
    @PostMapping("/transfers/{transferId}/approve")
    public ApiResponse<Void> approveStockTransfer(
            @PathVariable String transferId,
            @Valid @RequestBody ApproveStockTransferRequestDTO requestDTO) {
        
        inventoryManagementService.approveStockTransfer(transferId, requestDTO);
        return ApiResponse.success();
    }

    @Operation(summary = "执行调拨")
    @PostMapping("/transfers/{transferId}/execute")
    public ApiResponse<StockTransferExecutionResultDTO> executeStockTransfer(@PathVariable String transferId) {
        StockTransferExecutionResultDTO result = inventoryManagementService.executeStockTransfer(transferId);
        return ApiResponse.success(result);
    }

    // ==================== 安全库存 ====================

    @Operation(summary = "获取安全库存设置")
    @GetMapping("/safety-stock")
    public ApiResponse<List<SafetyStockSettingDTO>> getSafetyStockSettings(
            @RequestParam String merchantId,
            @RequestParam(required = false) String warehouseId,
            @RequestParam(required = false) String productId) {
        
        List<SafetyStockSettingDTO> settings = inventoryManagementService.getSafetyStockSettings(
                merchantId, warehouseId, productId);
        return ApiResponse.success(settings);
    }

    @Operation(summary = "设置安全库存")
    @PostMapping("/safety-stock")
    public ApiResponse<SafetyStockSettingDTO> setSafetyStockSetting(
            @Valid @RequestBody SetSafetyStockSettingRequestDTO requestDTO) {
        
        SafetyStockSettingDTO setting = inventoryManagementService.setSafetyStockSetting(requestDTO);
        return ApiResponse.success(setting);
    }

    @Operation(summary = "批量设置安全库存")
    @PostMapping("/safety-stock/batch")
    public ApiResponse<BatchSafetyStockSettingResultDTO> batchSetSafetyStockSettings(
            @Valid @RequestBody BatchSafetyStockSettingRequestDTO requestDTO) {
        
        BatchSafetyStockSettingResultDTO result = inventoryManagementService.batchSetSafetyStockSettings(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取安全库存分析")
    @GetMapping("/safety-stock/analysis")
    public ApiResponse<SafetyStockAnalysisDTO> getSafetyStockAnalysis(
            @RequestParam String merchantId,
            @RequestParam(required = false) String warehouseId,
            @RequestParam(required = false) String productId) {
        
        SafetyStockAnalysisDTO analysis = inventoryManagementService.getSafetyStockAnalysis(
                merchantId, warehouseId, productId);
        return ApiResponse.success(analysis);
    }

    // ==================== 库存报表 ====================

    @Operation(summary = "获取库存报表")
    @GetMapping("/reports")
    public ApiResponse<List<InventoryReportDTO>> getInventoryReports(
            @RequestParam String merchantId,
            @RequestParam(required = false) String reportType,
            @RequestParam(required = false) String warehouseId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        List<InventoryReportDTO> reports = inventoryManagementService.getInventoryReports(
                merchantId, reportType, warehouseId, startDate, endDate);
        return ApiResponse.success(reports);
    }

    @Operation(summary = "导出库存报表")
    @PostMapping("/reports/export")
    public ApiResponse<String> exportInventoryReport(
            @Valid @RequestBody ExportInventoryReportRequestDTO requestDTO) {
        
        String downloadUrl = inventoryManagementService.exportInventoryReport(requestDTO);
        return ApiResponse.success(downloadUrl);
    }

    @Operation(summary = "获取库存分析")
    @GetMapping("/analysis")
    public ApiResponse<InventoryAnalysisDTO> getInventoryAnalysis(
            @RequestParam String merchantId,
            @RequestParam(required = false) String warehouseId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        InventoryAnalysisDTO analysis = inventoryManagementService.getInventoryAnalysis(
                merchantId, warehouseId, startDate, endDate);
        return ApiResponse.success(analysis);
    }
}
