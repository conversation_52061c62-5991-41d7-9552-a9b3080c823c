package com.teleshop.merchant.dto.inventory;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 仓库DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseDTO {

    /**
     * 仓库ID
     */
    private String warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库代码
     */
    private String warehouseCode;

    /**
     * 仓库类型：MAIN(主仓), BRANCH(分仓), VIRTUAL(虚拟仓)
     */
    private String warehouseType;

    /**
     * 仓库地址
     */
    private WarehouseAddress address;

    /**
     * 联系人信息
     */
    private ContactInfo contactInfo;

    /**
     * 仓库容量
     */
    private WarehouseCapacity capacity;

    /**
     * 仓库状态
     */
    private String status;

    /**
     * 是否默认仓库
     */
    private Boolean isDefault;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WarehouseAddress {
        private String province;
        private String city;
        private String district;
        private String detailAddress;
        private String postalCode;
        private Double latitude;
        private Double longitude;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInfo {
        private String contactName;
        private String contactPhone;
        private String contactEmail;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WarehouseCapacity {
        private Double totalArea; // 总面积（平方米）
        private Double usedArea; // 已使用面积
        private Double availableArea; // 可用面积
        private Integer totalPositions; // 总货位数
        private Integer usedPositions; // 已使用货位数
        private Integer availablePositions; // 可用货位数
    }
}

/**
 * 创建仓库请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateWarehouseRequestDTO {

    /**
     * 仓库名称
     */
    @NotBlank(message = "仓库名称不能为空")
    private String warehouseName;

    /**
     * 仓库代码
     */
    @NotBlank(message = "仓库代码不能为空")
    private String warehouseCode;

    /**
     * 仓库类型
     */
    @NotBlank(message = "仓库类型不能为空")
    private String warehouseType;

    /**
     * 仓库地址
     */
    @NotNull(message = "仓库地址不能为空")
    private WarehouseDTO.WarehouseAddress address;

    /**
     * 联系人信息
     */
    @NotNull(message = "联系人信息不能为空")
    private WarehouseDTO.ContactInfo contactInfo;

    /**
     * 仓库容量
     */
    private WarehouseDTO.WarehouseCapacity capacity;

    /**
     * 是否默认仓库
     */
    private Boolean isDefault;

    /**
     * 描述
     */
    private String description;
}

/**
 * 商品库存DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductStockDTO {

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品SKU
     */
    private String productSku;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * 仓库ID
     */
    private String warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 当前库存
     */
    private Integer currentStock;

    /**
     * 可用库存
     */
    private Integer availableStock;

    /**
     * 锁定库存
     */
    private Integer lockedStock;

    /**
     * 在途库存
     */
    private Integer inTransitStock;

    /**
     * 安全库存
     */
    private Integer safetyStock;

    /**
     * 预警阈值
     */
    private Integer alertThreshold;

    /**
     * 预警级别
     */
    private String alertLevel;

    /**
     * 库存成本
     */
    private BigDecimal stockCost;

    /**
     * 平均成本
     */
    private BigDecimal averageCost;

    /**
     * 最后入库时间
     */
    private LocalDateTime lastInboundTime;

    /**
     * 最后出库时间
     */
    private LocalDateTime lastOutboundTime;

    /**
     * 库存周转率
     */
    private Double turnoverRate;

    /**
     * 库存天数
     */
    private Integer stockDays;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

/**
 * 库存变动记录DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockMovementDTO {

    /**
     * 变动记录ID
     */
    private String movementId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品SKU
     */
    private String productSku;

    /**
     * 仓库ID
     */
    private String warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 变动类型：INBOUND(入库), OUTBOUND(出库), ADJUST(调整), TRANSFER(调拨)
     */
    private String movementType;

    /**
     * 变动原因
     */
    private String movementReason;

    /**
     * 变动数量
     */
    private Integer quantity;

    /**
     * 变动前库存
     */
    private Integer stockBefore;

    /**
     * 变动后库存
     */
    private Integer stockAfter;

    /**
     * 单位成本
     */
    private BigDecimal unitCost;

    /**
     * 总成本
     */
    private BigDecimal totalCost;

    /**
     * 关联单据ID
     */
    private String referenceId;

    /**
     * 关联单据类型
     */
    private String referenceType;

    /**
     * 操作人员
     */
    private String operator;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 备注
     */
    private String remark;
}

/**
 * 库存调整请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockAdjustmentRequestDTO {

    /**
     * 商品ID
     */
    @NotBlank(message = "商品ID不能为空")
    private String productId;

    /**
     * 仓库ID
     */
    @NotBlank(message = "仓库ID不能为空")
    private String warehouseId;

    /**
     * 调整类型：INCREASE(增加), DECREASE(减少), SET(设置)
     */
    @NotBlank(message = "调整类型不能为空")
    private String adjustmentType;

    /**
     * 调整数量
     */
    @NotNull(message = "调整数量不能为空")
    private Integer quantity;

    /**
     * 调整原因
     */
    @NotBlank(message = "调整原因不能为空")
    private String reason;

    /**
     * 单位成本
     */
    private BigDecimal unitCost;

    /**
     * 备注
     */
    private String remark;
}

/**
 * 库存预警设置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockAlertSettingDTO {

    /**
     * 设置ID
     */
    private String settingId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 仓库ID
     */
    private String warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 预警阈值
     */
    private Integer alertThreshold;

    /**
     * 预警级别：LOW(低), MEDIUM(中), HIGH(高)
     */
    private String alertLevel;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 通知方式
     */
    private List<String> notificationMethods;

    /**
     * 通知接收人
     */
    private List<String> notificationRecipients;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

/**
 * 库存预警DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockAlertDTO {

    /**
     * 预警ID
     */
    private String alertId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品SKU
     */
    private String productSku;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * 仓库ID
     */
    private String warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 当前库存
     */
    private Integer currentStock;

    /**
     * 预警阈值
     */
    private Integer alertThreshold;

    /**
     * 安全库存
     */
    private Integer safetyStock;

    /**
     * 预警级别
     */
    private String alertLevel;

    /**
     * 预警状态：ACTIVE(活跃), HANDLED(已处理), IGNORED(已忽略)
     */
    private String alertStatus;

    /**
     * 预警时间
     */
    private LocalDateTime alertTime;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 处理人员
     */
    private String handler;

    /**
     * 处理方式
     */
    private String handleMethod;

    /**
     * 处理备注
     */
    private String handleRemark;

    /**
     * 建议补货数量
     */
    private Integer suggestedReplenishment;

    /**
     * 预计缺货时间
     */
    private LocalDateTime estimatedStockoutTime;
}

/**
 * 盘点任务DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StocktakingTaskDTO {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型：FULL(全盘), PARTIAL(部分盘点), CYCLE(循环盘点)
     */
    private String taskType;

    /**
     * 仓库ID
     */
    private String warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 盘点范围
     */
    private StocktakingScope scope;

    /**
     * 任务状态：CREATED(已创建), IN_PROGRESS(进行中), COMPLETED(已完成), CANCELLED(已取消)
     */
    private String status;

    /**
     * 计划开始时间
     */
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime plannedEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 盘点人员
     */
    private List<String> assignedUsers;

    /**
     * 盘点进度
     */
    private StocktakingProgress progress;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StocktakingScope {
        private String scopeType; // ALL(全部), CATEGORY(分类), PRODUCT(指定商品), LOCATION(指定位置)
        private List<String> scopeValues;
        private Map<String, Object> filters;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StocktakingProgress {
        private Integer totalItems; // 总盘点项目数
        private Integer completedItems; // 已完成项目数
        private Integer pendingItems; // 待盘点项目数
        private Double completionRate; // 完成率
        private Integer discrepancyItems; // 差异项目数
        private Double accuracyRate; // 准确率
    }
}

/**
 * 库存调拨DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTransferDTO {

    /**
     * 调拨单ID
     */
    private String transferId;

    /**
     * 调拨单号
     */
    private String transferNumber;

    /**
     * 调出仓库ID
     */
    private String fromWarehouseId;

    /**
     * 调出仓库名称
     */
    private String fromWarehouseName;

    /**
     * 调入仓库ID
     */
    private String toWarehouseId;

    /**
     * 调入仓库名称
     */
    private String toWarehouseName;

    /**
     * 调拨类型：NORMAL(普通调拨), EMERGENCY(紧急调拨), RETURN(退货调拨)
     */
    private String transferType;

    /**
     * 调拨状态：PENDING(待审核), APPROVED(已审核), IN_TRANSIT(运输中), COMPLETED(已完成), CANCELLED(已取消)
     */
    private String status;

    /**
     * 调拨商品列表
     */
    private List<TransferItem> items;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 申请时间
     */
    private LocalDateTime applicationTime;

    /**
     * 审核人
     */
    private String approver;

    /**
     * 审核时间
     */
    private LocalDateTime approvalTime;

    /**
     * 执行人
     */
    private String executor;

    /**
     * 执行时间
     */
    private LocalDateTime executionTime;

    /**
     * 调拨原因
     */
    private String reason;

    /**
     * 备注
     */
    private String remark;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransferItem {
        private String productId;
        private String productName;
        private String productSku;
        private Integer requestedQuantity;
        private Integer approvedQuantity;
        private Integer actualQuantity;
        private BigDecimal unitCost;
        private BigDecimal totalCost;
        private String status;
    }
}
