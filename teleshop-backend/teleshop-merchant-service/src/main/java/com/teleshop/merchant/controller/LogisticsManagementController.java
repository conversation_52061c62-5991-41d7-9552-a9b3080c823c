package com.teleshop.merchant.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.merchant.dto.logistics.*;
import com.teleshop.merchant.service.LogisticsManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "物流管理", description = "商家端物流管理相关接口")
@RestController
@RequestMapping("/api/v1/merchant/logistics")
@RequiredArgsConstructor
public class LogisticsManagementController {

    private final LogisticsManagementService logisticsManagementService;

    // ==================== 物流公司对接 ====================

    @Operation(summary = "获取可用物流公司列表")
    @GetMapping("/companies")
    public ApiResponse<List<LogisticsCompanyDTO>> getAvailableLogisticsCompanies() {
        List<LogisticsCompanyDTO> companies = logisticsManagementService.getAvailableLogisticsCompanies();
        return ApiResponse.success(companies);
    }

    @Operation(summary = "获取商家已对接物流公司")
    @GetMapping("/companies/merchant/{merchantId}")
    public ApiResponse<List<MerchantLogisticsCompanyDTO>> getMerchantLogisticsCompanies(
            @PathVariable String merchantId) {
        
        List<MerchantLogisticsCompanyDTO> companies = logisticsManagementService.getMerchantLogisticsCompanies(merchantId);
        return ApiResponse.success(companies);
    }

    @Operation(summary = "对接物流公司")
    @PostMapping("/companies/connect")
    public ApiResponse<MerchantLogisticsCompanyDTO> connectLogisticsCompany(
            @Valid @RequestBody ConnectLogisticsCompanyRequestDTO requestDTO) {
        
        MerchantLogisticsCompanyDTO company = logisticsManagementService.connectLogisticsCompany(requestDTO);
        return ApiResponse.success(company);
    }

    @Operation(summary = "更新物流公司配置")
    @PutMapping("/companies/{companyId}")
    public ApiResponse<MerchantLogisticsCompanyDTO> updateLogisticsCompanyConfig(
            @PathVariable String companyId,
            @Valid @RequestBody UpdateLogisticsCompanyConfigDTO requestDTO) {
        
        MerchantLogisticsCompanyDTO company = logisticsManagementService.updateLogisticsCompanyConfig(
                companyId, requestDTO);
        return ApiResponse.success(company);
    }

    @Operation(summary = "断开物流公司对接")
    @DeleteMapping("/companies/{companyId}")
    public ApiResponse<Void> disconnectLogisticsCompany(@PathVariable String companyId) {
        logisticsManagementService.disconnectLogisticsCompany(companyId);
        return ApiResponse.success();
    }

    // ==================== 运费模板管理 ====================

    @Operation(summary = "获取运费模板列表")
    @GetMapping("/freight-templates")
    public ApiResponse<List<FreightTemplateDTO>> getFreightTemplates(
            @RequestParam String merchantId,
            @RequestParam(required = false) String status) {
        
        List<FreightTemplateDTO> templates = logisticsManagementService.getFreightTemplates(merchantId, status);
        return ApiResponse.success(templates);
    }

    @Operation(summary = "创建运费模板")
    @PostMapping("/freight-templates")
    public ApiResponse<FreightTemplateDTO> createFreightTemplate(
            @RequestParam String merchantId,
            @Valid @RequestBody CreateFreightTemplateRequestDTO requestDTO) {
        
        FreightTemplateDTO template = logisticsManagementService.createFreightTemplate(merchantId, requestDTO);
        return ApiResponse.success(template);
    }

    @Operation(summary = "更新运费模板")
    @PutMapping("/freight-templates/{templateId}")
    public ApiResponse<FreightTemplateDTO> updateFreightTemplate(
            @PathVariable String templateId,
            @Valid @RequestBody UpdateFreightTemplateRequestDTO requestDTO) {
        
        FreightTemplateDTO template = logisticsManagementService.updateFreightTemplate(templateId, requestDTO);
        return ApiResponse.success(template);
    }

    @Operation(summary = "删除运费模板")
    @DeleteMapping("/freight-templates/{templateId}")
    public ApiResponse<Void> deleteFreightTemplate(@PathVariable String templateId) {
        logisticsManagementService.deleteFreightTemplate(templateId);
        return ApiResponse.success();
    }

    @Operation(summary = "计算运费")
    @PostMapping("/freight-templates/{templateId}/calculate")
    public ApiResponse<FreightCalculationResultDTO> calculateFreight(
            @PathVariable String templateId,
            @Valid @RequestBody FreightCalculationRequestDTO requestDTO) {
        
        FreightCalculationResultDTO result = logisticsManagementService.calculateFreight(templateId, requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 发货地址管理 ====================

    @Operation(summary = "获取发货地址列表")
    @GetMapping("/shipping-addresses")
    public ApiResponse<List<ShippingAddressDTO>> getShippingAddresses(@RequestParam String merchantId) {
        List<ShippingAddressDTO> addresses = logisticsManagementService.getShippingAddresses(merchantId);
        return ApiResponse.success(addresses);
    }

    @Operation(summary = "创建发货地址")
    @PostMapping("/shipping-addresses")
    public ApiResponse<ShippingAddressDTO> createShippingAddress(
            @RequestParam String merchantId,
            @Valid @RequestBody CreateShippingAddressRequestDTO requestDTO) {
        
        ShippingAddressDTO address = logisticsManagementService.createShippingAddress(merchantId, requestDTO);
        return ApiResponse.success(address);
    }

    @Operation(summary = "更新发货地址")
    @PutMapping("/shipping-addresses/{addressId}")
    public ApiResponse<ShippingAddressDTO> updateShippingAddress(
            @PathVariable String addressId,
            @Valid @RequestBody UpdateShippingAddressRequestDTO requestDTO) {
        
        ShippingAddressDTO address = logisticsManagementService.updateShippingAddress(addressId, requestDTO);
        return ApiResponse.success(address);
    }

    @Operation(summary = "删除发货地址")
    @DeleteMapping("/shipping-addresses/{addressId}")
    public ApiResponse<Void> deleteShippingAddress(@PathVariable String addressId) {
        logisticsManagementService.deleteShippingAddress(addressId);
        return ApiResponse.success();
    }

    @Operation(summary = "设置默认发货地址")
    @PostMapping("/shipping-addresses/{addressId}/set-default")
    public ApiResponse<Void> setDefaultShippingAddress(@PathVariable String addressId) {
        logisticsManagementService.setDefaultShippingAddress(addressId);
        return ApiResponse.success();
    }

    // ==================== 物流异常处理 ====================

    @Operation(summary = "获取物流异常列表")
    @GetMapping("/exceptions")
    public ApiResponse<List<LogisticsExceptionDTO>> getLogisticsExceptions(
            @RequestParam String merchantId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String exceptionType,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<LogisticsExceptionDTO> exceptions = logisticsManagementService.getLogisticsExceptions(
                merchantId, status, exceptionType, page, size);
        return ApiResponse.success(exceptions);
    }

    @Operation(summary = "处理物流异常")
    @PostMapping("/exceptions/{exceptionId}/handle")
    public ApiResponse<LogisticsExceptionHandleResultDTO> handleLogisticsException(
            @PathVariable String exceptionId,
            @Valid @RequestBody HandleLogisticsExceptionRequestDTO requestDTO) {
        
        LogisticsExceptionHandleResultDTO result = logisticsManagementService.handleLogisticsException(
                exceptionId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "批量处理物流异常")
    @PostMapping("/exceptions/batch-handle")
    public ApiResponse<BatchLogisticsExceptionHandleResultDTO> batchHandleLogisticsExceptions(
            @Valid @RequestBody BatchHandleLogisticsExceptionRequestDTO requestDTO) {
        
        BatchLogisticsExceptionHandleResultDTO result = logisticsManagementService.batchHandleLogisticsExceptions(requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 配送范围设置 ====================

    @Operation(summary = "获取配送范围设置")
    @GetMapping("/delivery-areas")
    public ApiResponse<List<DeliveryAreaDTO>> getDeliveryAreas(@RequestParam String merchantId) {
        List<DeliveryAreaDTO> areas = logisticsManagementService.getDeliveryAreas(merchantId);
        return ApiResponse.success(areas);
    }

    @Operation(summary = "设置配送范围")
    @PostMapping("/delivery-areas")
    public ApiResponse<DeliveryAreaDTO> setDeliveryArea(
            @RequestParam String merchantId,
            @Valid @RequestBody SetDeliveryAreaRequestDTO requestDTO) {
        
        DeliveryAreaDTO area = logisticsManagementService.setDeliveryArea(merchantId, requestDTO);
        return ApiResponse.success(area);
    }

    @Operation(summary = "更新配送范围")
    @PutMapping("/delivery-areas/{areaId}")
    public ApiResponse<DeliveryAreaDTO> updateDeliveryArea(
            @PathVariable String areaId,
            @Valid @RequestBody UpdateDeliveryAreaRequestDTO requestDTO) {
        
        DeliveryAreaDTO area = logisticsManagementService.updateDeliveryArea(areaId, requestDTO);
        return ApiResponse.success(area);
    }

    @Operation(summary = "删除配送范围")
    @DeleteMapping("/delivery-areas/{areaId}")
    public ApiResponse<Void> deleteDeliveryArea(@PathVariable String areaId) {
        logisticsManagementService.deleteDeliveryArea(areaId);
        return ApiResponse.success();
    }

    @Operation(summary = "检查配送范围")
    @PostMapping("/delivery-areas/check")
    public ApiResponse<DeliveryAreaCheckResultDTO> checkDeliveryArea(
            @RequestParam String merchantId,
            @Valid @RequestBody DeliveryAreaCheckRequestDTO requestDTO) {
        
        DeliveryAreaCheckResultDTO result = logisticsManagementService.checkDeliveryArea(merchantId, requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 物流成本分析 ====================

    @Operation(summary = "获取物流成本分析")
    @GetMapping("/cost-analysis")
    public ApiResponse<LogisticsCostAnalysisDTO> getLogisticsCostAnalysis(
            @RequestParam String merchantId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String logisticsCompanyId) {
        
        LogisticsCostAnalysisDTO analysis = logisticsManagementService.getLogisticsCostAnalysis(
                merchantId, startDate, endDate, logisticsCompanyId);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取物流效率分析")
    @GetMapping("/efficiency-analysis")
    public ApiResponse<LogisticsEfficiencyAnalysisDTO> getLogisticsEfficiencyAnalysis(
            @RequestParam String merchantId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        LogisticsEfficiencyAnalysisDTO analysis = logisticsManagementService.getLogisticsEfficiencyAnalysis(
                merchantId, startDate, endDate);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取物流服务质量分析")
    @GetMapping("/quality-analysis")
    public ApiResponse<LogisticsQualityAnalysisDTO> getLogisticsQualityAnalysis(
            @RequestParam String merchantId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        LogisticsQualityAnalysisDTO analysis = logisticsManagementService.getLogisticsQualityAnalysis(
                merchantId, startDate, endDate);
        return ApiResponse.success(analysis);
    }

    // ==================== 物流报表 ====================

    @Operation(summary = "获取物流报表")
    @GetMapping("/reports")
    public ApiResponse<List<LogisticsReportDTO>> getLogisticsReports(
            @RequestParam String merchantId,
            @RequestParam(required = false) String reportType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        List<LogisticsReportDTO> reports = logisticsManagementService.getLogisticsReports(
                merchantId, reportType, startDate, endDate);
        return ApiResponse.success(reports);
    }

    @Operation(summary = "导出物流报表")
    @PostMapping("/reports/export")
    public ApiResponse<String> exportLogisticsReport(
            @Valid @RequestBody ExportLogisticsReportRequestDTO requestDTO) {
        
        String downloadUrl = logisticsManagementService.exportLogisticsReport(requestDTO);
        return ApiResponse.success(downloadUrl);
    }

    // ==================== 物流设置 ====================

    @Operation(summary = "获取物流设置")
    @GetMapping("/settings")
    public ApiResponse<LogisticsSettingsDTO> getLogisticsSettings(@RequestParam String merchantId) {
        LogisticsSettingsDTO settings = logisticsManagementService.getLogisticsSettings(merchantId);
        return ApiResponse.success(settings);
    }

    @Operation(summary = "更新物流设置")
    @PutMapping("/settings")
    public ApiResponse<LogisticsSettingsDTO> updateLogisticsSettings(
            @RequestParam String merchantId,
            @Valid @RequestBody UpdateLogisticsSettingsRequestDTO requestDTO) {
        
        LogisticsSettingsDTO settings = logisticsManagementService.updateLogisticsSettings(merchantId, requestDTO);
        return ApiResponse.success(settings);
    }
}
