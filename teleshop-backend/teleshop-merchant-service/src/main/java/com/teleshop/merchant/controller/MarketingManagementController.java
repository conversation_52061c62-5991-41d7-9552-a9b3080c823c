package com.teleshop.merchant.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.merchant.dto.marketing.*;
import com.teleshop.merchant.service.MarketingManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "营销管理", description = "商家端营销工具和活动管理相关接口")
@RestController
@RequestMapping("/api/v1/merchant/marketing")
@RequiredArgsConstructor
public class MarketingManagementController {

    private final MarketingManagementService marketingManagementService;

    // ==================== 优惠券管理 ====================

    @Operation(summary = "获取优惠券列表")
    @GetMapping("/coupons")
    public ApiResponse<List<MerchantCouponDTO>> getCoupons(
            @RequestParam String merchantId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<MerchantCouponDTO> coupons = marketingManagementService.getCoupons(
                merchantId, status, type, page, size);
        return ApiResponse.success(coupons);
    }

    @Operation(summary = "创建优惠券")
    @PostMapping("/coupons")
    public ApiResponse<MerchantCouponDTO> createCoupon(
            @RequestParam String merchantId,
            @Valid @RequestBody CreateCouponRequestDTO requestDTO) {
        
        MerchantCouponDTO coupon = marketingManagementService.createCoupon(merchantId, requestDTO);
        return ApiResponse.success(coupon);
    }

    @Operation(summary = "更新优惠券")
    @PutMapping("/coupons/{couponId}")
    public ApiResponse<MerchantCouponDTO> updateCoupon(
            @PathVariable String couponId,
            @Valid @RequestBody UpdateCouponRequestDTO requestDTO) {
        
        MerchantCouponDTO coupon = marketingManagementService.updateCoupon(couponId, requestDTO);
        return ApiResponse.success(coupon);
    }

    @Operation(summary = "删除优惠券")
    @DeleteMapping("/coupons/{couponId}")
    public ApiResponse<Void> deleteCoupon(@PathVariable String couponId) {
        marketingManagementService.deleteCoupon(couponId);
        return ApiResponse.success();
    }

    @Operation(summary = "发布优惠券")
    @PostMapping("/coupons/{couponId}/publish")
    public ApiResponse<CouponPublishResultDTO> publishCoupon(
            @PathVariable String couponId,
            @Valid @RequestBody CouponPublishRequestDTO requestDTO) {
        
        CouponPublishResultDTO result = marketingManagementService.publishCoupon(couponId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "停用优惠券")
    @PostMapping("/coupons/{couponId}/disable")
    public ApiResponse<Void> disableCoupon(
            @PathVariable String couponId,
            @RequestParam String reason) {
        
        marketingManagementService.disableCoupon(couponId, reason);
        return ApiResponse.success();
    }

    @Operation(summary = "获取优惠券使用统计")
    @GetMapping("/coupons/{couponId}/usage-stats")
    public ApiResponse<CouponUsageStatsDTO> getCouponUsageStats(@PathVariable String couponId) {
        CouponUsageStatsDTO stats = marketingManagementService.getCouponUsageStats(couponId);
        return ApiResponse.success(stats);
    }

    // ==================== 限时折扣管理 ====================

    @Operation(summary = "获取限时折扣列表")
    @GetMapping("/flash-discounts")
    public ApiResponse<List<FlashDiscountDTO>> getFlashDiscounts(
            @RequestParam String merchantId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<FlashDiscountDTO> discounts = marketingManagementService.getFlashDiscounts(
                merchantId, status, page, size);
        return ApiResponse.success(discounts);
    }

    @Operation(summary = "创建限时折扣")
    @PostMapping("/flash-discounts")
    public ApiResponse<FlashDiscountDTO> createFlashDiscount(
            @RequestParam String merchantId,
            @Valid @RequestBody CreateFlashDiscountRequestDTO requestDTO) {
        
        FlashDiscountDTO discount = marketingManagementService.createFlashDiscount(merchantId, requestDTO);
        return ApiResponse.success(discount);
    }

    @Operation(summary = "更新限时折扣")
    @PutMapping("/flash-discounts/{discountId}")
    public ApiResponse<FlashDiscountDTO> updateFlashDiscount(
            @PathVariable String discountId,
            @Valid @RequestBody UpdateFlashDiscountRequestDTO requestDTO) {
        
        FlashDiscountDTO discount = marketingManagementService.updateFlashDiscount(discountId, requestDTO);
        return ApiResponse.success(discount);
    }

    @Operation(summary = "删除限时折扣")
    @DeleteMapping("/flash-discounts/{discountId}")
    public ApiResponse<Void> deleteFlashDiscount(@PathVariable String discountId) {
        marketingManagementService.deleteFlashDiscount(discountId);
        return ApiResponse.success();
    }

    @Operation(summary = "启动限时折扣")
    @PostMapping("/flash-discounts/{discountId}/start")
    public ApiResponse<FlashDiscountStartResultDTO> startFlashDiscount(@PathVariable String discountId) {
        FlashDiscountStartResultDTO result = marketingManagementService.startFlashDiscount(discountId);
        return ApiResponse.success(result);
    }

    @Operation(summary = "停止限时折扣")
    @PostMapping("/flash-discounts/{discountId}/stop")
    public ApiResponse<Void> stopFlashDiscount(
            @PathVariable String discountId,
            @RequestParam String reason) {
        
        marketingManagementService.stopFlashDiscount(discountId, reason);
        return ApiResponse.success();
    }

    // ==================== 满减活动管理 ====================

    @Operation(summary = "获取满减活动列表")
    @GetMapping("/full-reduction")
    public ApiResponse<List<FullReductionActivityDTO>> getFullReductionActivities(
            @RequestParam String merchantId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<FullReductionActivityDTO> activities = marketingManagementService.getFullReductionActivities(
                merchantId, status, page, size);
        return ApiResponse.success(activities);
    }

    @Operation(summary = "创建满减活动")
    @PostMapping("/full-reduction")
    public ApiResponse<FullReductionActivityDTO> createFullReductionActivity(
            @RequestParam String merchantId,
            @Valid @RequestBody CreateFullReductionRequestDTO requestDTO) {
        
        FullReductionActivityDTO activity = marketingManagementService.createFullReductionActivity(
                merchantId, requestDTO);
        return ApiResponse.success(activity);
    }

    @Operation(summary = "更新满减活动")
    @PutMapping("/full-reduction/{activityId}")
    public ApiResponse<FullReductionActivityDTO> updateFullReductionActivity(
            @PathVariable String activityId,
            @Valid @RequestBody UpdateFullReductionRequestDTO requestDTO) {
        
        FullReductionActivityDTO activity = marketingManagementService.updateFullReductionActivity(
                activityId, requestDTO);
        return ApiResponse.success(activity);
    }

    @Operation(summary = "删除满减活动")
    @DeleteMapping("/full-reduction/{activityId}")
    public ApiResponse<Void> deleteFullReductionActivity(@PathVariable String activityId) {
        marketingManagementService.deleteFullReductionActivity(activityId);
        return ApiResponse.success();
    }

    // ==================== 团购活动管理 ====================

    @Operation(summary = "获取团购活动列表")
    @GetMapping("/group-buy")
    public ApiResponse<List<MerchantGroupBuyDTO>> getGroupBuyActivities(
            @RequestParam String merchantId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<MerchantGroupBuyDTO> activities = marketingManagementService.getGroupBuyActivities(
                merchantId, status, page, size);
        return ApiResponse.success(activities);
    }

    @Operation(summary = "创建团购活动")
    @PostMapping("/group-buy")
    public ApiResponse<MerchantGroupBuyDTO> createGroupBuyActivity(
            @RequestParam String merchantId,
            @Valid @RequestBody CreateGroupBuyRequestDTO requestDTO) {
        
        MerchantGroupBuyDTO activity = marketingManagementService.createGroupBuyActivity(
                merchantId, requestDTO);
        return ApiResponse.success(activity);
    }

    @Operation(summary = "更新团购活动")
    @PutMapping("/group-buy/{activityId}")
    public ApiResponse<MerchantGroupBuyDTO> updateGroupBuyActivity(
            @PathVariable String activityId,
            @Valid @RequestBody UpdateGroupBuyRequestDTO requestDTO) {
        
        MerchantGroupBuyDTO activity = marketingManagementService.updateGroupBuyActivity(
                activityId, requestDTO);
        return ApiResponse.success(activity);
    }

    @Operation(summary = "获取团购活动统计")
    @GetMapping("/group-buy/{activityId}/stats")
    public ApiResponse<GroupBuyActivityStatsDTO> getGroupBuyActivityStats(@PathVariable String activityId) {
        GroupBuyActivityStatsDTO stats = marketingManagementService.getGroupBuyActivityStats(activityId);
        return ApiResponse.success(stats);
    }

    // ==================== 会员专享价格 ====================

    @Operation(summary = "获取会员价格设置")
    @GetMapping("/member-pricing")
    public ApiResponse<List<MemberPricingDTO>> getMemberPricing(
            @RequestParam String merchantId,
            @RequestParam(required = false) String productId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<MemberPricingDTO> pricing = marketingManagementService.getMemberPricing(
                merchantId, productId, page, size);
        return ApiResponse.success(pricing);
    }

    @Operation(summary = "设置会员价格")
    @PostMapping("/member-pricing")
    public ApiResponse<MemberPricingDTO> setMemberPricing(
            @RequestParam String merchantId,
            @Valid @RequestBody SetMemberPricingRequestDTO requestDTO) {
        
        MemberPricingDTO pricing = marketingManagementService.setMemberPricing(merchantId, requestDTO);
        return ApiResponse.success(pricing);
    }

    @Operation(summary = "批量设置会员价格")
    @PostMapping("/member-pricing/batch")
    public ApiResponse<BatchMemberPricingResultDTO> batchSetMemberPricing(
            @RequestParam String merchantId,
            @Valid @RequestBody BatchMemberPricingRequestDTO requestDTO) {
        
        BatchMemberPricingResultDTO result = marketingManagementService.batchSetMemberPricing(
                merchantId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "删除会员价格")
    @DeleteMapping("/member-pricing/{pricingId}")
    public ApiResponse<Void> deleteMemberPricing(@PathVariable String pricingId) {
        marketingManagementService.deleteMemberPricing(pricingId);
        return ApiResponse.success();
    }

    // ==================== 营销效果分析 ====================

    @Operation(summary = "获取营销活动概览")
    @GetMapping("/overview")
    public ApiResponse<MarketingOverviewDTO> getMarketingOverview(
            @RequestParam String merchantId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        MarketingOverviewDTO overview = marketingManagementService.getMarketingOverview(
                merchantId, startDate, endDate);
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取营销活动效果分析")
    @GetMapping("/effectiveness-analysis")
    public ApiResponse<MarketingEffectivenessAnalysisDTO> getMarketingEffectivenessAnalysis(
            @RequestParam String merchantId,
            @RequestParam(required = false) String activityType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        MarketingEffectivenessAnalysisDTO analysis = marketingManagementService.getMarketingEffectivenessAnalysis(
                merchantId, activityType, startDate, endDate);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取营销ROI分析")
    @GetMapping("/roi-analysis")
    public ApiResponse<MarketingROIAnalysisDTO> getMarketingROIAnalysis(
            @RequestParam String merchantId,
            @RequestParam(required = false) String activityId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        MarketingROIAnalysisDTO analysis = marketingManagementService.getMarketingROIAnalysis(
                merchantId, activityId, startDate, endDate);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取客户转化分析")
    @GetMapping("/conversion-analysis")
    public ApiResponse<CustomerConversionAnalysisDTO> getCustomerConversionAnalysis(
            @RequestParam String merchantId,
            @RequestParam(required = false) String activityType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        CustomerConversionAnalysisDTO analysis = marketingManagementService.getCustomerConversionAnalysis(
                merchantId, activityType, startDate, endDate);
        return ApiResponse.success(analysis);
    }

    // ==================== 营销工具 ====================

    @Operation(summary = "获取营销工具列表")
    @GetMapping("/tools")
    public ApiResponse<List<MarketingToolDTO>> getMarketingTools(@RequestParam String merchantId) {
        List<MarketingToolDTO> tools = marketingManagementService.getMarketingTools(merchantId);
        return ApiResponse.success(tools);
    }

    @Operation(summary = "获取营销建议")
    @GetMapping("/suggestions")
    public ApiResponse<List<MarketingSuggestionDTO>> getMarketingSuggestions(
            @RequestParam String merchantId,
            @RequestParam(required = false) String scene) {
        
        List<MarketingSuggestionDTO> suggestions = marketingManagementService.getMarketingSuggestions(
                merchantId, scene);
        return ApiResponse.success(suggestions);
    }

    @Operation(summary = "创建营销计划")
    @PostMapping("/plans")
    public ApiResponse<MarketingPlanDTO> createMarketingPlan(
            @RequestParam String merchantId,
            @Valid @RequestBody CreateMarketingPlanRequestDTO requestDTO) {
        
        MarketingPlanDTO plan = marketingManagementService.createMarketingPlan(merchantId, requestDTO);
        return ApiResponse.success(plan);
    }

    @Operation(summary = "获取营销计划列表")
    @GetMapping("/plans")
    public ApiResponse<List<MarketingPlanDTO>> getMarketingPlans(
            @RequestParam String merchantId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<MarketingPlanDTO> plans = marketingManagementService.getMarketingPlans(
                merchantId, status, page, size);
        return ApiResponse.success(plans);
    }

    @Operation(summary = "执行营销计划")
    @PostMapping("/plans/{planId}/execute")
    public ApiResponse<MarketingPlanExecutionResultDTO> executeMarketingPlan(@PathVariable String planId) {
        MarketingPlanExecutionResultDTO result = marketingManagementService.executeMarketingPlan(planId);
        return ApiResponse.success(result);
    }

    // ==================== 营销数据导出 ====================

    @Operation(summary = "导出营销数据")
    @PostMapping("/export")
    public ApiResponse<String> exportMarketingData(
            @RequestParam String merchantId,
            @Valid @RequestBody MarketingDataExportRequestDTO requestDTO) {
        
        String downloadUrl = marketingManagementService.exportMarketingData(merchantId, requestDTO);
        return ApiResponse.success(downloadUrl);
    }

    @Operation(summary = "获取营销报表")
    @GetMapping("/reports")
    public ApiResponse<List<MarketingReportDTO>> getMarketingReports(
            @RequestParam String merchantId,
            @RequestParam(required = false) String reportType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        List<MarketingReportDTO> reports = marketingManagementService.getMarketingReports(
                merchantId, reportType, startDate, endDate);
        return ApiResponse.success(reports);
    }
}
