package com.teleshop.admin.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.admin.dto.ecosystem.*;
import com.teleshop.admin.service.MerchantEcosystemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "商家生态管理", description = "商家生态管理相关接口")
@RestController
@RequestMapping("/api/v1/admin/merchant-ecosystem")
@RequiredArgsConstructor
public class MerchantEcosystemController {

    private final MerchantEcosystemService merchantEcosystemService;

    // ==================== 商家等级体系管理 ====================

    @Operation(summary = "获取商家等级体系")
    @GetMapping("/levels")
    public ApiResponse<List<MerchantLevelDTO>> getMerchantLevels() {
        List<MerchantLevelDTO> levels = merchantEcosystemService.getMerchantLevels();
        return ApiResponse.success(levels);
    }

    @Operation(summary = "创建商家等级")
    @PostMapping("/levels")
    public ApiResponse<MerchantLevelDTO> createMerchantLevel(
            @Valid @RequestBody CreateMerchantLevelRequestDTO requestDTO) {
        
        MerchantLevelDTO level = merchantEcosystemService.createMerchantLevel(requestDTO);
        return ApiResponse.success(level);
    }

    @Operation(summary = "更新商家等级")
    @PutMapping("/levels/{levelId}")
    public ApiResponse<MerchantLevelDTO> updateMerchantLevel(
            @PathVariable String levelId,
            @Valid @RequestBody UpdateMerchantLevelRequestDTO requestDTO) {
        
        MerchantLevelDTO level = merchantEcosystemService.updateMerchantLevel(levelId, requestDTO);
        return ApiResponse.success(level);
    }

    @Operation(summary = "删除商家等级")
    @DeleteMapping("/levels/{levelId}")
    public ApiResponse<Void> deleteMerchantLevel(@PathVariable String levelId) {
        merchantEcosystemService.deleteMerchantLevel(levelId);
        return ApiResponse.success();
    }

    @Operation(summary = "获取商家等级分布")
    @GetMapping("/levels/distribution")
    public ApiResponse<MerchantLevelDistributionDTO> getMerchantLevelDistribution() {
        MerchantLevelDistributionDTO distribution = merchantEcosystemService.getMerchantLevelDistribution();
        return ApiResponse.success(distribution);
    }

    @Operation(summary = "批量调整商家等级")
    @PostMapping("/levels/batch-adjust")
    public ApiResponse<BatchMerchantLevelAdjustmentResultDTO> batchAdjustMerchantLevels(
            @Valid @RequestBody BatchMerchantLevelAdjustmentRequestDTO requestDTO) {
        
        BatchMerchantLevelAdjustmentResultDTO result = merchantEcosystemService.batchAdjustMerchantLevels(requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 商家扶持政策 ====================

    @Operation(summary = "获取扶持政策列表")
    @GetMapping("/support-policies")
    public ApiResponse<List<MerchantSupportPolicyDTO>> getSupportPolicies(
            @RequestParam(required = false) String policyType,
            @RequestParam(required = false) String status) {
        
        List<MerchantSupportPolicyDTO> policies = merchantEcosystemService.getSupportPolicies(policyType, status);
        return ApiResponse.success(policies);
    }

    @Operation(summary = "创建扶持政策")
    @PostMapping("/support-policies")
    public ApiResponse<MerchantSupportPolicyDTO> createSupportPolicy(
            @Valid @RequestBody CreateSupportPolicyRequestDTO requestDTO) {
        
        MerchantSupportPolicyDTO policy = merchantEcosystemService.createSupportPolicy(requestDTO);
        return ApiResponse.success(policy);
    }

    @Operation(summary = "更新扶持政策")
    @PutMapping("/support-policies/{policyId}")
    public ApiResponse<MerchantSupportPolicyDTO> updateSupportPolicy(
            @PathVariable String policyId,
            @Valid @RequestBody UpdateSupportPolicyRequestDTO requestDTO) {
        
        MerchantSupportPolicyDTO policy = merchantEcosystemService.updateSupportPolicy(policyId, requestDTO);
        return ApiResponse.success(policy);
    }

    @Operation(summary = "发布扶持政策")
    @PostMapping("/support-policies/{policyId}/publish")
    public ApiResponse<Void> publishSupportPolicy(@PathVariable String policyId) {
        merchantEcosystemService.publishSupportPolicy(policyId);
        return ApiResponse.success();
    }

    @Operation(summary = "获取政策申请列表")
    @GetMapping("/support-policies/applications")
    public ApiResponse<List<PolicyApplicationDTO>> getPolicyApplications(
            @RequestParam(required = false) String policyId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<PolicyApplicationDTO> applications = merchantEcosystemService.getPolicyApplications(
                policyId, status, page, size);
        return ApiResponse.success(applications);
    }

    @Operation(summary = "审核政策申请")
    @PostMapping("/support-policies/applications/{applicationId}/review")
    public ApiResponse<PolicyApplicationReviewResultDTO> reviewPolicyApplication(
            @PathVariable String applicationId,
            @Valid @RequestBody ReviewPolicyApplicationRequestDTO requestDTO) {
        
        PolicyApplicationReviewResultDTO result = merchantEcosystemService.reviewPolicyApplication(
                applicationId, requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 商家培训体系 ====================

    @Operation(summary = "获取培训课程列表")
    @GetMapping("/training/courses")
    public ApiResponse<List<TrainingCourseDTO>> getTrainingCourses(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) String status) {
        
        List<TrainingCourseDTO> courses = merchantEcosystemService.getTrainingCourses(category, level, status);
        return ApiResponse.success(courses);
    }

    @Operation(summary = "创建培训课程")
    @PostMapping("/training/courses")
    public ApiResponse<TrainingCourseDTO> createTrainingCourse(
            @Valid @RequestBody CreateTrainingCourseRequestDTO requestDTO) {
        
        TrainingCourseDTO course = merchantEcosystemService.createTrainingCourse(requestDTO);
        return ApiResponse.success(course);
    }

    @Operation(summary = "更新培训课程")
    @PutMapping("/training/courses/{courseId}")
    public ApiResponse<TrainingCourseDTO> updateTrainingCourse(
            @PathVariable String courseId,
            @Valid @RequestBody UpdateTrainingCourseRequestDTO requestDTO) {
        
        TrainingCourseDTO course = merchantEcosystemService.updateTrainingCourse(courseId, requestDTO);
        return ApiResponse.success(course);
    }

    @Operation(summary = "发布培训课程")
    @PostMapping("/training/courses/{courseId}/publish")
    public ApiResponse<Void> publishTrainingCourse(@PathVariable String courseId) {
        merchantEcosystemService.publishTrainingCourse(courseId);
        return ApiResponse.success();
    }

    @Operation(summary = "获取培训记录")
    @GetMapping("/training/records")
    public ApiResponse<List<TrainingRecordDTO>> getTrainingRecords(
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String courseId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<TrainingRecordDTO> records = merchantEcosystemService.getTrainingRecords(
                merchantId, courseId, status, page, size);
        return ApiResponse.success(records);
    }

    @Operation(summary = "获取培训统计")
    @GetMapping("/training/statistics")
    public ApiResponse<TrainingStatisticsDTO> getTrainingStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        TrainingStatisticsDTO statistics = merchantEcosystemService.getTrainingStatistics(startDate, endDate);
        return ApiResponse.success(statistics);
    }

    // ==================== 商家考核评估 ====================

    @Operation(summary = "获取考核指标体系")
    @GetMapping("/assessment/indicators")
    public ApiResponse<List<AssessmentIndicatorDTO>> getAssessmentIndicators() {
        List<AssessmentIndicatorDTO> indicators = merchantEcosystemService.getAssessmentIndicators();
        return ApiResponse.success(indicators);
    }

    @Operation(summary = "创建考核指标")
    @PostMapping("/assessment/indicators")
    public ApiResponse<AssessmentIndicatorDTO> createAssessmentIndicator(
            @Valid @RequestBody CreateAssessmentIndicatorRequestDTO requestDTO) {
        
        AssessmentIndicatorDTO indicator = merchantEcosystemService.createAssessmentIndicator(requestDTO);
        return ApiResponse.success(indicator);
    }

    @Operation(summary = "更新考核指标")
    @PutMapping("/assessment/indicators/{indicatorId}")
    public ApiResponse<AssessmentIndicatorDTO> updateAssessmentIndicator(
            @PathVariable String indicatorId,
            @Valid @RequestBody UpdateAssessmentIndicatorRequestDTO requestDTO) {
        
        AssessmentIndicatorDTO indicator = merchantEcosystemService.updateAssessmentIndicator(indicatorId, requestDTO);
        return ApiResponse.success(indicator);
    }

    @Operation(summary = "获取商家考核结果")
    @GetMapping("/assessment/results")
    public ApiResponse<List<MerchantAssessmentResultDTO>> getMerchantAssessmentResults(
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String assessmentPeriod,
            @RequestParam(required = false) String level,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<MerchantAssessmentResultDTO> results = merchantEcosystemService.getMerchantAssessmentResults(
                merchantId, assessmentPeriod, level, page, size);
        return ApiResponse.success(results);
    }

    @Operation(summary = "执行商家考核")
    @PostMapping("/assessment/execute")
    public ApiResponse<AssessmentExecutionResultDTO> executeMerchantAssessment(
            @Valid @RequestBody ExecuteAssessmentRequestDTO requestDTO) {
        
        AssessmentExecutionResultDTO result = merchantEcosystemService.executeMerchantAssessment(requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 违规商家处罚 ====================

    @Operation(summary = "获取违规记录列表")
    @GetMapping("/violations")
    public ApiResponse<List<MerchantViolationDTO>> getMerchantViolations(
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String violationType,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<MerchantViolationDTO> violations = merchantEcosystemService.getMerchantViolations(
                merchantId, violationType, status, page, size);
        return ApiResponse.success(violations);
    }

    @Operation(summary = "创建违规记录")
    @PostMapping("/violations")
    public ApiResponse<MerchantViolationDTO> createMerchantViolation(
            @Valid @RequestBody CreateViolationRequestDTO requestDTO) {
        
        MerchantViolationDTO violation = merchantEcosystemService.createMerchantViolation(requestDTO);
        return ApiResponse.success(violation);
    }

    @Operation(summary = "处理违规记录")
    @PostMapping("/violations/{violationId}/handle")
    public ApiResponse<ViolationHandleResultDTO> handleMerchantViolation(
            @PathVariable String violationId,
            @Valid @RequestBody HandleViolationRequestDTO requestDTO) {
        
        ViolationHandleResultDTO result = merchantEcosystemService.handleMerchantViolation(violationId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取处罚规则")
    @GetMapping("/violations/penalty-rules")
    public ApiResponse<List<PenaltyRuleDTO>> getPenaltyRules() {
        List<PenaltyRuleDTO> rules = merchantEcosystemService.getPenaltyRules();
        return ApiResponse.success(rules);
    }

    @Operation(summary = "设置处罚规则")
    @PostMapping("/violations/penalty-rules")
    public ApiResponse<PenaltyRuleDTO> setPenaltyRule(
            @Valid @RequestBody SetPenaltyRuleRequestDTO requestDTO) {
        
        PenaltyRuleDTO rule = merchantEcosystemService.setPenaltyRule(requestDTO);
        return ApiResponse.success(rule);
    }

    // ==================== 商家激励机制 ====================

    @Operation(summary = "获取激励计划列表")
    @GetMapping("/incentives")
    public ApiResponse<List<MerchantIncentivePlanDTO>> getIncentivePlans(
            @RequestParam(required = false) String planType,
            @RequestParam(required = false) String status) {
        
        List<MerchantIncentivePlanDTO> plans = merchantEcosystemService.getIncentivePlans(planType, status);
        return ApiResponse.success(plans);
    }

    @Operation(summary = "创建激励计划")
    @PostMapping("/incentives")
    public ApiResponse<MerchantIncentivePlanDTO> createIncentivePlan(
            @Valid @RequestBody CreateIncentivePlanRequestDTO requestDTO) {
        
        MerchantIncentivePlanDTO plan = merchantEcosystemService.createIncentivePlan(requestDTO);
        return ApiResponse.success(plan);
    }

    @Operation(summary = "更新激励计划")
    @PutMapping("/incentives/{planId}")
    public ApiResponse<MerchantIncentivePlanDTO> updateIncentivePlan(
            @PathVariable String planId,
            @Valid @RequestBody UpdateIncentivePlanRequestDTO requestDTO) {
        
        MerchantIncentivePlanDTO plan = merchantEcosystemService.updateIncentivePlan(planId, requestDTO);
        return ApiResponse.success(plan);
    }

    @Operation(summary = "获取激励记录")
    @GetMapping("/incentives/records")
    public ApiResponse<List<IncentiveRecordDTO>> getIncentiveRecords(
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String planId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<IncentiveRecordDTO> records = merchantEcosystemService.getIncentiveRecords(
                merchantId, planId, status, page, size);
        return ApiResponse.success(records);
    }

    @Operation(summary = "发放激励奖励")
    @PostMapping("/incentives/distribute")
    public ApiResponse<IncentiveDistributionResultDTO> distributeIncentiveRewards(
            @Valid @RequestBody DistributeIncentiveRequestDTO requestDTO) {
        
        IncentiveDistributionResultDTO result = merchantEcosystemService.distributeIncentiveRewards(requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 商家生态分析 ====================

    @Operation(summary = "获取商家生态概览")
    @GetMapping("/overview")
    public ApiResponse<MerchantEcosystemOverviewDTO> getMerchantEcosystemOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        MerchantEcosystemOverviewDTO overview = merchantEcosystemService.getMerchantEcosystemOverview(startDate, endDate);
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取商家成长分析")
    @GetMapping("/growth-analysis")
    public ApiResponse<MerchantGrowthAnalysisDTO> getMerchantGrowthAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        MerchantGrowthAnalysisDTO analysis = merchantEcosystemService.getMerchantGrowthAnalysis(startDate, endDate);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取商家健康度分析")
    @GetMapping("/health-analysis")
    public ApiResponse<MerchantHealthAnalysisDTO> getMerchantHealthAnalysis() {
        MerchantHealthAnalysisDTO analysis = merchantEcosystemService.getMerchantHealthAnalysis();
        return ApiResponse.success(analysis);
    }
}
