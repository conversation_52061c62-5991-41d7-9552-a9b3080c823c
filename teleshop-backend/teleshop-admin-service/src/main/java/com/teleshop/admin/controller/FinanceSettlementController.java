package com.teleshop.admin.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.admin.dto.finance.*;
import com.teleshop.admin.service.FinanceSettlementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "财务和结算管理", description = "财务和结算管理相关接口")
@RestController
@RequestMapping("/api/v1/admin/finance-settlement")
@RequiredArgsConstructor
public class FinanceSettlementController {

    private final FinanceSettlementService financeSettlementService;

    // ==================== 平台收入分析 ====================

    @Operation(summary = "获取平台收入概览")
    @GetMapping("/revenue/overview")
    public ApiResponse<PlatformRevenueOverviewDTO> getPlatformRevenueOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        PlatformRevenueOverviewDTO overview = financeSettlementService.getPlatformRevenueOverview(startDate, endDate);
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取收入明细")
    @GetMapping("/revenue/details")
    public ApiResponse<List<RevenueDetailDTO>> getRevenueDetails(
            @RequestParam(required = false) String revenueType,
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<RevenueDetailDTO> details = financeSettlementService.getRevenueDetails(
                revenueType, merchantId, startDate, endDate, page, size);
        return ApiResponse.success(details);
    }

    @Operation(summary = "获取收入趋势分析")
    @GetMapping("/revenue/trend-analysis")
    public ApiResponse<RevenueTrendAnalysisDTO> getRevenueTrendAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String granularity) {
        
        RevenueTrendAnalysisDTO analysis = financeSettlementService.getRevenueTrendAnalysis(
                startDate, endDate, granularity);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取收入来源分析")
    @GetMapping("/revenue/source-analysis")
    public ApiResponse<RevenueSourceAnalysisDTO> getRevenueSourceAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        RevenueSourceAnalysisDTO analysis = financeSettlementService.getRevenueSourceAnalysis(startDate, endDate);
        return ApiResponse.success(analysis);
    }

    // ==================== 佣金费率管理 ====================

    @Operation(summary = "获取佣金费率配置")
    @GetMapping("/commission-rates")
    public ApiResponse<List<CommissionRateDTO>> getCommissionRates(
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String merchantLevel) {
        
        List<CommissionRateDTO> rates = financeSettlementService.getCommissionRates(categoryId, merchantLevel);
        return ApiResponse.success(rates);
    }

    @Operation(summary = "设置佣金费率")
    @PostMapping("/commission-rates")
    public ApiResponse<CommissionRateDTO> setCommissionRate(
            @Valid @RequestBody SetCommissionRateRequestDTO requestDTO) {
        
        CommissionRateDTO rate = financeSettlementService.setCommissionRate(requestDTO);
        return ApiResponse.success(rate);
    }

    @Operation(summary = "更新佣金费率")
    @PutMapping("/commission-rates/{rateId}")
    public ApiResponse<CommissionRateDTO> updateCommissionRate(
            @PathVariable String rateId,
            @Valid @RequestBody UpdateCommissionRateRequestDTO requestDTO) {
        
        CommissionRateDTO rate = financeSettlementService.updateCommissionRate(rateId, requestDTO);
        return ApiResponse.success(rate);
    }

    @Operation(summary = "批量设置佣金费率")
    @PostMapping("/commission-rates/batch-set")
    public ApiResponse<BatchCommissionRateResultDTO> batchSetCommissionRates(
            @Valid @RequestBody BatchSetCommissionRateRequestDTO requestDTO) {
        
        BatchCommissionRateResultDTO result = financeSettlementService.batchSetCommissionRates(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取佣金费率历史")
    @GetMapping("/commission-rates/{rateId}/history")
    public ApiResponse<List<CommissionRateHistoryDTO>> getCommissionRateHistory(@PathVariable String rateId) {
        List<CommissionRateHistoryDTO> history = financeSettlementService.getCommissionRateHistory(rateId);
        return ApiResponse.success(history);
    }

    @Operation(summary = "获取佣金收入统计")
    @GetMapping("/commission/statistics")
    public ApiResponse<CommissionStatisticsDTO> getCommissionStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String categoryId) {
        
        CommissionStatisticsDTO statistics = financeSettlementService.getCommissionStatistics(
                startDate, endDate, categoryId);
        return ApiResponse.success(statistics);
    }

    // ==================== 结算周期配置 ====================

    @Operation(summary = "获取结算周期配置")
    @GetMapping("/settlement-cycles")
    public ApiResponse<List<SettlementCycleDTO>> getSettlementCycles() {
        List<SettlementCycleDTO> cycles = financeSettlementService.getSettlementCycles();
        return ApiResponse.success(cycles);
    }

    @Operation(summary = "设置结算周期")
    @PostMapping("/settlement-cycles")
    public ApiResponse<SettlementCycleDTO> setSettlementCycle(
            @Valid @RequestBody SetSettlementCycleRequestDTO requestDTO) {
        
        SettlementCycleDTO cycle = financeSettlementService.setSettlementCycle(requestDTO);
        return ApiResponse.success(cycle);
    }

    @Operation(summary = "更新结算周期")
    @PutMapping("/settlement-cycles/{cycleId}")
    public ApiResponse<SettlementCycleDTO> updateSettlementCycle(
            @PathVariable String cycleId,
            @Valid @RequestBody UpdateSettlementCycleRequestDTO requestDTO) {
        
        SettlementCycleDTO cycle = financeSettlementService.updateSettlementCycle(cycleId, requestDTO);
        return ApiResponse.success(cycle);
    }

    @Operation(summary = "获取结算记录")
    @GetMapping("/settlements")
    public ApiResponse<List<SettlementRecordDTO>> getSettlementRecords(
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<SettlementRecordDTO> records = financeSettlementService.getSettlementRecords(
                merchantId, status, startDate, endDate, page, size);
        return ApiResponse.success(records);
    }

    @Operation(summary = "执行结算")
    @PostMapping("/settlements/execute")
    public ApiResponse<SettlementExecutionResultDTO> executeSettlement(
            @Valid @RequestBody ExecuteSettlementRequestDTO requestDTO) {
        
        SettlementExecutionResultDTO result = financeSettlementService.executeSettlement(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "确认结算")
    @PostMapping("/settlements/{settlementId}/confirm")
    public ApiResponse<Void> confirmSettlement(@PathVariable String settlementId) {
        financeSettlementService.confirmSettlement(settlementId);
        return ApiResponse.success();
    }

    @Operation(summary = "获取结算详情")
    @GetMapping("/settlements/{settlementId}")
    public ApiResponse<SettlementDetailDTO> getSettlementDetail(@PathVariable String settlementId) {
        SettlementDetailDTO detail = financeSettlementService.getSettlementDetail(settlementId);
        return ApiResponse.success(detail);
    }

    // ==================== 税务管理 ====================

    @Operation(summary = "获取税务配置")
    @GetMapping("/tax-config")
    public ApiResponse<List<TaxConfigDTO>> getTaxConfigs(@RequestParam(required = false) String region) {
        List<TaxConfigDTO> configs = financeSettlementService.getTaxConfigs(region);
        return ApiResponse.success(configs);
    }

    @Operation(summary = "设置税务配置")
    @PostMapping("/tax-config")
    public ApiResponse<TaxConfigDTO> setTaxConfig(@Valid @RequestBody SetTaxConfigRequestDTO requestDTO) {
        TaxConfigDTO config = financeSettlementService.setTaxConfig(requestDTO);
        return ApiResponse.success(config);
    }

    @Operation(summary = "更新税务配置")
    @PutMapping("/tax-config/{configId}")
    public ApiResponse<TaxConfigDTO> updateTaxConfig(
            @PathVariable String configId,
            @Valid @RequestBody UpdateTaxConfigRequestDTO requestDTO) {
        
        TaxConfigDTO config = financeSettlementService.updateTaxConfig(configId, requestDTO);
        return ApiResponse.success(config);
    }

    @Operation(summary = "获取税务报表")
    @GetMapping("/tax-reports")
    public ApiResponse<List<TaxReportDTO>> getTaxReports(
            @RequestParam(required = false) String reportType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        List<TaxReportDTO> reports = financeSettlementService.getTaxReports(reportType, startDate, endDate);
        return ApiResponse.success(reports);
    }

    @Operation(summary = "生成税务报表")
    @PostMapping("/tax-reports/generate")
    public ApiResponse<TaxReportGenerationResultDTO> generateTaxReport(
            @Valid @RequestBody GenerateTaxReportRequestDTO requestDTO) {
        
        TaxReportGenerationResultDTO result = financeSettlementService.generateTaxReport(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取发票管理")
    @GetMapping("/invoices")
    public ApiResponse<List<InvoiceDTO>> getInvoices(
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String invoiceType,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<InvoiceDTO> invoices = financeSettlementService.getInvoices(merchantId, invoiceType, status, page, size);
        return ApiResponse.success(invoices);
    }

    @Operation(summary = "开具发票")
    @PostMapping("/invoices")
    public ApiResponse<InvoiceDTO> issueInvoice(@Valid @RequestBody IssueInvoiceRequestDTO requestDTO) {
        InvoiceDTO invoice = financeSettlementService.issueInvoice(requestDTO);
        return ApiResponse.success(invoice);
    }

    // ==================== 财务对账 ====================

    @Operation(summary = "获取对账记录")
    @GetMapping("/reconciliation")
    public ApiResponse<List<ReconciliationRecordDTO>> getReconciliationRecords(
            @RequestParam(required = false) String reconciliationType,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<ReconciliationRecordDTO> records = financeSettlementService.getReconciliationRecords(
                reconciliationType, status, startDate, endDate, page, size);
        return ApiResponse.success(records);
    }

    @Operation(summary = "执行对账")
    @PostMapping("/reconciliation/execute")
    public ApiResponse<ReconciliationExecutionResultDTO> executeReconciliation(
            @Valid @RequestBody ExecuteReconciliationRequestDTO requestDTO) {
        
        ReconciliationExecutionResultDTO result = financeSettlementService.executeReconciliation(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取对账差异")
    @GetMapping("/reconciliation/{recordId}/discrepancies")
    public ApiResponse<List<ReconciliationDiscrepancyDTO>> getReconciliationDiscrepancies(
            @PathVariable String recordId) {
        
        List<ReconciliationDiscrepancyDTO> discrepancies = financeSettlementService.getReconciliationDiscrepancies(recordId);
        return ApiResponse.success(discrepancies);
    }

    @Operation(summary = "处理对账差异")
    @PostMapping("/reconciliation/discrepancies/{discrepancyId}/handle")
    public ApiResponse<ReconciliationDiscrepancyHandleResultDTO> handleReconciliationDiscrepancy(
            @PathVariable String discrepancyId,
            @Valid @RequestBody HandleReconciliationDiscrepancyRequestDTO requestDTO) {
        
        ReconciliationDiscrepancyHandleResultDTO result = financeSettlementService.handleReconciliationDiscrepancy(
                discrepancyId, requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 成本分析 ====================

    @Operation(summary = "获取成本分析概览")
    @GetMapping("/cost-analysis/overview")
    public ApiResponse<CostAnalysisOverviewDTO> getCostAnalysisOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        CostAnalysisOverviewDTO overview = financeSettlementService.getCostAnalysisOverview(startDate, endDate);
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取成本明细")
    @GetMapping("/cost-analysis/details")
    public ApiResponse<List<CostDetailDTO>> getCostDetails(
            @RequestParam(required = false) String costType,
            @RequestParam(required = false) String department,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<CostDetailDTO> details = financeSettlementService.getCostDetails(
                costType, department, startDate, endDate, page, size);
        return ApiResponse.success(details);
    }

    @Operation(summary = "获取成本趋势分析")
    @GetMapping("/cost-analysis/trend")
    public ApiResponse<CostTrendAnalysisDTO> getCostTrendAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String granularity) {
        
        CostTrendAnalysisDTO analysis = financeSettlementService.getCostTrendAnalysis(startDate, endDate, granularity);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取成本结构分析")
    @GetMapping("/cost-analysis/structure")
    public ApiResponse<CostStructureAnalysisDTO> getCostStructureAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        CostStructureAnalysisDTO analysis = financeSettlementService.getCostStructureAnalysis(startDate, endDate);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "设置成本预算")
    @PostMapping("/cost-budget")
    public ApiResponse<CostBudgetDTO> setCostBudget(@Valid @RequestBody SetCostBudgetRequestDTO requestDTO) {
        CostBudgetDTO budget = financeSettlementService.setCostBudget(requestDTO);
        return ApiResponse.success(budget);
    }

    @Operation(summary = "获取成本预算执行情况")
    @GetMapping("/cost-budget/execution")
    public ApiResponse<CostBudgetExecutionDTO> getCostBudgetExecution(
            @RequestParam(required = false) String budgetPeriod) {
        
        CostBudgetExecutionDTO execution = financeSettlementService.getCostBudgetExecution(budgetPeriod);
        return ApiResponse.success(execution);
    }

    // ==================== 财务报表 ====================

    @Operation(summary = "获取财务报表列表")
    @GetMapping("/financial-reports")
    public ApiResponse<List<FinancialReportDTO>> getFinancialReports(
            @RequestParam(required = false) String reportType,
            @RequestParam(required = false) String period) {
        
        List<FinancialReportDTO> reports = financeSettlementService.getFinancialReports(reportType, period);
        return ApiResponse.success(reports);
    }

    @Operation(summary = "生成财务报表")
    @PostMapping("/financial-reports/generate")
    public ApiResponse<FinancialReportGenerationResultDTO> generateFinancialReport(
            @Valid @RequestBody GenerateFinancialReportRequestDTO requestDTO) {
        
        FinancialReportGenerationResultDTO result = financeSettlementService.generateFinancialReport(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "导出财务报表")
    @PostMapping("/financial-reports/export")
    public ApiResponse<String> exportFinancialReport(
            @Valid @RequestBody ExportFinancialReportRequestDTO requestDTO) {
        
        String downloadUrl = financeSettlementService.exportFinancialReport(requestDTO);
        return ApiResponse.success(downloadUrl);
    }

    @Operation(summary = "获取财务概览")
    @GetMapping("/financial-overview")
    public ApiResponse<FinancialOverviewDTO> getFinancialOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        FinancialOverviewDTO overview = financeSettlementService.getFinancialOverview(startDate, endDate);
        return ApiResponse.success(overview);
    }
}
