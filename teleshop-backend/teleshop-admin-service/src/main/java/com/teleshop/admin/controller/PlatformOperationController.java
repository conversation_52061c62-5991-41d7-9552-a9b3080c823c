package com.teleshop.admin.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.admin.dto.operation.*;
import com.teleshop.admin.service.PlatformOperationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "平台运营管理", description = "平台运营管理相关接口")
@RestController
@RequestMapping("/api/v1/admin/platform-operation")
@RequiredArgsConstructor
public class PlatformOperationController {

    private final PlatformOperationService platformOperationService;

    // ==================== 平台活动策划和执行 ====================

    @Operation(summary = "获取平台活动列表")
    @GetMapping("/activities")
    public ApiResponse<List<PlatformActivityDTO>> getPlatformActivities(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String activityType,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<PlatformActivityDTO> activities = platformOperationService.getPlatformActivities(
                status, activityType, page, size);
        return ApiResponse.success(activities);
    }

    @Operation(summary = "创建平台活动")
    @PostMapping("/activities")
    public ApiResponse<PlatformActivityDTO> createPlatformActivity(
            @Valid @RequestBody CreatePlatformActivityRequestDTO requestDTO) {
        
        PlatformActivityDTO activity = platformOperationService.createPlatformActivity(requestDTO);
        return ApiResponse.success(activity);
    }

    @Operation(summary = "更新平台活动")
    @PutMapping("/activities/{activityId}")
    public ApiResponse<PlatformActivityDTO> updatePlatformActivity(
            @PathVariable String activityId,
            @Valid @RequestBody UpdatePlatformActivityRequestDTO requestDTO) {
        
        PlatformActivityDTO activity = platformOperationService.updatePlatformActivity(activityId, requestDTO);
        return ApiResponse.success(activity);
    }

    @Operation(summary = "发布平台活动")
    @PostMapping("/activities/{activityId}/publish")
    public ApiResponse<ActivityPublishResultDTO> publishPlatformActivity(
            @PathVariable String activityId,
            @Valid @RequestBody ActivityPublishRequestDTO requestDTO) {
        
        ActivityPublishResultDTO result = platformOperationService.publishPlatformActivity(activityId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "停止平台活动")
    @PostMapping("/activities/{activityId}/stop")
    public ApiResponse<Void> stopPlatformActivity(
            @PathVariable String activityId,
            @RequestParam String reason) {
        
        platformOperationService.stopPlatformActivity(activityId, reason);
        return ApiResponse.success();
    }

    @Operation(summary = "获取活动效果统计")
    @GetMapping("/activities/{activityId}/statistics")
    public ApiResponse<ActivityStatisticsDTO> getActivityStatistics(@PathVariable String activityId) {
        ActivityStatisticsDTO statistics = platformOperationService.getActivityStatistics(activityId);
        return ApiResponse.success(statistics);
    }

    // ==================== 首页轮播图管理 ====================

    @Operation(summary = "获取轮播图列表")
    @GetMapping("/banners")
    public ApiResponse<List<BannerDTO>> getBanners(
            @RequestParam(required = false) String position,
            @RequestParam(required = false) String status) {
        
        List<BannerDTO> banners = platformOperationService.getBanners(position, status);
        return ApiResponse.success(banners);
    }

    @Operation(summary = "创建轮播图")
    @PostMapping("/banners")
    public ApiResponse<BannerDTO> createBanner(@Valid @RequestBody CreateBannerRequestDTO requestDTO) {
        BannerDTO banner = platformOperationService.createBanner(requestDTO);
        return ApiResponse.success(banner);
    }

    @Operation(summary = "更新轮播图")
    @PutMapping("/banners/{bannerId}")
    public ApiResponse<BannerDTO> updateBanner(
            @PathVariable String bannerId,
            @Valid @RequestBody UpdateBannerRequestDTO requestDTO) {
        
        BannerDTO banner = platformOperationService.updateBanner(bannerId, requestDTO);
        return ApiResponse.success(banner);
    }

    @Operation(summary = "删除轮播图")
    @DeleteMapping("/banners/{bannerId}")
    public ApiResponse<Void> deleteBanner(@PathVariable String bannerId) {
        platformOperationService.deleteBanner(bannerId);
        return ApiResponse.success();
    }

    @Operation(summary = "调整轮播图排序")
    @PostMapping("/banners/reorder")
    public ApiResponse<List<BannerDTO>> reorderBanners(
            @Valid @RequestBody BannerReorderRequestDTO requestDTO) {
        
        List<BannerDTO> banners = platformOperationService.reorderBanners(requestDTO);
        return ApiResponse.success(banners);
    }

    @Operation(summary = "上传轮播图图片")
    @PostMapping("/banners/upload-image")
    public ApiResponse<String> uploadBannerImage(@RequestParam("file") MultipartFile file) {
        String imageUrl = platformOperationService.uploadBannerImage(file);
        return ApiResponse.success(imageUrl);
    }

    // ==================== 热门推荐位管理 ====================

    @Operation(summary = "获取推荐位列表")
    @GetMapping("/recommendations")
    public ApiResponse<List<RecommendationSlotDTO>> getRecommendationSlots(
            @RequestParam(required = false) String position,
            @RequestParam(required = false) String status) {
        
        List<RecommendationSlotDTO> slots = platformOperationService.getRecommendationSlots(position, status);
        return ApiResponse.success(slots);
    }

    @Operation(summary = "创建推荐位")
    @PostMapping("/recommendations")
    public ApiResponse<RecommendationSlotDTO> createRecommendationSlot(
            @Valid @RequestBody CreateRecommendationSlotRequestDTO requestDTO) {
        
        RecommendationSlotDTO slot = platformOperationService.createRecommendationSlot(requestDTO);
        return ApiResponse.success(slot);
    }

    @Operation(summary = "更新推荐位")
    @PutMapping("/recommendations/{slotId}")
    public ApiResponse<RecommendationSlotDTO> updateRecommendationSlot(
            @PathVariable String slotId,
            @Valid @RequestBody UpdateRecommendationSlotRequestDTO requestDTO) {
        
        RecommendationSlotDTO slot = platformOperationService.updateRecommendationSlot(slotId, requestDTO);
        return ApiResponse.success(slot);
    }

    @Operation(summary = "删除推荐位")
    @DeleteMapping("/recommendations/{slotId}")
    public ApiResponse<Void> deleteRecommendationSlot(@PathVariable String slotId) {
        platformOperationService.deleteRecommendationSlot(slotId);
        return ApiResponse.success();
    }

    @Operation(summary = "设置推荐商品")
    @PostMapping("/recommendations/{slotId}/products")
    public ApiResponse<RecommendationSlotDTO> setRecommendationProducts(
            @PathVariable String slotId,
            @Valid @RequestBody SetRecommendationProductsRequestDTO requestDTO) {
        
        RecommendationSlotDTO slot = platformOperationService.setRecommendationProducts(slotId, requestDTO);
        return ApiResponse.success(slot);
    }

    // ==================== 分类导航管理 ====================

    @Operation(summary = "获取分类导航树")
    @GetMapping("/categories/navigation")
    public ApiResponse<List<CategoryNavigationDTO>> getCategoryNavigation() {
        List<CategoryNavigationDTO> navigation = platformOperationService.getCategoryNavigation();
        return ApiResponse.success(navigation);
    }

    @Operation(summary = "更新分类导航")
    @PutMapping("/categories/navigation")
    public ApiResponse<List<CategoryNavigationDTO>> updateCategoryNavigation(
            @Valid @RequestBody UpdateCategoryNavigationRequestDTO requestDTO) {
        
        List<CategoryNavigationDTO> navigation = platformOperationService.updateCategoryNavigation(requestDTO);
        return ApiResponse.success(navigation);
    }

    @Operation(summary = "设置分类推荐")
    @PostMapping("/categories/{categoryId}/featured")
    public ApiResponse<CategoryFeaturedDTO> setCategoryFeatured(
            @PathVariable String categoryId,
            @Valid @RequestBody SetCategoryFeaturedRequestDTO requestDTO) {
        
        CategoryFeaturedDTO featured = platformOperationService.setCategoryFeatured(categoryId, requestDTO);
        return ApiResponse.success(featured);
    }

    // ==================== 搜索热词管理 ====================

    @Operation(summary = "获取搜索热词列表")
    @GetMapping("/search-keywords")
    public ApiResponse<List<SearchKeywordDTO>> getSearchKeywords(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {
        
        List<SearchKeywordDTO> keywords = platformOperationService.getSearchKeywords(type, status);
        return ApiResponse.success(keywords);
    }

    @Operation(summary = "创建搜索热词")
    @PostMapping("/search-keywords")
    public ApiResponse<SearchKeywordDTO> createSearchKeyword(
            @Valid @RequestBody CreateSearchKeywordRequestDTO requestDTO) {
        
        SearchKeywordDTO keyword = platformOperationService.createSearchKeyword(requestDTO);
        return ApiResponse.success(keyword);
    }

    @Operation(summary = "更新搜索热词")
    @PutMapping("/search-keywords/{keywordId}")
    public ApiResponse<SearchKeywordDTO> updateSearchKeyword(
            @PathVariable String keywordId,
            @Valid @RequestBody UpdateSearchKeywordRequestDTO requestDTO) {
        
        SearchKeywordDTO keyword = platformOperationService.updateSearchKeyword(keywordId, requestDTO);
        return ApiResponse.success(keyword);
    }

    @Operation(summary = "删除搜索热词")
    @DeleteMapping("/search-keywords/{keywordId}")
    public ApiResponse<Void> deleteSearchKeyword(@PathVariable String keywordId) {
        platformOperationService.deleteSearchKeyword(keywordId);
        return ApiResponse.success();
    }

    @Operation(summary = "获取搜索热词统计")
    @GetMapping("/search-keywords/statistics")
    public ApiResponse<SearchKeywordStatisticsDTO> getSearchKeywordStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        SearchKeywordStatisticsDTO statistics = platformOperationService.getSearchKeywordStatistics(startDate, endDate);
        return ApiResponse.success(statistics);
    }

    // ==================== 内容审核工作台 ====================

    @Operation(summary = "获取待审核内容列表")
    @GetMapping("/content-review/pending")
    public ApiResponse<List<ContentReviewDTO>> getPendingContentReviews(
            @RequestParam(required = false) String contentType,
            @RequestParam(required = false) String priority,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<ContentReviewDTO> reviews = platformOperationService.getPendingContentReviews(
                contentType, priority, page, size);
        return ApiResponse.success(reviews);
    }

    @Operation(summary = "审核内容")
    @PostMapping("/content-review/{reviewId}/review")
    public ApiResponse<ContentReviewResultDTO> reviewContent(
            @PathVariable String reviewId,
            @Valid @RequestBody ReviewContentRequestDTO requestDTO) {
        
        ContentReviewResultDTO result = platformOperationService.reviewContent(reviewId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "批量审核内容")
    @PostMapping("/content-review/batch-review")
    public ApiResponse<BatchContentReviewResultDTO> batchReviewContent(
            @Valid @RequestBody BatchReviewContentRequestDTO requestDTO) {
        
        BatchContentReviewResultDTO result = platformOperationService.batchReviewContent(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取审核统计")
    @GetMapping("/content-review/statistics")
    public ApiResponse<ContentReviewStatisticsDTO> getContentReviewStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String reviewerId) {
        
        ContentReviewStatisticsDTO statistics = platformOperationService.getContentReviewStatistics(
                startDate, endDate, reviewerId);
        return ApiResponse.success(statistics);
    }

    @Operation(summary = "设置审核规则")
    @PostMapping("/content-review/rules")
    public ApiResponse<ContentReviewRuleDTO> setContentReviewRule(
            @Valid @RequestBody SetContentReviewRuleRequestDTO requestDTO) {
        
        ContentReviewRuleDTO rule = platformOperationService.setContentReviewRule(requestDTO);
        return ApiResponse.success(rule);
    }

    @Operation(summary = "获取审核规则列表")
    @GetMapping("/content-review/rules")
    public ApiResponse<List<ContentReviewRuleDTO>> getContentReviewRules(
            @RequestParam(required = false) String contentType) {
        
        List<ContentReviewRuleDTO> rules = platformOperationService.getContentReviewRules(contentType);
        return ApiResponse.success(rules);
    }

    // ==================== 运营数据分析 ====================

    @Operation(summary = "获取运营概览")
    @GetMapping("/overview")
    public ApiResponse<OperationOverviewDTO> getOperationOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        OperationOverviewDTO overview = platformOperationService.getOperationOverview(startDate, endDate);
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取内容运营统计")
    @GetMapping("/content-statistics")
    public ApiResponse<ContentOperationStatisticsDTO> getContentOperationStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        ContentOperationStatisticsDTO statistics = platformOperationService.getContentOperationStatistics(
                startDate, endDate);
        return ApiResponse.success(statistics);
    }

    @Operation(summary = "获取活动效果分析")
    @GetMapping("/activity-analysis")
    public ApiResponse<ActivityEffectivenessAnalysisDTO> getActivityEffectivenessAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String activityType) {
        
        ActivityEffectivenessAnalysisDTO analysis = platformOperationService.getActivityEffectivenessAnalysis(
                startDate, endDate, activityType);
        return ApiResponse.success(analysis);
    }
}
