package com.teleshop.admin.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.admin.dto.service.*;
import com.teleshop.admin.service.CustomerServiceManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "客服和投诉管理", description = "客服和投诉管理相关接口")
@RestController
@RequestMapping("/api/v1/admin/customer-service")
@RequiredArgsConstructor
public class CustomerServiceController {

    private final CustomerServiceManagementService customerServiceManagementService;

    // ==================== 投诉工单系统 ====================

    @Operation(summary = "获取投诉工单列表")
    @GetMapping("/complaints")
    public ApiResponse<List<ComplaintTicketDTO>> getComplaintTickets(
            @RequestParam(required = false) String complaintType,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String priority,
            @RequestParam(required = false) String assigneeId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<ComplaintTicketDTO> tickets = customerServiceManagementService.getComplaintTickets(
                complaintType, status, priority, assigneeId, page, size);
        return ApiResponse.success(tickets);
    }

    @Operation(summary = "获取投诉工单详情")
    @GetMapping("/complaints/{ticketId}")
    public ApiResponse<ComplaintTicketDetailDTO> getComplaintTicketDetail(@PathVariable String ticketId) {
        ComplaintTicketDetailDTO detail = customerServiceManagementService.getComplaintTicketDetail(ticketId);
        return ApiResponse.success(detail);
    }

    @Operation(summary = "分配投诉工单")
    @PostMapping("/complaints/{ticketId}/assign")
    public ApiResponse<Void> assignComplaintTicket(
            @PathVariable String ticketId,
            @Valid @RequestBody AssignTicketRequestDTO requestDTO) {
        
        customerServiceManagementService.assignComplaintTicket(ticketId, requestDTO);
        return ApiResponse.success();
    }

    @Operation(summary = "处理投诉工单")
    @PostMapping("/complaints/{ticketId}/handle")
    public ApiResponse<ComplaintHandleResultDTO> handleComplaintTicket(
            @PathVariable String ticketId,
            @Valid @RequestBody HandleComplaintRequestDTO requestDTO) {
        
        ComplaintHandleResultDTO result = customerServiceManagementService.handleComplaintTicket(ticketId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "关闭投诉工单")
    @PostMapping("/complaints/{ticketId}/close")
    public ApiResponse<Void> closeComplaintTicket(
            @PathVariable String ticketId,
            @Valid @RequestBody CloseTicketRequestDTO requestDTO) {
        
        customerServiceManagementService.closeComplaintTicket(ticketId, requestDTO);
        return ApiResponse.success();
    }

    @Operation(summary = "重新打开投诉工单")
    @PostMapping("/complaints/{ticketId}/reopen")
    public ApiResponse<Void> reopenComplaintTicket(
            @PathVariable String ticketId,
            @RequestParam String reason) {
        
        customerServiceManagementService.reopenComplaintTicket(ticketId, reason);
        return ApiResponse.success();
    }

    @Operation(summary = "批量处理投诉工单")
    @PostMapping("/complaints/batch-handle")
    public ApiResponse<BatchComplaintHandleResultDTO> batchHandleComplaintTickets(
            @Valid @RequestBody BatchHandleComplaintRequestDTO requestDTO) {
        
        BatchComplaintHandleResultDTO result = customerServiceManagementService.batchHandleComplaintTickets(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取投诉统计")
    @GetMapping("/complaints/statistics")
    public ApiResponse<ComplaintStatisticsDTO> getComplaintStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String groupBy) {
        
        ComplaintStatisticsDTO statistics = customerServiceManagementService.getComplaintStatistics(
                startDate, endDate, groupBy);
        return ApiResponse.success(statistics);
    }

    // ==================== 客服绩效管理 ====================

    @Operation(summary = "获取客服人员列表")
    @GetMapping("/agents")
    public ApiResponse<List<CustomerServiceAgentDTO>> getCustomerServiceAgents(
            @RequestParam(required = false) String department,
            @RequestParam(required = false) String status) {
        
        List<CustomerServiceAgentDTO> agents = customerServiceManagementService.getCustomerServiceAgents(
                department, status);
        return ApiResponse.success(agents);
    }

    @Operation(summary = "获取客服绩效")
    @GetMapping("/agents/{agentId}/performance")
    public ApiResponse<AgentPerformanceDTO> getAgentPerformance(
            @PathVariable String agentId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        AgentPerformanceDTO performance = customerServiceManagementService.getAgentPerformance(
                agentId, startDate, endDate);
        return ApiResponse.success(performance);
    }

    @Operation(summary = "获取客服工作量统计")
    @GetMapping("/agents/workload-statistics")
    public ApiResponse<List<AgentWorkloadStatisticsDTO>> getAgentWorkloadStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        List<AgentWorkloadStatisticsDTO> statistics = customerServiceManagementService.getAgentWorkloadStatistics(
                startDate, endDate);
        return ApiResponse.success(statistics);
    }

    @Operation(summary = "设置绩效目标")
    @PostMapping("/agents/{agentId}/performance-target")
    public ApiResponse<PerformanceTargetDTO> setPerformanceTarget(
            @PathVariable String agentId,
            @Valid @RequestBody SetPerformanceTargetRequestDTO requestDTO) {
        
        PerformanceTargetDTO target = customerServiceManagementService.setPerformanceTarget(agentId, requestDTO);
        return ApiResponse.success(target);
    }

    @Operation(summary = "获取绩效排行榜")
    @GetMapping("/agents/performance-ranking")
    public ApiResponse<List<AgentPerformanceRankingDTO>> getAgentPerformanceRanking(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String rankingType) {
        
        List<AgentPerformanceRankingDTO> ranking = customerServiceManagementService.getAgentPerformanceRanking(
                startDate, endDate, rankingType);
        return ApiResponse.success(ranking);
    }

    // ==================== 服务质量监控 ====================

    @Operation(summary = "获取服务质量监控")
    @GetMapping("/quality-monitoring")
    public ApiResponse<List<ServiceQualityMonitoringDTO>> getServiceQualityMonitoring(
            @RequestParam(required = false) String agentId,
            @RequestParam(required = false) String monitoringType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<ServiceQualityMonitoringDTO> monitoring = customerServiceManagementService.getServiceQualityMonitoring(
                agentId, monitoringType, startDate, endDate, page, size);
        return ApiResponse.success(monitoring);
    }

    @Operation(summary = "创建质量监控记录")
    @PostMapping("/quality-monitoring")
    public ApiResponse<ServiceQualityMonitoringDTO> createQualityMonitoring(
            @Valid @RequestBody CreateQualityMonitoringRequestDTO requestDTO) {
        
        ServiceQualityMonitoringDTO monitoring = customerServiceManagementService.createQualityMonitoring(requestDTO);
        return ApiResponse.success(monitoring);
    }

    @Operation(summary = "获取服务质量评分")
    @GetMapping("/quality-scores")
    public ApiResponse<List<ServiceQualityScoreDTO>> getServiceQualityScores(
            @RequestParam(required = false) String agentId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        List<ServiceQualityScoreDTO> scores = customerServiceManagementService.getServiceQualityScores(
                agentId, startDate, endDate);
        return ApiResponse.success(scores);
    }

    @Operation(summary = "设置质量标准")
    @PostMapping("/quality-standards")
    public ApiResponse<QualityStandardDTO> setQualityStandard(
            @Valid @RequestBody SetQualityStandardRequestDTO requestDTO) {
        
        QualityStandardDTO standard = customerServiceManagementService.setQualityStandard(requestDTO);
        return ApiResponse.success(standard);
    }

    @Operation(summary = "获取质量标准列表")
    @GetMapping("/quality-standards")
    public ApiResponse<List<QualityStandardDTO>> getQualityStandards() {
        List<QualityStandardDTO> standards = customerServiceManagementService.getQualityStandards();
        return ApiResponse.success(standards);
    }

    // ==================== 纠纷仲裁流程 ====================

    @Operation(summary = "获取纠纷仲裁案例")
    @GetMapping("/arbitrations")
    public ApiResponse<List<ArbitrationCaseDTO>> getArbitrationCases(
            @RequestParam(required = false) String caseType,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String arbitratorId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<ArbitrationCaseDTO> cases = customerServiceManagementService.getArbitrationCases(
                caseType, status, arbitratorId, page, size);
        return ApiResponse.success(cases);
    }

    @Operation(summary = "获取仲裁案例详情")
    @GetMapping("/arbitrations/{caseId}")
    public ApiResponse<ArbitrationCaseDetailDTO> getArbitrationCaseDetail(@PathVariable String caseId) {
        ArbitrationCaseDetailDTO detail = customerServiceManagementService.getArbitrationCaseDetail(caseId);
        return ApiResponse.success(detail);
    }

    @Operation(summary = "分配仲裁员")
    @PostMapping("/arbitrations/{caseId}/assign-arbitrator")
    public ApiResponse<Void> assignArbitrator(
            @PathVariable String caseId,
            @Valid @RequestBody AssignArbitratorRequestDTO requestDTO) {
        
        customerServiceManagementService.assignArbitrator(caseId, requestDTO);
        return ApiResponse.success();
    }

    @Operation(summary = "提交仲裁决定")
    @PostMapping("/arbitrations/{caseId}/decision")
    public ApiResponse<ArbitrationDecisionResultDTO> submitArbitrationDecision(
            @PathVariable String caseId,
            @Valid @RequestBody SubmitArbitrationDecisionRequestDTO requestDTO) {
        
        ArbitrationDecisionResultDTO result = customerServiceManagementService.submitArbitrationDecision(
                caseId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "执行仲裁决定")
    @PostMapping("/arbitrations/{caseId}/execute")
    public ApiResponse<ArbitrationExecutionResultDTO> executeArbitrationDecision(@PathVariable String caseId) {
        ArbitrationExecutionResultDTO result = customerServiceManagementService.executeArbitrationDecision(caseId);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取仲裁统计")
    @GetMapping("/arbitrations/statistics")
    public ApiResponse<ArbitrationStatisticsDTO> getArbitrationStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        ArbitrationStatisticsDTO statistics = customerServiceManagementService.getArbitrationStatistics(
                startDate, endDate);
        return ApiResponse.success(statistics);
    }

    // ==================== 客服知识库管理 ====================

    @Operation(summary = "获取知识库文章")
    @GetMapping("/knowledge-base/articles")
    public ApiResponse<List<KnowledgeBaseArticleDTO>> getKnowledgeBaseArticles(
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status) {
        
        List<KnowledgeBaseArticleDTO> articles = customerServiceManagementService.getKnowledgeBaseArticles(
                categoryId, keyword, status);
        return ApiResponse.success(articles);
    }

    @Operation(summary = "创建知识库文章")
    @PostMapping("/knowledge-base/articles")
    public ApiResponse<KnowledgeBaseArticleDTO> createKnowledgeBaseArticle(
            @Valid @RequestBody CreateKnowledgeBaseArticleRequestDTO requestDTO) {
        
        KnowledgeBaseArticleDTO article = customerServiceManagementService.createKnowledgeBaseArticle(requestDTO);
        return ApiResponse.success(article);
    }

    @Operation(summary = "更新知识库文章")
    @PutMapping("/knowledge-base/articles/{articleId}")
    public ApiResponse<KnowledgeBaseArticleDTO> updateKnowledgeBaseArticle(
            @PathVariable String articleId,
            @Valid @RequestBody UpdateKnowledgeBaseArticleRequestDTO requestDTO) {
        
        KnowledgeBaseArticleDTO article = customerServiceManagementService.updateKnowledgeBaseArticle(
                articleId, requestDTO);
        return ApiResponse.success(article);
    }

    @Operation(summary = "删除知识库文章")
    @DeleteMapping("/knowledge-base/articles/{articleId}")
    public ApiResponse<Void> deleteKnowledgeBaseArticle(@PathVariable String articleId) {
        customerServiceManagementService.deleteKnowledgeBaseArticle(articleId);
        return ApiResponse.success();
    }

    @Operation(summary = "获取知识库分类")
    @GetMapping("/knowledge-base/categories")
    public ApiResponse<List<KnowledgeBaseCategoryDTO>> getKnowledgeBaseCategories() {
        List<KnowledgeBaseCategoryDTO> categories = customerServiceManagementService.getKnowledgeBaseCategories();
        return ApiResponse.success(categories);
    }

    @Operation(summary = "创建知识库分类")
    @PostMapping("/knowledge-base/categories")
    public ApiResponse<KnowledgeBaseCategoryDTO> createKnowledgeBaseCategory(
            @Valid @RequestBody CreateKnowledgeBaseCategoryRequestDTO requestDTO) {
        
        KnowledgeBaseCategoryDTO category = customerServiceManagementService.createKnowledgeBaseCategory(requestDTO);
        return ApiResponse.success(category);
    }

    @Operation(summary = "获取知识库使用统计")
    @GetMapping("/knowledge-base/usage-statistics")
    public ApiResponse<KnowledgeBaseUsageStatisticsDTO> getKnowledgeBaseUsageStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        KnowledgeBaseUsageStatisticsDTO statistics = customerServiceManagementService.getKnowledgeBaseUsageStatistics(
                startDate, endDate);
        return ApiResponse.success(statistics);
    }

    // ==================== 服务标准制定 ====================

    @Operation(summary = "获取服务标准")
    @GetMapping("/service-standards")
    public ApiResponse<List<ServiceStandardDTO>> getServiceStandards(
            @RequestParam(required = false) String standardType) {
        
        List<ServiceStandardDTO> standards = customerServiceManagementService.getServiceStandards(standardType);
        return ApiResponse.success(standards);
    }

    @Operation(summary = "创建服务标准")
    @PostMapping("/service-standards")
    public ApiResponse<ServiceStandardDTO> createServiceStandard(
            @Valid @RequestBody CreateServiceStandardRequestDTO requestDTO) {
        
        ServiceStandardDTO standard = customerServiceManagementService.createServiceStandard(requestDTO);
        return ApiResponse.success(standard);
    }

    @Operation(summary = "更新服务标准")
    @PutMapping("/service-standards/{standardId}")
    public ApiResponse<ServiceStandardDTO> updateServiceStandard(
            @PathVariable String standardId,
            @Valid @RequestBody UpdateServiceStandardRequestDTO requestDTO) {
        
        ServiceStandardDTO standard = customerServiceManagementService.updateServiceStandard(standardId, requestDTO);
        return ApiResponse.success(standard);
    }

    @Operation(summary = "发布服务标准")
    @PostMapping("/service-standards/{standardId}/publish")
    public ApiResponse<Void> publishServiceStandard(@PathVariable String standardId) {
        customerServiceManagementService.publishServiceStandard(standardId);
        return ApiResponse.success();
    }

    @Operation(summary = "获取服务标准执行情况")
    @GetMapping("/service-standards/compliance")
    public ApiResponse<List<ServiceStandardComplianceDTO>> getServiceStandardCompliance(
            @RequestParam(required = false) String standardId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        List<ServiceStandardComplianceDTO> compliance = customerServiceManagementService.getServiceStandardCompliance(
                standardId, startDate, endDate);
        return ApiResponse.success(compliance);
    }

    // ==================== 客服概览和统计 ====================

    @Operation(summary = "获取客服概览")
    @GetMapping("/overview")
    public ApiResponse<CustomerServiceOverviewDTO> getCustomerServiceOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        CustomerServiceOverviewDTO overview = customerServiceManagementService.getCustomerServiceOverview(
                startDate, endDate);
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取客服效率分析")
    @GetMapping("/efficiency-analysis")
    public ApiResponse<CustomerServiceEfficiencyAnalysisDTO> getCustomerServiceEfficiencyAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        CustomerServiceEfficiencyAnalysisDTO analysis = customerServiceManagementService.getCustomerServiceEfficiencyAnalysis(
                startDate, endDate);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取客户满意度分析")
    @GetMapping("/satisfaction-analysis")
    public ApiResponse<CustomerSatisfactionAnalysisDTO> getCustomerSatisfactionAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        CustomerSatisfactionAnalysisDTO analysis = customerServiceManagementService.getCustomerSatisfactionAnalysis(
                startDate, endDate);
        return ApiResponse.success(analysis);
    }
}
