package com.teleshop.admin.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.admin.dto.analytics.*;
import com.teleshop.admin.service.DataAnalyticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "数据分析和决策支持", description = "数据分析和决策支持相关接口")
@RestController
@RequestMapping("/api/v1/admin/data-analytics")
@RequiredArgsConstructor
public class DataAnalyticsController {

    private final DataAnalyticsService dataAnalyticsService;

    // ==================== 平台经营大屏 ====================

    @Operation(summary = "获取平台经营大屏数据")
    @GetMapping("/dashboard")
    public ApiResponse<PlatformDashboardDTO> getPlatformDashboard(
            @RequestParam(required = false) String timeRange) {
        
        PlatformDashboardDTO dashboard = dataAnalyticsService.getPlatformDashboard(timeRange);
        return ApiResponse.success(dashboard);
    }

    @Operation(summary = "获取实时业务指标")
    @GetMapping("/real-time-metrics")
    public ApiResponse<RealTimeMetricsDTO> getRealTimeMetrics() {
        RealTimeMetricsDTO metrics = dataAnalyticsService.getRealTimeMetrics();
        return ApiResponse.success(metrics);
    }

    @Operation(summary = "获取核心KPI指标")
    @GetMapping("/core-kpis")
    public ApiResponse<CoreKPIMetricsDTO> getCoreKPIMetrics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String granularity) {
        
        CoreKPIMetricsDTO kpis = dataAnalyticsService.getCoreKPIMetrics(startDate, endDate, granularity);
        return ApiResponse.success(kpis);
    }

    @Operation(summary = "获取业务趋势分析")
    @GetMapping("/business-trends")
    public ApiResponse<BusinessTrendAnalysisDTO> getBusinessTrendAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String metricType) {
        
        BusinessTrendAnalysisDTO trends = dataAnalyticsService.getBusinessTrendAnalysis(
                startDate, endDate, metricType);
        return ApiResponse.success(trends);
    }

    @Operation(summary = "获取地域分布分析")
    @GetMapping("/geographic-distribution")
    public ApiResponse<GeographicDistributionDTO> getGeographicDistribution(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String metricType) {
        
        GeographicDistributionDTO distribution = dataAnalyticsService.getGeographicDistribution(
                startDate, endDate, metricType);
        return ApiResponse.success(distribution);
    }

    // ==================== 用户行为分析 ====================

    @Operation(summary = "获取用户行为概览")
    @GetMapping("/user-behavior/overview")
    public ApiResponse<UserBehaviorOverviewDTO> getUserBehaviorOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        UserBehaviorOverviewDTO overview = dataAnalyticsService.getUserBehaviorOverview(startDate, endDate);
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取用户路径分析")
    @GetMapping("/user-behavior/path-analysis")
    public ApiResponse<UserPathAnalysisDTO> getUserPathAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String pathType) {
        
        UserPathAnalysisDTO pathAnalysis = dataAnalyticsService.getUserPathAnalysis(startDate, endDate, pathType);
        return ApiResponse.success(pathAnalysis);
    }

    @Operation(summary = "获取用户留存分析")
    @GetMapping("/user-behavior/retention-analysis")
    public ApiResponse<UserRetentionAnalysisDTO> getUserRetentionAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String cohortType) {
        
        UserRetentionAnalysisDTO retention = dataAnalyticsService.getUserRetentionAnalysis(
                startDate, endDate, cohortType);
        return ApiResponse.success(retention);
    }

    @Operation(summary = "获取用户画像分析")
    @GetMapping("/user-behavior/profile-analysis")
    @PostMapping("/user-behavior/profile-analysis")
    public ApiResponse<UserProfileAnalysisDTO> getUserProfileAnalysis(
            @Valid @RequestBody UserProfileAnalysisRequestDTO requestDTO) {
        
        UserProfileAnalysisDTO profile = dataAnalyticsService.getUserProfileAnalysis(requestDTO);
        return ApiResponse.success(profile);
    }

    @Operation(summary = "获取用户生命周期分析")
    @GetMapping("/user-behavior/lifecycle-analysis")
    public ApiResponse<UserLifecycleAnalysisDTO> getUserLifecycleAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        UserLifecycleAnalysisDTO lifecycle = dataAnalyticsService.getUserLifecycleAnalysis(startDate, endDate);
        return ApiResponse.success(lifecycle);
    }

    @Operation(summary = "获取用户流失分析")
    @GetMapping("/user-behavior/churn-analysis")
    public ApiResponse<UserChurnAnalysisDTO> getUserChurnAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        UserChurnAnalysisDTO churn = dataAnalyticsService.getUserChurnAnalysis(startDate, endDate);
        return ApiResponse.success(churn);
    }

    // ==================== 市场趋势分析 ====================

    @Operation(summary = "获取市场趋势概览")
    @GetMapping("/market-trends/overview")
    public ApiResponse<MarketTrendOverviewDTO> getMarketTrendOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        MarketTrendOverviewDTO overview = dataAnalyticsService.getMarketTrendOverview(startDate, endDate);
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取品类趋势分析")
    @GetMapping("/market-trends/category-trends")
    public ApiResponse<CategoryTrendAnalysisDTO> getCategoryTrendAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String categoryId) {
        
        CategoryTrendAnalysisDTO trends = dataAnalyticsService.getCategoryTrendAnalysis(
                startDate, endDate, categoryId);
        return ApiResponse.success(trends);
    }

    @Operation(summary = "获取价格趋势分析")
    @GetMapping("/market-trends/price-trends")
    public ApiResponse<PriceTrendAnalysisDTO> getPriceTrendAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String categoryId) {
        
        PriceTrendAnalysisDTO trends = dataAnalyticsService.getPriceTrendAnalysis(startDate, endDate, categoryId);
        return ApiResponse.success(trends);
    }

    @Operation(summary = "获取季节性分析")
    @GetMapping("/market-trends/seasonality-analysis")
    public ApiResponse<SeasonalityAnalysisDTO> getSeasonalityAnalysis(
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String analysisType) {
        
        SeasonalityAnalysisDTO seasonality = dataAnalyticsService.getSeasonalityAnalysis(categoryId, analysisType);
        return ApiResponse.success(seasonality);
    }

    @Operation(summary = "获取热门商品分析")
    @GetMapping("/market-trends/hot-products")
    public ApiResponse<HotProductAnalysisDTO> getHotProductAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String categoryId) {
        
        HotProductAnalysisDTO hotProducts = dataAnalyticsService.getHotProductAnalysis(
                startDate, endDate, categoryId);
        return ApiResponse.success(hotProducts);
    }

    // ==================== 竞品分析 ====================

    @Operation(summary = "获取竞品概览")
    @GetMapping("/competitor-analysis/overview")
    public ApiResponse<CompetitorOverviewDTO> getCompetitorOverview() {
        CompetitorOverviewDTO overview = dataAnalyticsService.getCompetitorOverview();
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取竞品价格对比")
    @GetMapping("/competitor-analysis/price-comparison")
    public ApiResponse<CompetitorPriceComparisonDTO> getCompetitorPriceComparison(
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String productId) {
        
        CompetitorPriceComparisonDTO comparison = dataAnalyticsService.getCompetitorPriceComparison(
                categoryId, productId);
        return ApiResponse.success(comparison);
    }

    @Operation(summary = "获取竞品市场份额分析")
    @GetMapping("/competitor-analysis/market-share")
    public ApiResponse<CompetitorMarketShareDTO> getCompetitorMarketShare(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String categoryId) {
        
        CompetitorMarketShareDTO marketShare = dataAnalyticsService.getCompetitorMarketShare(
                startDate, endDate, categoryId);
        return ApiResponse.success(marketShare);
    }

    @Operation(summary = "获取竞品策略分析")
    @GetMapping("/competitor-analysis/strategy-analysis")
    public ApiResponse<CompetitorStrategyAnalysisDTO> getCompetitorStrategyAnalysis(
            @RequestParam(required = false) String competitorId,
            @RequestParam(required = false) String analysisType) {
        
        CompetitorStrategyAnalysisDTO strategy = dataAnalyticsService.getCompetitorStrategyAnalysis(
                competitorId, analysisType);
        return ApiResponse.success(strategy);
    }

    @Operation(summary = "添加竞品监控")
    @PostMapping("/competitor-analysis/monitoring")
    public ApiResponse<CompetitorMonitoringDTO> addCompetitorMonitoring(
            @Valid @RequestBody AddCompetitorMonitoringRequestDTO requestDTO) {
        
        CompetitorMonitoringDTO monitoring = dataAnalyticsService.addCompetitorMonitoring(requestDTO);
        return ApiResponse.success(monitoring);
    }

    @Operation(summary = "获取竞品监控列表")
    @GetMapping("/competitor-analysis/monitoring")
    public ApiResponse<List<CompetitorMonitoringDTO>> getCompetitorMonitoringList() {
        List<CompetitorMonitoringDTO> monitoringList = dataAnalyticsService.getCompetitorMonitoringList();
        return ApiResponse.success(monitoringList);
    }

    // ==================== 业务预测模型 ====================

    @Operation(summary = "获取销售预测")
    @GetMapping("/prediction/sales-forecast")
    public ApiResponse<SalesForecastDTO> getSalesForecast(
            @RequestParam(required = false) String forecastPeriod,
            @RequestParam(required = false) String granularity,
            @RequestParam(required = false) String categoryId) {
        
        SalesForecastDTO forecast = dataAnalyticsService.getSalesForecast(forecastPeriod, granularity, categoryId);
        return ApiResponse.success(forecast);
    }

    @Operation(summary = "获取用户增长预测")
    @GetMapping("/prediction/user-growth-forecast")
    public ApiResponse<UserGrowthForecastDTO> getUserGrowthForecast(
            @RequestParam(required = false) String forecastPeriod,
            @RequestParam(required = false) String userType) {
        
        UserGrowthForecastDTO forecast = dataAnalyticsService.getUserGrowthForecast(forecastPeriod, userType);
        return ApiResponse.success(forecast);
    }

    @Operation(summary = "获取库存需求预测")
    @GetMapping("/prediction/inventory-demand-forecast")
    public ApiResponse<InventoryDemandForecastDTO> getInventoryDemandForecast(
            @RequestParam(required = false) String forecastPeriod,
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String warehouseId) {
        
        InventoryDemandForecastDTO forecast = dataAnalyticsService.getInventoryDemandForecast(
                forecastPeriod, categoryId, warehouseId);
        return ApiResponse.success(forecast);
    }

    @Operation(summary = "获取市场趋势预测")
    @GetMapping("/prediction/market-trend-forecast")
    public ApiResponse<MarketTrendForecastDTO> getMarketTrendForecast(
            @RequestParam(required = false) String forecastPeriod,
            @RequestParam(required = false) String trendType) {
        
        MarketTrendForecastDTO forecast = dataAnalyticsService.getMarketTrendForecast(forecastPeriod, trendType);
        return ApiResponse.success(forecast);
    }

    @Operation(summary = "创建自定义预测模型")
    @PostMapping("/prediction/custom-model")
    public ApiResponse<CustomPredictionModelDTO> createCustomPredictionModel(
            @Valid @RequestBody CreateCustomPredictionModelRequestDTO requestDTO) {
        
        CustomPredictionModelDTO model = dataAnalyticsService.createCustomPredictionModel(requestDTO);
        return ApiResponse.success(model);
    }

    @Operation(summary = "获取预测模型列表")
    @GetMapping("/prediction/models")
    public ApiResponse<List<PredictionModelDTO>> getPredictionModels(
            @RequestParam(required = false) String modelType,
            @RequestParam(required = false) String status) {
        
        List<PredictionModelDTO> models = dataAnalyticsService.getPredictionModels(modelType, status);
        return ApiResponse.success(models);
    }

    // ==================== 决策支持系统 ====================

    @Operation(summary = "获取决策建议")
    @GetMapping("/decision-support/recommendations")
    public ApiResponse<List<DecisionRecommendationDTO>> getDecisionRecommendations(
            @RequestParam(required = false) String decisionType,
            @RequestParam(required = false) String priority) {
        
        List<DecisionRecommendationDTO> recommendations = dataAnalyticsService.getDecisionRecommendations(
                decisionType, priority);
        return ApiResponse.success(recommendations);
    }

    @Operation(summary = "获取风险预警")
    @GetMapping("/decision-support/risk-alerts")
    public ApiResponse<List<RiskAlertDTO>> getRiskAlerts(
            @RequestParam(required = false) String riskType,
            @RequestParam(required = false) String severity) {
        
        List<RiskAlertDTO> alerts = dataAnalyticsService.getRiskAlerts(riskType, severity);
        return ApiResponse.success(alerts);
    }

    @Operation(summary = "获取机会识别")
    @GetMapping("/decision-support/opportunity-identification")
    public ApiResponse<List<OpportunityIdentificationDTO>> getOpportunityIdentification(
            @RequestParam(required = false) String opportunityType,
            @RequestParam(required = false) String timeframe) {
        
        List<OpportunityIdentificationDTO> opportunities = dataAnalyticsService.getOpportunityIdentification(
                opportunityType, timeframe);
        return ApiResponse.success(opportunities);
    }

    @Operation(summary = "获取策略效果评估")
    @GetMapping("/decision-support/strategy-evaluation")
    public ApiResponse<StrategyEvaluationDTO> getStrategyEvaluation(
            @RequestParam String strategyId,
            @RequestParam(required = false) String evaluationPeriod) {
        
        StrategyEvaluationDTO evaluation = dataAnalyticsService.getStrategyEvaluation(strategyId, evaluationPeriod);
        return ApiResponse.success(evaluation);
    }

    @Operation(summary = "创建决策方案")
    @PostMapping("/decision-support/decision-plan")
    public ApiResponse<DecisionPlanDTO> createDecisionPlan(
            @Valid @RequestBody CreateDecisionPlanRequestDTO requestDTO) {
        
        DecisionPlanDTO plan = dataAnalyticsService.createDecisionPlan(requestDTO);
        return ApiResponse.success(plan);
    }

    @Operation(summary = "获取决策方案列表")
    @GetMapping("/decision-support/decision-plans")
    public ApiResponse<List<DecisionPlanDTO>> getDecisionPlans(
            @RequestParam(required = false) String planType,
            @RequestParam(required = false) String status) {
        
        List<DecisionPlanDTO> plans = dataAnalyticsService.getDecisionPlans(planType, status);
        return ApiResponse.success(plans);
    }

    @Operation(summary = "执行决策方案")
    @PostMapping("/decision-support/decision-plans/{planId}/execute")
    public ApiResponse<DecisionPlanExecutionResultDTO> executeDecisionPlan(@PathVariable String planId) {
        DecisionPlanExecutionResultDTO result = dataAnalyticsService.executeDecisionPlan(planId);
        return ApiResponse.success(result);
    }

    // ==================== 自定义报表 ====================

    @Operation(summary = "创建自定义报表")
    @PostMapping("/custom-reports")
    public ApiResponse<CustomReportDTO> createCustomReport(
            @Valid @RequestBody CreateCustomReportRequestDTO requestDTO) {
        
        CustomReportDTO report = dataAnalyticsService.createCustomReport(requestDTO);
        return ApiResponse.success(report);
    }

    @Operation(summary = "获取自定义报表列表")
    @GetMapping("/custom-reports")
    public ApiResponse<List<CustomReportDTO>> getCustomReports(
            @RequestParam(required = false) String reportType,
            @RequestParam(required = false) String creatorId) {
        
        List<CustomReportDTO> reports = dataAnalyticsService.getCustomReports(reportType, creatorId);
        return ApiResponse.success(reports);
    }

    @Operation(summary = "生成报表数据")
    @PostMapping("/custom-reports/{reportId}/generate")
    public ApiResponse<CustomReportDataDTO> generateCustomReportData(
            @PathVariable String reportId,
            @Valid @RequestBody GenerateCustomReportRequestDTO requestDTO) {
        
        CustomReportDataDTO data = dataAnalyticsService.generateCustomReportData(reportId, requestDTO);
        return ApiResponse.success(data);
    }

    @Operation(summary = "导出报表")
    @PostMapping("/custom-reports/{reportId}/export")
    public ApiResponse<String> exportCustomReport(
            @PathVariable String reportId,
            @Valid @RequestBody ExportCustomReportRequestDTO requestDTO) {
        
        String downloadUrl = dataAnalyticsService.exportCustomReport(reportId, requestDTO);
        return ApiResponse.success(downloadUrl);
    }

    // ==================== 数据质量监控 ====================

    @Operation(summary = "获取数据质量概览")
    @GetMapping("/data-quality/overview")
    public ApiResponse<DataQualityOverviewDTO> getDataQualityOverview() {
        DataQualityOverviewDTO overview = dataAnalyticsService.getDataQualityOverview();
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取数据质量检查结果")
    @GetMapping("/data-quality/check-results")
    public ApiResponse<List<DataQualityCheckResultDTO>> getDataQualityCheckResults(
            @RequestParam(required = false) String dataSource,
            @RequestParam(required = false) String checkType) {
        
        List<DataQualityCheckResultDTO> results = dataAnalyticsService.getDataQualityCheckResults(
                dataSource, checkType);
        return ApiResponse.success(results);
    }

    @Operation(summary = "执行数据质量检查")
    @PostMapping("/data-quality/execute-check")
    public ApiResponse<DataQualityCheckExecutionResultDTO> executeDataQualityCheck(
            @Valid @RequestBody ExecuteDataQualityCheckRequestDTO requestDTO) {
        
        DataQualityCheckExecutionResultDTO result = dataAnalyticsService.executeDataQualityCheck(requestDTO);
        return ApiResponse.success(result);
    }
}
