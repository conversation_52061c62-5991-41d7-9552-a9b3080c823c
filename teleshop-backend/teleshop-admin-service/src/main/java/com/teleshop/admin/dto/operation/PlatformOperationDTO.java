package com.teleshop.admin.dto.operation;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 平台活动DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformActivityDTO {

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型：PROMOTION(促销活动), FESTIVAL(节日活动), BRAND(品牌活动), CATEGORY(品类活动)
     */
    private String activityType;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 活动封面图
     */
    private String coverImage;

    /**
     * 活动详情页URL
     */
    private String detailUrl;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动状态：DRAFT(草稿), PUBLISHED(已发布), RUNNING(进行中), ENDED(已结束), CANCELLED(已取消)
     */
    private String status;

    /**
     * 参与商家列表
     */
    private List<String> participatingMerchants;

    /**
     * 活动商品列表
     */
    private List<ActivityProduct> activityProducts;

    /**
     * 活动规则
     */
    private ActivityRules rules;

    /**
     * 活动统计
     */
    private ActivityStatistics statistics;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityProduct {
        private String productId;
        private String productName;
        private String productImage;
        private BigDecimal originalPrice;
        private BigDecimal activityPrice;
        private Integer stock;
        private Integer soldCount;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityRules {
        private String participationCondition; // 参与条件
        private Integer maxParticipants; // 最大参与人数
        private Integer limitPerUser; // 每人限购数量
        private List<String> applicableRegions; // 适用地区
        private Map<String, Object> customRules; // 自定义规则
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityStatistics {
        private Integer totalParticipants; // 总参与人数
        private Integer totalOrders; // 总订单数
        private BigDecimal totalRevenue; // 总收入
        private Integer pageViews; // 页面浏览量
        private Double conversionRate; // 转化率
    }
}

/**
 * 轮播图DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BannerDTO {

    /**
     * 轮播图ID
     */
    private String bannerId;

    /**
     * 轮播图标题
     */
    private String title;

    /**
     * 轮播图图片URL
     */
    private String imageUrl;

    /**
     * 跳转链接
     */
    private String linkUrl;

    /**
     * 跳转类型：PRODUCT(商品), CATEGORY(分类), ACTIVITY(活动), EXTERNAL(外部链接)
     */
    private String linkType;

    /**
     * 跳转目标ID
     */
    private String targetId;

    /**
     * 显示位置：HOME_BANNER(首页轮播), CATEGORY_BANNER(分类页轮播)
     */
    private String position;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;

    /**
     * 点击统计
     */
    private BannerStatistics statistics;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BannerStatistics {
        private Integer totalClicks; // 总点击数
        private Integer todayClicks; // 今日点击数
        private Double clickRate; // 点击率
        private Integer exposures; // 曝光次数
    }
}

/**
 * 推荐位DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendationSlotDTO {

    /**
     * 推荐位ID
     */
    private String slotId;

    /**
     * 推荐位名称
     */
    private String slotName;

    /**
     * 推荐位位置：HOME_HOT(首页热门), HOME_NEW(首页新品), CATEGORY_RECOMMEND(分类推荐)
     */
    private String position;

    /**
     * 推荐类型：MANUAL(手动), AUTO(自动), HYBRID(混合)
     */
    private String recommendationType;

    /**
     * 推荐商品列表
     */
    private List<RecommendedProduct> recommendedProducts;

    /**
     * 推荐规则
     */
    private RecommendationRules rules;

    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;

    /**
     * 更新频率：REAL_TIME(实时), HOURLY(每小时), DAILY(每日)
     */
    private String updateFrequency;

    /**
     * 推荐效果统计
     */
    private RecommendationStatistics statistics;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecommendedProduct {
        private String productId;
        private String productName;
        private String productImage;
        private BigDecimal price;
        private Integer salesCount;
        private Double score; // 推荐分数
        private String reason; // 推荐理由
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecommendationRules {
        private List<String> categories; // 适用分类
        private BigDecimal minPrice; // 最低价格
        private BigDecimal maxPrice; // 最高价格
        private Integer minSales; // 最低销量
        private Double minRating; // 最低评分
        private List<String> excludeProducts; // 排除商品
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecommendationStatistics {
        private Integer totalClicks; // 总点击数
        private Integer totalExposures; // 总曝光数
        private Double clickRate; // 点击率
        private Integer conversions; // 转化数
        private Double conversionRate; // 转化率
    }
}

/**
 * 分类导航DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryNavigationDTO {

    /**
     * 分类ID
     */
    private String categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类图标
     */
    private String categoryIcon;

    /**
     * 分类图片
     */
    private String categoryImage;

    /**
     * 父分类ID
     */
    private String parentCategoryId;

    /**
     * 分类层级
     */
    private Integer level;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 是否在导航中显示
     */
    private Boolean showInNavigation;

    /**
     * 是否为热门分类
     */
    private Boolean isHot;

    /**
     * 子分类列表
     */
    private List<CategoryNavigationDTO> children;

    /**
     * 推荐商品
     */
    private List<String> featuredProducts;

    /**
     * 分类统计
     */
    private CategoryStatistics statistics;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryStatistics {
        private Integer productCount; // 商品数量
        private Integer merchantCount; // 商家数量
        private Integer monthlyViews; // 月浏览量
        private Integer monthlyOrders; // 月订单量
    }
}

/**
 * 搜索热词DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchKeywordDTO {

    /**
     * 热词ID
     */
    private String keywordId;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 热词类型：HOT(热门), TRENDING(趋势), RECOMMENDED(推荐)
     */
    private String type;

    /**
     * 热度分数
     */
    private Double hotScore;

    /**
     * 搜索次数
     */
    private Integer searchCount;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;

    /**
     * 关联商品数量
     */
    private Integer relatedProductCount;

    /**
     * 转化率
     */
    private Double conversionRate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 统计数据
     */
    private KeywordStatistics statistics;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KeywordStatistics {
        private Integer todaySearchCount; // 今日搜索次数
        private Integer weeklySearchCount; // 周搜索次数
        private Integer monthlySearchCount; // 月搜索次数
        private Double searchTrend; // 搜索趋势（增长率）
        private Integer resultCount; // 搜索结果数量
    }
}

/**
 * 内容审核DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentReviewDTO {

    /**
     * 审核ID
     */
    private String reviewId;

    /**
     * 内容类型：PRODUCT(商品), MERCHANT(商家), COMMENT(评论), ACTIVITY(活动)
     */
    private String contentType;

    /**
     * 内容ID
     */
    private String contentId;

    /**
     * 内容标题
     */
    private String contentTitle;

    /**
     * 内容摘要
     */
    private String contentSummary;

    /**
     * 提交人
     */
    private String submitter;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 审核状态：PENDING(待审核), APPROVED(通过), REJECTED(拒绝), REVIEWING(审核中)
     */
    private String reviewStatus;

    /**
     * 审核优先级：LOW(低), MEDIUM(中), HIGH(高), URGENT(紧急)
     */
    private String priority;

    /**
     * 审核员ID
     */
    private String reviewerId;

    /**
     * 审核员姓名
     */
    private String reviewerName;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核意见
     */
    private String reviewComment;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 风险等级：LOW(低风险), MEDIUM(中风险), HIGH(高风险)
     */
    private String riskLevel;

    /**
     * 自动审核结果
     */
    private AutoReviewResult autoReviewResult;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AutoReviewResult {
        private Boolean passed; // 是否通过自动审核
        private Double confidenceScore; // 置信度分数
        private List<String> riskPoints; // 风险点
        private List<String> suggestions; // 建议
    }
}
