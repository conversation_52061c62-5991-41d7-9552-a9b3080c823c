package com.teleshop.admin.controller;

import com.teleshop.common.response.ApiResponse;
import com.teleshop.admin.dto.risk.*;
import com.teleshop.admin.service.RiskControlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Tag(name = "风控和安全管理", description = "风控和安全管理相关接口")
@RestController
@RequestMapping("/api/v1/admin/risk-control")
@RequiredArgsConstructor
public class RiskControlController {

    private final RiskControlService riskControlService;

    // ==================== 风险商家识别 ====================

    @Operation(summary = "获取风险商家列表")
    @GetMapping("/risky-merchants")
    public ApiResponse<List<RiskyMerchantDTO>> getRiskyMerchants(
            @RequestParam(required = false) String riskLevel,
            @RequestParam(required = false) String riskType,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<RiskyMerchantDTO> merchants = riskControlService.getRiskyMerchants(
                riskLevel, riskType, status, page, size);
        return ApiResponse.success(merchants);
    }

    @Operation(summary = "获取商家风险评估")
    @GetMapping("/merchants/{merchantId}/risk-assessment")
    public ApiResponse<MerchantRiskAssessmentDTO> getMerchantRiskAssessment(@PathVariable String merchantId) {
        MerchantRiskAssessmentDTO assessment = riskControlService.getMerchantRiskAssessment(merchantId);
        return ApiResponse.success(assessment);
    }

    @Operation(summary = "执行商家风险扫描")
    @PostMapping("/merchants/risk-scan")
    public ApiResponse<RiskScanResultDTO> executeMerchantRiskScan(
            @Valid @RequestBody MerchantRiskScanRequestDTO requestDTO) {
        
        RiskScanResultDTO result = riskControlService.executeMerchantRiskScan(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "处理风险商家")
    @PostMapping("/risky-merchants/{merchantId}/handle")
    public ApiResponse<RiskHandleResultDTO> handleRiskyMerchant(
            @PathVariable String merchantId,
            @Valid @RequestBody HandleRiskyMerchantRequestDTO requestDTO) {
        
        RiskHandleResultDTO result = riskControlService.handleRiskyMerchant(merchantId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "设置风险识别规则")
    @PostMapping("/risk-rules")
    public ApiResponse<RiskRuleDTO> setRiskRule(@Valid @RequestBody SetRiskRuleRequestDTO requestDTO) {
        RiskRuleDTO rule = riskControlService.setRiskRule(requestDTO);
        return ApiResponse.success(rule);
    }

    @Operation(summary = "获取风险识别规则列表")
    @GetMapping("/risk-rules")
    public ApiResponse<List<RiskRuleDTO>> getRiskRules(@RequestParam(required = false) String ruleType) {
        List<RiskRuleDTO> rules = riskControlService.getRiskRules(ruleType);
        return ApiResponse.success(rules);
    }

    // ==================== 异常交易监控 ====================

    @Operation(summary = "获取异常交易列表")
    @GetMapping("/abnormal-transactions")
    public ApiResponse<List<AbnormalTransactionDTO>> getAbnormalTransactions(
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String riskLevel,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<AbnormalTransactionDTO> transactions = riskControlService.getAbnormalTransactions(
                transactionType, riskLevel, status, page, size);
        return ApiResponse.success(transactions);
    }

    @Operation(summary = "获取交易风险分析")
    @GetMapping("/transactions/{transactionId}/risk-analysis")
    public ApiResponse<TransactionRiskAnalysisDTO> getTransactionRiskAnalysis(@PathVariable String transactionId) {
        TransactionRiskAnalysisDTO analysis = riskControlService.getTransactionRiskAnalysis(transactionId);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "处理异常交易")
    @PostMapping("/abnormal-transactions/{transactionId}/handle")
    public ApiResponse<TransactionHandleResultDTO> handleAbnormalTransaction(
            @PathVariable String transactionId,
            @Valid @RequestBody HandleAbnormalTransactionRequestDTO requestDTO) {
        
        TransactionHandleResultDTO result = riskControlService.handleAbnormalTransaction(transactionId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "设置交易监控规则")
    @PostMapping("/transaction-monitor-rules")
    public ApiResponse<TransactionMonitorRuleDTO> setTransactionMonitorRule(
            @Valid @RequestBody SetTransactionMonitorRuleRequestDTO requestDTO) {
        
        TransactionMonitorRuleDTO rule = riskControlService.setTransactionMonitorRule(requestDTO);
        return ApiResponse.success(rule);
    }

    @Operation(summary = "获取交易监控统计")
    @GetMapping("/transaction-monitor/statistics")
    public ApiResponse<TransactionMonitorStatisticsDTO> getTransactionMonitorStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        TransactionMonitorStatisticsDTO statistics = riskControlService.getTransactionMonitorStatistics(
                startDate, endDate);
        return ApiResponse.success(statistics);
    }

    // ==================== 刷单行为检测 ====================

    @Operation(summary = "获取刷单检测结果")
    @GetMapping("/fake-orders")
    public ApiResponse<List<FakeOrderDetectionDTO>> getFakeOrderDetections(
            @RequestParam(required = false) String merchantId,
            @RequestParam(required = false) String detectionType,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<FakeOrderDetectionDTO> detections = riskControlService.getFakeOrderDetections(
                merchantId, detectionType, status, page, size);
        return ApiResponse.success(detections);
    }

    @Operation(summary = "执行刷单检测")
    @PostMapping("/fake-orders/detect")
    public ApiResponse<FakeOrderDetectionResultDTO> executeFakeOrderDetection(
            @Valid @RequestBody FakeOrderDetectionRequestDTO requestDTO) {
        
        FakeOrderDetectionResultDTO result = riskControlService.executeFakeOrderDetection(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "处理刷单行为")
    @PostMapping("/fake-orders/{detectionId}/handle")
    public ApiResponse<FakeOrderHandleResultDTO> handleFakeOrder(
            @PathVariable String detectionId,
            @Valid @RequestBody HandleFakeOrderRequestDTO requestDTO) {
        
        FakeOrderHandleResultDTO result = riskControlService.handleFakeOrder(detectionId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "设置刷单检测规则")
    @PostMapping("/fake-order-rules")
    public ApiResponse<FakeOrderRuleDTO> setFakeOrderRule(
            @Valid @RequestBody SetFakeOrderRuleRequestDTO requestDTO) {
        
        FakeOrderRuleDTO rule = riskControlService.setFakeOrderRule(requestDTO);
        return ApiResponse.success(rule);
    }

    @Operation(summary = "获取刷单检测统计")
    @GetMapping("/fake-orders/statistics")
    public ApiResponse<FakeOrderStatisticsDTO> getFakeOrderStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        FakeOrderStatisticsDTO statistics = riskControlService.getFakeOrderStatistics(startDate, endDate);
        return ApiResponse.success(statistics);
    }

    // ==================== 恶意用户识别 ====================

    @Operation(summary = "获取恶意用户列表")
    @GetMapping("/malicious-users")
    public ApiResponse<List<MaliciousUserDTO>> getMaliciousUsers(
            @RequestParam(required = false) String userType,
            @RequestParam(required = false) String riskLevel,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<MaliciousUserDTO> users = riskControlService.getMaliciousUsers(
                userType, riskLevel, status, page, size);
        return ApiResponse.success(users);
    }

    @Operation(summary = "获取用户风险画像")
    @GetMapping("/users/{userId}/risk-profile")
    public ApiResponse<UserRiskProfileDTO> getUserRiskProfile(@PathVariable String userId) {
        UserRiskProfileDTO profile = riskControlService.getUserRiskProfile(userId);
        return ApiResponse.success(profile);
    }

    @Operation(summary = "执行用户风险扫描")
    @PostMapping("/users/risk-scan")
    public ApiResponse<UserRiskScanResultDTO> executeUserRiskScan(
            @Valid @RequestBody UserRiskScanRequestDTO requestDTO) {
        
        UserRiskScanResultDTO result = riskControlService.executeUserRiskScan(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "处理恶意用户")
    @PostMapping("/malicious-users/{userId}/handle")
    public ApiResponse<MaliciousUserHandleResultDTO> handleMaliciousUser(
            @PathVariable String userId,
            @Valid @RequestBody HandleMaliciousUserRequestDTO requestDTO) {
        
        MaliciousUserHandleResultDTO result = riskControlService.handleMaliciousUser(userId, requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 数据安全审计 ====================

    @Operation(summary = "获取数据访问日志")
    @GetMapping("/data-access-logs")
    public ApiResponse<List<DataAccessLogDTO>> getDataAccessLogs(
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String dataType,
            @RequestParam(required = false) String operation,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<DataAccessLogDTO> logs = riskControlService.getDataAccessLogs(
                userId, dataType, operation, startDate, endDate, page, size);
        return ApiResponse.success(logs);
    }

    @Operation(summary = "获取敏感数据访问记录")
    @GetMapping("/sensitive-data-access")
    public ApiResponse<List<SensitiveDataAccessDTO>> getSensitiveDataAccess(
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String dataCategory,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<SensitiveDataAccessDTO> access = riskControlService.getSensitiveDataAccess(
                userId, dataCategory, startDate, endDate, page, size);
        return ApiResponse.success(access);
    }

    @Operation(summary = "执行数据安全扫描")
    @PostMapping("/data-security-scan")
    public ApiResponse<DataSecurityScanResultDTO> executeDataSecurityScan(
            @Valid @RequestBody DataSecurityScanRequestDTO requestDTO) {
        
        DataSecurityScanResultDTO result = riskControlService.executeDataSecurityScan(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取数据安全统计")
    @GetMapping("/data-security/statistics")
    public ApiResponse<DataSecurityStatisticsDTO> getDataSecurityStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        DataSecurityStatisticsDTO statistics = riskControlService.getDataSecurityStatistics(startDate, endDate);
        return ApiResponse.success(statistics);
    }

    // ==================== 隐私保护合规 ====================

    @Operation(summary = "获取隐私合规检查结果")
    @GetMapping("/privacy-compliance")
    public ApiResponse<List<PrivacyComplianceCheckDTO>> getPrivacyComplianceChecks(
            @RequestParam(required = false) String checkType,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<PrivacyComplianceCheckDTO> checks = riskControlService.getPrivacyComplianceChecks(
                checkType, status, page, size);
        return ApiResponse.success(checks);
    }

    @Operation(summary = "执行隐私合规检查")
    @PostMapping("/privacy-compliance/check")
    public ApiResponse<PrivacyComplianceCheckResultDTO> executePrivacyComplianceCheck(
            @Valid @RequestBody PrivacyComplianceCheckRequestDTO requestDTO) {
        
        PrivacyComplianceCheckResultDTO result = riskControlService.executePrivacyComplianceCheck(requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "处理隐私合规问题")
    @PostMapping("/privacy-compliance/{checkId}/handle")
    public ApiResponse<PrivacyComplianceHandleResultDTO> handlePrivacyComplianceIssue(
            @PathVariable String checkId,
            @Valid @RequestBody HandlePrivacyComplianceRequestDTO requestDTO) {
        
        PrivacyComplianceHandleResultDTO result = riskControlService.handlePrivacyComplianceIssue(
                checkId, requestDTO);
        return ApiResponse.success(result);
    }

    @Operation(summary = "获取用户数据删除请求")
    @GetMapping("/data-deletion-requests")
    public ApiResponse<List<DataDeletionRequestDTO>> getDataDeletionRequests(
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<DataDeletionRequestDTO> requests = riskControlService.getDataDeletionRequests(status, page, size);
        return ApiResponse.success(requests);
    }

    @Operation(summary = "处理数据删除请求")
    @PostMapping("/data-deletion-requests/{requestId}/handle")
    public ApiResponse<DataDeletionHandleResultDTO> handleDataDeletionRequest(
            @PathVariable String requestId,
            @Valid @RequestBody HandleDataDeletionRequestDTO requestDTO) {
        
        DataDeletionHandleResultDTO result = riskControlService.handleDataDeletionRequest(requestId, requestDTO);
        return ApiResponse.success(result);
    }

    // ==================== 风控概览和统计 ====================

    @Operation(summary = "获取风控概览")
    @GetMapping("/overview")
    public ApiResponse<RiskControlOverviewDTO> getRiskControlOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        RiskControlOverviewDTO overview = riskControlService.getRiskControlOverview(startDate, endDate);
        return ApiResponse.success(overview);
    }

    @Operation(summary = "获取风险趋势分析")
    @GetMapping("/risk-trend-analysis")
    public ApiResponse<RiskTrendAnalysisDTO> getRiskTrendAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String riskType) {
        
        RiskTrendAnalysisDTO analysis = riskControlService.getRiskTrendAnalysis(startDate, endDate, riskType);
        return ApiResponse.success(analysis);
    }

    @Operation(summary = "获取风控效果评估")
    @GetMapping("/effectiveness-evaluation")
    public ApiResponse<RiskControlEffectivenessDTO> getRiskControlEffectiveness(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        RiskControlEffectivenessDTO effectiveness = riskControlService.getRiskControlEffectiveness(
                startDate, endDate);
        return ApiResponse.success(effectiveness);
    }
}
