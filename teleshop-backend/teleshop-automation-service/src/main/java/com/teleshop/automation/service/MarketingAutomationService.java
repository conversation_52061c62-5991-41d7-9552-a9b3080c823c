package com.teleshop.automation.service;

import com.teleshop.automation.entity.MarketingCampaign;
import com.teleshop.automation.entity.CampaignExecution;
import com.teleshop.automation.entity.CustomerSegment;
import com.teleshop.automation.repository.MarketingCampaignRepository;
import com.teleshop.automation.repository.CampaignExecutionRepository;
import com.teleshop.automation.repository.CustomerSegmentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 营销活动自动化服务
 * 提供营销活动的自动化创建、执行、监控和优化功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MarketingAutomationService {
    
    private final MarketingCampaignRepository campaignRepository;
    private final CampaignExecutionRepository executionRepository;
    private final CustomerSegmentRepository segmentRepository;
    private final RabbitTemplate rabbitTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CustomerAnalysisService customerAnalysisService;
    private final CouponService couponService;
    private final NotificationService notificationService;
    private final RecommendationService recommendationService;
    
    // Redis键前缀
    private static final String CAMPAIGN_LOCK_KEY = "campaign_lock:";
    private static final String CAMPAIGN_STATS_KEY = "campaign_stats:";
    private static final String USER_CAMPAIGN_KEY = "user_campaign:";
    
    // 消息队列
    private static final String MARKETING_QUEUE = "teleshop.marketing.campaign";
    private static final String COUPON_QUEUE = "teleshop.coupon.distribute";
    private static final String NOTIFICATION_QUEUE = "teleshop.notification.send";
    
    /**
     * 创建自动化营销活动
     */
    @Transactional
    public MarketingCampaign createAutomatedCampaign(MarketingCampaignRequest request) {
        try {
            log.info("创建自动化营销活动: name={}, type={}", request.getName(), request.getType());
            
            // 创建营销活动
            MarketingCampaign campaign = new MarketingCampaign();
            campaign.setName(request.getName());
            campaign.setType(request.getType());
            campaign.setDescription(request.getDescription());
            campaign.setStartTime(request.getStartTime());
            campaign.setEndTime(request.getEndTime());
            campaign.setStatus(MarketingCampaign.CampaignStatus.CREATED);
            campaign.setTargetSegment(request.getTargetSegment());
            campaign.setTriggerConditions(request.getTriggerConditions());
            campaign.setActionConfig(request.getActionConfig());
            campaign.setCreatedTime(LocalDateTime.now());
            
            campaign = campaignRepository.save(campaign);
            
            // 如果是立即执行的活动，启动执行
            if (request.getStartTime().isBefore(LocalDateTime.now().plusMinutes(5))) {
                startCampaignExecution(campaign.getId());
            }
            
            log.info("自动化营销活动创建成功: campaignId={}", campaign.getId());
            return campaign;
            
        } catch (Exception e) {
            log.error("创建自动化营销活动失败: request={}", request, e);
            throw new RuntimeException("创建自动化营销活动失败", e);
        }
    }
    
    /**
     * 启动营销活动执行
     */
    @Transactional
    public void startCampaignExecution(Long campaignId) {
        String lockKey = CAMPAIGN_LOCK_KEY + campaignId;
        
        try {
            // 获取分布式锁
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.MINUTES);
            if (!lockAcquired) {
                log.warn("营销活动正在执行中，跳过: campaignId={}", campaignId);
                return;
            }
            
            log.info("启动营销活动执行: campaignId={}", campaignId);
            
            MarketingCampaign campaign = campaignRepository.findById(campaignId)
                .orElseThrow(() -> new RuntimeException("营销活动不存在: " + campaignId));
            
            // 检查活动状态
            if (campaign.getStatus() != MarketingCampaign.CampaignStatus.CREATED &&
                campaign.getStatus() != MarketingCampaign.CampaignStatus.SCHEDULED) {
                log.warn("营销活动状态不允许执行: campaignId={}, status={}", campaignId, campaign.getStatus());
                return;
            }
            
            // 创建执行记录
            CampaignExecution execution = new CampaignExecution();
            execution.setCampaignId(campaignId);
            execution.setStatus(CampaignExecution.ExecutionStatus.RUNNING);
            execution.setStartTime(LocalDateTime.now());
            execution = executionRepository.save(execution);
            
            // 更新活动状态
            campaign.setStatus(MarketingCampaign.CampaignStatus.RUNNING);
            campaign.setCurrentExecutionId(execution.getId());
            campaignRepository.save(campaign);
            
            // 异步执行营销活动
            CompletableFuture.runAsync(() -> executeCampaign(campaign, execution));
            
            log.info("营销活动执行启动成功: campaignId={}, executionId={}", campaignId, execution.getId());
            
        } catch (Exception e) {
            log.error("启动营销活动执行失败: campaignId={}", campaignId, e);
        } finally {
            redisTemplate.delete(lockKey);
        }
    }
    
    /**
     * 执行营销活动
     */
    private void executeCampaign(MarketingCampaign campaign, CampaignExecution execution) {
        try {
            log.info("执行营销活动: campaignId={}, executionId={}", campaign.getId(), execution.getId());
            
            // 根据活动类型执行不同的逻辑
            switch (campaign.getType()) {
                case COUPON_DISTRIBUTION -> executeCouponDistribution(campaign, execution);
                case PERSONALIZED_RECOMMENDATION -> executePersonalizedRecommendation(campaign, execution);
                case FLASH_SALE -> executeFlashSale(campaign, execution);
                case USER_RETENTION -> executeUserRetention(campaign, execution);
                case NEW_USER_WELCOME -> executeNewUserWelcome(campaign, execution);
                case ABANDONED_CART_RECOVERY -> executeAbandonedCartRecovery(campaign, execution);
                case BIRTHDAY_PROMOTION -> executeBirthdayPromotion(campaign, execution);
                case LOYALTY_REWARD -> executeLoyaltyReward(campaign, execution);
                default -> {
                    log.warn("不支持的营销活动类型: {}", campaign.getType());
                    execution.setStatus(CampaignExecution.ExecutionStatus.FAILED);
                    execution.setErrorMessage("不支持的活动类型");
                }
            }
            
            // 更新执行状态
            if (execution.getStatus() == CampaignExecution.ExecutionStatus.RUNNING) {
                execution.setStatus(CampaignExecution.ExecutionStatus.COMPLETED);
            }
            execution.setEndTime(LocalDateTime.now());
            executionRepository.save(execution);
            
            // 更新活动状态
            campaign.setStatus(MarketingCampaign.CampaignStatus.COMPLETED);
            campaignRepository.save(campaign);
            
            log.info("营销活动执行完成: campaignId={}, executionId={}", campaign.getId(), execution.getId());
            
        } catch (Exception e) {
            log.error("执行营销活动失败: campaignId={}, executionId={}", campaign.getId(), execution.getId(), e);
            
            execution.setStatus(CampaignExecution.ExecutionStatus.FAILED);
            execution.setErrorMessage(e.getMessage());
            execution.setEndTime(LocalDateTime.now());
            executionRepository.save(execution);
            
            campaign.setStatus(MarketingCampaign.CampaignStatus.FAILED);
            campaignRepository.save(campaign);
        }
    }
    
    /**
     * 执行优惠券分发活动
     */
    private void executeCouponDistribution(MarketingCampaign campaign, CampaignExecution execution) {
        try {
            log.info("执行优惠券分发活动: campaignId={}", campaign.getId());
            
            // 获取目标用户群体
            List<Long> targetUsers = getTargetUsers(campaign.getTargetSegment());
            
            // 解析优惠券配置
            CouponConfig couponConfig = parseCouponConfig(campaign.getActionConfig());
            
            int successCount = 0;
            int failureCount = 0;
            
            for (Long userId : targetUsers) {
                try {
                    // 检查用户是否已经领取过
                    if (hasUserReceivedCoupon(userId, campaign.getId())) {
                        continue;
                    }
                    
                    // 创建并分发优惠券
                    CouponDistributionResult result = couponService.distributeCoupon(
                        userId, couponConfig, campaign.getId());
                    
                    if (result.isSuccess()) {
                        successCount++;
                        
                        // 发送通知
                        sendCouponNotification(userId, result.getCouponId(), couponConfig);
                        
                        // 记录用户活动参与
                        recordUserCampaignParticipation(userId, campaign.getId());
                        
                    } else {
                        failureCount++;
                        log.warn("优惠券分发失败: userId={}, reason={}", userId, result.getErrorMessage());
                    }
                    
                } catch (Exception e) {
                    failureCount++;
                    log.error("处理用户优惠券分发失败: userId={}", userId, e);
                }
            }
            
            // 更新执行统计
            execution.setTargetCount(targetUsers.size());
            execution.setSuccessCount(successCount);
            execution.setFailureCount(failureCount);
            
            log.info("优惠券分发活动执行完成: campaignId={}, target={}, success={}, failure={}", 
                campaign.getId(), targetUsers.size(), successCount, failureCount);
            
        } catch (Exception e) {
            log.error("执行优惠券分发活动失败: campaignId={}", campaign.getId(), e);
            throw e;
        }
    }
    
    /**
     * 执行个性化推荐活动
     */
    private void executePersonalizedRecommendation(MarketingCampaign campaign, CampaignExecution execution) {
        try {
            log.info("执行个性化推荐活动: campaignId={}", campaign.getId());
            
            List<Long> targetUsers = getTargetUsers(campaign.getTargetSegment());
            RecommendationConfig config = parseRecommendationConfig(campaign.getActionConfig());
            
            int successCount = 0;
            
            for (Long userId : targetUsers) {
                try {
                    // 生成个性化推荐
                    PersonalizedRecommendationResult result = recommendationService.generatePersonalizedRecommendation(
                        userId, config);
                    
                    if (result.isSuccess() && !result.getRecommendations().isEmpty()) {
                        // 发送推荐通知
                        sendRecommendationNotification(userId, result.getRecommendations());
                        successCount++;
                        
                        // 记录推荐活动
                        recordUserCampaignParticipation(userId, campaign.getId());
                    }
                    
                } catch (Exception e) {
                    log.error("处理用户个性化推荐失败: userId={}", userId, e);
                }
            }
            
            execution.setTargetCount(targetUsers.size());
            execution.setSuccessCount(successCount);
            
            log.info("个性化推荐活动执行完成: campaignId={}, target={}, success={}", 
                campaign.getId(), targetUsers.size(), successCount);
            
        } catch (Exception e) {
            log.error("执行个性化推荐活动失败: campaignId={}", campaign.getId(), e);
            throw e;
        }
    }
    
    /**
     * 执行限时抢购活动
     */
    private void executeFlashSale(MarketingCampaign campaign, CampaignExecution execution) {
        try {
            log.info("执行限时抢购活动: campaignId={}", campaign.getId());
            
            FlashSaleConfig config = parseFlashSaleConfig(campaign.getActionConfig());
            
            // 创建限时抢购商品
            FlashSaleCreationResult result = createFlashSaleProducts(config);
            if (!result.isSuccess()) {
                throw new RuntimeException("创建限时抢购商品失败: " + result.getErrorMessage());
            }
            
            // 获取目标用户并发送通知
            List<Long> targetUsers = getTargetUsers(campaign.getTargetSegment());
            
            int notificationCount = 0;
            for (Long userId : targetUsers) {
                try {
                    sendFlashSaleNotification(userId, result.getFlashSaleId(), config);
                    notificationCount++;
                } catch (Exception e) {
                    log.error("发送限时抢购通知失败: userId={}", userId, e);
                }
            }
            
            execution.setTargetCount(targetUsers.size());
            execution.setSuccessCount(notificationCount);
            
            log.info("限时抢购活动执行完成: campaignId={}, flashSaleId={}, notifications={}", 
                campaign.getId(), result.getFlashSaleId(), notificationCount);
            
        } catch (Exception e) {
            log.error("执行限时抢购活动失败: campaignId={}", campaign.getId(), e);
            throw e;
        }
    }
    
    /**
     * 执行用户留存活动
     */
    private void executeUserRetention(MarketingCampaign campaign, CampaignExecution execution) {
        try {
            log.info("执行用户留存活动: campaignId={}", campaign.getId());
            
            // 识别流失风险用户
            List<Long> riskUsers = customerAnalysisService.identifyChurnRiskUsers();
            
            RetentionConfig config = parseRetentionConfig(campaign.getActionConfig());
            
            int successCount = 0;
            for (Long userId : riskUsers) {
                try {
                    // 生成个性化留存方案
                    RetentionPlan plan = generateRetentionPlan(userId, config);
                    
                    // 执行留存措施
                    boolean executed = executeRetentionPlan(userId, plan);
                    if (executed) {
                        successCount++;
                        recordUserCampaignParticipation(userId, campaign.getId());
                    }
                    
                } catch (Exception e) {
                    log.error("处理用户留存失败: userId={}", userId, e);
                }
            }
            
            execution.setTargetCount(riskUsers.size());
            execution.setSuccessCount(successCount);
            
            log.info("用户留存活动执行完成: campaignId={}, target={}, success={}", 
                campaign.getId(), riskUsers.size(), successCount);
            
        } catch (Exception e) {
            log.error("执行用户留存活动失败: campaignId={}", campaign.getId(), e);
            throw e;
        }
    }
    
    /**
     * 执行新用户欢迎活动
     */
    private void executeNewUserWelcome(MarketingCampaign campaign, CampaignExecution execution) {
        try {
            log.info("执行新用户欢迎活动: campaignId={}", campaign.getId());
            
            // 获取新注册用户
            List<Long> newUsers = customerAnalysisService.getNewUsers(
                LocalDateTime.now().minusDays(1), LocalDateTime.now());
            
            WelcomeConfig config = parseWelcomeConfig(campaign.getActionConfig());
            
            int successCount = 0;
            for (Long userId : newUsers) {
                try {
                    // 发送欢迎礼包
                    boolean sent = sendWelcomePackage(userId, config);
                    if (sent) {
                        successCount++;
                        recordUserCampaignParticipation(userId, campaign.getId());
                    }
                    
                } catch (Exception e) {
                    log.error("处理新用户欢迎失败: userId={}", userId, e);
                }
            }
            
            execution.setTargetCount(newUsers.size());
            execution.setSuccessCount(successCount);
            
            log.info("新用户欢迎活动执行完成: campaignId={}, target={}, success={}", 
                campaign.getId(), newUsers.size(), successCount);
            
        } catch (Exception e) {
            log.error("执行新用户欢迎活动失败: campaignId={}", campaign.getId(), e);
            throw e;
        }
    }
    
    /**
     * 执行购物车挽回活动
     */
    private void executeAbandonedCartRecovery(MarketingCampaign campaign, CampaignExecution execution) {
        try {
            log.info("执行购物车挽回活动: campaignId={}", campaign.getId());
            
            // 获取废弃购物车用户
            List<AbandonedCartInfo> abandonedCarts = customerAnalysisService.getAbandonedCarts(
                LocalDateTime.now().minusHours(24));
            
            CartRecoveryConfig config = parseCartRecoveryConfig(campaign.getActionConfig());
            
            int successCount = 0;
            for (AbandonedCartInfo cartInfo : abandonedCarts) {
                try {
                    // 发送挽回消息
                    boolean sent = sendCartRecoveryMessage(cartInfo, config);
                    if (sent) {
                        successCount++;
                        recordUserCampaignParticipation(cartInfo.getUserId(), campaign.getId());
                    }
                    
                } catch (Exception e) {
                    log.error("处理购物车挽回失败: userId={}", cartInfo.getUserId(), e);
                }
            }
            
            execution.setTargetCount(abandonedCarts.size());
            execution.setSuccessCount(successCount);
            
            log.info("购物车挽回活动执行完成: campaignId={}, target={}, success={}", 
                campaign.getId(), abandonedCarts.size(), successCount);
            
        } catch (Exception e) {
            log.error("执行购物车挽回活动失败: campaignId={}", campaign.getId(), e);
            throw e;
        }
    }
    
    /**
     * 执行生日促销活动
     */
    private void executeBirthdayPromotion(MarketingCampaign campaign, CampaignExecution execution) {
        try {
            log.info("执行生日促销活动: campaignId={}", campaign.getId());
            
            // 获取今日生日用户
            List<Long> birthdayUsers = customerAnalysisService.getTodayBirthdayUsers();
            
            BirthdayPromotionConfig config = parseBirthdayPromotionConfig(campaign.getActionConfig());
            
            int successCount = 0;
            for (Long userId : birthdayUsers) {
                try {
                    // 发送生日祝福和优惠
                    boolean sent = sendBirthdayPromotion(userId, config);
                    if (sent) {
                        successCount++;
                        recordUserCampaignParticipation(userId, campaign.getId());
                    }
                    
                } catch (Exception e) {
                    log.error("处理生日促销失败: userId={}", userId, e);
                }
            }
            
            execution.setTargetCount(birthdayUsers.size());
            execution.setSuccessCount(successCount);
            
            log.info("生日促销活动执行完成: campaignId={}, target={}, success={}", 
                campaign.getId(), birthdayUsers.size(), successCount);
            
        } catch (Exception e) {
            log.error("执行生日促销活动失败: campaignId={}", campaign.getId(), e);
            throw e;
        }
    }
    
    /**
     * 执行忠诚度奖励活动
     */
    private void executeLoyaltyReward(MarketingCampaign campaign, CampaignExecution execution) {
        try {
            log.info("执行忠诚度奖励活动: campaignId={}", campaign.getId());
            
            // 获取高忠诚度用户
            List<Long> loyalUsers = customerAnalysisService.getHighLoyaltyUsers();
            
            LoyaltyRewardConfig config = parseLoyaltyRewardConfig(campaign.getActionConfig());
            
            int successCount = 0;
            for (Long userId : loyalUsers) {
                try {
                    // 发送忠诚度奖励
                    boolean sent = sendLoyaltyReward(userId, config);
                    if (sent) {
                        successCount++;
                        recordUserCampaignParticipation(userId, campaign.getId());
                    }
                    
                } catch (Exception e) {
                    log.error("处理忠诚度奖励失败: userId={}", userId, e);
                }
            }
            
            execution.setTargetCount(loyalUsers.size());
            execution.setSuccessCount(successCount);
            
            log.info("忠诚度奖励活动执行完成: campaignId={}, target={}, success={}", 
                campaign.getId(), loyalUsers.size(), successCount);
            
        } catch (Exception e) {
            log.error("执行忠诚度奖励活动失败: campaignId={}", campaign.getId(), e);
            throw e;
        }
    }
    
    /**
     * 定时检查待执行的营销活动
     */
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void checkPendingCampaigns() {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<MarketingCampaign> pendingCampaigns = campaignRepository.findPendingCampaigns(now);
            
            for (MarketingCampaign campaign : pendingCampaigns) {
                try {
                    startCampaignExecution(campaign.getId());
                } catch (Exception e) {
                    log.error("启动待执行营销活动失败: campaignId={}", campaign.getId(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("检查待执行营销活动失败", e);
        }
    }
    
    /**
     * 定时检查活动效果并优化
     */
    @Scheduled(fixedDelay = 3600000) // 每小时检查一次
    public void optimizeCampaigns() {
        try {
            List<MarketingCampaign> runningCampaigns = campaignRepository.findRunningCampaigns();
            
            for (MarketingCampaign campaign : runningCampaigns) {
                try {
                    analyzeCampaignPerformance(campaign);
                    optimizeCampaignIfNeeded(campaign);
                } catch (Exception e) {
                    log.error("优化营销活动失败: campaignId={}", campaign.getId(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("营销活动优化检查失败", e);
        }
    }
    
    // 辅助方法实现...
    private List<Long> getTargetUsers(String targetSegment) {
        // 根据目标群体获取用户列表
        return customerAnalysisService.getUsersBySegment(targetSegment);
    }
    
    private boolean hasUserReceivedCoupon(Long userId, Long campaignId) {
        String key = USER_CAMPAIGN_KEY + userId + ":" + campaignId;
        return redisTemplate.hasKey(key);
    }
    
    private void recordUserCampaignParticipation(Long userId, Long campaignId) {
        String key = USER_CAMPAIGN_KEY + userId + ":" + campaignId;
        redisTemplate.opsForValue().set(key, "participated", 30, TimeUnit.DAYS);
    }
    
    private void sendCouponNotification(Long userId, String couponId, CouponConfig config) {
        // 发送优惠券通知
        rabbitTemplate.convertAndSend(NOTIFICATION_QUEUE, Map.of(
            "userId", userId,
            "type", "COUPON_RECEIVED",
            "couponId", couponId,
            "message", String.format("您获得了一张%s优惠券", config.getName())
        ));
    }
    
    private void sendRecommendationNotification(Long userId, List<ProductRecommendation> recommendations) {
        // 发送推荐通知
        rabbitTemplate.convertAndSend(NOTIFICATION_QUEUE, Map.of(
            "userId", userId,
            "type", "PERSONALIZED_RECOMMENDATION",
            "recommendations", recommendations
        ));
    }
    
    private void sendFlashSaleNotification(Long userId, String flashSaleId, FlashSaleConfig config) {
        // 发送限时抢购通知
        rabbitTemplate.convertAndSend(NOTIFICATION_QUEUE, Map.of(
            "userId", userId,
            "type", "FLASH_SALE",
            "flashSaleId", flashSaleId,
            "message", String.format("限时抢购开始啦！%s", config.getTitle())
        ));
    }
    
    private boolean sendWelcomePackage(Long userId, WelcomeConfig config) {
        // 发送新用户欢迎礼包
        return true; // 简化实现
    }
    
    private boolean sendCartRecoveryMessage(AbandonedCartInfo cartInfo, CartRecoveryConfig config) {
        // 发送购物车挽回消息
        return true; // 简化实现
    }
    
    private boolean sendBirthdayPromotion(Long userId, BirthdayPromotionConfig config) {
        // 发送生日促销
        return true; // 简化实现
    }
    
    private boolean sendLoyaltyReward(Long userId, LoyaltyRewardConfig config) {
        // 发送忠诚度奖励
        return true; // 简化实现
    }
    
    private void analyzeCampaignPerformance(MarketingCampaign campaign) {
        // 分析活动效果
    }
    
    private void optimizeCampaignIfNeeded(MarketingCampaign campaign) {
        // 根据效果优化活动
    }
    
    // 配置解析方法的简化实现
    private CouponConfig parseCouponConfig(String actionConfig) { return new CouponConfig(); }
    private RecommendationConfig parseRecommendationConfig(String actionConfig) { return new RecommendationConfig(); }
    private FlashSaleConfig parseFlashSaleConfig(String actionConfig) { return new FlashSaleConfig(); }
    private RetentionConfig parseRetentionConfig(String actionConfig) { return new RetentionConfig(); }
    private WelcomeConfig parseWelcomeConfig(String actionConfig) { return new WelcomeConfig(); }
    private CartRecoveryConfig parseCartRecoveryConfig(String actionConfig) { return new CartRecoveryConfig(); }
    private BirthdayPromotionConfig parseBirthdayPromotionConfig(String actionConfig) { return new BirthdayPromotionConfig(); }
    private LoyaltyRewardConfig parseLoyaltyRewardConfig(String actionConfig) { return new LoyaltyRewardConfig(); }
    
    // 其他辅助方法的简化实现
    private FlashSaleCreationResult createFlashSaleProducts(FlashSaleConfig config) { return new FlashSaleCreationResult(true, "flash_sale_1"); }
    private RetentionPlan generateRetentionPlan(Long userId, RetentionConfig config) { return new RetentionPlan(); }
    private boolean executeRetentionPlan(Long userId, RetentionPlan plan) { return true; }
}
