package com.teleshop.automation.service;

import com.teleshop.automation.entity.SettlementTask;
import com.teleshop.automation.entity.SettlementRecord;
import com.teleshop.automation.entity.CommissionRule;
import com.teleshop.automation.repository.SettlementTaskRepository;
import com.teleshop.automation.repository.SettlementRecordRepository;
import com.teleshop.automation.repository.CommissionRuleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 财务结算自动化服务
 * 提供佣金计算、结算单生成、自动提现等财务自动化功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FinancialSettlementService {
    
    private final SettlementTaskRepository settlementTaskRepository;
    private final SettlementRecordRepository settlementRecordRepository;
    private final CommissionRuleRepository commissionRuleRepository;
    private final RabbitTemplate rabbitTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final OrderService orderService;
    private final MerchantService merchantService;
    private final PaymentService paymentService;
    private final NotificationService notificationService;
    
    // Redis键前缀
    private static final String SETTLEMENT_LOCK_KEY = "settlement_lock:";
    private static final String SETTLEMENT_CACHE_KEY = "settlement_cache:";
    private static final String COMMISSION_CACHE_KEY = "commission_cache:";
    
    // 消息队列
    private static final String SETTLEMENT_QUEUE = "teleshop.settlement.process";
    private static final String WITHDRAWAL_QUEUE = "teleshop.withdrawal.process";
    private static final String NOTIFICATION_QUEUE = "teleshop.notification.send";
    
    /**
     * 启动自动结算任务
     */
    @Transactional
    public void startAutoSettlement(SettlementRequest request) {
        String lockKey = SETTLEMENT_LOCK_KEY + request.getSettlementType() + ":" + request.getSettlementDate();
        
        try {
            // 获取分布式锁
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 2, TimeUnit.HOURS);
            if (!lockAcquired) {
                log.warn("结算任务正在进行中，跳过: type={}, date={}", 
                    request.getSettlementType(), request.getSettlementDate());
                return;
            }
            
            log.info("启动自动结算任务: type={}, date={}", 
                request.getSettlementType(), request.getSettlementDate());
            
            // 创建结算任务
            SettlementTask task = new SettlementTask();
            task.setSettlementType(request.getSettlementType());
            task.setSettlementDate(request.getSettlementDate());
            task.setStatus(SettlementTask.TaskStatus.RUNNING);
            task.setStartTime(LocalDateTime.now());
            task = settlementTaskRepository.save(task);
            
            // 根据结算类型执行不同的结算逻辑
            switch (request.getSettlementType()) {
                case DAILY_COMMISSION -> processDailyCommissionSettlement(task);
                case WEEKLY_COMMISSION -> processWeeklyCommissionSettlement(task);
                case MONTHLY_COMMISSION -> processMonthlyCommissionSettlement(task);
                case MERCHANT_SETTLEMENT -> processMerchantSettlement(task);
                case PLATFORM_SETTLEMENT -> processPlatformSettlement(task);
                case REFUND_SETTLEMENT -> processRefundSettlement(task);
                default -> {
                    log.warn("不支持的结算类型: {}", request.getSettlementType());
                    task.setStatus(SettlementTask.TaskStatus.FAILED);
                    task.setErrorMessage("不支持的结算类型");
                }
            }
            
            // 更新任务状态
            if (task.getStatus() == SettlementTask.TaskStatus.RUNNING) {
                task.setStatus(SettlementTask.TaskStatus.COMPLETED);
            }
            task.setEndTime(LocalDateTime.now());
            settlementTaskRepository.save(task);
            
            log.info("自动结算任务完成: taskId={}, type={}, status={}", 
                task.getId(), request.getSettlementType(), task.getStatus());
            
        } catch (Exception e) {
            log.error("启动自动结算任务失败: type={}, date={}", 
                request.getSettlementType(), request.getSettlementDate(), e);
        } finally {
            redisTemplate.delete(lockKey);
        }
    }
    
    /**
     * 处理日佣金结算
     */
    private void processDailyCommissionSettlement(SettlementTask task) {
        try {
            log.info("处理日佣金结算: taskId={}, date={}", task.getId(), task.getSettlementDate());
            
            LocalDate settlementDate = task.getSettlementDate();
            
            // 获取当日已完成的订单
            List<OrderInfo> completedOrders = orderService.getCompletedOrdersByDate(settlementDate);
            
            int processedCount = 0;
            BigDecimal totalCommission = BigDecimal.ZERO;
            
            for (OrderInfo order : completedOrders) {
                try {
                    // 计算佣金
                    CommissionCalculationResult commissionResult = calculateCommission(order);
                    if (!commissionResult.isSuccess()) {
                        log.warn("佣金计算失败: orderId={}, reason={}", 
                            order.getOrderId(), commissionResult.getErrorMessage());
                        continue;
                    }
                    
                    // 创建结算记录
                    SettlementRecord record = createSettlementRecord(
                        task.getId(), order, commissionResult);
                    settlementRecordRepository.save(record);
                    
                    // 更新商家账户余额
                    updateMerchantBalance(order.getMerchantId(), commissionResult.getMerchantCommission());
                    
                    // 更新平台收入
                    updatePlatformRevenue(commissionResult.getPlatformCommission());
                    
                    processedCount++;
                    totalCommission = totalCommission.add(commissionResult.getTotalCommission());
                    
                } catch (Exception e) {
                    log.error("处理订单佣金结算失败: orderId={}", order.getOrderId(), e);
                }
            }
            
            // 更新任务统计
            task.setProcessedCount(processedCount);
            task.setTotalAmount(totalCommission);
            
            log.info("日佣金结算完成: taskId={}, processed={}, totalCommission={}", 
                task.getId(), processedCount, totalCommission);
            
        } catch (Exception e) {
            log.error("处理日佣金结算失败: taskId={}", task.getId(), e);
            task.setStatus(SettlementTask.TaskStatus.FAILED);
            task.setErrorMessage(e.getMessage());
        }
    }
    
    /**
     * 处理商家结算
     */
    private void processMerchantSettlement(SettlementTask task) {
        try {
            log.info("处理商家结算: taskId={}, date={}", task.getId(), task.getSettlementDate());
            
            // 获取所有活跃商家
            List<MerchantInfo> activeMerchants = merchantService.getActiveMerchants();
            
            int processedCount = 0;
            BigDecimal totalSettlement = BigDecimal.ZERO;
            
            for (MerchantInfo merchant : activeMerchants) {
                try {
                    // 计算商家结算金额
                    MerchantSettlementResult settlementResult = calculateMerchantSettlement(
                        merchant.getMerchantId(), task.getSettlementDate());
                    
                    if (settlementResult.getSettlementAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        continue; // 跳过无结算金额的商家
                    }
                    
                    // 创建商家结算记录
                    SettlementRecord record = createMerchantSettlementRecord(
                        task.getId(), merchant, settlementResult);
                    settlementRecordRepository.save(record);
                    
                    // 检查是否满足自动提现条件
                    if (isAutoWithdrawalEnabled(merchant.getMerchantId()) && 
                        settlementResult.getSettlementAmount().compareTo(getAutoWithdrawalThreshold(merchant.getMerchantId())) >= 0) {
                        
                        // 触发自动提现
                        triggerAutoWithdrawal(merchant.getMerchantId(), settlementResult.getSettlementAmount());
                    }
                    
                    // 发送结算通知
                    sendSettlementNotification(merchant, settlementResult);
                    
                    processedCount++;
                    totalSettlement = totalSettlement.add(settlementResult.getSettlementAmount());
                    
                } catch (Exception e) {
                    log.error("处理商家结算失败: merchantId={}", merchant.getMerchantId(), e);
                }
            }
            
            task.setProcessedCount(processedCount);
            task.setTotalAmount(totalSettlement);
            
            log.info("商家结算完成: taskId={}, processed={}, totalSettlement={}", 
                task.getId(), processedCount, totalSettlement);
            
        } catch (Exception e) {
            log.error("处理商家结算失败: taskId={}", task.getId(), e);
            task.setStatus(SettlementTask.TaskStatus.FAILED);
            task.setErrorMessage(e.getMessage());
        }
    }
    
    /**
     * 处理平台结算
     */
    private void processPlatformSettlement(SettlementTask task) {
        try {
            log.info("处理平台结算: taskId={}, date={}", task.getId(), task.getSettlementDate());
            
            LocalDate settlementDate = task.getSettlementDate();
            
            // 计算平台收入
            PlatformRevenueResult revenueResult = calculatePlatformRevenue(settlementDate);
            
            // 计算平台支出
            PlatformExpenseResult expenseResult = calculatePlatformExpense(settlementDate);
            
            // 计算净利润
            BigDecimal netProfit = revenueResult.getTotalRevenue().subtract(expenseResult.getTotalExpense());
            
            // 创建平台结算记录
            SettlementRecord record = createPlatformSettlementRecord(
                task.getId(), settlementDate, revenueResult, expenseResult, netProfit);
            settlementRecordRepository.save(record);
            
            // 更新平台财务报表
            updatePlatformFinancialReport(settlementDate, revenueResult, expenseResult, netProfit);
            
            task.setProcessedCount(1);
            task.setTotalAmount(netProfit);
            
            log.info("平台结算完成: taskId={}, revenue={}, expense={}, netProfit={}", 
                task.getId(), revenueResult.getTotalRevenue(), expenseResult.getTotalExpense(), netProfit);
            
        } catch (Exception e) {
            log.error("处理平台结算失败: taskId={}", task.getId(), e);
            task.setStatus(SettlementTask.TaskStatus.FAILED);
            task.setErrorMessage(e.getMessage());
        }
    }
    
    /**
     * 处理退款结算
     */
    private void processRefundSettlement(SettlementTask task) {
        try {
            log.info("处理退款结算: taskId={}, date={}", task.getId(), task.getSettlementDate());
            
            LocalDate settlementDate = task.getSettlementDate();
            
            // 获取当日的退款订单
            List<RefundOrderInfo> refundOrders = orderService.getRefundOrdersByDate(settlementDate);
            
            int processedCount = 0;
            BigDecimal totalRefund = BigDecimal.ZERO;
            
            for (RefundOrderInfo refundOrder : refundOrders) {
                try {
                    // 计算退款结算
                    RefundSettlementResult refundResult = calculateRefundSettlement(refundOrder);
                    
                    // 创建退款结算记录
                    SettlementRecord record = createRefundSettlementRecord(
                        task.getId(), refundOrder, refundResult);
                    settlementRecordRepository.save(record);
                    
                    // 调整商家账户余额
                    adjustMerchantBalanceForRefund(refundOrder.getMerchantId(), refundResult.getMerchantAdjustment());
                    
                    // 调整平台收入
                    adjustPlatformRevenueForRefund(refundResult.getPlatformAdjustment());
                    
                    processedCount++;
                    totalRefund = totalRefund.add(refundResult.getRefundAmount());
                    
                } catch (Exception e) {
                    log.error("处理退款结算失败: refundOrderId={}", refundOrder.getRefundOrderId(), e);
                }
            }
            
            task.setProcessedCount(processedCount);
            task.setTotalAmount(totalRefund);
            
            log.info("退款结算完成: taskId={}, processed={}, totalRefund={}", 
                task.getId(), processedCount, totalRefund);
            
        } catch (Exception e) {
            log.error("处理退款结算失败: taskId={}", task.getId(), e);
            task.setStatus(SettlementTask.TaskStatus.FAILED);
            task.setErrorMessage(e.getMessage());
        }
    }
    
    /**
     * 计算佣金
     */
    private CommissionCalculationResult calculateCommission(OrderInfo order) {
        try {
            // 获取佣金规则
            CommissionRule rule = getCommissionRule(order.getMerchantId(), order.getCategoryId());
            if (rule == null) {
                return CommissionCalculationResult.failed("未找到适用的佣金规则");
            }
            
            BigDecimal orderAmount = order.getTotalAmount();
            BigDecimal platformCommissionRate = rule.getPlatformCommissionRate();
            BigDecimal merchantCommissionRate = BigDecimal.ONE.subtract(platformCommissionRate);
            
            // 计算平台佣金
            BigDecimal platformCommission = orderAmount.multiply(platformCommissionRate)
                .setScale(2, RoundingMode.HALF_UP);
            
            // 计算商家佣金
            BigDecimal merchantCommission = orderAmount.multiply(merchantCommissionRate)
                .setScale(2, RoundingMode.HALF_UP);
            
            return CommissionCalculationResult.success(
                orderAmount, platformCommission, merchantCommission);
            
        } catch (Exception e) {
            log.error("计算佣金失败: orderId={}", order.getOrderId(), e);
            return CommissionCalculationResult.failed("佣金计算异常: " + e.getMessage());
        }
    }
    
    /**
     * 计算商家结算金额
     */
    private MerchantSettlementResult calculateMerchantSettlement(Long merchantId, LocalDate settlementDate) {
        try {
            // 获取商家当前余额
            BigDecimal currentBalance = merchantService.getMerchantBalance(merchantId);
            
            // 获取最小结算金额
            BigDecimal minSettlementAmount = getMinSettlementAmount(merchantId);
            
            // 计算可结算金额
            BigDecimal settlementAmount = currentBalance.compareTo(minSettlementAmount) >= 0 ? 
                currentBalance : BigDecimal.ZERO;
            
            // 计算手续费
            BigDecimal serviceFee = calculateServiceFee(merchantId, settlementAmount);
            
            // 实际到账金额
            BigDecimal actualAmount = settlementAmount.subtract(serviceFee);
            
            return MerchantSettlementResult.builder()
                .merchantId(merchantId)
                .settlementAmount(settlementAmount)
                .serviceFee(serviceFee)
                .actualAmount(actualAmount)
                .settlementDate(settlementDate)
                .build();
            
        } catch (Exception e) {
            log.error("计算商家结算金额失败: merchantId={}", merchantId, e);
            return MerchantSettlementResult.failed("结算计算异常: " + e.getMessage());
        }
    }
    
    /**
     * 触发自动提现
     */
    private void triggerAutoWithdrawal(Long merchantId, BigDecimal amount) {
        try {
            log.info("触发自动提现: merchantId={}, amount={}", merchantId, amount);
            
            // 获取商家提现配置
            WithdrawalConfig config = merchantService.getWithdrawalConfig(merchantId);
            if (config == null || !config.isAutoWithdrawalEnabled()) {
                return;
            }
            
            // 创建提现申请
            WithdrawalRequest withdrawalRequest = WithdrawalRequest.builder()
                .merchantId(merchantId)
                .amount(amount)
                .withdrawalType(WithdrawalType.AUTO)
                .bankAccount(config.getDefaultBankAccount())
                .requestTime(LocalDateTime.now())
                .build();
            
            // 发送到提现处理队列
            rabbitTemplate.convertAndSend(WITHDRAWAL_QUEUE, withdrawalRequest);
            
            log.info("自动提现申请已提交: merchantId={}, amount={}", merchantId, amount);
            
        } catch (Exception e) {
            log.error("触发自动提现失败: merchantId={}, amount={}", merchantId, amount, e);
        }
    }
    
    /**
     * 定时执行日结算
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void scheduleDailySettlement() {
        try {
            LocalDate yesterday = LocalDate.now().minusDays(1);
            
            SettlementRequest request = SettlementRequest.builder()
                .settlementType(SettlementType.DAILY_COMMISSION)
                .settlementDate(yesterday)
                .build();
            
            startAutoSettlement(request);
            
        } catch (Exception e) {
            log.error("定时日结算失败", e);
        }
    }
    
    /**
     * 定时执行周结算
     */
    @Scheduled(cron = "0 0 3 ? * MON") // 每周一凌晨3点执行
    public void scheduleWeeklySettlement() {
        try {
            LocalDate lastWeek = LocalDate.now().minusWeeks(1);
            
            SettlementRequest request = SettlementRequest.builder()
                .settlementType(SettlementType.WEEKLY_COMMISSION)
                .settlementDate(lastWeek)
                .build();
            
            startAutoSettlement(request);
            
        } catch (Exception e) {
            log.error("定时周结算失败", e);
        }
    }
    
    /**
     * 定时执行月结算
     */
    @Scheduled(cron = "0 0 4 1 * ?") // 每月1号凌晨4点执行
    public void scheduleMonthlySettlement() {
        try {
            LocalDate lastMonth = LocalDate.now().minusMonths(1);
            
            SettlementRequest request = SettlementRequest.builder()
                .settlementType(SettlementType.MONTHLY_COMMISSION)
                .settlementDate(lastMonth)
                .build();
            
            startAutoSettlement(request);
            
        } catch (Exception e) {
            log.error("定时月结算失败", e);
        }
    }
    
    // 辅助方法实现...
    private CommissionRule getCommissionRule(Long merchantId, Long categoryId) {
        return commissionRuleRepository.findByMerchantIdAndCategoryId(merchantId, categoryId)
            .orElse(commissionRuleRepository.findDefaultRule());
    }
    
    private SettlementRecord createSettlementRecord(Long taskId, OrderInfo order, 
                                                   CommissionCalculationResult commissionResult) {
        SettlementRecord record = new SettlementRecord();
        record.setTaskId(taskId);
        record.setOrderId(order.getOrderId());
        record.setMerchantId(order.getMerchantId());
        record.setSettlementType(SettlementType.DAILY_COMMISSION);
        record.setOrderAmount(order.getTotalAmount());
        record.setPlatformCommission(commissionResult.getPlatformCommission());
        record.setMerchantCommission(commissionResult.getMerchantCommission());
        record.setSettlementTime(LocalDateTime.now());
        return record;
    }
    
    private void updateMerchantBalance(Long merchantId, BigDecimal amount) {
        merchantService.updateBalance(merchantId, amount);
    }
    
    private void updatePlatformRevenue(BigDecimal amount) {
        // 更新平台收入
    }
    
    private boolean isAutoWithdrawalEnabled(Long merchantId) {
        WithdrawalConfig config = merchantService.getWithdrawalConfig(merchantId);
        return config != null && config.isAutoWithdrawalEnabled();
    }
    
    private BigDecimal getAutoWithdrawalThreshold(Long merchantId) {
        WithdrawalConfig config = merchantService.getWithdrawalConfig(merchantId);
        return config != null ? config.getAutoWithdrawalThreshold() : BigDecimal.valueOf(1000);
    }
    
    private BigDecimal getMinSettlementAmount(Long merchantId) {
        return BigDecimal.valueOf(100); // 默认最小结算金额100元
    }
    
    private BigDecimal calculateServiceFee(Long merchantId, BigDecimal amount) {
        // 计算服务费，默认0.5%
        return amount.multiply(BigDecimal.valueOf(0.005)).setScale(2, RoundingMode.HALF_UP);
    }
    
    private void sendSettlementNotification(MerchantInfo merchant, MerchantSettlementResult result) {
        rabbitTemplate.convertAndSend(NOTIFICATION_QUEUE, Map.of(
            "merchantId", merchant.getMerchantId(),
            "type", "SETTLEMENT_COMPLETED",
            "amount", result.getActualAmount(),
            "message", String.format("结算完成，到账金额：%.2f元", result.getActualAmount())
        ));
    }
    
    // 其他辅助方法的简化实现
    private void processWeeklyCommissionSettlement(SettlementTask task) { /* 实现周结算逻辑 */ }
    private void processMonthlyCommissionSettlement(SettlementTask task) { /* 实现月结算逻辑 */ }
    private PlatformRevenueResult calculatePlatformRevenue(LocalDate date) { return new PlatformRevenueResult(); }
    private PlatformExpenseResult calculatePlatformExpense(LocalDate date) { return new PlatformExpenseResult(); }
    private SettlementRecord createMerchantSettlementRecord(Long taskId, MerchantInfo merchant, MerchantSettlementResult result) { return new SettlementRecord(); }
    private SettlementRecord createPlatformSettlementRecord(Long taskId, LocalDate date, PlatformRevenueResult revenue, PlatformExpenseResult expense, BigDecimal netProfit) { return new SettlementRecord(); }
    private SettlementRecord createRefundSettlementRecord(Long taskId, RefundOrderInfo refundOrder, RefundSettlementResult result) { return new SettlementRecord(); }
    private RefundSettlementResult calculateRefundSettlement(RefundOrderInfo refundOrder) { return new RefundSettlementResult(); }
    private void updatePlatformFinancialReport(LocalDate date, PlatformRevenueResult revenue, PlatformExpenseResult expense, BigDecimal netProfit) { /* 更新财务报表 */ }
    private void adjustMerchantBalanceForRefund(Long merchantId, BigDecimal adjustment) { /* 调整商家余额 */ }
    private void adjustPlatformRevenueForRefund(BigDecimal adjustment) { /* 调整平台收入 */ }
}
