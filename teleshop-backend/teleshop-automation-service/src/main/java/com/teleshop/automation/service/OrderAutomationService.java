package com.teleshop.automation.service;

import com.teleshop.automation.entity.AutomationTask;
import com.teleshop.automation.entity.WorkflowInstance;
import com.teleshop.automation.repository.AutomationTaskRepository;
import com.teleshop.automation.repository.WorkflowInstanceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.service.StateMachineService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 订单处理自动化服务
 * 提供订单全生命周期自动化处理功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderAutomationService {
    
    private final AutomationTaskRepository automationTaskRepository;
    private final WorkflowInstanceRepository workflowInstanceRepository;
    private final RuntimeService runtimeService;
    private final TaskService taskService;
    private final StateMachineService<OrderState, OrderEvent> stateMachineService;
    private final RabbitTemplate rabbitTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final InventoryService inventoryService;
    private final PaymentService paymentService;
    private final LogisticsService logisticsService;
    private final NotificationService notificationService;
    
    // Redis键前缀
    private static final String ORDER_LOCK_KEY = "order_lock:";
    private static final String ORDER_STATUS_KEY = "order_status:";
    private static final String AUTOMATION_CONFIG_KEY = "automation_config:";
    
    // 消息队列
    private static final String ORDER_EVENT_QUEUE = "teleshop.order.event";
    private static final String INVENTORY_QUEUE = "teleshop.inventory.update";
    private static final String LOGISTICS_QUEUE = "teleshop.logistics.create";
    
    /**
     * 启动订单自动化处理
     */
    @Transactional
    public void startOrderAutomation(Long orderId) {
        try {
            log.info("启动订单自动化处理: orderId={}", orderId);
            
            // 获取分布式锁
            String lockKey = ORDER_LOCK_KEY + orderId;
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.MINUTES);
            if (!lockAcquired) {
                log.warn("订单正在处理中，跳过自动化: orderId={}", orderId);
                return;
            }
            
            try {
                // 创建自动化任务
                AutomationTask task = createAutomationTask(orderId, AutomationTask.TaskType.ORDER_PROCESSING);
                task = automationTaskRepository.save(task);
                
                // 启动工作流
                String processInstanceId = startOrderWorkflow(orderId, task.getId());
                
                // 创建状态机实例
                StateMachine<OrderState, OrderEvent> stateMachine = stateMachineService.acquireStateMachine(
                    "order-" + orderId, true);
                stateMachine.getExtendedState().getVariables().put("orderId", orderId);
                stateMachine.getExtendedState().getVariables().put("taskId", task.getId());
                stateMachine.start();
                
                // 更新任务状态
                task.setStatus(AutomationTask.TaskStatus.RUNNING);
                task.setProcessInstanceId(processInstanceId);
                automationTaskRepository.save(task);
                
                // 触发订单确认事件
                triggerOrderEvent(orderId, OrderEvent.CONFIRM_ORDER);
                
                log.info("订单自动化处理启动成功: orderId={}, taskId={}, processInstanceId={}", 
                    orderId, task.getId(), processInstanceId);
                
            } finally {
                // 释放分布式锁
                redisTemplate.delete(lockKey);
            }
            
        } catch (Exception e) {
            log.error("启动订单自动化处理失败: orderId={}", orderId, e);
            throw new RuntimeException("启动订单自动化处理失败", e);
        }
    }
    
    /**
     * 处理订单确认
     */
    public void processOrderConfirmation(Long orderId) {
        try {
            log.info("处理订单确认: orderId={}", orderId);
            
            // 获取订单信息
            OrderInfo orderInfo = getOrderInfo(orderId);
            if (orderInfo == null) {
                throw new RuntimeException("订单不存在: " + orderId);
            }
            
            // 验证订单信息
            OrderValidationResult validationResult = validateOrder(orderInfo);
            if (!validationResult.isValid()) {
                handleOrderValidationFailure(orderId, validationResult);
                return;
            }
            
            // 检查库存
            InventoryCheckResult inventoryResult = inventoryService.checkInventory(orderInfo.getItems());
            if (!inventoryResult.isAvailable()) {
                handleInventoryShortage(orderId, inventoryResult);
                return;
            }
            
            // 锁定库存
            boolean inventoryLocked = inventoryService.lockInventory(orderId, orderInfo.getItems());
            if (!inventoryLocked) {
                handleInventoryLockFailure(orderId);
                return;
            }
            
            // 验证支付
            PaymentValidationResult paymentResult = paymentService.validatePayment(orderInfo.getPaymentInfo());
            if (!paymentResult.isValid()) {
                // 释放库存
                inventoryService.releaseInventory(orderId);
                handlePaymentValidationFailure(orderId, paymentResult);
                return;
            }
            
            // 更新订单状态
            updateOrderStatus(orderId, OrderState.CONFIRMED);
            
            // 触发库存扣减事件
            triggerOrderEvent(orderId, OrderEvent.DEDUCT_INVENTORY);
            
            log.info("订单确认处理完成: orderId={}", orderId);
            
        } catch (Exception e) {
            log.error("处理订单确认失败: orderId={}", orderId, e);
            handleOrderProcessingError(orderId, "订单确认失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理库存扣减
     */
    public void processInventoryDeduction(Long orderId) {
        try {
            log.info("处理库存扣减: orderId={}", orderId);
            
            OrderInfo orderInfo = getOrderInfo(orderId);
            
            // 执行库存扣减
            InventoryDeductionResult deductionResult = inventoryService.deductInventory(orderId, orderInfo.getItems());
            if (!deductionResult.isSuccess()) {
                handleInventoryDeductionFailure(orderId, deductionResult);
                return;
            }
            
            // 更新订单状态
            updateOrderStatus(orderId, OrderState.INVENTORY_DEDUCTED);
            
            // 发送库存更新消息
            rabbitTemplate.convertAndSend(INVENTORY_QUEUE, Map.of(
                "orderId", orderId,
                "items", orderInfo.getItems(),
                "operation", "DEDUCT"
            ));
            
            // 触发支付处理事件
            triggerOrderEvent(orderId, OrderEvent.PROCESS_PAYMENT);
            
            log.info("库存扣减处理完成: orderId={}", orderId);
            
        } catch (Exception e) {
            log.error("处理库存扣减失败: orderId={}", orderId, e);
            handleOrderProcessingError(orderId, "库存扣减失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理支付
     */
    public void processPayment(Long orderId) {
        try {
            log.info("处理支付: orderId={}", orderId);
            
            OrderInfo orderInfo = getOrderInfo(orderId);
            
            // 执行支付
            PaymentResult paymentResult = paymentService.processPayment(orderInfo.getPaymentInfo());
            if (!paymentResult.isSuccess()) {
                handlePaymentFailure(orderId, paymentResult);
                return;
            }
            
            // 更新订单状态
            updateOrderStatus(orderId, OrderState.PAID);
            
            // 记录支付信息
            recordPaymentInfo(orderId, paymentResult);
            
            // 触发物流创建事件
            triggerOrderEvent(orderId, OrderEvent.CREATE_LOGISTICS);
            
            log.info("支付处理完成: orderId={}, paymentId={}", orderId, paymentResult.getPaymentId());
            
        } catch (Exception e) {
            log.error("处理支付失败: orderId={}", orderId, e);
            handleOrderProcessingError(orderId, "支付处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建物流订单
     */
    public void createLogisticsOrder(Long orderId) {
        try {
            log.info("创建物流订单: orderId={}", orderId);
            
            OrderInfo orderInfo = getOrderInfo(orderId);
            
            // 选择最优仓库
            WarehouseSelectionResult warehouseResult = selectOptimalWarehouse(orderInfo);
            if (!warehouseResult.isSuccess()) {
                handleWarehouseSelectionFailure(orderId, warehouseResult);
                return;
            }
            
            // 创建物流订单
            LogisticsOrderResult logisticsResult = logisticsService.createLogisticsOrder(
                orderId, warehouseResult.getWarehouseId(), orderInfo.getDeliveryAddress());
            if (!logisticsResult.isSuccess()) {
                handleLogisticsCreationFailure(orderId, logisticsResult);
                return;
            }
            
            // 更新订单状态
            updateOrderStatus(orderId, OrderState.LOGISTICS_CREATED);
            
            // 发送物流创建消息
            rabbitTemplate.convertAndSend(LOGISTICS_QUEUE, Map.of(
                "orderId", orderId,
                "logisticsOrderId", logisticsResult.getLogisticsOrderId(),
                "warehouseId", warehouseResult.getWarehouseId()
            ));
            
            // 触发发货准备事件
            triggerOrderEvent(orderId, OrderEvent.PREPARE_SHIPMENT);
            
            log.info("物流订单创建完成: orderId={}, logisticsOrderId={}", 
                orderId, logisticsResult.getLogisticsOrderId());
            
        } catch (Exception e) {
            log.error("创建物流订单失败: orderId={}", orderId, e);
            handleOrderProcessingError(orderId, "物流订单创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 准备发货
     */
    public void prepareShipment(Long orderId) {
        try {
            log.info("准备发货: orderId={}", orderId);
            
            OrderInfo orderInfo = getOrderInfo(orderId);
            
            // 生成拣货单
            PickingListResult pickingResult = logisticsService.generatePickingList(orderId);
            if (!pickingResult.isSuccess()) {
                handlePickingListGenerationFailure(orderId, pickingResult);
                return;
            }
            
            // 自动分配拣货员（如果启用）
            if (isAutoPickingEnabled()) {
                PickerAssignmentResult assignmentResult = logisticsService.assignPicker(orderId);
                if (assignmentResult.isSuccess()) {
                    log.info("自动分配拣货员成功: orderId={}, pickerId={}", 
                        orderId, assignmentResult.getPickerId());
                }
            }
            
            // 更新订单状态
            updateOrderStatus(orderId, OrderState.SHIPMENT_PREPARING);
            
            // 发送通知给仓库
            notificationService.notifyWarehouse(orderId, "新订单待拣货", pickingResult.getPickingListId());
            
            // 设置自动发货检查（如果启用）
            if (isAutoShipmentEnabled()) {
                scheduleAutoShipmentCheck(orderId);
            }
            
            log.info("发货准备完成: orderId={}, pickingListId={}", orderId, pickingResult.getPickingListId());
            
        } catch (Exception e) {
            log.error("准备发货失败: orderId={}", orderId, e);
            handleOrderProcessingError(orderId, "发货准备失败: " + e.getMessage());
        }
    }
    
    /**
     * 自动发货检查
     */
    public void autoShipmentCheck(Long orderId) {
        try {
            log.info("自动发货检查: orderId={}", orderId);
            
            // 检查拣货状态
            PickingStatus pickingStatus = logisticsService.getPickingStatus(orderId);
            if (pickingStatus != PickingStatus.COMPLETED) {
                log.info("拣货未完成，跳过自动发货: orderId={}, status={}", orderId, pickingStatus);
                return;
            }
            
            // 检查质检状态（如果启用）
            if (isQualityCheckEnabled()) {
                QualityCheckStatus qcStatus = logisticsService.getQualityCheckStatus(orderId);
                if (qcStatus != QualityCheckStatus.PASSED) {
                    log.info("质检未通过，跳过自动发货: orderId={}, status={}", orderId, qcStatus);
                    return;
                }
            }
            
            // 执行自动发货
            triggerOrderEvent(orderId, OrderEvent.SHIP_ORDER);
            
        } catch (Exception e) {
            log.error("自动发货检查失败: orderId={}", orderId, e);
        }
    }
    
    /**
     * 发货处理
     */
    public void processShipment(Long orderId) {
        try {
            log.info("处理发货: orderId={}", orderId);
            
            OrderInfo orderInfo = getOrderInfo(orderId);
            
            // 生成发货单
            ShipmentResult shipmentResult = logisticsService.createShipment(orderId);
            if (!shipmentResult.isSuccess()) {
                handleShipmentCreationFailure(orderId, shipmentResult);
                return;
            }
            
            // 更新订单状态
            updateOrderStatus(orderId, OrderState.SHIPPED);
            
            // 发送发货通知
            notificationService.notifyCustomer(orderInfo.getCustomerId(), 
                "订单已发货", 
                String.format("您的订单 %s 已发货，快递单号：%s", orderId, shipmentResult.getTrackingNumber()));
            
            // 启动物流跟踪
            startLogisticsTracking(orderId, shipmentResult.getTrackingNumber());
            
            log.info("发货处理完成: orderId={}, trackingNumber={}", orderId, shipmentResult.getTrackingNumber());
            
        } catch (Exception e) {
            log.error("处理发货失败: orderId={}", orderId, e);
            handleOrderProcessingError(orderId, "发货处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动物流跟踪
     */
    private void startLogisticsTracking(Long orderId, String trackingNumber) {
        try {
            // 启动物流跟踪任务
            AutomationTask trackingTask = createAutomationTask(orderId, AutomationTask.TaskType.LOGISTICS_TRACKING);
            trackingTask.setRelatedId(trackingNumber);
            automationTaskRepository.save(trackingTask);
            
            // 定时查询物流状态
            scheduleLogisticsStatusCheck(orderId, trackingNumber);
            
        } catch (Exception e) {
            log.error("启动物流跟踪失败: orderId={}, trackingNumber={}", orderId, trackingNumber, e);
        }
    }
    
    /**
     * 处理订单完成
     */
    public void completeOrder(Long orderId) {
        try {
            log.info("完成订单: orderId={}", orderId);
            
            OrderInfo orderInfo = getOrderInfo(orderId);
            
            // 更新订单状态
            updateOrderStatus(orderId, OrderState.COMPLETED);
            
            // 发送完成通知
            notificationService.notifyCustomer(orderInfo.getCustomerId(), 
                "订单已完成", 
                String.format("您的订单 %s 已完成，感谢您的购买！", orderId));
            
            // 触发售后服务
            triggerAfterSalesService(orderId);
            
            // 更新客户积分（如果启用）
            if (isLoyaltyPointsEnabled()) {
                updateCustomerLoyaltyPoints(orderInfo.getCustomerId(), orderInfo.getTotalAmount());
            }
            
            // 完成自动化任务
            completeAutomationTask(orderId);
            
            log.info("订单完成处理完成: orderId={}", orderId);
            
        } catch (Exception e) {
            log.error("完成订单失败: orderId={}", orderId, e);
        }
    }
    
    /**
     * 触发订单事件
     */
    private void triggerOrderEvent(Long orderId, OrderEvent event) {
        try {
            StateMachine<OrderState, OrderEvent> stateMachine = stateMachineService.acquireStateMachine("order-" + orderId);
            stateMachine.sendEvent(event);
            
            // 发送事件消息
            rabbitTemplate.convertAndSend(ORDER_EVENT_QUEUE, Map.of(
                "orderId", orderId,
                "event", event.name(),
                "timestamp", LocalDateTime.now()
            ));
            
        } catch (Exception e) {
            log.error("触发订单事件失败: orderId={}, event={}", orderId, event, e);
        }
    }
    
    /**
     * 更新订单状态
     */
    private void updateOrderStatus(Long orderId, OrderState state) {
        try {
            String statusKey = ORDER_STATUS_KEY + orderId;
            redisTemplate.opsForValue().set(statusKey, state.name(), 24, TimeUnit.HOURS);
            
            // 调用订单服务更新状态
            // orderService.updateOrderStatus(orderId, state);
            
        } catch (Exception e) {
            log.error("更新订单状态失败: orderId={}, state={}", orderId, state, e);
        }
    }
    
    // 辅助方法实现...
    private AutomationTask createAutomationTask(Long orderId, AutomationTask.TaskType taskType) {
        AutomationTask task = new AutomationTask();
        task.setTaskType(taskType);
        task.setTargetId(orderId.toString());
        task.setStatus(AutomationTask.TaskStatus.CREATED);
        task.setCreatedTime(LocalDateTime.now());
        return task;
    }
    
    private String startOrderWorkflow(Long orderId, Long taskId) {
        Map<String, Object> variables = Map.of(
            "orderId", orderId,
            "taskId", taskId
        );
        return runtimeService.startProcessInstanceByKey("orderProcessing", variables).getId();
    }
    
    private OrderInfo getOrderInfo(Long orderId) {
        // 调用订单服务获取订单信息
        return new OrderInfo(); // 简化实现
    }
    
    private OrderValidationResult validateOrder(OrderInfo orderInfo) {
        // 实现订单验证逻辑
        return new OrderValidationResult(true, "");
    }
    
    private WarehouseSelectionResult selectOptimalWarehouse(OrderInfo orderInfo) {
        // 实现最优仓库选择逻辑
        return new WarehouseSelectionResult(true, 1L);
    }
    
    private void recordPaymentInfo(Long orderId, PaymentResult paymentResult) {
        // 记录支付信息
    }
    
    private void scheduleAutoShipmentCheck(Long orderId) {
        // 安排自动发货检查
    }
    
    private void scheduleLogisticsStatusCheck(Long orderId, String trackingNumber) {
        // 安排物流状态检查
    }
    
    private void triggerAfterSalesService(Long orderId) {
        // 触发售后服务
    }
    
    private void updateCustomerLoyaltyPoints(Long customerId, Double amount) {
        // 更新客户积分
    }
    
    private void completeAutomationTask(Long orderId) {
        // 完成自动化任务
    }
    
    private boolean isAutoPickingEnabled() {
        return getAutomationConfig("autoPickingEnabled", Boolean.class, false);
    }
    
    private boolean isAutoShipmentEnabled() {
        return getAutomationConfig("autoShipmentEnabled", Boolean.class, false);
    }
    
    private boolean isQualityCheckEnabled() {
        return getAutomationConfig("qualityCheckEnabled", Boolean.class, true);
    }
    
    private boolean isLoyaltyPointsEnabled() {
        return getAutomationConfig("loyaltyPointsEnabled", Boolean.class, true);
    }
    
    @SuppressWarnings("unchecked")
    private <T> T getAutomationConfig(String key, Class<T> type, T defaultValue) {
        try {
            String configKey = AUTOMATION_CONFIG_KEY + key;
            Object value = redisTemplate.opsForValue().get(configKey);
            return value != null ? (T) value : defaultValue;
        } catch (Exception e) {
            log.warn("获取自动化配置失败: key={}", key, e);
            return defaultValue;
        }
    }
    
    // 错误处理方法
    private void handleOrderValidationFailure(Long orderId, OrderValidationResult result) {
        log.warn("订单验证失败: orderId={}, reason={}", orderId, result.getErrorMessage());
        updateOrderStatus(orderId, OrderState.VALIDATION_FAILED);
    }
    
    private void handleInventoryShortage(Long orderId, InventoryCheckResult result) {
        log.warn("库存不足: orderId={}, shortage={}", orderId, result.getShortageItems());
        updateOrderStatus(orderId, OrderState.INVENTORY_SHORTAGE);
    }
    
    private void handleInventoryLockFailure(Long orderId) {
        log.warn("库存锁定失败: orderId={}", orderId);
        updateOrderStatus(orderId, OrderState.INVENTORY_LOCK_FAILED);
    }
    
    private void handlePaymentValidationFailure(Long orderId, PaymentValidationResult result) {
        log.warn("支付验证失败: orderId={}, reason={}", orderId, result.getErrorMessage());
        updateOrderStatus(orderId, OrderState.PAYMENT_VALIDATION_FAILED);
    }
    
    private void handleInventoryDeductionFailure(Long orderId, InventoryDeductionResult result) {
        log.warn("库存扣减失败: orderId={}, reason={}", orderId, result.getErrorMessage());
        updateOrderStatus(orderId, OrderState.INVENTORY_DEDUCTION_FAILED);
    }
    
    private void handlePaymentFailure(Long orderId, PaymentResult result) {
        log.warn("支付失败: orderId={}, reason={}", orderId, result.getErrorMessage());
        updateOrderStatus(orderId, OrderState.PAYMENT_FAILED);
        // 释放库存
        inventoryService.releaseInventory(orderId);
    }
    
    private void handleWarehouseSelectionFailure(Long orderId, WarehouseSelectionResult result) {
        log.warn("仓库选择失败: orderId={}, reason={}", orderId, result.getErrorMessage());
        updateOrderStatus(orderId, OrderState.WAREHOUSE_SELECTION_FAILED);
    }
    
    private void handleLogisticsCreationFailure(Long orderId, LogisticsOrderResult result) {
        log.warn("物流订单创建失败: orderId={}, reason={}", orderId, result.getErrorMessage());
        updateOrderStatus(orderId, OrderState.LOGISTICS_CREATION_FAILED);
    }
    
    private void handlePickingListGenerationFailure(Long orderId, PickingListResult result) {
        log.warn("拣货单生成失败: orderId={}, reason={}", orderId, result.getErrorMessage());
        updateOrderStatus(orderId, OrderState.PICKING_LIST_GENERATION_FAILED);
    }
    
    private void handleShipmentCreationFailure(Long orderId, ShipmentResult result) {
        log.warn("发货单创建失败: orderId={}, reason={}", orderId, result.getErrorMessage());
        updateOrderStatus(orderId, OrderState.SHIPMENT_CREATION_FAILED);
    }
    
    private void handleOrderProcessingError(Long orderId, String errorMessage) {
        log.error("订单处理错误: orderId={}, error={}", orderId, errorMessage);
        updateOrderStatus(orderId, OrderState.PROCESSING_ERROR);
        
        // 发送错误通知
        notificationService.notifyAdmin("订单处理错误", 
            String.format("订单 %s 处理失败: %s", orderId, errorMessage));
    }
}
