package com.teleshop.wallet.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包实体类
 * 对应数据库表：t_wallet
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_wallet", indexes = {
    @Index(name = "idx_user_id", columnList = "user_id"),
    @Index(name = "idx_wallet_type", columnList = "wallet_type"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_create_time", columnList = "create_time")
})
@EntityListeners(AuditingEntityListener.class)
@DynamicInsert
@DynamicUpdate
public class Wallet {

    /**
     * 钱包ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "wallet_id")
    private Long walletId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 钱包类型：1-余额钱包，2-积分钱包，3-U币钱包
     */
    @Column(name = "wallet_type", nullable = false)
    private Integer walletType;

    /**
     * 钱包名称
     */
    @Column(name = "wallet_name", length = 100)
    private String walletName;

    /**
     * 可用余额
     */
    @Column(name = "available_balance", precision = 20, scale = 8, columnDefinition = "DECIMAL(20,8) DEFAULT 0")
    private BigDecimal availableBalance;

    /**
     * 冻结余额
     */
    @Column(name = "frozen_balance", precision = 20, scale = 8, columnDefinition = "DECIMAL(20,8) DEFAULT 0")
    private BigDecimal frozenBalance;

    /**
     * 总余额
     */
    @Column(name = "total_balance", precision = 20, scale = 8, columnDefinition = "DECIMAL(20,8) DEFAULT 0")
    private BigDecimal totalBalance;

    /**
     * 钱包地址（加密货币钱包）
     */
    @Column(name = "wallet_address", length = 200)
    private String walletAddress;

    /**
     * 私钥（加密存储）
     */
    @Column(name = "private_key", length = 500)
    private String privateKey;

    /**
     * 公钥
     */
    @Column(name = "public_key", length = 500)
    private String publicKey;

    /**
     * 助记词（加密存储）
     */
    @Column(name = "mnemonic", length = 1000)
    private String mnemonic;

    /**
     * 钱包密码哈希
     */
    @Column(name = "password_hash", length = 255)
    private String passwordHash;

    /**
     * 支付密码哈希
     */
    @Column(name = "pay_password_hash", length = 255)
    private String payPasswordHash;

    /**
     * 是否设置支付密码：0-否，1-是
     */
    @Column(name = "has_pay_password", columnDefinition = "TINYINT DEFAULT 0")
    private Integer hasPayPassword;

    /**
     * 钱包状态：1-正常，2-冻结，3-禁用
     */
    @Column(name = "status", columnDefinition = "TINYINT DEFAULT 1")
    private Integer status;

    /**
     * 是否默认钱包：0-否，1-是
     */
    @Column(name = "is_default", columnDefinition = "TINYINT DEFAULT 0")
    private Integer isDefault;

    /**
     * 安全等级：1-低，2-中，3-高
     */
    @Column(name = "security_level", columnDefinition = "TINYINT DEFAULT 1")
    private Integer securityLevel;

    /**
     * 每日转账限额
     */
    @Column(name = "daily_transfer_limit", precision = 20, scale = 8)
    private BigDecimal dailyTransferLimit;

    /**
     * 单笔转账限额
     */
    @Column(name = "single_transfer_limit", precision = 20, scale = 8)
    private BigDecimal singleTransferLimit;

    /**
     * 今日已转账金额
     */
    @Column(name = "today_transfer_amount", precision = 20, scale = 8, columnDefinition = "DECIMAL(20,8) DEFAULT 0")
    private BigDecimal todayTransferAmount;

    /**
     * 最后交易时间
     */
    @Column(name = "last_transaction_time")
    private LocalDateTime lastTransactionTime;

    /**
     * 最后交易IP
     */
    @Column(name = "last_transaction_ip", length = 50)
    private String lastTransactionIp;

    /**
     * 钱包版本（用于乐观锁）
     */
    @Version
    @Column(name = "version", columnDefinition = "INT DEFAULT 0")
    private Integer version;

    /**
     * 扩展信息（JSON格式）
     */
    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    // 常量定义

    /**
     * 钱包类型常量
     */
    public static class WalletType {
        public static final int BALANCE = 1;    // 余额钱包
        public static final int POINTS = 2;     // 积分钱包
        public static final int UCOIN = 3;      // U币钱包
    }

    /**
     * 钱包状态常量
     */
    public static class Status {
        public static final int NORMAL = 1;     // 正常
        public static final int FROZEN = 2;     // 冻结
        public static final int DISABLED = 3;   // 禁用
    }

    /**
     * 安全等级常量
     */
    public static class SecurityLevel {
        public static final int LOW = 1;        // 低
        public static final int MEDIUM = 2;     // 中
        public static final int HIGH = 3;       // 高
    }

    /**
     * 布尔值常量
     */
    public static class BooleanValue {
        public static final int NO = 0;
        public static final int YES = 1;
    }

    /**
     * 计算总余额
     */
    public void calculateTotalBalance() {
        this.totalBalance = this.availableBalance.add(this.frozenBalance);
    }

    /**
     * 检查余额是否足够
     */
    public boolean hasEnoughBalance(BigDecimal amount) {
        return this.availableBalance.compareTo(amount) >= 0;
    }

    /**
     * 检查是否可以转账
     */
    public boolean canTransfer(BigDecimal amount) {
        if (this.status != Status.NORMAL) {
            return false;
        }
        if (!hasEnoughBalance(amount)) {
            return false;
        }
        if (this.singleTransferLimit != null && amount.compareTo(this.singleTransferLimit) > 0) {
            return false;
        }
        if (this.dailyTransferLimit != null) {
            BigDecimal todayTotal = this.todayTransferAmount.add(amount);
            if (todayTotal.compareTo(this.dailyTransferLimit) > 0) {
                return false;
            }
        }
        return true;
    }
}
