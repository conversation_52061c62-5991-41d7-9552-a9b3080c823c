package com.teleshop.message.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teleshop.message.entity.Message;
import com.teleshop.message.entity.OfflineMessage;
import com.teleshop.message.repository.OfflineMessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 离线消息服务
 * 处理用户离线时的消息存储和推送
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OfflineMessageService {
    
    private final OfflineMessageRepository offlineMessageRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;
    
    // Redis键前缀
    private static final String OFFLINE_MESSAGE_KEY = "offline_msg:";
    private static final String OFFLINE_COUNT_KEY = "offline_count:";
    private static final String MAX_OFFLINE_MESSAGES = "max_offline_messages";
    
    // 配置参数
    private static final int DEFAULT_MAX_OFFLINE_MESSAGES = 1000;
    private static final int OFFLINE_MESSAGE_EXPIRE_DAYS = 30;
    
    /**
     * 存储离线消息
     */
    @Transactional
    public void storeOfflineMessage(String userId, Message message) {
        try {
            // 检查离线消息数量限制
            if (isOfflineMessageLimitExceeded(userId)) {
                log.warn("用户 {} 离线消息数量超过限制，丢弃消息: {}", userId, message.getMessageId());
                return;
            }
            
            // 创建离线消息记录
            OfflineMessage offlineMessage = createOfflineMessage(userId, message);
            
            // 保存到数据库
            offlineMessageRepository.save(offlineMessage);
            
            // 缓存到Redis（用于快速访问）
            cacheOfflineMessage(userId, message);
            
            // 更新离线消息计数
            incrementOfflineMessageCount(userId);
            
            log.debug("离线消息存储成功: userId={}, messageId={}", userId, message.getMessageId());
            
        } catch (Exception e) {
            log.error("存储离线消息失败: userId={}, messageId={}", userId, message.getMessageId(), e);
            throw new RuntimeException("存储离线消息失败", e);
        }
    }
    
    /**
     * 获取用户的离线消息
     */
    public List<Message> getOfflineMessages(String userId) {
        try {
            // 先从Redis获取
            List<Message> cachedMessages = getCachedOfflineMessages(userId);
            if (!cachedMessages.isEmpty()) {
                return cachedMessages;
            }
            
            // 从数据库获取
            List<OfflineMessage> offlineMessages = offlineMessageRepository
                .findByUserIdAndPushedFalseOrderByCreatedTimeAsc(Long.valueOf(userId));
            
            List<Message> messages = offlineMessages.stream()
                .map(this::convertToMessage)
                .filter(message -> message != null && !message.isExpired())
                .toList();
            
            // 缓存到Redis
            if (!messages.isEmpty()) {
                cacheOfflineMessages(userId, messages);
            }
            
            log.debug("获取离线消息成功: userId={}, count={}", userId, messages.size());
            return messages;
            
        } catch (Exception e) {
            log.error("获取离线消息失败: userId={}", userId, e);
            return List.of();
        }
    }
    
    /**
     * 标记离线消息为已推送
     */
    @Transactional
    public void markMessagesAsPushed(String userId) {
        try {
            // 更新数据库记录
            int updatedCount = offlineMessageRepository.markAsPushed(Long.valueOf(userId));
            
            // 清除Redis缓存
            clearCachedOfflineMessages(userId);
            
            // 重置离线消息计数
            resetOfflineMessageCount(userId);
            
            log.debug("标记离线消息为已推送: userId={}, count={}", userId, updatedCount);
            
        } catch (Exception e) {
            log.error("标记离线消息失败: userId={}", userId, e);
        }
    }
    
    /**
     * 清理过期的离线消息
     */
    @Transactional
    public void cleanupExpiredMessages() {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(OFFLINE_MESSAGE_EXPIRE_DAYS);
            int deletedCount = offlineMessageRepository.deleteByCreatedTimeBefore(expireTime);
            
            log.info("清理过期离线消息完成，删除数量: {}", deletedCount);
            
        } catch (Exception e) {
            log.error("清理过期离线消息失败", e);
        }
    }
    
    /**
     * 获取用户离线消息数量
     */
    public long getOfflineMessageCount(String userId) {
        try {
            // 先从Redis获取
            String countKey = OFFLINE_COUNT_KEY + userId;
            Object count = redisTemplate.opsForValue().get(countKey);
            if (count != null) {
                return Long.parseLong(count.toString());
            }
            
            // 从数据库获取
            long dbCount = offlineMessageRepository.countByUserIdAndPushedFalse(Long.valueOf(userId));
            
            // 缓存到Redis
            redisTemplate.opsForValue().set(countKey, dbCount, 1, TimeUnit.HOURS);
            
            return dbCount;
            
        } catch (Exception e) {
            log.error("获取离线消息数量失败: userId={}", userId, e);
            return 0;
        }
    }
    
    /**
     * 删除用户的所有离线消息
     */
    @Transactional
    public void deleteAllOfflineMessages(String userId) {
        try {
            // 删除数据库记录
            int deletedCount = offlineMessageRepository.deleteByUserId(Long.valueOf(userId));
            
            // 清除Redis缓存
            clearCachedOfflineMessages(userId);
            resetOfflineMessageCount(userId);
            
            log.info("删除用户所有离线消息: userId={}, count={}", userId, deletedCount);
            
        } catch (Exception e) {
            log.error("删除离线消息失败: userId={}", userId, e);
        }
    }
    
    /**
     * 检查离线消息数量是否超过限制
     */
    private boolean isOfflineMessageLimitExceeded(String userId) {
        long currentCount = getOfflineMessageCount(userId);
        int maxMessages = getMaxOfflineMessages();
        return currentCount >= maxMessages;
    }
    
    /**
     * 获取最大离线消息数量配置
     */
    private int getMaxOfflineMessages() {
        try {
            Object maxMessages = redisTemplate.opsForValue().get(MAX_OFFLINE_MESSAGES);
            if (maxMessages != null) {
                return Integer.parseInt(maxMessages.toString());
            }
        } catch (Exception e) {
            log.warn("获取最大离线消息配置失败，使用默认值", e);
        }
        return DEFAULT_MAX_OFFLINE_MESSAGES;
    }
    
    /**
     * 创建离线消息记录
     */
    private OfflineMessage createOfflineMessage(String userId, Message message) {
        OfflineMessage offlineMessage = new OfflineMessage();
        offlineMessage.setUserId(Long.valueOf(userId));
        offlineMessage.setMessageId(message.getMessageId());
        offlineMessage.setMessageType(message.getMessageType());
        offlineMessage.setSenderId(message.getSenderId());
        offlineMessage.setSenderType(message.getSenderType());
        offlineMessage.setTitle(message.getTitle());
        offlineMessage.setContent(message.getContent());
        offlineMessage.setSummary(message.getSummary());
        offlineMessage.setPriority(message.getPriority());
        offlineMessage.setTimestamp(message.getTimestamp());
        offlineMessage.setExpireTime(message.getExpireTime());
        offlineMessage.setPushed(false);
        
        // 序列化完整消息数据
        try {
            String messageData = objectMapper.writeValueAsString(message);
            offlineMessage.setMessageData(messageData);
        } catch (Exception e) {
            log.warn("序列化消息数据失败: messageId={}", message.getMessageId(), e);
        }
        
        return offlineMessage;
    }
    
    /**
     * 转换离线消息为消息对象
     */
    private Message convertToMessage(OfflineMessage offlineMessage) {
        try {
            if (offlineMessage.getMessageData() != null) {
                // 从完整数据反序列化
                return objectMapper.readValue(offlineMessage.getMessageData(), Message.class);
            } else {
                // 从基础字段构建
                Message message = new Message();
                message.setMessageId(offlineMessage.getMessageId());
                message.setMessageType(offlineMessage.getMessageType());
                message.setSenderId(offlineMessage.getSenderId());
                message.setSenderType(offlineMessage.getSenderType());
                message.setReceiverId(offlineMessage.getUserId());
                message.setReceiverType(Message.ReceiverType.USER);
                message.setTitle(offlineMessage.getTitle());
                message.setContent(offlineMessage.getContent());
                message.setSummary(offlineMessage.getSummary());
                message.setPriority(offlineMessage.getPriority());
                message.setTimestamp(offlineMessage.getTimestamp());
                message.setExpireTime(offlineMessage.getExpireTime());
                message.setStatus(Message.MessageStatus.PENDING);
                return message;
            }
        } catch (Exception e) {
            log.error("转换离线消息失败: offlineMessageId={}", offlineMessage.getId(), e);
            return null;
        }
    }
    
    /**
     * 缓存离线消息到Redis
     */
    private void cacheOfflineMessage(String userId, Message message) {
        try {
            String key = OFFLINE_MESSAGE_KEY + userId;
            String messageJson = objectMapper.writeValueAsString(message);
            redisTemplate.opsForList().rightPush(key, messageJson);
            redisTemplate.expire(key, 24, TimeUnit.HOURS);
        } catch (Exception e) {
            log.warn("缓存离线消息失败: userId={}, messageId={}", userId, message.getMessageId(), e);
        }
    }
    
    /**
     * 批量缓存离线消息
     */
    private void cacheOfflineMessages(String userId, List<Message> messages) {
        try {
            String key = OFFLINE_MESSAGE_KEY + userId;
            String[] messageJsons = messages.stream()
                .map(message -> {
                    try {
                        return objectMapper.writeValueAsString(message);
                    } catch (Exception e) {
                        log.warn("序列化消息失败: messageId={}", message.getMessageId(), e);
                        return null;
                    }
                })
                .filter(json -> json != null)
                .toArray(String[]::new);
            
            if (messageJsons.length > 0) {
                redisTemplate.opsForList().rightPushAll(key, messageJsons);
                redisTemplate.expire(key, 24, TimeUnit.HOURS);
            }
        } catch (Exception e) {
            log.warn("批量缓存离线消息失败: userId={}", userId, e);
        }
    }
    
    /**
     * 从Redis获取缓存的离线消息
     */
    private List<Message> getCachedOfflineMessages(String userId) {
        try {
            String key = OFFLINE_MESSAGE_KEY + userId;
            List<Object> messageJsons = redisTemplate.opsForList().range(key, 0, -1);
            
            if (messageJsons == null || messageJsons.isEmpty()) {
                return List.of();
            }
            
            return messageJsons.stream()
                .map(json -> {
                    try {
                        return objectMapper.readValue(json.toString(), Message.class);
                    } catch (Exception e) {
                        log.warn("反序列化缓存消息失败: userId={}", userId, e);
                        return null;
                    }
                })
                .filter(message -> message != null && !message.isExpired())
                .toList();
                
        } catch (Exception e) {
            log.warn("获取缓存离线消息失败: userId={}", userId, e);
            return List.of();
        }
    }
    
    /**
     * 清除缓存的离线消息
     */
    private void clearCachedOfflineMessages(String userId) {
        try {
            String key = OFFLINE_MESSAGE_KEY + userId;
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.warn("清除缓存离线消息失败: userId={}", userId, e);
        }
    }
    
    /**
     * 增加离线消息计数
     */
    private void incrementOfflineMessageCount(String userId) {
        try {
            String countKey = OFFLINE_COUNT_KEY + userId;
            redisTemplate.opsForValue().increment(countKey);
            redisTemplate.expire(countKey, 1, TimeUnit.HOURS);
        } catch (Exception e) {
            log.warn("增加离线消息计数失败: userId={}", userId, e);
        }
    }
    
    /**
     * 重置离线消息计数
     */
    private void resetOfflineMessageCount(String userId) {
        try {
            String countKey = OFFLINE_COUNT_KEY + userId;
            redisTemplate.delete(countKey);
        } catch (Exception e) {
            log.warn("重置离线消息计数失败: userId={}", userId, e);
        }
    }
}
