package com.teleshop.message.service;

import com.teleshop.message.entity.Message;
import com.teleshop.message.websocket.WebSocketServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * 消息路由服务
 * 负责将消息路由到正确的目标
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MessageRoutingService {
    
    private final RabbitTemplate rabbitTemplate;
    private final PushNotificationService pushNotificationService;
    private final OfflineMessageService offlineMessageService;
    
    /**
     * 路由消息
     */
    public void routeMessage(Message message) {
        try {
            switch (message.getMessageType()) {
                case TEXT, IMAGE, AUDIO, VIDEO, FILE, LOCATION, LINK -> routeChatMessage(message);
                case SYSTEM -> routeSystemMessage(message);
                case NOTIFICATION -> routeNotificationMessage(message);
                case ORDER -> routeOrderMessage(message);
                case PAYMENT -> routePaymentMessage(message);
                case MARKETING -> routeMarketingMessage(message);
                case CUSTOMER_SERVICE -> routeCustomerServiceMessage(message);
                case ACK -> routeAckMessage(message);
                case ERROR -> routeErrorMessage(message);
                default -> {
                    log.warn("未知消息类型: {}", message.getMessageType());
                    routeDefaultMessage(message);
                }
            }
            
            // 标记消息为已发送
            message.markAsSent();
            
        } catch (Exception e) {
            log.error("消息路由失败: messageId={}, type={}", 
                message.getMessageId(), message.getMessageType(), e);
            message.markAsFailed();
            throw new RuntimeException("消息路由失败", e);
        }
    }
    
    /**
     * 路由聊天消息
     */
    private void routeChatMessage(Message message) {
        if (message.getReceiverType() == Message.ReceiverType.USER) {
            // 单聊消息
            routeToUser(message);
        } else if (message.getReceiverType() == Message.ReceiverType.GROUP) {
            // 群聊消息
            routeToGroup(message);
        } else if (message.getReceiverType() == Message.ReceiverType.CHANNEL) {
            // 频道消息
            routeToChannel(message);
        }
    }
    
    /**
     * 路由系统消息
     */
    private void routeSystemMessage(Message message) {
        if (message.getReceiverType() == Message.ReceiverType.BROADCAST) {
            // 系统广播
            WebSocketServer.broadcastMessage(message);
            
            // 发送到消息队列进行集群广播
            rabbitTemplate.convertAndSend("teleshop.system.broadcast", message);
        } else if (message.getReceiverType() == Message.ReceiverType.USER) {
            // 系统通知给特定用户
            routeToUser(message);
        } else if (message.getReceiverType() == Message.ReceiverType.ROLE) {
            // 系统通知给特定角色
            routeToRole(message);
        }
    }
    
    /**
     * 路由通知消息
     */
    private void routeNotificationMessage(Message message) {
        // 通知消息需要多渠道推送
        if (message.getReceiverType() == Message.ReceiverType.USER) {
            // WebSocket推送
            routeToUser(message);
            
            // 推送通知
            pushNotificationService.sendPushNotification(message);
            
            // 邮件通知（高优先级消息）
            if (message.getPriority() == Message.Priority.HIGH || 
                message.getPriority() == Message.Priority.URGENT) {
                sendEmailNotification(message);
            }
            
            // 短信通知（紧急消息）
            if (message.getPriority() == Message.Priority.URGENT) {
                sendSmsNotification(message);
            }
        } else if (message.getReceiverType() == Message.ReceiverType.BROADCAST) {
            // 广播通知
            WebSocketServer.broadcastMessage(message);
            rabbitTemplate.convertAndSend("teleshop.notification.broadcast", message);
        }
    }
    
    /**
     * 路由订单消息
     */
    private void routeOrderMessage(Message message) {
        // 订单消息路由到用户和商家
        routeToUser(message);
        
        // 发送到订单处理队列
        rabbitTemplate.convertAndSend("teleshop.order.message", message);
        
        // 如果是重要订单状态变更，发送推送通知
        if (isImportantOrderStatus(message)) {
            pushNotificationService.sendPushNotification(message);
        }
    }
    
    /**
     * 路由支付消息
     */
    private void routePaymentMessage(Message message) {
        // 支付消息路由到用户
        routeToUser(message);
        
        // 发送到支付处理队列
        rabbitTemplate.convertAndSend("teleshop.payment.message", message);
        
        // 支付消息通常需要推送通知
        pushNotificationService.sendPushNotification(message);
        
        // 重要支付事件发送邮件通知
        if (isImportantPaymentEvent(message)) {
            sendEmailNotification(message);
        }
    }
    
    /**
     * 路由营销消息
     */
    private void routeMarketingMessage(Message message) {
        if (message.getReceiverType() == Message.ReceiverType.USER) {
            // 个性化营销消息
            routeToUser(message);
        } else if (message.getReceiverType() == Message.ReceiverType.BROADCAST) {
            // 营销广播
            WebSocketServer.broadcastMessage(message);
            rabbitTemplate.convertAndSend("teleshop.marketing.broadcast", message);
        } else if (message.getReceiverType() == Message.ReceiverType.GROUP) {
            // 分组营销
            routeToGroup(message);
        }
        
        // 营销消息发送到营销分析队列
        rabbitTemplate.convertAndSend("teleshop.marketing.analytics", message);
    }
    
    /**
     * 路由客服消息
     */
    private void routeCustomerServiceMessage(Message message) {
        // 客服消息路由到用户和客服
        routeToUser(message);
        
        // 发送到客服处理队列
        rabbitTemplate.convertAndSend("teleshop.customer.service", message);
        
        // 如果是客服回复，发送推送通知
        if (message.getSenderType() == Message.SenderType.ADMIN ||
            message.getSenderType() == Message.SenderType.MERCHANT) {
            pushNotificationService.sendPushNotification(message);
        }
    }
    
    /**
     * 路由确认消息
     */
    private void routeAckMessage(Message message) {
        // 确认消息直接路由到目标用户
        routeToUser(message);
    }
    
    /**
     * 路由错误消息
     */
    private void routeErrorMessage(Message message) {
        // 错误消息路由到用户
        routeToUser(message);
        
        // 发送到错误处理队列
        rabbitTemplate.convertAndSend("teleshop.error.message", message);
    }
    
    /**
     * 默认消息路由
     */
    private void routeDefaultMessage(Message message) {
        if (message.getReceiverId() != null) {
            routeToUser(message);
        } else {
            log.warn("无法路由消息，缺少接收者信息: messageId={}", message.getMessageId());
        }
    }
    
    /**
     * 路由消息到用户
     */
    private void routeToUser(Message message) {
        String userId = message.getReceiverId().toString();
        
        // 检查用户是否在线
        if (WebSocketServer.isUserOnline(userId)) {
            // 用户在线，直接通过WebSocket发送
            WebSocketServer.sendMessageToUser(userId, message);
            message.markAsDelivered();
        } else {
            // 用户不在线，存储为离线消息
            offlineMessageService.storeOfflineMessage(userId, message);
            
            // 发送推送通知
            if (shouldSendPushNotification(message)) {
                pushNotificationService.sendPushNotification(message);
            }
        }
    }
    
    /**
     * 路由消息到群组
     */
    private void routeToGroup(Message message) {
        String conversationId = message.getConversationId();
        
        // 获取群组成员列表
        Set<String> groupMembers = getGroupMembers(conversationId);
        
        // 排除发送者
        groupMembers.remove(message.getSenderId().toString());
        
        // 发送给群组成员
        WebSocketServer.sendMessageToGroup(groupMembers, message);
        
        // 处理离线成员
        for (String memberId : groupMembers) {
            if (!WebSocketServer.isUserOnline(memberId)) {
                offlineMessageService.storeOfflineMessage(memberId, message);
            }
        }
    }
    
    /**
     * 路由消息到频道
     */
    private void routeToChannel(Message message) {
        String conversationId = message.getConversationId();
        
        // 获取频道订阅者列表
        Set<String> subscribers = getChannelSubscribers(conversationId);
        
        // 发送给订阅者
        WebSocketServer.sendMessageToGroup(subscribers, message);
        
        // 发送到频道消息队列
        rabbitTemplate.convertAndSend("teleshop.channel." + conversationId, message);
    }
    
    /**
     * 路由消息到角色
     */
    private void routeToRole(Message message) {
        String roleName = message.getConversationId(); // 使用conversationId存储角色名
        
        // 获取角色用户列表
        Set<String> roleUsers = getRoleUsers(roleName);
        
        // 发送给角色用户
        WebSocketServer.sendMessageToGroup(roleUsers, message);
        
        // 发送到角色消息队列
        rabbitTemplate.convertAndSend("teleshop.role." + roleName, message);
    }
    
    /**
     * 发送邮件通知
     */
    private void sendEmailNotification(Message message) {
        try {
            rabbitTemplate.convertAndSend("teleshop.email.notification", message);
        } catch (Exception e) {
            log.error("发送邮件通知失败: messageId={}", message.getMessageId(), e);
        }
    }
    
    /**
     * 发送短信通知
     */
    private void sendSmsNotification(Message message) {
        try {
            rabbitTemplate.convertAndSend("teleshop.sms.notification", message);
        } catch (Exception e) {
            log.error("发送短信通知失败: messageId={}", message.getMessageId(), e);
        }
    }
    
    /**
     * 检查是否应该发送推送通知
     */
    private boolean shouldSendPushNotification(Message message) {
        // 聊天消息、通知消息、订单消息、支付消息需要推送
        return message.getMessageType() == Message.MessageType.TEXT ||
               message.getMessageType() == Message.MessageType.IMAGE ||
               message.getMessageType() == Message.MessageType.NOTIFICATION ||
               message.getMessageType() == Message.MessageType.ORDER ||
               message.getMessageType() == Message.MessageType.PAYMENT ||
               message.getMessageType() == Message.MessageType.CUSTOMER_SERVICE;
    }
    
    /**
     * 检查是否为重要订单状态
     */
    private boolean isImportantOrderStatus(Message message) {
        String subType = message.getSubType();
        return "ORDER_CONFIRMED".equals(subType) ||
               "ORDER_SHIPPED".equals(subType) ||
               "ORDER_DELIVERED".equals(subType) ||
               "ORDER_CANCELLED".equals(subType);
    }
    
    /**
     * 检查是否为重要支付事件
     */
    private boolean isImportantPaymentEvent(Message message) {
        String subType = message.getSubType();
        return "PAYMENT_SUCCESS".equals(subType) ||
               "PAYMENT_FAILED".equals(subType) ||
               "REFUND_SUCCESS".equals(subType);
    }
    
    /**
     * 获取群组成员列表
     */
    private Set<String> getGroupMembers(String conversationId) {
        // 这里应该调用群组服务获取成员列表
        // 暂时返回空集合
        return Set.of();
    }
    
    /**
     * 获取频道订阅者列表
     */
    private Set<String> getChannelSubscribers(String conversationId) {
        // 这里应该调用频道服务获取订阅者列表
        // 暂时返回空集合
        return Set.of();
    }
    
    /**
     * 获取角色用户列表
     */
    private Set<String> getRoleUsers(String roleName) {
        // 这里应该调用认证服务获取角色用户列表
        // 暂时返回空集合
        return Set.of();
    }
}
