package com.teleshop.message.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 消息实体类
 * 统一消息模型，支持多种消息类型
 */
@Entity
@Table(name = "messages", indexes = {
    @Index(name = "idx_sender_id", columnList = "senderId"),
    @Index(name = "idx_receiver_id", columnList = "receiverId"),
    @Index(name = "idx_message_type", columnList = "messageType"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_timestamp", columnList = "timestamp"),
    @Index(name = "idx_conversation_id", columnList = "conversationId")
})
@Data
@EqualsAndHashCode(callSuper = false)
public class Message {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 消息唯一标识
     */
    @Column(unique = true, nullable = false, length = 64)
    private String messageId;
    
    /**
     * 发送者ID
     */
    @Column(nullable = false)
    private Long senderId;
    
    /**
     * 发送者类型
     */
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private SenderType senderType;
    
    /**
     * 接收者ID（单聊时使用）
     */
    private Long receiverId;
    
    /**
     * 接收者类型
     */
    @Enumerated(EnumType.STRING)
    private ReceiverType receiverType;
    
    /**
     * 会话ID（群聊或频道时使用）
     */
    private String conversationId;
    
    /**
     * 消息类型
     */
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private MessageType messageType;
    
    /**
     * 消息子类型
     */
    private String subType;
    
    /**
     * 消息内容
     */
    @Column(columnDefinition = "TEXT")
    @NotBlank(message = "消息内容不能为空")
    private String content;
    
    /**
     * 消息标题（通知类消息使用）
     */
    private String title;
    
    /**
     * 消息摘要
     */
    private String summary;
    
    /**
     * 消息优先级
     */
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    private Priority priority = Priority.NORMAL;
    
    /**
     * 消息状态
     */
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private MessageStatus status = MessageStatus.PENDING;
    
    /**
     * 是否需要确认回执
     */
    @Column(nullable = false)
    private Boolean requireAck = false;
    
    /**
     * 消息过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 消息发送时间
     */
    @Column(nullable = false)
    private LocalDateTime timestamp;
    
    /**
     * 消息创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdTime;
    
    /**
     * 消息更新时间
     */
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedTime;
    
    /**
     * 关联消息ID（回复消息时使用）
     */
    private String relatedMessageId;
    
    /**
     * 消息扩展属性（JSON格式）
     */
    @Column(columnDefinition = "JSON")
    private String extraData;
    
    /**
     * 消息附件信息
     */
    @OneToMany(mappedBy = "message", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private java.util.List<MessageAttachment> attachments;
    
    /**
     * 发送者类型枚举
     */
    public enum SenderType {
        USER("用户"),
        SYSTEM("系统"),
        MERCHANT("商家"),
        ADMIN("管理员"),
        BOT("机器人");
        
        private final String description;
        
        SenderType(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    /**
     * 接收者类型枚举
     */
    public enum ReceiverType {
        USER("用户"),
        GROUP("群组"),
        CHANNEL("频道"),
        BROADCAST("广播"),
        ROLE("角色");
        
        private final String description;
        
        ReceiverType(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        TEXT("文本消息"),
        IMAGE("图片消息"),
        AUDIO("语音消息"),
        VIDEO("视频消息"),
        FILE("文件消息"),
        LOCATION("位置消息"),
        LINK("链接消息"),
        SYSTEM("系统消息"),
        NOTIFICATION("通知消息"),
        ORDER("订单消息"),
        PAYMENT("支付消息"),
        MARKETING("营销消息"),
        CUSTOMER_SERVICE("客服消息"),
        ACK("确认消息"),
        ERROR("错误消息");
        
        private final String description;
        
        MessageType(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    /**
     * 消息优先级枚举
     */
    public enum Priority {
        LOW(1, "低优先级"),
        NORMAL(2, "普通优先级"),
        HIGH(3, "高优先级"),
        URGENT(4, "紧急优先级");
        
        private final int level;
        private final String description;
        
        Priority(int level, String description) {
            this.level = level;
            this.description = description;
        }
        
        public int getLevel() { return level; }
        public String getDescription() { return description; }
    }
    
    /**
     * 消息状态枚举
     */
    public enum MessageStatus {
        PENDING("待发送"),
        SENT("已发送"),
        DELIVERED("已送达"),
        READ("已读"),
        FAILED("发送失败"),
        EXPIRED("已过期"),
        RECALLED("已撤回");
        
        private final String description;
        
        MessageStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    /**
     * 检查消息是否已过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 检查是否为系统消息
     */
    public boolean isSystemMessage() {
        return SenderType.SYSTEM.equals(senderType) || MessageType.SYSTEM.equals(messageType);
    }
    
    /**
     * 检查是否为通知消息
     */
    public boolean isNotificationMessage() {
        return MessageType.NOTIFICATION.equals(messageType);
    }
    
    /**
     * 检查是否为业务消息
     */
    public boolean isBusinessMessage() {
        return MessageType.ORDER.equals(messageType) || 
               MessageType.PAYMENT.equals(messageType) ||
               MessageType.MARKETING.equals(messageType);
    }
    
    /**
     * 检查是否为聊天消息
     */
    public boolean isChatMessage() {
        return MessageType.TEXT.equals(messageType) ||
               MessageType.IMAGE.equals(messageType) ||
               MessageType.AUDIO.equals(messageType) ||
               MessageType.VIDEO.equals(messageType) ||
               MessageType.FILE.equals(messageType) ||
               MessageType.LOCATION.equals(messageType);
    }
    
    /**
     * 设置消息为已发送
     */
    public void markAsSent() {
        this.status = MessageStatus.SENT;
    }
    
    /**
     * 设置消息为已送达
     */
    public void markAsDelivered() {
        this.status = MessageStatus.DELIVERED;
    }
    
    /**
     * 设置消息为已读
     */
    public void markAsRead() {
        this.status = MessageStatus.READ;
    }
    
    /**
     * 设置消息为发送失败
     */
    public void markAsFailed() {
        this.status = MessageStatus.FAILED;
    }
    
    /**
     * 设置消息为已撤回
     */
    public void markAsRecalled() {
        this.status = MessageStatus.RECALLED;
    }
    
    /**
     * 生成消息ID
     */
    @PrePersist
    public void generateMessageId() {
        if (this.messageId == null) {
            this.messageId = java.util.UUID.randomUUID().toString().replace("-", "");
        }
        if (this.timestamp == null) {
            this.timestamp = LocalDateTime.now();
        }
    }
}
