package com.teleshop.message.service;

import com.teleshop.message.entity.Message;
import com.teleshop.message.repository.MessageRepository;
import com.teleshop.message.websocket.WebSocketServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 消息服务
 * 处理消息的发送、接收、存储和路由
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MessageService {
    
    private final MessageRepository messageRepository;
    private final RabbitTemplate rabbitTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final MessageRoutingService messageRoutingService;
    private final OfflineMessageService offlineMessageService;
    
    // Redis键前缀
    private static final String OFFLINE_MESSAGE_KEY = "offline_message:";
    private static final String MESSAGE_CACHE_KEY = "message_cache:";
    private static final String USER_LAST_READ_KEY = "user_last_read:";
    
    /**
     * 发送消息
     */
    @Transactional
    public Message sendMessage(Message message) {
        try {
            // 验证消息
            validateMessage(message);
            
            // 保存消息到数据库
            message = messageRepository.save(message);
            
            // 缓存消息
            cacheMessage(message);
            
            // 路由消息
            messageRoutingService.routeMessage(message);
            
            log.info("消息发送成功: messageId={}, type={}, sender={}, receiver={}", 
                message.getMessageId(), message.getMessageType(), 
                message.getSenderId(), message.getReceiverId());
            
            return message;
            
        } catch (Exception e) {
            log.error("消息发送失败: {}", e.getMessage(), e);
            message.markAsFailed();
            messageRepository.save(message);
            throw new RuntimeException("消息发送失败", e);
        }
    }
    
    /**
     * 批量发送消息
     */
    @Transactional
    public List<Message> sendBatchMessages(List<Message> messages) {
        try {
            // 批量保存消息
            List<Message> savedMessages = messageRepository.saveAll(messages);
            
            // 批量缓存消息
            savedMessages.forEach(this::cacheMessage);
            
            // 批量路由消息
            savedMessages.forEach(messageRoutingService::routeMessage);
            
            log.info("批量发送消息成功，数量: {}", savedMessages.size());
            return savedMessages;
            
        } catch (Exception e) {
            log.error("批量发送消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量发送消息失败", e);
        }
    }
    
    /**
     * 处理接收到的消息
     */
    @Transactional
    public void handleIncomingMessage(Message message) {
        try {
            // 验证消息
            validateMessage(message);
            
            // 保存消息
            message = messageRepository.save(message);
            
            // 缓存消息
            cacheMessage(message);
            
            // 处理消息确认
            if (message.getRequireAck()) {
                sendAckMessage(message);
            }
            
            // 路由消息给其他接收者
            messageRoutingService.routeMessage(message);
            
            log.debug("处理接收消息成功: messageId={}", message.getMessageId());
            
        } catch (Exception e) {
            log.error("处理接收消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("处理接收消息失败", e);
        }
    }
    
    /**
     * 获取用户的离线消息
     */
    public List<Message> getOfflineMessages(String userId) {
        try {
            return offlineMessageService.getOfflineMessages(userId);
        } catch (Exception e) {
            log.error("获取离线消息失败: userId={}", userId, e);
            return List.of();
        }
    }
    
    /**
     * 存储离线消息
     */
    public void storeOfflineMessage(String userId, Message message) {
        try {
            offlineMessageService.storeOfflineMessage(userId, message);
        } catch (Exception e) {
            log.error("存储离线消息失败: userId={}, messageId={}", userId, message.getMessageId(), e);
        }
    }
    
    /**
     * 标记离线消息为已推送
     */
    public void markOfflineMessagesAsPushed(String userId) {
        try {
            offlineMessageService.markMessagesAsPushed(userId);
        } catch (Exception e) {
            log.error("标记离线消息失败: userId={}", userId, e);
        }
    }
    
    /**
     * 发送系统通知
     */
    public void sendSystemNotification(String title, String content, Set<String> userIds) {
        try {
            for (String userId : userIds) {
                Message notification = createSystemNotification(title, content, Long.valueOf(userId));
                sendMessage(notification);
            }
            
            log.info("系统通知发送成功，接收用户数: {}", userIds.size());
            
        } catch (Exception e) {
            log.error("发送系统通知失败: {}", e.getMessage(), e);
            throw new RuntimeException("发送系统通知失败", e);
        }
    }
    
    /**
     * 广播消息
     */
    public void broadcastMessage(String title, String content) {
        try {
            Message broadcastMessage = createBroadcastMessage(title, content);
            
            // 保存消息
            broadcastMessage = messageRepository.save(broadcastMessage);
            
            // 通过WebSocket广播
            WebSocketServer.broadcastMessage(broadcastMessage);
            
            // 通过消息队列广播
            rabbitTemplate.convertAndSend("teleshop.broadcast", broadcastMessage);
            
            log.info("广播消息发送成功: messageId={}", broadcastMessage.getMessageId());
            
        } catch (Exception e) {
            log.error("广播消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("广播消息失败", e);
        }
    }
    
    /**
     * 撤回消息
     */
    @Transactional
    public void recallMessage(String messageId, Long operatorId) {
        try {
            Message message = messageRepository.findByMessageId(messageId)
                .orElseThrow(() -> new RuntimeException("消息不存在"));
            
            // 验证撤回权限
            if (!canRecallMessage(message, operatorId)) {
                throw new RuntimeException("无权撤回该消息");
            }
            
            // 标记消息为已撤回
            message.markAsRecalled();
            messageRepository.save(message);
            
            // 发送撤回通知
            sendRecallNotification(message);
            
            // 清除缓存
            clearMessageCache(messageId);
            
            log.info("消息撤回成功: messageId={}, operator={}", messageId, operatorId);
            
        } catch (Exception e) {
            log.error("撤回消息失败: messageId={}, operator={}", messageId, operatorId, e);
            throw new RuntimeException("撤回消息失败", e);
        }
    }
    
    /**
     * 标记消息为已读
     */
    @Transactional
    public void markMessageAsRead(String messageId, Long userId) {
        try {
            Message message = messageRepository.findByMessageId(messageId)
                .orElseThrow(() -> new RuntimeException("消息不存在"));
            
            // 验证读取权限
            if (!canReadMessage(message, userId)) {
                throw new RuntimeException("无权读取该消息");
            }
            
            // 标记为已读
            message.markAsRead();
            messageRepository.save(message);
            
            // 更新用户最后读取时间
            updateUserLastReadTime(userId, message.getConversationId());
            
            // 发送已读回执
            if (message.getRequireAck()) {
                sendReadReceipt(message, userId);
            }
            
            log.debug("消息标记为已读: messageId={}, userId={}", messageId, userId);
            
        } catch (Exception e) {
            log.error("标记消息已读失败: messageId={}, userId={}", messageId, userId, e);
        }
    }
    
    /**
     * 获取会话消息历史
     */
    public List<Message> getConversationHistory(String conversationId, Long userId, int page, int size) {
        try {
            // 验证访问权限
            if (!canAccessConversation(conversationId, userId)) {
                throw new RuntimeException("无权访问该会话");
            }
            
            return messageRepository.findByConversationIdOrderByTimestampDesc(
                conversationId, 
                org.springframework.data.domain.PageRequest.of(page, size)
            );
            
        } catch (Exception e) {
            log.error("获取会话历史失败: conversationId={}, userId={}", conversationId, userId, e);
            return List.of();
        }
    }
    
    /**
     * 验证消息
     */
    private void validateMessage(Message message) {
        if (message.getSenderId() == null) {
            throw new IllegalArgumentException("发送者ID不能为空");
        }
        
        if (message.getMessageType() == null) {
            throw new IllegalArgumentException("消息类型不能为空");
        }
        
        if (message.getContent() == null || message.getContent().trim().isEmpty()) {
            throw new IllegalArgumentException("消息内容不能为空");
        }
        
        // 检查消息是否过期
        if (message.isExpired()) {
            throw new IllegalArgumentException("消息已过期");
        }
    }
    
    /**
     * 缓存消息
     */
    private void cacheMessage(Message message) {
        try {
            String cacheKey = MESSAGE_CACHE_KEY + message.getMessageId();
            redisTemplate.opsForValue().set(cacheKey, message, 24, TimeUnit.HOURS);
        } catch (Exception e) {
            log.warn("缓存消息失败: messageId={}", message.getMessageId(), e);
        }
    }
    
    /**
     * 清除消息缓存
     */
    private void clearMessageCache(String messageId) {
        try {
            String cacheKey = MESSAGE_CACHE_KEY + messageId;
            redisTemplate.delete(cacheKey);
        } catch (Exception e) {
            log.warn("清除消息缓存失败: messageId={}", messageId, e);
        }
    }
    
    /**
     * 发送确认消息
     */
    private void sendAckMessage(Message originalMessage) {
        Message ackMessage = new Message();
        ackMessage.setMessageType(Message.MessageType.ACK);
        ackMessage.setSenderId(0L); // 系统发送
        ackMessage.setSenderType(Message.SenderType.SYSTEM);
        ackMessage.setReceiverId(originalMessage.getSenderId());
        ackMessage.setReceiverType(Message.ReceiverType.USER);
        ackMessage.setContent("消息已收到");
        ackMessage.setRelatedMessageId(originalMessage.getMessageId());
        ackMessage.setTimestamp(LocalDateTime.now());
        
        messageRoutingService.routeMessage(ackMessage);
    }
    
    /**
     * 创建系统通知
     */
    private Message createSystemNotification(String title, String content, Long userId) {
        Message notification = new Message();
        notification.setMessageType(Message.MessageType.NOTIFICATION);
        notification.setSenderId(0L);
        notification.setSenderType(Message.SenderType.SYSTEM);
        notification.setReceiverId(userId);
        notification.setReceiverType(Message.ReceiverType.USER);
        notification.setTitle(title);
        notification.setContent(content);
        notification.setPriority(Message.Priority.NORMAL);
        notification.setTimestamp(LocalDateTime.now());
        
        return notification;
    }
    
    /**
     * 创建广播消息
     */
    private Message createBroadcastMessage(String title, String content) {
        Message broadcast = new Message();
        broadcast.setMessageType(Message.MessageType.NOTIFICATION);
        broadcast.setSenderId(0L);
        broadcast.setSenderType(Message.SenderType.SYSTEM);
        broadcast.setReceiverType(Message.ReceiverType.BROADCAST);
        broadcast.setTitle(title);
        broadcast.setContent(content);
        broadcast.setPriority(Message.Priority.HIGH);
        broadcast.setTimestamp(LocalDateTime.now());
        
        return broadcast;
    }
    
    /**
     * 发送撤回通知
     */
    private void sendRecallNotification(Message originalMessage) {
        Message recallNotification = new Message();
        recallNotification.setMessageType(Message.MessageType.SYSTEM);
        recallNotification.setSenderId(0L);
        recallNotification.setSenderType(Message.SenderType.SYSTEM);
        recallNotification.setReceiverId(originalMessage.getReceiverId());
        recallNotification.setReceiverType(originalMessage.getReceiverType());
        recallNotification.setConversationId(originalMessage.getConversationId());
        recallNotification.setContent("消息已被撤回");
        recallNotification.setRelatedMessageId(originalMessage.getMessageId());
        recallNotification.setTimestamp(LocalDateTime.now());
        
        messageRoutingService.routeMessage(recallNotification);
    }
    
    /**
     * 发送已读回执
     */
    private void sendReadReceipt(Message originalMessage, Long readerId) {
        Message readReceipt = new Message();
        readReceipt.setMessageType(Message.MessageType.ACK);
        readReceipt.setSenderId(readerId);
        readReceipt.setSenderType(Message.SenderType.USER);
        readReceipt.setReceiverId(originalMessage.getSenderId());
        readReceipt.setReceiverType(Message.ReceiverType.USER);
        readReceipt.setContent("消息已读");
        readReceipt.setSubType("READ_RECEIPT");
        readReceipt.setRelatedMessageId(originalMessage.getMessageId());
        readReceipt.setTimestamp(LocalDateTime.now());
        
        messageRoutingService.routeMessage(readReceipt);
    }
    
    /**
     * 更新用户最后读取时间
     */
    private void updateUserLastReadTime(Long userId, String conversationId) {
        try {
            String key = USER_LAST_READ_KEY + userId + ":" + conversationId;
            redisTemplate.opsForValue().set(key, LocalDateTime.now(), 30, TimeUnit.DAYS);
        } catch (Exception e) {
            log.warn("更新用户最后读取时间失败: userId={}, conversationId={}", userId, conversationId, e);
        }
    }
    
    /**
     * 检查是否可以撤回消息
     */
    private boolean canRecallMessage(Message message, Long operatorId) {
        // 只有发送者或管理员可以撤回消息
        return message.getSenderId().equals(operatorId) || isAdmin(operatorId);
    }
    
    /**
     * 检查是否可以读取消息
     */
    private boolean canReadMessage(Message message, Long userId) {
        // 接收者或发送者可以读取消息
        return message.getReceiverId() != null && message.getReceiverId().equals(userId) ||
               message.getSenderId().equals(userId);
    }
    
    /**
     * 检查是否可以访问会话
     */
    private boolean canAccessConversation(String conversationId, Long userId) {
        // 这里应该实现具体的权限检查逻辑
        return true;
    }
    
    /**
     * 检查是否为管理员
     */
    private boolean isAdmin(Long userId) {
        // 这里应该调用认证服务检查用户权限
        return false;
    }
}
