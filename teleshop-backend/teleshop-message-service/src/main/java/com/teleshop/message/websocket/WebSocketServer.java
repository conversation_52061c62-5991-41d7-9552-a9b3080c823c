package com.teleshop.message.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teleshop.message.entity.Message;
import com.teleshop.message.service.MessageService;
import com.teleshop.message.service.UserSessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * WebSocket服务器
 * 处理客户端连接、消息发送和接收
 */
@ServerEndpoint("/websocket/{userId}")
@Component
@Slf4j
public class WebSocketServer {
    
    /**
     * 存储所有在线用户的Session
     * key: userId, value: Session集合（支持多端登录）
     */
    private static final ConcurrentHashMap<String, CopyOnWriteArraySet<Session>> USER_SESSIONS = new ConcurrentHashMap<>();
    
    /**
     * 存储Session到用户ID的映射
     * key: sessionId, value: userId
     */
    private static final ConcurrentHashMap<String, String> SESSION_USER_MAP = new ConcurrentHashMap<>();
    
    /**
     * 在线用户数量
     */
    private static volatile int onlineCount = 0;
    
    private static MessageService messageService;
    private static UserSessionService userSessionService;
    private static ObjectMapper objectMapper;
    
    @Autowired
    public void setMessageService(MessageService messageService) {
        WebSocketServer.messageService = messageService;
    }
    
    @Autowired
    public void setUserSessionService(UserSessionService userSessionService) {
        WebSocketServer.userSessionService = userSessionService;
    }
    
    @Autowired
    public void setObjectMapper(ObjectMapper objectMapper) {
        WebSocketServer.objectMapper = objectMapper;
    }
    
    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) {
        try {
            // 验证用户身份
            if (!isValidUser(userId, session)) {
                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "用户身份验证失败"));
                return;
            }
            
            // 添加用户会话
            addUserSession(userId, session);
            
            // 记录用户上线
            userSessionService.recordUserOnline(userId, session.getId());
            
            // 推送离线消息
            pushOfflineMessages(userId);
            
            log.info("用户 {} 建立WebSocket连接，当前在线人数: {}", userId, getOnlineCount());
            
            // 发送连接成功消息
            sendMessage(session, createSystemMessage("连接成功", "CONNECT_SUCCESS"));
            
        } catch (Exception e) {
            log.error("WebSocket连接建立失败: {}", e.getMessage(), e);
            try {
                session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "连接建立失败"));
            } catch (IOException ioException) {
                log.error("关闭WebSocket连接失败", ioException);
            }
        }
    }
    
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(Session session, @PathParam("userId") String userId) {
        try {
            // 移除用户会话
            removeUserSession(userId, session);
            
            // 记录用户下线
            userSessionService.recordUserOffline(userId, session.getId());
            
            log.info("用户 {} 断开WebSocket连接，当前在线人数: {}", userId, getOnlineCount());
            
        } catch (Exception e) {
            log.error("WebSocket连接关闭处理失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String messageText, Session session, @PathParam("userId") String userId) {
        try {
            log.debug("收到用户 {} 的消息: {}", userId, messageText);
            
            // 解析消息
            Message message = objectMapper.readValue(messageText, Message.class);
            message.setSenderId(Long.valueOf(userId));
            message.setSenderType(Message.SenderType.USER);
            
            // 处理消息
            messageService.handleIncomingMessage(message);
            
            // 发送确认回执
            sendMessage(session, createAckMessage(message.getMessageId()));
            
        } catch (Exception e) {
            log.error("处理WebSocket消息失败: {}", e.getMessage(), e);
            sendMessage(session, createErrorMessage("消息处理失败: " + e.getMessage()));
        }
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error, @PathParam("userId") String userId) {
        log.error("用户 {} 的WebSocket连接发生错误", userId, error);
        
        try {
            if (session.isOpen()) {
                sendMessage(session, createErrorMessage("连接发生错误"));
            }
        } catch (Exception e) {
            log.error("发送错误消息失败", e);
        }
    }
    
    /**
     * 向指定用户发送消息
     */
    public static void sendMessageToUser(String userId, Message message) {
        CopyOnWriteArraySet<Session> sessions = USER_SESSIONS.get(userId);
        if (sessions != null && !sessions.isEmpty()) {
            String messageText = serializeMessage(message);
            
            sessions.removeIf(session -> {
                try {
                    if (session.isOpen()) {
                        session.getBasicRemote().sendText(messageText);
                        return false;
                    } else {
                        return true; // 移除已关闭的会话
                    }
                } catch (IOException e) {
                    log.error("向用户 {} 发送消息失败", userId, e);
                    return true; // 移除发送失败的会话
                }
            });
            
            // 如果所有会话都已移除，清理用户记录
            if (sessions.isEmpty()) {
                USER_SESSIONS.remove(userId);
            }
        } else {
            // 用户不在线，存储为离线消息
            messageService.storeOfflineMessage(userId, message);
            log.debug("用户 {} 不在线，消息已存储为离线消息", userId);
        }
    }
    
    /**
     * 广播消息给所有在线用户
     */
    public static void broadcastMessage(Message message) {
        String messageText = serializeMessage(message);
        
        USER_SESSIONS.forEach((userId, sessions) -> {
            sessions.removeIf(session -> {
                try {
                    if (session.isOpen()) {
                        session.getBasicRemote().sendText(messageText);
                        return false;
                    } else {
                        return true;
                    }
                } catch (IOException e) {
                    log.error("向用户 {} 广播消息失败", userId, e);
                    return true;
                }
            });
        });
    }
    
    /**
     * 向指定用户组发送消息
     */
    public static void sendMessageToGroup(java.util.Set<String> userIds, Message message) {
        userIds.forEach(userId -> sendMessageToUser(userId, message));
    }
    
    /**
     * 获取在线用户数量
     */
    public static synchronized int getOnlineCount() {
        return onlineCount;
    }
    
    /**
     * 获取在线用户列表
     */
    public static java.util.Set<String> getOnlineUsers() {
        return USER_SESSIONS.keySet();
    }
    
    /**
     * 检查用户是否在线
     */
    public static boolean isUserOnline(String userId) {
        CopyOnWriteArraySet<Session> sessions = USER_SESSIONS.get(userId);
        return sessions != null && !sessions.isEmpty() && 
               sessions.stream().anyMatch(Session::isOpen);
    }
    
    /**
     * 添加用户会话
     */
    private void addUserSession(String userId, Session session) {
        USER_SESSIONS.computeIfAbsent(userId, k -> new CopyOnWriteArraySet<>()).add(session);
        SESSION_USER_MAP.put(session.getId(), userId);
        addOnlineCount();
    }
    
    /**
     * 移除用户会话
     */
    private void removeUserSession(String userId, Session session) {
        CopyOnWriteArraySet<Session> sessions = USER_SESSIONS.get(userId);
        if (sessions != null) {
            sessions.remove(session);
            if (sessions.isEmpty()) {
                USER_SESSIONS.remove(userId);
            }
        }
        SESSION_USER_MAP.remove(session.getId());
        subOnlineCount();
    }
    
    /**
     * 验证用户身份
     */
    private boolean isValidUser(String userId, Session session) {
        // 这里可以添加JWT令牌验证逻辑
        // 从session的查询参数或头部获取token进行验证
        try {
            Long.valueOf(userId); // 简单验证userId是否为数字
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 推送离线消息
     */
    private void pushOfflineMessages(String userId) {
        try {
            java.util.List<Message> offlineMessages = messageService.getOfflineMessages(userId);
            for (Message message : offlineMessages) {
                sendMessageToUser(userId, message);
            }
            
            // 标记离线消息为已推送
            messageService.markOfflineMessagesAsPushed(userId);
            
        } catch (Exception e) {
            log.error("推送离线消息失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 发送消息到指定会话
     */
    private void sendMessage(Session session, Message message) {
        try {
            if (session.isOpen()) {
                String messageText = serializeMessage(message);
                session.getBasicRemote().sendText(messageText);
            }
        } catch (IOException e) {
            log.error("发送消息失败", e);
        }
    }
    
    /**
     * 序列化消息
     */
    private static String serializeMessage(Message message) {
        try {
            return objectMapper.writeValueAsString(message);
        } catch (Exception e) {
            log.error("序列化消息失败", e);
            return "{\"type\":\"ERROR\",\"content\":\"消息序列化失败\"}";
        }
    }
    
    /**
     * 创建系统消息
     */
    private Message createSystemMessage(String content, String type) {
        Message message = new Message();
        message.setMessageType(Message.MessageType.SYSTEM);
        message.setContent(content);
        message.setSubType(type);
        message.setTimestamp(java.time.LocalDateTime.now());
        return message;
    }
    
    /**
     * 创建确认消息
     */
    private Message createAckMessage(String originalMessageId) {
        Message message = new Message();
        message.setMessageType(Message.MessageType.ACK);
        message.setContent("消息已收到");
        message.setRelatedMessageId(originalMessageId);
        message.setTimestamp(java.time.LocalDateTime.now());
        return message;
    }
    
    /**
     * 创建错误消息
     */
    private Message createErrorMessage(String errorContent) {
        Message message = new Message();
        message.setMessageType(Message.MessageType.ERROR);
        message.setContent(errorContent);
        message.setTimestamp(java.time.LocalDateTime.now());
        return message;
    }
    
    /**
     * 增加在线人数
     */
    private static synchronized void addOnlineCount() {
        onlineCount++;
    }
    
    /**
     * 减少在线人数
     */
    private static synchronized void subOnlineCount() {
        onlineCount--;
    }
}
