package com.teleshop.message.service;

import com.teleshop.message.entity.Message;
import com.teleshop.message.entity.PushNotification;
import com.teleshop.message.repository.PushNotificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 推送通知服务
 * 处理移动端推送通知的发送和管理
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PushNotificationService {
    
    private final PushNotificationRepository pushNotificationRepository;
    private final RabbitTemplate rabbitTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    
    // Redis键前缀
    private static final String DEVICE_TOKEN_KEY = "device_token:";
    private static final String PUSH_SETTINGS_KEY = "push_settings:";
    private static final String PUSH_STATS_KEY = "push_stats:";
    
    // 推送队列名称
    private static final String ANDROID_PUSH_QUEUE = "teleshop.push.android";
    private static final String IOS_PUSH_QUEUE = "teleshop.push.ios";
    private static final String WEB_PUSH_QUEUE = "teleshop.push.web";
    
    /**
     * 发送推送通知
     */
    @Transactional
    public void sendPushNotification(Message message) {
        try {
            // 检查用户推送设置
            if (!isPushEnabled(message.getReceiverId())) {
                log.debug("用户 {} 已关闭推送通知", message.getReceiverId());
                return;
            }
            
            // 检查消息类型是否需要推送
            if (!shouldPushMessage(message)) {
                log.debug("消息类型 {} 不需要推送", message.getMessageType());
                return;
            }
            
            // 获取用户设备信息
            Map<String, String> deviceTokens = getUserDeviceTokens(message.getReceiverId());
            if (deviceTokens.isEmpty()) {
                log.debug("用户 {} 没有注册设备令牌", message.getReceiverId());
                return;
            }
            
            // 创建推送通知记录
            PushNotification pushNotification = createPushNotification(message);
            pushNotification = pushNotificationRepository.save(pushNotification);
            
            // 发送到不同平台的推送队列
            for (Map.Entry<String, String> entry : deviceTokens.entrySet()) {
                String platform = entry.getKey();
                String deviceToken = entry.getValue();
                
                sendToPlatform(platform, deviceToken, message, pushNotification.getId());
            }
            
            // 更新推送统计
            updatePushStats(message.getReceiverId(), message.getMessageType());
            
            log.info("推送通知发送成功: messageId={}, userId={}, platforms={}", 
                message.getMessageId(), message.getReceiverId(), deviceTokens.keySet());
            
        } catch (Exception e) {
            log.error("发送推送通知失败: messageId={}, userId={}", 
                message.getMessageId(), message.getReceiverId(), e);
        }
    }
    
    /**
     * 批量发送推送通知
     */
    public void sendBatchPushNotifications(java.util.List<Message> messages) {
        for (Message message : messages) {
            try {
                sendPushNotification(message);
            } catch (Exception e) {
                log.error("批量推送失败: messageId={}", message.getMessageId(), e);
            }
        }
    }
    
    /**
     * 注册设备令牌
     */
    public void registerDeviceToken(Long userId, String platform, String deviceToken) {
        try {
            String key = DEVICE_TOKEN_KEY + userId;
            redisTemplate.opsForHash().put(key, platform, deviceToken);
            redisTemplate.expire(key, 30, TimeUnit.DAYS);
            
            log.info("设备令牌注册成功: userId={}, platform={}", userId, platform);
            
        } catch (Exception e) {
            log.error("注册设备令牌失败: userId={}, platform={}", userId, platform, e);
        }
    }
    
    /**
     * 注销设备令牌
     */
    public void unregisterDeviceToken(Long userId, String platform) {
        try {
            String key = DEVICE_TOKEN_KEY + userId;
            redisTemplate.opsForHash().delete(key, platform);
            
            log.info("设备令牌注销成功: userId={}, platform={}", userId, platform);
            
        } catch (Exception e) {
            log.error("注销设备令牌失败: userId={}, platform={}", userId, platform, e);
        }
    }
    
    /**
     * 更新用户推送设置
     */
    public void updatePushSettings(Long userId, Map<String, Boolean> settings) {
        try {
            String key = PUSH_SETTINGS_KEY + userId;
            redisTemplate.opsForHash().putAll(key, settings);
            redisTemplate.expire(key, 30, TimeUnit.DAYS);
            
            log.info("推送设置更新成功: userId={}, settings={}", userId, settings);
            
        } catch (Exception e) {
            log.error("更新推送设置失败: userId={}", userId, e);
        }
    }
    
    /**
     * 获取用户推送设置
     */
    public Map<String, Boolean> getPushSettings(Long userId) {
        try {
            String key = PUSH_SETTINGS_KEY + userId;
            Map<Object, Object> settings = redisTemplate.opsForHash().entries(key);
            
            Map<String, Boolean> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : settings.entrySet()) {
                result.put(entry.getKey().toString(), Boolean.valueOf(entry.getValue().toString()));
            }
            
            // 设置默认值
            result.putIfAbsent("chat_messages", true);
            result.putIfAbsent("order_updates", true);
            result.putIfAbsent("payment_notifications", true);
            result.putIfAbsent("marketing_messages", false);
            result.putIfAbsent("system_notifications", true);
            
            return result;
            
        } catch (Exception e) {
            log.error("获取推送设置失败: userId={}", userId, e);
            return getDefaultPushSettings();
        }
    }
    
    /**
     * 获取推送统计信息
     */
    public Map<String, Object> getPushStats(Long userId) {
        try {
            String key = PUSH_STATS_KEY + userId;
            Map<Object, Object> stats = redisTemplate.opsForHash().entries(key);
            
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : stats.entrySet()) {
                result.put(entry.getKey().toString(), entry.getValue());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取推送统计失败: userId={}", userId, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 标记推送通知为已送达
     */
    @Transactional
    public void markAsDelivered(Long pushNotificationId) {
        try {
            pushNotificationRepository.findById(pushNotificationId)
                .ifPresent(notification -> {
                    notification.setStatus(PushNotification.PushStatus.DELIVERED);
                    notification.setDeliveredTime(LocalDateTime.now());
                    pushNotificationRepository.save(notification);
                });
                
        } catch (Exception e) {
            log.error("标记推送通知送达失败: pushNotificationId={}", pushNotificationId, e);
        }
    }
    
    /**
     * 标记推送通知为已点击
     */
    @Transactional
    public void markAsClicked(Long pushNotificationId) {
        try {
            pushNotificationRepository.findById(pushNotificationId)
                .ifPresent(notification -> {
                    notification.setStatus(PushNotification.PushStatus.CLICKED);
                    notification.setClickedTime(LocalDateTime.now());
                    pushNotificationRepository.save(notification);
                });
                
        } catch (Exception e) {
            log.error("标记推送通知点击失败: pushNotificationId={}", pushNotificationId, e);
        }
    }
    
    /**
     * 检查用户是否启用推送
     */
    private boolean isPushEnabled(Long userId) {
        Map<String, Boolean> settings = getPushSettings(userId);
        return settings.getOrDefault("enabled", true);
    }
    
    /**
     * 检查消息是否需要推送
     */
    private boolean shouldPushMessage(Message message) {
        Map<String, Boolean> settings = getPushSettings(message.getReceiverId());
        
        return switch (message.getMessageType()) {
            case TEXT, IMAGE, AUDIO, VIDEO, FILE, LOCATION, LINK, CUSTOMER_SERVICE -> 
                settings.getOrDefault("chat_messages", true);
            case ORDER -> 
                settings.getOrDefault("order_updates", true);
            case PAYMENT -> 
                settings.getOrDefault("payment_notifications", true);
            case MARKETING -> 
                settings.getOrDefault("marketing_messages", false);
            case SYSTEM, NOTIFICATION -> 
                settings.getOrDefault("system_notifications", true);
            default -> false;
        };
    }
    
    /**
     * 获取用户设备令牌
     */
    private Map<String, String> getUserDeviceTokens(Long userId) {
        try {
            String key = DEVICE_TOKEN_KEY + userId;
            Map<Object, Object> tokens = redisTemplate.opsForHash().entries(key);
            
            Map<String, String> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : tokens.entrySet()) {
                result.put(entry.getKey().toString(), entry.getValue().toString());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取用户设备令牌失败: userId={}", userId, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 创建推送通知记录
     */
    private PushNotification createPushNotification(Message message) {
        PushNotification pushNotification = new PushNotification();
        pushNotification.setUserId(message.getReceiverId());
        pushNotification.setMessageId(message.getMessageId());
        pushNotification.setTitle(generatePushTitle(message));
        pushNotification.setContent(generatePushContent(message));
        pushNotification.setMessageType(message.getMessageType());
        pushNotification.setPriority(message.getPriority());
        pushNotification.setStatus(PushNotification.PushStatus.PENDING);
        pushNotification.setCreatedTime(LocalDateTime.now());
        
        return pushNotification;
    }
    
    /**
     * 发送到指定平台
     */
    private void sendToPlatform(String platform, String deviceToken, Message message, Long pushNotificationId) {
        try {
            Map<String, Object> pushData = createPushData(deviceToken, message, pushNotificationId);
            
            String queueName = switch (platform.toLowerCase()) {
                case "android" -> ANDROID_PUSH_QUEUE;
                case "ios" -> IOS_PUSH_QUEUE;
                case "web" -> WEB_PUSH_QUEUE;
                default -> {
                    log.warn("未知推送平台: {}", platform);
                    yield null;
                }
            };
            
            if (queueName != null) {
                rabbitTemplate.convertAndSend(queueName, pushData);
                log.debug("推送消息发送到队列: platform={}, queue={}", platform, queueName);
            }
            
        } catch (Exception e) {
            log.error("发送到平台失败: platform={}, deviceToken={}", platform, deviceToken, e);
        }
    }
    
    /**
     * 创建推送数据
     */
    private Map<String, Object> createPushData(String deviceToken, Message message, Long pushNotificationId) {
        Map<String, Object> pushData = new HashMap<>();
        pushData.put("deviceToken", deviceToken);
        pushData.put("title", generatePushTitle(message));
        pushData.put("body", generatePushContent(message));
        pushData.put("messageId", message.getMessageId());
        pushData.put("pushNotificationId", pushNotificationId);
        pushData.put("messageType", message.getMessageType().name());
        pushData.put("priority", message.getPriority().name());
        pushData.put("timestamp", message.getTimestamp());
        
        // 添加自定义数据
        Map<String, Object> customData = new HashMap<>();
        customData.put("conversationId", message.getConversationId());
        customData.put("senderId", message.getSenderId());
        customData.put("senderType", message.getSenderType().name());
        pushData.put("data", customData);
        
        return pushData;
    }
    
    /**
     * 生成推送标题
     */
    private String generatePushTitle(Message message) {
        if (message.getTitle() != null && !message.getTitle().isEmpty()) {
            return message.getTitle();
        }
        
        return switch (message.getMessageType()) {
            case TEXT, IMAGE, AUDIO, VIDEO, FILE -> "新消息";
            case ORDER -> "订单更新";
            case PAYMENT -> "支付通知";
            case MARKETING -> "优惠活动";
            case SYSTEM, NOTIFICATION -> "系统通知";
            case CUSTOMER_SERVICE -> "客服消息";
            default -> "TeleShop";
        };
    }
    
    /**
     * 生成推送内容
     */
    private String generatePushContent(Message message) {
        String content = message.getContent();
        if (content == null || content.isEmpty()) {
            return "您有一条新消息";
        }
        
        // 限制推送内容长度
        if (content.length() > 100) {
            return content.substring(0, 97) + "...";
        }
        
        return content;
    }
    
    /**
     * 更新推送统计
     */
    private void updatePushStats(Long userId, Message.MessageType messageType) {
        try {
            String key = PUSH_STATS_KEY + userId;
            String field = "total_sent";
            String typeField = messageType.name().toLowerCase() + "_sent";
            
            redisTemplate.opsForHash().increment(key, field, 1);
            redisTemplate.opsForHash().increment(key, typeField, 1);
            redisTemplate.expire(key, 30, TimeUnit.DAYS);
            
        } catch (Exception e) {
            log.warn("更新推送统计失败: userId={}", userId, e);
        }
    }
    
    /**
     * 获取默认推送设置
     */
    private Map<String, Boolean> getDefaultPushSettings() {
        Map<String, Boolean> defaults = new HashMap<>();
        defaults.put("enabled", true);
        defaults.put("chat_messages", true);
        defaults.put("order_updates", true);
        defaults.put("payment_notifications", true);
        defaults.put("marketing_messages", false);
        defaults.put("system_notifications", true);
        return defaults;
    }
}
