# TeleShop 消息推送服务配置
server:
  port: 8082
  servlet:
    context-path: /message-service

spring:
  application:
    name: teleshop-message-service
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: ${DB_USERNAME:teleshop}
    password: ${DB_PASSWORD:teleshop123}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 30
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
      pool-name: TeleShopMessageHikariCP
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 50
        order_inserts: true
        order_updates: true
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 1
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 50
          max-idle: 20
          min-idle: 10
          max-wait: 2000ms
  
  # RabbitMQ配置
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:teleshop}
    password: ${RABBITMQ_PASSWORD:teleshop123}
    virtual-host: ${RABBITMQ_VHOST:/teleshop}
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          max-interval: 10000
          multiplier: 2
  
  # WebSocket配置
  websocket:
    allowed-origins: "*"
    sockjs:
      enabled: true
    stomp:
      enabled: true
  
  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# TeleShop消息服务配置
teleshop:
  message:
    # WebSocket配置
    websocket:
      max-connections: 10000
      heartbeat-interval: 30000
      connection-timeout: 60000
      max-message-size: 1048576  # 1MB
    
    # 离线消息配置
    offline:
      max-messages-per-user: 1000
      expire-days: 30
      cleanup-interval: 3600000  # 1小时
    
    # 推送通知配置
    push:
      android:
        enabled: true
        server-key: ${ANDROID_PUSH_SERVER_KEY:}
        max-retry: 3
      ios:
        enabled: true
        key-id: ${IOS_PUSH_KEY_ID:}
        team-id: ${IOS_PUSH_TEAM_ID:}
        bundle-id: ${IOS_PUSH_BUNDLE_ID:com.teleshop.app}
        key-file: ${IOS_PUSH_KEY_FILE:}
        production: ${IOS_PUSH_PRODUCTION:false}
      web:
        enabled: true
        vapid-public-key: ${WEB_PUSH_VAPID_PUBLIC_KEY:}
        vapid-private-key: ${WEB_PUSH_VAPID_PRIVATE_KEY:}
    
    # 消息路由配置
    routing:
      default-queue: teleshop.message.default
      retry-attempts: 3
      retry-delay: 1000
      dead-letter-queue: teleshop.message.dlq
    
    # 消息过滤配置
    filter:
      spam-detection: true
      content-moderation: true
      rate-limiting: true
      max-messages-per-minute: 60

# 日志配置
logging:
  level:
    com.teleshop.message: DEBUG
    org.springframework.web.socket: DEBUG
    org.springframework.amqp: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/teleshop-message-service.log
    max-size: 100MB
    max-history: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,websocket
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: teleshop-message-service

# Swagger文档配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: TeleShop 消息推送服务 API
    description: TeleShop统一消息推送服务接口文档
    version: 1.0.0
    contact:
      name: TeleShop开发团队
      email: <EMAIL>

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: **************************************************************************************************************************************************************
  
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create-drop

logging:
  level:
    root: INFO
    com.teleshop.message: DEBUG

teleshop:
  message:
    websocket:
      max-connections: 100
    offline:
      max-messages-per-user: 100
    push:
      android:
        enabled: false
      ios:
        enabled: false
        production: false
      web:
        enabled: false

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: *************************************************************************************************************************************************************
  
  data:
    redis:
      host: test-redis
      port: 6379
  
  rabbitmq:
    host: test-rabbitmq
    port: 5672

teleshop:
  message:
    websocket:
      max-connections: 1000
    push:
      ios:
        production: false

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: jdbc:mysql://${DB_HOST}:${DB_PORT}/teleshop_message?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai
    hikari:
      maximum-pool-size: 100
  
  data:
    redis:
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
      password: ${REDIS_PASSWORD}
      ssl: true
  
  rabbitmq:
    host: ${RABBITMQ_HOST}
    port: ${RABBITMQ_PORT}
    username: ${RABBITMQ_USERNAME}
    password: ${RABBITMQ_PASSWORD}
    ssl:
      enabled: true

logging:
  level:
    root: WARN
    com.teleshop.message: INFO
  file:
    name: /var/log/teleshop/message-service.log

management:
  endpoint:
    health:
      show-details: never

teleshop:
  message:
    websocket:
      max-connections: 50000
    push:
      ios:
        production: true
