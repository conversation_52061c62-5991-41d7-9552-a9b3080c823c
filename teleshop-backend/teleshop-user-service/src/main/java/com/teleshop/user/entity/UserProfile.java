package com.teleshop.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户资料实体类
 * 对应数据库表：t_user_profile
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_user_profile", indexes = {
    @Index(name = "idx_user_id", columnList = "user_id"),
    @Index(name = "idx_real_name", columnList = "real_name"),
    @Index(name = "idx_id_card", columnList = "id_card")
})
@EntityListeners(AuditingEntityListener.class)
@DynamicInsert
@DynamicUpdate
public class UserProfile {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 真实姓名
     */
    @Column(name = "real_name", length = 50)
    private String realName;

    /**
     * 身份证号
     */
    @Column(name = "id_card", length = 18)
    private String idCard;

    /**
     * 身份证正面照
     */
    @Column(name = "id_card_front", length = 500)
    private String idCardFront;

    /**
     * 身份证背面照
     */
    @Column(name = "id_card_back", length = 500)
    private String idCardBack;

    /**
     * 职业
     */
    @Column(name = "occupation", length = 100)
    private String occupation;

    /**
     * 公司名称
     */
    @Column(name = "company", length = 200)
    private String company;

    /**
     * 学历：1-小学，2-初中，3-高中，4-大专，5-本科，6-硕士，7-博士
     */
    @Column(name = "education", columnDefinition = "TINYINT DEFAULT 0")
    private Integer education;

    /**
     * 年收入（万元）
     */
    @Column(name = "annual_income", columnDefinition = "DECIMAL(10,2) DEFAULT 0")
    private Double annualIncome;

    /**
     * 婚姻状况：1-未婚，2-已婚，3-离异，4-丧偶
     */
    @Column(name = "marital_status", columnDefinition = "TINYINT DEFAULT 0")
    private Integer maritalStatus;

    /**
     * 兴趣爱好（JSON格式）
     */
    @Column(name = "interests", length = 1000)
    private String interests;

    /**
     * 个性签名
     */
    @Column(name = "signature", length = 200)
    private String signature;

    /**
     * 实名认证状态：0-未认证，1-认证中，2-已认证，3-认证失败
     */
    @Column(name = "kyc_status", columnDefinition = "TINYINT DEFAULT 0")
    private Integer kycStatus;

    /**
     * 实名认证时间
     */
    @Column(name = "kyc_time")
    private LocalDateTime kycTime;

    /**
     * 认证失败原因
     */
    @Column(name = "kyc_fail_reason", length = 500)
    private String kycFailReason;

    /**
     * 隐私设置（JSON格式）
     */
    @Column(name = "privacy_settings", length = 1000)
    private String privacySettings;

    /**
     * 通知设置（JSON格式）
     */
    @Column(name = "notification_settings", length = 1000)
    private String notificationSettings;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    // 常量定义

    /**
     * 学历常量
     */
    public static class Education {
        public static final int UNKNOWN = 0;
        public static final int PRIMARY = 1;
        public static final int JUNIOR = 2;
        public static final int SENIOR = 3;
        public static final int COLLEGE = 4;
        public static final int BACHELOR = 5;
        public static final int MASTER = 6;
        public static final int DOCTOR = 7;
    }

    /**
     * 婚姻状况常量
     */
    public static class MaritalStatus {
        public static final int UNKNOWN = 0;
        public static final int SINGLE = 1;
        public static final int MARRIED = 2;
        public static final int DIVORCED = 3;
        public static final int WIDOWED = 4;
    }

    /**
     * KYC状态常量
     */
    public static class KycStatus {
        public static final int NOT_VERIFIED = 0;
        public static final int VERIFYING = 1;
        public static final int VERIFIED = 2;
        public static final int FAILED = 3;
    }
}
