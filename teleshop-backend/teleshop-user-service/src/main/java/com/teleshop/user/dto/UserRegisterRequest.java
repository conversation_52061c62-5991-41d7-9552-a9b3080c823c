package com.teleshop.user.dto;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * 用户注册请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserRegisterRequest {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    /**
     * 确认密码
     */
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 昵称
     */
    @Size(max = 100, message = "昵称长度不能超过100个字符")
    private String nickname;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @Size(min = 4, max = 6, message = "验证码长度不正确")
    private String verifyCode;

    /**
     * 验证码类型：1-短信，2-邮箱
     */
    @NotNull(message = "验证码类型不能为空")
    @Min(value = 1, message = "验证码类型不正确")
    @Max(value = 2, message = "验证码类型不正确")
    private Integer verifyType;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 注册来源
     */
    private String registerSource = "APP";

    /**
     * 用户协议同意标识
     */
    @NotNull(message = "必须同意用户协议")
    @AssertTrue(message = "必须同意用户协议")
    private Boolean agreeTerms;

    /**
     * 隐私政策同意标识
     */
    @NotNull(message = "必须同意隐私政策")
    @AssertTrue(message = "必须同意隐私政策")
    private Boolean agreePrivacy;
}
