package com.teleshop.user.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户登录响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserLoginResponse {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 手机号（脱敏）
     */
    private String phone;

    /**
     * 邮箱（脱敏）
     */
    private String email;

    /**
     * 用户状态
     */
    private Integer status;

    /**
     * 用户等级
     */
    private Integer level;

    /**
     * 积分
     */
    private Integer points;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 令牌过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 刷新令牌过期时间（秒）
     */
    private Long refreshExpiresIn;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 是否首次登录
     */
    private Boolean firstLogin;

    /**
     * 权限列表
     */
    private String[] permissions;

    /**
     * 角色列表
     */
    private String[] roles;
}
