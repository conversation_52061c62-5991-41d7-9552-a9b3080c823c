package com.teleshop.user.dto;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * 用户登录请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserLoginRequest {

    /**
     * 登录标识（用户名、手机号、邮箱）
     */
    @NotBlank(message = "登录标识不能为空")
    private String identifier;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 登录类型：1-用户名，2-手机号，3-邮箱
     */
    @NotNull(message = "登录类型不能为空")
    @Min(value = 1, message = "登录类型不正确")
    @Max(value = 3, message = "登录类型不正确")
    private Integer loginType;

    /**
     * 验证码（可选）
     */
    private String captcha;

    /**
     * 验证码ID（可选）
     */
    private String captchaId;

    /**
     * 记住我
     */
    private Boolean rememberMe = false;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 登录来源
     */
    private String loginSource = "APP";

    // 登录类型常量
    public static class LoginType {
        public static final int USERNAME = 1;
        public static final int PHONE = 2;
        public static final int EMAIL = 3;
    }
}
