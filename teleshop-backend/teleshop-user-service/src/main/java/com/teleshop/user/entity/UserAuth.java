package com.teleshop.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 用户认证实体类
 * 对应数据库表：t_user_auth
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_user_auth", indexes = {
    @Index(name = "idx_user_id", columnList = "user_id"),
    @Index(name = "idx_auth_type", columnList = "auth_type"),
    @Index(name = "idx_identifier", columnList = "identifier"),
    @Index(name = "idx_status", columnList = "status")
})
@EntityListeners(AuditingEntityListener.class)
@DynamicInsert
@DynamicUpdate
public class UserAuth {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 认证类型：1-用户名密码，2-手机号，3-邮箱，4-微信，5-QQ，6-支付宝
     */
    @Column(name = "auth_type", nullable = false)
    private Integer authType;

    /**
     * 认证标识（用户名、手机号、邮箱等）
     */
    @Column(name = "identifier", length = 100, nullable = false)
    private String identifier;

    /**
     * 认证凭证（密码、token等）
     */
    @Column(name = "credential", length = 500)
    private String credential;

    /**
     * 状态：1-正常，2-禁用
     */
    @Column(name = "status", columnDefinition = "TINYINT DEFAULT 1")
    private Integer status;

    /**
     * 是否已验证：0-未验证，1-已验证
     */
    @Column(name = "verified", columnDefinition = "TINYINT DEFAULT 0")
    private Integer verified;

    /**
     * 验证时间
     */
    @Column(name = "verified_time")
    private LocalDateTime verifiedTime;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_time")
    private LocalDateTime lastUsedTime;

    /**
     * 失败次数
     */
    @Column(name = "fail_count", columnDefinition = "INT DEFAULT 0")
    private Integer failCount;

    /**
     * 锁定时间
     */
    @Column(name = "locked_time")
    private LocalDateTime lockedTime;

    /**
     * 过期时间
     */
    @Column(name = "expire_time")
    private LocalDateTime expireTime;

    /**
     * 扩展信息（JSON格式）
     */
    @Column(name = "extra_info", length = 1000)
    private String extraInfo;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    // 常量定义

    /**
     * 认证类型常量
     */
    public static class AuthType {
        public static final int USERNAME_PASSWORD = 1;
        public static final int PHONE = 2;
        public static final int EMAIL = 3;
        public static final int WECHAT = 4;
        public static final int QQ = 5;
        public static final int ALIPAY = 6;
    }

    /**
     * 状态常量
     */
    public static class Status {
        public static final int NORMAL = 1;
        public static final int DISABLED = 2;
    }

    /**
     * 验证状态常量
     */
    public static class Verified {
        public static final int NO = 0;
        public static final int YES = 1;
    }
}
