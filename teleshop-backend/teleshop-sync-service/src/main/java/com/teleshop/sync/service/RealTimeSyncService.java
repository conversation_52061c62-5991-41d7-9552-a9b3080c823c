package com.teleshop.sync.service;

import com.teleshop.sync.entity.SyncEvent;
import com.teleshop.sync.entity.SyncRecord;
import com.teleshop.sync.repository.SyncEventRepository;
import com.teleshop.sync.repository.SyncRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 跨端数据实时同步服务
 * 实现库存、价格、促销、用户状态、订单状态的实时跨端同步
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RealTimeSyncService {
    
    private final SyncEventRepository syncEventRepository;
    private final SyncRecordRepository syncRecordRepository;
    private final RabbitTemplate rabbitTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final SimpMessagingTemplate messagingTemplate;
    private final InventoryService inventoryService;
    private final PriceService priceService;
    private final PromotionService promotionService;
    private final UserService userService;
    private final OrderService orderService;
    
    // Redis键前缀
    private static final String SYNC_LOCK_KEY = "sync_lock:";
    private static final String SYNC_STATUS_KEY = "sync_status:";
    private static final String SYNC_QUEUE_KEY = "sync_queue:";
    
    // 消息队列
    private static final String INVENTORY_SYNC_QUEUE = "teleshop.sync.inventory";
    private static final String PRICE_SYNC_QUEUE = "teleshop.sync.price";
    private static final String PROMOTION_SYNC_QUEUE = "teleshop.sync.promotion";
    private static final String USER_SYNC_QUEUE = "teleshop.sync.user";
    private static final String ORDER_SYNC_QUEUE = "teleshop.sync.order";
    
    // WebSocket主题
    private static final String INVENTORY_TOPIC = "/topic/inventory";
    private static final String PRICE_TOPIC = "/topic/price";
    private static final String PROMOTION_TOPIC = "/topic/promotion";
    private static final String USER_TOPIC = "/topic/user";
    private static final String ORDER_TOPIC = "/topic/order";
    
    /**
     * 同步库存数据
     */
    @Transactional
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void syncInventoryData(InventorySyncRequest request) {
        String syncId = generateSyncId("inventory", request.getProductId());
        String lockKey = SYNC_LOCK_KEY + syncId;
        
        try {
            // 获取分布式锁
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.warn("库存同步正在进行中，跳过: productId={}", request.getProductId());
                return;
            }
            
            log.info("开始同步库存数据: productId={}, stock={}", request.getProductId(), request.getStock());
            
            // 创建同步事件
            SyncEvent syncEvent = createSyncEvent(syncId, SyncEvent.SyncType.INVENTORY, request);
            syncEventRepository.save(syncEvent);
            
            // 获取当前库存信息
            InventoryInfo currentInventory = inventoryService.getInventoryInfo(request.getProductId());
            
            // 检查是否需要同步
            if (currentInventory != null && currentInventory.getStock().equals(request.getStock())) {
                log.info("库存数据无变化，跳过同步: productId={}", request.getProductId());
                return;
            }
            
            // 更新库存数据
            InventoryUpdateResult updateResult = inventoryService.updateInventory(
                request.getProductId(), request.getStock(), request.getWarehouseId());
            
            if (!updateResult.isSuccess()) {
                throw new RuntimeException("库存更新失败: " + updateResult.getErrorMessage());
            }
            
            // 创建同步记录
            List<SyncRecord> syncRecords = createInventorySyncRecords(syncId, request, updateResult);
            syncRecordRepository.saveAll(syncRecords);
            
            // 发送消息队列通知
            sendInventorySyncMessage(request, updateResult);
            
            // 发送WebSocket实时通知
            sendInventoryWebSocketNotification(request, updateResult);
            
            // 更新同步状态
            updateSyncStatus(syncId, SyncEvent.SyncStatus.COMPLETED);
            
            log.info("库存数据同步完成: productId={}, syncId={}", request.getProductId(), syncId);
            
        } catch (Exception e) {
            log.error("库存数据同步失败: productId={}, syncId={}", request.getProductId(), syncId, e);
            updateSyncStatus(syncId, SyncEvent.SyncStatus.FAILED);
            throw e;
        } finally {
            redisTemplate.delete(lockKey);
        }
    }
    
    /**
     * 同步价格数据
     */
    @Transactional
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void syncPriceData(PriceSyncRequest request) {
        String syncId = generateSyncId("price", request.getProductId());
        String lockKey = SYNC_LOCK_KEY + syncId;
        
        try {
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.warn("价格同步正在进行中，跳过: productId={}", request.getProductId());
                return;
            }
            
            log.info("开始同步价格数据: productId={}, price={}", request.getProductId(), request.getPrice());
            
            // 创建同步事件
            SyncEvent syncEvent = createSyncEvent(syncId, SyncEvent.SyncType.PRICE, request);
            syncEventRepository.save(syncEvent);
            
            // 获取当前价格信息
            PriceInfo currentPrice = priceService.getPriceInfo(request.getProductId());
            
            // 检查价格变化
            if (currentPrice != null && currentPrice.getPrice().equals(request.getPrice())) {
                log.info("价格数据无变化，跳过同步: productId={}", request.getProductId());
                return;
            }
            
            // 更新价格数据
            PriceUpdateResult updateResult = priceService.updatePrice(
                request.getProductId(), request.getPrice(), request.getEffectiveTime());
            
            if (!updateResult.isSuccess()) {
                throw new RuntimeException("价格更新失败: " + updateResult.getErrorMessage());
            }
            
            // 创建同步记录
            List<SyncRecord> syncRecords = createPriceSyncRecords(syncId, request, updateResult);
            syncRecordRepository.saveAll(syncRecords);
            
            // 发送消息队列通知
            sendPriceSyncMessage(request, updateResult);
            
            // 发送WebSocket实时通知
            sendPriceWebSocketNotification(request, updateResult);
            
            // 通知购物车价格变动
            notifyCartPriceChange(request.getProductId(), request.getPrice());
            
            // 更新同步状态
            updateSyncStatus(syncId, SyncEvent.SyncStatus.COMPLETED);
            
            log.info("价格数据同步完成: productId={}, syncId={}", request.getProductId(), syncId);
            
        } catch (Exception e) {
            log.error("价格数据同步失败: productId={}, syncId={}", request.getProductId(), syncId, e);
            updateSyncStatus(syncId, SyncEvent.SyncStatus.FAILED);
            throw e;
        } finally {
            redisTemplate.delete(lockKey);
        }
    }
    
    /**
     * 同步促销活动数据
     */
    @Transactional
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void syncPromotionData(PromotionSyncRequest request) {
        String syncId = generateSyncId("promotion", request.getPromotionId());
        String lockKey = SYNC_LOCK_KEY + syncId;
        
        try {
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.warn("促销同步正在进行中，跳过: promotionId={}", request.getPromotionId());
                return;
            }
            
            log.info("开始同步促销数据: promotionId={}, status={}", request.getPromotionId(), request.getStatus());
            
            // 创建同步事件
            SyncEvent syncEvent = createSyncEvent(syncId, SyncEvent.SyncType.PROMOTION, request);
            syncEventRepository.save(syncEvent);
            
            // 更新促销活动状态
            PromotionUpdateResult updateResult = promotionService.updatePromotionStatus(
                request.getPromotionId(), request.getStatus(), request.getUpdateTime());
            
            if (!updateResult.isSuccess()) {
                throw new RuntimeException("促销活动更新失败: " + updateResult.getErrorMessage());
            }
            
            // 创建同步记录
            List<SyncRecord> syncRecords = createPromotionSyncRecords(syncId, request, updateResult);
            syncRecordRepository.saveAll(syncRecords);
            
            // 发送消息队列通知
            sendPromotionSyncMessage(request, updateResult);
            
            // 发送WebSocket实时通知
            sendPromotionWebSocketNotification(request, updateResult);
            
            // 更新相关商品的促销状态
            updateProductPromotionStatus(request.getPromotionId(), request.getStatus());
            
            // 更新同步状态
            updateSyncStatus(syncId, SyncEvent.SyncStatus.COMPLETED);
            
            log.info("促销数据同步完成: promotionId={}, syncId={}", request.getPromotionId(), syncId);
            
        } catch (Exception e) {
            log.error("促销数据同步失败: promotionId={}, syncId={}", request.getPromotionId(), syncId, e);
            updateSyncStatus(syncId, SyncEvent.SyncStatus.FAILED);
            throw e;
        } finally {
            redisTemplate.delete(lockKey);
        }
    }
    
    /**
     * 同步用户状态数据
     */
    @Transactional
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void syncUserStatusData(UserStatusSyncRequest request) {
        String syncId = generateSyncId("user", request.getUserId());
        String lockKey = SYNC_LOCK_KEY + syncId;
        
        try {
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.warn("用户状态同步正在进行中，跳过: userId={}", request.getUserId());
                return;
            }
            
            log.info("开始同步用户状态: userId={}, status={}", request.getUserId(), request.getStatus());
            
            // 创建同步事件
            SyncEvent syncEvent = createSyncEvent(syncId, SyncEvent.SyncType.USER_STATUS, request);
            syncEventRepository.save(syncEvent);
            
            // 更新用户状态
            UserStatusUpdateResult updateResult = userService.updateUserStatus(
                request.getUserId(), request.getStatus(), request.getReason());
            
            if (!updateResult.isSuccess()) {
                throw new RuntimeException("用户状态更新失败: " + updateResult.getErrorMessage());
            }
            
            // 创建同步记录
            List<SyncRecord> syncRecords = createUserStatusSyncRecords(syncId, request, updateResult);
            syncRecordRepository.saveAll(syncRecords);
            
            // 发送消息队列通知
            sendUserStatusSyncMessage(request, updateResult);
            
            // 发送WebSocket实时通知
            sendUserStatusWebSocketNotification(request, updateResult);
            
            // 如果用户被禁用，处理相关业务
            if (request.getStatus() == UserStatus.DISABLED) {
                handleUserDisabled(request.getUserId());
            }
            
            // 更新同步状态
            updateSyncStatus(syncId, SyncEvent.SyncStatus.COMPLETED);
            
            log.info("用户状态同步完成: userId={}, syncId={}", request.getUserId(), syncId);
            
        } catch (Exception e) {
            log.error("用户状态同步失败: userId={}, syncId={}", request.getUserId(), syncId, e);
            updateSyncStatus(syncId, SyncEvent.SyncStatus.FAILED);
            throw e;
        } finally {
            redisTemplate.delete(lockKey);
        }
    }
    
    /**
     * 同步订单状态数据
     */
    @Transactional
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void syncOrderStatusData(OrderStatusSyncRequest request) {
        String syncId = generateSyncId("order", request.getOrderId());
        String lockKey = SYNC_LOCK_KEY + syncId;
        
        try {
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.warn("订单状态同步正在进行中，跳过: orderId={}", request.getOrderId());
                return;
            }
            
            log.info("开始同步订单状态: orderId={}, status={}", request.getOrderId(), request.getStatus());
            
            // 创建同步事件
            SyncEvent syncEvent = createSyncEvent(syncId, SyncEvent.SyncType.ORDER_STATUS, request);
            syncEventRepository.save(syncEvent);
            
            // 更新订单状态
            OrderStatusUpdateResult updateResult = orderService.updateOrderStatus(
                request.getOrderId(), request.getStatus(), request.getUpdateTime());
            
            if (!updateResult.isSuccess()) {
                throw new RuntimeException("订单状态更新失败: " + updateResult.getErrorMessage());
            }
            
            // 创建同步记录
            List<SyncRecord> syncRecords = createOrderStatusSyncRecords(syncId, request, updateResult);
            syncRecordRepository.saveAll(syncRecords);
            
            // 发送消息队列通知
            sendOrderStatusSyncMessage(request, updateResult);
            
            // 发送WebSocket实时通知
            sendOrderStatusWebSocketNotification(request, updateResult);
            
            // 根据订单状态执行相关业务逻辑
            handleOrderStatusChange(request.getOrderId(), request.getStatus());
            
            // 更新同步状态
            updateSyncStatus(syncId, SyncEvent.SyncStatus.COMPLETED);
            
            log.info("订单状态同步完成: orderId={}, syncId={}", request.getOrderId(), syncId);
            
        } catch (Exception e) {
            log.error("订单状态同步失败: orderId={}, syncId={}", request.getOrderId(), syncId, e);
            updateSyncStatus(syncId, SyncEvent.SyncStatus.FAILED);
            throw e;
        } finally {
            redisTemplate.delete(lockKey);
        }
    }
    
    /**
     * 批量同步数据
     */
    public void batchSyncData(BatchSyncRequest request) {
        log.info("开始批量同步数据: type={}, count={}", request.getSyncType(), request.getDataList().size());
        
        List<CompletableFuture<Void>> futures = request.getDataList().stream()
            .map(data -> CompletableFuture.runAsync(() -> {
                try {
                    switch (request.getSyncType()) {
                        case INVENTORY -> syncInventoryData((InventorySyncRequest) data);
                        case PRICE -> syncPriceData((PriceSyncRequest) data);
                        case PROMOTION -> syncPromotionData((PromotionSyncRequest) data);
                        case USER_STATUS -> syncUserStatusData((UserStatusSyncRequest) data);
                        case ORDER_STATUS -> syncOrderStatusData((OrderStatusSyncRequest) data);
                        default -> log.warn("不支持的同步类型: {}", request.getSyncType());
                    }
                } catch (Exception e) {
                    log.error("批量同步数据项失败: data={}", data, e);
                }
            }))
            .toList();
        
        // 等待所有同步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenRun(() -> log.info("批量同步数据完成: type={}", request.getSyncType()))
            .exceptionally(throwable -> {
                log.error("批量同步数据失败: type={}", request.getSyncType(), throwable);
                return null;
            });
    }
    
    // 辅助方法实现...
    private String generateSyncId(String type, Object identifier) {
        return String.format("%s_%s_%d", type, identifier, System.currentTimeMillis());
    }
    
    private SyncEvent createSyncEvent(String syncId, SyncEvent.SyncType syncType, Object request) {
        SyncEvent event = new SyncEvent();
        event.setSyncId(syncId);
        event.setSyncType(syncType);
        event.setRequestData(request.toString());
        event.setStatus(SyncEvent.SyncStatus.PROCESSING);
        event.setCreateTime(LocalDateTime.now());
        return event;
    }
    
    private void updateSyncStatus(String syncId, SyncEvent.SyncStatus status) {
        try {
            SyncEvent event = syncEventRepository.findBySyncId(syncId);
            if (event != null) {
                event.setStatus(status);
                event.setUpdateTime(LocalDateTime.now());
                syncEventRepository.save(event);
            }
        } catch (Exception e) {
            log.error("更新同步状态失败: syncId={}, status={}", syncId, status, e);
        }
    }
    
    private void sendInventorySyncMessage(InventorySyncRequest request, InventoryUpdateResult result) {
        Map<String, Object> message = Map.of(
            "type", "inventory_sync",
            "productId", request.getProductId(),
            "stock", request.getStock(),
            "warehouseId", request.getWarehouseId(),
            "timestamp", System.currentTimeMillis()
        );
        rabbitTemplate.convertAndSend(INVENTORY_SYNC_QUEUE, message);
    }
    
    private void sendInventoryWebSocketNotification(InventorySyncRequest request, InventoryUpdateResult result) {
        Map<String, Object> notification = Map.of(
            "productId", request.getProductId(),
            "stock", request.getStock(),
            "warehouseId", request.getWarehouseId(),
            "updateTime", LocalDateTime.now()
        );
        messagingTemplate.convertAndSend(INVENTORY_TOPIC, notification);
    }
    
    private void sendPriceSyncMessage(PriceSyncRequest request, PriceUpdateResult result) {
        Map<String, Object> message = Map.of(
            "type", "price_sync",
            "productId", request.getProductId(),
            "price", request.getPrice(),
            "effectiveTime", request.getEffectiveTime(),
            "timestamp", System.currentTimeMillis()
        );
        rabbitTemplate.convertAndSend(PRICE_SYNC_QUEUE, message);
    }
    
    private void sendPriceWebSocketNotification(PriceSyncRequest request, PriceUpdateResult result) {
        Map<String, Object> notification = Map.of(
            "productId", request.getProductId(),
            "price", request.getPrice(),
            "effectiveTime", request.getEffectiveTime(),
            "updateTime", LocalDateTime.now()
        );
        messagingTemplate.convertAndSend(PRICE_TOPIC, notification);
    }
    
    private void notifyCartPriceChange(Long productId, Double newPrice) {
        // 通知购物车价格变动
        Map<String, Object> cartNotification = Map.of(
            "type", "price_change",
            "productId", productId,
            "newPrice", newPrice,
            "timestamp", System.currentTimeMillis()
        );
        messagingTemplate.convertAndSend("/topic/cart/price-change", cartNotification);
    }
    
    private void sendPromotionSyncMessage(PromotionSyncRequest request, PromotionUpdateResult result) {
        Map<String, Object> message = Map.of(
            "type", "promotion_sync",
            "promotionId", request.getPromotionId(),
            "status", request.getStatus(),
            "updateTime", request.getUpdateTime(),
            "timestamp", System.currentTimeMillis()
        );
        rabbitTemplate.convertAndSend(PROMOTION_SYNC_QUEUE, message);
    }
    
    private void sendPromotionWebSocketNotification(PromotionSyncRequest request, PromotionUpdateResult result) {
        Map<String, Object> notification = Map.of(
            "promotionId", request.getPromotionId(),
            "status", request.getStatus(),
            "updateTime", request.getUpdateTime()
        );
        messagingTemplate.convertAndSend(PROMOTION_TOPIC, notification);
    }
    
    private void sendUserStatusSyncMessage(UserStatusSyncRequest request, UserStatusUpdateResult result) {
        Map<String, Object> message = Map.of(
            "type", "user_status_sync",
            "userId", request.getUserId(),
            "status", request.getStatus(),
            "reason", request.getReason(),
            "timestamp", System.currentTimeMillis()
        );
        rabbitTemplate.convertAndSend(USER_SYNC_QUEUE, message);
    }
    
    private void sendUserStatusWebSocketNotification(UserStatusSyncRequest request, UserStatusUpdateResult result) {
        Map<String, Object> notification = Map.of(
            "userId", request.getUserId(),
            "status", request.getStatus(),
            "reason", request.getReason(),
            "updateTime", LocalDateTime.now()
        );
        messagingTemplate.convertAndSend(USER_TOPIC, notification);
    }
    
    private void sendOrderStatusSyncMessage(OrderStatusSyncRequest request, OrderStatusUpdateResult result) {
        Map<String, Object> message = Map.of(
            "type", "order_status_sync",
            "orderId", request.getOrderId(),
            "status", request.getStatus(),
            "updateTime", request.getUpdateTime(),
            "timestamp", System.currentTimeMillis()
        );
        rabbitTemplate.convertAndSend(ORDER_SYNC_QUEUE, message);
    }
    
    private void sendOrderStatusWebSocketNotification(OrderStatusSyncRequest request, OrderStatusUpdateResult result) {
        Map<String, Object> notification = Map.of(
            "orderId", request.getOrderId(),
            "status", request.getStatus(),
            "updateTime", request.getUpdateTime()
        );
        messagingTemplate.convertAndSend(ORDER_TOPIC, notification);
    }
    
    // 其他辅助方法的简化实现
    private List<SyncRecord> createInventorySyncRecords(String syncId, InventorySyncRequest request, InventoryUpdateResult result) { return List.of(); }
    private List<SyncRecord> createPriceSyncRecords(String syncId, PriceSyncRequest request, PriceUpdateResult result) { return List.of(); }
    private List<SyncRecord> createPromotionSyncRecords(String syncId, PromotionSyncRequest request, PromotionUpdateResult result) { return List.of(); }
    private List<SyncRecord> createUserStatusSyncRecords(String syncId, UserStatusSyncRequest request, UserStatusUpdateResult result) { return List.of(); }
    private List<SyncRecord> createOrderStatusSyncRecords(String syncId, OrderStatusSyncRequest request, OrderStatusUpdateResult result) { return List.of(); }
    private void updateProductPromotionStatus(Long promotionId, String status) { /* 更新商品促销状态 */ }
    private void handleUserDisabled(Long userId) { /* 处理用户禁用相关业务 */ }
    private void handleOrderStatusChange(Long orderId, String status) { /* 处理订单状态变化相关业务 */ }
}
