package com.teleshop.monitoring.service;

import com.teleshop.monitoring.entity.MetricData;
import com.teleshop.monitoring.entity.AlertRule;
import com.teleshop.monitoring.entity.AlertRecord;
import com.teleshop.monitoring.repository.MetricDataRepository;
import com.teleshop.monitoring.repository.AlertRuleRepository;
import com.teleshop.monitoring.repository.AlertRecordRepository;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控服务
 * 提供系统性能指标收集、分析、告警功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PerformanceMonitoringService {
    
    private final MetricDataRepository metricDataRepository;
    private final AlertRuleRepository alertRuleRepository;
    private final AlertRecordRepository alertRecordRepository;
    private final MeterRegistry meterRegistry;
    private final RabbitTemplate rabbitTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final NotificationService notificationService;
    
    // 内存中的指标缓存
    private final Map<String, AtomicLong> counters = new ConcurrentHashMap<>();
    private final Map<String, Double> gauges = new ConcurrentHashMap<>();
    private final Map<String, Timer.Sample> timers = new ConcurrentHashMap<>();
    
    // Redis键前缀
    private static final String METRIC_KEY = "metric:";
    private static final String ALERT_KEY = "alert:";
    private static final String THRESHOLD_KEY = "threshold:";
    
    // 消息队列
    private static final String ALERT_QUEUE = "teleshop.alert.trigger";
    private static final String METRIC_QUEUE = "teleshop.metric.collect";
    
    /**
     * 记录业务指标
     */
    public void recordBusinessMetric(String metricName, double value, Map<String, String> tags) {
        try {
            // 记录到Micrometer
            Gauge.builder(metricName)
                .tags(tags)
                .register(meterRegistry, () -> value);
            
            // 缓存到Redis
            String key = METRIC_KEY + metricName;
            redisTemplate.opsForZSet().add(key, value, System.currentTimeMillis());
            redisTemplate.expire(key, 24, TimeUnit.HOURS);
            
            // 检查告警规则
            checkAlertRules(metricName, value, tags);
            
            log.debug("业务指标记录成功: metric={}, value={}, tags={}", metricName, value, tags);
            
        } catch (Exception e) {
            log.error("记录业务指标失败: metric={}, value={}", metricName, value, e);
        }
    }
    
    /**
     * 记录计数器指标
     */
    public void incrementCounter(String counterName, Map<String, String> tags) {
        try {
            // 更新内存计数器
            counters.computeIfAbsent(counterName, k -> new AtomicLong(0)).incrementAndGet();
            
            // 记录到Micrometer
            Counter.builder(counterName)
                .tags(tags)
                .register(meterRegistry)
                .increment();
            
            log.debug("计数器指标递增: counter={}, tags={}", counterName, tags);
            
        } catch (Exception e) {
            log.error("记录计数器指标失败: counter={}", counterName, e);
        }
    }
    
    /**
     * 开始计时
     */
    public String startTimer(String timerName) {
        try {
            String timerId = timerName + "_" + System.currentTimeMillis();
            Timer.Sample sample = Timer.start(meterRegistry);
            timers.put(timerId, sample);
            
            log.debug("计时器开始: timer={}, timerId={}", timerName, timerId);
            return timerId;
            
        } catch (Exception e) {
            log.error("启动计时器失败: timer={}", timerName, e);
            return null;
        }
    }
    
    /**
     * 结束计时
     */
    public void stopTimer(String timerId, String timerName, Map<String, String> tags) {
        try {
            Timer.Sample sample = timers.remove(timerId);
            if (sample != null) {
                Timer timer = Timer.builder(timerName)
                    .tags(tags)
                    .register(meterRegistry);
                
                long duration = sample.stop(timer);
                
                // 检查响应时间告警
                checkResponseTimeAlert(timerName, duration, tags);
                
                log.debug("计时器结束: timer={}, duration={}ms, tags={}", timerName, duration / 1_000_000, tags);
            }
            
        } catch (Exception e) {
            log.error("结束计时器失败: timerId={}, timer={}", timerId, timerName, e);
        }
    }
    
    /**
     * 收集系统指标
     */
    @Scheduled(fixedDelay = 60000) // 每分钟收集一次
    public void collectSystemMetrics() {
        try {
            // 收集JVM指标
            collectJvmMetrics();
            
            // 收集数据库连接池指标
            collectDatabaseMetrics();
            
            // 收集Redis连接指标
            collectRedisMetrics();
            
            // 收集业务指标
            collectBusinessMetrics();
            
            log.debug("系统指标收集完成");
            
        } catch (Exception e) {
            log.error("收集系统指标失败", e);
        }
    }
    
    /**
     * 收集JVM指标
     */
    private void collectJvmMetrics() {
        try {
            Runtime runtime = Runtime.getRuntime();
            
            // 内存使用情况
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
            
            recordBusinessMetric("jvm.memory.used", usedMemory, Map.of("type", "heap"));
            recordBusinessMetric("jvm.memory.usage.percent", memoryUsagePercent, Map.of("type", "heap"));
            
            // CPU使用率（简化实现）
            double cpuUsage = getCpuUsage();
            recordBusinessMetric("system.cpu.usage", cpuUsage, Map.of());
            
            // 线程数
            int threadCount = Thread.activeCount();
            recordBusinessMetric("jvm.threads.active", threadCount, Map.of());
            
        } catch (Exception e) {
            log.error("收集JVM指标失败", e);
        }
    }
    
    /**
     * 收集数据库指标
     */
    private void collectDatabaseMetrics() {
        try {
            // 数据库连接池指标（需要根据实际使用的连接池实现）
            DatabaseMetrics dbMetrics = getDatabaseMetrics();
            
            recordBusinessMetric("database.connections.active", dbMetrics.getActiveConnections(), 
                Map.of("database", "mysql"));
            recordBusinessMetric("database.connections.idle", dbMetrics.getIdleConnections(), 
                Map.of("database", "mysql"));
            recordBusinessMetric("database.connections.total", dbMetrics.getTotalConnections(), 
                Map.of("database", "mysql"));
            
            // 查询性能指标
            recordBusinessMetric("database.query.avg.time", dbMetrics.getAvgQueryTime(), 
                Map.of("database", "mysql"));
            recordBusinessMetric("database.query.slow.count", dbMetrics.getSlowQueryCount(), 
                Map.of("database", "mysql"));
            
        } catch (Exception e) {
            log.error("收集数据库指标失败", e);
        }
    }
    
    /**
     * 收集Redis指标
     */
    private void collectRedisMetrics() {
        try {
            RedisMetrics redisMetrics = getRedisMetrics();
            
            recordBusinessMetric("redis.connections.active", redisMetrics.getActiveConnections(), 
                Map.of("instance", "main"));
            recordBusinessMetric("redis.memory.used", redisMetrics.getUsedMemory(), 
                Map.of("instance", "main"));
            recordBusinessMetric("redis.commands.processed", redisMetrics.getCommandsProcessed(), 
                Map.of("instance", "main"));
            recordBusinessMetric("redis.hit.rate", redisMetrics.getHitRate(), 
                Map.of("instance", "main"));
            
        } catch (Exception e) {
            log.error("收集Redis指标失败", e);
        }
    }
    
    /**
     * 收集业务指标
     */
    private void collectBusinessMetrics() {
        try {
            BusinessMetrics businessMetrics = getBusinessMetrics();
            
            // 订单相关指标
            recordBusinessMetric("business.orders.total", businessMetrics.getTotalOrders(), 
                Map.of("period", "daily"));
            recordBusinessMetric("business.orders.success.rate", businessMetrics.getOrderSuccessRate(), 
                Map.of("period", "daily"));
            recordBusinessMetric("business.revenue.total", businessMetrics.getTotalRevenue(), 
                Map.of("period", "daily"));
            
            // 用户相关指标
            recordBusinessMetric("business.users.active", businessMetrics.getActiveUsers(), 
                Map.of("period", "daily"));
            recordBusinessMetric("business.users.new", businessMetrics.getNewUsers(), 
                Map.of("period", "daily"));
            
            // 系统性能指标
            recordBusinessMetric("business.api.response.time", businessMetrics.getAvgApiResponseTime(), 
                Map.of("endpoint", "all"));
            recordBusinessMetric("business.api.error.rate", businessMetrics.getApiErrorRate(), 
                Map.of("endpoint", "all"));
            
        } catch (Exception e) {
            log.error("收集业务指标失败", e);
        }
    }
    
    /**
     * 检查告警规则
     */
    private void checkAlertRules(String metricName, double value, Map<String, String> tags) {
        try {
            List<AlertRule> rules = alertRuleRepository.findByMetricNameAndEnabled(metricName, true);
            
            for (AlertRule rule : rules) {
                boolean triggered = evaluateAlertRule(rule, value, tags);
                if (triggered) {
                    triggerAlert(rule, value, tags);
                }
            }
            
        } catch (Exception e) {
            log.error("检查告警规则失败: metric={}, value={}", metricName, value, e);
        }
    }
    
    /**
     * 评估告警规则
     */
    private boolean evaluateAlertRule(AlertRule rule, double value, Map<String, String> tags) {
        try {
            // 检查标签匹配
            if (!isTagsMatch(rule.getTagFilters(), tags)) {
                return false;
            }
            
            // 检查阈值条件
            return switch (rule.getOperator()) {
                case GREATER_THAN -> value > rule.getThreshold();
                case LESS_THAN -> value < rule.getThreshold();
                case EQUALS -> Math.abs(value - rule.getThreshold()) < 0.001;
                case GREATER_THAN_OR_EQUAL -> value >= rule.getThreshold();
                case LESS_THAN_OR_EQUAL -> value <= rule.getThreshold();
                default -> false;
            };
            
        } catch (Exception e) {
            log.error("评估告警规则失败: ruleId={}", rule.getId(), e);
            return false;
        }
    }
    
    /**
     * 触发告警
     */
    private void triggerAlert(AlertRule rule, double value, Map<String, String> tags) {
        try {
            String alertKey = ALERT_KEY + rule.getId();
            
            // 检查告警频率限制
            if (isAlertRateLimited(alertKey, rule.getCooldownMinutes())) {
                return;
            }
            
            // 创建告警记录
            AlertRecord alertRecord = new AlertRecord();
            alertRecord.setRuleId(rule.getId());
            alertRecord.setMetricName(rule.getMetricName());
            alertRecord.setMetricValue(value);
            alertRecord.setThreshold(rule.getThreshold());
            alertRecord.setOperator(rule.getOperator());
            alertRecord.setTags(tags.toString());
            alertRecord.setAlertLevel(rule.getAlertLevel());
            alertRecord.setAlertMessage(buildAlertMessage(rule, value, tags));
            alertRecord.setTriggeredTime(LocalDateTime.now());
            alertRecord.setStatus(AlertRecord.AlertStatus.TRIGGERED);
            
            alertRecord = alertRecordRepository.save(alertRecord);
            
            // 发送告警通知
            sendAlertNotification(alertRecord);
            
            // 设置告警冷却时间
            setAlertCooldown(alertKey, rule.getCooldownMinutes());
            
            log.warn("告警触发: ruleId={}, metric={}, value={}, threshold={}", 
                rule.getId(), rule.getMetricName(), value, rule.getThreshold());
            
        } catch (Exception e) {
            log.error("触发告警失败: ruleId={}", rule.getId(), e);
        }
    }
    
    /**
     * 检查响应时间告警
     */
    private void checkResponseTimeAlert(String timerName, long durationNanos, Map<String, String> tags) {
        try {
            double durationMs = durationNanos / 1_000_000.0;
            
            // 检查响应时间告警规则
            List<AlertRule> rules = alertRuleRepository.findByMetricNameAndEnabled(
                "response.time." + timerName, true);
            
            for (AlertRule rule : rules) {
                if (durationMs > rule.getThreshold()) {
                    triggerAlert(rule, durationMs, tags);
                }
            }
            
        } catch (Exception e) {
            log.error("检查响应时间告警失败: timer={}, duration={}ns", timerName, durationNanos, e);
        }
    }
    
    /**
     * 发送告警通知
     */
    private void sendAlertNotification(AlertRecord alertRecord) {
        try {
            // 发送到告警队列
            rabbitTemplate.convertAndSend(ALERT_QUEUE, Map.of(
                "alertId", alertRecord.getId(),
                "level", alertRecord.getAlertLevel().name(),
                "message", alertRecord.getAlertMessage(),
                "metric", alertRecord.getMetricName(),
                "value", alertRecord.getMetricValue(),
                "threshold", alertRecord.getThreshold()
            ));
            
            // 根据告警级别发送不同类型的通知
            switch (alertRecord.getAlertLevel()) {
                case CRITICAL -> {
                    notificationService.sendSms("系统严重告警", alertRecord.getAlertMessage());
                    notificationService.sendEmail("系统严重告警", alertRecord.getAlertMessage());
                    notificationService.sendDingTalk("🚨 严重告警", alertRecord.getAlertMessage());
                }
                case HIGH -> {
                    notificationService.sendEmail("系统高级告警", alertRecord.getAlertMessage());
                    notificationService.sendDingTalk("⚠️ 高级告警", alertRecord.getAlertMessage());
                }
                case MEDIUM -> {
                    notificationService.sendDingTalk("⚡ 中级告警", alertRecord.getAlertMessage());
                }
                case LOW -> {
                    notificationService.sendDingTalk("ℹ️ 低级告警", alertRecord.getAlertMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("发送告警通知失败: alertId={}", alertRecord.getId(), e);
        }
    }
    
    /**
     * 生成告警消息
     */
    private String buildAlertMessage(AlertRule rule, double value, Map<String, String> tags) {
        return String.format("告警规则: %s\n指标: %s\n当前值: %.2f\n阈值: %.2f %s\n标签: %s\n时间: %s",
            rule.getRuleName(),
            rule.getMetricName(),
            value,
            rule.getThreshold(),
            rule.getOperator().getDescription(),
            tags,
            LocalDateTime.now()
        );
    }
    
    /**
     * 检查告警频率限制
     */
    private boolean isAlertRateLimited(String alertKey, int cooldownMinutes) {
        return redisTemplate.hasKey(alertKey);
    }
    
    /**
     * 设置告警冷却时间
     */
    private void setAlertCooldown(String alertKey, int cooldownMinutes) {
        redisTemplate.opsForValue().set(alertKey, "triggered", cooldownMinutes, TimeUnit.MINUTES);
    }
    
    /**
     * 检查标签匹配
     */
    private boolean isTagsMatch(String tagFilters, Map<String, String> tags) {
        if (tagFilters == null || tagFilters.isEmpty()) {
            return true;
        }
        
        // 简化的标签匹配实现
        // 实际应该支持更复杂的标签过滤语法
        return true;
    }
    
    // 辅助方法实现...
    private double getCpuUsage() {
        // 简化的CPU使用率获取
        return Math.random() * 100; // 实际应该使用系统API获取
    }
    
    private DatabaseMetrics getDatabaseMetrics() {
        // 获取数据库指标
        return new DatabaseMetrics(10, 5, 15, 50.0, 2);
    }
    
    private RedisMetrics getRedisMetrics() {
        // 获取Redis指标
        return new RedisMetrics(8, 1024 * 1024, 1000, 95.5);
    }
    
    private BusinessMetrics getBusinessMetrics() {
        // 获取业务指标
        return new BusinessMetrics(100, 98.5, 50000.0, 500, 20, 200.0, 1.5);
    }
    
    // 内部类定义
    private static class DatabaseMetrics {
        private final int activeConnections;
        private final int idleConnections;
        private final int totalConnections;
        private final double avgQueryTime;
        private final int slowQueryCount;
        
        public DatabaseMetrics(int activeConnections, int idleConnections, int totalConnections, 
                             double avgQueryTime, int slowQueryCount) {
            this.activeConnections = activeConnections;
            this.idleConnections = idleConnections;
            this.totalConnections = totalConnections;
            this.avgQueryTime = avgQueryTime;
            this.slowQueryCount = slowQueryCount;
        }
        
        // Getters
        public int getActiveConnections() { return activeConnections; }
        public int getIdleConnections() { return idleConnections; }
        public int getTotalConnections() { return totalConnections; }
        public double getAvgQueryTime() { return avgQueryTime; }
        public int getSlowQueryCount() { return slowQueryCount; }
    }
    
    private static class RedisMetrics {
        private final int activeConnections;
        private final long usedMemory;
        private final long commandsProcessed;
        private final double hitRate;
        
        public RedisMetrics(int activeConnections, long usedMemory, long commandsProcessed, double hitRate) {
            this.activeConnections = activeConnections;
            this.usedMemory = usedMemory;
            this.commandsProcessed = commandsProcessed;
            this.hitRate = hitRate;
        }
        
        // Getters
        public int getActiveConnections() { return activeConnections; }
        public long getUsedMemory() { return usedMemory; }
        public long getCommandsProcessed() { return commandsProcessed; }
        public double getHitRate() { return hitRate; }
    }
    
    private static class BusinessMetrics {
        private final int totalOrders;
        private final double orderSuccessRate;
        private final double totalRevenue;
        private final int activeUsers;
        private final int newUsers;
        private final double avgApiResponseTime;
        private final double apiErrorRate;
        
        public BusinessMetrics(int totalOrders, double orderSuccessRate, double totalRevenue,
                             int activeUsers, int newUsers, double avgApiResponseTime, double apiErrorRate) {
            this.totalOrders = totalOrders;
            this.orderSuccessRate = orderSuccessRate;
            this.totalRevenue = totalRevenue;
            this.activeUsers = activeUsers;
            this.newUsers = newUsers;
            this.avgApiResponseTime = avgApiResponseTime;
            this.apiErrorRate = apiErrorRate;
        }
        
        // Getters
        public int getTotalOrders() { return totalOrders; }
        public double getOrderSuccessRate() { return orderSuccessRate; }
        public double getTotalRevenue() { return totalRevenue; }
        public int getActiveUsers() { return activeUsers; }
        public int getNewUsers() { return newUsers; }
        public double getAvgApiResponseTime() { return avgApiResponseTime; }
        public double getApiErrorRate() { return apiErrorRate; }
    }
}
