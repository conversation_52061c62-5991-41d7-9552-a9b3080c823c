package com.teleshop.ai.service;

import com.teleshop.ai.entity.RiskEvent;
import com.teleshop.ai.entity.RiskRule;
import com.teleshop.ai.entity.RiskProfile;
import com.teleshop.ai.repository.RiskEventRepository;
import com.teleshop.ai.repository.RiskRuleRepository;
import com.teleshop.ai.repository.RiskProfileRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 实时风控服务
 * 提供实时风险识别、评估和控制功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RealTimeRiskControlService {
    
    private final RiskEventRepository riskEventRepository;
    private final RiskRuleRepository riskRuleRepository;
    private final RiskProfileRepository riskProfileRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final MachineLearningService mlService;
    private final DeviceFingerprintService deviceFingerprintService;
    private final GeolocationService geolocationService;
    private final BehaviorAnalysisService behaviorAnalysisService;
    
    // Redis键前缀
    private static final String RISK_SCORE_KEY = "risk_score:";
    private static final String RISK_COUNTER_KEY = "risk_counter:";
    private static final String BLACKLIST_KEY = "blacklist:";
    private static final String WHITELIST_KEY = "whitelist:";
    private static final String RISK_CACHE_KEY = "risk_cache:";
    
    /**
     * 实时风险评估
     */
    public RiskAssessmentResult assessRisk(RiskAssessmentRequest request) {
        try {
            log.debug("开始实时风险评估: userId={}, eventType={}", 
                request.getUserId(), request.getEventType());
            
            // 1. 快速黑白名单检查
            QuickCheckResult quickCheck = performQuickCheck(request);
            if (quickCheck.isBlocked()) {
                return createBlockedResult(quickCheck.getReason(), 1.0);
            }
            if (quickCheck.isWhitelisted()) {
                return createPassResult("白名单用户", 0.0);
            }
            
            // 2. 获取用户风险画像
            RiskProfile userProfile = getUserRiskProfile(request.getUserId());
            
            // 3. 设备指纹分析
            DeviceFingerprintResult deviceResult = analyzeDeviceFingerprint(request);
            
            // 4. 地理位置风险分析
            GeolocationRiskResult locationResult = analyzeGeolocationRisk(request);
            
            // 5. 行为模式分析
            BehaviorPatternResult behaviorResult = analyzeBehaviorPattern(request, userProfile);
            
            // 6. 规则引擎评估
            RuleEngineResult ruleResult = evaluateRiskRules(request, userProfile);
            
            // 7. 机器学习模型预测
            MLPredictionResult mlResult = performMLPrediction(request, userProfile);
            
            // 8. 综合风险评分
            double finalRiskScore = calculateFinalRiskScore(
                deviceResult, locationResult, behaviorResult, ruleResult, mlResult);
            
            // 9. 风险决策
            RiskDecision decision = makeRiskDecision(finalRiskScore, request.getEventType());
            
            // 10. 记录风险事件
            recordRiskEvent(request, finalRiskScore, decision);
            
            // 11. 更新用户风险画像
            updateUserRiskProfile(userProfile, finalRiskScore, decision);
            
            // 12. 缓存评估结果
            cacheAssessmentResult(request, finalRiskScore, decision);
            
            RiskAssessmentResult result = RiskAssessmentResult.builder()
                .userId(request.getUserId())
                .eventType(request.getEventType())
                .riskScore(finalRiskScore)
                .decision(decision)
                .riskFactors(buildRiskFactors(deviceResult, locationResult, behaviorResult, ruleResult, mlResult))
                .confidence(calculateConfidence(finalRiskScore))
                .timestamp(LocalDateTime.now())
                .build();
            
            log.info("实时风险评估完成: userId={}, riskScore={}, decision={}", 
                request.getUserId(), finalRiskScore, decision);
            
            return result;
            
        } catch (Exception e) {
            log.error("实时风险评估失败: userId={}", request.getUserId(), e);
            return createErrorResult("系统异常");
        }
    }
    
    /**
     * 快速黑白名单检查
     */
    private QuickCheckResult performQuickCheck(RiskAssessmentRequest request) {
        try {
            // 检查用户黑名单
            String userBlacklistKey = BLACKLIST_KEY + "user:" + request.getUserId();
            if (redisTemplate.hasKey(userBlacklistKey)) {
                return QuickCheckResult.blocked("用户在黑名单中");
            }
            
            // 检查IP黑名单
            String ipBlacklistKey = BLACKLIST_KEY + "ip:" + request.getClientIp();
            if (redisTemplate.hasKey(ipBlacklistKey)) {
                return QuickCheckResult.blocked("IP地址在黑名单中");
            }
            
            // 检查设备黑名单
            if (request.getDeviceId() != null) {
                String deviceBlacklistKey = BLACKLIST_KEY + "device:" + request.getDeviceId();
                if (redisTemplate.hasKey(deviceBlacklistKey)) {
                    return QuickCheckResult.blocked("设备在黑名单中");
                }
            }
            
            // 检查用户白名单
            String userWhitelistKey = WHITELIST_KEY + "user:" + request.getUserId();
            if (redisTemplate.hasKey(userWhitelistKey)) {
                return QuickCheckResult.whitelisted("用户在白名单中");
            }
            
            return QuickCheckResult.normal();
            
        } catch (Exception e) {
            log.error("快速检查失败", e);
            return QuickCheckResult.normal();
        }
    }
    
    /**
     * 设备指纹分析
     */
    private DeviceFingerprintResult analyzeDeviceFingerprint(RiskAssessmentRequest request) {
        try {
            DeviceFingerprintAnalysis analysis = deviceFingerprintService.analyzeFingerprint(
                request.getDeviceId(), request.getUserAgent(), request.getScreenResolution());
            
            double riskScore = 0.0;
            StringBuilder riskFactors = new StringBuilder();
            
            // 检查设备是否为模拟器
            if (analysis.isEmulator()) {
                riskScore += 30.0;
                riskFactors.append("设备疑似模拟器; ");
            }
            
            // 检查设备是否被多个账户使用
            if (analysis.getAssociatedUserCount() > 5) {
                riskScore += 25.0;
                riskFactors.append("设备关联多个账户; ");
            }
            
            // 检查设备指纹是否异常
            if (analysis.isFingerprintAbnormal()) {
                riskScore += 20.0;
                riskFactors.append("设备指纹异常; ");
            }
            
            return new DeviceFingerprintResult(riskScore, riskFactors.toString(), 0.3);
            
        } catch (Exception e) {
            log.error("设备指纹分析失败", e);
            return new DeviceFingerprintResult(0.0, "", 0.3);
        }
    }
    
    /**
     * 地理位置风险分析
     */
    private GeolocationRiskResult analyzeGeolocationRisk(RiskAssessmentRequest request) {
        try {
            GeolocationAnalysis analysis = geolocationService.analyzeLocation(
                request.getClientIp(), request.getLatitude(), request.getLongitude());
            
            double riskScore = 0.0;
            StringBuilder riskFactors = new StringBuilder();
            
            // 检查是否为高风险地区
            if (analysis.isHighRiskRegion()) {
                riskScore += 40.0;
                riskFactors.append("高风险地区; ");
            }
            
            // 检查位置是否异常变化
            if (analysis.isLocationJumpAbnormal()) {
                riskScore += 35.0;
                riskFactors.append("位置异常跳跃; ");
            }
            
            // 检查是否使用代理/VPN
            if (analysis.isUsingProxy()) {
                riskScore += 25.0;
                riskFactors.append("使用代理/VPN; ");
            }
            
            return new GeolocationRiskResult(riskScore, riskFactors.toString(), 0.25);
            
        } catch (Exception e) {
            log.error("地理位置风险分析失败", e);
            return new GeolocationRiskResult(0.0, "", 0.25);
        }
    }
    
    /**
     * 行为模式分析
     */
    private BehaviorPatternResult analyzeBehaviorPattern(RiskAssessmentRequest request, RiskProfile userProfile) {
        try {
            BehaviorAnalysis analysis = behaviorAnalysisService.analyzeBehavior(
                request.getUserId(), request.getEventType(), request.getEventData());
            
            double riskScore = 0.0;
            StringBuilder riskFactors = new StringBuilder();
            
            // 检查操作频率是否异常
            if (analysis.isFrequencyAbnormal()) {
                riskScore += 30.0;
                riskFactors.append("操作频率异常; ");
            }
            
            // 检查操作时间是否异常
            if (analysis.isTimingAbnormal()) {
                riskScore += 20.0;
                riskFactors.append("操作时间异常; ");
            }
            
            // 检查操作序列是否异常
            if (analysis.isSequenceAbnormal()) {
                riskScore += 25.0;
                riskFactors.append("操作序列异常; ");
            }
            
            // 检查与历史行为的偏差
            if (analysis.isDeviationFromHistory()) {
                riskScore += 20.0;
                riskFactors.append("偏离历史行为模式; ");
            }
            
            return new BehaviorPatternResult(riskScore, riskFactors.toString(), 0.25);
            
        } catch (Exception e) {
            log.error("行为模式分析失败", e);
            return new BehaviorPatternResult(0.0, "", 0.25);
        }
    }
    
    /**
     * 规则引擎评估
     */
    private RuleEngineResult evaluateRiskRules(RiskAssessmentRequest request, RiskProfile userProfile) {
        try {
            List<RiskRule> applicableRules = riskRuleRepository.findByEventTypeAndEnabledTrue(request.getEventType());
            
            double totalRiskScore = 0.0;
            StringBuilder triggeredRules = new StringBuilder();
            
            for (RiskRule rule : applicableRules) {
                RuleEvaluationResult evaluation = evaluateRule(rule, request, userProfile);
                if (evaluation.isTriggered()) {
                    totalRiskScore += evaluation.getRiskScore();
                    triggeredRules.append(rule.getRuleName()).append("; ");
                }
            }
            
            return new RuleEngineResult(totalRiskScore, triggeredRules.toString(), 0.15);
            
        } catch (Exception e) {
            log.error("规则引擎评估失败", e);
            return new RuleEngineResult(0.0, "", 0.15);
        }
    }
    
    /**
     * 机器学习模型预测
     */
    private MLPredictionResult performMLPrediction(RiskAssessmentRequest request, RiskProfile userProfile) {
        try {
            // 构建特征向量
            Map<String, Object> features = buildFeatureVector(request, userProfile);
            
            // 调用机器学习服务进行预测
            MLPrediction prediction = mlService.predictRisk(features);
            
            return new MLPredictionResult(
                prediction.getRiskScore() * 100, 
                prediction.getExplanation(), 
                0.05
            );
            
        } catch (Exception e) {
            log.error("机器学习预测失败", e);
            return new MLPredictionResult(0.0, "", 0.05);
        }
    }
    
    /**
     * 计算最终风险评分
     */
    private double calculateFinalRiskScore(DeviceFingerprintResult deviceResult,
                                         GeolocationRiskResult locationResult,
                                         BehaviorPatternResult behaviorResult,
                                         RuleEngineResult ruleResult,
                                         MLPredictionResult mlResult) {
        
        double weightedScore = 
            deviceResult.getRiskScore() * deviceResult.getWeight() +
            locationResult.getRiskScore() * locationResult.getWeight() +
            behaviorResult.getRiskScore() * behaviorResult.getWeight() +
            ruleResult.getRiskScore() * ruleResult.getWeight() +
            mlResult.getRiskScore() * mlResult.getWeight();
        
        // 确保分数在0-100范围内
        return Math.min(Math.max(weightedScore, 0.0), 100.0);
    }
    
    /**
     * 风险决策
     */
    private RiskDecision makeRiskDecision(double riskScore, RiskEvent.EventType eventType) {
        // 根据不同事件类型设置不同的阈值
        double blockThreshold = getBlockThreshold(eventType);
        double reviewThreshold = getReviewThreshold(eventType);
        
        if (riskScore >= blockThreshold) {
            return RiskDecision.BLOCK;
        } else if (riskScore >= reviewThreshold) {
            return RiskDecision.REVIEW;
        } else {
            return RiskDecision.PASS;
        }
    }
    
    /**
     * 记录风险事件
     */
    @Transactional
    private void recordRiskEvent(RiskAssessmentRequest request, double riskScore, RiskDecision decision) {
        try {
            RiskEvent event = new RiskEvent();
            event.setUserId(request.getUserId());
            event.setEventType(request.getEventType());
            event.setEventData(request.getEventData().toString());
            event.setRiskScore(riskScore);
            event.setDecision(decision);
            event.setClientIp(request.getClientIp());
            event.setDeviceId(request.getDeviceId());
            event.setUserAgent(request.getUserAgent());
            event.setTimestamp(LocalDateTime.now());
            
            riskEventRepository.save(event);
            
        } catch (Exception e) {
            log.error("记录风险事件失败", e);
        }
    }
    
    /**
     * 更新用户风险画像
     */
    @Transactional
    private void updateUserRiskProfile(RiskProfile profile, double riskScore, RiskDecision decision) {
        try {
            // 更新风险评分历史
            profile.updateRiskScore(riskScore);
            
            // 更新决策统计
            profile.updateDecisionStats(decision);
            
            // 更新最后活动时间
            profile.setLastActivityTime(LocalDateTime.now());
            
            riskProfileRepository.save(profile);
            
        } catch (Exception e) {
            log.error("更新用户风险画像失败", e);
        }
    }
    
    /**
     * 缓存评估结果
     */
    private void cacheAssessmentResult(RiskAssessmentRequest request, double riskScore, RiskDecision decision) {
        try {
            String cacheKey = RISK_CACHE_KEY + request.getUserId() + ":" + request.getEventType();
            Map<String, Object> result = Map.of(
                "riskScore", riskScore,
                "decision", decision.name(),
                "timestamp", LocalDateTime.now()
            );
            
            redisTemplate.opsForValue().set(cacheKey, result, 5, TimeUnit.MINUTES);
            
        } catch (Exception e) {
            log.error("缓存评估结果失败", e);
        }
    }
    
    /**
     * 获取用户风险画像
     */
    private RiskProfile getUserRiskProfile(Long userId) {
        return riskProfileRepository.findByUserId(userId)
            .orElseGet(() -> createDefaultRiskProfile(userId));
    }
    
    /**
     * 创建默认风险画像
     */
    private RiskProfile createDefaultRiskProfile(Long userId) {
        RiskProfile profile = new RiskProfile();
        profile.setUserId(userId);
        profile.setRiskLevel(RiskProfile.RiskLevel.LOW);
        profile.setCreatedTime(LocalDateTime.now());
        return riskProfileRepository.save(profile);
    }
    
    /**
     * 构建特征向量
     */
    private Map<String, Object> buildFeatureVector(RiskAssessmentRequest request, RiskProfile userProfile) {
        return Map.of(
            "userId", request.getUserId(),
            "eventType", request.getEventType().name(),
            "userRiskLevel", userProfile.getRiskLevel().name(),
            "accountAge", userProfile.getAccountAgeDays(),
            "transactionCount", userProfile.getTransactionCount(),
            "hour", LocalDateTime.now().getHour(),
            "dayOfWeek", LocalDateTime.now().getDayOfWeek().getValue()
        );
    }
    
    /**
     * 评估单个规则
     */
    private RuleEvaluationResult evaluateRule(RiskRule rule, RiskAssessmentRequest request, RiskProfile userProfile) {
        // 简化的规则评估逻辑
        boolean triggered = false;
        double riskScore = 0.0;
        
        // 这里应该实现具体的规则评估逻辑
        // 根据规则类型和条件进行评估
        
        return new RuleEvaluationResult(triggered, riskScore);
    }
    
    /**
     * 构建风险因子描述
     */
    private String buildRiskFactors(DeviceFingerprintResult deviceResult,
                                  GeolocationRiskResult locationResult,
                                  BehaviorPatternResult behaviorResult,
                                  RuleEngineResult ruleResult,
                                  MLPredictionResult mlResult) {
        StringBuilder factors = new StringBuilder();
        
        if (!deviceResult.getRiskFactors().isEmpty()) {
            factors.append("设备风险: ").append(deviceResult.getRiskFactors());
        }
        if (!locationResult.getRiskFactors().isEmpty()) {
            factors.append("位置风险: ").append(locationResult.getRiskFactors());
        }
        if (!behaviorResult.getRiskFactors().isEmpty()) {
            factors.append("行为风险: ").append(behaviorResult.getRiskFactors());
        }
        if (!ruleResult.getRiskFactors().isEmpty()) {
            factors.append("规则触发: ").append(ruleResult.getRiskFactors());
        }
        if (!mlResult.getRiskFactors().isEmpty()) {
            factors.append("模型预测: ").append(mlResult.getRiskFactors());
        }
        
        return factors.toString();
    }
    
    /**
     * 计算置信度
     */
    private double calculateConfidence(double riskScore) {
        // 基于风险分数计算置信度
        return Math.min(Math.abs(riskScore - 50) / 50.0, 1.0);
    }
    
    /**
     * 获取阻断阈值
     */
    private double getBlockThreshold(RiskEvent.EventType eventType) {
        return switch (eventType) {
            case LOGIN -> 80.0;
            case PAYMENT -> 70.0;
            case TRANSFER -> 65.0;
            case REGISTER -> 85.0;
            case ORDER -> 75.0;
            default -> 80.0;
        };
    }
    
    /**
     * 获取审核阈值
     */
    private double getReviewThreshold(RiskEvent.EventType eventType) {
        return switch (eventType) {
            case LOGIN -> 60.0;
            case PAYMENT -> 50.0;
            case TRANSFER -> 45.0;
            case REGISTER -> 65.0;
            case ORDER -> 55.0;
            default -> 60.0;
        };
    }
    
    // 创建结果对象的辅助方法
    private RiskAssessmentResult createBlockedResult(String reason, double confidence) {
        return RiskAssessmentResult.builder()
            .decision(RiskDecision.BLOCK)
            .riskScore(100.0)
            .confidence(confidence)
            .riskFactors(reason)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    private RiskAssessmentResult createPassResult(String reason, double riskScore) {
        return RiskAssessmentResult.builder()
            .decision(RiskDecision.PASS)
            .riskScore(riskScore)
            .confidence(1.0)
            .riskFactors(reason)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    private RiskAssessmentResult createErrorResult(String errorMessage) {
        return RiskAssessmentResult.builder()
            .decision(RiskDecision.PASS) // 系统异常时默认通过
            .riskScore(0.0)
            .confidence(0.0)
            .riskFactors(errorMessage)
            .timestamp(LocalDateTime.now())
            .build();
    }
}
