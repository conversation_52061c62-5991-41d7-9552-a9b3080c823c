package com.teleshop.ai.service;

import com.teleshop.ai.entity.ReviewTask;
import com.teleshop.ai.entity.ReviewRule;
import com.teleshop.ai.entity.ReviewResult;
import com.teleshop.ai.repository.ReviewTaskRepository;
import com.teleshop.ai.repository.ReviewRuleRepository;
import com.teleshop.ai.repository.ReviewResultRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 自动化审核服务
 * 提供商品、内容、用户行为等多维度自动化审核功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AutomatedReviewService {
    
    private final ReviewTaskRepository reviewTaskRepository;
    private final ReviewRuleRepository reviewRuleRepository;
    private final ReviewResultRepository reviewResultRepository;
    private final RabbitTemplate rabbitTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ContentModerationService contentModerationService;
    private final RiskAssessmentService riskAssessmentService;
    private final ImageRecognitionService imageRecognitionService;
    private final TextAnalysisService textAnalysisService;
    
    // Redis键前缀
    private static final String REVIEW_CACHE_KEY = "review_cache:";
    private static final String REVIEW_STATS_KEY = "review_stats:";
    private static final String BLACKLIST_KEY = "blacklist:";
    
    // 消息队列
    private static final String REVIEW_QUEUE = "teleshop.review.task";
    private static final String REVIEW_RESULT_QUEUE = "teleshop.review.result";
    
    /**
     * 提交审核任务
     */
    @Transactional
    public ReviewTask submitReviewTask(ReviewTaskRequest request) {
        try {
            // 创建审核任务
            ReviewTask task = createReviewTask(request);
            task = reviewTaskRepository.save(task);
            
            // 发送到审核队列
            rabbitTemplate.convertAndSend(REVIEW_QUEUE, task);
            
            log.info("审核任务提交成功: taskId={}, type={}, targetId={}", 
                task.getId(), task.getReviewType(), task.getTargetId());
            
            return task;
            
        } catch (Exception e) {
            log.error("提交审核任务失败: request={}", request, e);
            throw new RuntimeException("提交审核任务失败", e);
        }
    }
    
    /**
     * 执行自动化审核
     */
    @Transactional
    public ReviewResult executeAutomatedReview(ReviewTask task) {
        try {
            log.info("开始执行自动化审核: taskId={}, type={}", task.getId(), task.getReviewType());
            
            // 检查缓存
            String cacheKey = REVIEW_CACHE_KEY + task.getReviewType() + ":" + task.getTargetId();
            ReviewResult cachedResult = (ReviewResult) redisTemplate.opsForValue().get(cacheKey);
            if (cachedResult != null && !isResultExpired(cachedResult)) {
                return cachedResult;
            }
            
            // 获取适用的审核规则
            List<ReviewRule> applicableRules = getApplicableRules(task.getReviewType());
            
            // 执行多维度审核
            ReviewResult result = performMultiDimensionalReview(task, applicableRules);
            
            // 保存审核结果
            result = reviewResultRepository.save(result);
            
            // 缓存结果
            redisTemplate.opsForValue().set(cacheKey, result, 24, TimeUnit.HOURS);
            
            // 更新任务状态
            updateTaskStatus(task, result);
            
            // 发送审核结果通知
            notifyReviewResult(result);
            
            // 更新统计信息
            updateReviewStats(result);
            
            log.info("自动化审核完成: taskId={}, result={}, confidence={}", 
                task.getId(), result.getDecision(), result.getConfidence());
            
            return result;
            
        } catch (Exception e) {
            log.error("执行自动化审核失败: taskId={}", task.getId(), e);
            
            // 创建失败结果
            ReviewResult failureResult = createFailureResult(task, e.getMessage());
            reviewResultRepository.save(failureResult);
            
            return failureResult;
        }
    }
    
    /**
     * 执行多维度审核
     */
    private ReviewResult performMultiDimensionalReview(ReviewTask task, List<ReviewRule> rules) {
        ReviewResult.Builder resultBuilder = ReviewResult.builder()
            .taskId(task.getId())
            .reviewType(task.getReviewType())
            .targetId(task.getTargetId())
            .startTime(LocalDateTime.now());
        
        double totalScore = 0.0;
        double totalWeight = 0.0;
        StringBuilder riskFactors = new StringBuilder();
        
        // 根据审核类型执行不同的审核逻辑
        switch (task.getReviewType()) {
            case PRODUCT_REVIEW -> {
                ProductReviewResult productResult = reviewProduct(task);
                totalScore += productResult.getScore() * productResult.getWeight();
                totalWeight += productResult.getWeight();
                riskFactors.append(productResult.getRiskFactors());
            }
            case CONTENT_REVIEW -> {
                ContentReviewResult contentResult = reviewContent(task);
                totalScore += contentResult.getScore() * contentResult.getWeight();
                totalWeight += contentResult.getWeight();
                riskFactors.append(contentResult.getRiskFactors());
            }
            case USER_BEHAVIOR_REVIEW -> {
                UserBehaviorReviewResult behaviorResult = reviewUserBehavior(task);
                totalScore += behaviorResult.getScore() * behaviorResult.getWeight();
                totalWeight += behaviorResult.getWeight();
                riskFactors.append(behaviorResult.getRiskFactors());
            }
            case TRANSACTION_REVIEW -> {
                TransactionReviewResult transactionResult = reviewTransaction(task);
                totalScore += transactionResult.getScore() * transactionResult.getWeight();
                totalWeight += transactionResult.getWeight();
                riskFactors.append(transactionResult.getRiskFactors());
            }
        }
        
        // 应用审核规则
        for (ReviewRule rule : rules) {
            RuleEvaluationResult ruleResult = evaluateRule(rule, task);
            if (ruleResult.isTriggered()) {
                totalScore += ruleResult.getScore() * rule.getWeight();
                totalWeight += rule.getWeight();
                riskFactors.append(ruleResult.getDescription()).append("; ");
            }
        }
        
        // 计算最终分数和决策
        double finalScore = totalWeight > 0 ? totalScore / totalWeight : 0.0;
        ReviewDecision decision = determineDecision(finalScore, task.getReviewType());
        
        return resultBuilder
            .score(finalScore)
            .confidence(calculateConfidence(finalScore, totalWeight))
            .decision(decision)
            .riskFactors(riskFactors.toString())
            .endTime(LocalDateTime.now())
            .build();
    }
    
    /**
     * 商品审核
     */
    private ProductReviewResult reviewProduct(ReviewTask task) {
        try {
            Map<String, Object> productData = getProductData(task.getTargetId());
            
            double score = 100.0; // 基础分数
            StringBuilder riskFactors = new StringBuilder();
            
            // 1. 商品信息完整性检查
            if (!isProductInfoComplete(productData)) {
                score -= 20;
                riskFactors.append("商品信息不完整; ");
            }
            
            // 2. 商品图片审核
            List<String> imageUrls = getProductImages(productData);
            for (String imageUrl : imageUrls) {
                ImageModerationResult imageResult = imageRecognitionService.moderateImage(imageUrl);
                if (!imageResult.isApproved()) {
                    score -= 30;
                    riskFactors.append("商品图片违规: ").append(imageResult.getReason()).append("; ");
                }
            }
            
            // 3. 商品标题和描述审核
            String title = (String) productData.get("title");
            String description = (String) productData.get("description");
            
            TextModerationResult titleResult = textAnalysisService.moderateText(title);
            if (!titleResult.isApproved()) {
                score -= 25;
                riskFactors.append("商品标题违规: ").append(titleResult.getReason()).append("; ");
            }
            
            TextModerationResult descResult = textAnalysisService.moderateText(description);
            if (!descResult.isApproved()) {
                score -= 25;
                riskFactors.append("商品描述违规: ").append(descResult.getReason()).append("; ");
            }
            
            // 4. 价格合理性检查
            Double price = (Double) productData.get("price");
            if (isPriceUnreasonable(price, productData)) {
                score -= 15;
                riskFactors.append("商品价格异常; ");
            }
            
            // 5. 商家信誉检查
            Long merchantId = (Long) productData.get("merchantId");
            if (isMerchantBlacklisted(merchantId)) {
                score -= 50;
                riskFactors.append("商家信誉不良; ");
            }
            
            return new ProductReviewResult(Math.max(0, score), 1.0, riskFactors.toString());
            
        } catch (Exception e) {
            log.error("商品审核失败: targetId={}", task.getTargetId(), e);
            return new ProductReviewResult(0.0, 1.0, "审核系统异常");
        }
    }
    
    /**
     * 内容审核
     */
    private ContentReviewResult reviewContent(ReviewTask task) {
        try {
            Map<String, Object> contentData = getContentData(task.getTargetId());
            
            double score = 100.0;
            StringBuilder riskFactors = new StringBuilder();
            
            // 1. 文本内容审核
            String content = (String) contentData.get("content");
            TextModerationResult textResult = contentModerationService.moderateText(content);
            
            if (!textResult.isApproved()) {
                score -= textResult.getSeverity() * 20;
                riskFactors.append("文本内容违规: ").append(textResult.getReason()).append("; ");
            }
            
            // 2. 图片内容审核
            List<String> imageUrls = (List<String>) contentData.get("images");
            if (imageUrls != null) {
                for (String imageUrl : imageUrls) {
                    ImageModerationResult imageResult = contentModerationService.moderateImage(imageUrl);
                    if (!imageResult.isApproved()) {
                        score -= imageResult.getSeverity() * 25;
                        riskFactors.append("图片内容违规: ").append(imageResult.getReason()).append("; ");
                    }
                }
            }
            
            // 3. 敏感词检测
            List<String> sensitiveWords = textAnalysisService.detectSensitiveWords(content);
            if (!sensitiveWords.isEmpty()) {
                score -= sensitiveWords.size() * 10;
                riskFactors.append("包含敏感词: ").append(String.join(",", sensitiveWords)).append("; ");
            }
            
            // 4. 垃圾内容检测
            if (textAnalysisService.isSpamContent(content)) {
                score -= 40;
                riskFactors.append("疑似垃圾内容; ");
            }
            
            return new ContentReviewResult(Math.max(0, score), 1.0, riskFactors.toString());
            
        } catch (Exception e) {
            log.error("内容审核失败: targetId={}", task.getTargetId(), e);
            return new ContentReviewResult(0.0, 1.0, "审核系统异常");
        }
    }
    
    /**
     * 用户行为审核
     */
    private UserBehaviorReviewResult reviewUserBehavior(ReviewTask task) {
        try {
            Map<String, Object> behaviorData = getUserBehaviorData(task.getTargetId());
            
            double score = 100.0;
            StringBuilder riskFactors = new StringBuilder();
            
            // 1. 异常登录检测
            if (hasAbnormalLogin(behaviorData)) {
                score -= 30;
                riskFactors.append("异常登录行为; ");
            }
            
            // 2. 频繁操作检测
            if (hasFrequentOperations(behaviorData)) {
                score -= 25;
                riskFactors.append("频繁操作行为; ");
            }
            
            // 3. 恶意评价检测
            if (hasMaliciousReviews(behaviorData)) {
                score -= 35;
                riskFactors.append("恶意评价行为; ");
            }
            
            // 4. 虚假交易检测
            if (hasFakeTransactions(behaviorData)) {
                score -= 50;
                riskFactors.append("虚假交易行为; ");
            }
            
            return new UserBehaviorReviewResult(Math.max(0, score), 1.0, riskFactors.toString());
            
        } catch (Exception e) {
            log.error("用户行为审核失败: targetId={}", task.getTargetId(), e);
            return new UserBehaviorReviewResult(0.0, 1.0, "审核系统异常");
        }
    }
    
    /**
     * 交易审核
     */
    private TransactionReviewResult reviewTransaction(ReviewTask task) {
        try {
            Map<String, Object> transactionData = getTransactionData(task.getTargetId());
            
            double score = 100.0;
            StringBuilder riskFactors = new StringBuilder();
            
            // 1. 交易金额异常检测
            Double amount = (Double) transactionData.get("amount");
            if (isAmountAbnormal(amount, transactionData)) {
                score -= 40;
                riskFactors.append("交易金额异常; ");
            }
            
            // 2. 交易频率检测
            if (isTransactionFrequencyAbnormal(transactionData)) {
                score -= 30;
                riskFactors.append("交易频率异常; ");
            }
            
            // 3. 支付方式风险检测
            String paymentMethod = (String) transactionData.get("paymentMethod");
            if (isPaymentMethodRisky(paymentMethod)) {
                score -= 20;
                riskFactors.append("支付方式风险; ");
            }
            
            // 4. 地理位置异常检测
            if (isLocationAbnormal(transactionData)) {
                score -= 25;
                riskFactors.append("地理位置异常; ");
            }
            
            return new TransactionReviewResult(Math.max(0, score), 1.0, riskFactors.toString());
            
        } catch (Exception e) {
            log.error("交易审核失败: targetId={}", task.getTargetId(), e);
            return new TransactionReviewResult(0.0, 1.0, "审核系统异常");
        }
    }
    
    /**
     * 评估审核规则
     */
    private RuleEvaluationResult evaluateRule(ReviewRule rule, ReviewTask task) {
        try {
            // 根据规则类型执行不同的评估逻辑
            boolean triggered = false;
            String description = "";
            double score = 0.0;
            
            switch (rule.getRuleType()) {
                case KEYWORD_FILTER -> {
                    triggered = evaluateKeywordRule(rule, task);
                    if (triggered) {
                        score = rule.getScore();
                        description = "触发关键词过滤规则: " + rule.getRuleName();
                    }
                }
                case FREQUENCY_LIMIT -> {
                    triggered = evaluateFrequencyRule(rule, task);
                    if (triggered) {
                        score = rule.getScore();
                        description = "触发频率限制规则: " + rule.getRuleName();
                    }
                }
                case BLACKLIST_CHECK -> {
                    triggered = evaluateBlacklistRule(rule, task);
                    if (triggered) {
                        score = rule.getScore();
                        description = "触发黑名单检查规则: " + rule.getRuleName();
                    }
                }
                case RISK_THRESHOLD -> {
                    triggered = evaluateRiskThresholdRule(rule, task);
                    if (triggered) {
                        score = rule.getScore();
                        description = "触发风险阈值规则: " + rule.getRuleName();
                    }
                }
            }
            
            return new RuleEvaluationResult(triggered, score, description);
            
        } catch (Exception e) {
            log.error("评估审核规则失败: ruleId={}", rule.getId(), e);
            return new RuleEvaluationResult(false, 0.0, "规则评估异常");
        }
    }
    
    /**
     * 确定审核决策
     */
    private ReviewDecision determineDecision(double score, ReviewTask.ReviewType reviewType) {
        // 根据不同审核类型设置不同的阈值
        double approveThreshold = getApproveThreshold(reviewType);
        double rejectThreshold = getRejectThreshold(reviewType);
        
        if (score >= approveThreshold) {
            return ReviewDecision.APPROVED;
        } else if (score <= rejectThreshold) {
            return ReviewDecision.REJECTED;
        } else {
            return ReviewDecision.MANUAL_REVIEW;
        }
    }
    
    /**
     * 计算置信度
     */
    private double calculateConfidence(double score, double totalWeight) {
        // 基于分数和权重计算置信度
        double baseConfidence = Math.abs(score - 50) / 50.0; // 距离中间值越远置信度越高
        double weightFactor = Math.min(totalWeight / 10.0, 1.0); // 权重因子
        return Math.min(baseConfidence * weightFactor, 1.0);
    }
    
    // 辅助方法实现...
    private ReviewTask createReviewTask(ReviewTaskRequest request) {
        ReviewTask task = new ReviewTask();
        task.setReviewType(request.getReviewType());
        task.setTargetId(request.getTargetId());
        task.setTargetType(request.getTargetType());
        task.setPriority(request.getPriority());
        task.setStatus(ReviewTask.TaskStatus.PENDING);
        task.setSubmitterId(request.getSubmitterId());
        task.setSubmitTime(LocalDateTime.now());
        return task;
    }
    
    private List<ReviewRule> getApplicableRules(ReviewTask.ReviewType reviewType) {
        return reviewRuleRepository.findByReviewTypeAndEnabledTrue(reviewType);
    }
    
    private void updateTaskStatus(ReviewTask task, ReviewResult result) {
        task.setStatus(ReviewTask.TaskStatus.COMPLETED);
        task.setCompletedTime(LocalDateTime.now());
        reviewTaskRepository.save(task);
    }
    
    private void notifyReviewResult(ReviewResult result) {
        rabbitTemplate.convertAndSend(REVIEW_RESULT_QUEUE, result);
    }
    
    private void updateReviewStats(ReviewResult result) {
        String statsKey = REVIEW_STATS_KEY + result.getReviewType().name();
        redisTemplate.opsForHash().increment(statsKey, "total", 1);
        redisTemplate.opsForHash().increment(statsKey, result.getDecision().name().toLowerCase(), 1);
    }
    
    private ReviewResult createFailureResult(ReviewTask task, String errorMessage) {
        return ReviewResult.builder()
            .taskId(task.getId())
            .reviewType(task.getReviewType())
            .targetId(task.getTargetId())
            .decision(ReviewDecision.SYSTEM_ERROR)
            .score(0.0)
            .confidence(0.0)
            .riskFactors(errorMessage)
            .startTime(LocalDateTime.now())
            .endTime(LocalDateTime.now())
            .build();
    }
    
    // 其他辅助方法的简化实现...
    private boolean isResultExpired(ReviewResult result) {
        return result.getEndTime().isBefore(LocalDateTime.now().minusHours(24));
    }
    
    private Map<String, Object> getProductData(String targetId) {
        // 调用商品服务获取商品数据
        return Map.of();
    }
    
    private Map<String, Object> getContentData(String targetId) {
        // 调用内容服务获取内容数据
        return Map.of();
    }
    
    private Map<String, Object> getUserBehaviorData(String targetId) {
        // 调用用户服务获取行为数据
        return Map.of();
    }
    
    private Map<String, Object> getTransactionData(String targetId) {
        // 调用交易服务获取交易数据
        return Map.of();
    }
    
    private double getApproveThreshold(ReviewTask.ReviewType reviewType) {
        return switch (reviewType) {
            case PRODUCT_REVIEW -> 80.0;
            case CONTENT_REVIEW -> 85.0;
            case USER_BEHAVIOR_REVIEW -> 75.0;
            case TRANSACTION_REVIEW -> 70.0;
        };
    }
    
    private double getRejectThreshold(ReviewTask.ReviewType reviewType) {
        return switch (reviewType) {
            case PRODUCT_REVIEW -> 40.0;
            case CONTENT_REVIEW -> 30.0;
            case USER_BEHAVIOR_REVIEW -> 35.0;
            case TRANSACTION_REVIEW -> 25.0;
        };
    }
    
    // 其他检测方法的简化实现...
    private boolean isProductInfoComplete(Map<String, Object> productData) { return true; }
    private List<String> getProductImages(Map<String, Object> productData) { return List.of(); }
    private boolean isPriceUnreasonable(Double price, Map<String, Object> productData) { return false; }
    private boolean isMerchantBlacklisted(Long merchantId) { return false; }
    private boolean hasAbnormalLogin(Map<String, Object> behaviorData) { return false; }
    private boolean hasFrequentOperations(Map<String, Object> behaviorData) { return false; }
    private boolean hasMaliciousReviews(Map<String, Object> behaviorData) { return false; }
    private boolean hasFakeTransactions(Map<String, Object> behaviorData) { return false; }
    private boolean isAmountAbnormal(Double amount, Map<String, Object> transactionData) { return false; }
    private boolean isTransactionFrequencyAbnormal(Map<String, Object> transactionData) { return false; }
    private boolean isPaymentMethodRisky(String paymentMethod) { return false; }
    private boolean isLocationAbnormal(Map<String, Object> transactionData) { return false; }
    private boolean evaluateKeywordRule(ReviewRule rule, ReviewTask task) { return false; }
    private boolean evaluateFrequencyRule(ReviewRule rule, ReviewTask task) { return false; }
    private boolean evaluateBlacklistRule(ReviewRule rule, ReviewTask task) { return false; }
    private boolean evaluateRiskThresholdRule(ReviewRule rule, ReviewTask task) { return false; }
}
