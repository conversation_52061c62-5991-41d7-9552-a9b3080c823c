# TeleShop 认证服务配置
server:
  port: 8081
  servlet:
    context-path: /auth-service

spring:
  application:
    name: teleshop-auth-service
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************
    username: ${DB_USERNAME:teleshop}
    password: ${DB_PASSWORD:teleshop123}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
      pool-name: TeleShopAuthHikariCP
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms
  
  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# TeleShop认证配置
teleshop:
  jwt:
    secret: ${JWT_SECRET:TeleShop2024SecretKeyForJWTTokenGenerationAndValidation}
    access-token-expiration: ${JWT_ACCESS_TOKEN_EXPIRATION:3600}  # 1小时
    refresh-token-expiration: ${JWT_REFRESH_TOKEN_EXPIRATION:604800}  # 7天
    issuer: ${JWT_ISSUER:teleshop-auth-service}
  
  # 安全配置
  security:
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-digit: true
      require-special-char: false
    
    # 登录限制
    login:
      max-attempts: 5
      lockout-duration: 300  # 5分钟
      captcha-threshold: 3
    
    # 会话配置
    session:
      max-concurrent-sessions: 3
      prevent-login-if-maximum-exceeded: false
  
  # 验证码配置
  captcha:
    enabled: ${CAPTCHA_ENABLED:true}
    length: 4
    width: 120
    height: 40
    expiration: 300  # 5分钟

# 日志配置
logging:
  level:
    com.teleshop.auth: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/teleshop-auth-service.log
    max-size: 100MB
    max-history: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# Swagger文档配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: TeleShop 认证服务 API
    description: TeleShop统一身份认证服务接口文档
    version: 1.0.0
    contact:
      name: TeleShop开发团队
      email: <EMAIL>

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: ***********************************************************************************************************************************************************
  
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create-drop

logging:
  level:
    root: INFO
    com.teleshop.auth: DEBUG

teleshop:
  security:
    login:
      max-attempts: 10  # 开发环境放宽限制
  captcha:
    enabled: false  # 开发环境禁用验证码

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: **********************************************************************************************************************************************************
  
  data:
    redis:
      host: test-redis
      port: 6379

teleshop:
  jwt:
    access-token-expiration: 7200  # 测试环境延长到2小时

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: jdbc:mysql://${DB_HOST}:${DB_PORT}/teleshop_auth?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai
    hikari:
      maximum-pool-size: 50
  
  data:
    redis:
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
      password: ${REDIS_PASSWORD}
      ssl: true

logging:
  level:
    root: WARN
    com.teleshop.auth: INFO
  file:
    name: /var/log/teleshop/auth-service.log

management:
  endpoint:
    health:
      show-details: never
