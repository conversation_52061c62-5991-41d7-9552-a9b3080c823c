package com.teleshop.auth.service;

import com.teleshop.auth.dto.*;
import com.teleshop.auth.entity.User;
import com.teleshop.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 认证服务
 * 处理用户认证、令牌验证等核心业务逻辑
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthenticationService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenService jwtTokenService;
    private final UserPermissionService userPermissionService;
    
    /**
     * 用户认证
     */
    @Transactional
    public TokenInfo authenticate(LoginRequest request) {
        // 1. 根据登录类型查找用户
        User user = findUserByLoginType(request);
        
        // 2. 验证用户状态
        validateUserStatus(user);
        
        // 3. 验证密码
        validatePassword(request.getPassword(), user.getPasswordHash());
        
        // 4. 更新登录信息
        user.updateLastLogin(request.getLoginIp());
        userRepository.save(user);
        
        // 5. 生成令牌
        String accessToken = jwtTokenService.generateAccessToken(user);
        String refreshToken = jwtTokenService.generateRefreshToken(user);
        
        // 6. 构建用户信息
        TokenInfo.UserInfo userInfo = buildUserInfo(user);
        
        // 7. 构建令牌信息
        return TokenInfo.builder()
            .accessToken(accessToken)
            .refreshToken(refreshToken)
            .tokenType("Bearer")
            .expiresIn(3600L) // 1小时
            .userInfo(userInfo)
            .issuedAt(LocalDateTime.now())
            .build();
    }
    
    /**
     * 验证令牌
     */
    public TokenValidationResponse validateToken(String token) {
        try {
            var decodedJWT = jwtTokenService.verifyToken(token);
            
            Long userId = Long.valueOf(decodedJWT.getSubject());
            String username = decodedJWT.getClaim("username").asString();
            String userType = decodedJWT.getClaim("userType").asString();
            List<String> roles = decodedJWT.getClaim("roles").asList(String.class);
            
            // 获取用户权限
            List<String> permissions = userPermissionService.getUserPermissions(userId);
            
            return TokenValidationResponse.builder()
                .valid(true)
                .userId(userId)
                .username(username)
                .userType(userType)
                .roles(roles)
                .permissions(permissions)
                .expiresAt(decodedJWT.getExpiresAt().toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime())
                .expiringSoon(jwtTokenService.isTokenExpiringSoon(token))
                .build();
                
        } catch (Exception e) {
            return TokenValidationResponse.builder()
                .valid(false)
                .errorMessage(e.getMessage())
                .build();
        }
    }
    
    /**
     * 获取当前用户信息
     */
    public TokenInfo.UserInfo getCurrentUserInfo(String token) {
        Long userId = jwtTokenService.extractUserId(token);
        if (userId == null) {
            throw new RuntimeException("无效的令牌");
        }
        
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在"));
            
        return buildUserInfo(user);
    }
    
    /**
     * 检查用户名是否可用
     */
    public boolean isUsernameAvailable(String username) {
        return !userRepository.existsByUsername(username);
    }
    
    /**
     * 检查邮箱是否可用
     */
    public boolean isEmailAvailable(String email) {
        return !userRepository.existsByEmail(email);
    }
    
    /**
     * 检查手机号是否可用
     */
    public boolean isPhoneAvailable(String phone) {
        return !userRepository.existsByPhone(phone);
    }
    
    /**
     * 根据登录类型查找用户
     */
    private User findUserByLoginType(LoginRequest request) {
        Optional<User> userOpt = switch (request.getLoginType()) {
            case USERNAME -> userRepository.findByUsername(request.getUsername());
            case EMAIL -> userRepository.findByEmail(request.getUsername());
            case PHONE -> userRepository.findByPhone(request.getUsername());
        };
        
        return userOpt.orElseThrow(() -> new RuntimeException("用户不存在"));
    }
    
    /**
     * 验证用户状态
     */
    private void validateUserStatus(User user) {
        if (user.isDeleted()) {
            throw new RuntimeException("用户账户已删除");
        }
        
        if (user.isDisabled()) {
            throw new RuntimeException("用户账户已被禁用");
        }
        
        if (!user.isActive()) {
            throw new RuntimeException("用户账户未激活");
        }
    }
    
    /**
     * 验证密码
     */
    private void validatePassword(String rawPassword, String encodedPassword) {
        if (!passwordEncoder.matches(rawPassword, encodedPassword)) {
            throw new RuntimeException("用户名或密码错误");
        }
    }
    
    /**
     * 构建用户信息
     */
    private TokenInfo.UserInfo buildUserInfo(User user) {
        // 获取用户角色
        List<String> roles = user.getRoles().stream()
            .map(role -> role.getRoleName())
            .toList();
            
        // 获取用户权限
        List<String> permissions = userPermissionService.getUserPermissions(user.getId());
        
        return TokenInfo.UserInfo.builder()
            .userId(user.getId())
            .username(user.getUsername())
            .email(user.getEmail())
            .phone(user.getPhone())
            .userType(user.getUserType().name())
            .status(user.getStatus().name())
            .roles(roles)
            .permissions(permissions)
            .emailVerified(user.getEmailVerified())
            .phoneVerified(user.getPhoneVerified())
            .twoFactorEnabled(user.getTwoFactorEnabled())
            .lastLoginTime(user.getLastLoginTime())
            .build();
    }
}

/**
 * 用户权限服务
 * 处理用户权限相关逻辑
 */
@Service
@RequiredArgsConstructor
@Slf4j
class UserPermissionService {
    
    private final UserRepository userRepository;
    
    /**
     * 获取用户权限列表
     */
    public List<String> getUserPermissions(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在"));
            
        return user.getRoles().stream()
            .flatMap(role -> role.getPermissions().stream())
            .map(permission -> permission.getPermissionIdentifier())
            .distinct()
            .toList();
    }
    
    /**
     * 检查用户是否拥有指定权限
     */
    public boolean hasPermission(Long userId, String permission) {
        List<String> userPermissions = getUserPermissions(userId);
        return userPermissions.contains(permission);
    }
    
    /**
     * 检查用户是否拥有指定角色
     */
    public boolean hasRole(Long userId, String roleName) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在"));
            
        return user.getRoles().stream()
            .anyMatch(role -> role.getRoleName().equals(roleName));
    }
    
    /**
     * 检查用户是否为管理员
     */
    public boolean isAdmin(Long userId) {
        return hasRole(userId, "ADMIN") || hasRole(userId, "SUPER_ADMIN");
    }
    
    /**
     * 检查用户是否为商家
     */
    public boolean isMerchant(Long userId) {
        return hasRole(userId, "MERCHANT");
    }
}
