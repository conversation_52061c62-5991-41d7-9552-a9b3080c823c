package com.teleshop.auth.service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.teleshop.auth.entity.User;
import com.teleshop.auth.dto.TokenInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * JWT Token服务
 * 负责Token的生成、验证、刷新和撤销
 */
@Service
@Slf4j
public class JwtTokenService {
    
    @Value("${teleshop.jwt.secret}")
    private String jwtSecret;
    
    @Value("${teleshop.jwt.access-token-expiration:3600}")
    private Long accessTokenExpiration; // 访问令牌过期时间（秒）
    
    @Value("${teleshop.jwt.refresh-token-expiration:604800}")
    private Long refreshTokenExpiration; // 刷新令牌过期时间（秒）
    
    @Value("${teleshop.jwt.issuer:teleshop}")
    private String issuer;
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    public JwtTokenService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    /**
     * 生成访问令牌
     */
    public String generateAccessToken(User user) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            Date expiresAt = new Date(System.currentTimeMillis() + accessTokenExpiration * 1000);
            
            return JWT.create()
                .withIssuer(issuer)
                .withSubject(user.getId().toString())
                .withClaim("username", user.getUsername())
                .withClaim("userType", user.getUserType().name())
                .withClaim("roles", user.getRoles().stream()
                    .map(role -> role.getRoleName())
                    .toList())
                .withClaim("tokenType", "ACCESS")
                .withIssuedAt(new Date())
                .withExpiresAt(expiresAt)
                .withJWTId(UUID.randomUUID().toString())
                .sign(algorithm);
        } catch (JWTCreationException e) {
            log.error("生成访问令牌失败", e);
            throw new RuntimeException("生成访问令牌失败", e);
        }
    }
    
    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(User user) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            Date expiresAt = new Date(System.currentTimeMillis() + refreshTokenExpiration * 1000);
            String jti = UUID.randomUUID().toString();
            
            String refreshToken = JWT.create()
                .withIssuer(issuer)
                .withSubject(user.getId().toString())
                .withClaim("username", user.getUsername())
                .withClaim("tokenType", "REFRESH")
                .withIssuedAt(new Date())
                .withExpiresAt(expiresAt)
                .withJWTId(jti)
                .sign(algorithm);
            
            // 将刷新令牌存储到Redis中，用于撤销控制
            String redisKey = "refresh_token:" + user.getId() + ":" + jti;
            redisTemplate.opsForValue().set(redisKey, refreshToken, refreshTokenExpiration, TimeUnit.SECONDS);
            
            return refreshToken;
        } catch (JWTCreationException e) {
            log.error("生成刷新令牌失败", e);
            throw new RuntimeException("生成刷新令牌失败", e);
        }
    }
    
    /**
     * 验证令牌
     */
    public DecodedJWT verifyToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            JWTVerifier verifier = JWT.require(algorithm)
                .withIssuer(issuer)
                .build();
            
            DecodedJWT decodedJWT = verifier.verify(token);
            
            // 检查令牌是否在黑名单中
            if (isTokenBlacklisted(decodedJWT.getId())) {
                throw new JWTVerificationException("令牌已被撤销");
            }
            
            return decodedJWT;
        } catch (JWTVerificationException e) {
            log.warn("令牌验证失败: {}", e.getMessage());
            throw new RuntimeException("令牌验证失败", e);
        }
    }
    
    /**
     * 刷新访问令牌
     */
    public TokenInfo refreshAccessToken(String refreshToken) {
        try {
            DecodedJWT decodedJWT = verifyToken(refreshToken);
            
            // 验证是否为刷新令牌
            String tokenType = decodedJWT.getClaim("tokenType").asString();
            if (!"REFRESH".equals(tokenType)) {
                throw new RuntimeException("无效的刷新令牌");
            }
            
            // 检查刷新令牌是否存在于Redis中
            Long userId = Long.valueOf(decodedJWT.getSubject());
            String jti = decodedJWT.getId();
            String redisKey = "refresh_token:" + userId + ":" + jti;
            
            if (!redisTemplate.hasKey(redisKey)) {
                throw new RuntimeException("刷新令牌已失效");
            }
            
            // 这里应该从数据库获取用户信息，简化处理
            User user = new User();
            user.setId(userId);
            user.setUsername(decodedJWT.getClaim("username").asString());
            
            // 生成新的访问令牌
            String newAccessToken = generateAccessToken(user);
            
            return TokenInfo.builder()
                .accessToken(newAccessToken)
                .refreshToken(refreshToken) // 刷新令牌保持不变
                .tokenType("Bearer")
                .expiresIn(accessTokenExpiration)
                .build();
            
        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            throw new RuntimeException("刷新令牌失败", e);
        }
    }
    
    /**
     * 撤销令牌
     */
    public void revokeToken(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            String jti = decodedJWT.getId();
            Date expiresAt = decodedJWT.getExpiresAt();
            
            // 将令牌ID添加到黑名单
            long ttl = (expiresAt.getTime() - System.currentTimeMillis()) / 1000;
            if (ttl > 0) {
                String blacklistKey = "token_blacklist:" + jti;
                redisTemplate.opsForValue().set(blacklistKey, "revoked", ttl, TimeUnit.SECONDS);
            }
            
            // 如果是刷新令牌，从Redis中删除
            String tokenType = decodedJWT.getClaim("tokenType").asString();
            if ("REFRESH".equals(tokenType)) {
                Long userId = Long.valueOf(decodedJWT.getSubject());
                String refreshTokenKey = "refresh_token:" + userId + ":" + jti;
                redisTemplate.delete(refreshTokenKey);
            }
            
        } catch (Exception e) {
            log.error("撤销令牌失败", e);
            throw new RuntimeException("撤销令牌失败", e);
        }
    }
    
    /**
     * 撤销用户的所有令牌
     */
    public void revokeAllUserTokens(Long userId) {
        try {
            // 删除用户的所有刷新令牌
            String pattern = "refresh_token:" + userId + ":*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            
            log.info("已撤销用户 {} 的所有令牌", userId);
        } catch (Exception e) {
            log.error("撤销用户令牌失败", e);
            throw new RuntimeException("撤销用户令牌失败", e);
        }
    }
    
    /**
     * 检查令牌是否在黑名单中
     */
    private boolean isTokenBlacklisted(String jti) {
        String blacklistKey = "token_blacklist:" + jti;
        return redisTemplate.hasKey(blacklistKey);
    }
    
    /**
     * 从令牌中提取用户ID
     */
    public Long extractUserId(String token) {
        try {
            DecodedJWT decodedJWT = verifyToken(token);
            return Long.valueOf(decodedJWT.getSubject());
        } catch (Exception e) {
            log.error("提取用户ID失败", e);
            return null;
        }
    }
    
    /**
     * 从令牌中提取用户名
     */
    public String extractUsername(String token) {
        try {
            DecodedJWT decodedJWT = verifyToken(token);
            return decodedJWT.getClaim("username").asString();
        } catch (Exception e) {
            log.error("提取用户名失败", e);
            return null;
        }
    }
    
    /**
     * 从令牌中提取角色列表
     */
    public List<String> extractRoles(String token) {
        try {
            DecodedJWT decodedJWT = verifyToken(token);
            return decodedJWT.getClaim("roles").asList(String.class);
        } catch (Exception e) {
            log.error("提取角色列表失败", e);
            return List.of();
        }
    }
    
    /**
     * 检查令牌是否即将过期（30分钟内）
     */
    public boolean isTokenExpiringSoon(String token) {
        try {
            DecodedJWT decodedJWT = JWT.decode(token);
            Date expiresAt = decodedJWT.getExpiresAt();
            long timeUntilExpiry = expiresAt.getTime() - System.currentTimeMillis();
            return timeUntilExpiry < 30 * 60 * 1000; // 30分钟
        } catch (Exception e) {
            return true; // 如果无法解析，认为即将过期
        }
    }
}
