package com.teleshop.auth.repository;

import com.teleshop.auth.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问接口
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);
    
    /**
     * 根据手机号查找用户
     */
    Optional<User> findByPhone(String phone);
    
    /**
     * 根据用户名或邮箱或手机号查找用户
     */
    @Query("SELECT u FROM User u WHERE u.username = :identifier OR u.email = :identifier OR u.phone = :identifier")
    Optional<User> findByUsernameOrEmailOrPhone(@Param("identifier") String identifier);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);
    
    /**
     * 根据用户状态查找用户
     */
    List<User> findByStatus(User.UserStatus status);
    
    /**
     * 根据用户类型查找用户
     */
    List<User> findByUserType(User.UserType userType);
    
    /**
     * 根据用户状态和类型查找用户
     */
    Page<User> findByStatusAndUserType(User.UserStatus status, User.UserType userType, Pageable pageable);
    
    /**
     * 查找指定时间范围内注册的用户
     */
    @Query("SELECT u FROM User u WHERE u.createdTime BETWEEN :startTime AND :endTime")
    List<User> findByCreatedTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找指定时间范围内登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginTime BETWEEN :startTime AND :endTime")
    List<User> findByLastLoginTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找长时间未登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginTime < :cutoffTime OR u.lastLoginTime IS NULL")
    List<User> findInactiveUsers(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 根据注册来源查找用户
     */
    List<User> findByRegisterSource(String registerSource);
    
    /**
     * 查找已验证邮箱的用户
     */
    List<User> findByEmailVerifiedTrue();
    
    /**
     * 查找已验证手机的用户
     */
    List<User> findByPhoneVerifiedTrue();
    
    /**
     * 查找启用两步验证的用户
     */
    List<User> findByTwoFactorEnabledTrue();
    
    /**
     * 根据用户名模糊查询
     */
    @Query("SELECT u FROM User u WHERE u.username LIKE %:keyword% OR u.email LIKE %:keyword% OR u.phone LIKE %:keyword%")
    Page<User> searchUsers(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 统计用户总数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.status != 'DELETED'")
    long countActiveUsers();
    
    /**
     * 统计指定状态的用户数量
     */
    long countByStatus(User.UserStatus status);
    
    /**
     * 统计指定类型的用户数量
     */
    long countByUserType(User.UserType userType);
    
    /**
     * 统计今日注册用户数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE DATE(u.createdTime) = CURRENT_DATE")
    long countTodayRegistrations();
    
    /**
     * 统计今日活跃用户数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE DATE(u.lastLoginTime) = CURRENT_DATE")
    long countTodayActiveUsers();
    
    /**
     * 查找拥有指定角色的用户
     */
    @Query("SELECT DISTINCT u FROM User u JOIN u.roles r WHERE r.roleName = :roleName")
    List<User> findByRoleName(@Param("roleName") String roleName);
    
    /**
     * 查找拥有指定权限的用户
     */
    @Query("SELECT DISTINCT u FROM User u JOIN u.roles r JOIN r.permissions p WHERE p.permissionName = :permissionName")
    List<User> findByPermissionName(@Param("permissionName") String permissionName);
    
    /**
     * 批量更新用户状态
     */
    @Query("UPDATE User u SET u.status = :status WHERE u.id IN :userIds")
    int updateUserStatus(@Param("userIds") List<Long> userIds, @Param("status") User.UserStatus status);
    
    /**
     * 批量删除用户（软删除）
     */
    @Query("UPDATE User u SET u.status = 'DELETED' WHERE u.id IN :userIds")
    int softDeleteUsers(@Param("userIds") List<Long> userIds);
}

/**
 * 角色数据访问接口
 */
@Repository
interface RoleRepository extends JpaRepository<com.teleshop.auth.entity.Role, Long> {
    
    /**
     * 根据角色名查找角色
     */
    Optional<com.teleshop.auth.entity.Role> findByRoleName(String roleName);
    
    /**
     * 检查角色名是否存在
     */
    boolean existsByRoleName(String roleName);
    
    /**
     * 根据角色类型查找角色
     */
    List<com.teleshop.auth.entity.Role> findByRoleType(com.teleshop.auth.entity.Role.RoleType roleType);
    
    /**
     * 查找启用的角色
     */
    List<com.teleshop.auth.entity.Role> findByEnabledTrue();
    
    /**
     * 根据角色名模糊查询
     */
    @Query("SELECT r FROM Role r WHERE r.roleName LIKE %:keyword% OR r.displayName LIKE %:keyword%")
    Page<com.teleshop.auth.entity.Role> searchRoles(@Param("keyword") String keyword, Pageable pageable);
}

/**
 * 权限数据访问接口
 */
@Repository
interface PermissionRepository extends JpaRepository<com.teleshop.auth.entity.Permission, Long> {
    
    /**
     * 根据权限名查找权限
     */
    Optional<com.teleshop.auth.entity.Permission> findByPermissionName(String permissionName);
    
    /**
     * 检查权限名是否存在
     */
    boolean existsByPermissionName(String permissionName);
    
    /**
     * 根据资源查找权限
     */
    List<com.teleshop.auth.entity.Permission> findByResource(String resource);
    
    /**
     * 根据资源和动作查找权限
     */
    Optional<com.teleshop.auth.entity.Permission> findByResourceAndAction(String resource, String action);
    
    /**
     * 根据权限类型查找权限
     */
    List<com.teleshop.auth.entity.Permission> findByPermissionType(com.teleshop.auth.entity.Permission.PermissionType permissionType);
    
    /**
     * 查找启用的权限
     */
    List<com.teleshop.auth.entity.Permission> findByEnabledTrue();
    
    /**
     * 根据权限名模糊查询
     */
    @Query("SELECT p FROM Permission p WHERE p.permissionName LIKE %:keyword% OR p.displayName LIKE %:keyword%")
    Page<com.teleshop.auth.entity.Permission> searchPermissions(@Param("keyword") String keyword, Pageable pageable);
}
