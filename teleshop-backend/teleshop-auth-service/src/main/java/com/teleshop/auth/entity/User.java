package com.teleshop.auth.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 用户实体类
 * 统一身份认证系统的核心用户模型
 */
@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_username", columnList = "username"),
    @Index(name = "idx_email", columnList = "email"),
    @Index(name = "idx_phone", columnList = "phone"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_user_type", columnList = "userType")
})
@Data
@EqualsAndHashCode(callSuper = false)
public class User {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 用户名 - 唯一标识
     */
    @Column(unique = true, nullable = false, length = 50)
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_]{4,50}$", message = "用户名只能包含字母、数字和下划线，长度4-50位")
    private String username;
    
    /**
     * 密码哈希值
     */
    @Column(nullable = false)
    @NotBlank(message = "密码不能为空")
    private String passwordHash;
    
    /**
     * 邮箱地址
     */
    @Column(unique = true, length = 100)
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 手机号码
     */
    @Column(unique = true, length = 20)
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 用户状态
     * 1-正常, 2-禁用, 3-删除, 4-待激活
     */
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    private UserStatus status = UserStatus.PENDING_ACTIVATION;
    
    /**
     * 用户类型
     * 1-普通用户, 2-商家用户, 3-管理员
     */
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    private UserType userType = UserType.REGULAR_USER;
    
    /**
     * 注册来源
     */
    @Column(length = 20)
    private String registerSource = "APP";
    
    /**
     * 注册IP地址
     */
    @Column(length = 45)
    private String registerIp;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 最后登录IP
     */
    @Column(length = 45)
    private String lastLoginIp;
    
    /**
     * 邮箱验证状态
     */
    @Column(nullable = false)
    private Boolean emailVerified = false;
    
    /**
     * 手机验证状态
     */
    @Column(nullable = false)
    private Boolean phoneVerified = false;
    
    /**
     * 两步验证启用状态
     */
    @Column(nullable = false)
    private Boolean twoFactorEnabled = false;
    
    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedTime;
    
    /**
     * 用户角色关联
     */
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles = new HashSet<>();
    
    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        ACTIVE(1, "正常"),
        DISABLED(2, "禁用"),
        DELETED(3, "删除"),
        PENDING_ACTIVATION(4, "待激活");
        
        private final int code;
        private final String description;
        
        UserStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public int getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    /**
     * 用户类型枚举
     */
    public enum UserType {
        REGULAR_USER(1, "普通用户"),
        MERCHANT_USER(2, "商家用户"),
        ADMIN_USER(3, "管理员");
        
        private final int code;
        private final String description;
        
        UserType(int code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public int getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    /**
     * 检查用户是否激活
     */
    public boolean isActive() {
        return UserStatus.ACTIVE.equals(this.status);
    }
    
    /**
     * 检查用户是否被禁用
     */
    public boolean isDisabled() {
        return UserStatus.DISABLED.equals(this.status);
    }
    
    /**
     * 检查用户是否已删除
     */
    public boolean isDeleted() {
        return UserStatus.DELETED.equals(this.status);
    }
    
    /**
     * 检查是否为商家用户
     */
    public boolean isMerchant() {
        return UserType.MERCHANT_USER.equals(this.userType);
    }
    
    /**
     * 检查是否为管理员
     */
    public boolean isAdmin() {
        return UserType.ADMIN_USER.equals(this.userType);
    }
    
    /**
     * 更新最后登录信息
     */
    public void updateLastLogin(String loginIp) {
        this.lastLoginTime = LocalDateTime.now();
        this.lastLoginIp = loginIp;
    }
    
    /**
     * 激活用户账户
     */
    public void activate() {
        this.status = UserStatus.ACTIVE;
    }
    
    /**
     * 禁用用户账户
     */
    public void disable() {
        this.status = UserStatus.DISABLED;
    }
    
    /**
     * 验证邮箱
     */
    public void verifyEmail() {
        this.emailVerified = true;
    }
    
    /**
     * 验证手机
     */
    public void verifyPhone() {
        this.phoneVerified = true;
    }
    
    /**
     * 启用两步验证
     */
    public void enableTwoFactor() {
        this.twoFactorEnabled = true;
    }
    
    /**
     * 禁用两步验证
     */
    public void disableTwoFactor() {
        this.twoFactorEnabled = false;
    }
}
