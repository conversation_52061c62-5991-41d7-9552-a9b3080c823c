package com.teleshop.auth.controller;

import com.teleshop.auth.dto.*;
import com.teleshop.auth.service.AuthenticationService;
import com.teleshop.auth.service.JwtTokenService;
import com.teleshop.common.response.ApiResponse;
import com.teleshop.common.response.ResponseCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 提供用户登录、令牌刷新、令牌撤销等认证相关接口
 */
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {
    
    private final AuthenticationService authenticationService;
    private final JwtTokenService jwtTokenService;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "支持用户名、邮箱、手机号登录")
    public ApiResponse<TokenInfo> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            // 设置登录IP
            request.setLoginIp(getClientIpAddress(httpRequest));
            request.setUserAgent(httpRequest.getHeader("User-Agent"));
            
            TokenInfo tokenInfo = authenticationService.authenticate(request);
            
            log.info("用户登录成功: {}", request.getUsername());
            return ApiResponse.success(tokenInfo);
            
        } catch (Exception e) {
            log.error("用户登录失败: {}, 错误: {}", request.getUsername(), e.getMessage());
            return ApiResponse.error(ResponseCode.AUTHENTICATION_FAILED, e.getMessage());
        }
    }
    
    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新令牌", description = "使用刷新令牌获取新的访问令牌")
    public ApiResponse<TokenInfo> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        
        try {
            TokenInfo tokenInfo = jwtTokenService.refreshAccessToken(request.getRefreshToken());
            
            log.info("令牌刷新成功");
            return ApiResponse.success(tokenInfo);
            
        } catch (Exception e) {
            log.error("令牌刷新失败: {}", e.getMessage());
            return ApiResponse.error(ResponseCode.TOKEN_REFRESH_FAILED, e.getMessage());
        }
    }
    
    /**
     * 撤销令牌
     */
    @PostMapping("/revoke")
    @Operation(summary = "撤销令牌", description = "撤销指定的访问令牌或刷新令牌")
    public ApiResponse<Void> revokeToken(@Valid @RequestBody RevokeTokenRequest request) {
        
        try {
            jwtTokenService.revokeToken(request.getToken());
            
            log.info("令牌撤销成功");
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("令牌撤销失败: {}", e.getMessage());
            return ApiResponse.error(ResponseCode.TOKEN_REVOKE_FAILED, e.getMessage());
        }
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "撤销用户的所有令牌")
    public ApiResponse<Void> logout(HttpServletRequest request) {
        
        try {
            String token = extractTokenFromRequest(request);
            if (token != null) {
                Long userId = jwtTokenService.extractUserId(token);
                if (userId != null) {
                    jwtTokenService.revokeAllUserTokens(userId);
                    log.info("用户登出成功: {}", userId);
                }
            }
            
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("用户登出失败: {}", e.getMessage());
            return ApiResponse.error(ResponseCode.LOGOUT_FAILED, e.getMessage());
        }
    }
    
    /**
     * 验证令牌
     */
    @PostMapping("/validate")
    @Operation(summary = "验证令牌", description = "验证令牌的有效性并返回用户信息")
    public ApiResponse<TokenValidationResponse> validateToken(
            @RequestParam String token) {
        
        try {
            TokenValidationResponse response = authenticationService.validateToken(token);
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("令牌验证失败: {}", e.getMessage());
            
            TokenValidationResponse response = TokenValidationResponse.builder()
                .valid(false)
                .errorMessage(e.getMessage())
                .build();
            
            return ApiResponse.success(response);
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "根据令牌获取当前登录用户的详细信息")
    public ApiResponse<TokenInfo.UserInfo> getCurrentUser(HttpServletRequest request) {
        
        try {
            String token = extractTokenFromRequest(request);
            if (token == null) {
                return ApiResponse.error(ResponseCode.TOKEN_MISSING, "缺少访问令牌");
            }
            
            TokenInfo.UserInfo userInfo = authenticationService.getCurrentUserInfo(token);
            return ApiResponse.success(userInfo);
            
        } catch (Exception e) {
            log.error("获取当前用户信息失败: {}", e.getMessage());
            return ApiResponse.error(ResponseCode.GET_USER_INFO_FAILED, e.getMessage());
        }
    }
    
    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名", description = "检查用户名是否已被使用")
    public ApiResponse<Boolean> checkUsername(@RequestParam String username) {
        
        try {
            boolean available = authenticationService.isUsernameAvailable(username);
            return ApiResponse.success(available);
            
        } catch (Exception e) {
            log.error("检查用户名失败: {}", e.getMessage());
            return ApiResponse.error(ResponseCode.CHECK_USERNAME_FAILED, e.getMessage());
        }
    }
    
    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱", description = "检查邮箱是否已被使用")
    public ApiResponse<Boolean> checkEmail(@RequestParam String email) {
        
        try {
            boolean available = authenticationService.isEmailAvailable(email);
            return ApiResponse.success(available);
            
        } catch (Exception e) {
            log.error("检查邮箱失败: {}", e.getMessage());
            return ApiResponse.error(ResponseCode.CHECK_EMAIL_FAILED, e.getMessage());
        }
    }
    
    /**
     * 检查手机号是否可用
     */
    @GetMapping("/check-phone")
    @Operation(summary = "检查手机号", description = "检查手机号是否已被使用")
    public ApiResponse<Boolean> checkPhone(@RequestParam String phone) {
        
        try {
            boolean available = authenticationService.isPhoneAvailable(phone);
            return ApiResponse.success(available);
            
        } catch (Exception e) {
            log.error("检查手机号失败: {}", e.getMessage());
            return ApiResponse.error(ResponseCode.CHECK_PHONE_FAILED, e.getMessage());
        }
    }
    
    /**
     * 从请求中提取Token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
