package com.teleshop.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Token信息DTO
 * 用于返回认证成功后的令牌信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TokenInfo {
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";
    
    /**
     * 访问令牌过期时间（秒）
     */
    private Long expiresIn;
    
    /**
     * 令牌作用域
     */
    private String scope;
    
    /**
     * 用户信息
     */
    private UserInfo userInfo;
    
    /**
     * 令牌颁发时间
     */
    private LocalDateTime issuedAt;
    
    /**
     * 用户信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {
        
        /**
         * 用户ID
         */
        private Long userId;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 邮箱
         */
        private String email;
        
        /**
         * 手机号
         */
        private String phone;
        
        /**
         * 用户类型
         */
        private String userType;
        
        /**
         * 用户状态
         */
        private String status;
        
        /**
         * 角色列表
         */
        private List<String> roles;
        
        /**
         * 权限列表
         */
        private List<String> permissions;
        
        /**
         * 邮箱验证状态
         */
        private Boolean emailVerified;
        
        /**
         * 手机验证状态
         */
        private Boolean phoneVerified;
        
        /**
         * 两步验证启用状态
         */
        private Boolean twoFactorEnabled;
        
        /**
         * 最后登录时间
         */
        private LocalDateTime lastLoginTime;
    }
}

/**
 * 登录请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginRequest {
    
    /**
     * 用户名/邮箱/手机号
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 验证码（如果启用）
     */
    private String captcha;
    
    /**
     * 验证码ID
     */
    private String captchaId;
    
    /**
     * 登录类型
     * USERNAME - 用户名登录
     * EMAIL - 邮箱登录
     * PHONE - 手机号登录
     */
    private LoginType loginType = LoginType.USERNAME;
    
    /**
     * 记住我
     */
    private Boolean rememberMe = false;
    
    /**
     * 客户端信息
     */
    private String clientId;
    
    /**
     * 登录IP
     */
    private String loginIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    public enum LoginType {
        USERNAME, EMAIL, PHONE
    }
}

/**
 * 刷新令牌请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RefreshTokenRequest {
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 客户端ID
     */
    private String clientId;
}

/**
 * 撤销令牌请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RevokeTokenRequest {
    
    /**
     * 要撤销的令牌
     */
    private String token;
    
    /**
     * 令牌类型提示
     * access_token - 访问令牌
     * refresh_token - 刷新令牌
     */
    private String tokenTypeHint;
    
    /**
     * 客户端ID
     */
    private String clientId;
}

/**
 * 令牌验证响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TokenValidationResponse {
    
    /**
     * 令牌是否有效
     */
    private Boolean valid;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户类型
     */
    private String userType;
    
    /**
     * 角色列表
     */
    private List<String> roles;
    
    /**
     * 权限列表
     */
    private List<String> permissions;
    
    /**
     * 令牌过期时间
     */
    private LocalDateTime expiresAt;
    
    /**
     * 是否即将过期
     */
    private Boolean expiringSoon;
    
    /**
     * 错误信息（如果令牌无效）
     */
    private String errorMessage;
}
