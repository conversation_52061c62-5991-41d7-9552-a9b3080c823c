package com.teleshop.auth.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 角色实体类
 * 基于RBAC模型的角色定义
 */
@Entity
@Table(name = "roles", indexes = {
    @Index(name = "idx_role_name", columnList = "roleName"),
    @Index(name = "idx_role_type", columnList = "roleType")
})
@Data
@EqualsAndHashCode(callSuper = false)
public class Role {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 角色名称 - 唯一标识
     */
    @Column(unique = true, nullable = false, length = 50)
    @NotBlank(message = "角色名称不能为空")
    private String roleName;
    
    /**
     * 角色显示名称
     */
    @Column(nullable = false, length = 100)
    @NotBlank(message = "角色显示名称不能为空")
    private String displayName;
    
    /**
     * 角色描述
     */
    @Column(length = 500)
    private String description;
    
    /**
     * 角色类型
     * SYSTEM - 系统角色
     * BUSINESS - 业务角色
     * CUSTOM - 自定义角色
     */
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private RoleType roleType = RoleType.BUSINESS;
    
    /**
     * 角色状态
     */
    @Column(nullable = false)
    private Boolean enabled = true;
    
    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdTime;
    
    /**
     * 角色权限关联
     */
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();
    
    /**
     * 角色类型枚举
     */
    public enum RoleType {
        SYSTEM("系统角色"),
        BUSINESS("业务角色"),
        CUSTOM("自定义角色");
        
        private final String description;
        
        RoleType(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    /**
     * 添加权限
     */
    public void addPermission(Permission permission) {
        this.permissions.add(permission);
    }
    
    /**
     * 移除权限
     */
    public void removePermission(Permission permission) {
        this.permissions.remove(permission);
    }
    
    /**
     * 检查是否拥有指定权限
     */
    public boolean hasPermission(String permissionName) {
        return permissions.stream()
            .anyMatch(permission -> permission.getPermissionName().equals(permissionName));
    }
    
    /**
     * 启用角色
     */
    public void enable() {
        this.enabled = true;
    }
    
    /**
     * 禁用角色
     */
    public void disable() {
        this.enabled = false;
    }
}

/**
 * 权限实体类
 */
@Entity
@Table(name = "permissions", indexes = {
    @Index(name = "idx_permission_name", columnList = "permissionName"),
    @Index(name = "idx_resource_action", columnList = "resource, action")
})
@Data
@EqualsAndHashCode(callSuper = false)
class Permission {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 权限名称 - 唯一标识
     */
    @Column(unique = true, nullable = false, length = 100)
    @NotBlank(message = "权限名称不能为空")
    private String permissionName;
    
    /**
     * 权限显示名称
     */
    @Column(nullable = false, length = 100)
    @NotBlank(message = "权限显示名称不能为空")
    private String displayName;
    
    /**
     * 资源标识
     */
    @Column(nullable = false, length = 100)
    @NotBlank(message = "资源标识不能为空")
    private String resource;
    
    /**
     * 操作动作
     */
    @Column(nullable = false, length = 50)
    @NotBlank(message = "操作动作不能为空")
    private String action;
    
    /**
     * 权限描述
     */
    @Column(length = 500)
    private String description;
    
    /**
     * 权限类型
     */
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private PermissionType permissionType = PermissionType.FUNCTIONAL;
    
    /**
     * 权限状态
     */
    @Column(nullable = false)
    private Boolean enabled = true;
    
    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdTime;
    
    /**
     * 权限类型枚举
     */
    public enum PermissionType {
        FUNCTIONAL("功能权限"),
        DATA("数据权限"),
        API("接口权限");
        
        private final String description;
        
        PermissionType(String description) {
            this.description = description;
        }
        
        public String getDescription() { return description; }
    }
    
    /**
     * 构造权限标识符
     * 格式: resource:action
     */
    public String getPermissionIdentifier() {
        return resource + ":" + action;
    }
    
    /**
     * 启用权限
     */
    public void enable() {
        this.enabled = true;
    }
    
    /**
     * 禁用权限
     */
    public void disable() {
        this.enabled = false;
    }
}
