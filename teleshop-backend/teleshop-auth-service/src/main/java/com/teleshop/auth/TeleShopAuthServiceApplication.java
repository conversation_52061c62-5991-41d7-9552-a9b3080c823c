package com.teleshop.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * TeleShop 统一身份认证服务启动类
 * 
 * 功能特性：
 * - OAuth 2.0 + JWT 认证
 * - 统一用户管理
 * - 角色权限控制
 * - 多端登录支持
 * - 安全审计日志
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableCaching
@EnableAsync
@EnableTransactionManagement
public class TeleShopAuthServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(TeleShopAuthServiceApplication.class, args);
    }
}
