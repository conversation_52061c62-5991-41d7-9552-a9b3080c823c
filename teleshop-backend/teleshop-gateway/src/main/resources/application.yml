server:
  port: 8080

spring:
  application:
    name: teleshop-gateway
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: teleshop
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        namespace: teleshop
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # 用户服务路由
        - id: teleshop-user-service
          uri: lb://teleshop-user-service
          predicates:
            - Path=/api/v1/users/**,/api/v1/auth/**
          filters:
            - StripPrefix=0
            - name: AuthFilter
              args:
                excludePaths: /api/v1/auth/login,/api/v1/auth/register,/api/v1/auth/refresh

        # 商品服务路由
        - id: teleshop-product-service
          uri: lb://teleshop-product-service
          predicates:
            - Path=/api/v1/products/**,/api/v1/categories/**,/api/v1/brands/**
          filters:
            - StripPrefix=0

        # 订单服务路由
        - id: teleshop-order-service
          uri: lb://teleshop-order-service
          predicates:
            - Path=/api/v1/orders/**,/api/v1/cart/**
          filters:
            - StripPrefix=0

        # 钱包服务路由
        - id: teleshop-wallet-service
          uri: lb://teleshop-wallet-service
          predicates:
            - Path=/api/v1/wallets/**,/api/v1/payments/**,/api/v1/transactions/**
          filters:
            - StripPrefix=0
            - name: AuthFilter
              args:
                excludePaths: /api/v1/wallets/exchange-rates
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                redis-rate-limiter.requestedTokens: 1

        # 消息服务路由
        - id: teleshop-message-service
          uri: lb://teleshop-message-service
          predicates:
            - Path=/api/v1/messages/**,/api/v1/chats/**,/ws/**
          filters:
            - StripPrefix=0
            - name: AuthFilter
              args:
                excludePaths: ""

        # 商家服务路由
        - id: teleshop-merchant-service
          uri: lb://teleshop-merchant-service
          predicates:
            - Path=/api/v1/merchants/**,/api/v1/merchant-stores/**
          filters:
            - StripPrefix=0
            - name: AuthFilter
              args:
                excludePaths: /api/v1/merchants/public/**

        # 文件服务路由
        - id: teleshop-file-service
          uri: lb://teleshop-file-service
          predicates:
            - Path=/api/v1/files/**,/api/v1/upload/**
          filters:
            - StripPrefix=0
            - name: AuthFilter
              args:
                excludePaths: ""
            - name: RequestSize
              args:
                maxSize: 50MB

        # 通知服务路由
        - id: teleshop-notification-service
          uri: lb://teleshop-notification-service
          predicates:
            - Path=/api/v1/notifications/**,/api/v1/push/**
          filters:
            - StripPrefix=0
            - name: AuthFilter
              args:
                excludePaths: ""

        # 搜索服务路由
        - id: teleshop-search-service
          uri: lb://teleshop-search-service
          predicates:
            - Path=/api/v1/search/**,/api/v1/elasticsearch/**
          filters:
            - StripPrefix=0
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 20
                redis-rate-limiter.burstCapacity: 50
                redis-rate-limiter.requestedTokens: 1

        # 统计分析服务路由
        - id: teleshop-analytics-service
          uri: lb://teleshop-analytics-service
          predicates:
            - Path=/api/v1/analytics/**,/api/v1/reports/**
          filters:
            - StripPrefix=0
            - name: AuthFilter
              args:
                excludePaths: ""
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600

  # Redis配置
  redis:
    host: localhost
    port: 6379
    database: 0
    password: 
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

# 日志配置
logging:
  level:
    com.teleshop: DEBUG
    org.springframework.cloud.gateway: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway
  endpoint:
    health:
      show-details: always

# TeleShop网关配置
teleshop:
  gateway:
    # JWT配置
    jwt:
      secret: ${TELESHOP_JWT_SECRET:teleshop2024_gateway_secret_key_for_development_only}
      expiration: 7200000 # 2小时
      refresh-expiration: 604800000 # 7天
      issuer: teleshop-gateway
      audience: teleshop-app
    # 限流配置
    rate-limit:
      enabled: true
      capacity: 100 # 令牌桶容量
      refill-tokens: 50 # 每秒补充令牌数
      requested-tokens: 1 # 每次请求消耗令牌数
      # 特殊路径限流配置
      special-limits:
        - path: /api/v1/auth/login
          capacity: 10
          refill-tokens: 5
        - path: /api/v1/wallets/transfer
          capacity: 5
          refill-tokens: 2
        - path: /api/v1/wallets/withdraw
          capacity: 3
          refill-tokens: 1
    # 白名单配置
    whitelist:
      - /api/v1/auth/login
      - /api/v1/auth/register
      - /api/v1/auth/refresh
      - /api/v1/auth/send-verify-code
      - /api/v1/products/**
      - /api/v1/categories/**
      - /api/v1/brands/**
      - /api/v1/merchants/public/**
      - /api/v1/wallets/exchange-rates
      - /api/v1/search/public/**
      - /actuator/**
      - /swagger-ui/**
      - /swagger-resources/**
      - /v3/api-docs/**
      - /favicon.ico
    # 黑名单配置
    blacklist:
      enabled: true
      max-attempts: 5 # 最大尝试次数
      lock-duration: 300000 # 锁定时长（毫秒）
    # 安全配置
    security:
      # IP白名单
      ip-whitelist:
        enabled: false
        ips: []
      # 请求头安全
      headers:
        enabled: true
        x-frame-options: DENY
        x-content-type-options: nosniff
        x-xss-protection: "1; mode=block"
        strict-transport-security: "max-age=31536000; includeSubDomains"
      # 敏感信息过滤
      sensitive-filter:
        enabled: true
        fields:
          - password
          - payPassword
          - privateKey
          - secret
          - token
    # 监控配置
    monitoring:
      enabled: true
      metrics:
        enabled: true
        export-interval: 60000 # 导出间隔（毫秒）
      tracing:
        enabled: true
        sample-rate: 0.1 # 采样率
    # 缓存配置
    cache:
      enabled: true
      ttl: 300000 # 缓存TTL（毫秒）
      max-size: 10000 # 最大缓存条目数