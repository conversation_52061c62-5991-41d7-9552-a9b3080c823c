import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  InputNumber,
  Switch,
  message,
  Tabs,
  Tag,
  Tooltip,
  Dropdown,
  Progress,
  Statistic,
  Row,
  Col,
  Badge,
  Drawer,
  Radio,
  Checkbox,
  Divider,
  Alert,
  Typography,
  Empty,
  Spin,
  Timeline,
  Steps
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  BarChartOutlined,
  SettingOutlined,
  GiftOutlined,
  PercentageOutlined,
  TeamOutlined,
  CrownOutlined,
  TrophyOutlined,
  DollarOutlined,
  RiseOutlined,
  FallOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  StopOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useMarketingManagement } from '../../hooks/useMarketingManagement';
import { CouponModal } from './components/CouponModal';
import { FlashDiscountModal } from './components/FlashDiscountModal';
import { FullReductionModal } from './components/FullReductionModal';
import { GroupBuyModal } from './components/GroupBuyModal';
import { MemberPricingModal } from './components/MemberPricingModal';
import { MarketingAnalysisDrawer } from './components/MarketingAnalysisDrawer';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Text, Title } = Typography;
const { Step } = Steps;

const MarketingManagementPage: React.FC = () => {
  const {
    coupons,
    flashDiscounts,
    fullReductionActivities,
    groupBuyActivities,
    memberPricing,
    marketingOverview,
    loading,
    pagination,
    filters,
    selectedRowKeys,
    fetchCoupons,
    fetchFlashDiscounts,
    fetchFullReductionActivities,
    fetchGroupBuyActivities,
    fetchMemberPricing,
    fetchMarketingOverview,
    createCoupon,
    updateCoupon,
    deleteCoupon,
    publishCoupon,
    disableCoupon,
    createFlashDiscount,
    startFlashDiscount,
    stopFlashDiscount,
    setFilters,
    setSelectedRowKeys
  } = useMarketingManagement();

  const [activeTab, setActiveTab] = useState('overview');
  const [couponModalVisible, setCouponModalVisible] = useState(false);
  const [flashDiscountModalVisible, setFlashDiscountModalVisible] = useState(false);
  const [fullReductionModalVisible, setFullReductionModalVisible] = useState(false);
  const [groupBuyModalVisible, setGroupBuyModalVisible] = useState(false);
  const [memberPricingModalVisible, setMemberPricingModalVisible] = useState(false);
  const [analysisDrawerVisible, setAnalysisDrawerVisible] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<any>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    fetchMarketingOverview();
  }, []);

  useEffect(() => {
    switch (activeTab) {
      case 'coupons':
        fetchCoupons();
        break;
      case 'flash-discounts':
        fetchFlashDiscounts();
        break;
      case 'full-reduction':
        fetchFullReductionActivities();
        break;
      case 'group-buy':
        fetchGroupBuyActivities();
        break;
      case 'member-pricing':
        fetchMemberPricing();
        break;
    }
  }, [activeTab]);

  const couponColumns: ColumnsType<any> = [
    {
      title: '优惠券信息',
      key: 'couponInfo',
      width: 250,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>{record.couponName}</div>
          <div style={{ color: '#666', fontSize: 12 }}>代码: {record.couponCode}</div>
          <Tag color={getCouponTypeColor(record.couponType)}>
            {getCouponTypeText(record.couponType)}
          </Tag>
        </div>
      ),
    },
    {
      title: '折扣信息',
      key: 'discountInfo',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500, color: '#f5222d' }}>
            {record.couponType === 'PERCENTAGE' 
              ? `${record.discountValue}%` 
              : `¥${record.discountValue}`}
          </div>
          {record.minOrderAmount && (
            <div style={{ fontSize: 12, color: '#666' }}>
              满¥{record.minOrderAmount}可用
            </div>
          )}
        </div>
      ),
    },
    {
      title: '发放情况',
      key: 'distribution',
      width: 120,
      render: (_, record) => (
        <div>
          <Progress
            percent={Math.round((record.claimedQuantity / record.totalQuantity) * 100)}
            size="small"
            status={record.claimedQuantity >= record.totalQuantity ? 'exception' : 'active'}
          />
          <div style={{ fontSize: 12, marginTop: 4 }}>
            {record.claimedQuantity}/{record.totalQuantity}
          </div>
        </div>
      ),
    },
    {
      title: '使用情况',
      key: 'usage',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.usedQuantity}</div>
          <div style={{ fontSize: 12, color: '#666' }}>
            使用率: {record.claimedQuantity > 0 
              ? Math.round((record.usedQuantity / record.claimedQuantity) * 100) 
              : 0}%
          </div>
        </div>
      ),
    },
    {
      title: '有效期',
      key: 'validity',
      width: 180,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: 12 }}>
            {new Date(record.validFrom).toLocaleDateString()} - 
            {new Date(record.validTo).toLocaleDateString()}
          </div>
          <Tag color={getValidityStatusColor(record.validTo)}>
            {getValidityStatusText(record.validTo)}
          </Tag>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewCoupon(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditCoupon(record)}
              disabled={record.status === 'PUBLISHED'}
            />
          </Tooltip>
          <Tooltip title="数据分析">
            <Button
              type="text"
              icon={<BarChartOutlined />}
              onClick={() => handleViewAnalysis(record)}
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: getCouponActionItems(record),
            }}
          >
            <Button type="text" icon={<SettingOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  const getCouponTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'FIXED_AMOUNT': 'red',
      'PERCENTAGE': 'orange',
      'FREE_SHIPPING': 'green',
    };
    return colors[type] || 'default';
  };

  const getCouponTypeText = (type: string) => {
    const texts: Record<string, string> = {
      'FIXED_AMOUNT': '固定金额',
      'PERCENTAGE': '百分比',
      'FREE_SHIPPING': '免运费',
    };
    return texts[type] || type;
  };

  const getValidityStatusColor = (validTo: string) => {
    const now = new Date();
    const endDate = new Date(validTo);
    const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysLeft < 0) return 'red';
    if (daysLeft <= 7) return 'orange';
    return 'green';
  };

  const getValidityStatusText = (validTo: string) => {
    const now = new Date();
    const endDate = new Date(validTo);
    const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysLeft < 0) return '已过期';
    if (daysLeft === 0) return '今日到期';
    if (daysLeft <= 7) return `${daysLeft}天后到期`;
    return '有效';
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'DRAFT': 'default',
      'PUBLISHED': 'green',
      'DISABLED': 'red',
      'EXPIRED': 'volcano',
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts: Record<string, string> = {
      'DRAFT': '草稿',
      'PUBLISHED': '已发布',
      'DISABLED': '已停用',
      'EXPIRED': '已过期',
    };
    return texts[status] || status;
  };

  const getCouponActionItems = (record: any) => {
    const items = [];
    
    if (record.status === 'DRAFT') {
      items.push({
        key: 'publish',
        label: '发布',
        icon: <PlayCircleOutlined />,
        onClick: () => handlePublishCoupon(record),
      });
    }
    
    if (record.status === 'PUBLISHED') {
      items.push({
        key: 'disable',
        label: '停用',
        icon: <PauseCircleOutlined />,
        onClick: () => handleDisableCoupon(record),
      });
    }
    
    items.push(
      {
        key: 'copy',
        label: '复制',
        icon: <CopyOutlined />,
        onClick: () => handleCopyCoupon(record),
      },
      {
        type: 'divider',
      },
      {
        key: 'delete',
        label: '删除',
        icon: <DeleteOutlined />,
        danger: true,
        onClick: () => handleDeleteCoupon(record),
        disabled: record.status === 'PUBLISHED',
      }
    );
    
    return items;
  };

  const handleViewCoupon = (coupon: any) => {
    // 查看优惠券详情
  };

  const handleEditCoupon = (coupon: any) => {
    setSelectedActivity(coupon);
    setModalMode('edit');
    setCouponModalVisible(true);
  };

  const handleViewAnalysis = (activity: any) => {
    setSelectedActivity(activity);
    setAnalysisDrawerVisible(true);
  };

  const handlePublishCoupon = (coupon: any) => {
    Modal.confirm({
      title: '发布优惠券',
      content: '确定要发布这张优惠券吗？发布后将无法修改。',
      onOk: () => {
        publishCoupon(coupon.couponId);
      },
    });
  };

  const handleDisableCoupon = (coupon: any) => {
    Modal.confirm({
      title: '停用优惠券',
      content: '确定要停用这张优惠券吗？停用后用户将无法继续领取。',
      onOk: () => {
        disableCoupon(coupon.couponId, '商家主动停用');
      },
    });
  };

  const handleCopyCoupon = (coupon: any) => {
    setSelectedActivity({ ...coupon, couponName: `${coupon.couponName} - 副本` });
    setModalMode('create');
    setCouponModalVisible(true);
  };

  const handleDeleteCoupon = (coupon: any) => {
    Modal.confirm({
      title: '删除优惠券',
      content: '确定要删除这张优惠券吗？删除后无法恢复。',
      icon: <ExclamationCircleOutlined />,
      danger: true,
      onOk: () => {
        deleteCoupon(coupon.couponId);
      },
    });
  };

  const renderOverviewTab = () => (
    <div>
      {/* 营销概览统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="活动总数"
              value={marketingOverview?.totalActivities || 0}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中活动"
              value={marketingOverview?.activeActivities || 0}
              valueStyle={{ color: '#3f8600' }}
              prefix={<PlayCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="参与用户数"
              value={marketingOverview?.totalParticipants || 0}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="营销ROI"
              value={marketingOverview?.marketingROI || 0}
              precision={2}
              suffix="%"
              valueStyle={{ 
                color: (marketingOverview?.marketingROI || 0) > 0 ? '#3f8600' : '#cf1322' 
              }}
              prefix={
                (marketingOverview?.marketingROI || 0) > 0 ? <RiseOutlined /> : <FallOutlined />
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 快速创建活动 */}
      <Card title="快速创建活动" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedActivity(null);
                setCouponModalVisible(true);
              }}
            >
              <GiftOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 8 }} />
              <div>创建优惠券</div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedActivity(null);
                setFlashDiscountModalVisible(true);
              }}
            >
              <PercentageOutlined style={{ fontSize: 32, color: '#f5222d', marginBottom: 8 }} />
              <div>限时折扣</div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedActivity(null);
                setFullReductionModalVisible(true);
              }}
            >
              <DollarOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 8 }} />
              <div>满减活动</div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedActivity(null);
                setGroupBuyModalVisible(true);
              }}
            >
              <TeamOutlined style={{ fontSize: 32, color: '#722ed1', marginBottom: 8 }} />
              <div>团购活动</div>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 活动类型统计 */}
      {marketingOverview?.activityTypeStats && (
        <Card title="活动类型统计">
          <Row gutter={16}>
            {Object.entries(marketingOverview.activityTypeStats).map(([type, stats]) => (
              <Col span={6} key={type}>
                <Card size="small">
                  <Statistic
                    title={getActivityTypeText(type)}
                    value={stats.count}
                    suffix="个活动"
                  />
                  <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                    收入: ¥{stats.revenue?.toFixed(2) || 0}
                  </div>
                  <div style={{ fontSize: 12, color: '#666' }}>
                    参与: {stats.participants || 0}人
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Card>
      )}
    </div>
  );

  const getActivityTypeText = (type: string) => {
    const texts: Record<string, string> = {
      'COUPON': '优惠券',
      'FLASH_DISCOUNT': '限时折扣',
      'FULL_REDUCTION': '满减活动',
      'GROUP_BUY': '团购活动',
    };
    return texts[type] || type;
  };

  const renderCouponsTab = () => (
    <div>
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => {
                  setModalMode('create');
                  setSelectedActivity(null);
                  setCouponModalVisible(true);
                }}
              >
                创建优惠券
              </Button>
              <Button icon={<BarChartOutlined />}>
                批量分析
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索优惠券名称、代码"
                style={{ width: 250 }}
                onSearch={(value) => setFilters({ ...filters, keyword: value })}
              />
              <Select
                placeholder="状态筛选"
                style={{ width: 120 }}
                allowClear
                onChange={(value) => setFilters({ ...filters, status: value })}
              >
                <Option value="DRAFT">草稿</Option>
                <Option value="PUBLISHED">已发布</Option>
                <Option value="DISABLED">已停用</Option>
                <Option value="EXPIRED">已过期</Option>
              </Select>
            </Space>
          </Col>
        </Row>
      </Card>

      <Card>
        <Table
          columns={couponColumns}
          dataSource={coupons}
          loading={loading}
          pagination={pagination}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          scroll={{ x: 1200 }}
          rowKey="couponId"
        />
      </Card>
    </div>
  );

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>营销管理</Title>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="营销概览" key="overview">
          {renderOverviewTab()}
        </TabPane>
        <TabPane tab="优惠券" key="coupons">
          {renderCouponsTab()}
        </TabPane>
        <TabPane tab="限时折扣" key="flash-discounts">
          {/* 限时折扣内容 */}
        </TabPane>
        <TabPane tab="满减活动" key="full-reduction">
          {/* 满减活动内容 */}
        </TabPane>
        <TabPane tab="团购活动" key="group-buy">
          {/* 团购活动内容 */}
        </TabPane>
        <TabPane tab="会员价格" key="member-pricing">
          {/* 会员价格内容 */}
        </TabPane>
      </Tabs>

      {/* 优惠券弹窗 */}
      <CouponModal
        visible={couponModalVisible}
        mode={modalMode}
        coupon={selectedActivity}
        onCancel={() => setCouponModalVisible(false)}
        onSuccess={() => {
          setCouponModalVisible(false);
          fetchCoupons();
        }}
      />

      {/* 限时折扣弹窗 */}
      <FlashDiscountModal
        visible={flashDiscountModalVisible}
        mode={modalMode}
        discount={selectedActivity}
        onCancel={() => setFlashDiscountModalVisible(false)}
        onSuccess={() => {
          setFlashDiscountModalVisible(false);
          fetchFlashDiscounts();
        }}
      />

      {/* 满减活动弹窗 */}
      <FullReductionModal
        visible={fullReductionModalVisible}
        mode={modalMode}
        activity={selectedActivity}
        onCancel={() => setFullReductionModalVisible(false)}
        onSuccess={() => {
          setFullReductionModalVisible(false);
          fetchFullReductionActivities();
        }}
      />

      {/* 团购活动弹窗 */}
      <GroupBuyModal
        visible={groupBuyModalVisible}
        mode={modalMode}
        activity={selectedActivity}
        onCancel={() => setGroupBuyModalVisible(false)}
        onSuccess={() => {
          setGroupBuyModalVisible(false);
          fetchGroupBuyActivities();
        }}
      />

      {/* 会员价格弹窗 */}
      <MemberPricingModal
        visible={memberPricingModalVisible}
        mode={modalMode}
        pricing={selectedActivity}
        onCancel={() => setMemberPricingModalVisible(false)}
        onSuccess={() => {
          setMemberPricingModalVisible(false);
          fetchMemberPricing();
        }}
      />

      {/* 营销分析抽屉 */}
      <MarketingAnalysisDrawer
        visible={analysisDrawerVisible}
        activity={selectedActivity}
        onClose={() => setAnalysisDrawerVisible(false)}
      />
    </div>
  );
};

export default MarketingManagementPage;
