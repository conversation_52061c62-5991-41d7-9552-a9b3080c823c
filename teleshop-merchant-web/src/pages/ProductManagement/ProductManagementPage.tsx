import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Modal,
  Upload,
  message,
  Tabs,
  Tag,
  Tooltip,
  Dropdown,
  Progress,
  Statistic,
  Row,
  Col,
  Badge,
  Drawer,
  Form,
  InputNumber,
  Switch,
  Divider,
  Alert,
  Typography,
  Empty,
  Spin
} from 'antd';
import {
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  ExclamationCircleOutlined,
  BarChartOutlined,
  SettingOutlined,
  BulbOutlined,
  FileExcelOutlined,
  ReloadOutlined,
  FilterOutlined,
  MoreOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useProductManagement } from '../../hooks/useProductManagement';
import { ProductBatchImportModal } from './components/ProductBatchImportModal';
import { ProductTemplateModal } from './components/ProductTemplateModal';
import { ProductAnalysisDrawer } from './components/ProductAnalysisDrawer';
import { StockAlertModal } from './components/StockAlertModal';
import { PricingStrategyModal } from './components/PricingStrategyModal';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Text, Title } = Typography;

interface Product {
  id: string;
  name: string;
  sku: string;
  category: string;
  brand: string;
  price: number;
  stock: number;
  sales: number;
  status: string;
  auditStatus: string;
  createTime: string;
  updateTime: string;
  images: string[];
  description: string;
  tags: string[];
}

const ProductManagementPage: React.FC = () => {
  const {
    products,
    loading,
    pagination,
    filters,
    selectedRowKeys,
    templates,
    stockAlerts,
    salesStats,
    fetchProducts,
    handleBatchImport,
    handleBatchExport,
    handleBatchDelete,
    handleBatchStatusUpdate,
    handleCreateTemplate,
    handleUpdateTemplate,
    handleDeleteTemplate,
    handleStockAlertSettings,
    handlePricingStrategy,
    setFilters,
    setSelectedRowKeys
  } = useProductManagement();

  const [activeTab, setActiveTab] = useState('products');
  const [batchImportVisible, setBatchImportVisible] = useState(false);
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [analysisDrawerVisible, setAnalysisDrawerVisible] = useState(false);
  const [stockAlertModalVisible, setStockAlertModalVisible] = useState(false);
  const [pricingModalVisible, setPricingModalVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);

  useEffect(() => {
    fetchProducts();
  }, []);

  const columns: ColumnsType<Product> = [
    {
      title: '商品信息',
      key: 'productInfo',
      width: 300,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img
            src={record.images[0]}
            alt={record.name}
            style={{ width: 60, height: 60, objectFit: 'cover', marginRight: 12, borderRadius: 4 }}
          />
          <div>
            <div style={{ fontWeight: 500, marginBottom: 4 }}>{record.name}</div>
            <div style={{ color: '#666', fontSize: 12 }}>SKU: {record.sku}</div>
            <div style={{ color: '#666', fontSize: 12 }}>{record.category}</div>
          </div>
        </div>
      ),
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price) => <Text strong>¥{price.toFixed(2)}</Text>,
      sorter: true,
    },
    {
      title: '库存',
      dataIndex: 'stock',
      key: 'stock',
      width: 100,
      render: (stock) => (
        <span style={{ color: stock < 10 ? '#ff4d4f' : stock < 50 ? '#faad14' : '#52c41a' }}>
          {stock}
        </span>
      ),
      sorter: true,
    },
    {
      title: '销量',
      dataIndex: 'sales',
      key: 'sales',
      width: 100,
      sorter: true,
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (_, record) => (
        <Space direction="vertical" size={4}>
          <Tag color={getStatusColor(record.status)}>{getStatusText(record.status)}</Tag>
          <Tag color={getAuditStatusColor(record.auditStatus)}>{getAuditStatusText(record.auditStatus)}</Tag>
        </Space>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 150,
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewProduct(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditProduct(record)}
            />
          </Tooltip>
          <Tooltip title="数据分析">
            <Button
              type="text"
              icon={<BarChartOutlined />}
              onClick={() => handleViewAnalysis(record)}
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'copy',
                  label: '复制商品',
                  icon: <CopyOutlined />,
                  onClick: () => handleCopyProduct(record),
                },
                {
                  key: 'pricing',
                  label: '价格策略',
                  icon: <SettingOutlined />,
                  onClick: () => handlePricingStrategy(record),
                },
                {
                  key: 'stock-alert',
                  label: '库存预警',
                  icon: <WarningOutlined />,
                  onClick: () => handleStockAlert(record),
                },
                {
                  type: 'divider',
                },
                {
                  key: 'delete',
                  label: '删除',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => handleDeleteProduct(record),
                },
              ],
            }}
          >
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'ACTIVE': 'green',
      'INACTIVE': 'red',
      'DRAFT': 'orange',
      'OUT_OF_STOCK': 'volcano',
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts: Record<string, string> = {
      'ACTIVE': '上架',
      'INACTIVE': '下架',
      'DRAFT': '草稿',
      'OUT_OF_STOCK': '缺货',
    };
    return texts[status] || status;
  };

  const getAuditStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'APPROVED': 'green',
      'REJECTED': 'red',
      'PENDING': 'orange',
      'DRAFT': 'default',
    };
    return colors[status] || 'default';
  };

  const getAuditStatusText = (status: string) => {
    const texts: Record<string, string> = {
      'APPROVED': '审核通过',
      'REJECTED': '审核拒绝',
      'PENDING': '审核中',
      'DRAFT': '未提交',
    };
    return texts[status] || status;
  };

  const handleViewProduct = (product: Product) => {
    // 跳转到商品详情页
    window.open(`/merchant/products/${product.id}`, '_blank');
  };

  const handleEditProduct = (product: Product) => {
    // 跳转到商品编辑页
    window.open(`/merchant/products/${product.id}/edit`, '_blank');
  };

  const handleViewAnalysis = (product: Product) => {
    setSelectedProduct(product);
    setAnalysisDrawerVisible(true);
  };

  const handleCopyProduct = (product: Product) => {
    Modal.confirm({
      title: '复制商品',
      content: '确定要复制这个商品吗？',
      onOk: () => {
        // 实现复制逻辑
        message.success('商品复制成功');
      },
    });
  };

  const handleDeleteProduct = (product: Product) => {
    Modal.confirm({
      title: '删除商品',
      content: '确定要删除这个商品吗？删除后无法恢复。',
      icon: <ExclamationCircleOutlined />,
      danger: true,
      onOk: () => {
        // 实现删除逻辑
        message.success('商品删除成功');
      },
    });
  };

  const handleStockAlert = (product: Product) => {
    setSelectedProduct(product);
    setStockAlertModalVisible(true);
  };

  const handleBatchOperation = (operation: string) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要操作的商品');
      return;
    }

    switch (operation) {
      case 'delete':
        Modal.confirm({
          title: '批量删除',
          content: `确定要删除选中的 ${selectedRowKeys.length} 个商品吗？`,
          icon: <ExclamationCircleOutlined />,
          danger: true,
          onOk: () => handleBatchDelete(selectedRowKeys),
        });
        break;
      case 'activate':
        handleBatchStatusUpdate(selectedRowKeys, 'ACTIVE');
        break;
      case 'deactivate':
        handleBatchStatusUpdate(selectedRowKeys, 'INACTIVE');
        break;
      default:
        break;
    }
  };

  const renderProductsTab = () => (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="商品总数"
              value={salesStats?.totalProducts || 0}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="上架商品"
              value={salesStats?.activeProducts || 0}
              valueStyle={{ color: '#3f8600' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="库存预警"
              value={stockAlerts?.length || 0}
              valueStyle={{ color: '#cf1322' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待审核"
              value={salesStats?.pendingAudit || 0}
              valueStyle={{ color: '#faad14' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button type="primary" icon={<PlusOutlined />}>
                添加商品
              </Button>
              <Button icon={<UploadOutlined />} onClick={() => setBatchImportVisible(true)}>
                批量导入
              </Button>
              <Button icon={<DownloadOutlined />} onClick={() => handleBatchExport()}>
                批量导出
              </Button>
              <Button icon={<FileExcelOutlined />} onClick={() => setTemplateModalVisible(true)}>
                模板管理
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索商品名称、SKU"
                style={{ width: 250 }}
                onSearch={(value) => setFilters({ ...filters, keyword: value })}
              />
              <Button icon={<FilterOutlined />} onClick={() => setFilterDrawerVisible(true)}>
                筛选
              </Button>
              <Button icon={<ReloadOutlined />} onClick={() => fetchProducts()} />
            </Space>
          </Col>
        </Row>

        {selectedRowKeys.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <Alert
              message={
                <Space>
                  <span>已选择 {selectedRowKeys.length} 个商品</span>
                  <Button size="small" onClick={() => handleBatchOperation('activate')}>
                    批量上架
                  </Button>
                  <Button size="small" onClick={() => handleBatchOperation('deactivate')}>
                    批量下架
                  </Button>
                  <Button size="small" danger onClick={() => handleBatchOperation('delete')}>
                    批量删除
                  </Button>
                  <Button size="small" onClick={() => setSelectedRowKeys([])}>
                    取消选择
                  </Button>
                </Space>
              }
              type="info"
              showIcon
            />
          </div>
        )}
      </Card>

      {/* 商品表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={products}
          loading={loading}
          pagination={pagination}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          scroll={{ x: 1200 }}
          rowKey="id"
        />
      </Card>
    </div>
  );

  const renderTemplatesTab = () => (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setTemplateModalVisible(true)}>
            创建模板
          </Button>
        </div>
        <Table
          columns={[
            {
              title: '模板名称',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: '分类',
              dataIndex: 'category',
              key: 'category',
            },
            {
              title: '使用次数',
              dataIndex: 'usageCount',
              key: 'usageCount',
            },
            {
              title: '创建时间',
              dataIndex: 'createTime',
              key: 'createTime',
            },
            {
              title: '操作',
              key: 'action',
              render: (_, record) => (
                <Space>
                  <Button type="link" onClick={() => handleEditTemplate(record)}>
                    编辑
                  </Button>
                  <Button type="link" onClick={() => handleUseTemplate(record)}>
                    使用
                  </Button>
                  <Button type="link" danger onClick={() => handleDeleteTemplate(record.id)}>
                    删除
                  </Button>
                </Space>
              ),
            },
          ]}
          dataSource={templates}
          rowKey="id"
        />
      </Card>
    </div>
  );

  const renderStockAlertsTab = () => (
    <div>
      <Card>
        <Table
          columns={[
            {
              title: '商品信息',
              key: 'productInfo',
              render: (_, record) => (
                <div>
                  <div>{record.productName}</div>
                  <div style={{ color: '#666', fontSize: 12 }}>SKU: {record.sku}</div>
                </div>
              ),
            },
            {
              title: '当前库存',
              dataIndex: 'currentStock',
              key: 'currentStock',
              render: (stock) => (
                <span style={{ color: '#ff4d4f' }}>{stock}</span>
              ),
            },
            {
              title: '预警阈值',
              dataIndex: 'alertThreshold',
              key: 'alertThreshold',
            },
            {
              title: '预警级别',
              dataIndex: 'alertLevel',
              key: 'alertLevel',
              render: (level) => (
                <Tag color={level === 'HIGH' ? 'red' : level === 'MEDIUM' ? 'orange' : 'yellow'}>
                  {level === 'HIGH' ? '高' : level === 'MEDIUM' ? '中' : '低'}
                </Tag>
              ),
            },
            {
              title: '预警时间',
              dataIndex: 'alertTime',
              key: 'alertTime',
            },
            {
              title: '操作',
              key: 'action',
              render: (_, record) => (
                <Space>
                  <Button type="link" onClick={() => handleStockReplenishment(record)}>
                    补货
                  </Button>
                  <Button type="link" onClick={() => handleStockAlertSettings(record)}>
                    设置
                  </Button>
                </Space>
              ),
            },
          ]}
          dataSource={stockAlerts}
          rowKey="id"
        />
      </Card>
    </div>
  );

  const handleEditTemplate = (template: any) => {
    // 实现编辑模板逻辑
  };

  const handleUseTemplate = (template: any) => {
    // 实现使用模板逻辑
  };

  const handleStockReplenishment = (alert: any) => {
    // 实现补货逻辑
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>商品管理</Title>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="商品列表" key="products">
          {renderProductsTab()}
        </TabPane>
        <TabPane tab="商品模板" key="templates">
          {renderTemplatesTab()}
        </TabPane>
        <TabPane 
          tab={
            <Badge count={stockAlerts?.length || 0} size="small">
              库存预警
            </Badge>
          } 
          key="stock-alerts"
        >
          {renderStockAlertsTab()}
        </TabPane>
      </Tabs>

      {/* 批量导入弹窗 */}
      <ProductBatchImportModal
        visible={batchImportVisible}
        onCancel={() => setBatchImportVisible(false)}
        onSuccess={() => {
          setBatchImportVisible(false);
          fetchProducts();
        }}
      />

      {/* 模板管理弹窗 */}
      <ProductTemplateModal
        visible={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        onSuccess={() => {
          setTemplateModalVisible(false);
          // 刷新模板列表
        }}
      />

      {/* 商品分析抽屉 */}
      <ProductAnalysisDrawer
        visible={analysisDrawerVisible}
        product={selectedProduct}
        onClose={() => setAnalysisDrawerVisible(false)}
      />

      {/* 库存预警设置弹窗 */}
      <StockAlertModal
        visible={stockAlertModalVisible}
        product={selectedProduct}
        onCancel={() => setStockAlertModalVisible(false)}
        onSuccess={() => {
          setStockAlertModalVisible(false);
          // 刷新数据
        }}
      />

      {/* 价格策略弹窗 */}
      <PricingStrategyModal
        visible={pricingModalVisible}
        product={selectedProduct}
        onCancel={() => setPricingModalVisible(false)}
        onSuccess={() => {
          setPricingModalVisible(false);
          // 刷新数据
        }}
      />

      {/* 筛选抽屉 */}
      <Drawer
        title="商品筛选"
        placement="right"
        width={400}
        visible={filterDrawerVisible}
        onClose={() => setFilterDrawerVisible(false)}
      >
        <Form layout="vertical">
          <Form.Item label="商品状态">
            <Select placeholder="选择状态" allowClear>
              <Option value="ACTIVE">上架</Option>
              <Option value="INACTIVE">下架</Option>
              <Option value="DRAFT">草稿</Option>
              <Option value="OUT_OF_STOCK">缺货</Option>
            </Select>
          </Form.Item>
          <Form.Item label="审核状态">
            <Select placeholder="选择审核状态" allowClear>
              <Option value="APPROVED">审核通过</Option>
              <Option value="REJECTED">审核拒绝</Option>
              <Option value="PENDING">审核中</Option>
              <Option value="DRAFT">未提交</Option>
            </Select>
          </Form.Item>
          <Form.Item label="商品分类">
            <Select placeholder="选择分类" allowClear>
              {/* 分类选项 */}
            </Select>
          </Form.Item>
          <Form.Item label="价格范围">
            <Input.Group compact>
              <InputNumber placeholder="最低价" style={{ width: '45%' }} />
              <Input
                style={{ width: '10%', textAlign: 'center', pointerEvents: 'none' }}
                placeholder="~"
                disabled
              />
              <InputNumber placeholder="最高价" style={{ width: '45%' }} />
            </Input.Group>
          </Form.Item>
          <Form.Item label="库存范围">
            <Input.Group compact>
              <InputNumber placeholder="最低库存" style={{ width: '45%' }} />
              <Input
                style={{ width: '10%', textAlign: 'center', pointerEvents: 'none' }}
                placeholder="~"
                disabled
              />
              <InputNumber placeholder="最高库存" style={{ width: '45%' }} />
            </Input.Group>
          </Form.Item>
          <Form.Item label="创建时间">
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary">应用筛选</Button>
              <Button>重置</Button>
            </Space>
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};

export default ProductManagementPage;
