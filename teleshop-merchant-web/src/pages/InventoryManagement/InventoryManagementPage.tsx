import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  InputNumber,
  Switch,
  message,
  Tabs,
  Tag,
  Tooltip,
  Dropdown,
  Progress,
  Statistic,
  Row,
  Col,
  Badge,
  Drawer,
  Radio,
  Checkbox,
  Divider,
  Alert,
  Typography,
  Empty,
  Spin,
  Timeline,
  Steps,
  Upload,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  WarningOutlined,
  SwapOutlined,
  InboxOutlined,
  ExportOutlined,
  ImportOutlined,
  BarChartOutlined,
  SettingOutlined,
  BellOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  HomeOutlined,
  ShopOutlined,
  FileExcelOutlined,
  ReloadOutlined,
  FilterOutlined,
  SearchOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined,
  SyncOutlined,
  AuditOutlined,
  TruckOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useInventoryManagement } from '../../hooks/useInventoryManagement';
import { WarehouseModal } from './components/WarehouseModal';
import { StockAdjustmentModal } from './components/StockAdjustmentModal';
import { StockAlertSettingModal } from './components/StockAlertSettingModal';
import { StocktakingModal } from './components/StocktakingModal';
import { StockTransferModal } from './components/StockTransferModal';
import { InventoryAnalysisDrawer } from './components/InventoryAnalysisDrawer';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Text, Title } = Typography;
const { Dragger } = Upload;

const InventoryManagementPage: React.FC = () => {
  const {
    warehouses,
    productStocks,
    stockAlerts,
    stocktakingTasks,
    stockTransfers,
    inventoryOverview,
    loading,
    pagination,
    filters,
    selectedRowKeys,
    fetchWarehouses,
    fetchProductStocks,
    fetchStockAlerts,
    fetchStocktakingTasks,
    fetchStockTransfers,
    fetchInventoryOverview,
    createWarehouse,
    updateWarehouse,
    deleteWarehouse,
    adjustStock,
    batchAdjustStock,
    setStockAlertSetting,
    createStocktakingTask,
    createStockTransfer,
    setFilters,
    setSelectedRowKeys
  } = useInventoryManagement();

  const [activeTab, setActiveTab] = useState('overview');
  const [warehouseModalVisible, setWarehouseModalVisible] = useState(false);
  const [stockAdjustmentModalVisible, setStockAdjustmentModalVisible] = useState(false);
  const [stockAlertModalVisible, setStockAlertModalVisible] = useState(false);
  const [stocktakingModalVisible, setStocktakingModalVisible] = useState(false);
  const [stockTransferModalVisible, setStockTransferModalVisible] = useState(false);
  const [analysisDrawerVisible, setAnalysisDrawerVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [selectedWarehouse, setSelectedWarehouse] = useState<string>('all');

  useEffect(() => {
    fetchInventoryOverview();
    fetchWarehouses();
  }, []);

  useEffect(() => {
    switch (activeTab) {
      case 'stocks':
        fetchProductStocks();
        break;
      case 'alerts':
        fetchStockAlerts();
        break;
      case 'stocktaking':
        fetchStocktakingTasks();
        break;
      case 'transfers':
        fetchStockTransfers();
        break;
    }
  }, [activeTab, selectedWarehouse]);

  const stockColumns: ColumnsType<any> = [
    {
      title: '商品信息',
      key: 'productInfo',
      width: 250,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img
            src={record.productImage}
            alt={record.productName}
            style={{ width: 50, height: 50, objectFit: 'cover', marginRight: 12, borderRadius: 4 }}
          />
          <div>
            <div style={{ fontWeight: 500, marginBottom: 4 }}>{record.productName}</div>
            <div style={{ color: '#666', fontSize: 12 }}>SKU: {record.productSku}</div>
          </div>
        </div>
      ),
    },
    {
      title: '仓库',
      dataIndex: 'warehouseName',
      key: 'warehouseName',
      width: 120,
    },
    {
      title: '当前库存',
      dataIndex: 'currentStock',
      key: 'currentStock',
      width: 100,
      render: (stock, record) => (
        <span style={{ 
          color: getStockColor(stock, record.alertThreshold, record.safetyStock),
          fontWeight: 500 
        }}>
          {stock}
        </span>
      ),
      sorter: true,
    },
    {
      title: '可用库存',
      dataIndex: 'availableStock',
      key: 'availableStock',
      width: 100,
      sorter: true,
    },
    {
      title: '锁定库存',
      dataIndex: 'lockedStock',
      key: 'lockedStock',
      width: 100,
      render: (locked) => locked > 0 ? <span style={{ color: '#faad14' }}>{locked}</span> : locked,
    },
    {
      title: '安全库存',
      dataIndex: 'safetyStock',
      key: 'safetyStock',
      width: 100,
    },
    {
      title: '预警状态',
      key: 'alertStatus',
      width: 120,
      render: (_, record) => {
        const alertLevel = getAlertLevel(record.currentStock, record.alertThreshold, record.safetyStock);
        return alertLevel ? (
          <Tag color={getAlertLevelColor(alertLevel)} icon={<WarningOutlined />}>
            {getAlertLevelText(alertLevel)}
          </Tag>
        ) : (
          <Tag color="green" icon={<CheckCircleOutlined />}>正常</Tag>
        );
      },
    },
    {
      title: '库存成本',
      dataIndex: 'stockCost',
      key: 'stockCost',
      width: 120,
      render: (cost) => `¥${cost?.toFixed(2) || 0}`,
    },
    {
      title: '周转率',
      dataIndex: 'turnoverRate',
      key: 'turnoverRate',
      width: 100,
      render: (rate) => rate ? `${(rate * 100).toFixed(1)}%` : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="调整库存">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleStockAdjustment(record)}
            />
          </Tooltip>
          <Tooltip title="库存明细">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewStockDetail(record)}
            />
          </Tooltip>
          <Tooltip title="预警设置">
            <Button
              type="text"
              icon={<BellOutlined />}
              onClick={() => handleStockAlertSetting(record)}
            />
          </Tooltip>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'inbound',
                  label: '入库',
                  icon: <PlusCircleOutlined />,
                  onClick: () => handleStockInbound(record),
                },
                {
                  key: 'outbound',
                  label: '出库',
                  icon: <MinusCircleOutlined />,
                  onClick: () => handleStockOutbound(record),
                },
                {
                  key: 'transfer',
                  label: '调拨',
                  icon: <SwapOutlined />,
                  onClick: () => handleStockTransfer(record),
                },
                {
                  key: 'analysis',
                  label: '分析',
                  icon: <BarChartOutlined />,
                  onClick: () => handleStockAnalysis(record),
                },
              ],
            }}
          >
            <Button type="text" icon={<SettingOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  const getStockColor = (stock: number, alertThreshold: number, safetyStock: number) => {
    if (stock <= 0) return '#ff4d4f';
    if (stock <= safetyStock) return '#faad14';
    if (stock <= alertThreshold) return '#fa8c16';
    return '#52c41a';
  };

  const getAlertLevel = (stock: number, alertThreshold: number, safetyStock: number) => {
    if (stock <= 0) return 'CRITICAL';
    if (stock <= safetyStock) return 'HIGH';
    if (stock <= alertThreshold) return 'MEDIUM';
    return null;
  };

  const getAlertLevelColor = (level: string) => {
    const colors: Record<string, string> = {
      'CRITICAL': 'red',
      'HIGH': 'volcano',
      'MEDIUM': 'orange',
      'LOW': 'yellow',
    };
    return colors[level] || 'default';
  };

  const getAlertLevelText = (level: string) => {
    const texts: Record<string, string> = {
      'CRITICAL': '严重',
      'HIGH': '高',
      'MEDIUM': '中',
      'LOW': '低',
    };
    return texts[level] || level;
  };

  const handleStockAdjustment = (stock: any) => {
    setSelectedItem(stock);
    setStockAdjustmentModalVisible(true);
  };

  const handleViewStockDetail = (stock: any) => {
    // 查看库存明细
    Modal.info({
      title: '库存明细',
      width: 800,
      content: (
        <div>
          <Row gutter={16}>
            <Col span={12}>
              <Statistic title="当前库存" value={stock.currentStock} />
            </Col>
            <Col span={12}>
              <Statistic title="可用库存" value={stock.availableStock} />
            </Col>
          </Row>
          <Divider />
          <Row gutter={16}>
            <Col span={12}>
              <Statistic title="锁定库存" value={stock.lockedStock} />
            </Col>
            <Col span={12}>
              <Statistic title="在途库存" value={stock.inTransitStock} />
            </Col>
          </Row>
          <Divider />
          <Row gutter={16}>
            <Col span={12}>
              <Statistic title="安全库存" value={stock.safetyStock} />
            </Col>
            <Col span={12}>
              <Statistic title="预警阈值" value={stock.alertThreshold} />
            </Col>
          </Row>
        </div>
      ),
    });
  };

  const handleStockAlertSetting = (stock: any) => {
    setSelectedItem(stock);
    setStockAlertModalVisible(true);
  };

  const handleStockInbound = (stock: any) => {
    setSelectedItem({ ...stock, operationType: 'INBOUND' });
    setStockAdjustmentModalVisible(true);
  };

  const handleStockOutbound = (stock: any) => {
    setSelectedItem({ ...stock, operationType: 'OUTBOUND' });
    setStockAdjustmentModalVisible(true);
  };

  const handleStockTransfer = (stock: any) => {
    setSelectedItem(stock);
    setStockTransferModalVisible(true);
  };

  const handleStockAnalysis = (stock: any) => {
    setSelectedItem(stock);
    setAnalysisDrawerVisible(true);
  };

  const renderOverviewTab = () => (
    <div>
      {/* 库存概览统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总商品数"
              value={inventoryOverview?.totalProducts || 0}
              prefix={<InboxOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="库存预警"
              value={inventoryOverview?.alertProducts || 0}
              valueStyle={{ color: '#faad14' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="库存总值"
              value={inventoryOverview?.totalStockValue || 0}
              precision={2}
              prefix="¥"
              suffix="万"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均周转率"
              value={inventoryOverview?.averageTurnoverRate || 0}
              precision={1}
              suffix="%"
              valueStyle={{ 
                color: (inventoryOverview?.averageTurnoverRate || 0) > 50 ? '#3f8600' : '#faad14' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 仓库概览 */}
      <Card title="仓库概览" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          {warehouses.map((warehouse: any) => (
            <Col span={8} key={warehouse.warehouseId}>
              <Card size="small" hoverable>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 12 }}>
                  <HomeOutlined style={{ fontSize: 20, marginRight: 8, color: '#1890ff' }} />
                  <div>
                    <div style={{ fontWeight: 500 }}>{warehouse.warehouseName}</div>
                    <div style={{ fontSize: 12, color: '#666' }}>{warehouse.warehouseCode}</div>
                  </div>
                </div>
                <Row gutter={8}>
                  <Col span={12}>
                    <Statistic
                      title="商品种类"
                      value={warehouse.productCount || 0}
                      valueStyle={{ fontSize: 16 }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="库存总量"
                      value={warehouse.totalStock || 0}
                      valueStyle={{ fontSize: 16 }}
                    />
                  </Col>
                </Row>
                <div style={{ marginTop: 12 }}>
                  <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
                    容量使用率
                  </div>
                  <Progress
                    percent={warehouse.capacityUsageRate || 0}
                    size="small"
                    status={warehouse.capacityUsageRate > 80 ? 'exception' : 'active'}
                  />
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 快速操作 */}
      <Card title="快速操作">
        <Row gutter={16}>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedItem(null);
                setStockAdjustmentModalVisible(true);
              }}
            >
              <EditOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 8 }} />
              <div>库存调整</div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedItem(null);
                setStocktakingModalVisible(true);
              }}
            >
              <AuditOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 8 }} />
              <div>库存盘点</div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedItem(null);
                setStockTransferModalVisible(true);
              }}
            >
              <TruckOutlined style={{ fontSize: 32, color: '#722ed1', marginBottom: 8 }} />
              <div>库存调拨</div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              hoverable
              style={{ textAlign: 'center' }}
              onClick={() => {
                setModalMode('create');
                setSelectedItem(null);
                setWarehouseModalVisible(true);
              }}
            >
              <HomeOutlined style={{ fontSize: 32, color: '#fa8c16', marginBottom: 8 }} />
              <div>仓库管理</div>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );

  const renderStocksTab = () => (
    <div>
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => {
                  setModalMode('create');
                  setSelectedItem(null);
                  setStockAdjustmentModalVisible(true);
                }}
              >
                库存调整
              </Button>
              <Button icon={<ImportOutlined />}>
                批量导入
              </Button>
              <Button icon={<ExportOutlined />}>
                导出库存
              </Button>
              <Button icon={<AuditOutlined />}>
                发起盘点
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Select
                placeholder="选择仓库"
                style={{ width: 150 }}
                value={selectedWarehouse}
                onChange={setSelectedWarehouse}
              >
                <Option value="all">全部仓库</Option>
                {warehouses.map((warehouse: any) => (
                  <Option key={warehouse.warehouseId} value={warehouse.warehouseId}>
                    {warehouse.warehouseName}
                  </Option>
                ))}
              </Select>
              <Search
                placeholder="搜索商品名称、SKU"
                style={{ width: 250 }}
                onSearch={(value) => setFilters({ ...filters, keyword: value })}
              />
              <Select
                placeholder="预警状态"
                style={{ width: 120 }}
                allowClear
                onChange={(value) => setFilters({ ...filters, alertLevel: value })}
              >
                <Option value="CRITICAL">严重</Option>
                <Option value="HIGH">高</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="LOW">低</Option>
              </Select>
              <Button icon={<ReloadOutlined />} onClick={() => fetchProductStocks()} />
            </Space>
          </Col>
        </Row>

        {selectedRowKeys.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <Alert
              message={
                <Space>
                  <span>已选择 {selectedRowKeys.length} 个商品</span>
                  <Button size="small" onClick={() => handleBatchStockAdjustment()}>
                    批量调整
                  </Button>
                  <Button size="small" onClick={() => handleBatchAlertSetting()}>
                    批量预警设置
                  </Button>
                  <Button size="small" onClick={() => setSelectedRowKeys([])}>
                    取消选择
                  </Button>
                </Space>
              }
              type="info"
              showIcon
            />
          </div>
        )}
      </Card>

      <Card>
        <Table
          columns={stockColumns}
          dataSource={productStocks}
          loading={loading}
          pagination={pagination}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          scroll={{ x: 1400 }}
          rowKey="productId"
        />
      </Card>
    </div>
  );

  const handleBatchStockAdjustment = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要调整的商品');
      return;
    }
    setSelectedItem({ productIds: selectedRowKeys, operationType: 'BATCH_ADJUST' });
    setStockAdjustmentModalVisible(true);
  };

  const handleBatchAlertSetting = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要设置的商品');
      return;
    }
    setSelectedItem({ productIds: selectedRowKeys });
    setStockAlertModalVisible(true);
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>库存管理</Title>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="库存概览" key="overview">
          {renderOverviewTab()}
        </TabPane>
        <TabPane tab="库存列表" key="stocks">
          {renderStocksTab()}
        </TabPane>
        <TabPane 
          tab={
            <Badge count={stockAlerts?.length || 0} size="small">
              库存预警
            </Badge>
          } 
          key="alerts"
        >
          {/* 库存预警内容 */}
        </TabPane>
        <TabPane tab="库存盘点" key="stocktaking">
          {/* 库存盘点内容 */}
        </TabPane>
        <TabPane tab="库存调拨" key="transfers">
          {/* 库存调拨内容 */}
        </TabPane>
        <TabPane tab="仓库管理" key="warehouses">
          {/* 仓库管理内容 */}
        </TabPane>
      </Tabs>

      {/* 仓库管理弹窗 */}
      <WarehouseModal
        visible={warehouseModalVisible}
        mode={modalMode}
        warehouse={selectedItem}
        onCancel={() => setWarehouseModalVisible(false)}
        onSuccess={() => {
          setWarehouseModalVisible(false);
          fetchWarehouses();
        }}
      />

      {/* 库存调整弹窗 */}
      <StockAdjustmentModal
        visible={stockAdjustmentModalVisible}
        stock={selectedItem}
        onCancel={() => setStockAdjustmentModalVisible(false)}
        onSuccess={() => {
          setStockAdjustmentModalVisible(false);
          fetchProductStocks();
        }}
      />

      {/* 库存预警设置弹窗 */}
      <StockAlertSettingModal
        visible={stockAlertModalVisible}
        stock={selectedItem}
        onCancel={() => setStockAlertModalVisible(false)}
        onSuccess={() => {
          setStockAlertModalVisible(false);
          fetchProductStocks();
        }}
      />

      {/* 库存盘点弹窗 */}
      <StocktakingModal
        visible={stocktakingModalVisible}
        mode={modalMode}
        task={selectedItem}
        warehouses={warehouses}
        onCancel={() => setStocktakingModalVisible(false)}
        onSuccess={() => {
          setStocktakingModalVisible(false);
          fetchStocktakingTasks();
        }}
      />

      {/* 库存调拨弹窗 */}
      <StockTransferModal
        visible={stockTransferModalVisible}
        mode={modalMode}
        transfer={selectedItem}
        warehouses={warehouses}
        onCancel={() => setStockTransferModalVisible(false)}
        onSuccess={() => {
          setStockTransferModalVisible(false);
          fetchStockTransfers();
        }}
      />

      {/* 库存分析抽屉 */}
      <InventoryAnalysisDrawer
        visible={analysisDrawerVisible}
        stock={selectedItem}
        onClose={() => setAnalysisDrawerVisible(false)}
      />
    </div>
  );
};

export default InventoryManagementPage;
